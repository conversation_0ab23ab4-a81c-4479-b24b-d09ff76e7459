import * as Yup from "yup";

export const signInSchema = Yup.object({
  username: Yup.string().trim().required("Username is required"),
  password: Yup.string()
    .trim()
    .min(8, "Password must be at least 8 characters")
    .required("Please enter password"),
});

export const signUpSchema = Yup.object().shape({
  firstname: Yup.string().trim().required("First Name is required"),
  lastname: Yup.string().trim().required("Last Name is required"),
  username: Yup.string()
    .trim()
    .matches(
      /^[a-zA-Z0-9]+$/,
      "Username can only contain letters and numbers. Special characters are not allowed."
    )
    .required("Username is required"),
  email: Yup.string()
    .trim()
    .email("Invalid email address")
    .required("Email is required"),
  password: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must contain at least one uppercase letter, one lowercase letter, one numeric character, and one special character"
    )
    .required("Password is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("password"), undefined], "Passwords must match")
    .required("Confirm Password is required"),
});
