import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { login, signup } from "../services/authService";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

// Define the shape of the authentication context
interface AuthContextProps {
  isAuthenticated: boolean;
  setIsAuthenticated: (value: boolean) => void;
  signin: (payload: any) => void;
  logout: () => void;
  register: (payload: any, user_role_id: any) => void;
}

// Create the authentication context with an initial state
const AuthContext = createContext<AuthContextProps | undefined>(undefined);

// Define a custom hook to access the authentication context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Define the AuthProvider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const token = localStorage.getItem("token");

  useEffect(() => {
    if (token) {
      setIsAuthenticated(true);
    }
  }, [token]);

  const signin = async (payload: any) => {
    const response: any = await login(payload);
    if (response?.access_token) {
      setIsAuthenticated(true);
    }
    return response;
  };

  const register = async (payload: any, user_role_id: any) => {
    const response: any = await signup({
      ...payload,
      user_role_id: user_role_id,
    });
    return response;
  };

  const logout = () => {
    // Perform logout logic
    setIsAuthenticated(false);
    localStorage.removeItem("token");
    localStorage.removeItem("notifications");
  };

  const contextValue: AuthContextProps = {
    isAuthenticated,
    setIsAuthenticated,
    signin,
    logout,
    register,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};
