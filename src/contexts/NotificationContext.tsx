import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';

// Notification interface
interface Notification {
  id: string;
  message: string;
  read: boolean;
  timestamp: number;
  status?: string;
  status_code?: string;
  execution_id?: string;
  execution_type?: string;
  execution_status?: string;
}

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Notification) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearAllNotifications: () => void;
  unreadCount: number;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Load initial notifications from localStorage
  useEffect(() => {
    const storedNotifications = localStorage.getItem("notifications");
    if (storedNotifications) {
      try {
        const parsedNotifications = JSON.parse(storedNotifications);
        // Convert any old format notifications to the new format with read status
        const formattedNotifications = Array.isArray(parsedNotifications)
          ? parsedNotifications.map((item) => {
              // Handle the case of stored string messages
              const notificationData =
                typeof item === "string" ? JSON.parse(item) : item;

              return {
                id:
                  notificationData.id ||
                  `notification-${Date.now()}-${Math.random()
                    .toString(36)
                    .substring(2, 11)}`,
                message:
                  typeof item === "string"
                    ? item
                    : JSON.stringify(notificationData),
                read: notificationData.read || false,
                timestamp: notificationData.timestamp || Date.now(),
                ...notificationData,
              };
            })
          : [];
        setNotifications(formattedNotifications);
      } catch (error) {
        console.error("Error loading notifications from localStorage:", error);
      }
    }
  }, []);

  // Listen for storage events (for cross-tab synchronization)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'notifications' && e.newValue) {
        try {
          const updatedNotifications = JSON.parse(e.newValue);
          setNotifications(updatedNotifications);
        } catch (error) {
          console.error('Error parsing notifications from storage event:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Add a new notification
  const addNotification = useCallback((notification: Notification) => {
    setNotifications((prevNotifications) => {
      const updatedNotifications = [notification, ...prevNotifications];
      localStorage.setItem('notifications', JSON.stringify(updatedNotifications));
      return updatedNotifications;
    });
  }, []);

  // Mark a notification as read
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications((prevNotifications) => {
      const updatedNotifications = prevNotifications.map((notification) =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      );

      // Update localStorage with the new read status
      localStorage.setItem(
        "notifications",
        JSON.stringify(updatedNotifications)
      );
      return updatedNotifications;
    });
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    setNotifications((prevNotifications) => {
      const updatedNotifications = prevNotifications.map((notification) => ({
        ...notification,
        read: true,
      }));

      // Update localStorage with all notifications marked as read
      localStorage.setItem(
        "notifications",
        JSON.stringify(updatedNotifications)
      );
      return updatedNotifications;
    });
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
    localStorage.removeItem("notifications");
  }, []);

  const value = {
    notifications,
    addNotification,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    unreadCount: notifications.filter((notification) => !notification.read).length,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationContext;
