// AppContext.js
import { createContext, useContext, useEffect, useRef, useState } from "react";
import useFetchLinkedServices from "../hooks/useFetchLinkedServices";
import useFetchConnectionKeysAll from "../hooks/useFetchConnectionKeysAll";
import useFetchResourcesByIdMultipleIds from "../hooks/useFetchResourcesByMultipleIds";
import useFetchAllResources from "../hooks/useFetchAllResources";
import useFetchResourceByResourceId from "../hooks/useFetchResourceByResourceId";
import AceEditor from "react-ace";
import useFetchRuleById from "../hooks/useFetchRuleById";
import useFetchResourceColumnsByMultipleIds from "../hooks/useFetchResourceColumnsByMultipleIds";

// import { IResourceData } from "../types/resource";

interface RuleResourceContextProps {
  availColumns: string[];
  setAvailColumns: React.Dispatch<React.SetStateAction<any>>;
  availColumnsWithResourceDetail: { queryType: string; data: any[] } | null;
  setAvailColumnsWithResourceDetail: React.Dispatch<
    React.SetStateAction<{ queryType: string; data: any[] } | null>
  >;

  globalVariables: any;
  setGlobalVariables: React.Dispatch<React.SetStateAction<any>>;
  tempGlobalVariables: any;
  setTempGlobalVariables: React.Dispatch<React.SetStateAction<any>>;
  temporaryQueryValue: any;
  setTemporaryQueryValue: React.Dispatch<React.SetStateAction<any>>;
  queryBuilderTempValue: any;
  setQueryBuilderTempValue: React.Dispatch<React.SetStateAction<any>>;
  type: any;
  setType: React.Dispatch<React.SetStateAction<any>>;
  linkedServicesData: any;
  setLinkedServicesData: React.Dispatch<React.SetStateAction<any>>;
  fetchedConnectionKeys: any;
  setFetchedConnectionKeys: React.Dispatch<React.SetStateAction<any>>;
  fetchedAllConnectionKeys: any;
  setFetchedAllConnectionKeys: React.Dispatch<React.SetStateAction<any>>;
  isLoading: any;
  setIsLoading: any;
  resourceData: any;
  setResourceData: React.Dispatch<React.SetStateAction<any>>;
  resourceIds: any;
  setResourceIds: any;
  allResourcesData: any;
  setAllResourcesData: React.Dispatch<React.SetStateAction<any>>;
  resourceDetailData: any;
  setResourceDetailData: React.Dispatch<React.SetStateAction<any>>;
  currentResourceId: string | number;
  setCurrentResourceId: React.Dispatch<React.SetStateAction<string | number>>;
  editorRef: any;
  currentRuleId: string;
  setCurrentRuleId: React.Dispatch<React.SetStateAction<string | number>>;
  ruleData: any;
  setRuleData: React.Dispatch<React.SetStateAction<any>>;
  resourceColIds: any;
  setResourceColIds: React.Dispatch<React.SetStateAction<any>>;
  resourceColumnsData: any;
  setResourceColumnsData: React.Dispatch<React.SetStateAction<any>>;
  showAggregatedColumns: any;
  setShowAggregatedColumns: React.Dispatch<React.SetStateAction<any>>;
  aggregatedAvailColumns: any;
  setAggregatedAvailColumns: React.Dispatch<React.SetStateAction<any>>;
  setIsResourceEdit: React.Dispatch<React.SetStateAction<any>>;
  isResourceEdit: any;
  viewInlineVariables: any;
  setViewInlineVariables: React.Dispatch<React.SetStateAction<any>>;
  fileProcessingData: any;
  setFileProcessingData: React.Dispatch<React.SetStateAction<any>>;
}

const RuleResourceContext = createContext<RuleResourceContextProps | undefined>(
  undefined
);

const RuleResourceProvider = ({ children }: any) => {
  const [availColumns, setAvailColumns] = useState<string[]>([]);
  const [availColumnsWithResourceDetail, setAvailColumnsWithResourceDetail] =
    useState<{ queryType: string; data: any[] } | null>(null);

  const [globalVariables, setGlobalVariables] = useState<any>({});
  const [tempGlobalVariables, setTempGlobalVariables] = useState({});
  const [resourceIds, setResourceIds] = useState<any[]>([]);
  const [resourceColIds, setResourceColIds] = useState<any[]>([]);
  const [queryBuilderTempValue, setQueryBuilderTempValue] = useState("");
  const [temporaryQueryValue, setTemporaryQueryValue] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentResourceId, setCurrentResourceId] = useState<string | number>(
    0
  );
  const [type, setType] = useState<any>("");
  const [currentRuleId, setCurrentRuleId] = useState<any>(0);
  const editorRef = useRef<AceEditor | null>(null);

  const [isResourceEdit, setIsResourceEdit] = useState<string>("");
  const [viewInlineVariables, setViewInlineVariables] = useState<any>({
    rule_variables: {
      filterRule: null,
      adhoc_queries: null,
    },
    resource_variables: [],
    comparison_research_query_variables: [],
  });

  const [linkedServicesData, setLinkedServicesData] = useState<any>([]);
  const [fetchedConnectionKeys, setFetchedConnectionKeys] = useState<any>([]);
  const [fetchedAllConnectionKeys, setFetchedAllConnectionKeys] = useState<any>(
    []
  );
  const [fileProcessingData, setFileProcessingData] = useState<any>([]);
  const [resourceData, setResourceData] = useState<any>([]);
  const [allResourcesData, setAllResourcesData] = useState<any>([]);
  const [resourceDetailData, setResourceDetailData] = useState<any>({});
  const [ruleData, setRuleData] = useState<any>({});
  const [resourceColumnsData, setResourceColumnsData] = useState<any>([]);
  const [showAggregatedColumns, setShowAggregatedColumns] =
    useState<any>(false);
  const [aggregatedAvailColumns, setAggregatedAvailColumns] = useState<any>([]);

  const ruleResourceContextValue = {
    availColumns,
    setAvailColumns,
    availColumnsWithResourceDetail,
    setAvailColumnsWithResourceDetail,
    globalVariables,
    setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    temporaryQueryValue,
    setTemporaryQueryValue,
    type,
    setType,
    linkedServicesData,
    setLinkedServicesData,
    fetchedConnectionKeys,
    setFetchedConnectionKeys,
    fetchedAllConnectionKeys,
    setFetchedAllConnectionKeys,
    isLoading,
    setIsLoading,
    resourceData,
    setResourceData,
    resourceIds,
    setResourceIds,
    allResourcesData,
    setAllResourcesData,
    resourceDetailData,
    setResourceDetailData,
    currentResourceId,
    setCurrentResourceId,
    editorRef,
    currentRuleId,
    setCurrentRuleId,
    ruleData,
    setRuleData,
    resourceColumnsData,
    setResourceColumnsData,
    showAggregatedColumns,
    setShowAggregatedColumns,
    aggregatedAvailColumns,
    setAggregatedAvailColumns,
    resourceColIds,
    setResourceColIds,
    isResourceEdit,
    setIsResourceEdit,
    setViewInlineVariables,
    viewInlineVariables,
    fileProcessingData,
    setFileProcessingData,
  };

  return (
    <RuleResourceContext.Provider value={ruleResourceContextValue}>
      {children}
    </RuleResourceContext.Provider>
  );
};

const useRuleResourceContext = () => {
  const context = useContext(RuleResourceContext);
  if (!context) {
    throw new Error(
      "useRuleResourceContext must be used within a RuleResourceProvider"
    );
  }
  return context;
};

export { RuleResourceProvider, useRuleResourceContext };
