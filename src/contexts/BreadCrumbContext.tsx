// BreadCrumbContext.js
import { createContext, useContext, useState } from "react";

interface CurrentPage {
  name: string;
  id: number | null;
  url?: string;
}

interface BreadCrumbContextProps {
  currentBreadcrumbPage: CurrentPage;
  setCurrentBreadcrumbPage: React.Dispatch<React.SetStateAction<any>>;
}

const BreadCrumbContext = createContext<BreadCrumbContextProps | undefined>(
  undefined
);

const BreadCrumbProvider = ({ children }: any) => {
  const [currentBreadcrumbPage, setCurrentBreadcrumbPage] = useState({
    name: "",
    id: null,
    url: "",
  });

  const BreadCrumbContextValue = {
    currentBreadcrumbPage,
    setCurrentBreadcrumbPage,
  };

  return (
    <BreadCrumbContext.Provider value={BreadCrumbContextValue}>
      {children}
    </BreadCrumbContext.Provider>
  );
};

const useBreadCrumbContext = () => {
  const context = useContext(BreadCrumbContext);
  if (!context) {
    throw new Error(
      "useBreadCrumbContext must be used within a BreadCrumbProvider"
    );
  }
  return context;
};

export { BreadCrumbProvider, useBreadCrumbContext };
