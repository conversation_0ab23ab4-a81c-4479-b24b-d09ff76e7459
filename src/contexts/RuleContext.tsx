// AppContext.js
import { createContext, useContext, useEffect, useState } from "react";

import { IResourceData } from "../types/resource";

interface RuleContextProps {
  customFilters: any;
  setCustomFilters: React.Dispatch<React.SetStateAction<any>>;
  selectedResourceIds: any;
  setSelectedResourceIds: React.Dispatch<React.SetStateAction<any>>;
  allVariablesList: any;
  setAllVariablesList: React.Dispatch<React.SetStateAction<any>>;
  multipleResourcesData: any;
  setMultipleResourcesData: React.Dispatch<React.SetStateAction<any>>;
  customComparisonRules: any;
  setCustomComparisonRules: React.Dispatch<React.SetStateAction<any>>;
  toleranceData: any;
  setToleranceData: React.Dispatch<React.SetStateAction<any>>;
  selectedResourceColumns: any;
  setSelectedResourceColumns: React.Dispatch<React.SetStateAction<any>>;
  showQryModal: any;
  setShowQryModal: React.Dispatch<React.SetStateAction<any>>;
}

const RuleContext = createContext<RuleContextProps | undefined>(undefined);

interface FileDataItem {
  id: number;
  custom_validations: any;
}

const RuleProvider = ({ children }: any) => {
  //for pagination default values and states

  const [customFilters, setCustomFilters] = useState<any>([]);
  const [selectedResourceIds, setSelectedResourceIds] = useState<any>([]);
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [multipleResourcesData, setMultipleResourcesData] = useState<
    IResourceData[] | null
  >([]);
  const [customComparisonRules, setCustomComparisonRules] = useState<any[]>([]);
  const [toleranceData, setToleranceData] = useState<any>({
    tolerance_type: "percentage",
    tolerance_value: null,
    fallback_value: null,
  });
  const [selectedResourceColumns, setSelectedResourceColumns] = useState<any>(
    []
  );
  const [showQryModal, setShowQryModal] = useState<any>(false);

  const ruleContextValue = {
    customFilters,
    setCustomFilters,
    selectedResourceIds,
    setSelectedResourceIds,
    allVariablesList,
    setAllVariablesList,
    multipleResourcesData,
    setMultipleResourcesData,
    customComparisonRules,
    setCustomComparisonRules,
    toleranceData,
    setToleranceData,
    selectedResourceColumns,
    setSelectedResourceColumns,
    showQryModal,
    setShowQryModal,
  };

  return (
    <RuleContext.Provider value={ruleContextValue}>
      {children}
    </RuleContext.Provider>
  );
};

const useRuleContext = () => {
  const context = useContext(RuleContext);
  if (!context) {
    throw new Error("useRuleContext must be used within a RuleProvider");
  }
  return context;
};

export { RuleProvider, useRuleContext };
