// AppContext.js
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";

import useFetchLinkedServices from "../hooks/useFetchLinkedServices";
import useFetchFileProcessing from "../hooks/useFetchFileProcessing";
import useFetchConnectionKey from "../hooks/useFetchConnectionKey";
import useFetchResourceColumnsByDomain from "../hooks/useFetchResourceColumnsByDomain";
import useFetchResourceColumnsById from "../hooks/useFetchResourceColumnsById";
import useFetchResources from "../hooks/useFetchResources";
import { useParams } from "react-router";
import { useSearchParams } from "react-router-dom";
import useFetchResourceById from "../hooks/useFetchResourceById";
import { cloneResource } from "../services/resourcesService";
import { useNavigate } from "react-router-dom";
import useFetchDomains from "../hooks/useFetchDomains";
import useFetchConnectionKeysAll from "../hooks/useFetchConnectionKeysAll";
import { useToast } from "../services/utils";

interface FormData {
  id: number;
  resource_name: string;
  resource_type: string;
  resource_prefix: string;
  domain_id: number | null;
  resource_location: string;
  resource_path: string;
  aggregation_type: string;
  filter_rules: any[];
  additional_resource_data: [];
  // additional_properties: {
  resource_definition: {
    api_definition: any;
  };
  // }
}

interface ResourceContextProps {
  formData: any;
  setFormData: React.Dispatch<React.SetStateAction<any>>;
  resourceId: any;
  setResourceId: React.Dispatch<React.SetStateAction<any>>;
  errors: any;
  setErrors: React.Dispatch<React.SetStateAction<any>>;
  crossFieldErrors: any;
  setCrossFieldErrors: React.Dispatch<React.SetStateAction<any>>;
  isLoading: any;
  setIsLoading: React.Dispatch<React.SetStateAction<any>>;
  currentDomainId: any;
  setCurrentDomainId: React.Dispatch<React.SetStateAction<any>>;
  resourceColumnId: any;
  setResourceColumnId: React.Dispatch<React.SetStateAction<any>>;
  linkedServicesData: any;
  setLinkedServicesData: React.Dispatch<React.SetStateAction<any>>;
  fileProcessingData: any;
  setFileProcessingData: React.Dispatch<React.SetStateAction<any>>;
  connectionKeysData: any;
  setConnectionKeysData: React.Dispatch<React.SetStateAction<any>>;
  resourceColumns: any;
  setResourceColumns: React.Dispatch<React.SetStateAction<any>>;
  fetchedResourceColumnData: any;
  setFetchedResourceColumnData: React.Dispatch<React.SetStateAction<any>>;
  allResourcesData: any;
  setAllResourcesData: React.Dispatch<React.SetStateAction<any>>;
  allResourcesAdditionalData: any;
  setAllResourcesAdditionalData: React.Dispatch<React.SetStateAction<any>>;
  resourceData: any;
  setResourceData: React.Dispatch<React.SetStateAction<any>>;
  resourceColumnDataObj: any;
  setResourceColumnDataObj: React.Dispatch<React.SetStateAction<any>>;
  resourceColumnData: any;
  setResourceColumnData: React.Dispatch<React.SetStateAction<any>>;
  currentResourceColumnId: any;
  setCurrentResourceColumnId: React.Dispatch<React.SetStateAction<any>>;
  editResourceId: any;
  currentResourceId: any;
  setCurrentResourceId: React.Dispatch<React.SetStateAction<any>>;
  openValidationDialog: any;
  setOpenValidationDialog: React.Dispatch<React.SetStateAction<any>>;
  handleCloseValidationDialog: any;
  selectedRowId: any;
  setSelectedRowId: React.Dispatch<React.SetStateAction<any>>;
  validationData: any;
  setValidationData: React.Dispatch<React.SetStateAction<any>>;
  isDailogEdit: any;
  setIsDailogEdit: React.Dispatch<React.SetStateAction<any>>;
  onSaveCustomValidation: any;
  fileData: any;
  setFileData: React.Dispatch<React.SetStateAction<any>>;
  resourceColumnFileData: any;
  setResourceColumnFileData: React.Dispatch<React.SetStateAction<any>>;
  pageContext: any;
  setPageContext: React.Dispatch<React.SetStateAction<any>>;
  dailogEditIndex: any;
  setDailogEditIndex: React.Dispatch<React.SetStateAction<any>>;
  // CDMColumns: any;
  // SetCDMColumns: React.Dispatch<React.SetStateAction<any>>;
  openDerivedColumnDialog: any;
  setOpenDerivedColumnDialog: React.Dispatch<React.SetStateAction<any>>;
  handleCloseDerivedColumnDialog: any;
  derivedColumnData: any;
  setDerivedColumnData: React.Dispatch<React.SetStateAction<any>>;
  derivedModalType: any;
  setDerivedModalType: React.Dispatch<React.SetStateAction<any>>;
  onSaveDerivedColumn: any;
  openReferenceDialog: any;
  setOpenReferenceDialog: any;
  handleCloseReferenceDialog: any;
  referenceData: any;
  setReferenceData: any;
  onSaveReferenceDialog: any;
  resourcesData: any;
  setResourcesData: React.Dispatch<React.SetStateAction<any>>;
  // isEditAdditionalResource: boolean;
  // setIsEditAdditionalResource: React.Dispatch<React.SetStateAction<any>>;
  cloneResourceDialog: boolean;
  setCloneResourceDialog: React.Dispatch<React.SetStateAction<boolean>>;
  cloneResourceData: any;
  setCloneResourceData: React.Dispatch<React.SetStateAction<any>>;
  handleCloseCloneResourceDialog: any;
  onSaveCloneResource: any;
  baseResourceColumns: any;
  setBaseResourceColumns: React.Dispatch<React.SetStateAction<any>>;
  domainsData: any;
  setDomainsData: React.Dispatch<React.SetStateAction<any>>;
  fetchedConnectionKeys: any;
  setFetchedConnectionKeys: React.Dispatch<React.SetStateAction<any>>;
  refetchData: any;
  fetchKey: any;
}

const ResourceContext = createContext<ResourceContextProps | undefined>(
  undefined
);

interface FileDataItem {
  id: number;
  custom_validations: any;
}

const ResourceProvider = ({ children }: any) => {
  const { id }: any = useParams();
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [formData, setFormData] = useState<FormData>({
    id: 1,
    resource_name: "",
    resource_type: "",
    resource_prefix: "",
    domain_id: null,
    resource_location: "local",
    resource_path: "",
    aggregation_type: "flat",
    filter_rules: [],
    additional_resource_data: [],
    // additional_properties: {
    resource_definition: {
      api_definition: {
        method: "get",
        content_type: "application/json",
      },
    },
    // }
  });
  const [resourceId, setResourceId] = useState<any>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [errors, setErrors] = useState<{ [key: string]: any }>({
    resource_name: "",
    resource_type: "",
    resource_prefix: "",
    domain_id: "",
    resource_location: "",
    resource_path: "",
    aggregation_type: "",
    filter_rules: [],
  });
  const [crossFieldErrors, setCrossFieldErrors] = useState<{
    [key: string]: any;
  }>({});

  const { domainId, id: editResourceId } = useParams();
  const [searchParams] = useSearchParams();
  // const [linkedServicesData] = useFetchLinkedServices({ setIsLoading });
  // const [fileProcessingData] = useFetchFileProcessing({ setIsLoading });
  const [currentResourceId, setCurrentResourceId] = useState<any>(null);
  const [resourceColumnData, setResourceColumnData] = useState<any>(null);
  const [selectedRowId, setSelectedRowId] = useState<any>(null);
  const [isDailogEdit, setIsDailogEdit] = useState<boolean>(false);
  const [fileData, setFileData] = useState<FileDataItem[]>([]);
  const [fetchKey, setFetchKey] = useState(0);
  const [resourceColumnFileData, setResourceColumnFileData] = useState<
    FileDataItem[]
  >([]);
  const [pageContext, setPageContext] = useState("resource");
  // const [CDMColumns, SetCDMColumns] = useState<string[]>([]);
  const [dailogEditIndex, setDailogEditIndex] = useState<number>(null!);
  const [openDerivedColumnDialog, setOpenDerivedColumnDialog] =
    useState<boolean>(false);
  const [derivedModalType, setDerivedModalType] = useState<any>("add");
  const [openReferenceDialog, setOpenReferenceDialog] =
    useState<boolean>(false);
  const [derivedColumnData, setDerivedColumnData] = useState<any>({
    query: "",
    column_name: "",
  });
  const [validationData, setValidationData] = useState<any>({
    name: "",
    filter_rule: "",
    expression: "",
  });
  const [referenceData, setReferenceData] = useState<any>({
    source: "internal",
    source_type: "",
    use_translation: false,
  });

  const [openValidationDialog, setOpenValidationDialog] =
    useState<boolean>(false);
  const [currentDomainId, setCurrentDomainId] = useState<any>(
    Number(domainId) || null
  );
  // const [resourceColumns] = useFetchResourceColumnsByDomain({
  //   currentDomainId,
  //   setIsLoading,
  // });
  const [currentResourceColumnId, setCurrentResourceColumnId] = useState<any>();
  const [resourceColumnId, setResourceColumnId] = useState<any>();
  const [resourcesData, setResourcesData] = useState<any>([]);
  // const [isEditAdditionalResource, setIsEditAdditionalResource] =
  //   useState<boolean>(false);
  const [cloneResourceDialog, setCloneResourceDialog] =
    useState<boolean>(false);
  const [cloneResourceData, setCloneResourceData] = useState<any>({
    resource_name: "",
    resource_type: "",
    resource_prefix: "",
  });
  const [baseResourceColumns, setBaseResourceColumns] = useState<any>([]);

  const [linkedServicesData, setLinkedServicesData] = useState<any[]>([]);
  const [fileProcessingData, setFileProcessingData] = useState<any[]>([]);
  const [resourceColumns, setResourceColumns] = useState<any[]>([]);
  const [connectionKeysData, setConnectionKeysData] = useState<any[]>([]);
  const [fetchedConnectionKeys, setFetchedConnectionKeys] = useState<any[]>([]);
  const [fetchedResourceColumnData, setFetchedResourceColumnData] = useState<
    any[]
  >([]);
  const [allResourcesData, setAllResourcesData] = useState<any[]>([]);
  const [resourceData, setResourceData] = useState<any[]>([]);
  const [allResourcesAdditionalData, setAllResourcesAdditionalData] = useState<
    any[]
  >([]);
  const [resourceColumnDataObj, setResourceColumnDataObj] = useState<any[]>([]);
  const [domainsData, setDomainsData] = useState<any[]>([]);

  const getCurrentData = () => {
    return pageContext === "resourceColumn" ? resourceColumnFileData : fileData;
  };
  const getCurrentSetData = () => {
    return pageContext === "resourceColumn"
      ? setResourceColumnFileData
      : setFileData;
  };

  useEffect(() => {
    setCurrentResourceColumnId(
      Number(searchParams.get("resourceColumn")) || ""
    );
    setResourceColumnId(Number(searchParams.get("resourceColumn")) || "");
  }, [searchParams.get("resourceColumn")]);
  // const [connectionKeysData] = useFetchConnectionKey({
  //   filterKeys: (formData as any)?.linked_service?.connection_details
  //     ?.connection_keys,
  //   setIsLoading,
  // });
  // const [fetchedConnectionKeys] = useFetchConnectionKeysAll({ setIsLoading });

  // const [fetchedresourceColumnData] = useFetchResourceColumnsById({
  //   resourceColumnId,
  //   setIsLoading,
  // });
  // const [allResourcesData] = useFetchResources({
  //   currentDomainId,
  //   setIsLoading,
  // });
  // let [resourceData, resourceColumnDataObj] = useFetchResourceById({
  //   currentResourceId,
  //   currentResourceColumnId,
  //   setIsLoading,
  //   fetchKey,
  // });
  const refetchData = useCallback(() => {
    setFetchKey((prevKey) => prevKey + 1);
  }, []);
  // const [domainsData] = useFetchDomains({ setIsLoading });

  useEffect(() => {
    if (resourceData) {
      setResourcesData(resourceData);
    }
  }, [resourceData]);
  //functions starts here
  const handleCloseValidationDialog = () => {
    setOpenValidationDialog(false);
    setSelectedRowId(null);
    setValidationData({
      name: "",
      filter_rule: "",
      expression: "",
    });
    setIsDailogEdit(false);
  };

  const onSaveCustomValidation = () => {
    const currentData = getCurrentData();
    const setCurrentData = getCurrentSetData();
    let conditions = {
      name: validationData.name || "",
      filter_rule: validationData.filter_rule || "",
      expression: validationData.expression || "",
      // inline_variables: inlineVariables || [],
    };
    let conditionsArray: any = [];
    let prevValidationData = currentData?.find(
      (file: any) => file.id === selectedRowId
    )?.custom_validations;

    if (isDailogEdit) {
      conditionsArray = conditions;
    } else {
      conditionsArray = [conditions];
    }
    if (prevValidationData?.length > 0 && !isDailogEdit) {
      conditionsArray = [...prevValidationData, ...conditionsArray];
    }
    if (isDailogEdit) {
      let newStateValidationData = prevValidationData;
      newStateValidationData[dailogEditIndex] = conditionsArray;
      conditionsArray = newStateValidationData;
    }
    conditionsArray?.length > 0 &&
      setCurrentData((prev) => {
        return prev.map((item) => {
          if (item.id === selectedRowId) {
            item.custom_validations = conditionsArray;
          }
          return item;
        });
      });
    setOpenValidationDialog(false);
    setSelectedRowId(null);
    setIsDailogEdit(false);
  };

  const handleCloseDerivedColumnDialog = () => {
    setOpenDerivedColumnDialog(false);
    setSelectedRowId(null);
    setDerivedColumnData({});
    setDerivedModalType("");
  };

  const onSaveDerivedColumn = () => {
    const currentData = getCurrentData();
    const setCurrentData = getCurrentSetData();
    let newColumn: any = {};
    if (
      derivedColumnData?.column_name?.trim() &&
      derivedColumnData?.query?.trim()
    ) {
      if (derivedModalType === "edit") {
        const derivedData = currentData.find(
          (item: any) => item.id === selectedRowId
        );
        newColumn = {
          ...derivedData,
          column_name: derivedColumnData?.column_name || "",
          derived_column_definition: {
            required_columns: derivedColumnData?.columns || [],
            sql_expression: derivedColumnData?.query || "",
            // inline_variables: inlineVariables || [],
          },
        };
        setCurrentData((prev: any) => {
          return prev.map((item: any) => {
            if (item.id === selectedRowId) {
              item = newColumn;
            }
            return item;
          });
        });
      } else {
        newColumn = {
          id: currentData?.length,
          domain_column: "",
          column_name: derivedColumnData?.column_name || "",
          datatype: "",
          is_mandatory: "",
          is_reference: false,
          data_format: "",
          key: "",
          action: "",
          is_derived: true,
          derived_column_definition: {
            required_columns: derivedColumnData?.columns || [],
            sql_expression: derivedColumnData?.query || "",
            // inline_variables: inlineVariables || [],
          },
          severity_level: "Low",
          is_active: true,
        };

        setCurrentData((prev: any) => {
          return [...prev, newColumn];
        });
      }
    }
    setOpenDerivedColumnDialog(false);
    setDerivedModalType("add");
    setDerivedColumnData({});
  };

  const handleCloseReferenceDialog = () => {
    setOpenReferenceDialog(false);
    setReferenceData({});
    setSelectedRowId(null);
  };
  const handleCloseCloneResourceDialog = () => {
    setCloneResourceDialog(false);
    setCloneResourceData({});
  };
  const onSaveReferenceDialog = () => {
    const setCurrentData = getCurrentSetData();
    setCurrentData((prev) => {
      return prev.map((item: any) => {
        if (item.id === selectedRowId) {
          item.is_reference = true;
          item.reference_column_definition = {
            // inline_variables: inlineVariables || [],
            source: referenceData.source,
            source_type: referenceData.source_type,
            use_translation: referenceData.use_translation,
            connection_key: referenceData?.connection_key?.id,
            linked_service_id: referenceData?.linked_service?.id,
            linked_service_code: referenceData?.linked_service?.code,
            connection_key_code: referenceData?.connection_key?.code,
          };
        }
        setOpenReferenceDialog(false);
        return item;
      });
    });
  };
  const onSaveCloneResource = () => {
    const reqBody = {
      resource_id: id,
      resource_name: cloneResourceData.resource_name,
      resource_type: cloneResourceData.resource_type,
      resource_prefix: cloneResourceData.resource_prefix,
      code: cloneResourceData.code,
    };
    cloneResource(reqBody)
      .then((response) => {
        showToast("Resource clone successfully!", "success");
        navigate(`/resource/all/view/${response?.id}`);
      })
      .catch((error) => {
        showToast(`Cannot create clone resource`, "error");
      });
    handleCloseCloneResourceDialog();
  };
  const resourceContextValue = {
    formData,
    setFormData,
    resourceId,
    setResourceId,
    errors,
    setErrors,
    crossFieldErrors,
    setCrossFieldErrors,
    isLoading,
    setIsLoading,
    linkedServicesData,
    setLinkedServicesData,
    fileProcessingData,
    setFileProcessingData,
    connectionKeysData,
    setConnectionKeysData,
    resourceColumns,
    setResourceColumns,
    fetchedResourceColumnData,
    setFetchedResourceColumnData,
    allResourcesData,
    setAllResourcesData,
    allResourcesAdditionalData,
    setAllResourcesAdditionalData,
    currentDomainId,
    resourceColumnId,
    setCurrentDomainId,
    setResourceColumnId,
    resourceData,
    setResourceData,
    resourceColumnDataObj,
    setResourceColumnDataObj,
    setResourceColumnData,
    resourceColumnData,
    currentResourceColumnId,
    setCurrentResourceColumnId,
    editResourceId,
    currentResourceId,
    setCurrentResourceId,
    openValidationDialog,
    setOpenValidationDialog,
    handleCloseValidationDialog,
    selectedRowId,
    setSelectedRowId,
    validationData,
    setValidationData,
    isDailogEdit,
    setIsDailogEdit,
    onSaveCustomValidation,
    fileData,
    setFileData,
    resourceColumnFileData,
    setResourceColumnFileData,
    pageContext,
    setPageContext,
    dailogEditIndex,
    setDailogEditIndex,
    openDerivedColumnDialog,
    setOpenDerivedColumnDialog,
    handleCloseDerivedColumnDialog,
    derivedColumnData,
    setDerivedColumnData,
    derivedModalType,
    setDerivedModalType,
    onSaveDerivedColumn,
    openReferenceDialog,
    setOpenReferenceDialog,
    handleCloseReferenceDialog,
    referenceData,
    setReferenceData,
    onSaveReferenceDialog,
    resourcesData,
    setResourcesData,
    // isEditAdditionalResource,
    // setIsEditAdditionalResource,
    cloneResourceDialog,
    setCloneResourceDialog,
    cloneResourceData,
    setCloneResourceData,
    handleCloseCloneResourceDialog,
    onSaveCloneResource,
    baseResourceColumns,
    setBaseResourceColumns,
    domainsData,
    setDomainsData,
    fetchedConnectionKeys,
    setFetchedConnectionKeys,
    refetchData,
    fetchKey,
  };

  return (
    <ResourceContext.Provider value={resourceContextValue}>
      {children}
    </ResourceContext.Provider>
  );
};

const useResourceContext = () => {
  const context = useContext(ResourceContext);
  if (!context) {
    throw new Error(
      "useResourceContext must be used within a ResourceProvider"
    );
  }
  return context;
};

export { ResourceProvider, useResourceContext };
