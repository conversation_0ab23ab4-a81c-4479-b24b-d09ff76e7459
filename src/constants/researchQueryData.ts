export const researchQueryResourcesData = [
    {
        "domain_id": 5,
        "additional_properties": {
            "resource_definition": {
                "type": "local",
                "local_definition": {
                    "resource_path": "E:\\Data_Files\\SampleFiles\\Research_query_files",
                    "file_name": "GWP_Position_202212124_Change_int.xlsx",
                    "column_delimiter": "|",
                    "connection_key": 28,
                    "connection_key_code": "connection_key_28"
                },
                "blob_definition": null,
                "sql_definition": null,
                "sftp_definition": null,
                "api_definition": {
                    "request_timeout": 0
                },
                "resource_path": "E:\\Data_Files\\SampleFiles\\Research_query_files",
                "file_name": "GWP_Position_202212124_Change_int.xlsx",
                "column_delimiter": "|",
                "connection_key": 28,
                "connection_key_code": "connection_key_28"
            },
            "aggregation_properties": null,
            "resource_column_details_id": 602,
            "resource_column_details_code": "resource_913",
            "additional_resource_data": [
                {
                    "resource_id": 1408,
                    "resource_code": "resource_1408",
                    "base_column_names": [
                        "Security Code"
                    ],
                    "add_on_column_names": [
                        "FishSecID"
                    ],
                    "merge_type": "outer",
                    "generate_derived_columns_before_merge_additional_resource": null,
                    "id": 1408,
                    "resource_name": "DEMO2_ADDITIONAL_RES",
                    "resource_type": "DEMO_Add_Resource",
                    "resource_prefix": "DEMO_Add_Resource",
                    "code": "resource_1408",
                    "aggregation_type": "flat",
                    "domain_id": 5,
                    "domain_name": "Position",
                    "domain_code": "POS!",
                    "is_active": true,
                    "linked_service_id": 27,
                    "linked_service_code": "localFile1",
                    "current_url": null,
                    "file_processing_id": 4,
                    "file_processing_code": "file_processing_4",
                    "additional_properties": {
                        "resource_definition": {
                            "type": "local",
                            "local_definition": {
                                "resource_path": "E:\\Data_Files\\SampleFiles\\Research_query_files",
                                "file_name": "AdditionalResource_3.xlsx",
                                "column_delimiter": ",",
                                "connection_key": 28,
                                "connection_key_code": "connection_key_28"
                            },
                            "blob_definition": null,
                            "sql_definition": null,
                            "sftp_definition": null,
                            "api_definition": {
                                "request_timeout": 0
                            },
                            "resource_path": "E:\\Data_Files\\SampleFiles\\Research_query_files",
                            "file_name": "AdditionalResource_3.xlsx",
                            "column_delimiter": ",",
                            "connection_key": 28,
                            "connection_key_code": "connection_key_28"
                        },
                        "aggregation_properties": null,
                        "resource_column_details_id": 1090,
                        "resource_column_details_code": "resource_column_1090",
                        "additional_resource_data": [],
                        "filter_rules": [],
                        "inline_variables": {
                            "custom_val_col": "FishIndustry",
                            "var10": 4
                        }
                    },
                    "version": 14,
                    "created_by": 24,
                    "modified_by": 132,
                    "created_by_user": {
                        "email": "<EMAIL>",
                        "firstname": "R",
                        "lastname": "K",
                        "id": 24,
                        "username": "RK"
                    },
                    "modified_by_user": {
                        "email": "<EMAIL>",
                        "firstname": "JItendra",
                        "lastname": "Sankhla",
                        "id": 132,
                        "username": "jitendra_12"
                    },
                    "created_on": "2024-02-14T17:04:13",
                    "updated_on": "2024-11-25T13:15:31"
                }
            ],
            "filter_rules": [
                {
                    "name": "Total cost base filter",
                    "sql_query": "[$$column1$$] > $$var5$$"
                }
            ],
            "inline_variables": {
                "VAr5": 1500,
                "COlumn1": "Total Cost Base",
                "Var1": 1600,
                "var2": 1700,
                "var3": 1000,
                "var4": 50,
                "column4": "Market Value Base",
                "colUMN5": "Quantity",
                "column6": "Unit Cost Base",
                "source_type": "GWPPOSTYP",
                "custom_vAl_col": "FishIndustry",
                "var10": 4
            }
        },
        "domain_code": "POS!",
        "is_active": true,
        "resource_name": "DEMO_GWP",
        "domain_name": "Position!",
        "created_on": "2022-09-20T00:00:00",
        "id": 913,
        "linked_service_id": 27,
        "updated_on": "2025-04-03T04:07:16",
        "resource_type": "GWP@",
        "linked_service_code": "localFile1",
        "created_by": 3,
        "resource_prefix": "GWP@",
        "current_url": null,
        "modified_by": 24,
        "code": "resource_913",
        "file_processing_id": 4,
        "version": 40,
        "aggregation_type": "flat",
        "file_processing_code": null,
        "modified_by_user": {
            "firstname": "R",
            "lastname": "K",
            "email": "<EMAIL>",
            "username": "RK",
            "id": 24
        },
        "created_by_user": {
            "firstname": "Vis",
            "lastname": "test",
            "email": "<EMAIL>",
            "username": "vis123",
            "id": 3
        }
    },
    {
        "domain_id": 5,
        "additional_properties": {
            "resource_definition": {
                "type": "local",
                "local_definition": {
                    "resource_path": "E:\\Data_Files\\SampleFiles\\Research_query_files",
                    "file_name": "CRD_Position_202212124_Change_int.xlsx",
                    "column_delimiter": "|",
                    "connection_key": 28,
                    "connection_key_code": "connection_key_28"
                },
                "blob_definition": null,
                "sql_definition": null,
                "sftp_definition": null,
                "api_definition": {
                    "request_timeout": 0
                },
                "resource_path": "E:\\Data_Files\\SampleFiles\\Research_query_files",
                "file_name": "CRD_Position_202212124_Change_int.xlsx",
                "column_delimiter": "|",
                "connection_key": 28,
                "connection_key_code": "connection_key_28"
            },
            "aggregation_properties": null,
            "resource_column_details_id": 603,
            "resource_column_details_code": "resource_column_603",
            "additional_resource_data": [],
            "filter_rules": [],
            "inline_variables": {
                "VAr5": 1500,
                "COlumn1": "Total Cost Base",
                "Var1": 1600,
                "var2": 1700,
                "var3": 1000,
                "var4": 50,
                "column4": "Market Value Base",
                "colUMN5": "Quantity",
                "column6": "Unit Cost Base",
                "source_type": "GWPPOSTYP",
                "custom_vAl_col": "FishIndustry",
                "variable_value1": "MKT_VAL_SOD",
                "var10": 4
            }
        },
        "domain_code": "POS!",
        "is_active": true,
        "resource_name": "DEMO_CRD",
        "domain_name": "Position!",
        "created_on": "2022-09-21T00:00:00",
        "id": 914,
        "linked_service_id": 27,
        "updated_on": "2025-03-04T09:05:23",
        "resource_type": "CRD",
        "linked_service_code": "localFile1",
        "created_by": 3,
        "resource_prefix": "CRD",
        "current_url": null,
        "modified_by": 24,
        "code": "resource_914",
        "file_processing_id": 4,
        "version": 13,
        "aggregation_type": "flat",
        "file_processing_code": null,
        "modified_by_user": {
            "firstname": "R",
            "lastname": "K",
            "email": "<EMAIL>",
            "username": "RK",
            "id": 24
        },
        "created_by_user": {
            "firstname": "Vis",
            "lastname": "test",
            "email": "<EMAIL>",
            "username": "vis123",
            "id": 3
        }
    },
    {
        "domain_id": 5,
        "additional_properties": {
            "resource_definition": {
                "type": "local",
                "local_definition": {
                    "resource_path": "E:\\Data_Files\\SampleFiles\\Research_query_files",
                    "file_name": "FactSet_Position_202212124_Change_int.xlsx",
                    "column_delimiter": "|",
                    "connection_key": 28,
                    "connection_key_code": "connection_key_28"
                },
                "blob_definition": null,
                "sql_definition": null,
                "sftp_definition": null,
                "api_definition": {
                    "request_timeout": 0
                },
                "resource_path": "E:\\Data_Files\\SampleFiles\\Research_query_files",
                "file_name": "FactSet_Position_202212124_Change_int.xlsx",
                "column_delimiter": "|",
                "connection_key": 28,
                "connection_key_code": "connection_key_28"
            },
            "aggregation_properties": null,
            "resource_column_details_id": 604,
            "resource_column_details_code": "resource_column_604",
            "additional_resource_data": [],
            "filter_rules": [],
            "inline_variables": {
                "VAr5": 1500,
                "COlumn1": "Total Cost Base",
                "Var1": 1600,
                "var2": 1700,
                "var3": 1000,
                "var4": 50,
                "column4": "Market Value Base",
                "colUMN5": "Quantity",
                "column6": "Unit Cost Base",
                "source_type": "GWPPOSTYP",
                "custom_vAl_col": "FishIndustry",
                "var10": 4
            }
        },
        "domain_code": "POS!",
        "is_active": true,
        "resource_name": "DEMO_FACTSET",
        "domain_name": "Position!",
        "created_on": "2022-09-22T00:00:00",
        "id": 915,
        "linked_service_id": 27,
        "updated_on": "2024-12-10T06:03:19",
        "resource_type": "Factset",
        "linked_service_code": "localFile1",
        "created_by": 3,
        "resource_prefix": "Factset",
        "current_url": null,
        "modified_by": 50,
        "code": "resource_915",
        "file_processing_id": 4,
        "version": 7,
        "aggregation_type": "flat",
        "file_processing_code": null,
        "modified_by_user": {
            "firstname": "Naveen",
            "lastname": "Kirar",
            "email": "<EMAIL>",
            "username": "kirarnaveen@",
            "id": 50
        },
        "created_by_user": {
            "firstname": "Vis",
            "lastname": "test",
            "email": "<EMAIL>",
            "username": "vis123",
            "id": 3
        }
    }
];
