import React, { useEffect, useState } from "react";
import { Typo<PERSON>, Box, Tooltip, CircularProgress } from "@mui/material";

import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import InfoIcon from "@mui/icons-material/Info";
import { useToast } from "../../services/utils";
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
} from "@mui/lab";
import {
  IconAddOrange,
  IconDiscard,
  IconCheckCircleIconSvg,
  IconReplaceOrange,
  IconCrossCircleIconSvg,
} from "../../common/utils/icons";
import ConfirmationDialog from "../Dialogs/ConfirmationDialog";

import {
  entity_name,
  getStepStatus,
  handleNavigateToEntity,
  onAddReplaceEntity,
} from "../../services/utils/AddReplaceImportEntity";
import { useNavigate } from "react-router-dom";
const orderedKeys = [
  "domains",
  "connection_keys",
  "linked_services",
  "file_processing_attributes",
  "run_instances",
  "resource_column_details",
  "resources",
];

const ImportedAssociatedEntity = ({
  uploadedFileData,
  setUploadedFileData,
  validatedData,
}: any) => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [openConfirmation, setOpenConfirmation] = useState<boolean>(false);
  const [discardEntity, setDiscardEntity] = useState<any>({
    entityName: "",
    entityType: "",
    entityCode: "",
  });

  const [manageEntityIconColor, setManageEntityIconColor] = useState<any>({
    entityKey: "",
    domains: "not-started",
    connection_keys: "not-started",
    linked_services: "not-started",
    file_processing_attributes: "not-started",
    run_instances: "not-started",
    resource_column_details: "not-started",
    resources: "not-started",
  });
  const [btnLoader, setBtnLoader] = useState("");

  useEffect(() => {
    function mergeData(
      entities: any[],
      associatedEntities: {
        linked_services?: any[];
        connection_keys?: any[];
        domains?: any[];
        resource_column_details?: any[];
        file_processing_attributes?: any[];
        run_instances?: any[];
        resources?: any[];
      }
    ) {
      // Create a map of entities for quick lookup
      let entitiesMap: any = {};
      entities.forEach((entity: { entity_type: any; entity_id: any }) => {
        let key = `${entity.entity_type}_${entity.entity_id}`;
        entitiesMap[key] = entity;
      });

      // Helper function to process each entity type
      const processEntities = (
        entityArray: any[] | undefined,
        entityType: string
      ) => {
        entityArray?.forEach((item: { id: any; details: any }) => {
          let key = `${entityType}_${item.id}`;
          if (entitiesMap[key]) {
            item.details = entitiesMap[key];
            item.details.status = "not_started";
          }
        });
      };

      // Process each entity type
      processEntities(associatedEntities.linked_services, "linked_services");
      processEntities(associatedEntities.connection_keys, "connection_keys");
      processEntities(associatedEntities.domains, "domains");
      processEntities(
        associatedEntities.resource_column_details,
        "resource_column_details"
      );
      processEntities(
        associatedEntities.file_processing_attributes,
        "file_processing_attributes"
      );
      processEntities(associatedEntities.run_instances, "run_instances");
      processEntities(associatedEntities.resources, "resources");

      return associatedEntities;
    }

    let mergedJson: any;
    if (
      validatedData?.associated_entities_validation_result &&
      uploadedFileData?.associated_entities
    ) {
      mergedJson = mergeData(
        validatedData?.associated_entities_validation_result,
        uploadedFileData?.associated_entities
      );
    } else {
      mergedJson = uploadedFileData?.associated_entities;
    }
    setUploadedFileData((prev: any) => ({
      ...prev,
      associated_entities: mergedJson,
    }));
  }, [validatedData]);
  const handleCancel = () => {
    setOpenConfirmation(false);
  };
  const handleSave = () => {
    setUploadedFileData((prev: any) => {
      const updatedData = { ...prev };

      // Find the correct key in associated_entities
      const associatedEntityKey = Object.keys(prev.associated_entities).find(
        (key) => key === discardEntity?.entityType
      );

      if (associatedEntityKey) {
        // Filter out the object with the matching code
        if (discardEntity?.entityType === "domains") {
          updatedData.associated_entities[associatedEntityKey] =
            updatedData.associated_entities[associatedEntityKey].filter(
              (item: any) => item.domain_code !== discardEntity?.entityCode
            );
        } else {
          updatedData.associated_entities[associatedEntityKey] =
            updatedData.associated_entities[associatedEntityKey].filter(
              (item: any) => item.code !== discardEntity?.entityCode
            );
        }
      }

      return updatedData;
    });
    setOpenConfirmation(false);
  };

  const handleAddReplaceEntity = async (
    entity: any,
    actionType: string,
    entityType: string
  ) => {
    setBtnLoader(entityType);

    const response = await onAddReplaceEntity(
      entity,
      actionType,
      entityType,
      uploadedFileData?.associated_entities,
      showToast
    );

    if (response) {
      setUploadedFileData((prev: any) => {
        const updatedData = { ...prev };

        // Find the correct key in associated_entities
        const associatedEntityKey = Object.keys(prev.associated_entities).find(
          (key) => key === entityType
        );

        if (associatedEntityKey) {
          // Find the correct object in the array by matching the code
          const index = updatedData.associated_entities[
            associatedEntityKey
          ].findIndex((item: any) => item.code === entity?.code);

          if (index !== -1) {
            // Update the details of the matched object
            updatedData.associated_entities[associatedEntityKey][
              index
            ].details = {
              ...updatedData.associated_entities[associatedEntityKey][index]
                .details,
              status: "success",
              entity_id_in_target_env: response.id,
              is_already_exist: true,
            };
          }
        }

        return updatedData;
      });
    } else {
      setUploadedFileData((prev: any) => {
        const updatedData = { ...prev };

        // Find the correct key in associated_entities
        const associatedEntityKey = Object.keys(prev.associated_entities).find(
          (key) => key === entityType
        );

        if (associatedEntityKey) {
          // Find the correct object in the array by matching the code
          const index = updatedData.associated_entities[
            associatedEntityKey
          ].findIndex((item: any) => item.code === entity?.code);

          if (index !== -1) {
            // Update the details of the matched object
            updatedData.associated_entities[associatedEntityKey][
              index
            ].details = {
              ...updatedData.associated_entities[associatedEntityKey][index]
                .details,
              status: "failed",
            };
          }
        }

        return updatedData;
      });
    }
    setBtnLoader("");
  };
  useEffect(() => {
    orderedKeys.forEach((key) => {
      if (
        uploadedFileData?.associated_entities &&
        Object.keys(uploadedFileData?.associated_entities).length > 0
      ) {
        const value = uploadedFileData?.associated_entities[key];
        if (value && value.length > 0) {
          const lastEntity = value[value.length - 1];
          const failedEntity = value.find(
            (entity: { details: { status: string } }) =>
              entity?.details?.status === "failed"
          );

          if (lastEntity || failedEntity) {
            setManageEntityIconColor((prev: any) => ({
              ...prev,
              entityKey: key,
              [key]:
                lastEntity?.details?.status || failedEntity?.details?.status,
            }));
          }
        }
      }
    });
  }, [uploadedFileData, orderedKeys]);
  const getDisplayName = (entity: any, key: string) => {
    const foundEntity = entity_name.find(
      (item: { entity: string }) => item.entity === key
    );
    if (foundEntity) {
      return entity?.[foundEntity.display_name];
    }
    return key;
  };
  return (
    <Box>
      <div className="timeline">
        <Timeline position="right">
          {(!uploadedFileData?.associated_entities ||
            Object.keys(uploadedFileData?.associated_entities).length ===
              0) && (
            <Box className="no-data-avai">
              <InfoIcon />
              There are no associated component available in{" "}
              {uploadedFileData?.entity_name}
            </Box>
          )}
          {orderedKeys.map((key, index) => {
            if (uploadedFileData?.associated_entities) {
              const value = uploadedFileData?.associated_entities[key];
              // const stepStatus = getMainStepStatus(key);
              if (value && value.length > 0) {
                return (
                  <TimelineItem
                    key={key}
                    className={`status-${manageEntityIconColor[key]}`}
                    sx={{ mb: 2 }}
                  >
                    <TimelineSeparator>
                      {manageEntityIconColor[key] === "failed" ? (
                        <IconCrossCircleIconSvg />
                      ) : (
                        <IconCheckCircleIconSvg />
                      )}
                      <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent sx={{ padding: 0 }}>
                      <div className="accordion-panel">
                        <Accordion
                          className="heading-bold"
                          defaultExpanded={true}
                        >
                          <AccordionSummary
                            id="panel3-header"
                            expandIcon={<ExpandMoreIcon />}
                          >
                            <Typography>{`Import associated ${key
                              .replace(/_/g, " ")
                              .replace(/\b\w/g, (match: string) =>
                                match.toUpperCase()
                              )}`}</Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Box className="nested-child">
                              <Timeline position="right">
                                {value.map((entity: any, index: number) => {
                                  const stepStatus = getStepStatus(
                                    entity?.details?.status
                                  );

                                  return (
                                    <>
                                      <TimelineItem
                                        className={`status-${stepStatus}`}
                                      >
                                        <TimelineSeparator>
                                          {stepStatus === "failed" ? (
                                            <IconCrossCircleIconSvg />
                                          ) : (
                                            <IconCheckCircleIconSvg />
                                          )}
                                          {value.length - 1 !== index && (
                                            <TimelineConnector />
                                          )}
                                        </TimelineSeparator>
                                        <TimelineContent sx={{ padding: 0 }}>
                                          <div className="child-item">
                                            <div className="column-first">
                                              {entity?.details
                                                ?.is_already_exist ? (
                                                <span>
                                                  <span
                                                    className="import-navigate-entity"
                                                    onClick={() =>
                                                      handleNavigateToEntity(
                                                        entity,
                                                        key
                                                      )
                                                    }
                                                  >
                                                    '
                                                    {key !==
                                                    "file_processing_attributes"
                                                      ? getDisplayName(
                                                          entity,
                                                          key
                                                        )
                                                      : "File processing attribute"}
                                                  </span>
                                                  ' with ID{" "}
                                                  {
                                                    entity?.details
                                                      ?.entity_id_in_target_env
                                                  }{" "}
                                                  and code '
                                                  {entity?.code ||
                                                    entity?.domain_code}
                                                  ' already exists
                                                </span>
                                              ) : (
                                                `'${
                                                  key !==
                                                  "file_processing_attributes"
                                                    ? getDisplayName(
                                                        entity,
                                                        key
                                                      )
                                                    : "File processing attribute"
                                                }' with code ${
                                                  entity?.code ||
                                                  entity?.domain_code
                                                } does not exist`
                                              )}
                                            </div>
                                            <div className="column-second">
                                              {!entity?.details
                                                ?.is_already_exist ? (
                                                <Tooltip
                                                  title="Create"
                                                  placement="top"
                                                  onClick={() =>
                                                    handleAddReplaceEntity(
                                                      entity,
                                                      "add",
                                                      key
                                                    )
                                                  }
                                                >
                                                  <button className="stepper-btn btn-plus">
                                                    {btnLoader === key ? (
                                                      <span className="loader">
                                                        <CircularProgress />
                                                      </span>
                                                    ) : (
                                                      <IconAddOrange />
                                                    )}
                                                  </button>
                                                </Tooltip>
                                              ) : (
                                                <>
                                                  <Tooltip
                                                    title="Override"
                                                    placement="top"
                                                    onClick={() =>
                                                      handleAddReplaceEntity(
                                                        entity,
                                                        "replace",
                                                        key
                                                      )
                                                    }
                                                  >
                                                    <button className="stepper-btn btn-db">
                                                      {btnLoader === key ? (
                                                        <span className="loader">
                                                          <CircularProgress />
                                                        </span>
                                                      ) : (
                                                        <IconReplaceOrange />
                                                      )}
                                                    </button>
                                                  </Tooltip>
                                                  <Tooltip
                                                    title="Discard"
                                                    placement="top"
                                                  >
                                                    <button
                                                      className="stepper-btn btn-bin"
                                                      onClick={() => {
                                                        console.log(
                                                          "entity",
                                                          entity
                                                        );
                                                        setOpenConfirmation(
                                                          true
                                                        );
                                                        setDiscardEntity({
                                                          entityType: key,
                                                          entityCode:
                                                            entity?.code ||
                                                            entity?.domain_code,
                                                          entityName:
                                                            entity?.name ||
                                                            entity?.domain_name,
                                                        });
                                                      }}
                                                    >
                                                      <IconDiscard />
                                                    </button>
                                                  </Tooltip>
                                                </>
                                              )}
                                            </div>
                                          </div>
                                        </TimelineContent>
                                      </TimelineItem>
                                    </>
                                  );
                                })}
                              </Timeline>
                            </Box>
                          </AccordionDetails>
                        </Accordion>
                      </div>
                    </TimelineContent>
                  </TimelineItem>
                );
              }
              return null;
            }
          })}
        </Timeline>
      </div>
      <ConfirmationDialog
        title={"Confirm discard component"}
        dialogContent={`Are you sure,you want to discard this override operation ?This will also get removed from 'Preview/Validate components' screen.`}
        handleCancel={handleCancel}
        openConfirmation={openConfirmation}
        handleConfirm={handleSave}
      />
    </Box>
  );
};

export default ImportedAssociatedEntity;
