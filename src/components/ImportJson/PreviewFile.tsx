import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  Skeleton,
} from "@mui/material";
import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useState,
} from "react";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-json";
import "ace-builds/src-noconflict/theme-github";
import { GridExpandMoreIcon } from "@mui/x-data-grid";
import { removeKeys } from "../../services/utils";
import { keysToRemove } from "../../services/constants";

interface IPreviewFileProps {
  uploadedFileData: any;
  setUploadedFileData: Dispatch<SetStateAction<any>>;
  loading: boolean;
}

const orderedKeys = [
  "domains",
  "connection_keys",
  "linked_services",
  "file_processing_attributes",
  "run_instances",
  "resource_column_details",
  "resources",
];

const PreviewFile = ({
  uploadedFileData,
  setUploadedFileData,
  loading,
}: IPreviewFileProps) => {
  const [expandedParentAccordion, setExpandedParentAccordion] =
    useState("main-entity");
  const [expandedChildAccordion, setExpandedChildAccordion] = useState(
    uploadedFileData?.entity_name ?? ""
  );

  const handleMainEditorChange = useCallback(
    (newValue: string) => {
      try {
        const parsedValue = JSON.parse(newValue);
        setUploadedFileData((prev: any) => ({
          ...prev,
          entity_description: parsedValue,
        }));
      } catch (error) {
        console.error("Invalid JSON:", error);
      }
    },
    [uploadedFileData, setUploadedFileData]
  );

  const handleAssociatedEditorChange = useCallback(
    (newValue: string, entityKey: any) => {
      try {
        const parsedValue = JSON.parse(newValue);
        setUploadedFileData((prev: any) => ({
          ...prev,
          associated_entities: {
            ...prev.associated_entities,
            [entityKey]: parsedValue,
          },
        }));
      } catch (error) {
        console.error("Invalid JSON:", error);
      }
    },
    [setUploadedFileData]
  );
  const handleParentAccordionChange =
    (panel: any) => (event: any, isExpanded: any) => {
      setExpandedParentAccordion(isExpanded ? panel : null);
    };
  const handleChildAccordionChange =
    (panel: any) => (event: any, isExpanded: any) => {
      setExpandedChildAccordion(isExpanded ? panel : null);
    };
  return (
    <div className="accordion-panel accordion-group-parents">
      {loading ? (
        <Box>
          {Array.from(new Array(2)).map((_, index) => (
            <Skeleton key={index} height={30} />
          ))}
          <br />
          {Array.from(new Array(3)).map((_, index) => (
            <Skeleton key={index} height={30} />
          ))}
        </Box>
      ) : (
        <>
          {uploadedFileData?.entity_description && (
            <Accordion
              className="heading-bold"
              expanded={expandedParentAccordion === "main-entity"}
              onChange={handleParentAccordionChange("main-entity")}
            >
              <AccordionSummary
                aria-controls="panel3-content"
                id="panel3-header"
                expandIcon={<GridExpandMoreIcon />}
              >
                <h4 className="acc-h4">Main component</h4>
              </AccordionSummary>
              <AccordionDetails>
                <div className="accordion-panel accordion-group">
                  <Accordion
                    className="heading-bold"
                    expanded={
                      expandedChildAccordion === uploadedFileData?.entity_name
                    }
                    onChange={handleChildAccordionChange(
                      uploadedFileData?.entity_name
                    )}
                  >
                    <AccordionSummary
                      aria-controls="panel1-content"
                      id="panel1-header"
                      expandIcon={<GridExpandMoreIcon />}
                    >
                      <h4 className="acc-h4">
                        {uploadedFileData?.entity_name &&
                          uploadedFileData?.entity_name
                            .replace(/_/g, " ")
                            .toUpperCase()}
                      </h4>
                    </AccordionSummary>
                    <AccordionDetails>
                      <AceEditor
                        mode="json"
                        theme="github"
                        onChange={handleMainEditorChange}
                        value={JSON.stringify(
                          // removeKeys(
                          //   uploadedFileData?.entity_description,
                          //   keysToRemove
                          // ),
                          uploadedFileData?.entity_description,
                          null,
                          2
                        )}
                        width="100%"
                        height="200px"
                        editorProps={{ $blockScrolling: true }}
                        showGutter
                        aria-label="queryEditor"
                        name="editor"
                        fontSize={15}
                        minLines={4}
                        maxLines={10}
                        showPrintMargin={false}
                        setOptions={{
                          useWorker: false,
                        }}
                      />
                    </AccordionDetails>
                  </Accordion>
                </div>
              </AccordionDetails>
            </Accordion>
          )}
          {uploadedFileData?.associated_entities &&
            Object.keys(uploadedFileData?.associated_entities).length > 0 && (
              <Accordion
                className="heading-bold"
                expanded={expandedParentAccordion === "associated-entity"}
                onChange={handleParentAccordionChange("associated-entity")}
              >
                <AccordionSummary
                  aria-controls="panel3-content"
                  id="panel3-header"
                  expandIcon={<GridExpandMoreIcon />}
                >
                  <h4 className="acc-h4">Associated components</h4>
                </AccordionSummary>
                <AccordionDetails>
                  {orderedKeys.map((key) => {
                    const value = uploadedFileData.associated_entities[key];
                    if (value && value.length > 0) {
                      return (
                        <div
                          key={key}
                          className="accordion-panel accordion-group"
                        >
                          <Accordion
                            className="heading-bold"
                            expanded={expandedChildAccordion === key}
                            onChange={handleChildAccordionChange(key)}
                          >
                            <AccordionSummary
                              aria-controls={`${key}-content`}
                              id={`${key}-header`}
                              expandIcon={<GridExpandMoreIcon />}
                            >
                              <h4 className="acc-h4">
                                {key.replace(/_/g, " ").toUpperCase()}
                              </h4>
                            </AccordionSummary>
                            <AccordionDetails>
                              <AceEditor
                                mode="json"
                                theme="github"
                                onChange={(newValue) =>
                                  handleAssociatedEditorChange(newValue, key)
                                }
                                value={JSON.stringify(value, null, 2)}
                                width="100%"
                                height="200px"
                                editorProps={{ $blockScrolling: true }}
                                showGutter
                                aria-label="queryEditor"
                                name={`editor-${key}`}
                                fontSize={15}
                                minLines={4}
                                maxLines={10}
                                showPrintMargin={false}
                                // readOnly={true}
                                setOptions={{
                                  useWorker: false,
                                }}
                              />
                            </AccordionDetails>
                          </Accordion>
                        </div>
                      );
                    }
                    return null;
                  })}
                </AccordionDetails>
              </Accordion>
            )}
        </>
      )}
    </div>
  );
};

export default PreviewFile;
