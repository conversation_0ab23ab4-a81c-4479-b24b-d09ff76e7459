import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Typography,
  LinearProgress,
  IconButton,
  Toolt<PERSON>,
} from "@mui/material";
import "ace-builds/src-noconflict/mode-json";
import "ace-builds/src-noconflict/theme-github";
import { IconSvgUploadOrange } from "../../common/utils/icons";
import { GridCloseIcon, GridExpandMoreIcon } from "@mui/x-data-grid";
import PreviewFile from "./PreviewFile";
import { ValidateImportedDomain } from "../../services/domainsService";
import { useToast } from "../../services/utils";
import {
  ValidateImportedResource,
  ValidateImportedResourceColumnDetails,
} from "../../services/resourcesService";
import { ValidateImportedRule } from "../../services/rulesService";
import ImportedAssociatedEntity from "./ImportedAssociatedEntity";
import ConfirmationDialog from "../Dialogs/ConfirmationDialog";
import ImportedMainEntity from "./ImportedMainEntity";
import { useLocation, useNavigate } from "react-router-dom";
import { onAddReplaceEntity } from "../../services/utils/AddReplaceImportEntity";
import Loader from "../Molecules/Loader/Loader";

const validationFunctions = {
  domain: ValidateImportedDomain,
  resource: ValidateImportedResource,
  "resource column details": ValidateImportedResourceColumnDetails,
  rule: ValidateImportedRule,
};

const ImportJson = () => {
  const location = useLocation();
  const { state } = useLocation();
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [importType, setImportType] = useState("");
  const domainSteps = [
    {
      label: `Upload component`,
      subLabel: "Upload exported definition file",
    },
    {
      label: `Preview/Validate components`,
      subLabel: "Preview/Validate definitions",
    },
    {
      label: `Import ${importType
        .replace(/_/g, " ")
        .replace(/\b\w/g, (match: string) => match.toUpperCase())}`,
      subLabel: `Finish importing ${importType
        .replace(/_/g, " ")
        .replace(/\b\w/g, (match: string) => match.toUpperCase())} process`,
    },
  ];

  const steps = [
    {
      label: `Upload component`,
      subLabel: "Upload exported definition file",
    },
    {
      label: `Preview/Validate components`,
      subLabel: "Preview/Validate definitions",
    },
    {
      label: "Import associated components",
      subLabel: "Import associated definitions",
    },
    {
      label: `Import ${importType
        .replace(/_/g, " ")
        .replace(/\b\w/g, (match: string) => match.toUpperCase())}`,
      subLabel: `Finish importing ${importType
        .replace(/_/g, " ")
        .replace(/\b\w/g, (match: string) => match.toUpperCase())} process`,
    },
  ];

  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [file, setFile] = useState<any>({ name: "" });
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFileData, setUploadedFileData] = useState<any>({});
  const [validatedData, setValidatedData] = useState<any>([]);
  const [stepper, setStepper] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [openConfirmation, setOpenConfirmation] = useState<boolean>(false);

  const fileInputRef: any = React.useRef(null);

  useEffect(() => {
    if (importType === "domain") {
      setStepper(domainSteps);
    } else {
      setStepper(steps);
    }
  }, [state, importType]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    let import_type = searchParams.get("type") ?? "";
    if (import_type === "resource-column")
      import_type = "resource column details";
    setImportType(import_type ?? "");
  }, [location.search]);
  const handleClick = () => {
    fileInputRef.current.click();
  };

  const handleChange = (event: any) => {
    handleFileUpload(event);
  };

  const handleNext = () => {
    let missingEntities: any;
    if (activeStep === 1) {
      setLoading(true);
      const entityName: keyof typeof validationFunctions =
        uploadedFileData?.entity_name;
      if (entityName in validationFunctions) {
        validationFunctions[entityName](uploadedFileData)
          .then((response) => {
            if (!response) return;

            if (
              (uploadedFileData?.associated_entities === null ||
                uploadedFileData?.associated_entities?.length === 0) &&
              importType !== "domain"
            ) {
              missingEntities = response.associated_entities_validation_result
                .filter(
                  (entity: { is_already_exist: any }) =>
                    !entity.is_already_exist
                )
                .reduce(
                  (
                    acc: { [x: string]: any[] },
                    entity: { entity_type: string | number; entity_code: any }
                  ) => {
                    if (!acc[entity.entity_type]) {
                      acc[entity.entity_type] = [];
                    }
                    acc[entity.entity_type].push(entity.entity_code);
                    return acc;
                  },
                  {}
                );
              if (Object.keys(missingEntities).length > 0) {
                const validationMessage = Object.entries(missingEntities).map(
                  ([entityType, entityCodes]) => ({
                    id: entityType,
                    message: entityCodes,
                  })
                );

                const temp = (
                  <div>
                    The below associated components do not exist in the System
                    right now:
                    {validationMessage.map((msg: any) => (
                      <div key={msg.id}>
                        <span style={{ fontWeight: 900 }}>{msg.id}</span>
                        <ul style={{ margin: "0 0 15px" }}>
                          {msg?.message.map((code: any, index: number) => (
                            <li key={index}>{code}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                    Please make sure to import or create these prior to the
                    current import process.
                  </div>
                );

                showToast(temp, "error");
                setLoading(false);
              } else {
                setValidatedData(response);
                showToast(
                  `Components definitions are validated successfully!`,
                  "success"
                );
                setActiveStep((prevActiveStep) => prevActiveStep + 1);
                setLoading(false);
              }
            } else {
              setValidatedData(response);
              showToast(
                `Components definitions are validated successfully!`,
                "success"
              );
              setActiveStep((prevActiveStep) => prevActiveStep + 1);
              setLoading(false);
            }
          })
          .catch((error) => {
            console.log("error >>", error);
            showToast(`Components definitions are not validated!`, "error");
            setLoading(false);
          });
      }
    } else if (activeStep === stepper.length - 1) {
      switch (uploadedFileData?.entity_name) {
        case "domain":
        case "domains":
          navigate("/domain");
          break;
        case "resource column details":
        case "resource_column_details":
          navigate("/resource-columns/all");

          break;
        case "resource":
        case "resources":
          navigate("/resource/all");

          break;
        case "rule":
        case "rules":
          navigate("/rules-list/all");

          break;
        default:
          console.error(
            `Unknown component type: ${uploadedFileData?.entity_name}`
          );
      }
      showToast(
        `${uploadedFileData?.entity_name
          .replace(/_/g, " ")
          .replace(/^./, (match: string) => match.toUpperCase())
          .replace(/ (?=.) /g, (match: string) => match.toUpperCase())
          .replace(
            /s$/,
            ""
          )} and its associated components are imported successfully!`,
        "success"
      );
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
    // setLoading(false);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleFileUpload = (e: any) => {
    try {
      const uploadedFile = e.target.files[0];
      if (!uploadedFile) return;
      // Check if the file is a JSON file
      if (uploadedFile.type !== "application/json") {
        showToast(`"Please import a valid JSON file only!"`, "error");
        return;
      }

      const reader = new FileReader();

      reader.onload = (event) => {
        const fileContent = event.target?.result;
        const jsonContent = JSON.parse(fileContent as string);
        if (importType === jsonContent?.entity_name) {
          setUploadedFileData(jsonContent);
          setFile(uploadedFile);
          showToast(
            `${importType
              .replace(/_/g, " ")
              .replace(/\b\w/g, (match: string) =>
                match.toUpperCase()
              )} definition is uploaded successfully!`,
            "success"
          );

          const uploadSpeed = 100; // Simulated upload speed in ms
          const progressInterval = setInterval(() => {
            setUploadProgress((prevProgress) => {
              const nextProgress = prevProgress + uploadedFile.size / 100;
              if (nextProgress >= 100) {
                clearInterval(progressInterval);
                return 100;
              }
              return nextProgress;
            });
          }, uploadSpeed);
        } else {
          setFile({ name: "" });
          setUploadProgress(0);
          setUploadedFileData({});
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
          showToast(
            `Please import a valid ${importType
              .replace(/_/g, " ")
              .replace(/\b\w/g, (match: string) =>
                match.toUpperCase()
              )} definition!`,
            "error"
          );
        }
      };

      reader.onerror = (event) => {
        console.error(
          "File could not be read! Code " + event.target?.error?.code
        );
      };
      reader.readAsText(uploadedFile);
    } catch (error) {
      console.error("Error reading file:", error);
    }
  };

  // const handleValidation = (step: any) => {
  //   // Simulate API validation
  //   setValidationStatus((prevStatus) => ({
  //     ...prevStatus,
  //     [step]: Math.random() > 0.5 ? "success" : "error", // Simulate random success/error
  //   }));
  // };

  const handleDiscardAll = () => {
    const filterEntities = (entities: any) => {
      if (!entities) return [];
      return entities.filter(
        (entity: { details: { is_already_exist: boolean } }) =>
          !entity.details.is_already_exist
      );
    };
    if (uploadedFileData && uploadedFileData.associated_entities) {
      const newAssociatedEntities: any = {};

      for (const [key, entities] of Object.entries(
        uploadedFileData.associated_entities
      )) {
        const filteredEntities = filterEntities(entities as any[]);
        if (filteredEntities.length > 0) {
          newAssociatedEntities[key] = filteredEntities;
        }
      }

      const newData = {
        ...uploadedFileData,
        associated_entities: newAssociatedEntities,
      };

      setUploadedFileData(newData);
    }
    setOpenConfirmation(false);
  };

  const handleCancel = () => {
    setOpenConfirmation(false);
  };
  const handleExecuteAll = async () => {
    setIsLoading(true);
    const entityOrder = [
      "domains",
      "connection_keys",
      "linked_services",
      "file_processing_attributes",
      "run_instances",
      "resource_column_details",
      "resources",
    ];

    const processEntity = async (index: number) => {
      if (
        index >= entityOrder.length &&
        uploadedFileData?.associated_entities
      ) {
        return;
      }

      const entityType = entityOrder[index];
      const entities = uploadedFileData.associated_entities[entityType];
      if (entities) {
        for (const entity of entities) {
          try {
            const response = await onAddReplaceEntity(
              entity,
              entity?.details?.is_already_exist ? "replace" : "add",
              entityType,
              uploadedFileData.associated_entities,
              showToast
            );
            if (response) {
              setUploadedFileData((prev: any) => {
                const updatedData = { ...prev };

                // Find the correct key in associated_entities
                const associatedEntityKey = Object.keys(
                  prev.associated_entities
                ).find((key) => key === entityType);

                if (associatedEntityKey) {
                  // Find the correct object in the array by matching the code
                  const index = updatedData.associated_entities[
                    associatedEntityKey
                  ].findIndex((item: any) => item.code === entity?.code);

                  if (index !== -1) {
                    // Update the details of the matched object
                    updatedData.associated_entities[associatedEntityKey][
                      index
                    ].details = {
                      ...updatedData.associated_entities[associatedEntityKey][
                        index
                      ].details,
                      status: "success",
                      entity_id_in_target_env: response.id,
                      is_already_exist: true,
                    };
                  }
                }

                return updatedData;
              });
            } else {
              setUploadedFileData((prev: any) => {
                const updatedData = { ...prev };

                // Find the correct key in associated_entities
                const associatedEntityKey = Object.keys(
                  prev.associated_entities
                ).find((key) => key === entityType);

                if (associatedEntityKey) {
                  // Find the correct object in the array by matching the code
                  const index = updatedData.associated_entities[
                    associatedEntityKey
                  ].findIndex((item: any) => item.code === entity?.code);

                  if (index !== -1) {
                    // Update the details of the matched object
                    updatedData.associated_entities[associatedEntityKey][
                      index
                    ].details = {
                      ...updatedData.associated_entities[associatedEntityKey][
                        index
                      ].details,
                      status: "failed",
                    };
                  }
                }

                return updatedData;
              });
              return;
            }
            setIsLoading(false);
          } catch (error) {
            console.error(`Error processing ${entityType}:`, error);
            setIsLoading(false);
            // Decide whether to continue or stop the process on error
            // return; // Uncomment this line to stop on first error
          }
        }
      }
      // Process next entity type
      await processEntity(index + 1);
    };

    // Start processing with the first entity type
    await processEntity(0);
  };
  const isButtonEnabled = () => {
    if (activeStep === 0 && uploadProgress < 100) {
      return false;
    }

    const checkEntity = (entities: any[]) =>
      entities.every(
        (entity: { details: { is_already_exist: any } }) =>
          entity?.details?.is_already_exist
      );

    const associatedEntities = uploadedFileData?.associated_entities || {};

    return (
      (activeStep !== 2 ||
        (checkEntity(associatedEntities.linked_services || []) &&
          checkEntity(associatedEntities.connection_keys || []) &&
          checkEntity(associatedEntities.domains || []) &&
          checkEntity(associatedEntities.file_processing || []) &&
          checkEntity(associatedEntities.run_instances || []) &&
          checkEntity(associatedEntities.resources || []) &&
          checkEntity(associatedEntities.resource_column_details || []))) &&
      (activeStep !== 3 ||
        uploadedFileData?.entity_description?.details?.is_already_exist !==
          false)
    );
  };
  return (
    <>
      {isLoading && <Loader isLoading={isLoading} />}
      <Box className="mui-stepper">
        <Stepper activeStep={activeStep} className="header-stepper">
          {stepper.map((step: any, index: any) => (
            <Step
              key={index}
              className={`${activeStep === index ? "active-tab" : ""}`}
            >
              <StepLabel>
                <div>
                  <Typography variant="body1">{step.label}</Typography>
                  <Typography variant="caption">{step.subLabel}</Typography>
                </div>
              </StepLabel>
            </Step>
          ))}
        </Stepper>
        <Box className="stepper-body">
          {activeStep === 0 && (
            <>
              <Box className="file-upload-box">
                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: "none" }}
                  onChange={handleChange}
                />
                <Box onClick={handleClick} className="svg-input-opener">
                  <IconSvgUploadOrange />
                  <h3> Browse file</h3>
                </Box>

                <p>Supported format: JSON</p>
              </Box>
              {file && file?.name && (
                <Box className="uploading-files">
                  <h4>{uploadProgress < 100 ? "Uploading" : "Uploaded"}</h4>
                  <Box className="file-name">
                    <Typography> {file?.name}</Typography>
                    <LinearProgress
                      variant="determinate"
                      value={uploadProgress}
                    />
                    <IconButton
                      className="icon-close"
                      onClick={() => {
                        setFile({ name: "" });
                        setUploadProgress(0);
                        setUploadedFileData({});
                        if (fileInputRef.current) {
                          fileInputRef.current.value = "";
                        }
                      }}
                    >
                      <GridCloseIcon />
                    </IconButton>
                  </Box>
                </Box>
              )}
            </>
          )}
          {activeStep === 1 && (
            <>
              <PreviewFile
                uploadedFileData={uploadedFileData}
                setUploadedFileData={setUploadedFileData}
                loading={loading}
              />
            </>
          )}
          {uploadedFileData?.entity_name !== "domain" && activeStep === 2 && (
            <ImportedAssociatedEntity
              uploadedFileData={uploadedFileData}
              setUploadedFileData={setUploadedFileData}
              validatedData={validatedData}
            />
          )}
          {activeStep ===
            (uploadedFileData?.entity_name !== "domain" ? 3 : 2) && (
            <ImportedMainEntity
              uploadedFileData={uploadedFileData}
              setUploadedFileData={setUploadedFileData}
              validatedData={validatedData}
            />
          )}
        </Box>
        <Box
          className={`stepper-footer ${
            activeStep === 2 &&
            uploadedFileData?.entity_name !== "domain" &&
            uploadedFileData?.associated_entities &&
            Object.keys(uploadedFileData?.associated_entities).length > 0 &&
            "between"
          }`}
        >
          {uploadedFileData?.entity_name !== "domain" &&
            activeStep === 2 &&
            uploadedFileData?.associated_entities &&
            Object.keys(uploadedFileData?.associated_entities).length > 0 && (
              <>
                <Box sx={{ columnGap: "10px", display: "flex" }}>
                  <Button
                    className="btn-orange btn-border"
                    onClick={handleExecuteAll}
                  >
                    Execute All
                  </Button>
                  <Tooltip
                    title="It will discard existing component"
                    placement="top"
                  >
                    <Button
                      className="btn-orange btn-border"
                      onClick={() => {
                        setOpenConfirmation(true);
                      }}
                    >
                      Discard All
                    </Button>
                  </Tooltip>
                </Box>
              </>
            )}
          <Box sx={{ columnGap: "10px", display: "flex" }}>
            {activeStep !== 0 && (
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
                className="btn-orange btn-border"
              >
                Back
              </Button>
            )}
            <Button
              variant="contained"
              onClick={handleNext}
              className="btn-orange"
              disabled={!isButtonEnabled()}
            >
              {activeStep === 1
                ? "Validate"
                : activeStep === stepper.length - 1
                ? "Finish"
                : "Next"}
            </Button>
          </Box>
        </Box>
      </Box>
      <ConfirmationDialog
        title={"Confirm discard component"}
        dialogContent={`Are you sure,you want to discard this override operations?This will also get removed from 'Preview/Validate components' screen.`}
        handleCancel={handleCancel}
        openConfirmation={openConfirmation}
        handleConfirm={handleDiscardAll}
      />
    </>
  );
};

export default ImportJson;
