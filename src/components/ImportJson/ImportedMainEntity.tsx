import React, { useEffect, useState } from "react";
import { Box, CircularProgress, Tooltip } from "@mui/material";
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
} from "@mui/lab";
import { useToast } from "../../services/utils";
import {
  IconAddOrange,
  IconDiscard,
  IconCheckCircleIconSvg,
  IconReplaceOrange,
} from "../../common/utils/icons";

import {
  entity_name,
  getStepStatus,
  handleNavigateToEntity,
  onAddReplaceEntity,
} from "../../services/utils/AddReplaceImportEntity";

const ImportedMainEntity = ({
  uploadedFileData,
  setUploadedFileData,
  validatedData,
}: any) => {
  useEffect(() => {
    if (uploadedFileData && uploadedFileData.entity_description) {
      setUploadedFileData((prev: any) => ({
        ...prev,
        entity_description: {
          ...prev.entity_description,
          details: validatedData?.source_entity_validation_result,
        },
      }));
    }
  }, []);
  const { showToast } = useToast();

  const stepStatus = getStepStatus(
    uploadedFileData?.entity_description?.details?.status
  );
  const [btnLoader, setBtnLoader] = useState(false);

  const handleAddReplaceEntity = async (
    entity: any,
    actionType: string,
    entityType: string
  ) => {
    setBtnLoader(true);
    const response = await onAddReplaceEntity(
      entity,
      actionType,
      entityType,
      uploadedFileData?.associated_entities,
      showToast
    );
    if (response) {
      setUploadedFileData((prev: any) => ({
        ...prev,
        entity_description: {
          ...prev.entity_description,
          details: {
            ...prev.entity_description.details,
            status: "success",
            entity_id_in_target_env: response?.id,
            is_already_exist: true,
          },
        },
      }));
    }
    setBtnLoader(false);
  };
  const getDisplayName = (uploadedFileData: any) => {
    const foundEntity = entity_name.find(
      (item: { entity: string }) =>
        item.entity === uploadedFileData?.entity_name
    );
    if (foundEntity) {
      return uploadedFileData?.entity_description?.[foundEntity.display_name];
    }
    return uploadedFileData?.entity_name;
  };
  return (
    <Box className="timeline">
      <Timeline position="right">
        <>
          <TimelineItem className={`status-${stepStatus}`}>
            <TimelineSeparator>
              <IconCheckCircleIconSvg />
            </TimelineSeparator>
            <TimelineContent sx={{ padding: 0 }}>
              <div className="child-item mb-0">
                <div className="column-first">
                  {uploadedFileData?.entity_description?.details
                    ?.is_already_exist ? (
                    <span>
                      <span
                        className="import-navigate-entity"
                        onClick={() =>
                          handleNavigateToEntity(
                            uploadedFileData?.entity_description,
                            uploadedFileData?.entity_name
                          )
                        }
                      >
                        '
                        {uploadedFileData?.entity_name !==
                        "file_processing_attributes"
                          ? getDisplayName(uploadedFileData)
                          : "File processing attribute"}
                      </span>
                      ' with ID
                      {
                        uploadedFileData?.entity_description?.details
                          ?.entity_id_in_target_env
                      }{" "}
                      and code '
                      {uploadedFileData?.entity_description?.code ||
                        uploadedFileData?.entity_description?.domain_code}
                      ' already exists
                    </span>
                  ) : (
                    `'${
                      uploadedFileData?.entity_name !==
                      "file_processing_attributes"
                        ? getDisplayName(uploadedFileData)
                        : "File processing attribute"
                    }' with code '${
                      uploadedFileData?.entity_description?.code ||
                      uploadedFileData?.entity_description?.domain_code
                    }' does not exist`
                  )}
                </div>
                <div className="column-second">
                  {!uploadedFileData?.entity_description?.details
                    ?.is_already_exist ? (
                    <Tooltip
                      title="Create"
                      placement="top"
                      onClick={() =>
                        handleAddReplaceEntity(
                          uploadedFileData?.entity_description,
                          "add",
                          uploadedFileData?.entity_name
                        )
                      }
                    >
                      <button className="stepper-btn btn-plus">
                        {btnLoader ? (
                          <span className="loader">
                            <CircularProgress />
                          </span>
                        ) : (
                          <IconAddOrange />
                        )}
                      </button>
                    </Tooltip>
                  ) : (
                    <>
                      <Tooltip
                        title="Override"
                        placement="top"
                        onClick={() =>
                          handleAddReplaceEntity(
                            uploadedFileData?.entity_description,
                            "replace",
                            uploadedFileData?.entity_name
                          )
                        }
                      >
                        <button className="stepper-btn btn-db">
                          {btnLoader ? (
                            <span className="loader">
                              <CircularProgress />
                            </span>
                          ) : (
                            <IconReplaceOrange />
                          )}
                        </button>
                      </Tooltip>
                    </>
                  )}
                </div>
              </div>
            </TimelineContent>
          </TimelineItem>
        </>
      </Timeline>
    </Box>
  );
};

export default ImportedMainEntity;
