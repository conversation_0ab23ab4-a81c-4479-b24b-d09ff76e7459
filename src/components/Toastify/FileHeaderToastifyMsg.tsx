const FileHeaderToastifyMsg = ({ unmatchedHeaders }: any) => {
  return (
    <div className="file-headers-list">
      Oops! Some Excel headers didn't match. Please ensure the following headers
      are included in your uploaded file:
      <ul>
        {unmatchedHeaders.map((item: any, index: number) => {
          return <li key={index}>{item}</li>;
        })}
      </ul>
    </div>
  );
};

export default FileHeaderToastifyMsg;
