import React, { useState } from "react";
import { Icon<PERSON>utton, Menu, MenuItem, Tooltip } from "@mui/material";
import { IconMenus } from "../../../common/utils/icons";

const DropdownMenu = ({ dropdownMenuItems }: any) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleMenuItemClick = (item: any) => {
    handleClose();
    if (item.onClick) {
      item.onClick();
    }
  };

  return (
    <div>
      <Tooltip title="More..." placement="top" arrow>
        <IconButton
          className="datagrid-action-btn"
          color="inherit"
          onClick={(event) => handleClick(event)}
        >
          <IconMenus />
        </IconButton>
      </Tooltip>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        sx={{
          "& .MuiMenu-list": {
            "& li": {
              paddingRight: "0px",
              paddingLeft: "0px",
              paddingTop: "0px",
              paddingBottom: "0px",
              "& button": {
                fontSize: "13px",
                color: "#142a41",
                display: "flex",
                columnGap: "12px",
                width: "100%",
                justifyContent: "flex-start",
                paddingRight: "16px",
                paddingLeft: "16px",
                paddingTop: "10px",
                paddingBottom: "10px",
              },
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, .15)",
              },
            },
          },
        }}
      >
        {dropdownMenuItems &&
          dropdownMenuItems?.map((item: any, index: number) => {
            return (
              <MenuItem key={index} onClick={() => handleMenuItemClick(item)}>
                {item}
              </MenuItem>
            );
          })}
      </Menu>
    </div>
  );
};

export default DropdownMenu;
