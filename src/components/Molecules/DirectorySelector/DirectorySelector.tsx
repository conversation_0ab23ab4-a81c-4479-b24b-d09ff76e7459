import React, { useState, useEffect } from "react";
import { Autocomplete, TextField, Button, Grid, Tooltip } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

const DirectorySelector = ({ data, setFileData }: any) => {
  const [directories, setDirectories] = useState<any>([]);
  const [currentDirectory, setCurrentDirectory] = useState<any>(null);
  const [directoryHistory, setDirectoryHistory] = useState<any>([]);

  const extractDirectoriesAndFiles = (data: any, path: string) => {
    const directorySet = new Set();
    const files: any[] = [];

    data.rows.forEach((row: { [x: string]: any }) => {
      const blobName = row["0"];
      const rootDirectory = `${blobName.split("/")[0]}/`;
      const effectivePath = path === "/" ? rootDirectory : path;

      if (blobName.startsWith(effectivePath)) {
        const relativePath = blobName.slice(effectivePath.length);
        const parts = relativePath.split("/");

        // Check for directory and file separately
        const isDirectory = parts.length > 1 && !parts[0].includes(".");

        // Add directories
        if (isDirectory) {
          const subdirectory = `${effectivePath}${parts[0]}/`;
          directorySet.add(subdirectory);
        }

        // Add files only if the prefix matches
        if (blobName.startsWith(effectivePath)) {
          files.push(row);
        }
      }
    });

    return {
      directories: Array.from(directorySet),
      files,
    };
  };

  useEffect(() => {
    if (data.rows.length > 0) {
      const firstBlobName = data.rows[0]["0"];
      const initialRootDirectory = `${firstBlobName.split("/")[0]}/`;
      setCurrentDirectory(initialRootDirectory);
    }
  }, [data]);

  useEffect(() => {
    if (currentDirectory) {
      const { directories: fetchedDirectories, files: fetchedFiles } =
        extractDirectoriesAndFiles(data, currentDirectory);
      setDirectories(fetchedDirectories);
      setFileData(fetchedFiles);
    }
  }, [currentDirectory, data, setFileData]);

  const handleDirectoryChange = (
    event: any,
    newValue: { id: string; name: string } | null
  ) => {
    if (newValue) {
      setDirectoryHistory((prev: any) => [...prev, currentDirectory]);
      setCurrentDirectory(newValue.id);
    }
  };

  const handleGoBack = () => {
    if (directoryHistory.length > 0) {
      const previousDir = directoryHistory[directoryHistory.length - 1];
      setDirectoryHistory((prev: string | any[]) => prev.slice(0, -1));
      setCurrentDirectory(previousDir);
    }
  };

  return (
    <>
      {directoryHistory.length !== 0 ? (
        <Grid item>
          <label className="label-text line-height16">&nbsp;</label>

          <Tooltip title="Back to previous directory" placement="top">
            <Button onClick={handleGoBack} className="btn-orange btn-border">
              <ArrowBackIcon />
            </Button>
          </Tooltip>
        </Grid>
      ) : null}
      <Grid item xs={12} sm={12} md={6} lg={3} xl={3}>
        {currentDirectory && (
          <Autocomplete
            fullWidth
            disableClearable
            options={directories.map((dir: any) => ({ id: dir, name: dir }))}
            getOptionLabel={(option) => option.name}
            onChange={handleDirectoryChange}
            className="form-control-autocomplete"
            value={{ id: currentDirectory, name: currentDirectory }}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Directory"
                placeholder="Select..."
              />
            )}
          />
        )}
      </Grid>
    </>
  );
};

export default DirectorySelector;
