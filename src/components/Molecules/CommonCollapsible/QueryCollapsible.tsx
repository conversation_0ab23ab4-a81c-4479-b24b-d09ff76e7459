import { Box, Collapse, Grid } from "@mui/material";
import React, { useState } from "react";

interface QueryCollapsibleProps {
  children?: React.ReactNode;
  checked: boolean;
  className?: any;
}

const QueryCollapsible: React.FC<QueryCollapsibleProps> = ({
  children,
  checked,
  className,
}) => {
  return (
    <Grid item xs={12} sm={6} md={4} lg={4} xl={4} className={className}>
      <Collapse
        in={checked}
        style={{ transformOrigin: "0 0 0 0" }}
        {...(checked ? { timeout: 1000 } : {})}
      >
        <Box>{children}</Box>
      </Collapse>
    </Grid>
  );
};

export default QueryCollapsible;
