import { Box } from "@mui/material";

import { IconIssueClose } from "../../../common/utils/icons";

interface ISnapShotProps {
  snapShotData?: any;
  setSnapShotData?: any;
}
const IssuesListSnapShot = ({
  snapShotData,
  setSnapShotData,
}: ISnapShotProps) => {
  const handleClose = () => {
    setSnapShotData((prev: any) => ({
      ...prev,
      isVisible: false,
      snapShotData: [],
      isBackDropVisible: false,
    }));
  };

  return (
    <>
      <Box
        className={`filter-sidebar large-panel
        ${snapShotData?.isVisible ? "show" : "hide"}`}
      >
        <div className="filter-header">
          SnapShot Data
          <div className="close-filter" onClick={handleClose}>
            <IconIssueClose />
          </div>
        </div>
        <div className="filter-body">
          <Box
            className="incident-resources"
            sx={{ maxHeight: "60vh", overflowY: "auto" }}
          >
            <table className="table1">
              {Object.entries(snapShotData?.snapShotData).map(
                ([key, value]: any, index: any) => {
                  return (
                    <tr key={index}>
                      <td>{key}</td>
                      <td>
                        <Box className="word-break-all">{value ?? "N/A"}</Box>
                      </td>
                    </tr>
                  );
                }
              )}
            </table>
          </Box>
        </div>
      </Box>
    </>
  );
};

export default IssuesListSnapShot;
