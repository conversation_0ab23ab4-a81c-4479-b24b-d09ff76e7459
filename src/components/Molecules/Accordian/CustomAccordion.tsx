import React, { useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

interface CustomAccordionProps {
  expandId: string;
  title: string;
  handleChangeAccordion?: (
    panel: string
  ) => (event: React.SyntheticEvent, isExpanded: boolean) => void;
  children?: React.ReactNode;
  isEnabled?: boolean;
  isDefaultExpaned?: boolean;
  topMargin?: number;
  accodionTopGap?: number;
}

const CustomAccordion: React.FC<CustomAccordionProps> = ({
  expandId,
  title,
  handleChangeAccordion,
  children,
  isEnabled,
  topMargin,
  isDefaultExpaned,
  accodionTopGap,
}) => {
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(
    isDefaultExpaned ? expandId : false
  );

  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      if (isEnabled) {
        setExpandedAccordion(isExpanded ? panel : false);
        handleChangeAccordion &&
          handleChangeAccordion(panel)(event, isExpanded);
      }
    };
  React.useEffect(() => {
    if (isDefaultExpaned) {
      setExpandedAccordion(expandId);
    } else {
      setExpandedAccordion(false);
    }
  }, [isDefaultExpaned, expandId]);

  return (
    <Box className="accordion-panel" sx={{ marginBottom: "8px" }}>
      <Accordion
        className="heading-bold box-shadow"
        expanded={expandedAccordion === expandId}
        onChange={handleChange(expandId)}
        sx={{
          marginTop: `${topMargin ?? 16}px !important`,
        }}
      >
        <AccordionSummary
          aria-controls="panel1d-content"
          id={expandId}
          expandIcon={isEnabled ? <ExpandMoreIcon /> : ""}
          className="min-header"
        >
          {title}
        </AccordionSummary>
        <AccordionDetails
          sx={{ paddingTop: accodionTopGap ? `${accodionTopGap}px` : "16px" }}
          style={{ display: isEnabled ? "block" : "none" }}
        >
          {children}
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default CustomAccordion;
