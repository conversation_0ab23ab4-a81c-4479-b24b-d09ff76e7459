import React from "react";
import { Backdrop, Box } from "@mui/material";
import loaderImg from "../../../assets/loader.png";
// import "./Loader.css"; // Import the CSS file

interface LoaderProps {
  isLoading: boolean;
  zIndex?: number;
}

const Loader = ({ isLoading, zIndex }: LoaderProps) => {
  return (
    <Backdrop
      sx={{
        color: "#fff",
        backgroundColor: "rgba(0, 0, 0, 0.65)",
        zIndex: (theme) => (zIndex ? 999999 : theme.zIndex.drawer + 1),
      }}
      open={isLoading}
    >
      <Box className="loaderContainer">
        <Box className="loaderImg" component="img" src={loaderImg} />
        <Box className="loader">
          <Box className="dot"></Box>
          <Box className="dot"></Box>
          <Box className="dot"></Box>
        </Box>
      </Box>
    </Backdrop>
  );
};

export default Loader;
