import React, { useRef, useState } from "react";
import { Box } from "@mui/material";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-sql";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-noconflict/ext-language_tools";

const CommonEditor = ({
  title,
  editorValue,
  setEditorValue,
  editorRef,
}: any) => {
  const editorOptions = {
    enableLiveAutocompletion: true,
    enableBasicAutocompletion: true,
    enableSnippets: false,
    showLineNumbers: true,
    tabSize: 2,
    fontSize: 20,
  };
  const [error, setErrors] = useState<{ [key: string]: string }>({});
  // const editorRef = useRef<AceEditor | null>(null);
  const handleSqlChange = (newSqlQuery: string) => {
    setEditorValue(newSqlQuery);
    if (newSqlQuery) {
      setErrors((prevError) => ({
        ...prevError,
        query: "",
      }));
    } else {
      setErrors((prevError) => ({
        ...prevError,
        query: "Please enter query",
      }));
    }
  };
  return (
    <Box>
      <Box className="label-text">
        {title}
        <span className="required-asterisk">*</span>
      </Box>
      <Box>
        <AceEditor
          mode="sql"
          theme="github"
          onChange={handleSqlChange}
          value={editorValue || ""}
          width="100%"
          height="100px"
          editorProps={{ $blockScrolling: true }}
          setOptions={editorOptions}
          ref={editorRef}
          showGutter
          // placeholder="Write your query here..."
          // id="editor"
          aria-label="editor"
          name="editor"
          fontSize={16}
          minLines={8}
          maxLines={8}
          showPrintMargin={false}
          className={`sql-editor ${error?.query ? "has-error" : ""}`}
        />
        <span className="validation-error m-0">
          {error?.query && <span>{error?.query}</span>}
        </span>
      </Box>
    </Box>
  );
};

export default CommonEditor;
