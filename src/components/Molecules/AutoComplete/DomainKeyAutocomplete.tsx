import { <PERSON>comple<PERSON>, <PERSON>, <PERSON>uI<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ield } from "@mui/material";
import { CheckCircleRounded } from "@mui/icons-material";

interface DomainKeyAutocompleteProps {
  id: string;
  domainKeyOptions: any[];
  selectedDomainKey?: any;
  onDomainKeyChange: (event: any, newValue: any) => void;
  popperAnchorId: string;
  isMulti?: boolean;
  className?: string;
  error?: any;
  helperText?: string;
  isDisabled?: boolean;
  required?: boolean;
}

const DomainKeyAutocomplete = ({
  id,
  domainKeyOptions,
  selectedDomainKey,
  onDomainKeyChange,
  popperAnchorId,
  isMulti = true,
  className,
  error,
  helperText,
  isDisabled = false,
  required,
}: DomainKeyAutocompleteProps) => {
  const domainKeysPopper = (props: any) => (
    <Popper
      {...props}
      className="filter-autocomplete-zindex"
      anchorEl={document.getElementById(popperAnchorId)}
    />
  );
  return (
    <div className="form-group non-flex">
      <div className="form-label line-height16 label-text">
        <span className="text-capitalize">{`${id.split("-")[0]} `}</span>
        Domain Key
        {required ? <span className="required-asterisk">*</span> : ""}
      </div>
      <div className="form-input">
        <Autocomplete
          id={id}
          PopperComponent={domainKeysPopper}
          fullWidth={true}
          className={className}
          multiple={isMulti}
          options={domainKeyOptions}
          getOptionLabel={(option: any) => option?.name}
          disableCloseOnSelect
          onChange={onDomainKeyChange}
          renderInput={(params) => (
            <div className="autocomplete-chips-direction">
              <TextField
                {...params}
                variant="outlined"
                placeholder="Select..."
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!error}
                helperText={helperText || ""}
              />
            </div>
          )}
          renderTags={(value, getTagProps) => {
            if (isMulti) {
              return (
                <div className="align-from-left">
                  {selectedDomainKey &&
                    selectedDomainKey.map((option: any, index: number) => (
                      <Chip
                        {...getTagProps({ index })}
                        key={index}
                        label={option?.name}
                      />
                    ))}
                </div>
              );
            } else {
              return selectedDomainKey && selectedDomainKey.length > 0 ? (
                <Chip label={selectedDomainKey[0]?.name} />
              ) : null;
            }
          }}
          renderOption={(props, option: any, { selected }) => (
            <MenuItem
              {...props}
              key={`${option?.name}`}
              value={option?.name}
              sx={{ justifyContent: "space-between" }}
            >
              {option?.name}
              {selected ? <CheckCircleRounded color="info" /> : null}
            </MenuItem>
          )}
          value={selectedDomainKey}
          isOptionEqualToValue={(option: any, value: any) =>
            option.name === value.name
          }
          disabled={Boolean(isDisabled)}
        />
      </div>
    </div>
  );
};

export default DomainKeyAutocomplete;
