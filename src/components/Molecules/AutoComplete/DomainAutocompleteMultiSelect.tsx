import React from "react";
import { Autocomplete, Chip, MenuItem, <PERSON><PERSON>ield, Popper } from "@mui/material";
import { CheckCircleRounded } from "@mui/icons-material";

interface Domain {
  id: number;
  domain_name: string;
  created_on: string;
}

interface DomainAutocompleteProps {
  id: string;
  domainsData: Domain[];
  selectedDomains: Domain[];
  handleChange: (event: any, newValue: Domain[]) => void;
  label: string;
}

const DomainAutocompleteMultiSelect = ({
  id,
  domainsData,
  selectedDomains,
  handleChange,
  label,
}: DomainAutocompleteProps) => {
  const renderMultipleDomainsPopper = (props: any) => (
    <Popper
      {...props}
      className="filter-autocomplete-zindex"
      anchorEl={document.getElementById(id)}
    />
  );

  return (
    <>
      <div className="form-group non-flex">
        <div className="form-label">{label}</div>
        <div className="form-input">
          <Autocomplete
            id={id}
            PopperComponent={renderMultipleDomainsPopper}
            fullWidth={true}
            className={`form-control-autocomplete`}
            multiple
            options={domainsData}
            getOptionLabel={(option: any) => option.domain_name}
            disableCloseOnSelect
            onChange={handleChange}
            renderInput={(params) => (
              <>
                <div className="autocomplete-chips-direction">
                  <TextField
                    {...params}
                    variant="outlined"
                    placeholder="Select..."
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </div>
              </>
            )}
            renderTags={(value, getTagProps) => {
              return (
                <div className="align-from-left">
                  {selectedDomains.map((option: any, index: number) => (
                    <Chip
                      {...getTagProps({ index })}
                      key={index}
                      label={option?.domain_name}
                    />
                  ))}
                </div>
              );
            }}
            renderOption={(props, option: any, { selected }) => (
              <MenuItem
                {...props}
                key={`${option?.id}-${option?.created_on}`}
                value={option?.domain_name}
                sx={{ justifyContent: "space-between" }}
              >
                {option?.domain_name}
                {selected ? <CheckCircleRounded color="info" /> : null}
              </MenuItem>
            )}
            value={selectedDomains}
            isOptionEqualToValue={(option: any, value: any) =>
              option.id === value.id
            }
          />
        </div>
      </div>
    </>
  );
};

export default DomainAutocompleteMultiSelect;
