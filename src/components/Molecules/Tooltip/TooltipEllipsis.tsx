import React from "react";
import { Tooltip, Typography } from "@mui/material";

// A reusable Tooltip component
interface TooltipEllipsisProps {
  text: string;
}

const TooltipEllipsis: React.FC<TooltipEllipsisProps> = ({ text }) => {
  // Regex to clean unwanted semicolons
  const regex = /;(?![\w\s])/g;
  const cleanText = text.replace(regex, "");

  return (
    <Tooltip
      title={<Typography sx={{ fontSize: "0.875rem" }}>{cleanText}</Typography>}
      placement="top"
      className="tooltip-ellipsis"
    >
      <span className="text-ellipsis">{cleanText}</span>
    </Tooltip>
  );
};

export default TooltipEllipsis;
