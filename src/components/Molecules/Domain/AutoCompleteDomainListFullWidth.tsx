import React, { Change<PERSON><PERSON>, Dispatch, SetStateAction } from "react";
import useFetchDomains from "../../../hooks/useFetchDomains";
import FlexBetween from "../../FlexBetween";
import { Autocomplete, TextField } from "@mui/material";

interface IAutoCompleteDomainList {
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  handleOnChangeDomain: (event: ChangeEvent<any>, value: any) => void;
  currentDomainId: string | number | null;
  className?: string;
  name?: string;
  error?: boolean;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
}

/**
 * All domains list with auto complete dropdown functionality
 * @returns
 * Selected domain with id and name
 */
const AutoCompleteDomainListFullWidth: React.FC<IAutoCompleteDomainList> = ({
  setIsLoading,
  handleOnChangeDomain,
  currentDomainId,
  className,
  name,
  error,
  helperText,
  required,
  disabled,
}) => {
  //hooks
  const [domainsData] = useFetchDomains({ setIsLoading });
  return (
    <FlexBetween>
      <Autocomplete
        className={className}
        defaultValue={currentDomainId || ""}
        options={domainsData}
        getOptionLabel={(option) => option.domain_name || ""}
        value={
          domainsData?.find((option) => option.id === currentDomainId) || null
        }
        disabled={disabled}
        renderInput={(params) => (
          <TextField
            name={name}
            style={{ color: "#000000" }}
            {...params}
            label={
              <span>
                Domain
                {required && <span className="required-asterisk">*</span>}
              </span>
            }
            placeholder="Select..."
            InputProps={{
              ...params.InputProps,
              autoComplete: "new-password", // Fix for browser autofill issue
            }}
            InputLabelProps={{
              shrink: true,
            }}
            error={error}
            helperText={helperText}
          />
        )}
        fullWidth
        loadingText="Loading..."
        onChange={(event, value) => handleOnChangeDomain(event, value)}
      />
    </FlexBetween>
  );
};

export default AutoCompleteDomainListFullWidth;
