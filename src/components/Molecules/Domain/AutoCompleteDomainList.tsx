import React, { ChangeE<PERSON>, Dispatch, SetStateAction } from "react";
import useFetchDomains from "../../../hooks/useFetchDomains";
import { Autocomplete, TextField } from "@mui/material";

interface IAutoCompleteDomainList {
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  handelOnChangeDomain: (event: ChangeEvent<any>, value: any) => void;
  currentDomainId: string | number | null;
  sx?: React.CSSProperties;
  className?: string;
  isDisabled?: boolean;
  required?: boolean;
  error?: any;
  helperText?: string;
  listLabel?: string;
}

/**
 * All domains list with auto complete dropdown functionality
 * @returns
 * Selected domain with id and name
 */
const AutoCompleteDomainList: React.FC<IAutoCompleteDomainList> = ({
  setIsLoading,
  handelOnChangeDomain,
  currentDomainId,
  sx,
  className,
  isDisabled = false,
  required,
  error,
  helperText,
  listLabel,
}) => {
  const [domainsData] = useFetchDomains({ setIsLoading });
  //hooks
  return (
    <Autocomplete
      sx={sx}
      defaultValue={
        currentDomainId
          ? domainsData?.find((option) => option.id === currentDomainId)
          : null
      }
      options={domainsData}
      value={
        domainsData?.find((option) => option.id === currentDomainId) || null
      }
      getOptionLabel={(option) => option.domain_name || ""}
      renderInput={(params) => (
        <TextField
          style={{ color: "#000000" }}
          {...params}
          label={
            <span>
              {listLabel ? listLabel : "Domain"}
              {required ? <span className="required-asterisk">*</span> : ""}
            </span>
          }
          // required={required}
          placeholder="Select..."
          InputLabelProps={{
            shrink: true,
          }}
          error={!!error}
          helperText={helperText || ""}
        />
      )}
      className={className}
      loadingText="Loading..."
      onChange={(event, value) => handelOnChangeDomain(event, value)}
      disabled={Boolean(isDisabled)}
    />
  );
};

export default AutoCompleteDomainList;
