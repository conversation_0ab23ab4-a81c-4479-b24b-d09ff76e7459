import React, { ChangeE<PERSON>, Dispatch, SetStateAction } from "react";
import useFetchRunInstance from "../../../hooks/useFetchRunInstance";
import { Autocomplete, Box, TextField } from "@mui/material";

interface IRunNameDropdown {
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  handleChangeRunName: (event: ChangeEvent<any>, value: any) => void;
  currentRunName: string | null;
  className?: string;
  name?: string;
  error?: boolean;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
}

const RunNameDropdown: React.FC<IRunNameDropdown> = ({
  setIsLoading,
  handleChangeRunName,
  currentRunName,
  className,
  name,
  error,
  helperText,
  required,
  disabled
}) => {
  // Hooks
  const [runInstance] = useFetchRunInstance({ setIsLoading });

  return (
    <Box>
      <Box className="label-text">Run Name{required && <span className="required-asterisk">*</span>}</Box>
      <Autocomplete
        className={className}
        defaultValue={currentRunName || ""}
        options={runInstance || []}
        getOptionLabel={(option) => option.run_name || ""}
        value={runInstance?.find((option: any) => option.run_name === currentRunName) || null}
        disabled={disabled}
        renderInput={(params) => (
          <TextField
            name={name}
            // required={required}
            style={{ color: "#000000" }}
            {...params}
            // label="Run Name"
            placeholder="Select..."
            InputProps={{
              ...params.InputProps,
              autoComplete: "new-password",
            }}
            InputLabelProps={{
              shrink: true,
            }}
            error={error}
            helperText={helperText}
          />
        )}
        fullWidth
        loadingText="Loading..."
        onChange={(event, value) => handleChangeRunName(event, value)}
      />
    </Box>
  );
};

export default RunNameDropdown;
