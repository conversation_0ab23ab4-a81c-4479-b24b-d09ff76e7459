import { Box, Button, Collapse, Grid, TextField } from "@mui/material";
import React, { useState } from "react";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import { addNewVariableSchema } from "../../../schemas";
import { useToast } from "../../../services/utils";

interface AvailableVariablesProps {
  error: any;
  setErrors: any;
  handleVariableSelect: any;
  isFlex?: boolean;
}
const AvailableVariables = ({
  error,
  setErrors,
  handleVariableSelect,
  isFlex,
}: AvailableVariablesProps) => {
  const [isAddVariable, setIsAddVariable] = useState(false);
  const [checked, setChecked] = React.useState(false);
  const [newVariable, setNewVariable] = useState({
    var_name: "",
    var_value: "",
  });

  const { globalVariables, setGlobalVariables, tempGlobalVariables } =
    useRuleResourceContext();
  const { showToast } = useToast();

  const isValidName = (name: string) => /^[a-zA-Z0-9_]+$/.test(name);

  const handleAddNewVariable = (name: string, value: any) => {
    if (!isValidName(name)) {
      showToast(
        "Variable Name can only contain alphabets, numbers, and underscores.",
        "warning"
      );
      return;
    }
    setNewVariable((prev) => ({
      ...prev,
      [name.toLowerCase()]: value,
    }));
    validateField(name, value);
  };
  const saveNewVariable = async () => {
    try {
      await addNewVariableSchema.validate(newVariable, {
        abortEarly: false,
      });
      setGlobalVariables((prev: any) => ({
        ...prev,
        [newVariable.var_name.toLowerCase()]: newVariable.var_value,
      }));
      setIsAddVariable(false);
      setChecked((prev) => !prev);
      setNewVariable({
        var_name: "",
        var_value: "",
      });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors && validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            newErrors[error.path] = error.message;
          }
        );
      }
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...newVariable, [name]: value };
      await addNewVariableSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  return (
    <>
      <Box className={`${isFlex ? "flex-props" : ""}`}>
        <h3 className="mb-0 mb-8">Available Variables: </h3>
        <div className="avail-columns-group">
          {globalVariables &&
            Object.keys(globalVariables).map((key, index) => (
              <button
                className="avail-columns"
                key={index}
                onClick={() => handleVariableSelect(key)}
                type="button"
                style={{ display: `${key === "" ? "none" : "block"}` }}
              >
                {key}
              </button>
            ))}

          {!isAddVariable && (
            <Button
              variant="contained"
              color="secondary"
              onClick={() => {
                setIsAddVariable(true);
                setChecked((prev) => !prev);
              }}
              className="btn-orange btn-dark btn-sm plus-btn-sm"
            >
              <AddSharpIcon />
            </Button>
          )}
        </div>
      </Box>

      <Collapse
        in={checked}
        style={{ transformOrigin: "0 0 0 0" }}
        {...(checked ? { timeout: 1000 } : {})}
      >
        <Box sx={{ mt: 2, mb: 2 }}>
          <Grid
            container
            rowSpacing={2}
            columnSpacing={2.5}
            className="add-variable-form"
          >
            <Grid item xs={6} sm={6} md={4} lg={3}>
              <label className="label-text">
                Variable name <span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                name="var_name"
                // placeholder="Ex: Sample variable name"
                value={newVariable.var_name}
                onChange={(event) =>
                  handleAddNewVariable(event.target.name, event.target.value)
                }
                fullWidth
                variant="outlined"
                className={`form-control-autocomplete editor-textbox ${
                  error?.var_name ? "has-error" : ""
                }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!error?.var_name}
                helperText={error?.var_name}
              />
            </Grid>

            <Grid item xs={6} sm={6} md={4} lg={3}>
              <label className="label-text">
                Variable value <span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                name="var_value"
                value={newVariable.var_value}
                // placeholder="Ex: Sample variable value"
                onChange={(event) =>
                  handleAddNewVariable(event.target.name, event.target.value)
                }
                fullWidth
                variant="outlined"
                className={`form-control-autocomplete editor-textbox ${
                  error?.var_value ? "has-error" : ""
                }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!error?.var_value}
                helperText={error?.var_value}
              />
            </Grid>
            <Grid item>
              <Box>
                <Box
                  sx={{
                    display: { md: "block", sm: "none", xs: "none" },
                  }}
                  className="label-text"
                >
                  &nbsp;
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    columnGap: "8px",
                    justifyContent: "flex-end",
                  }}
                >
                  <Button
                    color="secondary"
                    variant="contained"
                    className="btn-orange"
                    onClick={saveNewVariable}
                  >
                    <SaveOutlinedIcon /> Save
                  </Button>
                  <Button
                    color="secondary"
                    variant="contained"
                    onClick={() => {
                      setIsAddVariable(false);
                      setChecked((prev) => !prev);
                      setErrors((prev: any) => ({
                        ...prev,
                        var_name: undefined,
                        var_value: undefined,
                      }));
                      setNewVariable({
                        var_name: "",
                        var_value: "",
                      });
                    }}
                    className="btn-orange btn-dark"
                  >
                    Cancel
                  </Button>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Collapse>
    </>
  );
};

export default AvailableVariables;
