import React, { useEffect } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
  IconButton,
  Tab,
  <PERSON>bs,
  <PERSON>lt<PERSON>,
  Typo<PERSON>,
  Button,
} from "@mui/material";
import FlexBetween from "../../FlexBetween";
import {
  getDateFromTimestamp,
  getFormattedTime,
} from "../../../services/utils";
import { Link, useNavigate } from "react-router-dom";
import { IconReRunWhite } from "../../../common/utils/icons";

interface IValidationResultHeader {
  validateResult: any;
}

const ValidationResultHeader = ({
  validateResult,
}: IValidationResultHeader) => {
  const navigate = useNavigate();
  const baseResourceId =
    validateResult?.additional_properties?.base_resource_validation_result;
  return (
    <div
      className={`header-group ${
        baseResourceId != null ? "has-base-resource" : ""
      }`}
    >
      <div className="heading">
        <strong> Execution name:</strong>
        {
          validateResult?.complete_validation_params?.validation_request_body
            ?.execution_name
        }
      </div>
      <div className="column-right d-flex align-items-center">
        <div className="rerun-btn">
          <Button
            className="btn-orange btn-dark btn-sm plus-btn-sm"
            onClick={() =>
              navigate(
                `/resource/${validateResult?.domain_id}/re-run-validate-resource/${validateResult?.resource_id}?re-run=true&validation-id=${validateResult?.id}&run-id=${validateResult?.run_id}&run-name=${validateResult?.run_name}`
              )
            }
          >
            <IconReRunWhite /> &nbsp; ReRun
          </Button>
        </div>
        <Box className="timestamp-info">
          {validateResult?.execution_time &&
            `${getDateFromTimestamp(
              validateResult?.execution_time
            )} ${getFormattedTime(validateResult?.execution_time)}`}
        </Box>
        <div className="badges-group">
          {validateResult?.is_success === true && (
            <label className={`badges badge-base badge-success`}>Success</label>
          )}
          {validateResult?.is_success === false && (
            <label className="badges badge-base badge-error">Failed</label>
          )}
          {validateResult?.run_id && (
            <label className="badges badge-base badge-blue">
              Run Id : {validateResult?.run_id}
            </label>
          )}
          {validateResult?.run_name && (
            <label className="badges badge-base badge-orange">
              run Name : {validateResult?.run_name}
            </label>
          )}
          <div className="button-tree-panel">
            {baseResourceId != null && (
              <div>
                <Button
                  variant="contained"
                  className="btn-parent base-resource-validation-result"
                >
                  <a
                    href={`/validation-execution-history/validate-result/${baseResourceId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Base Resource Validation Result
                  </a>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ValidationResultHeader;
