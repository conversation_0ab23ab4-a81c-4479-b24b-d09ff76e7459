import React, { useEffect, useState } from "react";
import { useToast } from "../../../services/utils";
import { Tooltip } from "@mui/material";
const ResourceColumnNameCell = ({
  params,
  fileData,
  setFileData,
  manageAvailableColumns,
  availColumns,
  setAvailColumns,
  setAvailColumnsWithResourceDetail,
  inputRefs,
}: any) => {
  const { showToast } = useToast();
  const [localColumnName, setLocalColumnName] = useState(
    params.row.column_name || ""
  );
  const [oldColumnName, setOldColumnName] = useState("");

  useEffect(() => {
    setLocalColumnName(params.row.column_name || "");
  }, [params.row.column_name]);

  const handleFocusIn = (e: React.FocusEvent<HTMLInputElement>) => {
    setOldColumnName(e.target.value);
  };

  const handleOnBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const updatedColumns = manageAvailableColumns(
      availColumns,
      oldColumnName,
      oldColumnName && oldColumnName.length > 0 ? "update" : "add",
      params.row.column_name
    );
    setAvailColumns(updatedColumns);
    setAvailColumnsWithResourceDetail(null);
  };

  const valueField = fileData.find(
    (item: any) => item.id === params.row.id
  )?.column_name;
  const isNameFilled = valueField !== "";

  const handleChangeName = (e: any) => {
    const value = e.target.value;
    if (value.toLowerCase() === "file_name") {
      const isFileNameExist = fileData.some(
        (fileItem: any) => fileItem.column_name.toLowerCase() === "file_name"
      );
      if (isFileNameExist) {
        showToast(
          `"file_name" is reserved key column, and already exists. Please provide a different name.`,
          "warning"
        );
        return;
      }
    }
    setLocalColumnName(value);
    setFileData((prev: any) => {
      return prev.map((item: any) => {
        if (item.id === params.row.id) {
          return { ...item, column_name: value, changed: true };
        }
        return item;
      });
    });
  };

  return (
    <Tooltip title={valueField} placement="top">
      <input
        value={localColumnName}
        onChange={handleChangeName}
        className={`form-control-1 border-0 input-ellipsis ${
          !isNameFilled ? "border-red" : "p-0"
        }`}
        style={{ fontSize: "14px", fontWeight: 500 }}
        onKeyDown={(e: any) => {
          e.stopPropagation();
        }}
        onBlur={handleOnBlur}
        onFocus={handleFocusIn}
        ref={(el) => (inputRefs.current[params?.id] = el)}
      />
    </Tooltip>
  );
};

export default ResourceColumnNameCell;
