import React, { ChangeEvent, useEffect, useState, useCallback } from "react";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";
import { MenuItem, Grid, Checkbox, Chip } from "@mui/material";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { Popper } from "@mui/base";

interface GroupedAutocompleteProps {
  data: Record<string, string[]>;
  label: string | undefined;
  placeholder: string | undefined;
  selectedColumns: string[] | any[];
  selectedOptions: any;
  setSelectedOptions: React.Dispatch<React.SetStateAction<any>>;
  errors: any;
  setErrors: React.Dispatch<React.SetStateAction<any>>;
  id: any;
}

const GroupedAutocomplete: React.FC<GroupedAutocompleteProps> = ({
  data,
  label,
  placeholder,
  selectedColumns,
  selectedOptions,
  setSelectedOptions,
  id,
  errors,
  setErrors,
}) => {
  const [options, setOptions] = useState<any>([]);
  const [isOptionSelected, setIsOptionSelected] = useState<boolean>(false);

  useEffect(() => {
    if (data) {
      const option = Object.entries(data).reduce(
        (acc, [category, items]) => [
          ...acc,
          ...(Array.isArray(items)
            ? items.map((item) => ({ category, label: item }))
            : [{ category, label: items }]),
        ],
        [] as { category: string; label: string }[]
      );
      setOptions(option);
    }
  }, [data]);
  useEffect(() => {
    // Create a new array to store filtered columns as objects
    const columnsWithCategory: any[] = [];

    if (selectedColumns && data) {
      // Iterate through each category in the original data
      for (const category in data) {
        if (Object.prototype.hasOwnProperty.call(data, category)) {
          // Check if the value associated with the category is an array
          if (Array.isArray(data[category])) {
            // Filter the columns in the current category based on the selected columns
            const filteredColumns = data[category].filter((column) =>
              selectedColumns.includes(column)
            );

            // Add the filtered columns to the new array as objects
            filteredColumns.forEach((label) => {
              columnsWithCategory.push({
                category,
                label,
              });
            });
          }
        }
      }

      setSelectedOptions(columnsWithCategory);
    }
  }, [selectedColumns, data]);

  const handleSelectAll = (category: string) => {
    setIsOptionSelected(true);
    const categoryOptions = options.filter(
      (option: { category: string }) => option.category === category
    );
    const allSelected = categoryOptions.every(
      (option: { category: string; label: string }) =>
        selectedOptions.some(
          (selectedOption: { category: string; label: string }) =>
            selectedOption.category === option.category &&
            selectedOption.label === option.label
        )
    );

    if (allSelected) {
      // Deselect all options in the category
      const newSelectedOptions = selectedOptions.filter(
        (selectedOption: { label: string; category: string }) =>
          !categoryOptions.some(
            (catOption: { label: string; category: string }) =>
              catOption.label === selectedOption.label
          )
      );
      setSelectedOptions(newSelectedOptions);
    } else {
      // Select all options in the category
      const newSelectedOptions = [
        ...selectedOptions,
        ...categoryOptions.filter(
          (catOption: { category: string; label: string }) =>
            !selectedOptions.some(
              (selectedOption: { category: string; label: string }) =>
                selectedOption.label === catOption.label
            )
        ),
        ...options.filter((catOption: { category: string; label: string }) =>
          categoryOptions.some(
            (selectedOption: { category: string; label: string }) =>
              selectedOption.label === catOption.label &&
              selectedOption.category != catOption.category
          )
        ),
      ];
      setSelectedOptions(newSelectedOptions);
    }
  };

  const handleIndividualOption = (option: {
    category: string;
    label: string;
  }) => {
    const isSelected = selectedOptions.some(
      (selectedOption: { category: string; label: string }) =>
        selectedOption.category === option.category &&
        selectedOption.label === option.label
    );

    if (isSelected) {
      // Deselect the individual option
      const newSelectedOptions = selectedOptions.filter(
        (selectedOption: { label: string }) =>
          selectedOption.label !== option.label
      );
      setSelectedOptions(newSelectedOptions);
    } else {
      // Select the individual option
      const matchedOptions = options.filter(
        (option1: { label: any }) => option1.label === option.label
      );
      setSelectedOptions([...selectedOptions, ...matchedOptions]);
    }
  };
  useEffect(() => {
    if (selectedOptions && selectedOptions.length > 0) {
      setErrors((prev: any) => ({
        ...prev,
        base_resource_columns: undefined,
      }));
    } else if (isOptionSelected) {
      setErrors((prev: any) => ({
        ...prev,
        base_resource_columns: "Base Resource Columns is required",
      }));
    }
  }, [selectedOptions, isOptionSelected]);
  const renderPopper = useCallback((props: any, id: any) => {
    return (
      <Popper
        {...props}
        anchorEl={document.getElementById(`auto-complete-grouped-${id}`)}
      />
    );
  }, []);
  const handleChipClick = (option: any) => {
    const newSelectedOptions = selectedOptions.filter(
      (selectedOption: { label: string }) =>
        selectedOption.label !== option.label
    );
    setSelectedOptions(newSelectedOptions);
    if (newSelectedOptions.length === 0) {
      setIsOptionSelected(true);
    }
  };
  return (
    <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
      <Autocomplete
        id={`auto-complete-grouped-${id}`}
        PopperComponent={(props: any) => renderPopper(props, id)}
        multiple
        options={options}
        className={`form-control-autocomplete ${
          errors?.base_resource_columns ? "has-error" : ""
        }`}
        groupBy={(option) => option.category}
        disableCloseOnSelect
        getOptionLabel={(option) => option.label}
        value={selectedOptions}
        onChange={(event, value) => {
          setSelectedOptions(value);
          setIsOptionSelected(true);
        }}
        renderInput={(params) => (
          <>
            <div className="autocomplete-chips-direction">
              <TextField
                {...params}
                label={
                  <span>
                    {label}
                    <span className="required-asterisk">*</span>
                  </span>
                }
                variant="outlined"
                placeholder={placeholder || ""}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.base_resource_columns}
                helperText={errors?.base_resource_columns}
              />
            </div>
          </>
        )}
        renderOption={(props, option, { selected }) => (
          <MenuItem
            {...props}
            value={option}
            sx={{ justifyContent: "space-between", marginLeft: "20px" }}
          >
            <Checkbox
              icon={<CheckBoxOutlineBlankIcon fontSize="small" />}
              checkedIcon={<CheckBoxIcon fontSize="small" color="primary" />}
              checked={
                selected ||
                selectedOptions.some(
                  (selectedOption: { category: any; label: any }) =>
                    selectedOption.category === option.category &&
                    selectedOption.label === option.label
                )
              }
              onChange={() => handleIndividualOption(option)}
            />
            <div>{option.label}</div>
          </MenuItem>
        )}
        renderTags={(value, getTagProps) => {
          return (
            <>
              <div className="vertical-scroll">
                {value.map((option, index) => (
                  <Chip
                    {...getTagProps({ index })}
                    key={index}
                    label={option.label}
                    onDelete={() => handleChipClick(option)}
                  />
                ))}
              </div>
            </>
          );
        }}
        renderGroup={(params) => (
          <div key={params.key}>
            <MenuItem>
              <Checkbox
                onChange={() => handleSelectAll(params.group)}
                checked={options
                  .filter(
                    (option: { category: string }) =>
                      option.category === params.group
                  )
                  .every((option: { category: string; label: string }) =>
                    selectedOptions.some(
                      (selectedOption: { category: string; label: string }) =>
                        selectedOption.category === option.category &&
                        selectedOption.label === option.label
                    )
                  )}
                indeterminate={options
                  .filter(
                    (option: { category: string }) =>
                      option.category === params.group
                  )
                  .some((option: { category: string; label: string }) =>
                    selectedOptions.some(
                      (selectedOption: { category: string; label: string }) =>
                        selectedOption.category === option.category &&
                        selectedOption.label === option.label
                    )
                  )}
                color="primary"
              />
              <span style={{ fontWeight: "bold" }}>
                {params.group === "unique_columns"
                  ? "Unique column"
                  : params.group === "derived_column"
                  ? "Derived Column"
                  : "General Columns"}
              </span>
            </MenuItem>
            {params.children}
          </div>
        )}
      />
    </Grid>
  );
};
export default GroupedAutocomplete;
