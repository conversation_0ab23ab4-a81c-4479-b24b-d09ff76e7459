import React, { ChangeEvent, Dispatch, SetStateAction } from "react";
import FlexBetween from "../../FlexBetween";
import { Autocomplete, InputLabel, TextField } from "@mui/material";
import useFetchResources from "../../../hooks/useFetchResources";

interface IAutoCompleteResourceList {
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  handleOnChangeResource: (event: ChangeEvent<any>, value: any) => void;
  currentDomainId?: string | number | null;
  currentResourceId?: string | number | null;
  className?: string;
  name?: string;
  error?: boolean;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
}
interface IResourceData {
  id: string | number;
  resource_name: string;
}
/**
 * All resource list with auto complete dropdown functionality
 * @returns
 * Selected resource with id and name
 */
const AutoCompleteResourceListFullWidth: React.FC<
  IAutoCompleteResourceList
> = ({
  setIsLoading,
  handleOnChangeResource,
  currentDomainId,
  currentResourceId,
  className,
  name,
  error,
  helperText,
  disabled,
}) => {
    //hooks
    const [resourcesData] = useFetchResources({ currentDomainId, setIsLoading });
    return (
      <FlexBetween>
        <Autocomplete
          className={className}
          defaultValue={
            (resourcesData || []).find((option) => option.id === currentResourceId) ||
            null
          }
          options={resourcesData || []}
          getOptionLabel={(option: IResourceData) =>
            String(option.resource_name) || ""
          }
          value={
            (resourcesData || []).find((option) => option.id === currentResourceId) ||
            null
          }
          disabled={disabled}
          renderInput={(params) => (
            <>
              <InputLabel htmlFor={name} style={{ color: "red" }}>
                Resource *
              </InputLabel>
              <TextField
                name={name}
                style={{ color: "#000000" }}
                {...params}
                placeholder="Select..."
                InputProps={{
                  ...params.InputProps,
                  autoComplete: "new-password",
                }}
                InputLabelProps={{
                  shrink: true,
                }}
                error={error}
                helperText={helperText}
              />
            </>
          )}
          fullWidth
          loadingText="Loading..."
          onChange={(event, value) => handleOnChangeResource(event, value)}
        />
      </FlexBetween>
    );
  };

export default AutoCompleteResourceListFullWidth;
