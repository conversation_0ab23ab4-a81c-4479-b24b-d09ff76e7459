import { Grid, TextField } from "@mui/material";
import { toast } from "react-toastify";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import { useToast } from "../../../services/utils";

const RenderVariables = ({ error, setErrors }: any) => {
  const { tempGlobalVariables, setTempGlobalVariables } =
    useRuleResourceContext();
  const { showToast } = useToast();

  const isValidName = (name: string) => /^[a-zA-Z0-9_]+$/.test(name);

  const handleAddColumn = (e: any) => {
    if (e.target.name === "") {
      showToast("Please enter Variable Name", "warning");
      return;
    }
    if (!isValidName(e.target.name)) {
      showToast(
        "Variable Name can only contain alphabets, numbers, and underscores.",
        "warning"
      );
      return;
    }
    setTempGlobalVariables((prev: any) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));

    setErrors((prevErrors: any) => ({
      ...prevErrors,
      [e.target.name]: e.target.value ? "" : `Please enter ${e.target.name}`,
    }));
  };

  return (
    <>
      {tempGlobalVariables && (
        <Grid item xs={12}>
          <table className="inline-variables-table">
            <tbody>
              {Object.keys(tempGlobalVariables).map((key, index) => (
                <tr key={index}>
                  <td>
                    <div className="inner-column">
                      <div className="label">
                        <strong>{key}</strong>
                        <span className="required-asterisk">*</span>:
                      </div>
                      <TextField
                        fullWidth
                        type="text"
                        placeholder={`Enter ${key} value`}
                        name={key}
                        className={`form-control  ${
                          error?.[key] ? "has-error" : ""
                        }`}
                        onChange={handleAddColumn}
                        sx={{ width: "100%" }}
                        value={
                          tempGlobalVariables &&
                          typeof tempGlobalVariables === "object" &&
                          tempGlobalVariables[key] !== undefined
                            ? tempGlobalVariables[key]
                            : ""
                        }
                        error={!!error?.[key] || !!error?.duplicateColumn}
                        helperText={
                          error?.[key] || error?.duplicateColumn || ""
                        }
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </Grid>
      )}
    </>
  );
};

export default RenderVariables;
