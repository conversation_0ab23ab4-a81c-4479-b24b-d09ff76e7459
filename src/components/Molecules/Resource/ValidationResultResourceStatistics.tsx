import React from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
  IconButton,
  Tab,
  <PERSON>bs,
  Tooltip,
  Typography,
  <PERSON>ton,
} from "@mui/material";
interface IValidationResultResourceStatistics {
  validateResult: any;
}

const ValidationResultResourceStatistics = ({
  validateResult,
}: IValidationResultResourceStatistics) => {
  return (
    <Box className="text-box-card full-radius" sx={{ marginBottom: 1 }}>
      <div className="additional-props">
        <h4>Resource Statistics</h4>
        <Grid
          container
          rowSpacing={{ xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}
          columnSpacing={{ xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }}
          wrap="wrap"
        >
          <Grid item xs={12} sm={6} md={4} lg={3} xl={2}>
            <div className="additional-box">
              <div className="additional-header">Total Records</div>
              <div className="additional-desc">
                {validateResult?.additional_properties?.total_records != null &&
                validateResult?.additional_properties?.total_records !== "" ? (
                  <p>
                    <span>
                      {validateResult?.additional_properties?.total_records}
                    </span>{" "}
                    Records Found
                  </p>
                ) : (
                  <p>N/A</p>
                )}
              </div>
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={3} xl={2}>
            <div className="additional-box">
              <div className="additional-header">unique records</div>
              <div className="additional-desc">
                {validateResult?.additional_properties?.unique_records !=
                  null &&
                validateResult?.additional_properties?.unique_records !== "" ? (
                  <p>
                    <span>
                      {validateResult?.additional_properties?.unique_records}
                    </span>{" "}
                    Records Found
                  </p>
                ) : (
                  <p>N/A</p>
                )}
              </div>
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={3} xl={2}>
            <div className="additional-box">
              <div className="additional-header">total invalid records</div>
              <div className="additional-desc">
                {validateResult?.additional_properties?.total_invalid_records !=
                  null &&
                validateResult?.additional_properties?.total_invalid_records !==
                  "" ? (
                  <p>
                    <span>
                      {
                        validateResult?.additional_properties
                          ?.total_invalid_records
                      }
                    </span>{" "}
                    Records Found
                  </p>
                ) : (
                  <p>N/A</p>
                )}
              </div>
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={3} xl={2}>
            <div className="additional-box">
              <div className="additional-header">total duplicate records</div>
              <div className="additional-desc">
                {validateResult?.additional_properties
                  ?.total_duplicate_records != null &&
                validateResult?.additional_properties
                  ?.total_duplicate_records !== "" ? (
                  <p>
                    <span>
                      {
                        validateResult?.additional_properties
                          ?.total_duplicate_records
                      }
                    </span>{" "}
                    Records Found
                  </p>
                ) : (
                  <p>N/A</p>
                )}
              </div>
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={3} xl={2}>
            <div className="additional-box">
              <div className="additional-header">total validation errors</div>
              <div className="additional-desc">
                {validateResult?.additional_properties
                  ?.total_validation_errors != null &&
                validateResult?.additional_properties
                  ?.total_validation_errors !== "" ? (
                  <p>
                    <span>
                      {
                        validateResult?.additional_properties
                          ?.total_validation_errors
                      }
                    </span>{" "}
                    Records Found
                  </p>
                ) : (
                  <p>N/A</p>
                )}
              </div>
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={3} xl={2}>
            <div className="additional-box">
              <div className="additional-header">total time</div>
              <div className="additional-desc">
                {validateResult?.additional_properties?.total_time != null &&
                validateResult?.additional_properties?.total_time !== "" ? (
                  <p>
                    <span>
                      {validateResult?.additional_properties?.total_time}
                    </span>{" "}
                    seconds
                  </p>
                ) : (
                  <p>N/A</p>
                )}
              </div>
            </div>
          </Grid>
        </Grid>
      </div>
    </Box>
  );
};

export default ValidationResultResourceStatistics;
