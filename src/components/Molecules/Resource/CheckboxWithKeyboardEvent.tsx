import { Checkbox } from "@mui/material";
import React from "react";

const CheckboxWithKeyboardEvent = (props: any) => {
  const { params, handleChange } = props;
  const paramsValue =
    typeof params?.value === "string" ? params?.value?.trim() : params?.value;

  const handleCheckboxKeyPress = (
    event: React.KeyboardEvent<HTMLDivElement>
  ) => {
    if (event.key === " " || event.key === "Spacebar") {
      event.preventDefault();
      event.stopPropagation();
      const valueToCheck =
        paramsValue === "false"
          ? false
          : paramsValue === "true"
          ? true
          : Boolean(paramsValue);
      const simulatedChangeEvent: React.ChangeEvent<HTMLInputElement> = {
        target: { checked: !valueToCheck },
      } as any;
      handleChange(simulatedChangeEvent);
    }
  };
  const isTruthy = ["Y", "y", "Yes", "yes", true, "true"].includes(paramsValue);
  return (
    <div
      onKeyDown={handleCheckboxKeyPress}
      role="checkbox"
      className="keyboard-checked"
    >
      <Checkbox
        checked={Boolean(params.value) && isTruthy}
        onChange={handleChange}
        sx={{ "&.Mui-checked": { color: "#FFA500" } }}
      />
    </div>
  );
};
export default CheckboxWithKeyboardEvent;
