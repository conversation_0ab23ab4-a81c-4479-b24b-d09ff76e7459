import React from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Tooltip,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import InfoIcon from "@mui/icons-material/Info";
import { formattedJson } from "../../../services/utils";

interface IValidationResultParameters {
  validateResult: any;
}

const ValidationResultParameters = ({
  validateResult,
}: IValidationResultParameters) => {
  const validationPath =
    validateResult?.complete_validation_params?.validation_request_body;
  const { file_name, resource_path, resource_id, ...otherProps } =
    validationPath?.resource_data || {};
  const resource_name = validateResult?.resource_name;
  const linked_service_code =
    validationPath?.resource_data?.linked_service_code || "N/A";
  const connection_key = validationPath?.resource_data?.connection_key || "N/A";
  return (
    <div className="accordion-panel">
      <Accordion className="heading-bold" defaultExpanded>
        <AccordionSummary
          aria-controls="panel2d-content"
          id="panel2d-header"
          expandIcon={<ExpandMoreIcon />}
        >
          <h4>Validation Parameters </h4>
        </AccordionSummary>
        <AccordionDetails sx={{ paddingTop: "16px" }}>
          <table className="custom-table">
            <tbody>
              <tr>
                <th>Resource Name (Resource ID)</th>
                <th className="w-320">File Name </th>
                <th>Linked Service Code</th>
                <th>Connection Key</th>
                <th className="w-280">Resource Location</th>
                <th>Additional Resource Info</th>
              </tr>
              <tr>
                <td>
                  {resource_name} ({resource_id ?? ""}){" "}
                </td>
                <td>
                  <div className="file-scroll">
                    <div className="break-word word-break-all">
                      {validateResult?.complete_validation_params?.file_names
                        ? validateResult?.complete_validation_params?.file_names.map(
                            (fileName: any, index: number) => {
                              return (
                                <Box className="file-name-box" key={index}>
                                  {fileName}
                                </Box>
                              );
                            }
                          )
                        : "N/A"}
                    </div>
                  </div>
                </td>
                <td>{linked_service_code}</td>
                <td>{connection_key || "N/A"}</td>
                <td>
                  <div className="align-center word-break-all">
                    {(resource_path && resource_path.replace(/"/g, "")) ||
                      "N/A"}
                  </div>
                </td>
                <td>
                  <span className="position-relative">
                    <Tooltip
                      componentsProps={{
                        tooltip: { className: "wide-tooltip w-380" },
                      }}
                      title={
                        <pre
                          style={{
                            whiteSpace: "pre-wrap",
                            margin: 0,
                            maxHeight: "200px",
                            overflowY: "auto",
                          }}
                        >
                          <React.Fragment>
                            <Typography color="inherit">
                              Additional Resource Info
                            </Typography>

                            <Typography>
                              {formattedJson(JSON.stringify(otherProps))}
                            </Typography>
                          </React.Fragment>
                        </pre>
                      }
                    >
                      <InfoIcon
                        sx={{
                          position: "absolute",
                          top: "50%",
                          transform: "translateY(-50%)",
                          right: "-24px",
                          width: "16px",
                          cursor: "pointer",
                        }}
                      />
                    </Tooltip>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </AccordionDetails>
      </Accordion>
    </div>
  );
};

export default ValidationResultParameters;
