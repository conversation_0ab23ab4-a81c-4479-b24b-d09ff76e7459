import React from "react";
import { DatePicker } from "@mui/x-date-pickers";
import dayjs, { Dayjs } from "dayjs";
import { pickersLayoutClasses } from '@mui/x-date-pickers/PickersLayout';


interface ICustomDatePickerProps {
  value: Dayjs | null;
  onChange: (newValue: Dayjs | null) => void;
}

const CustomDatePicker: React.FC<ICustomDatePickerProps> = ({ value, onChange }) => {
  return (
    <DatePicker
      value={value}
      onChange={(newValue) => onChange(newValue)}
      minDate={dayjs().subtract(6, "month")}
      maxDate={dayjs()}
      format="MM-DD-YYYY"
      className="form-control-autocomplete date-picker"
      slotProps={{
        actionBar: { actions: ["today"] },
        layout: {
          sx: {
            [`.${pickersLayoutClasses.actionBar}`]: {
              gridColumn: 2,
              color:"#000000"
            },
          },
        },
     }}
    />
  );
};

export default CustomDatePicker;
