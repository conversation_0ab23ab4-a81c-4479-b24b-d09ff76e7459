import {
  Di<PERSON>atch,
  SetStateAction,
  use<PERSON><PERSON>back,
  useEffect,
  useState,
} from "react";
import CloseIcon from "@mui/icons-material/Close";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { CheckCircleRounded } from "@mui/icons-material";

import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Chip,
  MenuItem,
  Popper,
  TextField,
} from "@mui/material";
import AutoCompleteDomainList from "../Domain/AutoCompleteDomainList";
import DomainAutocompleteMultiSelect from "../AutoComplete/DomainAutocompleteMultiSelect";
import DomainKeyAutocomplete from "../AutoComplete/DomainKeyAutocomplete";

interface Domain {
  id: number;
  domain_name: string;
  created_on: string;
}

interface SearchFilter {
  source_domain_id_filter: number[];
  target_domain_id_filter: number[];
  source_domain_key_filter: string[];
  target_domain_key_filter: string[];
}

interface ILinkageFilterSearchProps {
  isFilterVisible: boolean;
  setIsFilterVisible: Dispatch<SetStateAction<boolean>>;
  searchFilterData: SearchFilter;
  setSearchFilterData: Dispatch<SetStateAction<SearchFilter>>;
  domainsData: Domain[];
}

const LinkageFilterSearch = ({
  isFilterVisible,
  setIsFilterVisible,
  searchFilterData,
  setSearchFilterData,
  domainsData,
}: ILinkageFilterSearchProps) => {
  // initial States
  const initialFilterSearch: SearchFilter = {
    source_domain_id_filter: [],
    target_domain_id_filter: [],
    source_domain_key_filter: [],
    target_domain_key_filter: [],
  };

  // States
  const initialDomainKey = {
    sourceDomainKey: [],
    target_domain_key_filter: [],
    targetDomainKey: [],
    source_domain_key_filter: [],
  };

  const [filterSearch, setFilterSearch] = useState<any>(initialFilterSearch);
  const [domainKeys, setDomainKeys] = useState(initialDomainKey);
  const [isFilterEnabled, setIsFilterEnabled] = useState(false);

  useEffect(() => {
    const sourceKeys = filterSearch.source_domain_id_filter
      .map((id: any) => domainsData.find((domain) => domain.id === id))
      .filter((domain: any) => domain)
      .flatMap((domain: any) =>
        domain.domain_properties.columns.map((column: any) => ({
          name: column.name,
        }))
      );

    const targetKeys = filterSearch.target_domain_id_filter
      .map((id: any) => domainsData.find((domain) => domain.id === id))
      .filter((domain: any) => domain)
      .flatMap((domain: any) =>
        domain.domain_properties.columns.map((column: any) => ({
          name: column.name,
        }))
      );

    setDomainKeys((prevState) => ({
      ...prevState,
      sourceDomainKey: sourceKeys,
      targetDomainKey: targetKeys,
    }));
  }, [
    filterSearch?.source_domain_id_filter,
    filterSearch?.target_domain_id_filter,
  ]);

  // Handlers
  const handleClearFilterSearch = () => {
    setFilterSearch(initialFilterSearch);
    setSearchFilterData(initialFilterSearch);
    setDomainKeys(initialDomainKey);
    setIsFilterVisible(false);
    setIsFilterEnabled(false);
  };

  const handleSubmitFilterSearch = () => {
    setSearchFilterData(filterSearch);

    setIsFilterVisible(false);
    setIsFilterEnabled(true);
  };

  const handleChange = (event: any, newValue: any, keyToUpdate: string) => {
    setFilterSearch((prev: any) => {
      const selectedSourceId = newValue.map((item: any) => item?.id);

      const updatedFilter = {
        ...prev,
        [keyToUpdate]: selectedSourceId,
      };

      //code to remove the corresponding  keys if domain is removed
      if (keyToUpdate === "source_domain_id_filter") {
        const removedKeys = filterSearch.source_domain_id_filter
          .filter((id: any) => !selectedSourceId.includes(id))
          .map((id: any) => domainsData.find((domain) => domain.id === id))
          .flatMap((domain: any) =>
            domain?.domain_properties?.columns.map((column: any) => ({
              name: column.name,
            }))
          );

        const remainingKeys = domainKeys.sourceDomainKey.filter(
          (key: any) =>
            !removedKeys.some((removedKey: any) => removedKey.name === key.name)
        );

        const remainingSelectedKeys =
          domainKeys.source_domain_key_filter.filter(
            (key: any) =>
              !removedKeys.some(
                (removedKey: any) => removedKey.name === key.name
              )
          );

        setDomainKeys((prevState) => ({
          ...prevState,
          sourceDomainKey: remainingKeys,
          source_domain_key_filter: remainingSelectedKeys,
        }));
        updatedFilter.source_domain_key_filter =
          updatedFilter.source_domain_key_filter.filter(
            (key: any) =>
              !removedKeys.some((removedKey: any) => removedKey.name === key)
          );
      }
      //end

      //code to remove the corresponding  keys if domain is removed
      if (keyToUpdate === "target_domain_id_filter") {
        const removedKeys = filterSearch.target_domain_id_filter
          .filter((id: any) => !selectedSourceId.includes(id))
          .map((id: any) => domainsData.find((domain) => domain.id === id))
          .flatMap((domain: any) =>
            domain?.domain_properties?.columns.map((column: any) => ({
              name: column.name,
            }))
          );

        const remainingKeys = domainKeys.targetDomainKey.filter(
          (key: any) =>
            !removedKeys.some((removedKey: any) => removedKey.name === key.name)
        );
        const remainingSelectedKeys =
          domainKeys.target_domain_key_filter.filter(
            (key: any) =>
              !removedKeys.some(
                (removedKey: any) => removedKey.name === key.name
              )
          );

        setDomainKeys((prevState) => ({
          ...prevState,
          targetDomainKey: remainingKeys,
          target_domain_key_filter: remainingSelectedKeys,
        }));
        updatedFilter.target_domain_key_filter =
          updatedFilter.target_domain_key_filter.filter(
            (key: any) =>
              !removedKeys.some((removedKey: any) => removedKey.name === key)
          );
      }
      //end

      return updatedFilter;
    });
  };

  const handleDomainKeyChange = (
    event: any,
    newValue: any,
    keyToUpdate: string
  ) => {
    setFilterSearch((prev: any) => {
      const selectedDomainKey = newValue.map((item: any) => ({
        name: item?.name,
      }));
      setDomainKeys((prevState) => ({
        ...prevState,
        [keyToUpdate]: selectedDomainKey,
      }));
      return {
        ...prev,
        [keyToUpdate]: newValue.map((item: any) => item?.name),
      };
    });
  };

  return (
    <div className={`filter-sidebar ${isFilterVisible ? "show" : "hide"}`}>
      <div className="filter-header">
        Filter
        <div
          className="close-filter"
          onClick={() => {
            setIsFilterVisible(false);
          }}
        >
          <CloseIcon />
        </div>
      </div>
      <div className="filter-body">
        <div className="not-has-accordion">
          <DomainAutocompleteMultiSelect
            label={"Source domain name"}
            id="source-domain-autocomplete"
            domainsData={domainsData}
            selectedDomains={
              filterSearch.source_domain_id_filter
                ? filterSearch.source_domain_id_filter.map((id: any) =>
                    domainsData.find((domain) => domain.id === id)
                  )
                : []
            }
            handleChange={(e, value) =>
              handleChange(e, value, "source_domain_id_filter")
            }
          />

          <DomainKeyAutocomplete
            id="source-domain-keys-autocomplete"
            domainKeyOptions={domainKeys.sourceDomainKey}
            selectedDomainKey={domainKeys.source_domain_key_filter}
            onDomainKeyChange={(e, value) =>
              handleDomainKeyChange(e, value, "source_domain_key_filter")
            }
            popperAnchorId="source-domain-keys-autocomplete"
            className={`form-control-autocomplete`}
          />

          <DomainAutocompleteMultiSelect
            label={"Target domain name"}
            id="target-domain-autocomplete"
            domainsData={domainsData}
            selectedDomains={
              filterSearch.target_domain_id_filter
                ? filterSearch.target_domain_id_filter.map((id: any) =>
                    domainsData.find((domain) => domain.id === id)
                  )
                : []
            }
            handleChange={(e, value) =>
              handleChange(e, value, "target_domain_id_filter")
            }
          />
          <DomainKeyAutocomplete
            id="target-domain-keys-autocomplete"
            domainKeyOptions={domainKeys.targetDomainKey}
            selectedDomainKey={domainKeys.target_domain_key_filter}
            onDomainKeyChange={(e, value) =>
              handleDomainKeyChange(e, value, "target_domain_key_filter")
            }
            popperAnchorId="target-domain-keys-autocomplete"
            className={`form-control-autocomplete`}
          />
        </div>
      </div>
      <div className="filter-footer">
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange"
          onClick={handleSubmitFilterSearch}
          disabled={
            JSON.stringify(initialFilterSearch) === JSON.stringify(filterSearch)
          }
        >
          Apply Filter
        </Button>

        <button
          className="btn-orange btn-border cursor-pointer"
          onClick={handleClearFilterSearch}
          color="secondary"
          disabled={
            !isFilterEnabled &&
            JSON.stringify(initialFilterSearch) === JSON.stringify(filterSearch)
          }
        >
          Clear Filter
        </button>
      </div>
    </div>
  );
};

export default LinkageFilterSearch;
