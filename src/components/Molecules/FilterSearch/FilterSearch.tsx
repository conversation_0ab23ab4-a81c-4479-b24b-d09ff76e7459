import { Dispatch, SetStateAction, useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  Box,
  Tooltip,
  Autocomplete,
  Popper,
} from "@mui/material";
import { DatePicker, pickersLayoutClasses } from "@mui/x-date-pickers";
import dayjs, { Dayjs } from "dayjs";
import InfoIcon from "@mui/icons-material/Info";
import useFetchUsersList from "../../../hooks/useFetchUsersList";
import AutoCompleteDomainList from "../Domain/AutoCompleteDomainList";
const executionOptions = ["Completed", "In_progress"];

interface IFilterSearchProps {
  setSearchFilterData: Dispatch<SetStateAction<any>>;
  isFilterVisible: boolean;
  setIsFilterVisible: Dispatch<SetStateAction<boolean>>;
  FilterFor: string;
  searchFilterData: any;
  currentPageType?: any;
  requiredFields?: Array<
    | "date"
    | "domain_id"
    | "run"
    | "resource"
    | "rule"
    | "user"
    | "generic_research_id"
    | "generic_research_code"
    | "execution_status"
    | "generic_execution_id"
  >;
  fieldRequirements?: any;
}

const FilterSearch = ({
  setSearchFilterData,
  isFilterVisible,
  setIsFilterVisible,
  FilterFor,
  searchFilterData,
  currentPageType,
  requiredFields,
  fieldRequirements,
}: IFilterSearchProps) => {
  const initialFilterSearch = {
    domain_id: "",
    domain_name: "",
    resource_id: "",
    resource_name: "",
    rule_id: "",
    rule_name: "",
    run_id: "",
    run_name: "",
    date: "",
    end_date: "",
    generic_research_query_id: "",
    generic_research_query_code: "",
    execution_status: "",
    generic_execution_id: "",
  };
  const initialSearchFilterData = {
    domain_id: null,
    domain_name: "",
    resource_id: null,
    resource_name: "",
    rule_id: null,
    rule_name: "",
    run_id: null,
    run_name: "",
    end_date: null,
    start_date: null,
    date: null,
    assigned_user: "",
    assigned_by: "",
    execution_id: "",
  };
  const [filterSearch, setFilterSearch] = useState<any>(initialFilterSearch);

  const handleFilterSearchChange = (e: any) => {
    const { name, value } = e.target;
    setFilterSearch((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };
  const handleFilterUserChange = (newValue: any, name: string) => {
    setFilterSearch((prev: any) => ({
      ...prev,
      [name]: newValue?.username,
    }));
  };

  const handleFilterSearchIdChange = (e: any) => {
    const { name, value } = e.target;
    const trimmedValue = value.trim();
    // Only allow numeric input and basic controls like Backspace, Tab, Arrow keys
    if (/^\d*$/.test(trimmedValue)) {
      setFilterSearch((prev: any) => ({
        ...prev,
        [name]: value,
      }));
    }
  };
  const handleChangeDateFilterSearch = (
    value: Dayjs | null,
    field: "date" | "end_date"
  ) => {
    const formattedDate = dayjs(value).isValid()
      ? dayjs(value).format("YYYY-MM-DD")
      : "";
    setFilterSearch((prevFormData: any) => ({
      ...prevFormData,
      [field]: formattedDate,
    }));
  };
  const handleSubmitFilterSearch = () => {
    if (FilterFor === "issues") {
      setSearchFilterData({
        ...filterSearch,
        start_date: filterSearch.date,
        end_date: filterSearch.end_date,
        date: undefined,
      });
    } else {
      setSearchFilterData(filterSearch);
    }
    setIsFilterVisible(false);
  };
  const handleClearFilterSearch = () => {
    setFilterSearch(initialFilterSearch);
    if (
      FilterFor === "issues" &&
      JSON.stringify(searchFilterData) !==
        JSON.stringify(initialSearchFilterData)
    ) {
      // for issue list filters
      setSearchFilterData(initialSearchFilterData);
    } else {
      //for default search filters
      setSearchFilterData(initialFilterSearch);
    }
    setIsFilterVisible(false);
  };
  const [allUsersList, setallUsersList] = useState<any>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [usersList] = useFetchUsersList({
    setIsLoading,
  });
  useEffect(() => {
    setallUsersList(usersList);
  }, [usersList]);
  useEffect(() => {
    setFilterSearch((prev: any) => initialFilterSearch);
  }, [currentPageType]);
  const [currentDomainId, setCurrentDomainId] = useState<any>(Number() || null);
  const handelOnChangeDomain = (e: any, value: any) => {
    setFilterSearch((prevFormData: any) => ({
      ...prevFormData,
      domain_name: value?.id,
    }));
    setCurrentDomainId(value?.id);
  };
  const isDomainIdRequired = requiredFields?.includes("domain_id");
  const isDateRequired = requiredFields?.includes("date");
  const isRunRequired = requiredFields?.includes("run");
  const isResourceRequired = requiredFields?.includes("resource");
  const isRuleRequired = requiredFields?.includes("rule");
  const isUserRequired = requiredFields?.includes("user");
  const isGenericResearchIdRequired = requiredFields?.includes(
    "generic_research_id"
  );
  const isGenericResearchCodeRequired = requiredFields?.includes(
    "generic_research_code"
  );
  const isExecutionStatusRequired =
    requiredFields?.includes("execution_status");

  const isExecutionIdRequired = requiredFields?.includes(
    "generic_execution_id"
  );

  return (
    <div className={`filter-sidebar ${isFilterVisible ? "show" : "hide"}`}>
      <div className="filter-header">
        Filter
        <div
          className="close-filter"
          onClick={() => {
            setIsFilterVisible(false);
            // setFilterSearch(searchFilterData);
          }}
        >
          <CloseIcon />
        </div>
      </div>
      <div className="filter-body">
        <div className="accordion-panel alternative">
          {/* Date section start */}
          {isDateRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4">Date</h4>
              </AccordionSummary>
              <AccordionDetails>
                {/* start date or date  section start*/}

                <div className="form-group">
                  <div className="form-label">
                    <span className="position-relative">
                      {fieldRequirements?.dateTitle
                        ? fieldRequirements?.dateTitle
                        : "Start Date"}

                      {fieldRequirements?.tooltipRequired && (
                        <span>
                          <Tooltip
                            title="Search Results are from selected date to till today"
                            placement="top"
                            arrow
                          >
                            <span className="upload-icon-info">
                              <InfoIcon sx={{ fontSize: 16 }} />
                            </span>
                          </Tooltip>
                        </span>
                      )}
                    </span>
                  </div>
                  <div className="form-input">
                    <Box className="datepicker-container-extra-icon">
                      <DatePicker
                        value={dayjs(filterSearch?.date)}
                        onChange={(value: any) =>
                          handleChangeDateFilterSearch(value, "date")
                        }
                        minDate={dayjs().subtract(6, "month")}
                        maxDate={dayjs()}
                        format="YYYY-MM-DD"
                        className="form-control-autocomplete date-picker"
                        slotProps={{
                          actionBar: { actions: ["today"] },
                          layout: {
                            sx: {
                              [`.${pickersLayoutClasses.actionBar}`]: {
                                gridColumn: 2,
                                color: "#000000",
                              },
                            },
                          },
                        }}
                      />
                    </Box>
                  </div>
                </div>
                {/* start date or date section end  */}
                {/* end date or date  */}
                {FilterFor === "issues" && (
                  <div className="form-group">
                    <div className="form-label">
                      <span className="position-relative">End Date</span>
                    </div>
                    <div className="form-input">
                      <Box className="datepicker-container-extra-icon">
                        <DatePicker
                          value={dayjs(filterSearch?.end_date)}
                          onChange={(value: any) =>
                            handleChangeDateFilterSearch(value, "end_date")
                          }
                          minDate={dayjs().subtract(6, "month")}
                          maxDate={dayjs()}
                          format="YYYY-MM-DD"
                          className="form-control-autocomplete date-picker"
                          slotProps={{
                            actionBar: { actions: ["today"] },
                            layout: {
                              sx: {
                                [`.${pickersLayoutClasses.actionBar}`]: {
                                  gridColumn: 2,
                                  color: "#000000",
                                },
                              },
                            },
                          }}
                        />
                      </Box>
                    </div>
                  </div>
                )}
                {/* end date or date  */}
              </AccordionDetails>
            </Accordion>
          )}
          {/* Date section end */}
          {/* user section start */}
          {isUserRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4">User</h4>
              </AccordionSummary>
              <AccordionDetails>
                <div className="form-group non-flex">
                  <div className="form-label">Assigned By</div>
                  <Box className="form-input">
                    <Autocomplete
                      sx={{
                        "& .MuiSelect-select": {
                          height: "36px",
                        },
                      }}
                      className={`form-control-autocomplete`}
                      options={Array.isArray(allUsersList) ? allUsersList : []}
                      getOptionLabel={(option) => option.username}
                      onChange={(e, newValue) =>
                        handleFilterUserChange(newValue, "assigned_by")
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          variant="outlined"
                          name="assigned_by"
                        />
                      )}
                      value={
                        (allUsersList?.length > 0 &&
                          allUsersList?.find(
                            (user: { username: any }) =>
                              user.username === filterSearch?.assigned_by
                          )) ||
                        null
                      }
                      isOptionEqualToValue={(option, value) =>
                        option.username === value.username
                      }
                      PopperComponent={(props) => (
                        <Popper
                          sx={{
                            "& .MuiAutocomplete-listbox": {
                              "& .MuiAutocomplete-option": {
                                "&.Mui-focused[aria-selected='true']": {
                                  backgroundColor: "rgba(0, 0, 0, 0.10)",
                                },
                                "&.Mui-focused": {
                                  backgroundColor: "rgba(0, 0, 0, 0.10)",
                                },
                              },
                            },
                          }}
                          {...props}
                        />
                      )}
                    />
                  </Box>
                </div>
                <div className="form-group non-flex">
                  <div className="form-label">Assigned User</div>
                  <Box className="form-input">
                    <Autocomplete
                      sx={{
                        "& .MuiSelect-select": {
                          height: "36px",
                        },
                      }}
                      className={`form-control-autocomplete`}
                      options={Array.isArray(allUsersList) ? allUsersList : []}
                      getOptionLabel={(option) => option.username}
                      onChange={(e, newValue) =>
                        handleFilterUserChange(newValue, "assigned_user")
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          variant="outlined"
                          name="assigned_user"
                        />
                      )}
                      value={
                        (allUsersList?.length > 0 &&
                          allUsersList?.find(
                            (user: { username: any }) =>
                              user.username === filterSearch?.assigned_user
                          )) ||
                        null
                      }
                      isOptionEqualToValue={(option, value) =>
                        option.username === value.username
                      }
                      PopperComponent={(props) => (
                        <Popper
                          sx={{
                            "& .MuiAutocomplete-listbox": {
                              "& .MuiAutocomplete-option": {
                                "&.Mui-focused[aria-selected='true']": {
                                  backgroundColor: "rgba(0, 0, 0, 0.10)",
                                },
                                "&.Mui-focused": {
                                  backgroundColor: "rgba(0, 0, 0, 0.10)",
                                },
                              },
                            },
                          }}
                          {...props}
                        />
                      )}
                    />
                  </Box>
                </div>
              </AccordionDetails>
            </Accordion>
          )}
          {/* user section end */}

          {isDomainIdRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4">Domain</h4>
              </AccordionSummary>
              <AccordionDetails>
                {FilterFor !== "issues" ? (
                  <div className="form-group">
                    <div className="form-label">Domain Id</div>
                    <div className="form-input">
                      <TextField
                        type="text"
                        name="domain_id"
                        value={filterSearch?.domain_id}
                        onChange={handleFilterSearchIdChange}
                        className="form-control"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="form-group non-flex">
                    <div className="form-label">Domain</div>
                    <div className="form-input">
                      <AutoCompleteDomainList
                        setIsLoading={setIsLoading}
                        handelOnChangeDomain={handelOnChangeDomain}
                        currentDomainId={currentDomainId}
                        className={`form-control-autocomplete autocomplete-no-label`}
                        required={false}
                      />
                    </div>
                  </div>
                )}
                {FilterFor !== "issues" && (
                  <div className="form-group">
                    <div className="form-label">Domain Name </div>
                    <div className="form-input">
                      <TextField
                        type="text"
                        name="domain_name"
                        value={filterSearch?.domain_name}
                        onChange={handleFilterSearchChange}
                        className="form-control"
                      />
                    </div>
                  </div>
                )}
              </AccordionDetails>
            </Accordion>
          )}

          {isResourceRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4">Resource</h4>
              </AccordionSummary>
              <AccordionDetails>
                <div className="form-group">
                  <div className="form-label">Resource Id</div>
                  <div className="form-input">
                    <TextField
                      type="text"
                      name="resource_id"
                      value={filterSearch?.resource_id}
                      onChange={handleFilterSearchIdChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="form-group">
                  <div className="form-label">Resource Name </div>
                  <div className="form-input">
                    <TextField
                      type="text"
                      name="resource_name"
                      value={filterSearch?.resource_name}
                      onChange={handleFilterSearchChange}
                      className="form-control"
                    />
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          )}
          {isRuleRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4">
                  {FilterFor === "rule" ||
                  (FilterFor === "issues" && currentPageType === "execution")
                    ? "Rule"
                    : "Resource"}
                </h4>
              </AccordionSummary>
              <AccordionDetails>
                {FilterFor === "issues" ? (
                  <div className="form-group">
                    <div className="form-label">
                      {currentPageType === "execution"
                        ? "Rule Id"
                        : "Resource Id"}
                    </div>
                    <div className="form-input">
                      <TextField
                        type="text"
                        name={
                          currentPageType === "execution"
                            ? "rule_id"
                            : "resource_id"
                        }
                        value={
                          currentPageType === "execution"
                            ? filterSearch?.rule_id
                            : filterSearch?.resource_id
                        }
                        onChange={handleFilterSearchIdChange}
                        className="form-control"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="form-group">
                    <div className="form-label">Rule Id</div>
                    <div className="form-input">
                      <TextField
                        type="text"
                        name="rule_id"
                        value={filterSearch?.rule_id}
                        onChange={handleFilterSearchIdChange}
                        className="form-control"
                      />
                    </div>
                  </div>
                )}
                {FilterFor !== "issues" && (
                  <div className="form-group">
                    <div className="form-label">Rule Name </div>
                    <div className="form-input">
                      <TextField
                        type="text"
                        name="rule_name"
                        value={filterSearch?.rule_name}
                        onChange={handleFilterSearchChange}
                        className="form-control"
                      />
                    </div>
                  </div>
                )}
                {FilterFor === "issues" && (
                  <div className="form-group">
                    <div className="form-label">Execution Id </div>
                    <div className="form-input">
                      <TextField
                        type="text"
                        name="execution_id"
                        value={filterSearch?.execution_id}
                        onChange={handleFilterSearchChange}
                        className="form-control"
                      />
                    </div>
                  </div>
                )}
              </AccordionDetails>
            </Accordion>
          )}
          {isRunRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4">Run</h4>
              </AccordionSummary>
              <AccordionDetails>
                <div className="form-group">
                  <div className="form-label">Run Id</div>
                  <div className="form-input">
                    <TextField
                      type="text"
                      name="run_id"
                      value={filterSearch?.run_id}
                      onChange={handleFilterSearchIdChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="form-group">
                  <div className="form-label">Run Name </div>
                  <div className="form-input">
                    <TextField
                      type="text"
                      name="run_name"
                      value={filterSearch?.run_name}
                      onChange={handleFilterSearchChange}
                      className="form-control"
                    />
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          )}
          {isGenericResearchIdRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4"> Research Query Id</h4>
              </AccordionSummary>
              <AccordionDetails>
                <div className="form-group">
                  <div className="form-label"> Id</div>
                  <div className="form-input">
                    <TextField
                      type="text"
                      name="generic_research_query_id"
                      value={filterSearch?.generic_research_query_id}
                      onChange={handleFilterSearchIdChange}
                      className="form-control"
                    />
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          )}
          {isGenericResearchCodeRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4"> Research Query Code</h4>
              </AccordionSummary>
              <AccordionDetails>
                <div className="form-group">
                  <div className="form-label"> Code</div>
                  <div className="form-input">
                    <TextField
                      type="text"
                      name="generic_research_query_code"
                      value={filterSearch?.generic_research_query_code}
                      onChange={handleFilterSearchChange}
                      className="form-control"
                    />
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          )}
          {isExecutionStatusRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4"> Execution Status</h4>
              </AccordionSummary>
              <AccordionDetails>
                <div className="form-group">
                  <div className="form-label">Execution Status</div>
                  <div className="form-input">
                    <Autocomplete
                      className={`form-control-autocomplete`}
                      options={executionOptions}
                      value={filterSearch?.execution_status || null}
                      onChange={(event, newValue) => {
                        handleFilterSearchChange({
                          target: {
                            name: "execution_status",
                            value: newValue || "",
                          },
                        });
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          className="form-control"
                          name="execution_status"
                        />
                      )}
                    />
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          )}
          {isExecutionIdRequired && (
            <Accordion>
              <AccordionSummary
                aria-controls="panel2d-content"
                className="min-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4 className="acc-h4"> Execution Id</h4>
              </AccordionSummary>
              <AccordionDetails>
                <div className="form-group">
                  <div className="form-label"> Id </div>
                  <div className="form-input">
                    <TextField
                      type="text"
                      name="generic_execution_id"
                      value={filterSearch?.generic_execution_id}
                      onChange={handleFilterSearchChange}
                      className="form-control"
                      placeholder="IDs Ex:1,2,3"
                    />
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          )}
        </div>
      </div>
      <div className="filter-footer">
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange"
          onClick={handleSubmitFilterSearch}
        >
          Apply Filter
        </Button>

        <button
          className="btn-orange btn-border cursor-pointer"
          onClick={handleClearFilterSearch}
          color="secondary"
        >
          Clear Filter
        </button>
      </div>
    </div>
  );
};

export default FilterSearch;
