import { Dispatch, SetStateAction, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import { Button, Box, Select, MenuItem, Grid } from "@mui/material";
import dayjs from "dayjs";
import RunNameDropdown from "../RunName/RunNameDropDown";
import { showConsolidateReportSchema } from "../../../schemas";
import CustomDatePicker from "../Datepicker/CustomDatePicker";

interface IConsolidatedReportsFilter {
  setFormData: Dispatch<SetStateAction<any>>;
  isFilterVisible: boolean;
  setIsFilterVisible: Dispatch<SetStateAction<boolean>>;
  formData: any;
  handleSubmitReport: any;
}

const ConsolidatedReportsFilter = ({
  setFormData,
  isFilterVisible,
  setIsFilterVisible,
  handleSubmitReport,
  formData,
}: IConsolidatedReportsFilter) => {
  const [isloading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleClearFilterSearch = () => {
    setFormData({
      run_name: "",
      run_date: dayjs().subtract(1, "day"),
      report_type: "",
    });
    setFormData({
      run_name: "",
      run_date: dayjs().subtract(1, "day"),
      report_type: "",
    });
    setErrors({});
    setIsFilterVisible(false);
  };
  const handleChangeRunName = (e: any, value: any) => {
    setFormData({
      ...formData,
      run_name: value?.run_name,
    });
    validateField("run_name", value?.run_name);
  };
  const handleChangeRunDate = (value: any) => {
    setFormData({
      ...formData,
      run_date: value,
    });
  };

  const validateField = async (name: any, value: any) => {
    try {
      const partialReferenceData = {
        ...formData,
        [name]: value,
      };
      await showConsolidateReportSchema.validateAt(name, partialReferenceData);
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  return (
    <div className={`filter-sidebar ${isFilterVisible ? "show" : "hide"}`}>
      <div className="filter-header">
        Consolidated Reports
        <div
          className="close-filter"
          onClick={() => {
            setIsFilterVisible(false);
          }}
        >
          <CloseIcon />
        </div>
      </div>
      <div className="filter-body">
        <Box className="text-box-card bg-white no-radius border-0">
          <div className="accordion-panel alternative">
            <Grid container rowSpacing={3} columnSpacing={3}>
              <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                <RunNameDropdown
                  setIsLoading={setIsLoading}
                  handleChangeRunName={handleChangeRunName}
                  currentRunName={formData?.run_name}
                  className={`form-control-autocomplete ${
                    errors?.run_name ? "has-error" : ""
                  }`}
                  name="run_name"
                  required={true}
                  error={!!errors.run_name}
                  helperText={errors.run_name || ""}
                />
              </Grid>
              <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                <Box className="label-text">
                  Run Date<span className="required-asterisk">*</span>
                </Box>
                <CustomDatePicker
                  value={formData?.run_date}
                  onChange={(newValue) => handleChangeRunDate(newValue)}
                />
              </Grid>
              <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                <Box className="label-text">
                  Report Type<span className="required-asterisk">*</span>
                </Box>
                <Select
                  required
                  labelId="report-type-label"
                  id="report-type"
                  value={formData?.report_type}
                  displayEmpty
                  className={`form-control-1 alternative-1 ${
                    errors?.report_type ? "has-error" : ""
                  }`}
                  onChange={(e: any) => {
                    const { name, value } = e.target;
                    setFormData({
                      ...formData,
                      [name]: value,
                    });
                    validateField(name, value);
                  }}
                  name="report_type"
                  error={!!errors.report_type}
                >
                  <MenuItem value="" disabled>
                    Select...
                  </MenuItem>
                  <MenuItem value="detail">Detail</MenuItem>
                  <MenuItem value="summary">Summary</MenuItem>
                </Select>
                <span className="validation-error">
                  {errors.report_type && <span>{errors.report_type}</span>}
                </span>
              </Grid>
            </Grid>
          </div>
        </Box>
      </div>
      <div className="filter-footer">
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange"
          onClick={async () => {
            try {
              await showConsolidateReportSchema.validate(formData, {
                abortEarly: false,
              });
              handleSubmitReport();
            } catch (validationErrors: any) {
              const newErrors: { [key: string]: string } = {};
              validationErrors.inner.forEach(
                (error: { path: string | number; message: string }) => {
                  newErrors[error.path] = error.message;
                }
              );
              setErrors(newErrors);
            }
          }}
        >
          Show Report
        </Button>

        <button
          className="btn-orange btn-border cursor-pointer"
          onClick={handleClearFilterSearch}
          color="secondary"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ConsolidatedReportsFilter;
