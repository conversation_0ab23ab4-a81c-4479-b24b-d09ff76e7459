import { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import {
  Button,
  Box,
  Grid,
  Autocomplete,
  TextField,
  TextareaAutosize,
  <PERSON>lt<PERSON>,
  Popper,
} from "@mui/material";

import { issueStatus } from "../../../services/constants";
import {
  upadateComparisonIssues,
  upadateValidationIssues,
} from "../../../services/IssueManagementService";
import {
  capitalize,
  getFormattedDateInYYMMDD,
  getFormattedDateTime,
  getFormattedDateTimeWithT,
  getFormattedTimeInDobuleDigit,
  useToast,
} from "../../../services/utils";
import { IconIssueClose } from "../../../common/utils/icons";
import useFetchIssueHistory from "../../../hooks/useFetchIssueHistory";
import Loader from "../Loader/Loader";

interface IDashboardUserDetails {
  dashboardUserDetailsData?: any;
  setDashboardUserDetailsData: any;
  allUsersList: any;
  setIsLoading: any;
  setDetailViewDataRows: any;
  issueFrom?: string;
}
const DashboardUserDetails = ({
  dashboardUserDetailsData,
  setDashboardUserDetailsData,
  allUsersList,
  setIsLoading,
  setDetailViewDataRows,
  issueFrom = "comparison",
}: IDashboardUserDetails) => {
  const userInfoString = localStorage.getItem("userInfo");
  const userInfo = userInfoString ? JSON.parse(userInfoString) : null;
  const { showToast } = useToast();
  const [currentIssueId, setCurrentIssueId] = useState<
    number | null | undefined
  >(null);

  const [formData, setFormData] = useState<any>({});
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [issueHistoryList, setIssueHistoryList] = useState<any>([]);
  const [isUserDetailLoading, setIsUserDetailLoading] = useState(false);
  const [fetchedIssueHistory] = useFetchIssueHistory({
    currentIssueId,
    setIsLoading: setIsUserDetailLoading,
    issueFrom,
  });
  const [charCount, setCharCount] = useState(0);
  const [errors, setErrors] = useState({
    exceedCount: false,
  });
  useEffect(() => {
    setIssueHistoryList(fetchedIssueHistory);
  }, [fetchedIssueHistory]);

  useEffect(() => {
    if (dashboardUserDetailsData) {
      setCurrentIssueId(dashboardUserDetailsData?.issueId);
    }
  }, [dashboardUserDetailsData]);

  useEffect(() => {
    if (dashboardUserDetailsData) {
      const data = {
        incidentId: dashboardUserDetailsData?.incidentId,
        comment: "",
        status: dashboardUserDetailsData?.current_status,
        assigned_user: dashboardUserDetailsData?.assigned_user,
        assigned_by: userInfo?.username,
        issueId: dashboardUserDetailsData?.issueId,
      };
      setFormData(data);
      setInitialFormData(data);
    }
  }, [dashboardUserDetailsData]);

  const handleChangeComment = (event: any) => {
    const newComment = event.target.value;
    const limitedComment =
      newComment.length <= 500 ? newComment : newComment.slice(0, 500);
    setCharCount(limitedComment.length);
    setFormData((prev: any) => ({
      ...prev,
      comment: limitedComment,
    }));
    setErrors((prev: any) => ({
      ...prev,
      exceedCount: limitedComment.length === 500,
    }));
  };

  const handleChangeStatus = (value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      status: value,
    }));
  };

  const handleClose = () => {
    setDashboardUserDetailsData((prev: any) => ({
      ...prev,
      status: false,
      issueAnalysis: [],
    }));
    setFormData({});
    setCurrentIssueId(null);
    setDashboardUserDetailsData({
      panelType:
        dashboardUserDetailsData.panelType === "incidentDetail"
          ? "incidentDetail"
          : "incidentResource",
    });
    setErrors((prev: any) => ({
      ...prev,
      exceedCount: false,
    }));
    setCharCount(0);
    setIssueHistoryList([]);
  };

  const handleSaveIssue = () => {
    const issueId = dashboardUserDetailsData?.issueId;

    const payload: any = {
      assigned_user: formData?.assigned_user,
      assigned_by: formData?.assigned_by,
      comment: formData?.comment,
      status: formData?.status,
    };
    const cleanedPayload = Object.fromEntries(
      Object.entries(payload).filter(
        ([key, value]) => value !== null && value !== ""
      )
    );

    if (issueFrom === "comparison") {
      upadateComparisonIssues(issueId, cleanedPayload)
        .then((response: any) => {
          if (response) {
            showToast("Issue updated successfully!", "success");
            setDetailViewDataRows((prevState: any) => {
              return prevState.map((item: any) => {
                if (item.rowId == dashboardUserDetailsData?.incidentId) {
                  return {
                    ...item,
                    current_status: formData?.status,
                    assigned_user: formData?.assigned_user,
                    comment: formData?.comment,
                  };
                }
                return item;
              });
            });
            const currentDate = new Date();
            const updatedData = {
              ...payload,
              create_date: getFormattedDateTimeWithT(currentDate),
              issue_id: currentIssueId,
            };
            issueHistoryList.unshift(updatedData);
            setCurrentIssueId(null);
            setDashboardUserDetailsData({
              panelType: "incidentDetail",
            });
          }
        })
        .catch((error) => {
          showToast(`Cannot update issue`, "error");
        });
    } else {
      upadateValidationIssues(issueId, payload)
        .then((response: any) => {
          if (response) {
            showToast("Issue updated successfully!", "success");
            setDetailViewDataRows((prevState: any) => {
              return prevState.map((item: any) => {
                if (item.rowId == dashboardUserDetailsData?.incidentId) {
                  return {
                    ...item,
                    current_status: formData?.status,
                    assigned_user: formData?.assigned_user,
                    comment: formData?.comment,
                  };
                }
                return item;
              });
            });
            const currentDate = new Date();
            const updatedData = {
              ...payload,
              create_date: getFormattedDateTimeWithT(currentDate),
              issue_id: currentIssueId,
            };
            issueHistoryList.unshift(updatedData);
            setCurrentIssueId(null);
            setDashboardUserDetailsData({
              panelType: "incidentDetail",
            });
          }
        })
        .catch((error) => {
          showToast(`Cannot update issue`, "error");
        });
    }

    handleClose();
  };

  const issueDataLength =
    dashboardUserDetailsData?.issueAnalysis?.length > 0
      ? Object.keys(dashboardUserDetailsData?.issueAnalysis[1]?.data).length
      : 0;
  const prefixPanelmaxLength =
    dashboardUserDetailsData?.issueAnalysis?.length > 0 &&
    dashboardUserDetailsData?.issueAnalysis[0]?.prefix.some(
      (item: any) => item.length > 20
    );
  return (
    <>
      <Loader isLoading={isUserDetailLoading} />
      <Box
        className={`filter-sidebar medium-panel ${
          dashboardUserDetailsData.panelType !== "incidentDetail"
            ? "large-panel"
            : ""
        } ${
          prefixPanelmaxLength
            ? "two"
            : issueDataLength === 1
            ? "one"
            : issueDataLength === 2
            ? "two"
            : ""
        }
        ${dashboardUserDetailsData?.status ? "show" : "hide"}`}
      >
        <div className="filter-header">
          {`${
            dashboardUserDetailsData.panelType === "incidentDetail"
              ? `Incident id: ${dashboardUserDetailsData?.incidentId}`
              : "Issue Analysis"
          }`}

          <div className="close-filter" onClick={handleClose}>
            <IconIssueClose />
          </div>
        </div>
        <div className="filter-body">
          {dashboardUserDetailsData.panelType === "incidentDetail" ? (
            <Box className="text-box-card bg-white no-radius border-0">
              <div className="accordion-panel alternative">
                <Grid container rowSpacing={3} columnSpacing={3}>
                  {/* <Grid item xs={6} sm={6} md={6} lg={6} xl={6}>
                    <label htmlFor="status" className="label-text pt-11">
                      Incident id:
                      <strong>{dashboardUserDetailsData?.incidentId}</strong>
                    </label>
                  </Grid> */}
                  <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                    <Autocomplete
                      sx={{
                        "& .MuiSelect-select": {
                          height: "36px",
                        },
                      }}
                      className={`form-control-autocomplete capitalize`}
                      options={Array.isArray(issueStatus) ? issueStatus : []}
                      getOptionLabel={(option) => option}
                      onChange={(event, value) => handleChangeStatus(value)}
                      value={capitalize(formData?.status) || ""}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Update Status"
                          variant="outlined"
                        />
                      )}
                      isOptionEqualToValue={(option, value) => option === value}
                      PopperComponent={(props) => (
                        <Popper
                          sx={{
                            "& .MuiAutocomplete-listbox": {
                              "& .MuiAutocomplete-option": {
                                "&.Mui-focused[aria-selected='true']": {
                                  backgroundColor: "rgba(0, 0, 0, 0.10)",
                                },
                                "&.Mui-focused": {
                                  backgroundColor: "rgba(0, 0, 0, 0.10)",
                                },
                              },
                            },
                          }}
                          {...props}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                    <Autocomplete
                      sx={{
                        "& .MuiSelect-select": {
                          height: "36px",
                        },
                      }}
                      className={`form-control-autocomplete`}
                      options={Array.isArray(allUsersList) ? allUsersList : []}
                      getOptionLabel={(option) => option.username}
                      onChange={(event, value) => {
                        setFormData((prev: any) => ({
                          ...prev,
                          assigned_user: value?.username,
                        }));
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Assign to user"
                          variant="outlined"
                        />
                      )}
                      value={
                        (allUsersList?.length > 0 &&
                          allUsersList?.find(
                            (user: { username: any }) =>
                              user.username === formData?.assigned_user
                          )) ||
                        null
                      }
                      isOptionEqualToValue={(option, value) =>
                        option.username === value.username
                      }
                      PopperComponent={(props) => (
                        <Popper
                          sx={{
                            "& .MuiAutocomplete-listbox": {
                              "& .MuiAutocomplete-option": {
                                "&.Mui-focused[aria-selected='true']": {
                                  backgroundColor: "rgba(0, 0, 0, 0.10)",
                                },
                                "&.Mui-focused": {
                                  backgroundColor: "rgba(0, 0, 0, 0.10)",
                                },
                              },
                            },
                          }}
                          {...props}
                        />
                      )}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    md={12}
                    lg={12}
                    xl={12}
                    className="incident-box"
                  >
                    <Box className="comment-section">
                      <Box>
                        <h3>
                          Comments&nbsp;
                          <span
                            className={`normal ${
                              errors?.exceedCount ? "required m-0" : ""
                            }`}
                          >
                            ({charCount}/500)
                          </span>
                        </h3>
                      </Box>
                      <Box className="comment-box">
                        {/* <strong className="mb-1">Add a Comment</strong> */}
                        <Box className="mb-2">
                          <TextareaAutosize
                            className={`form-control-1 textarea`}
                            minRows={3}
                            maxRows={6}
                            aria-label="minimum height"
                            placeholder="Add a comment"
                            value={formData?.comment}
                            name={"comment"}
                            onChange={handleChangeComment}
                          />
                        </Box>
                        {/* <Box className="d-flex justify-content-end">
                          <Button
                            className="btn-orange btn-sm"
                            onClick={handleAddComment}
                          >
                            Add comment
                          </Button>
                        </Box> */}
                        {/* <ul style={{ marginTop: "24px" }}>
                          {incidentComments?.map(
                            (comment: any, commentIndex: any) => (
                              <li key={commentIndex}>
                                <Box>
                                  <strong>User Name: </strong>
                                  {comment.assigned_user}
                                </Box>
                                <Box>
                                  <strong>Comment: </strong> {comment.comment}
                                </Box>
                                <Box>
                                  <strong>Date and time: </strong>
                                  {getFormattedDateTime(comment.create_date)}
                                </Box>
                              </li>
                            )
                          )}
                        </ul> */}
                      </Box>
                    </Box>
                  </Grid>
                </Grid>

                <Box className="issue-trails">
                  <IssueHistoryList issueHistoryList={issueHistoryList} />
                </Box>
              </div>
            </Box>
          ) : dashboardUserDetailsData?.issueAnalysis &&
            dashboardUserDetailsData?.issueAnalysis[0]?.prefix.length === 0 ? (
            <Box className="incident-resources">No Data Available</Box>
          ) : (
            <Box
              className="incident-resources"
              sx={{ maxHeight: "50vh", overflowY: "auto" }}
            >
              <Grid container rowSpacing={3} columnSpacing={3}>
                {dashboardUserDetailsData?.issueAnalysis &&
                  dashboardUserDetailsData?.issueAnalysis[0]?.prefix.map(
                    (prefix: any) => {
                      const data =
                        dashboardUserDetailsData?.issueAnalysis[1]?.data[
                          prefix
                        ];

                      return (
                        <Grid
                          item
                          xs={12}
                          sm={12}
                          md={
                            prefix.length > 20
                              ? 12
                              : issueDataLength === 1
                              ? 12
                              : issueDataLength === 2
                              ? 12
                              : 6
                          }
                          lg={
                            prefix.length > 20
                              ? 12
                              : issueDataLength === 1
                              ? 12
                              : issueDataLength === 2
                              ? 6
                              : 4
                          }
                          key={prefix}
                        >
                          <Box className="incident-list">
                            <h3>
                              <Box className="word-break-all">{prefix}</Box>
                            </h3>
                            <table className="table1">
                              {data.map((item: any, index: any) => {
                                const key = Object.keys(item)[0];
                                const value = item[key];
                                return (
                                  <tr key={index}>
                                    <td>
                                      <Box className="word-break-all">
                                        {key}
                                      </Box>
                                    </td>
                                    <td>
                                      <Box className="word-break-all">
                                        {value ?? "N/A"}
                                      </Box>
                                    </td>
                                  </tr>
                                );
                              })}
                            </table>
                          </Box>
                        </Grid>
                      );
                    }
                  )}
              </Grid>
            </Box>
          )}
        </div>
        {dashboardUserDetailsData.panelType === "incidentDetail" ? (
          <div className="filter-footer">
            <Button
              variant="contained"
              color="secondary"
              className="btn-orange"
              onClick={handleSaveIssue}
              disabled={
                (formData?.assigned_user === initialFormData?.assigned_user ||
                  !formData?.assigned_user) &&
                (formData?.status === initialFormData?.status ||
                  !formData?.status) &&
                (formData?.comment === initialFormData?.comment ||
                  !formData?.comment)
              }
            >
              Save
            </Button>

            <button
              className="btn-orange btn-border cursor-pointer"
              onClick={handleClose}
              color="secondary"
            >
              Cancel
            </button>
          </div>
        ) : null}
      </Box>
    </>
  );
};

export default DashboardUserDetails;

const IssueHistoryList = ({
  issueHistoryList,
}: {
  issueHistoryList: any[];
}) => {
  return (
    <ul>
      {issueHistoryList?.map((item: any, index: number) => {
        const formattedDate = getFormattedDateInYYMMDD(item?.create_date);
        const formattedTime = getFormattedTimeInDobuleDigit(item?.create_date);

        return (
          <li key={index}>
            <div className="list-box">
              <div className="date-time-col">
                <div className="time-col">
                  <strong> Date & Time : </strong>
                  {formattedDate} - {formattedTime}
                </div>
              </div>

              {item?.assigned_by && <strong>{`${item?.assigned_by} `}</strong>}
              {item?.assigned_by && item?.assigned_user && (
                <>
                  assigned to <strong>{item?.assigned_user}</strong> and{" "}
                </>
              )}
              {item?.status && (
                <>
                  changed the Status as{" "}
                  <strong className={`status ${item?.status.toLowerCase()}`}>
                    {item?.status}
                  </strong>
                </>
              )}
              {item?.comment && (
                <div className="comment word-break-all max-width100 break-word">
                  <strong>Comment: </strong>
                  {`${item.comment}`}
                </div>
              )}
            </div>
          </li>
        );
      })}
    </ul>
  );
};
