import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Grid,
  TextField,
  FormControl,
  FormControlLabel,
  Switch,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { executionNameSchema } from "../../../schemas";
import CustomAccordion from "../Accordian/CustomAccordion";

interface ExecutionNameProps {
  handleChangeAccordion?: any;
  errors?: any;
  setErrors?: any;
  executionName?: any;
  setExecutionName?: any;
  executionMode?: boolean;
  setExecutionMode?: any;
  isShowExecutionMode?: boolean;
}

const ExecutionName = ({
  handleChangeAccordion,
  errors,
  setErrors,
  executionName,
  setExecutionName,
  executionMode,
  setExecutionMode,
  isShowExecutionMode = true,
}: ExecutionNameProps) => {
  const handleExeuctionName = (e: any) => {
    setExecutionName(e.target.value);
    validateField(e.target.name, e.target.value);
  };

  const handleExecutionModeChange = (e: any) => {
    setExecutionMode && setExecutionMode(e.target.checked);
  };

  const validateField = async (name: any, value: any) => {
    try {
      const partialFormData = { executionName, [name]: value };
      await executionNameSchema.validateAt(name, partialFormData);
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: "" }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  return (
    <CustomAccordion
      expandId="panel2d-header-ExecutionName"
      title={"Execution Name"}
      isEnabled={true}
      isDefaultExpaned={true}
    >
      <Grid container spacing={4}>
        <Grid item xs={12} sm={12} md={4} lg={4}>
          <TextField
            type="text"
            fullWidth
            variant="outlined"
            placeholder="Enter execution name"
            className={`form-control-autocomplete ${
              errors?.execution_name ? "has-error" : ""
            }`}
            value={executionName}
            name="execution_name"
            onChange={handleExeuctionName}
            error={!!errors?.execution_name}
            helperText={errors?.execution_name || ""}
          />
        </Grid>
        {isShowExecutionMode && (
          <Grid item xs={12} sm={12} md={4} lg={4}>
            <FormControl component="fieldset" className="switch-controller">
              <FormControlLabel
                control={
                  <Switch
                    checked={executionMode}
                    onChange={handleExecutionModeChange}
                    name="executionMode"
                    color="primary"
                    sx={{
                      "& .MuiSwitch-switchBase.Mui-checked": {
                        color: "var(--orange)",
                      },
                      "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                        {
                          backgroundColor: "var(--orange)",
                        },
                    }}
                  />
                }
                label={
                  <Typography>
                    {executionMode
                      ? "Execution in Background"
                      : "Execution in Normal"}
                  </Typography>
                }
              />
            </FormControl>
          </Grid>
        )}
      </Grid>
    </CustomAccordion>
  );
};

export default ExecutionName;
