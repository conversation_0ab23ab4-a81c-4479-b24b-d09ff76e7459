import {
  Autocomplete,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  TextareaAutosize,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  massComparisonIssuesUpadate,
  massValidationIssuesUpadate,
} from "../../../services/IssueManagementService";
import { useToast } from "../../../services/utils";
import { issueStatus } from "../../../services/constants";

interface IncidentUserandStatusProps {
  userDialogData: any;
  setUserDialogData: any;
  allUsersList: any;
  setDetailViewDataRows: any;
  setSelectedIds: any;
  issueFrom?: "comparison" | "validation";
}

const IncidentUserandStatusDialog = ({
  userDialogData,
  setUserDialogData,
  allUsersList,
  setDetailViewDataRows,
  setSelectedIds,
  issueFrom = "comparison",
}: IncidentUserandStatusProps) => {
  const userInfoString = localStorage.getItem("userInfo");
  const userInfo = userInfoString ? JSON.parse(userInfoString) : null;
  const { showToast } = useToast();
  const [charCount, setCharCount] = useState(0);
  const [errors, setErrors] = useState({
    exceedCount: false,
  });

  const [formData, setFormData] = useState<any>({
    status: "",
    assigned_user: "",
    comment: "",
  });

  const handleCloseDialog = () => {
    setFormData({});
    setUserDialogData((prev: any) => ({
      ...prev,
      status: false,
      dialogName: "",
    }));
    setErrors((prev: any) => ({
      ...prev,
      exceedCount: false,
    }));
    setCharCount(0);
  };

  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };
  const handleStatusUpdate = () => {
    const payload = {
      issue_ids: userDialogData?.selectedIds,
      issues_update: {
        assigned_user: formData?.assigned_user || "",
        assigned_by: userInfo?.username || "",
        status: formData?.status || "",
        comment: formData?.comment || "",
      },
    };
    const cleanedPayload = Object.fromEntries(
      Object.entries(payload).filter(
        ([key, value]) => value !== null && value !== ""
      )
    );

    if (issueFrom === "comparison") {
      massComparisonIssuesUpadate(cleanedPayload)
        .then((response) => {
          if (response) {
            setDetailViewDataRows((prevState: any) =>
              prevState.map((item: any) => {
                if (userDialogData?.selectedIds?.includes(item.rowId)) {
                  return {
                    ...item,
                    current_status: formData?.status,
                    assigned_user: formData?.assigned_user,
                    comment: formData?.comment,
                  };
                }
                return item;
              })
            );
            setSelectedIds([]);
            showToast("Issue updated successfully!", "success");
          }
        })
        .catch((error) => {
          showToast(`Cannot update issue`, "error");
        });
    } else {
      massValidationIssuesUpadate(payload)
        .then((response) => {
          if (response) {
            setDetailViewDataRows((prevState: any) =>
              prevState.map((item: any) => {
                if (userDialogData?.selectedIds?.includes(item.rowId)) {
                  return {
                    ...item,
                    current_status: formData?.status,
                    assigned_user: formData?.assigned_user,
                    comment: formData?.comment,
                  };
                }
                return item;
              })
            );
            setSelectedIds([]);
            showToast("Issue updated successfully!", "success");
          }
        })
        .catch((error) => {
          showToast(`Cannot update issue`, "error");
        });
    }
  };

  const handleChangeStatus = (value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      status: value,
    }));
  };
  const handleChangeComment = (event: any) => {
    const newComment = event.target.value;
    const limitedComment =
      newComment.length <= 500 ? newComment : newComment.slice(0, 500);
    setCharCount(limitedComment.length);
    setFormData((prev: any) => ({
      ...prev,
      comment: limitedComment,
    }));
    setErrors((prev: any) => ({
      ...prev,
      exceedCount: limitedComment.length === 500,
    }));
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={userDialogData?.status}
      onClose={(event, reason) => handleClose(reason)}
      className="modal-dialog-1"
    >
      <DialogTitle className="dialog-title">
        <span>{userDialogData?.dialogName}</span>
        <button className="close-icon" onClick={handleCloseDialog}></button>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Box>
          <Grid container rowSpacing={3} columnSpacing={3}>
            <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
              <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                <Autocomplete
                  sx={{
                    "& .MuiSelect-select": {
                      height: "36px",
                    },
                  }}
                  className={`form-control-autocomplete`}
                  options={issueStatus}
                  getOptionLabel={(option) => option}
                  onChange={(event, value) => handleChangeStatus(value)}
                  value={formData?.status}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Update Status"
                      variant="outlined"
                    />
                  )}
                  isOptionEqualToValue={(option, value) => option === value}
                />
              </Grid>
            </Grid>
            <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
              <Autocomplete
                sx={{
                  "& .MuiSelect-select": {
                    height: "36px",
                  },
                }}
                className={`form-control-autocomplete`}
                options={allUsersList}
                getOptionLabel={(option) => {
                  return option?.username || "";
                }}
                value={
                  (allUsersList?.length > 0 &&
                    allUsersList?.find(
                      (user: { username: string | undefined }) =>
                        user.username === formData?.assigned_user
                    )) ||
                  null
                }
                onChange={(event, value) => {
                  setFormData((prev: any) => ({
                    ...prev,
                    assigned_user: value?.username,
                  }));
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Assign to user"
                    variant="outlined"
                  />
                )}
              />
            </Grid>
            <Grid
              item
              xs={12}
              sm={12}
              md={12}
              lg={12}
              xl={12}
              className="incident-box"
            >
              <Box className="comment-section">
                <Box>
                  <h3>
                    Comments&nbsp;
                    <span
                      className={`normal ${
                        errors?.exceedCount ? "required m-0" : ""
                      }`}
                    >
                      ({charCount}/500)
                    </span>
                  </h3>
                </Box>
                <Box className="comment-box">
                  <TextareaAutosize
                    className={`form-control-1 textarea`}
                    minRows={3}
                    maxRows={6}
                    aria-label="minimum height"
                    placeholder="Add a comment"
                    value={formData?.comment}
                    name={"comment"}
                    onChange={handleChangeComment}
                  />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          type="button"
          color="secondary"
          variant="contained"
          onClick={(event) => {
            handleCloseDialog();
            handleStatusUpdate();
          }}
          className="btn-orange"
          disabled={
            !(formData.status || formData.comment || formData.assigned_user)
          }
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default IncidentUserandStatusDialog;
