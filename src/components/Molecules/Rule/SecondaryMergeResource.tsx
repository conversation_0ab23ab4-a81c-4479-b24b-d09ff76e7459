import {
  Autocomplete,
  Box,
  Grid,
  Select,
  TextField,
  MenuItem,
  Button,
  Tooltip,
} from "@mui/material";
import React, { useCallback, useState, useEffect, useMemo } from "react";
import { IconMinusWhite, IconPlusWhite } from "../../../common/utils/icons";
import { CheckCircleRounded } from "@mui/icons-material";
import { useRuleContext } from "../../../contexts/RuleContext";
import { mergeType } from "../../../services/constants";
import { getResourceColumnsDetailWithLoading } from "../../../services/resourcesService";

const SecondaryMergeResource = ({
  resourcesData,
  dataSetTypeIdx,
  resourcesColumnData,
  setSecondaryMergeResource,
  secondaryMergeResource,
  parentId,
  setIsLoading,
  errors,
  validateSecondaryResourceField,
}: any) => {
  const [isSelecting, setIsSelecting] = useState<boolean>(false);

  const onSelectResource = async (
    dataObj: any,
    index: number,
    parent_id: number
  ) => {
    if (!dataObj) {
      setSecondaryMergeResource((prevState: any) => {
        const updatedItems = prevState.map((item: any) => {
          if (item?.parent_id === parentId) {
            if (item.someProperty) {
              return { clean: true };
            }
            return { parent_id: parentId, isBtnShow: false };
          }
          return item;
        });
        return updatedItems;
      });
    }

    const result: any = await getResourceColumnsDetailWithLoading({
      resourceColumnDetailId:
        dataObj?.additional_properties?.resource_column_details_id,
      setIsLoading,
    });

    setSecondaryMergeResource((prevState: any) => {
      const existingResourceIndex = prevState.findIndex((resource: any) => {
        return resource.parent_id === parent_id;
      });

      if (existingResourceIndex !== -1) {
        const updatedResources = [...prevState];
        updatedResources[existingResourceIndex] = {
          ...updatedResources[existingResourceIndex],
          resource_id: dataObj?.id,
          resource_code: dataObj?.code,
          resource_column_details_id:
            dataObj?.additional_properties?.resource_column_details_id,
          isBtnShow: true,
          merge_on: [],
          resourceColumnDetails: result,
        };
        return updatedResources;
      } else {
        return [
          ...prevState,
          {
            resource_id: dataObj?.id,
            resource_code: dataObj?.code,
            resource_column_details_id:
              dataObj?.additional_properties?.resource_column_details_id,
            isBtnShow: true,
            merge_on: [],
            resourceColumnDetails: result,
          },
        ];
      }
    });
    const updatedSecondaryResource = [...secondaryMergeResource];
    const secondaryResource = updatedSecondaryResource.map((item: any) => ({
      resource_id: item.resource_id, // Handle the case where resource_id might be null or undefined
    }));
    validateSecondaryResourceField(
      "secondaryResource",
      index,
      "resource_id",
      secondaryResource
    );
  };
  const onSelectResourceColumns = (
    value: any,
    index: number,
    parent_id: number
  ) => {
    setSecondaryMergeResource((prevState: any) =>
      prevState.map((resource: any, index: any) => {
        if (resource.parent_id === parent_id) {
          return {
            ...resource,
            merge_on: value,
          };
        }
        return resource;
      })
    );
    const updatedSecondaryResource = [...secondaryMergeResource];
    const secondaryResource = updatedSecondaryResource.map((item: any) => ({
      resource_id: item.resource_id, // Handle the case where resource_id might be null or undefined
    }));
    validateSecondaryResourceField(
      "secondaryResource",
      index,
      "resource_columns",
      secondaryResource
    );
  };

  return (
    <>
      {secondaryMergeResource[dataSetTypeIdx]?.parent_id === parentId &&
      (secondaryMergeResource[dataSetTypeIdx]?.isBtnShow === undefined ||
        secondaryMergeResource[dataSetTypeIdx]?.isBtnShow === false) &&
      !secondaryMergeResource[dataSetTypeIdx]?.resource_id ? (
        <Grid item lg={1}>
          <Box
            className="label-text"
            sx={{ paddingTop: "10px", paddingRight: "16px" }}
          >
            &nbsp;
          </Box>
          <Tooltip title="Add Secondary Merge Resource" placement="top" arrow>
            <Button
              variant="contained"
              color="secondary"
              onClick={() => {
                const updateResource = () => {
                  setSecondaryMergeResource((prevState: any) => {
                    const updatedState = [...prevState];
                    const indexToUpdate = dataSetTypeIdx;
                    updatedState[indexToUpdate] = {
                      ...updatedState[indexToUpdate],
                      isBtnShow: true,
                      parent_id: parentId,
                      resource_id: null,
                      merge_on: [],
                    };
                    return updatedState;
                  });
                };

                updateResource();
              }}
              className="btn-orange btn-nostyle btn-blue"
              sx={{ gap: 1 }}
            >
              <IconPlusWhite />
            </Button>
          </Tooltip>
        </Grid>
      ) : (
        <>
          {secondaryMergeResource[dataSetTypeIdx]?.parent_id === parentId && (
            <>
              <Grid item xs={1}>
                &nbsp;
              </Grid>
              <Grid item xs={1}>
                <Box
                  className="label-text"
                  sx={{ paddingTop: "10px", paddingRight: "16px" }}
                >
                  &nbsp;
                </Box>
                <Tooltip
                  title="Remove Secondary Merge Resource"
                  placement="top"
                  arrow
                >
                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={() => {
                      setSecondaryMergeResource((prevState: any) => {
                        const updatedItems = prevState.map((item: any) => {
                          if (item?.parent_id === parentId) {
                            if (item.someProperty) {
                              return { clean: true };
                            }
                            return { parent_id: parentId, isBtnShow: false };
                          }
                          return item;
                        });
                        return updatedItems;
                      });
                    }}
                    className="btn-orange btn-nostyle btn-blue"
                    sx={{ gap: 1 }}
                  >
                    <IconMinusWhite />
                  </Button>
                </Tooltip>
              </Grid>
              <Grid item xs={11}>
                <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                  <Grid item xs={6}>
                    <Box
                      className="label-text"
                      sx={{ paddingTop: "10px", paddingRight: "16px" }}
                    >
                      Select Secondary Merge Resource
                    </Box>
                    <Autocomplete
                      fullWidth
                      //className={`form-control-autocomplete`}
                      className={`form-control-autocomplete ${
                        errors?.[
                          `secondaryResource[${dataSetTypeIdx}].resource_id`
                        ]
                          ? "has-error"
                          : ""
                      }`}
                      options={resourcesData}
                      getOptionLabel={(option: any) =>
                        option?.resource_name || ""
                      }
                      onChange={(event, value) => {
                        if (!value) {
                          // Handle the clear (set value to null or reset accordingly)
                          onSelectResource(null, dataSetTypeIdx, parentId);
                        } else {
                          // Handle the selection of a resource
                          onSelectResource(value, dataSetTypeIdx, parentId);
                        }
                      }}
                      renderInput={(params) => (
                        <div className="autocomplete-chips-direction">
                          <TextField
                            {...params}
                            variant="outlined"
                            placeholder="Select..."
                            InputLabelProps={{
                              shrink: true,
                            }}
                            error={Boolean(
                              !!errors?.[
                                `secondaryResource[${dataSetTypeIdx}].resource_id`
                              ]
                            )}
                            helperText={
                              errors?.[
                                `secondaryResource[${dataSetTypeIdx}].resource_id`
                              ] ?? ""
                            }
                          />
                        </div>
                      )}
                      value={
                        resourcesData.find((option: any) => {
                          const resourceId =
                            secondaryMergeResource[dataSetTypeIdx]?.resource_id;
                          return option.id === resourceId;
                        }) || null
                      }
                      renderOption={(props, option, state) => (
                        <li
                          {...props}
                          key={
                            option?.id ||
                            `${option?.resource_name}-${state.index}`
                          }
                        >
                          {option?.resource_name}
                        </li>
                      )}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Box
                      className="label-text"
                      sx={{ paddingTop: "10px", paddingRight: "16px" }}
                    >
                      Select Resource Column for Secondary Merge Resource
                    </Box>
                    <Autocomplete
                      fullWidth={true}
                      className={`form-control-autocomplete ${
                        errors?.[
                          `secondaryResource[${dataSetTypeIdx}].resource_columns`
                        ]
                          ? "has-error"
                          : ""
                      }`}
                      disabled={Boolean(isSelecting)}
                      multiple
                      options={
                        (secondaryMergeResource.length > 0 &&
                          secondaryMergeResource[
                            dataSetTypeIdx
                          ]?.resourceColumnDetails?.resource_column_properties?.resource_columns
                            .filter(
                              (item: any) => item.column_name !== "file_name"
                            )
                            .map((item: any) => item.column_name)) ||
                        []
                      }
                      getOptionLabel={(option) => option}
                      disableCloseOnSelect
                      onChange={(event, value) => {
                        onSelectResourceColumns(
                          value,
                          dataSetTypeIdx,
                          parentId
                        );
                      }}
                      value={secondaryMergeResource[dataSetTypeIdx]?.merge_on}
                      renderInput={(params: any) => (
                        <>
                          <div className="autocomplete-chips-direction">
                            <TextField
                              {...params}
                              variant="outlined"
                              placeholder="Select..."
                              InputLabelProps={{
                                shrink: true,
                              }}
                              error={Boolean(
                                !!errors?.[
                                  `secondaryResource[${dataSetTypeIdx}].resource_columns`
                                ]
                              )}
                              helperText={
                                errors?.[
                                  `secondaryResource[${dataSetTypeIdx}].resource_columns`
                                ] ?? ""
                              }
                            />
                          </div>
                        </>
                      )}
                      renderOption={(
                        props: any,
                        option: any,
                        { selected }: any
                      ) => (
                        <MenuItem
                          {...props}
                          key={option}
                          value={option}
                          sx={{ justifyContent: "space-between" }}
                        >
                          {option}
                          {selected ? (
                            <CheckCircleRounded color="info" />
                          ) : null}
                        </MenuItem>
                      )}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </>
          )}
        </>
      )}
    </>
  );
};

export default React.memo(SecondaryMergeResource);
