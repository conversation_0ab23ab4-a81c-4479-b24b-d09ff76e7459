import React, { <PERSON><PERSON><PERSON>Handler, ReactNode } from "react";
import { Button, ButtonProps } from "@mui/material";

interface IButtonComponent {
  children: ReactNode;
  handelClickEvent: MouseEventHandler<HTMLButtonElement>;
  buttonColor?: ButtonProps["color"];
  className?: string;
  disabled?: boolean;
}

const ButtonComponent: React.FC<IButtonComponent> = ({
  children,
  handelClickEvent,
  buttonColor,
  className,
  disabled = false,
}) => {
  return (
    <Button
      className={className}
      variant="contained"
      color={buttonColor || "secondary"}
      onClick={handelClickEvent}
      sx={{ margin: "0px" }}
      disabled={disabled}
    >
      {children}
    </Button>
  );
};

export default ButtonComponent;
