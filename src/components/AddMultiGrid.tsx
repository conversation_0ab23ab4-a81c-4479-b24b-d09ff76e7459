import React, { useState } from "react";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import { Button, Grid, TextField, IconButton, Box } from "@mui/material";
import Collapse from "@mui/material/Collapse";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { addMultiGridSchema } from "../schemas";
import { IconDeleteBlueSvg } from "../common/utils/icons";

interface AddMultiGridProps {
  gridValue: any;
  setGridValue: any;
  isViewOnly: boolean;
  heading: string;
}
const AddMultiGrid = React.memo(
  ({ gridValue, setGridValue, isViewOnly, heading }: AddMultiGridProps) => {
    const [isAddNewGrid, setIsAddNewGrid] = useState(false);
    const [checked, setChecked] = React.useState(false);
    const [newGrid, setNewGrid] = useState({
      name: "",
      value: "",
    });
    const [error, setErrors] = useState<any>({});
    const validateField = async (name: any, value: any) => {
      try {
        // Create a partial form data object with only the field being changed
        const partialFormData = { ...newGrid, [name]: value };
        await addMultiGridSchema.validateAt(name, partialFormData);
        // If validation passes, clear any previous errors for this field
        setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
      } catch (validationError: any) {
        // If validation fails, set the error message for this field
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          [name]: validationError.message,
        }));
      }
    };

    const handleAddNewVariable = (name: string, value: any) => {
      setNewGrid((prev) => ({
        ...prev,
        [name.toLowerCase()]: value,
      }));
      validateField(name, value);
    };

    const saveNewVariable = async () => {
      try {
        await addMultiGridSchema.validate(newGrid, {
          abortEarly: false,
        });
        setGridValue((prev: any) => ({
          ...prev,
          [newGrid.name.toLowerCase()]: newGrid.value,
        }));
        setIsAddNewGrid(false);
        setChecked((prev) => !prev);
        setNewGrid({
          name: "",
          value: "",
        });
      } catch (validationErrors: any) {
        const newErrors: { [key: string]: string } = {};
        if (validationErrors && validationErrors.inner) {
          validationErrors.inner.forEach(
            (error: { path: string | number; message: string }) => {
              newErrors[error.path] = error.message;
            }
          );
        }
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          ...newErrors,
        }));
      }
    };
    const handleDeleteVariable = (key: string) => {
      const { [key]: _, ...rest } = gridValue;
      setGridValue(rest);
    };
    return (
      <>
        <Box className="params-box">
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-start",
              columnGap: "8px",
              minHeight: "23px",
              marginBottom: "8px",
            }}
          >
            <label className="label-text mb-0">{heading}:</label>
            <div className="avail-columns-group mb-0">
              {!isAddNewGrid && (
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={() => {
                    setIsAddNewGrid(true);
                    setChecked((prev) => !prev);
                  }}
                  className="btn-orange btn-dark btn-sm plus-btn-sm"
                >
                  <AddSharpIcon />
                </Button>
              )}
            </div>
          </Box>
          <div className="avail-columns-group">
            {gridValue &&
              Object.keys(gridValue).map((key, index) => (
                <Box key={index} className="avail-columns">
                  <button
                    type="button"
                    style={{ display: `${key === "" ? "none" : "block"}` }}
                  >
                    {key} : {gridValue[key]}
                  </button>
                  <IconButton
                    sx={{ color: "grey" }}
                    onClick={() => handleDeleteVariable(key)}
                  >
                    <IconDeleteBlueSvg />
                  </IconButton>
                </Box>
              ))}
          </div>
          <Box>
            <Collapse
              in={checked}
              style={{ transformOrigin: "0 0 0 0" }}
              {...(checked ? { timeout: 1000 } : {})}
              className="collapse-no-bdr"
            >
              <Grid
                container
                rowSpacing={2}
                columnSpacing={2.5}
                className="add-variable-form"
              >
                <Grid item xs={6} sm={6} md={4} lg={3}>
                  <label className="label-text">
                    Name <span className="required-asterisk">*</span>
                  </label>
                  <TextField
                    type="text"
                    name="name"
                    value={newGrid.name}
                    onChange={(event) =>
                      handleAddNewVariable(
                        event.target.name,
                        event.target.value
                      )
                    }
                    fullWidth
                    variant="outlined"
                    className={`form-control-autocomplete editor-textbox ${
                      error?.name ? "has-error" : ""
                    }`}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!error?.name}
                    helperText={error?.name}
                  />
                </Grid>

                <Grid item xs={6} sm={6} md={4} lg={3}>
                  <label className="label-text">
                    Value <span className="required-asterisk">*</span>
                  </label>
                  <TextField
                    type="text"
                    name="value"
                    value={newGrid.value}
                    onChange={(event) =>
                      handleAddNewVariable(
                        event.target.name,
                        event.target.value
                      )
                    }
                    fullWidth
                    variant="outlined"
                    className={`form-control-autocomplete editor-textbox ${
                      error?.value ? "has-error" : ""
                    }`}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    error={!!error?.value}
                    helperText={error?.value}
                  />
                </Grid>
                <Grid item>
                  <Box>
                    <Box
                      sx={{
                        display: { md: "block", sm: "none", xs: "none" },
                      }}
                      className="label-text"
                    >
                      &nbsp;
                    </Box>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        columnGap: 2.5,
                        justifyContent: "flex-end",
                      }}
                    >
                      <Button
                        color="secondary"
                        variant="contained"
                        className="btn-orange"
                        onClick={saveNewVariable}
                      >
                        <SaveOutlinedIcon /> Save
                      </Button>
                      <Button
                        color="secondary"
                        variant="contained"
                        onClick={() => {
                          setIsAddNewGrid(false);
                          setChecked((prev) => !prev);
                          setErrors((prev: any) => ({
                            ...prev,
                            name: undefined,
                            value: undefined,
                          }));
                          setNewGrid({
                            name: "",
                            value: "",
                          });
                        }}
                        className="btn-orange btn-dark"
                      >
                        Cancel
                      </Button>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Collapse>
          </Box>
        </Box>
      </>
    );
  }
);

export default AddMultiGrid;
