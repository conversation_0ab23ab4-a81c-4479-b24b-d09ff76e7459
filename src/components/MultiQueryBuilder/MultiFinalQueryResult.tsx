// import { Box, Grid, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import RenderVariables from "../Molecules/Resource/RenderVariables";

interface Props {
  sqlQry: string;
  error?: any;
  setErrors: React.Dispatch<React.SetStateAction<any>>;
}

const MultiFinalQueryResult: React.FC<Props> = ({
  sqlQry,
  error,
  setErrors,
}: any) => {
  const {
    globalVariables,
    // setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    // tempLocalVariables,
    // setTempLocalVariables,
  } = useRuleResourceContext();

  const [variables, setVariables] = useState<any>([]);

  // Use a regular expression to find variables wrapped with $$
  const extractVariables = (query: any) => {
    const matches = query?.match(/\$\$(.*?)\$\$/g);
    if (!matches) return [];
    return matches.map((match: any) => match.slice(2, -2).toLowerCase());
  };

  // useEffect(() => {
  //   if (variables?.length > 0 && tempGlobalVariables) {
  //     console.log("tempGlobalVariables", tempGlobalVariables);
  //     const globalVars: any = {};
  //     variables.forEach((variable: any) => {
  //       if (tempGlobalVariables[variable] !== undefined) {
  //         globalVars[variable] = tempGlobalVariables[variable];
  //       } else {
  //         globalVars[variable] = "abc";
  //       }
  //     });
  //     // Compare the current tempLocalVariables with the new ones to avoid unnecessary updates
  //     if (JSON.stringify(tempGlobalVariables) !== JSON.stringify(globalVars)) {
  //       // setTempLocalVariables(globalVars);
  //       setTempGlobalVariables(globalVars);
  //       console.log(globalVars, ">>>");
  //     }
  //   }
  // }, [
  //   variables,
  //   tempGlobalVariables,
  //   // tempLocalVariables,
  //   // setTempLocalVariables,
  // ]);
  useEffect(() => {
    const queryVariables: any = extractVariables(sqlQry?.query);
    const filterVariables: any = extractVariables(sqlQry?.filter);

    const getUniqueValues = (arr: any) => {
      return arr.filter((v: any, i: any, a: any) => a.indexOf(v) === i);
    };
    setVariables(
      getUniqueValues([...queryVariables, ...filterVariables]) ?? []
    );
  }, [sqlQry?.query, sqlQry?.filter]);
  useEffect(() => {
    if (variables?.length > 0 && globalVariables) {
      const globalVars: any = {};
      variables.forEach((variable: any) => {
        if (globalVariables[variable] !== undefined) {
          globalVars[variable] = globalVariables[variable];
        } else if (tempGlobalVariables[variable] !== undefined) {
          globalVars[variable] = tempGlobalVariables[variable];
        } else {
          globalVars[variable] = "";
        }
      });
      // Compare the current tempLocalVariables with the new ones to avoid unnecessary updates
      if (JSON.stringify(tempGlobalVariables) !== JSON.stringify(globalVars)) {
        // setTempLocalVariables(globalVars);
        setTempGlobalVariables(globalVars);
      }
    } else {
      setTempGlobalVariables({});
    }
  }, [variables]);

  // const isValidName = (name: string) => /^[a-zA-Z0-9_]+$/.test(name);

  // const handleAddColumn = (e: any) => {
  //   if (e.target.name === "") {
  //     toast.warning("Please enter Variable Name");
  //     return;
  //   }
  //   if (!isValidName(e.target.name)) {
  //     toast.warning(
  //       "Variable Name can only contain alphabets, numbers, and underscores."
  //     );
  //     return;
  //   }
  //   setTempGlobalVariables((prevGlobalVariables: any) => ({
  //     ...prevGlobalVariables,
  //     [e.target.name]: e.target.value,
  //   }));
  //   // setTempGlobalVariables((prevLocalVariables: any) => ({
  //   //   ...prevLocalVariables,
  //   //   [e.target.name]: e.target.value,
  //   // }));
  //   setErrors((prevErrors: any) => ({
  //     ...prevErrors,
  //     [e.target.name]: e.target.value ? "" : `Please enter ${e.target.name}`,
  //   }));
  // };

  return (
    <>
      {/* {variables?.length > 0 &&
        variables.map((variable: any, index: number) => (
          <tr key={index}>
            <td>
              <div className="inner-column">
                <div className="label auto-width">
                  <strong>{variable}</strong>
                  <span className="required-asterisk">*</span>:
                </div>
                <TextField
                  fullWidth
                  type="text"
                  // placeholder={`Enter ${variable} value`}
                  name={variable}
                  className={`form-control  ${
                    error?.[variable] ? "has-error" : ""
                  }`}
                  onChange={handleAddColumn}
                  sx={{ width: "100%" }}
                  value={
                    tempGlobalVariables &&
                    typeof tempGlobalVariables === "object" &&
                    tempGlobalVariables[variable] !== undefined
                      ? tempGlobalVariables[variable]
                      : ""
                  }
                  error={!!error?.[variable] || !!error?.duplicateColumn}
                  helperText={error?.[variable] || error?.duplicateColumn || ""}
                />
              </div>
            </td>
          </tr>
        ))} */}
      <RenderVariables
        error={error}
        setErrors={setErrors}
        // editorRef={editorRef}
        // variables={tempGlobalVariables}
        // setVariables={setTempGlobalVariables}
        // globalVariables={globalVariables}
        // setGlobalVariables={setGlobalVariables}
      />
    </>
  );
};

export default React.memo(MultiFinalQueryResult);
