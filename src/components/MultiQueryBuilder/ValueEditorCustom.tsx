import { Input } from "@mui/material";
import { ValueEditor as De<PERSON>ultV<PERSON><PERSON>Editor } from "react-querybuilder";

interface ValueEditorProps {
  field: string;
  fieldData: any;
  operator: string;
  value: any;
  handleOnChange: (value: any) => void;
  valueSource: any;
  path: any; // Update the type to number[]
  level: any; // Update the type to number
  schema: any;
  rule: any;
}

const ValueEditorCustom: React.FC<ValueEditorProps> = (props) => {
  const { field, fieldData, operator, value, handleOnChange } = props;

  if (operator === "case" || operator === "CASE") {
    const handleWhenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      handleOnChange({ ...value, when: e.target.value });
    };
    const handleThenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      handleOnChange({ ...value, then: e.target.value });
    };
    const handleElseChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      handleOnChange({ ...value, else: e.target.value });
    };

    return (
      <>
        <Input
          onChange={handleWhenChange}
          value={value && value.when ? value.when : ""}
          aria-label="WHEN"
          name="WHEN"
          placeholder="WHEN"
        />
        <Input
          onChange={handleThenChange}
          value={value && value.then ? value.then : ""}
          aria-label="THEN"
          name="THEN"
          placeholder="THEN"
        />
        <Input
          onChange={handleElseChange}
          value={value && value.else ? value.else : ""}
          aria-label="ELSE"
          name="ELSE"
          placeholder="ELSE"
        />
      </>
    );
  }
  if (
    operator === "GROUP_BY" ||
    operator === "GROUP BY" ||
    operator === "group_by"
  ) {
    return <></>;
  }
  if (
    operator === "ORDER_BY" ||
    operator === "ORDER BY" ||
    operator === "order_by"
  ) {
    return <></>;
  }
  if (operator === "HAVING" || operator === "having") {
    return <></>;
  }

  return <DefaultValueEditor {...props} />;
};

export default ValueEditorCustom;
