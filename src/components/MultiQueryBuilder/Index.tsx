import React, { useEffect, useState } from "react";
import {
  defaultOperators,
  defaultValidator,
  formatQuery,
} from "react-querybuilder";
import "react-querybuilder/dist/query-builder.scss";
import "../../styles/styles.scss";
import { Box, Grid, Typography } from "@mui/material";
import QueryBuilderComponent from "./SQL/QueryBuilder.component";
import SQLFormat from "./SQL/SQLFormat";
// import FinalQueryResult from "./MultiFinalQueryResult";
import { fields } from "./fields";
import { customRules } from "../../services/constants/Rules";
import { customOperators } from "../../services/constants/Operators";
import MultiFinalQueryResult from "./MultiFinalQueryResult";

interface Rule {
  field: string;
  operator: string;
  value: string | number | boolean;
}

interface Query {
  combinator: string;
  rules: Rule[];
}

interface MyComponentProps {
  handleChange?: any;
  //tempQuery?: any;
  isMarginToTop?: boolean;
  errors?: any;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  isDailogEdit?: boolean;
}

const initialQuery: Query = { combinator: "and", rules: [] };

const QueryBuilder: React.FC<MyComponentProps> = (props: any) => {
  const {
    handleChange,
    //tempQuery,
    isMarginToTop,
    errors,
    setErrors,
    isDailogEdit,
  } = props;

  const [query, setQuery] = useState<Query>(initialQuery);
  const [sqlQry, setSqlQry] = useState<any>("");
  const [conditions, setConditions] = useState<string>("");
  const [groupBy, setGroupBy] = useState<string>("");

  const myCustomOperators = [...defaultOperators, ...customOperators];
  useEffect(() => {
    const handelBuildQry = () => {
      const allconditions = query?.rules
        .filter((rule) => rule.field && rule.value)
        .map((rule) => customRules(rule))
        .filter(Boolean)
        .join(" and ");
      setConditions(allconditions);

      const groupByAll = query?.rules
        .filter((rule) => rule.operator === "group_by")
        .map((rule) => rule.field);
      setGroupBy(groupByAll.join(", "));

      const orderByAll = query?.rules
        .filter((rule) => rule.operator === "order_by")
        .map((rule) => rule.field);

      // let qry = `SELECT * FROM my_table `;
      let qry = "";
      let qryGroupBy = "";
      let qryOrderBY = "";
      let qryCondition = "";

      if (conditions.length > 0) {
        qryCondition = `WHERE (${allconditions}) `;
      }

      if (groupBy.length > 0) {
        qryGroupBy = `GROUP BY ${groupBy} `;
      }

      if (orderByAll.length > 0) {
        qryOrderBY = `ORDER BY ${orderByAll.join(", ")} `;
      }

      return `${qry} ${qryCondition} ${qryGroupBy} ${qryOrderBY}`;
    };
  }, [query, conditions, groupBy]);

  const handleQueryChange = (query: Query) => {
    setQuery(query);
  };

  useEffect(() => {
    if (sqlQry !== undefined) handleChange(sqlQry, []);
  }, [sqlQry]);

  return (
    <>
      <Grid
        container
        rowSpacing={2.5}
        columnSpacing={6.25}
        className="seprator-line1"
      >
        <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
          <Box
            className={`modal-left-column ${
              isMarginToTop ? "negative-margin" : ""
            } `}
          >
            {/* <Box className="sql-result-box">
          <FinalQueryResult sqlQry={sqlQry} />
        </Box> */}
            <Box className="sql-textarea-box">
              <SQLFormat
                isDailogEdit={isDailogEdit}
                setSqlQry={setSqlQry}
                //tempQuery={queryBuilderTempValue}
                errors={errors}
                setErrors={setErrors}
              />
            </Box>
            {/* convert query builder expression in query condition */}
            {/* <Box>{formatQuery(query, "sql")}</Box> */}
            <Box className="sql-result-box">
              {/* Final query result */}

              <MultiFinalQueryResult
                sqlQry={sqlQry}
                error={errors}
                setErrors={setErrors}
              />

              {/* custom SQL Query editor */}
            </Box>
          </Box>
        </Grid>
        {/* <Grid item xs={12} sm={12} md={6} lg={6} xl={6}>
          <Box className="sql-query-box">
            <Typography className="sub-title">
              Filter results using SQL query
            </Typography>
             
            <QueryBuilderComponent
              fields={fields}
              query={query}
              handleQueryChange={handleQueryChange}
              defaultValidator={defaultValidator}
              newOperators={myCustomOperators}
            />
          </Box>
        </Grid> */}
      </Grid>
    </>
  );
};

export default QueryBuilder;
