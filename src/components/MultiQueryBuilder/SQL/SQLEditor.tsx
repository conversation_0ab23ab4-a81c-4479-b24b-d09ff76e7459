import React, { useState, useRef, useEffect } from "react";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-sql";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-noconflict/ext-language_tools";
import { Box, TextField, Button, Grid } from "@mui/material";
// import AddSharpIcon from "@mui/icons-material/AddSharp";
// import { addNewVariableSchema } from "../../../schemas";
// import Collapse from "@mui/material/Collapse";
// import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
// import { toast } from "react-toastify";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import AvailableVariables from "../../Molecules/Variables/AvailableVariables";

interface SQLEditorProps {
  setCustomQuery: (query: []) => void;
  //tempQuery?: any;
  errors?: any;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  isDailogEdit?: boolean;
}
interface FormDataProps {
  name: string;
  query: string;
  filter: string;
}

const SQLEditor: React.FC<SQLEditorProps> = ({
  setCustomQuery,
  //tempQuery,
  errors,
  setErrors,
  isDailogEdit,
}) => {
  const {
    availColumns,
    // globalVariables,
    // tempGlobalVariables,
    // setTempGlobalVariables,
    queryBuilderTempValue,
  } = useRuleResourceContext();
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [formData, setFormData] = useState<FormDataProps>({
    name: "",
    query: "",
    filter: "",
  });
  const [queryOrFilter, setQueryOrFilter] = useState({
    isQueryColumn: true,
    isFilterColumn: false,
  });
  // const [isAddVariable, setIsAddVariable] = useState(false);
  // const [newVariable, setNewVariable] = useState({
  //   var_name: "",
  //   var_value: "",
  // });

  // const [checked, setChecked] = React.useState(false);

  const editorRef = useRef<AceEditor | null>(null);
  const filterEditorRef = useRef<AceEditor | null>(null);
  const editorOptions = {
    enableLiveAutocompletion: true,
    enableBasicAutocompletion: true,
    enableSnippets: false,
    showLineNumbers: true,
    tabSize: 2,
    fontSize: 20,
  };

  // useEffect(() => {
  //   console.log("called>>", globalVariables);
  //   setTempGlobalVariables((prev: any) => ({
  //     ...prev,
  //     ...globalVariables,
  //   }));
  // }, []);
  const handleColumnsForQueryOrFilter = (editorName: string) => {
    if (editorName == "queryEditor") {
      setQueryOrFilter({
        isQueryColumn: true,
        isFilterColumn: false,
      });
    } else {
      setQueryOrFilter({
        isQueryColumn: false,
        isFilterColumn: true,
      });
    }
  };
  const handleVariableSelect = (variable: any) => {
    const editor = queryOrFilter.isQueryColumn
      ? editorRef.current?.editor
      : filterEditorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      `$$${variable}$$` +
      " " +
      currentLine.slice(currentColumn);
    if (queryOrFilter.isQueryColumn) {
      setFormData((prevData) => ({
        ...prevData,
        query: updatedLine,
      }));
    } else {
      setFormData((prevData) => ({
        ...prevData,
        filter: updatedLine,
      }));
    }
    editor.session.insert(currentPosition, `$$${variable}$$`);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + variable.length + 4,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };
  const handleColumnSelect = (column: string) => {
    const editor = queryOrFilter.isQueryColumn
      ? editorRef.current?.editor
      : filterEditorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      `[${column}]` +
      " " +
      currentLine.slice(currentColumn);

    if (queryOrFilter.isQueryColumn) {
      setFormData((prevData) => ({
        ...prevData,
        query: updatedLine,
      }));
    } else {
      setFormData((prevData) => ({
        ...prevData,
        filter: updatedLine,
      }));
    }
    setSelectedColumns([...selectedColumns, column]);
    editor.session.insert(currentPosition, `[${column}]`);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + column.length + 2,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  const handleSqlChange = (value: string, editorName: string) => {
    switch (editorName) {
      case "name":
        setFormData((prevData) => ({
          ...prevData,
          name: value,
        }));
        if (value) {
          setErrors((prevError) => ({
            ...prevError,
            name: "",
          }));
        } else {
          setErrors((prevError) => ({
            ...prevError,
            name: "Please enter name",
          }));
        }
        break;
      case "filter":
        setFormData((prevData) => ({
          ...prevData,
          filter: value,
        }));
        break;
      case "query":
        setFormData((prevData) => ({
          ...prevData,
          query: value,
        }));
        if (value) {
          setErrors((prevError) => ({
            ...prevError,
            expression: "",
          }));
        } else {
          setErrors((prevError) => ({
            ...prevError,
            expression: "Please enter query",
          }));
        }
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    const finalQuery: any = formData;
    setCustomQuery(finalQuery);
  }, [formData]);

  useEffect(() => {
    if (isDailogEdit) {
      const newData = {
        name: queryBuilderTempValue?.name,
        query: queryBuilderTempValue?.expression,
        filter: queryBuilderTempValue?.filter_rule,
      };
      setFormData(newData);
    }
  }, [queryBuilderTempValue]);

  // const validateField = async (name: any, value: any) => {
  //   try {
  //     // Create a partial form data object with only the field being changed
  //     const partialFormData = { ...newVariable, [name]: value };
  //     await addNewVariableSchema.validateAt(name, partialFormData);
  //     // If validation passes, clear any previous errors for this field
  //     setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
  //   } catch (validationError: any) {
  //     // If validation fails, set the error message for this field
  //     setErrors((prevErrors) => ({
  //       ...prevErrors,
  //       [name]: validationError.message,
  //     }));
  //   }
  // };
  // const isValidName = (name: string) => /^[a-zA-Z0-9_]+$/.test(name);

  // const handleAddNewVariable = (name: string, value: any) => {
  //   if (!isValidName(name)) {
  //     toast.warning(
  //       "Variable Name can only contain alphabets, numbers, and underscores."
  //     );
  //     return;
  //   }
  //   setNewVariable((prev) => ({
  //     ...prev,
  //     [name.toLowerCase()]: value,
  //   }));
  //   validateField(name, value);
  // };

  // const saveNewVariable = async () => {
  //   try {
  //     await addNewVariableSchema.validate(newVariable, {
  //       abortEarly: false,
  //     });
  //     setTempGlobalVariables((prev: any) => ({
  //       ...prev,
  //       [newVariable.var_name.toLowerCase()]: newVariable.var_value,
  //     }));
  //     setIsAddVariable(false);
  //     setChecked((prev) => !prev);
  //     setNewVariable({
  //       var_name: "",
  //       var_value: "",
  //     });
  //   } catch (validationErrors: any) {
  //     const newErrors: { [key: string]: string } = {};
  //     if (validationErrors && validationErrors.inner) {
  //       validationErrors.inner.forEach(
  //         (error: { path: string | number; message: string }) => {
  //           newErrors[error.path] = error.message;
  //         }
  //       );
  //     }
  //     setErrors((prevErrors) => ({
  //       ...prevErrors,
  //       ...newErrors,
  //     }));
  //   }
  // };
  return (
    <Box>
      <Box sx={{ marginBottom: 3 }} className="available-box box-alt">
        <h3 className="mb-0 mb-8">Available Columns:</h3>
        <div className="avail-columns-group">
          {/* {CDMColumns &&
            CDMColumns.map((column, index) => ( */}
          {availColumns &&
            availColumns.map((column, index) => (
              <button
                className="avail-columns"
                key={index}
                onClick={() => handleColumnSelect(column)}
              >
                {column}
              </button>
            ))}
        </div>
      </Box>
      {/* <Box sx={{ marginBottom: 3 }} className="available-box">
        <h3 className="mb-0 mb-8">Available Variables:</h3>
        <div className="avail-columns-group">
          {tempGlobalVariables &&
            Object.keys(tempGlobalVariables).map((key, index) => (
              <button
                className="avail-columns"
                key={index}
                onClick={() => handleVariableSelect(key)}
              >
                {key}
              </button>
            ))}
          {!isAddVariable && (
            <Button
              variant="contained"
              color="secondary"
              onClick={() => {
                setIsAddVariable(true);
                setChecked((prev) => !prev);
              }}
              className="btn-orange btn-dark btn-sm"
            >
              <AddSharpIcon />
            </Button>
          )}
        </div>

        <>
          <Collapse
            in={checked}
            style={{ transformOrigin: "0 0 0 0" }}
            {...(checked ? { timeout: 1000 } : {})}
          >
            <Grid
              container
              rowSpacing={1}
              columnSpacing={1}
              className="add-variable-form"
            >
              <Grid item xs>
                <label className="label-text">
                  Variable name <span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  name="var_name"
                  // placeholder="Ex: Sample variable name"
                  value={newVariable.var_name}
                  onChange={(event) =>
                    handleAddNewVariable(event.target.name, event.target.value)
                  }
                  fullWidth
                  variant="outlined"
                  className={`form-control-autocomplete editor-textbox ${
                    errors?.var_name ? "has-error" : ""
                  }`}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.var_name}
                  helperText={errors?.var_name}
                />
              </Grid>

              <Grid item xs>
                <label className="label-text">
                  Variable value <span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  name="var_value"
                  value={newVariable.var_value}
                  // placeholder="Ex: Sample variable value"
                  onChange={(event) =>
                    handleAddNewVariable(event.target.name, event.target.value)
                  }
                  fullWidth
                  variant="outlined"
                  className={`form-control-autocomplete editor-textbox ${
                    errors?.var_value ? "has-error" : ""
                  }`}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.var_value}
                  helperText={errors?.var_value}
                />
              </Grid>
              <Grid item>
                <Box>
                  <Box
                    sx={{ display: { md: "block", sm: "block", xs: "none" } }}
                    className="label-text"
                  >
                    &nbsp;
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      columnGap: "8px",
                      justifyContent: "flex-end",
                    }}
                  >
                    <Button
                      type="submit"
                      color="secondary"
                      variant="contained"
                      className="btn-orange"
                      onClick={saveNewVariable}
                    >
                      <SaveOutlinedIcon /> Save
                    </Button>
                    <Button
                      color="secondary"
                      variant="contained"
                      onClick={() => {
                        setIsAddVariable(false);
                        setChecked((prev) => !prev);
                        setErrors((prev: any) => ({
                          ...prev,
                          var_name: undefined,
                          var_value: undefined,
                        }));
                        setNewVariable({
                          var_name: "",
                          var_value: "",
                        });
                      }}
                      className="btn-orange btn-dark"
                    >
                      Cancel
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Collapse>
        </>
      </Box> */}
      <Box sx={{ marginBottom: 3 }}>
        <Box className={`available-box `}>
          <AvailableVariables
            error={errors}
            setErrors={setErrors}
            handleVariableSelect={handleVariableSelect}
          />
        </Box>
      </Box>

      <Box sx={{ marginBottom: 2 }}>
        <TextField
          type="text"
          title="Heading"
          name="heading"
          value={formData.name}
          // placeholder="Ex: Sample name"
          onChange={(event) => handleSqlChange(event.target.value, "name")}
          fullWidth
          // label="Name"
          label={
            <span>
              Name<span className="required-asterisk">*</span>
            </span>
          }
          variant="outlined"
          className={`form-control-autocomplete editor-textbox ${
            errors?.name ? "has-error" : ""
          }`}
          InputLabelProps={{
            shrink: true,
          }}
          error={!!errors?.name}
          helperText={errors?.name}
        />
      </Box>
      <Box sx={{ marginBottom: 2 }}>
        <Box className="label-text">
          Write a custom SQL Query<span className="required-asterisk">*</span>
        </Box>
        <AceEditor
          mode="sql"
          theme="github"
          onChange={(value) => handleSqlChange(value, "query")}
          onFocus={() => handleColumnsForQueryOrFilter("queryEditor")}
          value={formData.query}
          width="100%"
          height="100px"
          editorProps={{ $blockScrolling: true }}
          setOptions={editorOptions}
          ref={editorRef}
          showGutter
          // placeholder={` ${formData.query ? "" : "Write your query here..."} `}
          aria-label="queryEditor"
          name="editor"
          fontSize={15}
          minLines={10}
          maxLines={10}
          showPrintMargin={false}
          className={`sql-editor ${errors?.expression ? "has-error" : ""}`}
        />
        <span className="validation-error ml-0">
          {errors?.expression && (
            <span className="ml-14">{errors?.expression}</span>
          )}
        </span>
      </Box>
      <Box sx={{ marginBottom: 2 }}>
        <Box className="label-text">Write a custom Filter</Box>
        <AceEditor
          mode="sql"
          theme="github"
          onChange={(value) => handleSqlChange(value, "filter")}
          onFocus={() => handleColumnsForQueryOrFilter("filterEditor")}
          value={formData.filter}
          width="100%"
          height="100px"
          editorProps={{ $blockScrolling: true }}
          setOptions={editorOptions}
          ref={filterEditorRef}
          showGutter
          // placeholder={` ${formData.filter ? "" : "Write your filters here..."
          //   } `}
          aria-label="filterEditor"
          name="filterEditor"
          fontSize={15}
          minLines={10}
          maxLines={10}
          showPrintMargin={false}
          className="sql-editor"
        />
      </Box>
    </Box>
  );
};

export default SQLEditor;
