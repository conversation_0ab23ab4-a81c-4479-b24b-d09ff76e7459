import { Box } from "@mui/material";
import React from "react";
import SQLEditor from "./SQLEditor";

interface SQLFormatProps {
  setSqlQry: (query: []) => void; // need to change
  //tempQuery?: any;
  errors?: any;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  isDailogEdit?: boolean;
}

const SQLFormat: React.FC<SQLFormatProps> = ({
  setSqlQry,
  //tempQuery,
  errors,
  setErrors,
  isDailogEdit,
}) => {
  return (
    <Box>
      {/* <h3> 
      Write a custom SQL query</h3> */}
      <Box>
        <SQLEditor
          setCustomQuery={setSqlQry}
          //tempQuery={tempQuery}
          errors={errors}
          setErrors={setErrors}
          isDailogEdit={isDailogEdit}
        />
      </Box>
    </Box>
  );
};

export default SQLFormat;
