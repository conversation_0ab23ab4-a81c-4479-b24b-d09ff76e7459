import { convertToReadableFormat } from "../../services/utils";

export const validator = (r: any) => !!r.value;

export const customFields: string[] = ['AccountCode', 'SecurityID', 'LongShortCode', 'Quantity', 'MarketValueBase', 'AccruedIncomeBase', 'AccruedIncomeBase', 'BaseUnitCost', 'TotalCostBase', 'UnrealizedTotalGain', 'PositionEffectiveDate', 'FXRate', 'CUSIP'];

export const dynamicsFields = (): any[] => {
  const newFields = customFields.map((field) => ({
    name: field,
    label: convertToReadableFormat(field),
    placeholder: convertToReadableFormat(field),
    validator,
  }));
  return newFields;
};

export const fields: any[] = [
  {
    name: 'AccountCode',
    label: 'Account Code',
    placeholder: 'Account Code',
    valueSources: ['field', 'value'],
    validator,
  },
  {
    name: 'SecurityID',
    label: 'Security ID',
    placeholder: 'Enter ID',
    valueSources: ['field', 'value'],
    validator,
  },
  {
    name: 'LongShortCode',
    label: 'Long Short Code',
    valueSources: ['field', 'value'],
    validator,
  },
  {
    name: 'Quantity',
    label: 'Quantity',
    valueSources: ['field', 'value'],
    validator,
  },
  {
    name: 'MarketValueBase',
    label: 'Market Value Base',
    valueSources: ['field', 'value'],
    inputType: 'number',
    validator,
  },
  {
    name: 'AccruedIncomeBase',
    label: 'Accrued Income Base',
    valueSources: ['field', 'value'],
    inputType: 'number',
    validator,
  },
  {
    name: 'BaseUnitCost',
    label: 'Base Unit Cost',
    valueSources: ['field', 'value'],
    inputType: 'number',
    validator,
  },
  {
    name: 'TotalCostBase',
    label: 'Total Cost Base',
    valueSources: ['field', 'value'],
    inputType: 'number',
    validator,
  },
  {
    name: 'UnrealizedTotalGain',
    label: 'Unrealized Total Gain',
    valueSources: ['field', 'value'],
    inputType: 'number',
    validator,
  },
  {
    name: 'PositionEffectiveDate',
    label: 'Position Effective Date',
    valueSources: ['field', 'value'],
    inputType: 'date',
  },
  {
    name: 'FXRate',
    label: 'FX Rate',
    valueSources: ['field', 'value'],
    validator,
  },
  {
    name: 'CUSIP',
    label: 'CUSIP',
    valueSources: ['field', 'value'],
    validator,
  },
];

export const derivedColumns: any[] = [
  {
    name: 'LongShortCode',
    label: 'Long Short Code',
    valueSources: ['value', 'field'],
    comparator: 'groupNumber',
    groupNumber: [
      { name: 'firstName', label: 'First Name' },
      { name: 'lastName', label: 'Last Name' },
      { name: 'age', label: 'Age' },
      // Add more fields as needed
    ],
    validator,
  },
];
