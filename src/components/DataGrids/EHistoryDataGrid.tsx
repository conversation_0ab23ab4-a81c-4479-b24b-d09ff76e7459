import * as React from "react";
import {
  DataGridPremium,
  GridColDef,
  GridRowModel,
  GridToolbarContainer,
  GridToolbarExport,
} from "@mui/x-data-grid-premium";
import Grid from "@mui/material/Grid";
import { Box, Tooltip, Button, Stack, Menu, MenuItem } from "@mui/material";
import FlexBetween from "../FlexBetween";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import Typography from "@mui/material/Typography";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CustomPaginationActions from "../Molecules/Paginations/CustomPaginationActions";
import { IconDownloadSvg } from "../../common/utils/icons";
import { disableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";

interface DataTableProps {
  dataRows: any[];
  dataColumns: GridColDef[];
  loading?: boolean;
  checkboxSelection?: boolean;
  className?: string;
  buttonText?: string;
  buttonClass?: string;
  buttonClick?: () => void;
  buttonIcon?: React.ReactNode;
  dataListTitle?: string;
  isExportButtonRequired?: boolean;
  rowCount?: number;
  pageSizeOptions?: number[];
  paginationModel?: any;
  onPaginationModelChange?: (params: any) => void;
  historyType: string;
  downloadFromAPIFile?: (params: any) => void;
  setLoading?: (params: any) => any;
  disableColumnFilter?: boolean;
  rowGroupByColumnProps?: any;
  singlePageMaxHeightDiff?: number;
  isShowInComparisonHistory?: boolean;
}

const EHistoryDataTable: React.FC<DataTableProps> = ({
  dataRows,
  dataColumns,
  checkboxSelection = false,
  loading,
  className,
  buttonText,
  buttonClass,
  buttonClick,
  buttonIcon,
  dataListTitle,
  isExportButtonRequired,
  rowCount,
  pageSizeOptions,
  paginationModel,
  onPaginationModelChange,
  historyType,
  downloadFromAPIFile,
  setLoading,
  disableColumnFilter,
  rowGroupByColumnProps = { ...disableRowGroupByColumnProps },
  singlePageMaxHeightDiff,
  isShowInComparisonHistory = false,
}) => {
  function CustomToolbar() {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const handleClick = (event: any) => {
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleExportExcel = () => {
      downloadFromAPIFile && downloadFromAPIFile(setLoading);
      handleClose();
    };
    return (
      <GridToolbarContainer className="custom-toolbar">
        <Stack
          spacing={1}
          direction="row"
          alignItems="center"
          justifyContent={dataListTitle ? "space-between" : "flex-end"}
          sx={{ width: "100%", marginRight: 3, marginLeft: 3 }}
        >
          {dataListTitle && <h2 className="list-title">{dataListTitle}</h2>}
          <Box sx={{ display: "flex", columnGap: "10px" }}>
            {buttonClick && (
              <Button
                color="secondary"
                variant="contained"
                onClick={buttonClick}
                className={buttonClass}
              >
                <span
                  style={{
                    marginRight: "4px",
                    position: "relative",
                    top: "2px",
                  }}
                >
                  {buttonIcon}
                </span>{" "}
                {buttonText}
              </Button>
            )}
            {downloadFromAPIFile && (
              <>
                <button
                  className="btn-export-1 btn-orange btn-border"
                  onClick={(e) => handleClick(e)}
                >
                  <IconDownloadSvg />
                </button>
                <Menu
                  anchorEl={anchorEl}
                  keepMounted
                  open={Boolean(anchorEl)}
                  onClose={handleClose}
                >
                  <MenuItem onClick={handleExportExcel}>
                    Download as Excel
                  </MenuItem>
                </Menu>
              </>
            )}
            {isExportButtonRequired && !downloadFromAPIFile && (
              <Tooltip
                title="Download"
                placement="top"
                arrow
                className="export-csv"
              >
                <GridToolbarExport
                  className="btn-orange btn-sm btn-border btn-export"
                  // csvOptions={{
                  //   fields: dataColumns
                  //     .filter(
                  //       (col) =>
                  //         col.field !== "action" &&
                  //         col.field !== "__detail_panel_toggle__"
                  //     )
                  //     .map((col) => col.field),
                  // }}
                  excelOptions={{
                    fields: dataColumns
                      .filter((col) => col.field !== "action")
                      .map((col) => col.field),
                  }}
                  printOptions={{ disableToolbarButton: true }}
                  csvOptions={{ disableToolbarButton: true }}
                />
              </Tooltip>
            )}
          </Box>
        </Stack>
      </GridToolbarContainer>
    );
  }

  return (
    <FlexBetween height="100%" width="100%" mt="20px">
      <div style={{ width: "100%" }}>
        <DataGridPremium
          getRowHeight={() => "auto"}
          className={className}
          sx={{
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#146FD0",
              fontWeight: 700,
            },
            minHeight: "100px",
            // disable sort arrow in header
            "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer": {},
            "& .MuiDataGrid-sortIcon": {},
            "& .MuiDataGrid-virtualScroller": {
              maxHeight: singlePageMaxHeightDiff
                ? `calc(100vh - ${singlePageMaxHeightDiff}px)`
                : `calc(100vh - 230px)`,
              minHeight: singlePageMaxHeightDiff ? "100px" : "230px",
              overflowY: "auto !important",
              overflowX: "auto",
            },
            "& .MuiDataGrid-overlayWrapperInner": {
              height: "calc(100vh - 230px) !important",
            },
            "& .MuiDataGrid-row .MuiIconButton-sizeSmall": {
              transition: "transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms",
            },
            "& .MuiDataGrid-row.MuiDataGrid-row--detailPanelExpanded .MuiIconButton-sizeSmall":
              {
                transform: "rotateZ(180deg)",
              },
          }}
          componentsProps={{
            pagination: {
              ActionsComponent: CustomPaginationActions,
            },
          }}
          rows={dataRows || []}
          columns={dataColumns}
          paginationMode="server"
          checkboxSelection={checkboxSelection}
          loading={loading}
          pagination={true}
          getRowId={(row: GridRowModel) => row?.id}
          //autoHeight={true}
          disableColumnFilter={disableColumnFilter}
          slots={{
            toolbar: CustomToolbar,
          }}
          getRowClassName={(params: any) => {
            return params?.row?.is_derived ? "derived-column" : "d-none";
          }}
          getDetailPanelHeight={(params: any) => {
            const isCustomValidation =
              params?.row?.custom_validations?.expressions[0]?.length > 0;
            return params?.row?.is_derived || isCustomValidation
              ? "auto"
              : "auto";
          }}
          getDetailPanelContent={(params: any) => {
            const { additional_properties } = params?.row || {};
            return (
              <Grid
                container
                rowSpacing={1.5}
                columnSpacing={2.5}
                className="rule-execution"
                alignItems={"stretch"}
                sx={{ marginBottom: 2 }}
              >
                {historyType === "Execution" ? (
                  <>
                    <Grid item xs={12} md={7}>
                      <Box className="text-box-card">
                        {/* <h4 className="h4">Rules Executed</h4> */}
                        <h4 className="h4">Summary Results</h4>
                        {additional_properties?.comparison_rule_exec_details
                          ?.length > 0 ? (
                          <>
                            <table
                              className="resource-table"
                              style={{ marginBottom: "30px" }}
                            >
                              <thead>
                                <tr>
                                  <td className="first-col">
                                    <h4 className="mb-0">Rule Name</h4>
                                  </td>
                                  <td width="150">
                                    <h4 className="mb-0">Total Records</h4>
                                  </td>
                                  <td width="170">
                                    <h4 className="mb-0">Mismatched Records</h4>
                                  </td>
                                </tr>
                              </thead>
                              <tbody>
                                {additional_properties?.comparison_rule_exec_details?.map(
                                  (item: any, idx: any) => {
                                    return (
                                      <tr key={idx}>
                                        <td className="first-col">
                                          <p className="p">
                                            {item?.check_name || 0}
                                          </p>
                                        </td>
                                        <td valign="top">
                                          <p className="p">
                                            {item?.total_records || 0}
                                          </p>
                                        </td>
                                        <td valign="top">
                                          <p className="p">
                                            {item?.mismatched_records || 0}
                                          </p>
                                        </td>
                                      </tr>
                                    );
                                  }
                                )}
                              </tbody>
                            </table>
                          </>
                        ) : null}
                        <table
                          className="resource-table"
                          style={{ marginBottom: "30px" }}
                        >
                          <tbody>
                            <tr>
                              <td>
                                <h4 className="mb-0">Total Merged Records</h4>
                              </td>
                              <td>
                                <h4 className="mb-0">Total Common Records</h4>
                              </td>
                              <td>
                                <h4 className="mb-0">
                                  Total Mismatched Records
                                </h4>
                              </td>
                              <td>
                                <h4 className="mb-0">
                                  Total Mismatched Records For All Columns
                                </h4>
                              </td>
                              <td>
                                <h4 className="mb-0">
                                  Total Mismatched Records Store In DB
                                </h4>
                              </td>

                              {isShowInComparisonHistory &&
                              additional_properties?.total_false_positive_missing_records ? (
                                <td>
                                  <h4 className="mb-0">
                                    Total False Positive Missing Records
                                  </h4>
                                </td>
                              ) : (
                                ""
                              )}
                            </tr>
                            <tr>
                              <td>
                                <p className="p">
                                  {additional_properties?.total_records || 0}
                                </p>
                              </td>
                              <td>
                                <p className="p">
                                  {additional_properties?.total_common_records ||
                                    0}
                                </p>
                              </td>
                              <td>
                                <p className="p">
                                  {additional_properties?.total_mismatched_records ||
                                    0}
                                </p>
                              </td>
                              <td>
                                <p className="p">
                                  {additional_properties?.total_mismatched_records_for_all_columns ||
                                    0}
                                </p>
                              </td>
                              <td>
                                <p className="p">
                                  {additional_properties?.total_mismatched_records_stored_in_db ||
                                    0}
                                </p>
                              </td>

                              {isShowInComparisonHistory &&
                              additional_properties?.total_false_positive_missing_records ? (
                                <td>
                                  <p className="p">
                                    {additional_properties?.total_false_positive_missing_records ||
                                      0}
                                  </p>
                                </td>
                              ) : (
                                ""
                              )}
                            </tr>
                          </tbody>
                        </table>
                        <table className="resource-table">
                          <tbody>
                            <tr>
                              <td>
                                <h4 className="mb-0">Total Missing Records</h4>
                              </td>
                              <td>
                                <h4 className="mb-0">
                                  Total Unique Missing Records
                                </h4>
                              </td>
                              <td>
                                <h4 className="mb-0">
                                  Total Missing Records Store In DB
                                </h4>
                              </td>
                              {isShowInComparisonHistory &&
                              additional_properties?.total_false_positive_missing_records ? (
                                <td>
                                  <h4 className="mb-0">
                                    Total False Positive Missing Records
                                  </h4>
                                </td>
                              ) : (
                                ""
                              )}
                            </tr>
                            <tr>
                              <td>
                                <p className="p">
                                  {additional_properties?.total_missing_records ||
                                    0}
                                </p>
                              </td>
                              <td>
                                <p className="p">
                                  {additional_properties?.total_unique_missing_records ||
                                    0}
                                </p>
                              </td>
                              <td>
                                <p className="p">
                                  {additional_properties?.total_missing_records_stored_in_db ||
                                    0}
                                </p>
                              </td>
                              {isShowInComparisonHistory &&
                              additional_properties?.total_false_positive_missing_records ? (
                                <td>
                                  <p className="p">
                                    {additional_properties?.total_false_positive_missing_records ||
                                      0}
                                  </p>
                                </td>
                              ) : (
                                ""
                              )}
                            </tr>
                          </tbody>
                        </table>
                      </Box>
                    </Grid>
                    <Grid item xs={9} md={5}>
                      <Box className="text-box-card" sx={{ marginBottom: 2 }}>
                        {/* <h4 className="h4">Validation Results</h4> */}
                        <h4 className="h4">Detailed Results</h4>
                        <table className="resource-table">
                          <tbody>
                            <tr>
                              <td className="first-col">
                                <h4 className="mb-0">Resource Name</h4>
                              </td>
                              <td>
                                <h4 className="mb-0">Resource Statistics</h4>
                              </td>
                            </tr>
                            {additional_properties?.validation_exec_details
                              ?.length > 0 &&
                              additional_properties?.validation_exec_details?.map(
                                (item: any, idx: any) => {
                                  return (
                                    <React.Fragment key={idx}>
                                      <tr>
                                        <td className="first-col" valign="top">
                                          <h5>{item?.resource_name}</h5>
                                        </td>
                                        <td valign="top">
                                          <Accordion
                                            sx={{
                                              boxShadow: "none",
                                              backgroundColor: "transparent",
                                              border: "none",
                                              "& .MuiAccordionSummary-root": {
                                                padding: "0px",
                                              },
                                              "& .MuiAccordionSummary-content":
                                                {
                                                  margin: "0px",
                                                },
                                              "& .MuiAccordionSummary-expandIcon":
                                                {
                                                  padding: "0px",
                                                },
                                              "& .MuiAccordionDetails-root": {
                                                padding: "0px",
                                              },
                                              "& .MuiAccordionSummary-root.Mui-expanded":
                                                {
                                                  minHeight: "0px",
                                                },
                                            }}
                                          >
                                            <AccordionSummary
                                              expandIcon={<ExpandMoreIcon />}
                                              aria-controls="panel1a-content"
                                              id="panel1a-header"
                                            >
                                              <Typography
                                                className="p"
                                                sx={{
                                                  display: "flex",
                                                  justifyContent:
                                                    "space-between",
                                                  width: "100%",
                                                  paddingRight: "16px",
                                                  alignItems: "center",
                                                }}
                                              >
                                                <span>
                                                  Total Invalid Records
                                                </span>
                                                <span
                                                  style={{
                                                    minWidth: "45px",
                                                    textAlign: "right",
                                                  }}
                                                >
                                                  {item?.validation_result
                                                    ?.additional_properties
                                                    ?.total_invalid_records ||
                                                    0}
                                                </span>
                                              </Typography>
                                            </AccordionSummary>
                                            <AccordionDetails className="list-item-group">
                                              <div className="list-item">
                                                <span>
                                                  Total Validation Errors
                                                </span>
                                                <span className="record-count">
                                                  {item?.validation_result
                                                    ?.additional_properties
                                                    ?.total_validation_errors ||
                                                    0}
                                                </span>
                                              </div>
                                              <div className="list-item">
                                                <span>Total Records Count</span>
                                                <span className="record-count">
                                                  {item?.total_records_count ||
                                                    0}
                                                </span>
                                              </div>
                                              <div className="list-item">
                                                <span>Total Columns Count</span>
                                                <span className="record-count">
                                                  {item?.total_columns_count ||
                                                    0}
                                                </span>
                                              </div>
                                              <div className="list-item">
                                                <span>
                                                  Filtered Records By Resource
                                                  Filters
                                                </span>
                                                <span className="record-count">
                                                  {item?.filtered_records_count ||
                                                    item?.validation_result
                                                      ?.additional_properties
                                                      ?.filtered_records_by_resource_filters ||
                                                    0}
                                                </span>
                                              </div>
                                              <div className="list-item">
                                                <span>
                                                  Filtered Records By Null
                                                  Values In Unique Keys
                                                </span>
                                                <span className="record-count">
                                                  {item?.validation_result
                                                    ?.additional_properties
                                                    ?.filtered_null_unique_key_records_count ||
                                                    item?.validation_result
                                                      ?.additional_properties
                                                      ?.filtered_records_by_null_values_in_unique_keys ||
                                                    0}
                                                </span>
                                              </div>
                                              <div className="list-item">
                                                <span>
                                                  Total Duplicate Records
                                                </span>
                                                <span className="record-count">
                                                  {item?.validation_result
                                                    ?.additional_properties
                                                    ?.total_duplicate_records ||
                                                    0}
                                                </span>
                                              </div>
                                              <div className="list-item">
                                                <span>Unique Records</span>
                                                <span className="record-count">
                                                  {item?.unique_records || 0}
                                                </span>
                                              </div>

                                              <div className="list-item">
                                                <span>
                                                  {" "}
                                                  Filtered Records By Rule
                                                  Filters
                                                </span>
                                                <span className="record-count">
                                                  {item?.filtered_records_by_rule_filters ||
                                                    0}
                                                </span>
                                              </div>

                                              <div className="list-item">
                                                <span>
                                                  Missing Records Count
                                                </span>
                                                <span className="record-count">
                                                  {item?.missing_records_count ||
                                                    0}
                                                </span>
                                              </div>
                                              <div className="list-item">
                                                <span>
                                                  False Positive Missing Records
                                                  Count
                                                </span>
                                                <span className="record-count">
                                                  {item?.false_positive_missing_records_count ||
                                                    0}
                                                </span>
                                              </div>
                                            </AccordionDetails>
                                          </Accordion>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td height="6" colSpan={2}></td>
                                      </tr>
                                    </React.Fragment>
                                  );
                                }
                              )}
                            {(additional_properties?.validation_exec_details
                              ?.length === 0 ||
                              additional_properties?.validation_exec_details ===
                                null) && (
                              <tr>
                                <td style={{ textAlign: "left" }} colSpan={2}>
                                  <p className="p no-italic">
                                    No results are available for validation
                                    process.
                                  </p>
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </Box>
                    </Grid>
                  </>
                ) : (
                  <>
                    <Grid
                      item
                      xs={12}
                      sm={12}
                      md={
                        additional_properties?.validation_errors_summary
                          ?.length > 0
                          ? 7
                          : 12
                      }
                      sx={{ paddingBottom: "16px" }}
                    >
                      <Box className="text-box-card">
                        <h4 className="h4">Resource Statistics</h4>
                        <Grid container columnSpacing={3} rowSpacing={1}>
                          <Grid
                            sx={{ fontSize: "14px" }}
                            item
                            xs={12}
                            sm={12}
                            md={6}
                            lg={4}
                          >
                            <span>
                              Total Records :{" "}
                              {additional_properties?.total_records}
                            </span>
                          </Grid>
                          <Grid
                            sx={{ fontSize: "14px" }}
                            item
                            xs={12}
                            sm={12}
                            md={6}
                            lg={4}
                          >
                            <span>
                              Unique Records :{" "}
                              {additional_properties?.unique_records}
                            </span>
                          </Grid>
                          <Grid
                            sx={{ fontSize: "14px" }}
                            item
                            xs={12}
                            sm={12}
                            md={6}
                            lg={4}
                          >
                            <span>
                              Total Duplicate Records :{" "}
                              {additional_properties?.total_duplicate_records}
                            </span>
                          </Grid>
                          <Grid
                            sx={{ fontSize: "14px" }}
                            item
                            xs={12}
                            sm={12}
                            md={6}
                            lg={4}
                          >
                            <span>
                              Total Invalid Records :{" "}
                              {additional_properties?.total_invalid_records}
                            </span>
                          </Grid>

                          <Grid
                            sx={{ fontSize: "14px" }}
                            item
                            xs={12}
                            sm={12}
                            md={6}
                            lg={4}
                          >
                            <span>
                              Total Validation Errors :{" "}
                              {additional_properties?.total_validation_errors}
                            </span>
                          </Grid>
                          <Grid
                            sx={{ fontSize: "14px" }}
                            item
                            xs={12}
                            sm={12}
                            md={6}
                            lg={4}
                          >
                            <span>
                              Total Time : {additional_properties?.total_time}{" "}
                              seconds
                            </span>
                          </Grid>
                          <Grid
                            sx={{ fontSize: "14px" }}
                            item
                            xs={12}
                            sm={12}
                            md={6}
                            lg={4}
                          >
                            <span>
                              Filtered Records :{" "}
                              {additional_properties?.filtered_records_by_resource_filters ||
                                0}
                            </span>
                          </Grid>
                          <Grid
                            sx={{ fontSize: "14px" }}
                            item
                            xs={12}
                            sm={12}
                            md={6}
                            lg={4}
                          >
                            <span>
                              Filtered Records By Null Values :{" "}
                              {additional_properties?.filtered_records_by_null_values_in_unique_keys ||
                                0}
                            </span>
                          </Grid>
                        </Grid>
                      </Box>
                    </Grid>
                    {additional_properties?.validation_errors_summary?.length >
                      0 && (
                      <Grid
                        item
                        xs={12}
                        sm={12}
                        md={5}
                        sx={{ paddingBottom: "16px" }}
                      >
                        <Box className="text-box-card">
                          <h4 className="h4">Column Statistics</h4>
                          <Grid
                            container
                            columnSpacing={3}
                            rowSpacing={1}
                            className="column_statistics"
                          >
                            {additional_properties?.validation_errors_summary
                              ?.length > 0 &&
                              additional_properties?.validation_errors_summary.map(
                                (item: any, index: number) => {
                                  return (
                                    <React.Fragment key={index}>
                                      <Grid
                                        sx={{ fontSize: "14px" }}
                                        item
                                        xs={12}
                                        sm={12}
                                        md={12}
                                        lg={12}
                                        xl={6}
                                      >
                                        <span>
                                          Column Name : {item?.column_name}
                                        </span>
                                      </Grid>
                                      <Grid
                                        sx={{ fontSize: "14px" }}
                                        item
                                        xs={12}
                                        sm={12}
                                        md={12}
                                        lg={12}
                                        xl={6}
                                      >
                                        <span>
                                          Total Validation Errors :{" "}
                                          {item?.total_validation_errors}
                                        </span>
                                      </Grid>
                                      {index <
                                        additional_properties
                                          ?.validation_errors_summary?.length -
                                          1 && (
                                        <Grid
                                          sx={{ fontSize: "14px" }}
                                          item
                                          xs={12}
                                          sm={12}
                                          md={12}
                                          lg={12}
                                        >
                                          <div className="seprator"></div>
                                        </Grid>
                                      )}
                                    </React.Fragment>
                                  );
                                }
                              )}
                          </Grid>
                        </Box>
                      </Grid>
                    )}
                  </>
                )}
              </Grid>
            );
          }}
          //for server side pagination
          rowCount={rowCount}
          pageSizeOptions={pageSizeOptions}
          paginationModel={paginationModel}
          onPaginationModelChange={onPaginationModelChange}
          {...rowGroupByColumnProps}
          localeText={{ noRowsLabel: "Data not available" }}
        />
      </div>
    </FlexBetween>
  );
};

export default EHistoryDataTable;
