import React, { useState } from "react";
import {
  DataGridPremium,
  GridColDef,
  GridRowModel,
  GridToolbarContainer,
  GridToolbarExport,
} from "@mui/x-data-grid-premium";
import Grid from "@mui/material/Grid";
import { Box, Tooltip, Button, Stack, IconButton } from "@mui/material";
import FlexBetween from "../FlexBetween";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import Typography from "@mui/material/Typography";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CustomPaginationActions from "../Molecules/Paginations/CustomPaginationActions";
import { IconDownloadSvg } from "../../common/utils/icons";
import ValidationErrorDataGrid from "./ValidationErrorDataGrid";
import { disableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";
import { getFormattedKeysPairs } from "../../services/utils";

interface DataTableProps {
  dataRows: any[];
  dataColumns: GridColDef[];
  loading?: boolean;
  checkboxSelection?: boolean;
  className?: string;
  buttonText?: string;
  buttonClass?: string;
  buttonClick?: () => void;
  buttonIcon?: React.ReactNode;
  dataListTitle?: string;
  isExportButtonRequired?: boolean;
  rowCount?: number;
  pageSizeOptions?: number[];
  paginationModel?: any;
  onPaginationModelChange?: (params: any) => void;
  historyType: string;
  downloadFromAPIFile?: (params: any) => void;
  setLoading?: (params: any) => any;
  disableColumnFilter?: boolean;
  rowGroupByColumnProps?: any;
  validationErrorProps?: any;
}

const ValidationSummaryDataGrid: React.FC<DataTableProps> = ({
  dataRows,
  dataColumns,
  checkboxSelection = false,
  loading,
  className,
  buttonText,
  buttonClass,
  buttonClick,
  buttonIcon,
  dataListTitle,
  isExportButtonRequired,
  rowCount,
  pageSizeOptions,
  paginationModel,
  onPaginationModelChange,
  historyType,
  downloadFromAPIFile,
  setLoading,
  disableColumnFilter,
  rowGroupByColumnProps = { ...disableRowGroupByColumnProps },
  validationErrorProps: {
    dataRows: validationDataRows,
    dataColumns: ValidationDataColumns,
    loading: ValidationIsLoading,
    className: ValidationClassName,
    gridColumnLength: ValidationGridColumnLength,
    dataListTitle: ValidationDataListTitle,
    isHeight100: ValidationIsHeight100,
    pinnedColumns,
    handleMoveColumns,
    isAllColumnPinned,
    disableColumnPinning,
    validationCheckboxSelection,
    selectedIds,
    setSelectedIds,
    validationRowCount,
    validationPageSizeOptions,
    validationPaginationModel,
    validationOnPaginationModelChange,
    validationPaginationMode = "client",
  },
}) => {
  const [expandedRowId, setExpandedRowId] = useState<any>(null);

  function CustomToolbar() {
    return (
      <GridToolbarContainer className="custom-toolbar">
        <Stack
          spacing={1}
          direction="row"
          alignItems="center"
          justifyContent={dataListTitle ? "space-between" : "flex-end"}
          sx={{ width: "100%", marginRight: 3, marginLeft: 3 }}
        >
          {dataListTitle && <h2 className="list-title">{dataListTitle}</h2>}
          <Box sx={{ display: "flex", columnGap: "10px" }}>
            {buttonClick && (
              <Button
                color="secondary"
                variant="contained"
                onClick={buttonClick}
                className={buttonClass}
              >
                <span
                  style={{
                    marginRight: "4px",
                    position: "relative",
                    top: "2px",
                  }}
                >
                  {buttonIcon}
                </span>{" "}
                {buttonText}
              </Button>
            )}
            {downloadFromAPIFile && (
              <Tooltip title="Download Excel" placement="top">
                <button
                  className="btn-export-1 btn-orange btn-border"
                  onClick={() => downloadFromAPIFile(setLoading)}
                >
                  <IconDownloadSvg />
                </button>
              </Tooltip>
            )}
            {isExportButtonRequired && !downloadFromAPIFile && (
              // <Tooltip
              //   title="Download"
              //   placement="top"
              //   arrow
              //   className="export-csv"
              // >
              <GridToolbarExport
                className="btn-orange btn-sm btn-border btn-export"
                // csvOptions={{
                //   fields: dataColumns
                //     .filter(
                //       (col) =>
                //         col.field !== "action" &&
                //         col.field !== "__detail_panel_toggle__"
                //     )
                //     .map((col) => col.field),
                // }}
                excelOptions={{
                  fields: dataColumns
                    .filter(
                      (col) =>
                        col.field !== "action" &&
                        col.field !== "__detail_panel_toggle__"
                    )
                    .map((col) => col.field),
                }}
                printOptions={{ disableToolbarButton: true }}
                csvOptions={{ disableToolbarButton: true }}
              />
              // </Tooltip>
            )}
          </Box>
        </Stack>
      </GridToolbarContainer>
    );
  }

  return (
    <FlexBetween height="100%" width="100%">
      <div style={{ width: "100%" }}>
        <DataGridPremium
          getRowHeight={() => "auto"}
          className={className}
          sx={{
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#146FD0",
              fontWeight: 700,
            },
            minHeight: "100px",
            // disable sort arrow in header
            "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer": {},
            "& .MuiDataGrid-sortIcon": {},
            "& .MuiDataGrid-virtualScroller": {
              maxHeight: "calc(100vh - 230px)",
              //minHeight: "230px",
              overflowY: "auto !important",
              //overflowX: "hidden",
            },
            "& .MuiDataGrid-row .MuiIconButton-sizeSmall": {
              transition: "transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms",
            },
            "& .MuiDataGrid-row.MuiDataGrid-row--detailPanelExpanded .MuiIconButton-sizeSmall":
              {
                transform: "rotateZ(180deg)",
              },
          }}
          componentsProps={{
            pagination: {
              ActionsComponent: CustomPaginationActions,
            },
          }}
          rows={dataRows || []}
          columns={dataColumns}
          paginationMode="server"
          checkboxSelection={checkboxSelection}
          loading={loading}
          pagination={true}
          getRowId={(row: GridRowModel) => row?.id}
          autoHeight={true}
          disableColumnFilter={disableColumnFilter}
          slots={{
            toolbar: CustomToolbar,
          }}
          getRowClassName={(params: any) => {
            return params?.row?.is_derived ? "derived-column" : "d-none";
          }}
          getDetailPanelHeight={(params: any) => {
            const isCustomValidation =
              params?.row?.custom_validations?.expressions[0]?.length > 0;
            return params?.row?.is_derived || isCustomValidation
              ? "auto"
              : "auto";
          }}
          detailPanelExpandedRowIds={
            expandedRowId !== null ? [expandedRowId] : []
          }
          onDetailPanelExpandedRowIdsChange={(ids) => {
            setExpandedRowId(ids.length > 0 ? ids[ids.length - 1] : null);
          }}
          getDetailPanelContent={(params: any) => {
            const { general_validation_errors } = params?.row || {};
            const { custom_validation_errros } = params?.row || {};
            const validationRows: any = [];
            params?.row?.validation_errors?.forEach((item: any, idx: any) => {
              const { key_columns, ...rest } = item ?? {};
              const row: any = {
                id: idx,
                ...getFormattedKeysPairs(item?.key_columns),
                ...rest,
              };

              validationRows.push(row);
            });
            return (
              <>
                <Box className="rule-execution rule-execution-history italic-none">
                  <div className="accordion-panel alternative">
                    <Grid container columnSpacing={{ xl: 4, lg: 3, md: 2 }}>
                      <Grid
                        item
                        xs={12}
                        sm={12}
                        md={12}
                        sx={{ paddingBottom: "6px" }}
                      >
                        <Accordion className="heading-bold">
                          <AccordionSummary
                            aria-controls="panel2d-content"
                            id="panel2d-header"
                            expandIcon={<ExpandMoreIcon />}
                          >
                            <h4 className="acc-h4">Validation Errors</h4>
                          </AccordionSummary>
                          <AccordionDetails sx={{ paddingTop: "16px" }}>
                            <Box className="position-relative">
                              <IconButton
                                onClick={handleMoveColumns}
                                className={`icon-pin-datagrid navbar-icon burger  ${
                                  isAllColumnPinned ? "active" : ""
                                }`}
                              >
                                <div className="bar bar1"></div>
                                <div className="bar bar2"></div>
                                <div className="bar bar3"></div>
                              </IconButton>

                              <ValidationErrorDataGrid
                                dataRows={validationDataRows}
                                dataColumns={ValidationDataColumns}
                                loading={ValidationIsLoading}
                                className={ValidationClassName}
                                dataListTitle={ValidationDataListTitle}
                                gridColumnLength={ValidationGridColumnLength}
                                isHeight100={ValidationIsHeight100}
                                pinnedColumns={
                                  validationDataRows?.length > 0
                                    ? pinnedColumns
                                    : {}
                                }
                                disableColumnPinning={disableColumnPinning}
                                validationCheckboxSelection={
                                  validationCheckboxSelection
                                }
                                selectedIds={selectedIds}
                                setSelectedIds={setSelectedIds}
                                paginationMode="server"
                                rowCount={validationRowCount}
                                pageSizeOptions={
                                  validationPageSizeOptions || pageSizeOptions
                                }
                                paginationModel={validationPaginationModel}
                                onPaginationModelChange={
                                  validationOnPaginationModelChange
                                }
                              />
                            </Box>
                          </AccordionDetails>
                        </Accordion>
                      </Grid>
                      {general_validation_errors && (
                        <Grid
                          item
                          xs={12}
                          sm={12}
                          md={12}
                          sx={{ paddingBottom: "6px" }}
                        >
                          <Accordion className="heading-bold">
                            <AccordionSummary
                              aria-controls="panel2d-content"
                              id="panel2d-header"
                              expandIcon={<ExpandMoreIcon />}
                            >
                              <h4 className="acc-h4">
                                General Validation Errors
                              </h4>
                            </AccordionSummary>
                            <AccordionDetails sx={{ paddingTop: "16px" }}>
                              <Grid
                                container
                                rowSpacing={{ xl: 3, lg: 3, md: 2 }}
                              >
                                {general_validation_errors &&
                                  Object.keys(general_validation_errors).map(
                                    (item: any, index: any) => {
                                      return (
                                        <Grid
                                          sx={{ fontSize: "14px" }}
                                          item
                                          xs={12}
                                          sm={12}
                                          md={12}
                                          lg={12}
                                          className="no-italic"
                                          key={index}
                                        >
                                          <span>
                                            <strong>{item}:</strong>{" "}
                                            {general_validation_errors[item]}
                                          </span>
                                        </Grid>
                                      );
                                    }
                                  )}
                              </Grid>
                            </AccordionDetails>
                          </Accordion>
                        </Grid>
                      )}
                      {custom_validation_errros && (
                        <Grid
                          item
                          xs={12}
                          sm={12}
                          md={12}
                          sx={{ paddingBottom: "16px" }}
                        >
                          <Accordion className="heading-bold">
                            <AccordionSummary
                              aria-controls="panel3d-content"
                              id="panel3d-header"
                              expandIcon={<ExpandMoreIcon />}
                            >
                              <h4 className="acc-h4">
                                Custom Validation Errors
                              </h4>
                            </AccordionSummary>
                            <AccordionDetails sx={{ paddingTop: "16px" }}>
                              <Grid
                                container
                                rowSpacing={{ xl: 3, lg: 3, md: 2 }}
                              >
                                {custom_validation_errros &&
                                  custom_validation_errros !== null &&
                                  custom_validation_errros.map((item: any) => {
                                    return (
                                      <Grid
                                        sx={{ fontSize: "14px" }}
                                        item
                                        xs={12}
                                        sm={12}
                                        md={12}
                                        lg={12}
                                        className="no-italic"
                                      >
                                        <strong>Custom Validation Name:</strong>
                                        {item.custom_validation_name}
                                        <Box
                                          sx={{ marginBottom: "12px" }}
                                        ></Box>
                                        <strong>
                                          Total Validation Errors:
                                        </strong>
                                        {item.total_validation_errors}
                                      </Grid>
                                    );
                                  })}
                              </Grid>
                            </AccordionDetails>
                          </Accordion>
                        </Grid>
                      )}
                    </Grid>
                  </div>
                </Box>
              </>
            );
          }}
          //for server side pagination
          rowCount={rowCount || 0}
          pageSizeOptions={pageSizeOptions}
          paginationModel={paginationModel}
          onPaginationModelChange={onPaginationModelChange}
          {...rowGroupByColumnProps}
          localeText={{ noRowsLabel: "Data not available" }}
        />
      </div>
    </FlexBetween>
  );
};

export default ValidationSummaryDataGrid;
