import React, { useEffect, useState, useRef } from "react";
import {
  DataGridPremium,
  GridColDef,
  GridFeatureMode,
  GridPagination,
  GridRowModel,
  GridToolbarFilterButton,
} from "@mui/x-data-grid-premium";
import FlexBetween from "../FlexBetween";
import CustomNoRowsOverlay from "./CustomNoRowsOverlay";
import { GridApiPremium } from "@mui/x-data-grid-premium/models/gridApiPremium";
import { Box, Button } from "@mui/material";

interface DataTableProps {
  dataRows: any[];
  dataColumns: GridColDef[];
  loading?: boolean;
  checkboxSelection?: boolean;
  className?: string;
  buttonText?: string;
  buttonClass?: string;
  buttonClick?: () => void;
  buttonIcon?: React.ReactNode;
  dataListTitle?: string;
  disableColumnFilter?: boolean;
  rowCount?: number;
  pageSizeOptions?: number[];
  paginationModel?: any;
  onPaginationModelChange?: (params: any) => void;
  paginationMode?: GridFeatureMode;
  tableHeight?: number;
  singlePageMaxHeightDiff?: number;
  disableColumnReorder?: boolean;
  columnGroupingModel?: any;
  setResultColumns?: any;
  pinnedColumns?: any;
  disableColumnPinning?: any;
  selectedIds?: any;
  setSelectedIds?: any;
}

const RulesListDataTable: React.FC<DataTableProps> = ({
  dataRows,
  dataColumns,
  checkboxSelection = false,
  loading,
  className,
  buttonText,
  buttonClass,
  buttonClick,
  buttonIcon,
  dataListTitle,
  disableColumnFilter,
  rowCount,
  pageSizeOptions,
  paginationModel,
  onPaginationModelChange,
  paginationMode = "client",
  tableHeight,
  singlePageMaxHeightDiff,
  disableColumnReorder,
  columnGroupingModel,
  setResultColumns,
  pinnedColumns,
  disableColumnPinning,
  selectedIds,
  setSelectedIds,
}) => {
  const dataGridProps = {
    // ... other props
    paginationMode,
    pagination: paginationMode !== "client",
    ...(paginationMode !== "client"
      ? {
          rowCount,
          pageSizeOptions,
          paginationModel,
          onPaginationModelChange,
        }
      : {}),
  };
  const apiRef = useRef<GridApiPremium>(null!);

  React.useEffect(() => {
    if (!setResultColumns) return;
    if (selectedIds) {
      const selectedRows = dataRows.filter((row) => {
        return selectedIds.includes(row.rowId);
      });
      setResultColumns((prev: any) => {
        const updatedColumns =
          selectedIds && selectedIds.length === 0 ? [] : [...prev];
        selectedRows.forEach((row) => {
          const formattedRow = Object.keys(row).reduce((acc: any, key: any) => {
            if (key !== "message" || key !== "Quantity") {
              const newKey = key.replace(/'/g, "");
              acc[newKey] = row[key];
            }
            return acc;
          }, {});
          if (
            !updatedColumns.some((item) => item.rowId === formattedRow.rowId)
          ) {
            updatedColumns.push(formattedRow);
          }
        });
        return updatedColumns;
      });
    }
  }, [selectedIds]);

  const [maxHeight, setMaxHeight] = useState<any>(
    window.innerHeight - (singlePageMaxHeightDiff ?? 0)
  );
  React.useEffect(() => {
    const handleResize = () => {
      setMaxHeight(window.innerHeight - (singlePageMaxHeightDiff ?? 0));
    };
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  useEffect(() => {
    if (apiRef.current) {
      setTimeout(() => {
        apiRef?.current?.resetRowHeights && apiRef?.current?.resetRowHeights();
      }, 300);
    }
  }, [dataRows]);
  const previousSelectedIds = useRef(selectedIds);
  useEffect(() => {
    // Before data reload, persist the selectedIds
    previousSelectedIds.current = selectedIds;
  }, [selectedIds]);
  useEffect(() => {
    if (dataRows && dataRows.length && setSelectedIds) {
      setSelectedIds(previousSelectedIds.current); // Restore selectedIds after data reload
    }
  }, [dataRows]);
  const CustomFooter = ({ selectedIds }: any) => (
    <div className="datatable-footer">
      <strong>Selected IDs:</strong> [{selectedIds.join(", ")}]
      <Button
        className="btn-orange btn-dark btn-sm plus-btn-sm"
        onClick={() => setSelectedIds([])}
      >
        Reset
      </Button>
    </div>
  );

  return (
    <FlexBetween height="100%" width="100%">
      <div
        style={{
          height: pinnedColumns
            ? `${
                dataRows.length < 5
                  ? dataRows.length * 60 + 215
                  : dataRows.length < 11
                  ? dataRows.length * 40 + 100
                  : maxHeight + 95
              }px`
            : "auto",
          width: "100%",
        }}
      >
        <DataGridPremium
          apiRef={apiRef}
          getRowHeight={() => "auto"}
          className={`${className} 
          ${columnGroupingModel ? "columnGroupingModel" : ""} 
          ${setResultColumns ? "checkbox-result-column" : ""} `}
          sx={{
            "&.columnGroupingModel": {
              "& .MuiDataGrid-columnHeaders": {
                overflow: "visible",
              },
              "& .MuiDataGrid-row": {
                backgroundColor: "transparent",
                "&:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                },
              },
            },
            "& .MuiDataGrid-main": {
              position: "relative",
              "& .MuiDataGrid-columnHeader--filledGroup": {
                borderBottom: "solid 1px #e0e0e0",
                "& .MuiDataGrid-withBorderColor": {
                  borderColor: "transparent",
                },
                // "& + .MuiDataGrid-columnHeader--filledGroup ": {
                //   "&:before": {
                //     content: '""',
                //     background: "#e0e0e0",
                //     width: "1px",
                //     height: "1000px",
                //     position: "absolute",
                //     top: "0px",
                //     left: "0px",
                //     zIndex: "9999999",
                //   },
                // },
              },
            },
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#146FD0",
              fontWeight: 700,
            },
            "& .MuiDataGrid-overlayWrapperInner": {
              // height: "auto !important", //commented because its resizing the overlay window backdrop widow

              "& .MuiDataGrid-overlay": {
                backgroundColor: "rgba(0, 0, 0, 0.1)",
              },
            },
            "& .MuiDataGrid-footerContainer": {},
            minHeight: "100px",
            // disable sort arrow in header
            "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer": {},
            "& .MuiDataGrid-columnHeader .MuiDataGrid-columnHeaderTitleContainerContent .MuiCheckbox-root":
              { display: "none" },
            "& .MuiDataGrid-menuIcon": {
              display: "block",
            },
            "& .MuiDataGrid-sortIcon": {},
            "& .MuiDataGrid-virtualScroller": {
              minHeight: singlePageMaxHeightDiff
                ? "100px"
                : columnGroupingModel
                ? "inherit"
                : "100px",
              maxHeight: singlePageMaxHeightDiff
                ? `calc(100vh - ${singlePageMaxHeightDiff}px)`
                : tableHeight && dataRows.length < 6
                ? tableHeight
                : `calc(100vh - 230px)`,
              overflowY: "auto !important",
              // overflowX: { md: "auto" },
            },
          }}
          rows={dataRows}
          columns={dataColumns}
          checkboxSelection={checkboxSelection}
          rowSelectionModel={selectedIds}
          onRowSelectionModelChange={(selectionModel) => {
            if (!setResultColumns) return;
            if (selectedIds) {
              const currentSelection = selectionModel;
              if (currentSelection.length > selectedIds.length) {
                setSelectedIds((prevSelectedIds: any) => {
                  const updatedSelectedIds = new Set(prevSelectedIds);
                  selectionModel.forEach((id: any) => {
                    return updatedSelectedIds.add(id);
                  });
                  return Array.from(updatedSelectedIds);
                });
              } else {
                const deselectedId = selectedIds.find(
                  (id: any) => !currentSelection.includes(id)
                );
                setSelectedIds((prev: any) => {
                  return prev.filter((item: any) => item !== deselectedId);
                });
              }
            }
          }}
          loading={loading}
          getRowId={(row: GridRowModel) => (row?.rowId ? row?.rowId : row?.id)}
          //autoHeight={true}
          disableColumnFilter={disableColumnFilter}
          disableColumnSelector={true}
          disableColumnMenu={false}
          getRowClassName={(params: any) => {
            return params?.row?.is_derived ? "derived-column" : "";
          }}
          disableRowGrouping={true}
          disableAggregation={true}
          slots={{
            noRowsOverlay: CustomNoRowsOverlay,
            ...(selectedIds &&
              selectedIds.length > 0 && {
                footer: () => (
                  <>
                    <Box className="d-flex justify-content-between align-items-center">
                      <CustomFooter selectedIds={selectedIds} />
                      <Box className="MuiDataGrid-footerContainer">
                        <GridPagination />
                      </Box>
                    </Box>
                  </>
                ),
              }),
          }}
          //for server side pagination
          {...dataGridProps}
          disableColumnReorder={disableColumnReorder ? true : false}
          columnGroupingModel={columnGroupingModel ? columnGroupingModel : []}
          experimentalFeatures={{
            columnGrouping: columnGroupingModel ? true : false,
          }}
          pinnedColumns={pinnedColumns}
          disableColumnPinning={
            disableColumnPinning ? true : disableColumnPinning
          }
        />
      </div>
    </FlexBetween>
  );
};

export default RulesListDataTable;
