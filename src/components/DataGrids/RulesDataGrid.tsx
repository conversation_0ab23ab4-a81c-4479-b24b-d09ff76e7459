import * as React from "react";
import {
  DataGridPremium,
  GridColDef,
  GridRowModel,
  GridToolbarContainer,
  GridToolbarExport,
} from "@mui/x-data-grid-premium";
import { Box, Button, Grid, Stack, Tooltip } from "@mui/material";
import FlexBetween from "../FlexBetween";
import { IconEditSvg, IconDeleteSvg } from "../../common/utils/icons";
import AddSharpIcon from "@mui/icons-material/AddSharp";

interface DataTableProps {
  dataRows: any;
  dataColumns: GridColDef[];
  loading?: boolean;
  checkboxSelection?: boolean;
  className?: string;
  dataListTitle?: string;
  handleEditToleranceValue?: (rowData: any) => void;
  handleDeleteToleranceValue?: (rowData: any) => void;
  handleAddFilter?: any;
  datagridType?: string;
  getRowHeight?: string;
}

const RulesDataTable: React.FC<DataTableProps> = ({
  dataRows,
  dataColumns,
  checkboxSelection = false,
  loading,
  className,
  dataListTitle,
  handleEditToleranceValue,
  handleDeleteToleranceValue,
  handleAddFilter,
  datagridType,
  getRowHeight,
}) => {
  function CustomToolbar() {
    return (
      <GridToolbarContainer className="custom-toolbar">
        <Stack
          spacing={1}
          direction="row"
          alignItems="center"
          justifyContent={dataListTitle ? "space-between" : "flex-end"}
          sx={{ width: "100%", marginRight: 3, marginLeft: 3 }}
        >
          {dataListTitle && <h2 className="list-title">{dataListTitle}</h2>}
          <Box sx={{ display: "flex", columnGap: "10px" }}>
            {datagridType && datagridType == "ResourceFilter" && (
              <Button
                variant="contained"
                color="secondary"
                onClick={handleAddFilter}
                className="btn-orange btn-dark"
              >
                <AddSharpIcon /> &nbsp; Filter
              </Button>
            )}
            <GridToolbarExport
              className="btn-orange btn-sm btn-border btn-export"
              // csvOptions={{
              //   fields: dataColumns
              //     .filter((col) => col.field !== "action" && col.field !== "srNo" && col.field !== "id")
              //     .map((col) => col.field),
              // }}
              excelOptions={{
                fields: dataColumns
                  .filter(
                    (col) =>
                      col.field !== "action" &&
                      col.field !== "srNo" &&
                      col.field !== "__detail_panel_toggle__" &&
                      col.field !== "id"
                  )
                  .map((col) => col.field),
              }}
              printOptions={{ disableToolbarButton: true }}
              csvOptions={{ disableToolbarButton: true }}
            />
          </Box>
        </Stack>
      </GridToolbarContainer>
    );
  }

  return (
    <FlexBetween height="100%" width="100%">
      <div style={{ width: "100%" }}>
        <DataGridPremium
          className={className}
          sx={{
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#146FD0",
              fontWeight: 700,
            },
            minHeight: "100px",
            // disable sort arrow in header
            "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer": {
              display: "none",
              visibility: "hidden",
            },
            "& .MuiDataGrid-menuIcon": {
              display: "none",
            },
            "& .MuiDataGrid-sortIcon": {
              display: "none",
            },
          }}
          rows={dataRows}
          columns={dataColumns}
          // paginationMode="server"
          checkboxSelection={Boolean(false)}
          loading={loading}
          // onRowClick={(params:any) => handleRowClick(params?.id)}
          paginationMode="client"
          pagination={true}
          getRowId={(row: GridRowModel) => row.id}
          autoHeight={true}
          disableColumnFilter={true}
          slots={{
            toolbar: CustomToolbar,
          }}
          getRowHeight={() => (getRowHeight ? "auto" : null)}
          getDetailPanelHeight={(params: any) => {
            return "auto";
          }}
          getDetailPanelContent={(params: any) => {
            const isTolerance =
              params?.row?.domain_name && params?.row?.tolerance_type;
            return (
              <>
                {isTolerance ? (
                  <>
                    <Box sx={{ p: 2 }}>
                      <strong style={{ fontSize: "14px" }}>
                        Tolerance Data
                      </strong>
                      :
                      <div className="detail-panel">
                        <Grid container columnSpacing={2} rowSpacing={2}>
                          <Grid item>
                            <Box
                              className={`validation-item-box ${
                                (handleEditToleranceValue ||
                                  handleDeleteToleranceValue) &&
                                "btns-pad"
                              }`}
                            >
                              <Box>
                                <strong>Tolerance type: </strong>
                                {params?.row?.tolerance_type}
                              </Box>
                              <Box>
                                <strong>Tolerance value : </strong>
                                {params?.row?.tolerance_value}
                              </Box>
                              {params?.row?.tolerance_type === "percentage" &&
                                typeof params?.row?.fallback_value ===
                                  "number" && (
                                  <Box>
                                    <strong>Fallback value : </strong>
                                    {params?.row?.fallback_value}
                                  </Box>
                                )}
                              {(handleEditToleranceValue ||
                                handleDeleteToleranceValue) && (
                                <>
                                  <Box className={`action-btns gap-20`}>
                                    {handleEditToleranceValue && (
                                      <>
                                        <Box
                                          className="edit-btn"
                                          onClick={() =>
                                            handleEditToleranceValue(
                                              params?.row
                                            )
                                          }
                                        >
                                          <IconEditSvg />
                                        </Box>
                                      </>
                                    )}
                                    {handleDeleteToleranceValue && (
                                      <>
                                        <Box
                                          className="delete-btn"
                                          onClick={() =>
                                            handleDeleteToleranceValue(
                                              params?.row
                                            )
                                          }
                                        >
                                          <IconDeleteSvg />
                                        </Box>
                                      </>
                                    )}
                                  </Box>
                                </>
                              )}
                            </Box>
                          </Grid>
                        </Grid>
                      </div>
                    </Box>
                  </>
                ) : null}
              </>
            );
          }}
        />
      </div>
    </FlexBetween>
  );
};

export default RulesDataTable;
