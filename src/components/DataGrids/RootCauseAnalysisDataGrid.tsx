import React, { useEffect, useState, useCallback } from "react";
import {
  DataGridPremium,
  GridColDef,
  GridFeatureMode,
  GridRowModel,
  GridFilterModel,
  GridOverlay,
} from "@mui/x-data-grid-premium";
import FlexBetween from "../FlexBetween";
import { Box, IconButton, TextField } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import { debounce } from "lodash";
import CustomNoRowsOverlay from "./CustomNoRowsOverlay";

interface DataTableProps {
  dataRows: any[];
  dataColumns: GridColDef[];
  loading?: boolean;
  checkboxSelection?: boolean;
  className?: string;
  disableColumnFilter?: boolean;
  rowCount?: number;
  pageSizeOptions?: number[];
  paginationModel?: any;
  onPaginationModelChange?: (params: any) => void;
  paginationMode?: GridFeatureMode;
  tableHeight?: number;
  isFilterHide: boolean;
  errorMsg?: any;
}

const CustomHeaderFilter = ({
  column,
  filterValue,
  onFilterChange,
  isFilterHide,
}: any) => {
  const [inputValue, setInputValue] = useState(filterValue || "");

  const debouncedFilterChange = useCallback(
    debounce((value: any) => onFilterChange(value), 300),
    []
  );

  useEffect(() => {
    debouncedFilterChange(inputValue);
    return () => {
      debouncedFilterChange.cancel();
    };
  }, [inputValue, debouncedFilterChange]);

  return (
    <Box display="flex" flexDirection="column">
      <Box className="header-name">{column?.headerName}</Box>
      <Box className={`textbox-group-wrapper ${isFilterHide ? "hide" : ""}`}>
        <Box className={`textbox-group`}>
          <TextField
            variant="outlined"
            size="small"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={`Filter ${column?.headerName}`}
          />
          <IconButton>
            <SearchIcon />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
};

const RootCauseAnalysisDataGrid: React.FC<DataTableProps> = ({
  dataRows,
  dataColumns,
  checkboxSelection = false,
  loading,
  className,
  disableColumnFilter,
  rowCount,
  pageSizeOptions = [25],
  paginationModel,
  onPaginationModelChange,
  paginationMode = "client",
  tableHeight,
  isFilterHide,
  errorMsg,
}) => {
  const [filterModel, setFilterModel] = useState<GridFilterModel>({
    items: [],
  });
  const [filteredDataRows, setFilteredDataRows] = useState<any[]>(dataRows);

  useEffect(() => {
    // Filter rows based on the filterModel
    const applyFilters = () => {
      let filteredRows = [...dataRows];

      filterModel.items.forEach((filter) => {
        const { field, value } = filter;
        const lowerCaseValue = value?.toString().toLowerCase() || "";

        filteredRows = filteredRows.filter((row) => {
          const cellValue = row[field]?.toString().toLowerCase() || "";
          return cellValue.includes(lowerCaseValue);
        });
      });

      setFilteredDataRows(filteredRows);
    };

    applyFilters();
  }, [filterModel, dataRows]);

  const handleFilterChange = (field: string, value: string) => {
    const newFilterItems = filterModel.items.filter(
      (item) => item.field !== field
    );

    if (value) {
      newFilterItems.push({
        field: field,
        operator: "contains",
        value,
      });
    }

    setFilterModel({ items: newFilterItems });
  };

  const columnWithFilters = dataColumns.map((column) => ({
    ...column,
    sortable: false,
    renderHeader: (params: any) => (
      <CustomHeaderFilter
        column={params.colDef}
        filterValue={
          filterModel.items.find((item) => item.field === column.field)
            ?.value || ""
        }
        onFilterChange={(value: any) => handleFilterChange(column.field, value)}
        isFilterHide={isFilterHide}
      />
    ),
  }));

  return (
    <FlexBetween height="100%" width="100%">
      <div style={{ width: "100%" }}>
        <DataGridPremium
          getRowHeight={() => "auto"}
          className={`${className}`}
          sx={{
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#146FD0",
              fontWeight: 700,
            },
            minHeight: "100px",
            "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer": {},
            "& .MuiDataGrid-menuIcon": {
              display: "none",
            },
            "& .MuiDataGrid-sortIcon": {},
            "& .MuiDataGrid-virtualScroller": {
              maxHeight: "775px",
              overflowY: "auto !important",
              overflowX: { md: "auto" },
              minHeight: "200px",
            },
          }}
          rows={filteredDataRows}
          columns={columnWithFilters}
          checkboxSelection={checkboxSelection}
          loading={loading}
          getRowId={(row: GridRowModel) => row.id}
          disableColumnFilter={disableColumnFilter}
          disableColumnSelector={true}
          disableColumnMenu={false}
          getRowClassName={(params) =>
            params?.row?.is_derived ? "derived-column" : ""
          }
          disableColumnPinning={true}
          disableRowGrouping={true}
          disableAggregation={true}
          filterModel={filterModel}
          onFilterModelChange={(newFilterModel) =>
            setFilterModel(newFilterModel)
          }
          paginationModel={paginationModel}
          onPaginationModelChange={onPaginationModelChange}
          pageSizeOptions={pageSizeOptions}
          paginationMode={paginationMode}
          pagination
          rowCount={
            paginationMode === "client" ? filteredDataRows.length : rowCount
          }
          slots={{
            noRowsOverlay: errorMsg
              ? () => (
                  <GridOverlay>
                    <div
                      style={{
                        minHeight: "100px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        padding: "0 20px",
                      }}
                    >
                      {errorMsg}
                    </div>
                  </GridOverlay>
                )
              : CustomNoRowsOverlay,
          }}
        />
      </div>
    </FlexBetween>
  );
};
export default RootCauseAnalysisDataGrid;
