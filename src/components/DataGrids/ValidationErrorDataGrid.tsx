import React, { useEffect, useState, useRef } from "react";
import {
  DataGridPremium,
  GridColDef,
  GridPagination,
  GridRowModel,
} from "@mui/x-data-grid-premium";
import { <PERSON>, Button } from "@mui/material";
import FlexBetween from "../FlexBetween";
import { getSanatizedCodeForValidate } from "../../services/utils";
import ReadMoreLess from "../../components/ReadMoreLess";
import { GridApiPremium } from "@mui/x-data-grid-premium/models/gridApiPremium";

interface DataTableProps {
  dataRows: any[];
  dataColumns: GridColDef[];
  loading?: boolean;
  className?: string;
  dataListTitle?: string;
  gridColumnLength?: number;
  isHeight100?: boolean;
  pinnedColumns?: any;
  disableColumnPinning?: any;
  validationCheckboxSelection?: boolean;
  selectedIds?: any;
  setSelectedIds?: any;
  rowCount?: number;
  pageSizeOptions?: number[];
  paginationModel?: any;
  onPaginationModelChange?: (params: any) => void;
  paginationMode?: any;
}
const ValidationErrorDataGrid: React.FC<DataTableProps> = ({
  dataRows,
  dataColumns,
  loading,
  className,
  dataListTitle,
  gridColumnLength,
  isHeight100,
  pinnedColumns,
  disableColumnPinning,
  validationCheckboxSelection,
  selectedIds,
  setSelectedIds,
  rowCount,
  pageSizeOptions,
  paginationModel,
  onPaginationModelChange,
  paginationMode = "client",
}) => {
  const dataGridProps = {
    paginationMode,
    pagination: paginationMode !== "client",
    ...(paginationMode !== "client"
      ? {
          rowCount,
          pageSizeOptions,
          paginationModel,
          onPaginationModelChange,
        }
      : {}),
  };
  const apiRef = useRef<GridApiPremium>(null!);
  const singlePageMaxHeightDiff = 300;
  const [maxHeight, setMaxHeight] = useState<any>(singlePageMaxHeightDiff);
  React.useEffect(() => {
    const handleResize = () => {
      setMaxHeight(singlePageMaxHeightDiff);
    };
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  useEffect(() => {
    if (apiRef.current) {
      setTimeout(() => {
        apiRef?.current?.resetRowHeights && apiRef?.current?.resetRowHeights();
      }, 500);
    }
  }, [dataRows]);

  const previousSelectedIds = useRef(selectedIds);
  useEffect(() => {
    // Before data reload, persist the selectedIds
    previousSelectedIds.current = selectedIds;
  }, [selectedIds]);

  useEffect(() => {
    if (dataRows && dataRows.length && setSelectedIds) {
      setSelectedIds(previousSelectedIds.current); // Restore selectedIds after data reload
    }
  }, [dataRows]);
  const CustomFooter = ({ selectedIds }: any) => (
    <div className="datatable-footer">
      <strong>Selected IDs:</strong> [{selectedIds.join(", ")}]
      <Button
        className="btn-orange btn-dark btn-sm plus-btn-sm"
        onClick={() => setSelectedIds([])}
      >
        Reset
      </Button>
    </div>
  );

  return (
    <FlexBetween height="100%" width="100%" mt={`${isHeight100 ? "" : "20px"}`}>
      <div
        style={{
          height: pinnedColumns
            ? `${
                dataRows.length < 5
                  ? dataRows.length * 65 + 315
                  : dataRows.length < 11
                  ? dataRows.length * 60 + 100
                  : maxHeight + 195
              }px`
            : "auto",
          width: "100%",
        }}
      >
        {dataListTitle && <h4 className="dataListTitle">{dataListTitle}</h4>}
        {dataColumns.length > 0 ? (
          <DataGridPremium
            apiRef={apiRef}
            getRowHeight={() => "auto"}
            className={className}
            sx={{
              "&.MuiDataGrid-root--densityStandard": {
                padding: "0",
              },
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: "#146FD0",
                fontWeight: 700,
                borderTop: "none",
              },
              "& .MuiDataGrid-row:hover ": {
                background: "white !important",
              },
              "& .MuiDataGrid-row:nth-of-type(even) ": {
                background: "white",
              },
              minHeight: "100px",
              // disable sort arrow in header
              "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer":
                {},
              "& .MuiDataGrid-menuIcon": {
                display: "none",
              },
              "& .MuiDataGrid-sortIcon": {},
              "& .MuiDataGrid-virtualScroller": {
                minHeight: singlePageMaxHeightDiff ? "100px" : "100px",
                maxHeight: `calc(100vh - 230px)`,
                overflowY: "auto !important",
              },
              "& .MuiDataGrid-row": {
                //alignItems: "flex-start",
              },
              "& .MuiDataGrid-columnHeader .MuiDataGrid-columnHeaderTitleContainerContent .MuiCheckbox-root":
                { display: "none" },
              "& .MuiDataGrid-row .MuiIconButton-sizeSmall": {
                transition: "transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms",
              },
              "& .MuiDataGrid-row.MuiDataGrid-row--detailPanelExpanded .MuiIconButton-sizeSmall":
                {
                  transform: "rotateZ(180deg)",
                },
            }}
            rows={dataRows || []}
            columns={dataColumns}
            loading={loading}
            getRowId={(row: GridRowModel) =>
              row?.rowId ? row?.rowId : row?.id
            }
            //autoHeight={true}
            disableColumnFilter={true}
            pinnedColumns={pinnedColumns}
            disableColumnPinning={disableColumnPinning}
            checkboxSelection={validationCheckboxSelection}
            rowSelectionModel={selectedIds}
            onRowSelectionModelChange={(selectionModel) => {
              if (selectedIds) {
                const currentSelection = selectionModel;
                if (currentSelection.length > selectedIds.length) {
                  setSelectedIds((prevSelectedIds: any) => {
                    const updatedSelectedIds = new Set(prevSelectedIds);
                    selectionModel.forEach((id: any) => {
                      return updatedSelectedIds.add(id);
                    });
                    return Array.from(updatedSelectedIds);
                  });
                } else {
                  const deselectedId = selectedIds.find(
                    (id: any) => !currentSelection.includes(id)
                  );
                  setSelectedIds((prev: any) => {
                    return prev.filter((item: any) => item !== deselectedId);
                  });
                }
              }
            }}
            getRowClassName={(params: any) => {
              return params?.row?.is_derived ? "derived-column" : "d-none";
            }}
            getDetailPanelHeight={(params: any) => {
              const customValidationKeys = Object.keys(params?.row).filter(
                (key) => key.includes("_custom_validation")
              );
              const isCustomValidation = customValidationKeys.every(
                (key) =>
                  params?.row.hasOwnProperty(key) &&
                  (params?.row[key] === null || params?.row[key] === "")
              );
              return isCustomValidation ? 0 : "auto";
            }}
            slots={{
              ...(selectedIds &&
                selectedIds.length > 0 && {
                  footer: () => (
                    <>
                      <Box className="d-flex justify-content-between align-items-center">
                        <CustomFooter selectedIds={selectedIds} />
                        <Box className="MuiDataGrid-footerContainer">
                          <GridPagination />
                        </Box>
                      </Box>
                    </>
                  ),
                }),
            }}
            //for server side pagination
            {...dataGridProps}
          />
        ) : (
          <div>No Validation Errors Found</div>
        )}
      </div>
    </FlexBetween>
  );
};

export default ValidationErrorDataGrid;
