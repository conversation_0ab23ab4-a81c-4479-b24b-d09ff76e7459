import * as React from "react";
import {
  DataGridPremium,
  GridColDef,
  GridFeatureMode,
  GridRowModel,
  GridToolbarContainer,
  GridToolbarExport,
} from "@mui/x-data-grid-premium";
import { Box, Grid, Button, Stack, Typography } from "@mui/material";
import FlexBetween from "../FlexBetween";
import { IconEditSvg, IconDeleteSvg } from "../../common/utils/icons";
import useFetchAllLinkedServices from "../../hooks/useFetchAllLinkedServices";
import useFetchAllConnectionKey from "../../hooks/useFetchAllConnectionKey";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import { disableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";
import useFetchAllResources from "../../hooks/useFetchAllResources";

interface DataTableProps {
  dataRows: any;
  dataColumns: GridColDef[];
  loading?: boolean;
  checkboxSelection?: boolean;
  className?: string;
  buttonText?: string;
  buttonClass?: string;
  buttonClick?: () => void;
  buttonIcon?: React.ReactNode;
  dataListTitle?: string;
  handleEditValidationDialog?: (id: number, index: number) => void;
  handleDeleteValidation?: (index: number, rowId: number) => void;
  rowCount?: number;
  pageSizeOptions?: number[];
  paginationModel?: any;
  onPaginationModelChange?: (params: any) => void;
  paginationMode?: GridFeatureMode;
  rowGroupByColumnProps?: any;
  handleEditReferenceDialog?: (
    id: number,
    connectionKeys: any,
    linkedServices: any
  ) => void;
  handleDeleteReference?: (id: number) => void;
  handleAddMoreRow?: () => void;
  handleDeleteAllRows?: () => void;
  isExportButtonDisabled?: boolean;
  isGetDetailPanelRequired?: boolean;
  handleTransition?: (isChecked: boolean, actionType?: string) => void;
  singlePageMaxHeightDiff?: number;
  disableColumnMenu?: boolean;
  filterModel?: any;
  isPaginationRequired?: boolean;
  isPaginationChangeRequiredOnClientSide?: boolean;
  minHeight?: any;
  topMargin?: any;
}

const DataTable: React.FC<DataTableProps> = ({
  dataRows,
  dataColumns,
  checkboxSelection = false,
  rowGroupByColumnProps = { ...disableRowGroupByColumnProps },
  loading,
  className,
  buttonText,
  buttonClass,
  buttonClick,
  buttonIcon,
  dataListTitle,
  handleEditValidationDialog,
  handleDeleteValidation,
  rowCount,
  pageSizeOptions,
  paginationModel,
  onPaginationModelChange,
  paginationMode = "client",
  handleEditReferenceDialog,
  handleDeleteReference,
  handleAddMoreRow,
  handleDeleteAllRows,
  isExportButtonDisabled,
  isGetDetailPanelRequired,
  handleTransition,
  singlePageMaxHeightDiff,
  disableColumnMenu,
  filterModel,
  isPaginationRequired = true,
  isPaginationChangeRequiredOnClientSide,
  minHeight,
  topMargin,
}) => {
  const [linkedServiceIds, setLinkedServiceIds] = React.useState<any[]>([]);
  const [connectionKeyIds, setConnectionKeyIds] = React.useState<any[]>([]);
  const [resourcesIds, setResourcesIds] = React.useState<any[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [linkedServicesData] = useFetchAllLinkedServices({
    linkedServiceIds,
    setIsLoading,
  });

  const [connectionKeysData] = useFetchAllConnectionKey({
    connectionKeyIds,
    setIsLoading,
  });
  // const [resourcesData] = useFetchAllResources({
  //   resourcesIds,
  //   setIsLoading,
  // });

  const getLinkedServiceCodebyId = (id: number | string | null | undefined) => {
    const linkedData = linkedServicesData?.find((item: any) => item.id === id);
    return linkedData ? linkedData.name : null;
  };

  const getConnectionKeybyId = (id: number | string | null | undefined) => {
    const connectionData = connectionKeysData?.find(
      (item: any) => item.id === id
    );

    return connectionData ? connectionData?.name : null;
  };
  function CustomToolbar() {
    return (
      <GridToolbarContainer className="custom-toolbar">
        <Stack
          spacing={1}
          direction="row"
          alignItems="center"
          justifyContent={dataListTitle ? "space-between" : "flex-end"}
          sx={{ width: "100%", marginRight: 3, marginLeft: 3 }}
        >
          {dataListTitle && <h2 className="list-title">{dataListTitle}</h2>}
          <Box sx={{ display: "flex", columnGap: "10px" }}>
            {buttonClick && (
              <Button
                color="secondary"
                variant="contained"
                onClick={buttonClick}
                className={buttonClass}
              >
                <span
                  style={{
                    marginRight: "4px",
                    position: "relative",
                    top: "2px",
                  }}
                >
                  {buttonIcon}
                </span>
                {buttonText}
              </Button>
            )}
            {handleAddMoreRow && (
              <Button
                color="secondary"
                variant="contained"
                onClick={handleAddMoreRow}
                className="btn-orange btn-sm"
              >
                <AddCircleOutlineIcon /> &nbsp;Add New Column
              </Button>
            )}
            {handleTransition && (
              <Button
                variant="contained"
                color="secondary"
                className="btn-orange"
                onClick={() => handleTransition(true, "add")}
              >
                + &nbsp;Query
              </Button>
            )}
            {handleDeleteAllRows && dataRows.length > 0 && (
              <Button
                color="secondary"
                variant="contained"
                onClick={handleDeleteAllRows}
                className="btn-orange btn-border btn-sm"
              >
                <DeleteOutlineIcon /> &nbsp;Delete all columns
              </Button>
            )}
            <GridToolbarExport
              className="btn-orange btn-sm btn-border btn-export"
              // csvOptions={{
              //   fields: dataColumns
              //     .filter(
              //       (col) =>
              //         col.field !== "action" &&
              //         col.field !== "__detail_panel_toggle__"
              //     )
              //     .map((col) => col.field),
              // }}
              excelOptions={{
                fields: dataColumns
                  .filter(
                    (col) =>
                      col.field !== "action" &&
                      col.field !== "__detail_panel_toggle__" &&
                      col.field !== "checkbox"
                  )
                  .map((col) => col.field),
              }}
              printOptions={{ disableToolbarButton: true }}
              csvOptions={{ disableToolbarButton: true }}
            />
          </Box>
        </Stack>
      </GridToolbarContainer>
    );
  }

  const dataGridProps = {
    // ... other props
    paginationMode,
    pagination: paginationMode !== "client",
    ...(paginationMode !== "client"
      ? {
          rowCount,
          pageSizeOptions,
          paginationModel,
          onPaginationModelChange,
        }
      : {
          pagination: true, // Set to false for client-side pagination
          pageSize: paginationMode === "client" ? 25 : undefined,
          rowsPerPageOptions: paginationMode === "client" ? [25] : undefined,
          ...(isPaginationChangeRequiredOnClientSide
            ? { paginationModel, onPaginationModelChange }
            : {}),
        }),
  };
  React.useEffect(() => {
    // Use a set to keep track of unique linked service ids
    const uniqueLinkedServiceIds = new Set<number>();
    const uniqueConnectionKeyIds = new Set<number>();
    if (dataRows) {
      dataRows.forEach((element: any) => {
        if (
          element?.reference_column_definition &&
          element?.reference_column_definition?.linked_service_id
        ) {
          // Check if the id is not already in the set
          if (
            !uniqueLinkedServiceIds.has(
              element.reference_column_definition.linked_service_id
            )
          ) {
            // Add the id to the set and the array
            uniqueLinkedServiceIds.add(
              element.reference_column_definition.linked_service_id
            );
            setLinkedServiceIds((prev) => [
              ...prev,
              element.reference_column_definition.linked_service_id,
            ]);
          }
          if (
            !uniqueConnectionKeyIds.has(
              element.reference_column_definition.connection_key
            )
          ) {
            // Add the id to the set and the array
            uniqueConnectionKeyIds.add(
              element.reference_column_definition.connection_key
            );
            setConnectionKeyIds((prev) => [
              ...prev,
              element.reference_column_definition.connection_key,
            ]);
          }
        }
        if (element?.source?.length > 0) {
          const getLinkedServicesId = element?.source
            .map((item: any) => item?.linked_service_id)
            .filter((id: any) => id !== undefined && id !== null);
          setLinkedServiceIds(getLinkedServicesId);
          const getConnectionKeysId = element?.source
            .map((item: any) => item?.connection_key_id)
            .filter((id: any) => id !== undefined && id !== null);
          setConnectionKeyIds(getConnectionKeysId);
          const getResourcesId = element?.source
            .map((item: any) => item?.resource_id)
            .filter((id: any) => id !== undefined && id !== null);

          setResourcesIds(getResourcesId);
        }
      });
    }
  }, [dataRows]);

  return (
    <FlexBetween height="100%" width="100%" mt={topMargin ?? "20px"}>
      <div style={{ width: "100%" }}>
        <DataGridPremium
          getRowHeight={() => "auto"}
          className={className}
          sx={{
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#146FD0",
              fontWeight: 700,
            },
            minHeight: minHeight ? minHeight : "100px",
            // disable sort arrow in header
            "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer": {},
            "& .MuiDataGrid-menuIcon": {
              display: "block",
            },
            "& .MuiDataGrid-sortIcon": {},
            "& .MuiDataGrid-virtualScroller": {
              maxHeight: singlePageMaxHeightDiff
                ? `calc(100vh - ${singlePageMaxHeightDiff}px)`
                : `calc(100vh - 230px)`,
              minHeight: minHeight
                ? minHeight
                : singlePageMaxHeightDiff
                ? "100px"
                : "230px",
              overflowY: "auto !important",
              overflowX: { md: "auto" },
            },
          }}
          rows={dataRows}
          columns={dataColumns}
          checkboxSelection={checkboxSelection}
          loading={loading}
          getRowId={(row: GridRowModel) => row.id}
          //autoHeight={true}
          slots={
            isExportButtonDisabled
              ? false
              : {
                  toolbar: CustomToolbar,
                }
          }
          getRowClassName={(params: any) => {
            return `
            ${
              params?.row?.column_name === "file_name" ? "disabled-column" : ""
            } 
            ${params?.row?.is_derived ? "derived-column" : "d-none"} 
            ${params?.row?.changed && "changed-row"}`;
          }}
          getDetailPanelHeight={(params: any) => {
            const isCustomValidation =
              params?.row?.custom_validations?.length > 0;
            return params?.row?.is_derived || isCustomValidation
              ? "auto"
              : "auto";
          }}
          getDetailPanelContent={
            isGetDetailPanelRequired == false
              ? null
              : (params: any) => {
                  const isCustomValidation =
                    params?.row?.custom_validations?.length > 0;
                  const isReference =
                    params?.row?.is_reference &&
                    params?.row?.reference_column_definition != null;
                  const customValidations =
                    params?.row?.custom_validations?.length > 1
                      ? params?.row?.custom_validations
                      : params?.row?.custom_validations;
                  const customValidationsId = params?.row.id;
                  const referenceId = params?.row.id;
                  const requiredColumns =
                    params?.row?.derived_column_definition?.required_columns;
                  const isMixedQuery =
                    params?.row?.source?.length > 0 &&
                    params?.row?.source[0]?.type === "Mixed";

                  return (
                    <>
                      {params?.row?.is_derived ? (
                        <>
                          {params?.row?.derived_column_definition
                            ?.sql_expression && (
                            <Box sx={{ p: 2 }}>
                              <strong style={{ fontSize: "14px" }}>
                                Expression
                              </strong>{" "}
                              :
                              <div className="detail-panel">
                                {JSON.stringify(
                                  params?.row?.derived_column_definition
                                    ?.sql_expression
                                )?.split(";")}
                              </div>
                            </Box>
                          )}
                          {requiredColumns && requiredColumns.length > 0 && (
                            <Box sx={{ p: 2 }}>
                              <strong style={{ fontSize: "14px" }}>
                                Required Columns
                              </strong>{" "}
                              :
                              <div className="detail-panel">
                                {requiredColumns.join(",")}
                              </div>
                            </Box>
                          )}
                        </>
                      ) : null}
                      {isCustomValidation ? (
                        <>
                          <Box sx={{ p: 2 }}>
                            <strong
                              style={{
                                fontSize: "14px",
                                marginBottom: "5px",
                                display: "block",
                              }}
                            >
                              Validations:
                            </strong>
                            <div className="detail-panel">
                              <Grid container columnSpacing={2} rowSpacing={2}>
                                {customValidations?.map(
                                  (validation: any, i: number) => {
                                    return (
                                      <Grid item key={validation.name}>
                                        <Box
                                          className={`validation-item-box ${
                                            (handleEditValidationDialog ||
                                              handleDeleteValidation) &&
                                            "btns-pad"
                                          }`}
                                        >
                                          <Box>
                                            <strong>Name: </strong>
                                            {validation.name}
                                          </Box>
                                          <Box>
                                            <strong>Query: </strong>
                                            {validation.expression}
                                          </Box>
                                          {validation.filter_rule && (
                                            <Box>
                                              <strong>Filter: </strong>
                                              {validation.filter_rule}
                                            </Box>
                                          )}
                                          {(handleEditValidationDialog ||
                                            handleDeleteValidation) && (
                                            <>
                                              <Box className="action-btns">
                                                {handleEditValidationDialog && (
                                                  <>
                                                    <Box
                                                      className="edit-btn"
                                                      onClick={() =>
                                                        handleEditValidationDialog(
                                                          customValidationsId,
                                                          i
                                                        )
                                                      }
                                                    >
                                                      <IconEditSvg />
                                                    </Box>
                                                  </>
                                                )}
                                                {handleDeleteValidation && (
                                                  <>
                                                    <Box
                                                      className="delete-btn"
                                                      onClick={() =>
                                                        handleDeleteValidation(
                                                          i,
                                                          customValidationsId
                                                        )
                                                      }
                                                    >
                                                      <IconDeleteSvg />
                                                    </Box>
                                                  </>
                                                )}
                                              </Box>
                                            </>
                                          )}
                                        </Box>
                                      </Grid>
                                    );
                                  }
                                )}
                              </Grid>
                            </div>
                          </Box>
                        </>
                      ) : null}
                      {isReference ? (
                        <>
                          <Box sx={{ p: 2 }}>
                            <strong style={{ fontSize: "14px" }}>
                              Reference Definition
                            </strong>
                            :
                            <div className="detail-panel">
                              <Grid container columnSpacing={2} rowSpacing={2}>
                                <Grid item>
                                  <Box
                                    className={`validation-item-box ${
                                      (handleEditReferenceDialog ||
                                        handleDeleteReference) &&
                                      "btns-pad"
                                    } ${
                                      params?.row?.reference_column_definition
                                        ?.source === "internal"
                                        ? ""
                                        : "min-height"
                                    }`}
                                  >
                                    <Box>
                                      <strong>Source: </strong>{" "}
                                      {
                                        params?.row?.reference_column_definition
                                          ?.source
                                      }
                                    </Box>
                                    <Box>
                                      <strong>Type: </strong>
                                      {
                                        params?.row?.reference_column_definition
                                          ?.source_type
                                      }
                                    </Box>
                                    <Box>
                                      <strong>Translation: </strong>
                                      {params?.row?.reference_column_definition
                                        ?.use_translation
                                        ? "TRUE"
                                        : "FALSE"}
                                    </Box>
                                    {params?.row?.reference_column_definition
                                      ?.linked_service_id && (
                                      <Box>
                                        <strong>Linked Service: </strong>{" "}
                                        {
                                          linkedServicesData.find(
                                            (linkedServices: any) =>
                                              linkedServices?.id ===
                                              params?.row
                                                ?.reference_column_definition
                                                ?.linked_service_id
                                          )?.name
                                        }
                                      </Box>
                                    )}
                                    {params?.row?.reference_column_definition
                                      ?.linked_service_code && (
                                      <Box>
                                        <strong>Linked Service Code: </strong>{" "}
                                        {
                                          linkedServicesData.find(
                                            (linkedServices: any) =>
                                              linkedServices?.id ===
                                              params?.row
                                                ?.reference_column_definition
                                                ?.linked_service_id
                                          )?.code
                                        }
                                      </Box>
                                    )}
                                    {params?.row?.reference_column_definition
                                      ?.connection_key && (
                                      <Box>
                                        <strong>Connection key: </strong> {}
                                        {
                                          connectionKeysData.find(
                                            (connectionKey: any) =>
                                              connectionKey?.id ===
                                              params?.row
                                                ?.reference_column_definition
                                                ?.connection_key
                                          )?.name
                                        }
                                      </Box>
                                    )}
                                    {(handleEditReferenceDialog ||
                                      handleDeleteReference) && (
                                      <>
                                        <Box
                                          className={`action-btns ${
                                            params?.row
                                              ?.reference_column_definition
                                              ?.source === "internal"
                                              ? "gap-18"
                                              : "gap-25"
                                          }`}
                                        >
                                          {handleEditReferenceDialog && (
                                            <>
                                              <Box
                                                className="edit-btn"
                                                onClick={() =>
                                                  handleEditReferenceDialog(
                                                    referenceId,
                                                    connectionKeysData,
                                                    linkedServicesData
                                                  )
                                                }
                                              >
                                                <IconEditSvg />
                                              </Box>
                                            </>
                                          )}
                                          {handleDeleteReference && (
                                            <>
                                              <Box
                                                className="delete-btn"
                                                onClick={() =>
                                                  handleDeleteReference(
                                                    referenceId
                                                  )
                                                }
                                              >
                                                <IconDeleteSvg />
                                              </Box>
                                            </>
                                          )}
                                        </Box>
                                      </>
                                    )}
                                  </Box>
                                </Grid>
                              </Grid>
                            </div>
                          </Box>
                        </>
                      ) : null}
                      {isMixedQuery && (
                        <Box sx={{ p: 1 }}>
                          <Box
                            sx={{
                              display: "flex",
                              gap: 1,
                              "& > *": {
                                flex: 1,
                                minWidth: 0,
                              },
                            }}
                          >
                            {params?.row.source.map(
                              (source: any, index: number) => (
                                <Box
                                  key={index}
                                  sx={{
                                    bgcolor: "background.paper",
                                    borderRadius: 1,
                                    boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                                    overflow: "hidden",
                                    display: "flex",
                                    flexDirection: "column",
                                    border: `1px solid var(--table-border)`,
                                  }}
                                >
                                  <Box
                                    sx={{
                                      bgcolor: "primary.main",
                                      color: "white",
                                      p: 0.75,
                                      display: "flex",
                                      alignItems: "center",
                                      gap: 1,
                                    }}
                                  >
                                    <Typography
                                      variant="subtitle2"
                                      sx={{ fontWeight: 500, color: "#000000" }}
                                    >
                                      {source?.dependency_marker}
                                    </Typography>
                                    {source?.name && (
                                      <Typography
                                        variant="body2"
                                        sx={{ opacity: 0.9 }}
                                        noWrap
                                      >
                                        {source?.name}
                                      </Typography>
                                    )}
                                  </Box>

                                  <Box
                                    sx={{
                                      p: 1,
                                      display: "flex",
                                      flexDirection: "column",
                                      gap: 1,
                                      flex: 1,
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        display: "flex",
                                        gap: 1,
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          flex: 1,
                                          p: 0.75,
                                          bgcolor: "grey.50",
                                          borderRadius: 0.5,
                                        }}
                                      >
                                        <Typography
                                          variant="caption"
                                          color="text.secondary"
                                          display="block"
                                          gutterBottom
                                        >
                                          Sub Type
                                        </Typography>
                                        <Typography variant="body2" noWrap>
                                          {source.sub_type}
                                        </Typography>
                                      </Box>

                                      {source?.sub_type !== "Resource" && (
                                        <>
                                          <Box
                                            sx={{
                                              flex: 1,
                                              p: 0.75,
                                              bgcolor: "grey.50",
                                              borderRadius: 0.5,
                                            }}
                                          >
                                            <Typography
                                              variant="caption"
                                              color="text.secondary"
                                              display="block"
                                              gutterBottom
                                            >
                                              Linked Service
                                            </Typography>
                                            <Typography variant="body2" noWrap>
                                              {getLinkedServiceCodebyId(
                                                source.linked_service_id
                                              ) || "N/A"}
                                            </Typography>
                                          </Box>

                                          <Box
                                            sx={{
                                              flex: 1,
                                              p: 0.75,
                                              bgcolor: "grey.50",
                                              borderRadius: 0.5,
                                            }}
                                          >
                                            <Typography
                                              variant="caption"
                                              color="text.secondary"
                                              display="block"
                                              gutterBottom
                                            >
                                              Connection Key
                                            </Typography>
                                            <Typography variant="body2" noWrap>
                                              {getConnectionKeybyId(
                                                source.connection_key_id
                                              ) || "N/A"}
                                            </Typography>
                                          </Box>
                                        </>
                                      )}
                                    </Box>

                                    <Box
                                      sx={{
                                        p: 0.75,
                                        bgcolor: "grey.50",
                                        borderRadius: 0.5,
                                        flex: 1,
                                        display: "flex",
                                        flexDirection: "column",
                                        minHeight: 0,
                                      }}
                                    >
                                      <Typography
                                        variant="caption"
                                        color="text.secondary"
                                        display="block"
                                        gutterBottom
                                      >
                                        Query
                                      </Typography>
                                      <Box
                                        sx={{
                                          flex: 1,
                                          overflow: "auto",
                                          bgcolor: "background.paper",
                                          borderRadius: 0.5,
                                          p: 0.5,
                                        }}
                                      >
                                        <Typography
                                          component="pre"
                                          sx={{
                                            m: 0,
                                            fontFamily: "monospace",
                                            fontSize: "0.75rem",
                                            lineHeight: 1.4,
                                            whiteSpace: "pre-wrap",
                                            wordBreak: "break-word",
                                          }}
                                        >
                                          {source.data_pull_query?.replace(
                                            /"/g,
                                            ""
                                          )}
                                        </Typography>
                                      </Box>
                                    </Box>
                                  </Box>
                                </Box>
                              )
                            )}
                          </Box>
                        </Box>
                      )}
                    </>
                  );
                }
          }
          //for server side pagination
          {...dataGridProps}
          {...rowGroupByColumnProps}
          pagination={isPaginationRequired}
          disableRowSelectionOnClick={true}
          disableColumnMenu={disableColumnMenu ? disableColumnMenu : false}
          filterModel={filterModel}
        />
      </div>
    </FlexBetween>
  );
};

export default DataTable;
