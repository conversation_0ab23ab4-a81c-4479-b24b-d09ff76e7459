import React, { useState, useEffect, useRef } from "react";
import { GRID_CHECKBOX_SELECTION_COL_DEF } from "@mui/x-data-grid-pro";
import {
  DataGridPremium,
  GridRowModel,
  GridFeatureMode,
  GridPagination,
} from "@mui/x-data-grid-premium";
import {
  Tooltip,
  IconButton,
  Box,
  Button,
  Typography,
  Backdrop,
  CircularProgress,
} from "@mui/material";
import IncidentUserandStatusDialog from "../Molecules/Rule/IncidentUserandStatusDialog";
import {
  IconIncidentDetails,
  IconIncidentResources,
} from "../../common/utils/icons";
import DashboardUserDetails from "../Molecules/Rule/DashboardUserDetails";
import {
  capitalize,
  getFormattedDateTime,
  getSanatizedCode,
} from "../../services/utils";
import useFetchUsersList from "../../hooks/useFetchUsersList";
import { useLocation, useParams } from "react-router-dom";
import StatusCell from "../../common/utils/statusIcon";
import IssuesListSnapShot from "../Molecules/IssuesList/IssuesListSnapShot";
import {
  CommentColumn,
  CurrentStatusColumn,
  UserColumn,
} from "../../common/utils/commonActionsFields";
import { GridApiPremium } from "@mui/x-data-grid-premium/models/gridApiPremium";

interface IssueListDataGridProps {
  setUserDialogData: any;
  userDialogData: any;
  isLoading?: any;
  setIsLoading?: any;
  searchFilterData?: any;
  fetchedIssuesList: any;
  currentPageType: any;
  paginationModel?: any;
  onPaginationModelChange?: (params: any) => void;
  paginationMode?: GridFeatureMode;
  rowCount: any;
  pageSizeOptions?: number[];
}

function useQuery() {
  return new URLSearchParams(useLocation().search);
}
const initialDashboardData = {
  status: false,
  assigned_user: "",
  issueId: null,
  isBackDropVisible: false,
};

const initialSnapShotData = {
  isVisible: false,
  snapShotData: [],
  isBackDropVisible: false,
};

const IssueListDataGrid = ({
  setUserDialogData,
  userDialogData,
  isLoading,
  setIsLoading,
  searchFilterData,
  fetchedIssuesList,
  currentPageType,
  paginationModel,
  onPaginationModelChange,
  paginationMode = "client",
  rowCount,
  pageSizeOptions,
}: IssueListDataGridProps) => {
  const { ruleResultId }: any = useParams();
  const apiRef = useRef<GridApiPremium>(null!);

  const [detailViewDataRows, setDetailViewDataRows] = useState<any>([]);
  const [detailViewDataColumns, setDetailViewDataColumns] = useState<any>([]);
  const [selectedIds, setSelectedIds] = useState<any>([]);
  //const [isLoading, setIsLoading] = useState<boolean>(false);
  const [allUsersList, setallUsersList] = useState<any>([]);
  const [keyColumns, setKeyColumns] = useState<any>([]);
  const [isUserLoading, setIsUserLoading] = useState<boolean>(false);
  const [currentExecutionId, setCurrentExecutionId] = useState<
    number | null | undefined
  >(null);

  // execution id is static
  const dataGridProps = {
    // ... other props
    paginationMode,
    pagination: paginationMode !== "client",
    ...(paginationMode !== "client"
      ? {
          rowCount,
          pageSizeOptions,
          paginationModel,
          onPaginationModelChange,
        }
      : {
          pagination: true, // Set to false for client-side pagination
          pageSize: paginationMode === "client" ? 25 : undefined,
          rowsPerPageOptions: paginationMode === "client" ? [25] : undefined,
        }),
  };

  const [dashboardUserDetailsData, setDashboardUserDetailsData] =
    useState(initialDashboardData);
  const [snapShotData, setSnapShotData] = useState(initialSnapShotData);

  //commented this code only to check validation
  const getRecordDetailsForKey = (items: any[], keyToUse: string) => {
    const item = items[0];
    if (keyToUse === "comparison_keys") {
      return (
        item?.mismatched_record_details?.[keyToUse] ||
        item?.missing_record_details?.[keyToUse]
      );
    } else {
      return (
        item?.validation_error_record_details?.[keyToUse] ||
        item?.null_key_error_record_details?.[keyToUse] ||
        item?.duplicate_record_details?.[keyToUse]
      );
    }
  };

  useEffect(() => {
    if (fetchedIssuesList && fetchedIssuesList?.items?.length > 0) {
      const keyToUse =
        currentPageType === "execution" ? "comparison_keys" : "resource_keys";
      const rawData = getRecordDetailsForKey(
        fetchedIssuesList?.items || [],
        keyToUse
      );
      if (rawData) {
        try {
          //const comparisonKeysObj = JSON.parse(rawData);
          if (rawData) {
            const keys = Object.keys(rawData);
            setKeyColumns(keys);
          }
        } catch (error) {
          console.error("Error parsing JSON:", error);
        }
      }
    }
    if (dashboardUserDetailsData?.status === true) {
      setDashboardUserDetailsData(initialDashboardData);
    }
    if (snapShotData?.isVisible === true) {
      setSnapShotData(initialSnapShotData);
    }
  }, [fetchedIssuesList, currentPageType]);

  const [usersList] = useFetchUsersList({
    setIsLoading: setIsUserLoading,
  });
  useEffect(() => {
    setCurrentExecutionId(ruleResultId);
  }, [ruleResultId]);

  useEffect(() => {
    setallUsersList(usersList);
  }, [usersList]);
  const getFormattedKeysPairs = (inputString: string | undefined) => {
    const pairs: any = {};

    if (inputString && typeof inputString === "string") {
      const cleanedString = inputString.trim().slice(1, -1);
      const cleanedWithoutQuotes =
        cleanedString.startsWith('"') && cleanedString.endsWith('"')
          ? cleanedString.slice(1, -1)
          : cleanedString;

      cleanedWithoutQuotes.split(",").forEach((pair) => {
        if (pair.trim() !== "None") {
          let [key, value] = pair.trim().split(":");
          key = key.replace(/^"|"$/g, "").trim();
          if (key && value) {
            pairs[key.trim()] = getSanatizedCode(value.trim());
          }
        }
      });
    }

    return pairs;
  };

  useEffect(() => {
    if (fetchedIssuesList?.items?.length > 0) {
      const updatedDetailView = fetchedIssuesList?.items?.map((detail: any) => {
        return {
          ...detail,
          comparison_keys: getFormattedKeysPairs(detail.comparison_keys),
          additional_info: getFormattedKeysPairs(detail.additional_info),
          rowId: detail.id,
        };
      });
      setDetailViewDataRows(updatedDetailView);
    } else {
      setDetailViewDataRows([]);
      setDetailViewDataColumns([]);
    }
  }, [fetchedIssuesList]);

  useEffect(() => {
    if (
      detailViewDataRows?.length > 0 &&
      (detailViewDataRows[0]?.mismatched_record_details?.comparison_keys ||
        detailViewDataRows[0]?.missing_record_details?.comparison_keys ||
        detailViewDataRows[0]?.validation_error_record_details?.resource_keys ||
        detailViewDataRows[0]?.null_key_error_record_details ||
        detailViewDataRows[0]?.duplicate_record_details?.resource_keys)
    ) {
      getKeyColumns(detailViewDataRows);
      getKeyRows(detailViewDataRows);
    }
  }, [detailViewDataRows]);

  const processRows = (rows: any[]) => {
    if (!rows) return;
    return rows.map((row: any) => {
      const updatedRow = { ...row };

      Object.keys(updatedRow).forEach((key) => {
        if (typeof updatedRow[key] === "string") {
          if (
            /\.0$/.test(updatedRow[key]) &&
            !/\.0[1-9]/.test(updatedRow[key])
          ) {
            updatedRow[key] = updatedRow[key].slice(0, -2);
          }
        }
      });

      return updatedRow;
    });
  };
  const getRecordDetails = (item: any, key: string, currentPageType: any) => {
    return currentPageType === "execution"
      ? item?.mismatched_record_details?.[key] ||
          item?.missing_record_details?.[key]
      : item?.validation_error_record_details?.[key] ||
          //item?.null_key_error_record_details?.[key] ||
          item?.duplicate_record_details?.[key];
  };

  const getKeyRows = (missing_records_details: any) => {
    const rows: any = missing_records_details?.map((item: any, idx: any) => {
      const diffInDays =
        (new Date(new Date().toISOString().split("T")[0]).getTime() -
          new Date(item.first_occurrence.split("T")[0]).getTime()) /
        (1000 * 3600 * 24);
      const recordDetails = getRecordDetails(
        item,
        currentPageType === "execution" ? "comparison_keys" : "resource_keys",
        currentPageType
      );

      return {
        id: item?.id,
        rowId: item?.id,

        snapShot:
          item?.mismatched_record_details?.record_snapshot?.record_data ||
          item?.missing_record_details?.record_snapshot?.record_data ||
          item?.validation_error_record_details?.record_snapshot?.record_data ||
          item?.null_key_error_record_details?.record_snapshot?.record_data ||
          item?.duplicate_record_details?.record_snapshot?.record_data,
        keys: { ...recordDetails },
        rule_id: item?.rule_id,
        execution_id: item?.execution_id,
        error_message:
          item?.mismatched_record_details?.error_message ||
          item?.missing_record_details?.error_message ||
          item?.validation_error_record_details?.error_message ||
          item?.null_key_error_record_details?.error_message ||
          item?.duplicate_record_details?.error_message,

        issue_type: item?.issue_type,
        first_occurrence: item?.first_occurrence,
        days_in_queue: diffInDays,
        current_status: item?.current_status,
        comment: item?.comment,
        assigned_user: item?.assigned_user,
        create_date: item?.create_date,
        domain_name: item?.domain_record_details?.domain_name,
        name:
          currentPageType === "execution"
            ? item?.rule_record_details?.name
            : item?.resource_record_details?.resource_name,
        execution_name: item?.execution_record_details?.execution_name,
      };
    });
    const processedRows = processRows(rows);
    setDetailViewDataRows(processedRows);
  };

  const getKeyColumns = (missing_records_details: any) => {
    const keyToUse =
      currentPageType === "execution" ? "comparison_keys" : "resource_keys";

    const rawData = getRecordDetails(
      missing_records_details[0],
      keyToUse,
      currentPageType
    );

    setDetailViewDataColumns([
      //...columns,
      {
        field: "keys",
        headerName: "Keys",
        flex: 1,
        minWidth: 250,
        renderCell: (params: any) => {
          return (
            <div
              dangerouslySetInnerHTML={{
                __html:
                  params &&
                  params?.row?.keys &&
                  Object.entries(params?.row?.keys).length > 0
                    ? Object.entries(params?.row?.keys)
                        .map(([key, value]: any, index: any) => {
                          return (
                            `${key}: ${value}` +
                            (index !==
                            Object.entries(params?.row?.keys).length - 1
                              ? "<br />"
                              : "")
                          );
                        })
                        .join("")
                    : "N/A",
              }}
            />
          );
        },
      },
      {
        field: "issue_type",
        headerName: "Issue Type",
        flex: 1,
        minWidth: 150,
        renderCell: (params: any) => {
          return <Box>{capitalize(params.value)}</Box>;
        },
      },
      {
        field: "domain_name",
        headerName: "Domain Name",
        flex: 1,
        minWidth: 150,
        renderCell: (params: any) => {
          return <Box className="word-break-all">{params.value}</Box>;
        },
      },
      {
        field: "execution_name",
        headerName: "Execution Name",
        flex: 1,
        minWidth: 150,
        renderCell: (params: any) => {
          return <Box className="word-break-all">{params.value}</Box>;
        },
      },
      {
        field: "name",
        headerName:
          currentPageType === "execution" ? "Rule Name" : "Resource Name",
        flex: 1,
        minWidth: 200,
        renderCell: (params: any) => {
          return <Box className="word-break-all">{params.value}</Box>;
        },
      },
      {
        field: "error_message",
        headerName: "Error Message",
        minWidth: 500,
        renderCell: (params: any) => {
          return (
            <Tooltip title={params.value} placement="top">
              <Box>{params.value}</Box>
            </Tooltip>
          );
        },
      },
      {
        field: "days_in_queue",
        headerName: "Days in queue",
        flex: 1,
        minWidth: 210,
        renderCell: (params: any) => {
          return (
            <Box>
              {params?.row?.first_occurrence === null ? (
                params.value ?? 0
              ) : (
                <Tooltip
                  title={
                    <Typography
                      sx={{ fontSize: "0.875rem" }}
                    >{`First Occurrence : ${getFormattedDateTime(
                      params?.row?.first_occurrence
                    )}`}</Typography>
                  }
                  placement="top"
                  className="tooltip-ellipsis"
                >
                  {params.value ?? 0}
                </Tooltip>
              )}
            </Box>
          );
        },
      },
      CurrentStatusColumn({}),
      UserColumn({}),
      CommentColumn({}),

      {
        field: "action",
        headerName: "Action",
        align: "center",
        headerAlign: "center",
        minWidth: "130",
        pinned: "right",
        renderCell: (params: any, index: any) => {
          return (
            <>
              <Box sx={{ display: "inline-flex", columnGap: "12px" }}>
                <Tooltip
                  title={`Incident Details : ${params?.row?.id}`}
                  placement="top"
                  arrow
                >
                  <Button
                    className="btn-blue btn-orange btn-sm"
                    onClick={(event) => {
                      event.stopPropagation();
                      setDashboardUserDetailsData((prev: any) => ({
                        ...prev,
                        status: true,
                        incidentId: params.row.rowId,
                        assigned_user: params.row.assigned_user,
                        panelType: "incidentDetail",
                        isBackDropVisible: true,
                        comment: [
                          {
                            comment: params.row.comment,
                            create_date: params.row.create_date,
                            assigned_user: params.row?.assigned_user || "",
                          },
                        ],
                        current_status: params.row.current_status,
                        issueId: params.row.id,
                      }));
                    }}
                  >
                    <IconIncidentDetails />
                  </Button>
                </Tooltip>
                <Tooltip title="SnapShot Records" placement="top" arrow>
                  <Button
                    className="btn-blue btn-orange btn-sm"
                    onClick={(event) => {
                      event.stopPropagation();
                      setSnapShotData((prev: any) => ({
                        ...prev,
                        isVisible: true,
                        isBackDropVisible: true,
                        snapShotData: params?.row?.snapShot,
                      }));
                    }}
                  >
                    <IconIncidentResources />
                  </Button>
                </Tooltip>
              </Box>
            </>
          );
        },
      },
    ]);
  };

  React.useEffect(() => {
    setUserDialogData((prev: any) => ({
      ...prev,
      selectedIds,
    }));
  }, [selectedIds]);
  const [isAllColumnPinned, setIsAllColumnPinned] = useState(false);
  const [pinnedColumns, setPinnedColumns] = useState({
    left: [GRID_CHECKBOX_SELECTION_COL_DEF.field],
    right: ["current_status", "username", "comment", "action"],
  });

  useEffect(() => {
    setPinnedColumns((prevState) => {
      const updatedLeft = [
        GRID_CHECKBOX_SELECTION_COL_DEF.field,
        ...keyColumns,
      ];
      const updatedRight = [...prevState.right];

      return {
        left: updatedLeft,
        right: updatedRight,
      };
    });
  }, [keyColumns]);

  const handleMoveColumns = () => {
    setPinnedColumns((prevState) => {
      const columnsToAdd = ["current_status", "username", "comment"];
      const isColumnsAvailable = columnsToAdd.every((item) =>
        prevState.right.includes(item)
      );
      const isKeyColumnAvailable = keyColumns.every((item: any) =>
        prevState.left.includes(item)
      );
      setIsAllColumnPinned(isColumnsAvailable);
      if (isColumnsAvailable && isKeyColumnAvailable) {
        return {
          left: prevState.left.filter((item) => !keyColumns.includes(item)),
          right: prevState.right.filter((item) => !columnsToAdd.includes(item)),
        };
      } else {
        return {
          left: [...prevState.left, ...keyColumns],
          right: [...columnsToAdd, ...prevState.right],
        };
      }
    });
  };
  const [maxHeight, setMaxHeight] = useState<any>(window.innerHeight - 270);

  React.useEffect(() => {
    const handleResize = () => {
      setMaxHeight(window.innerHeight - 270);
    };
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const rowHeight =
    detailViewDataRows.length < 5
      ? detailViewDataRows.length * 60 + 171
      : detailViewDataRows.length < 11
      ? detailViewDataRows.length * 40 + 100
      : maxHeight + 95;
  useEffect(() => {
    if (apiRef.current) {
      setTimeout(() => {
        apiRef?.current?.resetRowHeights && apiRef?.current?.resetRowHeights();
      }, 500);
    }
  }, [detailViewDataRows]);
  const CustomFooter = ({ selectedIds }: any) => (
    <div className="datatable-footer">
      <strong>Selected IDs:</strong> [{selectedIds.join(", ")}]
      <Button
        className="btn-orange btn-dark btn-sm plus-btn-sm"
        onClick={() => setSelectedIds([])}
      >
        Reset
      </Button>
    </div>
  );

  const previousSelectedIds = useRef(selectedIds);
  useEffect(() => {
    // Before data reload, persist the selectedIds
    previousSelectedIds.current = selectedIds;
  }, [selectedIds]);
  useEffect(() => {
    if (detailViewDataRows && detailViewDataRows.length && setSelectedIds) {
      setSelectedIds(previousSelectedIds.current); // Restore selectedIds after data reload
    }
  }, [detailViewDataRows]);
  return (
    <>
      <>
        <Backdrop
          sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
          open={
            (dashboardUserDetailsData?.isBackDropVisible ||
              snapShotData.isBackDropVisible) ??
            false
          }
        ></Backdrop>
        <Box
          className="position-relative"
          style={{
            height: pinnedColumns ? `${rowHeight}px` : "auto",
            maxHeight: pinnedColumns ? `${rowHeight}px` : "auto",
            minHeight: pinnedColumns ? `${rowHeight}px` : "auto",
            width: "100%",
          }}
        >
          {detailViewDataRows?.length > 0 && (
            <IconButton
              onClick={handleMoveColumns}
              className={`icon-pin-datagrid navbar-icon burger  ${
                isAllColumnPinned ? "active" : ""
              }`}
            >
              <div className="bar bar1"></div>
              <div className="bar bar2"></div>
              <div className="bar bar3"></div>
            </IconButton>
          )}

          <DataGridPremium
            apiRef={apiRef}
            getRowHeight={() => "auto"}
            className="dataTable no-radius pt-0 bdr-top-0 datatable-column-sep checkbox-result-column remove-pinnedColumnHeaders-bg pinnedColumnHeaders-bg-height"
            sx={{
              "&.columnGroupingModel": {
                "& .MuiDataGrid-columnHeaders": {
                  overflow: "visible",
                },
                "& .MuiDataGrid-row": {
                  backgroundColor: "transparent",
                  "&:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.04)",
                  },
                },
              },
              "& .MuiDataGrid-main": {
                position: "relative",
                "& .MuiDataGrid-columnHeader--filledGroup": {
                  borderBottom: "solid 1px #e0e0e0",
                  "& .MuiDataGrid-withBorderColor": {
                    borderColor: "transparent",
                  },
                  "& + .MuiDataGrid-columnHeader--filledGroup ": {
                    "&:before": {
                      content: '""',
                      background: "#e0e0e0",
                      width: "1px",
                      height: "1000px",
                      position: "absolute",
                      top: "0px",
                      left: "0px",
                      zIndex: "9999999",
                    },
                  },
                },
              },
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: "#146FD0",
                fontWeight: 700,
              },
              "& .MuiDataGrid-overlayWrapperInner": {
                // height: "auto !important", //commented because its resizing the overlay window backdrop widow

                "& .MuiDataGrid-overlay": {
                  backgroundColor: "rgba(0, 0, 0, 0.1)",
                },
              },
              "& .MuiDataGrid-footerContainer": {},
              minHeight: "100px",
              // disable sort arrow in header
              "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer":
                {},
              "& .MuiDataGrid-columnHeader .MuiDataGrid-columnHeaderTitleContainerContent .MuiCheckbox-root":
                { display: "none" },
              "& .MuiDataGrid-menuIcon": {
                display: "block",
              },
              "& .MuiDataGrid-sortIcon": {},
              "& .MuiDataGrid-virtualScroller": {
                minHeight: "100px",
                maxHeight: `calc(100vh - 280px)`,
                overflowY: "auto !important",
                overflowX: { md: "auto" },
              },
            }}
            rows={detailViewDataRows}
            columns={detailViewDataColumns}
            checkboxSelection={true}
            rowSelectionModel={selectedIds}
            onRowSelectionModelChange={(selectionModel) => {
              if (selectedIds) {
                const currentSelection = selectionModel;
                if (currentSelection.length > selectedIds.length) {
                  setSelectedIds((prevSelectedIds: any) => {
                    const updatedSelectedIds = new Set(prevSelectedIds);
                    selectionModel.forEach((id: any) => {
                      return updatedSelectedIds.add(id);
                    });
                    return Array.from(updatedSelectedIds);
                  });
                } else {
                  const deselectedId = selectedIds.find(
                    (id: any) => !currentSelection.includes(id)
                  );
                  setSelectedIds((prev: any) => {
                    return prev.filter((item: any) => item !== deselectedId);
                  });
                }
              }
            }}
            //checkboxSelection={checkboxSelection}
            //rowSelectionModel={selectedIds}
            loading={isLoading}
            getRowId={(row: GridRowModel) => row?.id}
            //autoHeight={true}
            disableColumnFilter={true}
            disableColumnSelector={true}
            disableColumnMenu={false}
            getRowClassName={(params: any) => {
              return params?.row?.is_derived ? "derived-column" : "";
            }}
            pinnedColumns={detailViewDataRows?.length > 0 ? pinnedColumns : {}}
            disableColumnPinning={false}
            disableRowGrouping={true}
            disableAggregation={true}
            slots={{
              ...(selectedIds &&
                selectedIds.length > 0 && {
                  footer: () => (
                    <>
                      <Box className="d-flex justify-content-between align-items-center">
                        <CustomFooter selectedIds={selectedIds} />
                        <Box className="MuiDataGrid-footerContainer">
                          <GridPagination />
                        </Box>
                      </Box>
                    </>
                  ),
                }),
            }}
            {...dataGridProps}
          />
        </Box>
      </>

      <DashboardUserDetails
        dashboardUserDetailsData={dashboardUserDetailsData}
        setDashboardUserDetailsData={setDashboardUserDetailsData}
        allUsersList={allUsersList}
        setIsLoading={setIsLoading}
        setDetailViewDataRows={setDetailViewDataRows}
        issueFrom={
          currentPageType === "execution" ? "comparison" : "validation"
        }
      />
      <IssuesListSnapShot
        snapShotData={snapShotData}
        setSnapShotData={setSnapShotData}
      />
      <IncidentUserandStatusDialog
        userDialogData={userDialogData}
        setUserDialogData={setUserDialogData}
        allUsersList={allUsersList}
        setDetailViewDataRows={setDetailViewDataRows}
        issueFrom={
          currentPageType === "execution" ? "comparison" : "validation"
        }
        setSelectedIds={setSelectedIds}
      />
    </>
  );
};

export default IssueListDataGrid;
