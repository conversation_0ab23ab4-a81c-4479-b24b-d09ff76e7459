import * as React from "react";
import {
  DataGridPremium,
  GridColDef,
  GridRowModel,
  GridToolbarContainer,
  GridToolbarExport,
} from "@mui/x-data-grid-premium";
import {
  Box,
  Tooltip,
  Button,
  Stack,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";
import FlexBetween from "../FlexBetween";
import FirstPageIcon from "@mui/icons-material/FirstPage";
import KeyboardArrowLeft from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight";
import LastPageIcon from "@mui/icons-material/LastPage";
import { useTheme } from "@mui/material/styles";
import CustomPaginationActions from "../Molecules/Paginations/CustomPaginationActions";
import { IconDownloadSvg } from "../../common/utils/icons";
interface DataTableProps {
  dataRows: any[];
  dataColumns: GridColDef[];
  loading?: boolean;
  checkboxSelection?: boolean;
  className?: string;
  buttonText?: string;
  buttonClass?: string;
  buttonClick?: () => void;
  buttonIcon?: React.ReactNode;
  dataListTitle?: string;
  isExportButtonRequired?: boolean;
  rowCount?: number;
  pageSizeOptions?: number[];
  paginationModel?: any;
  onPaginationModelChange?: (params: any) => void;
  downloadFromAPIFile?: (params: any) => void;
  setLoading?: (params: any) => any;
  singlePageMaxHeightDiff?: number;
}

interface TablePaginationActionsProps {
  count: number;
  page: number;
  rowsPerPage: number;
  onPageChange: (
    event: React.MouseEvent<HTMLButtonElement>,
    newPage: number
  ) => void;
}

const ConsolidateDataTable: React.FC<DataTableProps> = ({
  dataRows,
  dataColumns,
  checkboxSelection = false,
  loading,
  className,
  buttonText,
  buttonClass,
  buttonClick,
  buttonIcon,
  dataListTitle,
  isExportButtonRequired,
  rowCount,
  pageSizeOptions,
  paginationModel,
  onPaginationModelChange,
  downloadFromAPIFile,
  setLoading,
  singlePageMaxHeightDiff,
}) => {
  function CustomToolbar() {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const handleClick = (event: any) => {
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleExportExcel = () => {
      downloadFromAPIFile && downloadFromAPIFile(setLoading);
      handleClose();
    };
    return (
      <GridToolbarContainer className="custom-toolbar">
        <Stack
          spacing={1}
          direction="row"
          alignItems="center"
          justifyContent={dataListTitle ? "space-between" : "flex-end"}
          sx={{ width: "100%", marginRight: 3, marginLeft: 3 }}
        >
          {dataListTitle && <h2 className="list-title">{dataListTitle}</h2>}
          <Box sx={{ display: "flex", columnGap: "10px" }}>
            {buttonClick && (
              <Button
                color="secondary"
                variant="contained"
                onClick={buttonClick}
                className={buttonClass}
              >
                <span
                  style={{
                    marginRight: "4px",
                    position: "relative",
                    top: "2px",
                  }}
                >
                  {buttonIcon}
                </span>{" "}
                {buttonText}
              </Button>
            )}
            {/* {downloadFromAPIFile && (
              <Tooltip title="Download Excel" placement="top">
                <button
                  className="btn-export-1 btn-orange btn-border"
                  onClick={() => downloadFromAPIFile(setLoading)}
                >
                  <IconDownloadSvg />
                </button>
              </Tooltip>
            )} */}
            {downloadFromAPIFile && (
              <>
                <button
                  className="btn-export-1 btn-orange btn-border"
                  onClick={(e) => handleClick(e)}
                >
                  <IconDownloadSvg />
                </button>
                <Menu
                  anchorEl={anchorEl}
                  keepMounted
                  open={Boolean(anchorEl)}
                  onClose={handleClose}
                >
                  <MenuItem onClick={handleExportExcel}>
                    Download as Excel
                  </MenuItem>
                </Menu>
              </>
            )}
            {isExportButtonRequired && !downloadFromAPIFile && (
              <Tooltip
                title="Download"
                placement="top"
                arrow
                className="export-csv"
              >
                <GridToolbarExport
                  className="btn-orange btn-sm btn-border btn-export"
                  // csvOptions={{
                  //   fields: dataColumns
                  //     .filter(
                  //       (col) =>
                  //         col.field !== "action" &&
                  //         col.field !== "__detail_panel_toggle__"
                  //     )
                  //     .map((col) => col.field),
                  // }}
                  excelOptions={{
                    fields: dataColumns
                      .filter((col) => col.field !== "action")
                      .map((col) => col.field),
                  }}
                  printOptions={{ disableToolbarButton: true }}
                  csvOptions={{ disableToolbarButton: true }}
                />
              </Tooltip>
            )}
          </Box>
        </Stack>
      </GridToolbarContainer>
    );
  }

  return (
    <FlexBetween height="100%" width="100%" mt="20px">
      <div style={{ width: "100%" }}>
        <DataGridPremium
          getRowHeight={() => "auto"}
          className={className}
          componentsProps={{
            pagination: {
              ActionsComponent: CustomPaginationActions,
            },
          }}
          sx={{
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: "#146FD0",
              fontWeight: 700,
            },
            minHeight: "100px",
            // disable sort arrow in header
            "& .MuiDataGrid-columnHeader .MuiDataGrid-iconButtonContainer": {},
            "& .MuiDataGrid-menuIcon": {
              display: "none",
            },
            "& .MuiDataGrid-sortIcon": {},
            "& .MuiDataGrid-virtualScroller": {
              maxHeight: singlePageMaxHeightDiff
                ? `calc(100vh - ${singlePageMaxHeightDiff}px)`
                : `calc(100vh - 230px)`,
              minHeight: singlePageMaxHeightDiff ? "100px" : "230px",
              overflowY: "auto !important",
              overflowX: { md: "auto" },
            },
            "& .MuiDataGrid-row .MuiIconButton-sizeSmall": {
              transition: "transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms",
            },
            "& .MuiDataGrid-row.MuiDataGrid-row--detailPanelExpanded .MuiIconButton-sizeSmall":
              {
                transform: "rotateZ(180deg)",
              },
          }}
          rows={dataRows || []}
          columns={dataColumns}
          paginationMode="server"
          checkboxSelection={checkboxSelection}
          loading={loading}
          pagination={true}
          getRowId={(row: GridRowModel) => row?.id}
          autoHeight={true}
          disableColumnFilter={true}
          slots={{
            toolbar: CustomToolbar,
          }}
          getRowClassName={(params: any) => {
            return params?.row?.is_derived ? "derived-column" : "d-none";
          }}
          rowCount={rowCount}
          pageSizeOptions={pageSizeOptions}
          paginationModel={paginationModel}
          onPaginationModelChange={onPaginationModelChange}
        />
      </div>
    </FlexBetween>
  );
};

export default ConsolidateDataTable;
