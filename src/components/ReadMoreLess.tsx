import React, { useState } from "react";

function ReadMoreLess({ data, charLimit = 100 }: any) {
  const [isShowMore, setIsShowMore] = useState(false);
  const toggleReadMoreLess = () => {
    setIsShowMore(!isShowMore);
  };

  return (
    <div className="card">
      {!isShowMore && `${data.substr(0, charLimit)}...`}
      {isShowMore && data}

      <button onClick={toggleReadMoreLess} className="ml-5 link-btn">
        {isShowMore ? "Read Less" : "Read More"}
      </button>
    </div>
  );
}

export default ReadMoreLess;
