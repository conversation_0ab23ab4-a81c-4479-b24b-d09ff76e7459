import React from 'react';
import { useWebSocketService } from '../services/webSocketService';

// This component doesn't render anything visible
// It just initializes and manages the WebSocket connection
const WebSocketProvider: React.FC = () => {
  // Initialize the WebSocket service
  useWebSocketService();
  
  // This component doesn't render anything
  return null;
};

export default WebSocketProvider;
