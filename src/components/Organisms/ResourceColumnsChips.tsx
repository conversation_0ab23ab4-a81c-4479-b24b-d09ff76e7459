import React, { useState, ChangeEvent, useEffect } from "react";
import { Box, Grid, IconButton, Tab, Tabs, Tooltip, Chip } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import { filterColumns } from "@mui/x-data-grid-pro/components/DataGridProVirtualScroller";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";

interface IResourceColumnsChips {
  setEditorValue: any;
  editorRef: any;
  columns: any;
  isFinalQuery: any;
  dialogType?: string;
  queryBuilderType?: string;
}

const ResourceColumnsChips = ({
  setEditorValue,
  editorRef,
  columns,
  isFinalQuery,
  dialogType,
  queryBuilderType,
}: IResourceColumnsChips) => {
  // States
  const [searchText, setSearchText] = useState<string>("");
  const [filteredChips, setFilteredChips] = useState<any>({
    resourceColumns: [],
    missingColumns: [],
    mismatchedColumns: [],
    mergedColumns: [],
    mixedQueryData: [],
    fetchedColumns: [],
  });
  const [selectedTab, setSelectedTab] = useState<string>("resourceColumns");

  // Handle Search Change
  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchText(event.target.value);
  };

  // Filter Chips
  useEffect(() => {
    if (columns) {
      const knownKeys = [
        "resourceColumns",
        "missingColumns",
        "mismatchedColumns",
        "mergedColumns",
        "mixedQueryData",
      ];

      const dynamicKeys: Record<string, any[]> = {};

      Object.keys(columns).forEach((key) => {
        if (!knownKeys.includes(key)) {
          dynamicKeys[key] = columns[key].filter((chip: any) =>
            chip.name.toLowerCase().includes(searchText.toLowerCase())
          );
        }
      });

      setFilteredChips({
        resourceColumns: columns.resourceColumns?.filter(
          (chip: any) =>
            chip.name &&
            chip.name.toLowerCase().includes(searchText.toLowerCase())
        ),
        missingColumns: columns.missingColumns?.filter(
          (chip: any) =>
            chip.name &&
            chip.name.toLowerCase().includes(searchText.toLowerCase())
        ),
        fetchedColumns: columns.fetchedColumns?.filter(
          (chip: any) =>
            chip.name &&
            chip.name.toLowerCase().includes(searchText.toLowerCase())
        ),
        mismatchedColumns: columns.mismatchedColumns?.filter(
          (chip: any) =>
            chip.name &&
            chip.name.toLowerCase().includes(searchText.toLowerCase())
        ),
        mergedColumns: columns.mergedColumns?.filter(
          (chip: any) =>
            chip.name &&
            chip.name.toLowerCase().includes(searchText.toLowerCase())
        ),
        mixedQueryData: (() => {
          const result: any = {};
          if (columns.mixedQueryData) {
            Object.entries(columns.mixedQueryData).forEach(
              ([resourceId, resource]: any) => {
                result[resourceId] = {
                  name: resource.name,
                  columns: resource.columns.filter(
                    (chip: any) =>
                      chip.name &&
                      chip.name.toLowerCase().includes(searchText.toLowerCase())
                  ),
                };
              }
            );
          }
          return result;
        })(),
        ...dynamicKeys,
      });
    }
  }, [searchText, columns]);

  // Determine First Tab to Select

  // Handle Column Select
  const handleColumnSelect = (column: string, columnName: string) => {
    if (!editorRef.current) return;
    const editor = editorRef.current.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;

    let updatedColumn = column;

    if (columnName === "missing") {
      updatedColumn = `[##MISSING## ${column}]`;
    } else if (columnName === "mismatched") {
      updatedColumn = `[##MISMATCHED## ${column}]`;
    } else if (columnName === "resource") {
      updatedColumn = `[${column}]`;
    } else if (columnName === "merged") {
      updatedColumn = `[${column}]`;
    } else {
      updatedColumn = `[##${columnName}## ${column}]`;
    }

    const updatedLine =
      currentLine.slice(0, currentColumn) +
      updatedColumn +
      " " +
      currentLine.slice(currentColumn);

    setEditorValue(updatedLine);
    editor.session.insert(currentPosition, updatedColumn);

    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + updatedColumn.length + 1,
    };

    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  // Handle Tab Change
  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
  };

  const dynamicKeys = Object.keys(filteredChips).filter(
    (key) =>
      ![
        "resourceColumns",
        "missingColumns",
        "mismatchedColumns",
        "mergedColumns",
        "mixedQueryData",
        "mixedDependencyColumns",
        "ownResourceColumn",
        "fetchedColumns",
      ].includes(key)
  );

  const tabContent: Record<string, JSX.Element> = {};
  const availableTabs: { key: string; label: string }[] = [];
  useEffect(() => {
    if (
      availableTabs.length > 0 &&
      !availableTabs.find((tab) => tab.key === selectedTab)
    ) {
      setSelectedTab(availableTabs[0].key);
    }
  }, [filteredChips, availableTabs]);

  if (queryBuilderType === "rule_research_query") {
    if (
      isFinalQuery ||
      filteredChips?.resourceColumns?.length > 0 ||
      filteredChips?.mergedColumns?.length > 0 ||
      filteredChips?.missingColumns?.length > 0 ||
      filteredChips?.mismatchedColumns?.length > 0 ||
      filteredChips?.ownResourceColumn?.length > 0 ||
      (filteredChips?.mixedDependencyColumns &&
        filteredChips.mixedDependencyColumns.length > 0) ||
      filteredChips?.fetchedColumns?.length > 0
    ) {
      // Static tabs
      if (filteredChips.resourceColumns?.length > 0) {
        tabContent.resourceColumns = (
          <>
            {filteredChips.resourceColumns.map((option: any) => (
              <Chip
                key={option.name}
                label={option.name}
                onClick={() => handleColumnSelect(option.name, "resource")}
              />
            ))}
          </>
        );
        availableTabs.push({
          key: "resourceColumns",
          label: "Resource Columns",
        });
      }

      if (filteredChips.mergedColumns?.length > 0) {
        tabContent.mergedColumns = (
          <>
            {filteredChips.mergedColumns.map((option: any) => (
              <Chip
                key={option.name}
                label={option.name}
                onClick={() => handleColumnSelect(option.name, "merged")}
              />
            ))}
          </>
        );
        availableTabs.push({ key: "mergedColumns", label: "Merged Columns" });
      }
      if (dialogType !== "mixed") {
        (filteredChips?.ownResourceColumn || []).forEach((item: any) => {
          if (item.columns?.length > 0) {
            const tabKey = `${item.name}`;
            tabContent[tabKey] = (
              <>
                {item.columns.map((option: any) => (
                  <Chip
                    key={option.name}
                    label={option.name}
                    onClick={() => handleColumnSelect(option.name, "resource")}
                  />
                ))}
              </>
            );
            availableTabs.push({
              key: tabKey,
              label: item.name || `Resource ${item.name}`,
            });
          }
        });
      }
      (filteredChips?.fetchedColumns || []).forEach((item: any) => {
        if (item.columns?.length > 0) {
          const tabKey = `fetchedColumns_${item.name}`;
          tabContent[tabKey] = (
            <>
              {item.columns.map((option: any) => (
                <Chip
                  key={option.name}
                  label={option.name}
                  onClick={() => handleColumnSelect(option.name, "resource")}
                />
              ))}
            </>
          );
          availableTabs.push({
            key: tabKey,
            label: item.name || `Resource ${item.name}`,
          });
        }
      });

      if (filteredChips.missingColumns?.length > 0) {
        tabContent.missingColumns = (
          <>
            {filteredChips.missingColumns.map((option: any) => (
              <Chip
                key={option.name}
                label={option.name}
                onClick={() => handleColumnSelect(option.name, "missing")}
              />
            ))}
          </>
        );
        availableTabs.push({ key: "missingColumns", label: "Missing Columns" });
      }

      if (filteredChips.mismatchedColumns?.length > 0) {
        tabContent.mismatchedColumns = (
          <>
            {filteredChips.mismatchedColumns.map((option: any) => (
              <Chip
                key={option.name}
                label={option.name}
                onClick={() => handleColumnSelect(option.name, "mismatched")}
              />
            ))}
          </>
        );
        availableTabs.push({
          key: "mismatchedColumns",
          label: "Mismatched Columns",
        });
      }

      // Mixed Dependency Columns Tabs
      (filteredChips?.mixedDependencyColumns || []).forEach((item: any) => {
        if (item.columns?.length > 0) {
          const tabKey = `mixedDependency_${item.name}`;
          tabContent[tabKey] = (
            <>
              {item.columns.map((option: any) => (
                <Chip
                  key={option.name}
                  label={option.name}
                  onClick={() => handleColumnSelect(option.name, item.name)}
                />
              ))}
            </>
          );
          availableTabs.push({
            key: tabKey,
            label: item.name || `Resource ${item.name}`,
          });
        }
      });

      // Mixed Query Data Tabs
      dialogType == "mixed" &&
        Object.entries(filteredChips.mixedQueryData || {}).forEach(
          ([resourceId, resourceData]: any) => {
            if (resourceData.columns?.length > 0) {
              const tabKey = `mixedQueryData_${resourceId}`;
              tabContent[tabKey] = (
                <>
                  {resourceData.columns.map((option: any) => (
                    <Chip
                      key={option.name}
                      label={option.name}
                      onClick={() =>
                        handleColumnSelect(option.name, "resource")
                      }
                    />
                  ))}
                </>
              );
              availableTabs.push({
                key: tabKey,
                label: resourceData.name || `Resource ${resourceId}`,
              });
            }
          }
        );

      // Dynamic keys
      dynamicKeys.forEach((key) => {
        if (filteredChips[key]?.length > 0) {
          tabContent[key] = (
            <>
              {filteredChips[key].map((option: any) => (
                <Chip
                  key={option.name}
                  label={option.name}
                  onClick={() => handleColumnSelect(option.name, key)}
                />
              ))}
            </>
          );
          availableTabs.push({ key, label: key.toUpperCase() });
        }
      });
    } else {
      // If not final query, show only dynamic keys
      dynamicKeys.forEach((key) => {
        if (filteredChips[key]?.length > 0) {
          tabContent[key] = (
            <>
              {filteredChips[key].map((option: any) => (
                <Chip
                  key={option.name}
                  label={option.name}
                  onClick={() => handleColumnSelect(option.name, key)}
                />
              ))}
            </>
          );
          availableTabs.push({ key, label: key.toUpperCase() });
        }
      });
    }
  } else if (queryBuilderType === "resource_query") {
    (filteredChips?.resourceColumns || []).forEach((item: any) => {
      if (item.columns?.length > 0) {
        const tabKey = `resource_${item.name}`;
        tabContent[tabKey] = (
          <>
            {item.columns.map((option: any) => (
              <Chip
                key={option.name}
                label={option.name}
                onClick={() => handleColumnSelect(option.name, "resource")}
              />
            ))}
          </>
        );
        availableTabs.push({
          key: tabKey,
          label: item.name || `Resource ${item.name}`,
        });
      }
    });
    (filteredChips?.mixedDependencyColumns || []).forEach((item: any) => {
      if (item.columns?.length > 0) {
        const tabKey = `mixedDependency_${item.name}`;
        tabContent[tabKey] = (
          <>
            {item.columns.map((option: any) => (
              <Chip
                key={option.name}
                label={option.name}
                onClick={() => handleColumnSelect(option.name, item.name)}
              />
            ))}
          </>
        );
        availableTabs.push({
          key: tabKey,
          label: item.name || `Resource ${item.name}`,
        });
      }
    });
  } else if (queryBuilderType === "external_query") {
    (filteredChips?.fetchedColumns || []).forEach((item: any) => {
      if (item.columns?.length > 0) {
        const tabKey = `fetchedColumns_${item.name}`;
        tabContent[tabKey] = (
          <>
            {item.columns.map((option: any) => (
              <Chip
                key={option.name}
                label={option.name}
                onClick={() => handleColumnSelect(option.name, "resource")}
              />
            ))}
          </>
        );
        availableTabs.push({
          key: tabKey,
          label: item.name || `Resource ${item.name}`,
        });
      }
    });
    (filteredChips?.mixedDependencyColumns || []).forEach((item: any) => {
      if (item.columns?.length > 0) {
        const tabKey = `mixedDependency_${item.name}`;
        tabContent[tabKey] = (
          <>
            {item.columns.map((option: any) => (
              <Chip
                key={option.name}
                label={option.name}
                onClick={() => handleColumnSelect(option.name, item?.name)}
              />
            ))}
          </>
        );
        availableTabs.push({
          key: tabKey,
          label: item.name || `Resource ${item.name}`,
        });
      }
    });
  } else if (queryBuilderType === "merged_query") {
    if (filteredChips.mergedColumns?.length > 0) {
      tabContent.mergedColumns = (
        <>
          {filteredChips.mergedColumns.map((option: any) => (
            <Chip
              key={option.name}
              label={option.name}
              onClick={() => handleColumnSelect(option.name, "merged")}
            />
          ))}
        </>
      );
      availableTabs.push({ key: "mergedColumns", label: "Merged Columns" });
    }
  } else if (queryBuilderType === "main_query") {
    Object.entries(filteredChips.mixedQueryData || {}).forEach(
      ([resourceId, resourceData]: any) => {
        if (resourceData.columns?.length > 0) {
          const tabKey = `mixedQueryData_${resourceId}`;
          tabContent[tabKey] = (
            <>
              {resourceData.columns.map((option: any) => (
                <Chip
                  key={option.name}
                  label={option.name}
                  onClick={() =>
                    handleColumnSelect(option.name, resourceData.name)
                  }
                />
              ))}
            </>
          );
          availableTabs.push({
            key: tabKey,
            label: resourceData.name || `Resource ${resourceId}`,
          });
        }
      }
    );
  }
  return (
    <Grid item xl={12} lg={12} md={12} sm={12}>
      <div className="columns-datagrid">
        <>
          <Tabs
            value={selectedTab}
            onChange={handleChange}
            textColor="secondary"
            indicatorColor="secondary"
            className="rs-query-tabs"
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              maxWidth: "100%",
              "& .MuiTabs-scroller": {
                overflow: "auto !important",
              },
              "& .MuiTabs-flexContainer": {
                flexWrap: "nowrap",
              },
            }}
          >
            {availableTabs.map((tab) => (
              <Tab
                key={tab.key}
                value={tab.key}
                label={
                  <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                    {tab.label}
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigator.clipboard.writeText(tab.label);
                      }}
                    >
                      <ContentCopyIcon sx={{ fontSize: 16, color: "white" }} />
                    </IconButton>
                  </Box>
                }
              />
            ))}
          </Tabs>
          {availableTabs.length > 0 && (
            <Box className="rs-query-tabs-content">
              {tabContent[selectedTab]}
            </Box>
          )}
        </>
      </div>
    </Grid>
  );
};

export default ResourceColumnsChips;
