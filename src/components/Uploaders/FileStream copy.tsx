import React, { useState } from 'react';

const FileStream: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setSelectedFile(event.target.files[0]);
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile) {
      alert('Please select a file before uploading.');
      return;
    }

    const fileStream = selectedFile.stream();
    const reader = fileStream.getReader();

    const CHUNK_SIZE = 1024 * 1024; // 1000KB chunks, adjust as needed

    const readChunk = async () => {
      const { done, value } = await reader.read();
      console.log('Actual chunk size:', value?.length);
      return { done, value };
    };

    let offset = 0;

    const uploadChunk = async () => {
      const chunk = await readChunk();

      if (chunk.done) {
        reader.releaseLock(); // Release the lock when done
        return;
      }

      // Check if chunk.value is not undefined before creating Blob
      if (chunk.value !== undefined) {
        console.log('Uploading chunk:', {
          size: chunk.value.length,
          offset,
          totalSize: selectedFile.size,
        });
        const formData = new FormData();
        formData.append('file', new Blob([chunk.value], { type: selectedFile.type }), selectedFile.name);
        formData.append('offset', offset.toString()); // Convert offset to string

        try {
          const response = await fetch('http://localhost:5000/upload-file/upload', {
            method: 'POST',
            body: formData,
          });

          if (response.ok) {
            const result = await response.json();
            console.log('Chunk uploaded successfully:', result);
            offset += CHUNK_SIZE;
            uploadChunk(); // Continue with the next chunk
          } else {
            console.error('Chunk upload failed:', response.statusText);
          }
        } catch (error) {
          console.error('Error uploading chunk:', error);
        }
      }
    };

    uploadChunk(); // Start the upload process
  };

  return (
    <div>
      <input type="file" onChange={handleFileChange} />
      <button onClick={handleFileUpload}>Upload File</button>
    </div>
  );
};

export default FileStream;