import { Box, LinearProgress, Typography } from "@mui/material";
import React, { useRef } from "react";
import { IconCloseSvgWhite } from "../../common/utils/icons";

const FileStreamUpload: any = (props: any) => {
  const {
    handleFileUpload,
    setSelectedFile,
    selectedFile,
    fileStreamIndex,
    isLoadingFile,
    uploadProgress,
    cancelUploadFile,
  } = props;
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setSelectedFile(event.target.files[0]);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };
  const handleCancelUpload = () => {
    if (cancelUploadFile) {
      cancelUploadFile.cancel();
    }
  };
  return (
    <>
      <div className="large-file-upload-button">
        <input
          type="file"
          name={`file-upload-${fileStreamIndex}`}
          ref={fileInputRef}
          id={`file-upload-${fileStreamIndex}`}
          onChange={handleFileChange}
          style={{ display: "none" }}
          className="inputfile"
        />
        <label
          htmlFor={`file-upload-${fileStreamIndex}`}
          className="file-upload"
        >
          <span>{selectedFile ? selectedFile.name : "Upload File..."}</span>
        </label>
        <button
          onClick={handleFileUpload}
          className="btn-no-border btn-orange btn-dark"
          disabled={isLoadingFile}
        >
          Upload File
        </button>
        {isLoadingFile && (
          <button
            onClick={handleCancelUpload}
            className="btn-no-border btn-orange btn-dark btn-close-white"
            disabled={!isLoadingFile}
          >
            <IconCloseSvgWhite />
          </button>
        )}
      </div>

      {isLoadingFile && (
        <>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Box sx={{ width: "100%", mr: 1 }}>
              <LinearProgress
                sx={{
                  backgroundColor: "rgba(0, 0, 0, .2)",
                  height: "6px",
                  borderRadius: "3px",
                  "& .MuiLinearProgress-bar": {
                    backgroundColor: "#f48400",
                    //transform: "translateX(-50%) !important",
                  },
                }}
                variant="determinate"
                value={uploadProgress}
              />
            </Box>
            <Box sx={{ minWidth: 35, textAlign: "right" }}>
              <Typography variant="body2" color="text.secondary">{`${Math.round(
                uploadProgress
              )}%`}</Typography>
            </Box>
          </Box>
        </>
      )}
    </>
  );
};

export default FileStreamUpload;
