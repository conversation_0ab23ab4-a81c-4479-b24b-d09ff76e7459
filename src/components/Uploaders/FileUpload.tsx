import React, { useRef, useState, ChangeEvent } from "react";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@mui/material";
import uploadIcon from "../../assets/svgs/icon-upload-white.svg";
import ConfirmDailog from "../Dialogs/ConfirmDailog";
import { IconUploadBase } from "../../common/utils/icons";

interface FileUploadButtonProps {
  onFileUpload: (file: File) => void;
  sx?: any;
  className?: string;
  isFileUpload?: boolean;
  fileData?: any;
}

function FileUploadButton({
  onFileUpload,
  sx,
  className,
  isFileUpload,
  fileData,
}: FileUploadButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isDailogOpen, setIsDailogOpen] = useState(false);

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  const handleCancelUpload = () => {
    setIsDailogOpen(false);
    setSelectedFile(null);
  };
  const handleConfirmUpload = () => {
    setIsDailogOpen(false);
    uploadFile(selectedFile as File);
  };

  const handleFileInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0];
    if (file) {
      setSelectedFile(file);
      const hasReservedKey =
        fileData.length === 1 &&
        fileData.some(
          (fileItem: any) => fileItem.column_name.toLowerCase() === "file_name"
        );

      if (fileData && fileData.length > 0 && !hasReservedKey) {
        setIsDailogOpen(true);
      } else {
        uploadFile(file);
      }
    }
  };

  const uploadFile = async (file: File) => {
    try {
      setIsLoading(true);
      // Simulate file upload delay with setTimeout
      await new Promise((resolve) => setTimeout(resolve, 2000));
      onFileUpload(file);
    } catch (error) {
      console.error("Error uploading file:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Box sx={sx} className={className}>
        <input
          ref={fileInputRef}
          type="file"
          style={{ display: "none" }}
          onChange={handleFileInputChange}
          onClick={(event: any) => {
            event.target.value = null;
          }}
        />
        <Button
          sx={{
            height: "50px",
            "&:hover": {
              backgroundColor: "transparent",
            },
          }}
          variant="contained"
          onClick={handleButtonClick}
        >
          <input
            style={{ border: "none" }}
            value={
              selectedFile && !isFileUpload
                ? selectedFile.name
                : "Upload File..."
            }
            type="text"
            disabled
          />{" "}
          {isLoading ? (
            <CircularProgress className="upload-loader" size={24} />
          ) : (
            <Box className="img-icon">
              <IconUploadBase />
            </Box>
          )}
        </Button>
      </Box>
      <ConfirmDailog
        isDailogOpen={isDailogOpen}
        handleCancelAction={handleCancelUpload}
        handleConfirmAction={handleConfirmUpload}
        dailogTitle={"Confirm Upload"}
        dailogDescription={
          "File upload will replace existing data, still want to continue?"
        }
      />
    </>
  );
}

export default FileUploadButton;
