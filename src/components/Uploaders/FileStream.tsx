import React, { useState, ChangeEvent } from 'react';
import axios from 'axios';

const FileUploadComponent: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    const chunkSize = 1024 * 1024; // 1 MB chunks
    const totalChunks = Math.ceil(file.size / chunkSize);

    const reader = new FileReader();

    for (let currentChunk = 0; currentChunk < totalChunks; currentChunk++) {
      const start = currentChunk * chunkSize;
      const end = Math.min(start + chunkSize, file.size);

      const chunk = file.slice(start, end);

      reader.readAsArrayBuffer(chunk);

      
      await new Promise<void>((resolve) => {
        reader.onload = () => {
          const data = new Uint8Array(reader.result as ArrayBuffer);
          const formData = new FormData();
          formData.append('chunk', new Blob([data]));

          // formData.append('chunk', data);
          formData.append('currentChunk', currentChunk.toString());
          formData.append('totalChunks', totalChunks.toString());
          formData.append('filename', file.name);

          axios.post('http://172.176.208.22/config/upload-file/upload', formData)
            .then((response) => {
              console.log(response.data);
              resolve();
            })
            .catch((error) => {
              console.error(error);
              resolve();
            });
        };
      });
    }

    axios.post('http://172.176.208.22/config/upload-file/upload', { filename: file.name })
      .then((response) => {
        console.log(response.data);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  return (
    <div>
      <input type="file" onChange={handleFileChange} />
      <button onClick={handleUpload}>Upload</button>
    </div>
  );
};

export default FileUploadComponent;
