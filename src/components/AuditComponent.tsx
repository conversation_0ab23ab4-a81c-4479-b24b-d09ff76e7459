import React, { useState } from "react";
import { Box, Grid, Button } from "@mui/material";
import Loader from "./Molecules/Loader/Loader";
import FlexBetween from "./FlexBetween";
import DataTable from "./DataGrids/DataGrid";
import DiffAceEditorDialog from "./Dialogs/DiffAceEditorDialog";
import { toast } from "react-toastify";
import { useToast } from "../services/utils";

interface IAuditComponent {
  auditColumnData: any;
  auditRowData: any;
  isLoading: boolean;
  selectedRows: any[];
  setSelectedRows: React.Dispatch<React.SetStateAction<any[]>>;
  toggleSelection: (row: any) => void;
  dataListTitle: string;
  page: number;
  setPage: React.Dispatch<React.SetStateAction<number>>;
  pSize: number;
  setPSize: React.Dispatch<React.SetStateAction<number>>;
  openDialog: boolean;
  setOpenDialog: React.Dispatch<React.SetStateAction<boolean>>;
  auditSource: string;
  setAuditSource: React.Dispatch<React.SetStateAction<string>>;
  singlePageMaxHeightDiff?: number;
}

const AuditComponent: React.FC<IAuditComponent> = ({
  auditColumnData,
  auditRowData,
  isLoading,
  selectedRows,
  setSelectedRows,
  toggleSelection,
  dataListTitle,
  page,
  setPage,
  pSize,
  setPSize,
  openDialog,
  setOpenDialog,
  auditSource,
  setAuditSource,
  singlePageMaxHeightDiff,
}) => {
  const { showToast } = useToast();
  const handleCompare = () => {
    if (selectedRows.length !== 2) {
      showToast(`please select at least 2 items`, "warning");
      return;
    }
    setOpenDialog(true);
  };
  const handleDialogClose = () => {
    if (auditSource === "currentCompare") {
      setSelectedRows((prev: any) => prev.slice(0, -1));
      setAuditSource("");
    }
    setOpenDialog(false);
  };
  return (
    <Box>
      <Loader isLoading={isLoading} />
      <Box className="">
        <Grid>
          <Grid container rowSpacing={2.5} columnSpacing={4}>
            <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
              <Box
                display="flex"
                justifyContent="flex-end"
                sx={{
                  columnGap: { md: "20px", sm: "0", xs: "0px" },
                  rowGap: {
                    xl: "0",
                    lg: "0",
                    md: "20",
                    sm: "20px",
                    xs: "20px",
                  },
                  flexDirection: {
                    xl: "row",
                    lg: "row",
                    md: "row",
                    sm: "column",
                    xs: "column",
                  },
                }}
              >
                <Button
                  className="btn-orange"
                  variant="contained"
                  color={"secondary"}
                  onClick={() => {
                    handleCompare();
                  }}
                  sx={{ margin: "0px" }}
                >
                  Compare
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      <FlexBetween>
        <DataTable
          dataColumns={auditColumnData}
          dataRows={
            auditRowData?.items
              ? auditRowData?.items.map((row: any) => ({
                  ...row,
                  checkbox: { toggleSelection },
                }))
              : []
          }
          loading={isLoading}
          dataListTitle={dataListTitle || ""}
          className="dataTable no-radius hide-progress-icon"
          paginationMode="server"
          isGetDetailPanelRequired={false}
          rowCount={auditRowData?.total || 0}
          pageSizeOptions={[25]}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={singlePageMaxHeightDiff}
        />
      </FlexBetween>
      <DiffAceEditorDialog
        openDialog={openDialog}
        value={selectedRows}
        handleDialogClose={handleDialogClose}
      />
    </Box>
  );
};

export default AuditComponent;
