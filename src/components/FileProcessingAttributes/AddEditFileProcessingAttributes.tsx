import {
  Checkbox,
  FormGroup,
  Box,
  Button,
  Grid,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useToast } from "../../services/utils";
import ButtonComponent from "../Button.Component";
import { addEditFileProcessingSchema } from "../../schemas";
import useFetchFileProcessingById from "../../hooks/useFetchFileProcessingById";
import {
  addFileProcessing,
  updateFileProcessing,
} from "../../services/fileProcessingService";
import Loader from "../Molecules/Loader/Loader";
import FlexBetween from "../FlexBetween";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";

const AddEditFileProcessingAttributes: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { id: fileProcessingId } = useParams();
  const { showToast } = useToast();
  const [formData, setFormData] = useState<any>({
    resource_type: null,
    file_processing_attributes: {
      file_format: null,
      schema_definition: null,
      field_delimiter: null,
      custom_attributes: {
        type: null,
        store_name: null,
        store_label: null,
      },
      has_footer: true,
      footer_lines: 0,
      has_header: true,
      header_lines: 0,
      skip_rows: 0,
      has_multiple_sheets: false,
      skip_blanks: true,
      compression: null,
      compression_codec: null,
      line_terminator: null,
      has_comments: null,
      comments_marker: null,
      encoding: null,
      bad_lines: null,
    },
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // hooks
  const [fileProcessingData] = useFetchFileProcessingById({
    setIsLoading,
    currentFileProcessingId: fileProcessingId,
  });

  const handleFormData = (e: any) => {
    if (e.target.name === "resource_type" || e.target.name === "code") {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
      validateField(e.target.name, e.target.value);
    } else if (e.target.getAttribute("type") === "checkbox") {
      setFormData({
        ...formData,
        file_processing_attributes: {
          ...formData.file_processing_attributes,
          [e.target.name]: e.target.checked,
        },
      });
    } else if (["type", "store_label", "store_name"].includes(e.target.name)) {
      setFormData({
        ...formData,
        file_processing_attributes: {
          ...formData.file_processing_attributes,
          custom_attributes: {
            ...formData.file_processing_attributes.custom_attributes,
            [e.target.name]: e.target.value,
          },
        },
      });
    } else {
      setFormData({
        ...formData,
        file_processing_attributes: {
          ...formData.file_processing_attributes,
          [e.target.name]: e.target.value,
        },
      });
    }
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addEditFileProcessingSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  // handel event when save a file processing
  const onSaveFileProcessingAttributes = async (e: any) => {
    const serviceAction = fileProcessingId
      ? updateFileProcessing
      : addFileProcessing;
    e.preventDefault();
    try {
      await addEditFileProcessingSchema.validate(
        {
          ...formData,
          ...formData.file_processing_attributes,
          ...formData.file_processing_attributes.custom_attributes,
        },
        {
          abortEarly: false,
        }
      );
      const reqBody = { ...formData };
      serviceAction({
        currentFileProcessingId: fileProcessingId,
        payload: reqBody,
      })
        .then((response) => {
          if (response) {
            showToast(
              `File processing ${
                fileProcessingId ? "updated" : "created"
              } successfully!`,
              "success"
            );
            navigate("/file-processing-attributes");
          }
        })
        .catch((error) => {
          console.error(
            `Cannot ${fileProcessingId ? "update" : "create"} file processing`
          );
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  useEffect(() => {
    if (fileProcessingData?.resource_type) {
      setFormData({
        ...fileProcessingData,
      });
      setInitialFormData(JSON.parse(JSON.stringify(fileProcessingData)));
    }
    setCurrentBreadcrumbPage({
      name: fileProcessingData?.resource_type,
      id: fileProcessingData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [fileProcessingData]);

  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);

    setHasChanges(formChanged);
  }, [formData, initialFormData]);
  return (
    <>
      <Loader isLoading={isLoading} />
      <Box>
        <form onSubmit={onSaveFileProcessingAttributes} autoComplete="off">
          <Box className="text-box-card-white m-0">
            <Box className="text-box-header ">
              <h3>
                {fileProcessingId ? "Edit " : "Add "}
                File Processing Attributes
              </h3>
            </Box>
            <Grid>
              <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Resource Type <span className="required-asterisk">*</span>
                  </label>
                  <TextField
                    type="text"
                    // placeholder="Ex: resource_type"
                    name="resource_type"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.resource_type ? "has-error" : ""
                    }`}
                    onBlur={(e) => validateField(e.target.name, e.target.value)}
                    error={!!errors?.resource_type}
                    helperText={errors?.resource_type || ""}
                    value={formData?.resource_type}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Code <span className="required-asterisk">*</span>
                  </label>
                  <TextField
                    type="text"
                    name="code"
                    onChange={(e: any) => {
                      if (!fileProcessingData?.code) {
                        handleFormData(e);
                      }
                    }}
                    className={`form-control ${
                      errors?.code ? "has-error" : ""
                    }`}
                    onBlur={(e) => {
                      if (!fileProcessingData?.code) {
                        validateField(e.target.name, e.target.value);
                      }
                    }}
                    error={!!errors?.code}
                    helperText={errors?.code || ""}
                    value={formData?.code}
                    disabled={fileProcessingData?.code ? true : false}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">File Format</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: file_format"
                    name="file_format"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.file_format ? "has-error" : ""
                    }`}
                    error={!!errors?.file_format}
                    helperText={errors?.file_format || ""}
                    value={formData?.file_processing_attributes?.file_format}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Schema Definition</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: schema_definition"
                    name="schema_definition"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.schema_definition ? "has-error" : ""
                    }`}
                    error={!!errors?.schema_definition}
                    helperText={errors?.schema_definition || ""}
                    value={
                      formData?.file_processing_attributes?.schema_definition
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Field Delimiter</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: field_delimiter"
                    name="field_delimiter"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.field_delimiter ? "has-error" : ""
                    }`}
                    error={!!errors?.field_delimiter}
                    helperText={errors?.field_delimiter || ""}
                    value={
                      formData?.file_processing_attributes?.field_delimiter
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Custom Attributes Type</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: custom_attributes_type"
                    name="type"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.type ? "has-error" : ""
                    }`}
                    error={!!errors?.type}
                    helperText={errors?.type || ""}
                    value={
                      formData?.file_processing_attributes?.custom_attributes
                        ?.type
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Custom Attributes Store Name
                  </label>
                  <TextField
                    type="text"
                    // placeholder="Ex: custom_attributes_store_name"
                    name="store_name"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.store_name ? "has-error" : ""
                    }`}
                    error={!!errors?.store_name}
                    helperText={errors?.store_name || ""}
                    value={
                      formData?.file_processing_attributes?.custom_attributes
                        ?.store_name
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Custom Attributes Store Label
                  </label>
                  <TextField
                    type="text"
                    // placeholder="Ex: custom_attributes_store_label"
                    name="store_label"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.store_label ? "has-error" : ""
                    }`}
                    error={!!errors?.store_label}
                    helperText={errors?.store_label || ""}
                    value={
                      formData?.file_processing_attributes?.custom_attributes
                        ?.store_label
                    }
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Skip Rows</label>
                  <TextField
                    type="number"
                    // placeholder="Ex: skip_rows"
                    name="skip_rows"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.skip_rows ? "has-error" : ""
                    }`}
                    error={!!errors?.skip_rows}
                    helperText={errors?.skip_rows || ""}
                    value={formData?.file_processing_attributes?.skip_rows}
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Compression</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: compression"
                    name="compression"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.compression ? "has-error" : ""
                    }`}
                    error={!!errors?.compression}
                    helperText={errors?.compression || ""}
                    value={formData?.file_processing_attributes?.compression}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Compression Codec</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: compression_codec"
                    name="compression_codec"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.compression_codec ? "has-error" : ""
                    }`}
                    error={!!errors?.compression_codec}
                    helperText={errors?.compression_codec || ""}
                    value={
                      formData?.file_processing_attributes?.compression_codec
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Line Terminator</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: line_terminator"
                    name="line_terminator"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.line_terminator ? "has-error" : ""
                    }`}
                    error={!!errors?.line_terminator}
                    helperText={errors?.line_terminator || ""}
                    value={
                      formData?.file_processing_attributes?.line_terminator
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Has Comments</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: has_comments"
                    name="has_comments"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.has_comments ? "has-error" : ""
                    }`}
                    error={!!errors?.has_comments}
                    helperText={errors?.has_comments || ""}
                    value={formData?.file_processing_attributes?.has_comments}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Comments Marker</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: comments_marker"
                    name="comments_marker"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.comments_marker ? "has-error" : ""
                    }`}
                    error={!!errors?.comments_marker}
                    helperText={errors?.comments_marker || ""}
                    value={
                      formData?.file_processing_attributes?.comments_marker
                    }
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Encoding</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: encoding"
                    name="encoding"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.encoding ? "has-error" : ""
                    }`}
                    error={!!errors?.encoding}
                    helperText={errors?.encoding || ""}
                    value={formData?.file_processing_attributes?.encoding}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">Bad Lines</label>
                  <TextField
                    type="text"
                    // placeholder="Ex: bad_lines"
                    name="bad_lines"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.bad_lines ? "has-error" : ""
                    }`}
                    error={!!errors?.bad_lines}
                    helperText={errors?.bad_lines || ""}
                    value={formData?.file_processing_attributes?.bad_lines}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
                  <label className="label-text">&nbsp;</label>
                  <Grid container columnGap={2}>
                    <Grid item>
                      <label
                        className="base-resource-checkbox"
                        style={{ marginTop: "8px" }}
                      >
                        <Checkbox
                          title="Has Header"
                          name="has_header"
                          className="refrence-checkbox"
                          checked={
                            formData?.file_processing_attributes?.has_header
                          }
                          onChange={handleFormData}
                          sx={{
                            "&.Mui-checked": {
                              color: "#FFA500",
                            },
                          }}
                        />
                        <span>Has Header:</span>
                      </label>
                    </Grid>
                    <Grid item>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <FormGroup
                          row
                          className={`form-group-row ${
                            !formData?.file_processing_attributes?.has_header &&
                            "disabled-row"
                          }`}
                        >
                          <Button variant="contained" disableElevation>
                            <span>Header Lines</span>
                          </Button>
                          <TextField
                            type="number"
                            // placeholder="Ex: 5"
                            name="header_lines"
                            className={`form-control`}
                            value={
                              formData?.file_processing_attributes?.header_lines
                            }
                            onChange={handleFormData}
                            sx={{
                              maxWidth: {
                                xs: "90px",
                                sm: "90px",
                                md: "90px",
                                lg: "90px",
                                xl: "150px",
                              },
                            }}
                            disabled={
                              !formData?.file_processing_attributes?.has_header
                            }
                          />
                        </FormGroup>
                      </Box>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
                  <label className="label-text">&nbsp;</label>
                  <Grid container columnGap={2}>
                    <Grid item>
                      <label
                        className="base-resource-checkbox"
                        style={{ marginTop: "8px" }}
                      >
                        <Checkbox
                          title="Has Footer"
                          name="has_footer"
                          className="refrence-checkbox"
                          checked={
                            formData?.file_processing_attributes?.has_footer
                          }
                          onChange={handleFormData}
                          sx={{
                            "&.Mui-checked": {
                              color: "#FFA500",
                            },
                          }}
                        />
                        <span>Has Footer:</span>
                      </label>
                    </Grid>
                    <Grid item style={{ display: "block" }}>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <FormGroup
                          row
                          className={`form-group-row ${
                            !formData?.file_processing_attributes?.has_footer &&
                            "disabled-row"
                          }`}
                        >
                          <Button variant="contained" disableElevation>
                            <span>Footer Lines</span>
                          </Button>

                          <TextField
                            type="number"
                            // placeholder="Ex: 5"s
                            name="footer_lines"
                            className={`form-control`}
                            onChange={handleFormData}
                            sx={{
                              maxWidth: {
                                xs: "90px",
                                sm: "90px",
                                md: "90px",
                                lg: "90px",
                                xl: "150px",
                              },
                            }}
                            value={
                              formData?.file_processing_attributes?.footer_lines
                            }
                            disabled={
                              !formData?.file_processing_attributes?.has_footer
                            }
                          />
                        </FormGroup>
                      </Box>
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
                  <Grid
                    container
                    alignItems={"center"}
                    display={"flex"}
                    columnGap={2}
                  >
                    <Grid item>
                      <label
                        className="base-resource-checkbox"
                        style={{ marginTop: "8px" }}
                      >
                        <Checkbox
                          title="has_multiple_sheets"
                          name="has_multiple_sheets"
                          className="refrence-checkbox"
                          checked={
                            formData?.file_processing_attributes
                              ?.has_multiple_sheets
                          }
                          onChange={handleFormData}
                          sx={{
                            "&.Mui-checked": {
                              color: "#FFA500",
                            },
                          }}
                        />
                        <span>Has Multiple Sheets</span>
                      </label>
                    </Grid>
                    <Grid item>
                      <label
                        className="base-resource-checkbox"
                        style={{ marginTop: "8px" }}
                      >
                        <Checkbox
                          title="skip_blanks"
                          name="skip_blanks"
                          className="refrence-checkbox"
                          checked={
                            formData?.file_processing_attributes?.skip_blanks
                          }
                          onChange={handleFormData}
                          sx={{
                            "&.Mui-checked": {
                              color: "#FFA500",
                            },
                          }}
                        />
                        <span>Skip Blanks</span>
                      </label>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Box>
            <FlexBetween
              gap="3rem"
              sx={{
                marginTop: "6px",
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <ButtonComponent
                className="btn-orange"
                handelClickEvent={onSaveFileProcessingAttributes}
                disabled={!hasChanges}
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </ButtonComponent>
            </FlexBetween>
          </Box>
        </form>
      </Box>
    </>
  );
};

export default AddEditFileProcessingAttributes;
