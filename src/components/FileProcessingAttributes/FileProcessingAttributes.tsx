import React, { useEffect, useMemo, useState } from "react";
import {
  Stack,
  Box,
  Grid,
  IconButton,
  InputBase,
  Tooltip,
  TextField,
  Button,
} from "@mui/material";
import FlexBetween from "../../components/FlexBetween";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import { useToast } from "../../services/utils";
import { useNavigate } from "react-router-dom";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { Search } from "@mui/icons-material";
import iconDelete from "../../assets/svgs/icon-delete-orange.svg";
import iconEdit from "../../assets/svgs/icon-edit-orange.svg";
import iconEye from "../../assets/svgs/icon-eye-orange.svg";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import useFetchFileProcessing from "../../hooks/useFetchFileProcessing";
import ButtonComponent from "../Button.Component";
import { deleteFileProcessing } from "../../services/fileProcessingService";
import {
  downloadFileProcessingAttributesExportFile,
  getFormattedDateTime,
} from "../../services/utils";
import DropdownMenu from "../Molecules/DropdownMenu/DropdownMenu";
import {
  IconDeleteBlueSvg,
  IconEditBase,
  IconExportIconBlue,
  IconEyeBase,
  IconImportFileWhite,
} from "../../common/utils/icons";
import Loader from "../Molecules/Loader/Loader";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const FileProcessingAttributes: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  // states
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any[]>([]);
  const [fileProcessingData, setFileProcessingData] = useState<any[]>([]);
  // hooks
  const [fetchedFileProcessings] = useFetchFileProcessing({ setIsLoading });
  const [searchQuery, setSearchQuery] = useState("");

  const handelClickEvent = () => {
    navigate("/file-processing-attributes/add");
  };

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
      renderCell: (params: any) => <></>,
    },
    {
      field: "id",
      headerName: "Id",
      minWidth: 80,
      flex: 1,
      renderCell: (params: any) => {
        return <span>{params.id}</span>;
      },
    },
    {
      field: "resource_type",
      headerName: "Resource",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "updated_on",
      headerName: "Last Modified",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return getFormattedDateTime(params?.value);
      },
    },

    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 170,
      renderCell: (params: any) => {
        const handleDelete = async () => {
          setIsLoading(true);
          setFileData((prev) =>
            prev.filter((item) => item.id !== params.row.id)
          );
          await deleteFileProcessing(params.row.id);
          const updatedFileProcessingData = fileData.filter(
            (file_processing) => file_processing.id !== params.row.id
          );
          setFileData(updatedFileProcessingData);
          setFileProcessingData(updatedFileProcessingData);
          setIsLoading(false);
          showToast("File processing deleted successfully", "success");
        };
        return (
          <>
            <Tooltip title="View FileProcessing" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  navigate(`/file-processing-attributes/view/${params.row.id}`);
                }}
              >
                <IconEyeBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit FileProcessing" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  navigate(`/file-processing-attributes/edit/${params.row.id}`);
                }}
              >
                <IconEditBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete FileProcessing" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => handleDelete()}
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </Tooltip>
            {/* <DropdownMenu
              dropdownMenuItems={[
                <IconButton
                  key="export"
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    downloadFileProcessingAttributesExportFile(
                      params.row.id,
                      setIsDownloadLoading
                    )
                  }
                >
                  <IconExportIconBlue />
                  Export
                </IconButton>,
              ]}
            /> */}
          </>
        );
      },
    },
  ];

  // for update file processing attributes list data
  useEffect(() => {
    if (fetchedFileProcessings?.length) {
      setFileData(fetchedFileProcessings);
      setFileProcessingData(fetchedFileProcessings);
    }
  }, [fetchedFileProcessings]);

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    // Filter file processing attributes based on the searchQuery
    const filteredFileProcessings = fileProcessingData.filter(
      (fileProcessing) => {
        return fileProcessing.resource_type
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
      }
    );
    //You can set the filtered file processing attributes as a result in your component state
    setFileData(filteredFileProcessings);
  };
  const filteredData = useMemo(() => {
    return fileData?.filter((item: any, index: number) => {
      return item.resource_type
        .toLowerCase()
        .includes(searchQuery?.toLowerCase());
    });
  }, [fileData, searchQuery]);

  return (
    <>
      <Loader isLoading={isDownloadLoading} />
      <Box className="text-box-card list-page-card compact-text-box-card">
        <Grid container rowSpacing={2.5} columnSpacing={4}>
          <Grid item xs={12} sm={12} md={6} lg={3} xl={3}>
            <label className="label-text">
              Search File Processing Attributes
            </label>
            <form onSubmit={handleSearchSubmit} className="common-search-panel">
              <InputBase
                placeholder="Search..."
                className="search-textbox"
                value={searchQuery}
                onChange={handleSearchInputChange}
                onKeyDown={(event) => {
                  if (event.key === "Enter") {
                    event.preventDefault();
                  }
                }}
              />
              <IconButton className="search-icon" type="submit">
                <Search className="svg_icons" />
              </IconButton>
            </form>
          </Grid>
          <Grid item xs>
            <label className="label-text">&nbsp;</label>
            <Box
              display="flex"
              justifyContent="flex-end"
              sx={{
                columnGap: { md: "20px", sm: "0", xs: "0px" },
                rowGap: {
                  xl: "0",
                  lg: "0",
                  md: "20",
                  sm: "20px",
                  xs: "20px",
                },
                flexDirection: {
                  xl: "row",
                  lg: "row",
                  md: "row",
                  sm: "column",
                  xs: "column",
                },
              }}
            >
              {/* <Button
                onClick={() =>
                  navigate(`/file-processing-attributes/import-entity`, {
                    state: {
                      import_defination_name: "File Processing Attributes",
                    },
                  })
                }
                className="btn-orange btn-dark"
                sx={{ columnGap: 1 }}
              >
                <IconImportFileWhite />
                Import
              </Button> */}
              <ButtonComponent
                className="btn-orange"
                handelClickEvent={handelClickEvent}
              >
                <AddSharpIcon sx={{ marginRight: "4px" }} /> File Processing
                Attributes
              </ButtonComponent>
            </Box>
          </Grid>
        </Grid>
      </Box>
      <FlexBetween>
        <DataTable
          dataColumns={columns}
          dataRows={filteredData}
          loading={isLoading}
          dataListTitle={"File Processing Attributes List"}
          className="dataTable no-radius"
          singlePageMaxHeightDiff={378}
        />
      </FlexBetween>
    </>
  );
};

export default FileProcessingAttributes;
