import {
  Checkbox,
  FormGroup,
  Box,
  Button,
  Grid,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useToast } from "../../services/utils";
import ButtonComponent from "../Button.Component";
import { addEditFileProcessingSchema } from "../../schemas";
import useFetchFileProcessingById from "../../hooks/useFetchFileProcessingById";
import {
  addFileProcessing,
  updateFileProcessing,
} from "../../services/fileProcessingService";
import Loader from "../Molecules/Loader/Loader";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { IconBtnEditBase } from "../../common/utils/icons";

const ViewFileProcessingAttributes: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { id: fileProcessingId } = useParams();
  const [formData, setFormData] = useState<any>({
    resource_type: "",
    file_processing_attributes: {
      file_format: "",
      schema_definition: "",
      field_delimiter: "",
      custom_attributes: {
        type: "",
        store_name: "",
        store_label: "",
      },
      has_footer: true,
      footer_lines: 0,
      has_header: true,
      header_lines: 0,
      skip_rows: 0,
      has_multiple_sheets: true,
      skip_blanks: true,
      compression: "",
      compression_codec: "",
      line_terminator: "",
      has_comments: "",
      comments_marker: "",
      encoding: "",
      bad_lines: "",
    },
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isHasHeader, setIsHasHeader] = useState<boolean>(false);
  const [isHasFooter, setIsHasFooter] = useState<boolean>(false);

  // hooks
  const [fileProcessingData] = useFetchFileProcessingById({
    setIsLoading,
    currentFileProcessingId: fileProcessingId,
  });

  const handleFormData = (e: any) => {
    if (e.target.name === "resource_type") {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value,
      });
    } else if (e.target.getAttribute("type") === "checkbox") {
      setFormData({
        ...formData,
        file_processing_attributes: {
          ...formData.file_processing_attributes,
          [e.target.name]: e.target.checked,
        },
      });
    } else if (["type", "store_label", "store_name"].includes(e.target.name)) {
      setFormData({
        ...formData,
        file_processing_attributes: {
          ...formData.file_processing_attributes,
          custom_attributes: {
            ...formData.file_processing_attributes.custom_attributes,
            [e.target.name]: e.target.value,
          },
        },
      });
    } else {
      setFormData({
        ...formData,
        file_processing_attributes: {
          ...formData.file_processing_attributes,
          [e.target.name]: e.target.value,
        },
      });
    }

    validateField(e.target.name, e.target.value);
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addEditFileProcessingSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  // handel event when save a file processing
  const onSaveFileProcessingAttributes = async (e: any) => {
    const serviceAction = fileProcessingId
      ? updateFileProcessing
      : addFileProcessing;
    e.preventDefault();
    try {
      await addEditFileProcessingSchema.validate(
        {
          ...formData,
          ...formData.file_processing_attributes,
          ...formData.file_processing_attributes.custom_attributes,
        },
        {
          abortEarly: false,
        }
      );
      const reqBody = { ...formData };
      serviceAction({
        currentFileProcessingId: fileProcessingId,
        payload: reqBody,
      })
        .then((response) => {
          showToast(
            `File processing ${
              fileProcessingId ? "updated" : "created"
            } successfully!`,
            "success"
          );
          navigate("/file-processing-attributes");
        })
        .catch((error) => {
          console.error(
            `Cannot ${fileProcessingId ? "update" : "create"} file processing`
          );
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  useEffect(() => {
    if (fileProcessingData?.resource_type) {
      setFormData({
        ...fileProcessingData,
      });
    }
    setCurrentBreadcrumbPage({
      name: fileProcessingData?.resource_type,
      id: fileProcessingData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [fileProcessingData]);
  return (
    <>
      {isLoading ? (
        <Loader isLoading={isLoading} />
      ) : (
        <Box>
          <form onSubmit={onSaveFileProcessingAttributes} autoComplete="off">
            <Box className="text-box-card-white compact-text-box-card">
              <Box className="text-box-header">
                <h3>File Processing Attributes Details</h3>
                <button
                  className="btn-nostyle icon-btn-edit"
                  onClick={() =>
                    navigate(
                      `/file-processing-attributes/edit/${fileProcessingId}`
                    )
                  }
                >
                  <IconBtnEditBase />
                </button>
              </Box>
              <Grid>
                <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Resource Type</label>
                    <div className="form-control">
                      {formData?.resource_type}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">File Format</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.file_format}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Schema Definition</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.schema_definition}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Field Delimiter</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.field_delimiter}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Custom Attributes Type</label>
                    <div className="form-control">
                      {
                        formData?.file_processing_attributes?.custom_attributes
                          ?.type
                      }
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">
                      Custom Attributes Store Name
                    </label>
                    <div className="form-control">
                      {
                        formData?.file_processing_attributes?.custom_attributes
                          ?.store_name
                      }
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">
                      Custom Attributes Store Label
                    </label>
                    <div className="form-control">
                      {
                        formData?.file_processing_attributes?.custom_attributes
                          ?.store_label
                      }
                    </div>
                  </Grid>

                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Skip Rows</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.skip_rows}
                    </div>
                  </Grid>

                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Compression</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.compression}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Compression Codec</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.compression_codec}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Line Terminator</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.line_terminator}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Has Comments</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.has_comments}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Comments Marker</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.comments_marker}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Encoding</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.encoding}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Bad Lines</label>
                    <div className="form-control">
                      {formData?.file_processing_attributes?.bad_lines}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">Code</label>
                    <div className="form-control">
                      {formData?.code ?? "N/A"}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
                    <Grid container columnGap={2}>
                      <Grid item>
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <FormGroup
                            row
                            className={`form-group-row ${
                              !formData?.file_processing_attributes
                                ?.has_header && "disabled-row"
                            }`}
                          >
                            <Button variant="contained" disableElevation>
                              <span>Header Lines</span>
                            </Button>
                            <TextField
                              type="number"
                              // placeholder="Ex: 5"
                              name="header_lines"
                              className="form-control"
                              value={
                                formData?.file_processing_attributes
                                  ?.header_lines
                              }
                              disabled
                              sx={{
                                maxWidth: {
                                  xs: "90px",
                                  sm: "90px",
                                  md: "90px",
                                  lg: "90px",
                                  xl: "150px",
                                },
                              }}
                            />
                          </FormGroup>
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
                    <Grid container columnGap={2}>
                      <Grid item style={{ display: "block" }}>
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <FormGroup
                            row
                            className={`form-group-row ${
                              !formData?.file_processing_attributes
                                ?.has_footer && "disabled-row"
                            }`}
                          >
                            <Button variant="contained" disableElevation>
                              <span>Footer Lines</span>
                            </Button>

                            <TextField
                              type="number"
                              // placeholder="Ex: 5"
                              name="footer_lines"
                              className="form-control"
                              sx={{
                                maxWidth: {
                                  xs: "90px",
                                  sm: "90px",
                                  md: "90px",
                                  lg: "90px",
                                  xl: "150px",
                                },
                              }}
                              value={
                                formData?.file_processing_attributes
                                  ?.footer_lines
                              }
                              disabled
                            />
                          </FormGroup>
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>

                  <Grid item xs={12} sm={12} md={6} lg={4} xl={4}>
                    <Grid
                      container
                      alignItems={"center"}
                      display={"flex"}
                      columnGap={2}
                    >
                      <Grid item>
                        <label
                          className="base-resource-checkbox"
                          style={{ marginTop: "8px" }}
                        >
                          <Checkbox
                            title="has_multiple_sheets"
                            name="has_multiple_sheets"
                            className="refrence-checkbox"
                            checked={
                              formData?.file_processing_attributes
                                ?.has_multiple_sheets
                            }
                            disabled
                            sx={{
                              "&.Mui-checked": {
                                color: "#FFA500",
                              },
                            }}
                          />
                          <span>Has Multiple Sheets</span>
                        </label>
                      </Grid>
                      <Grid item>
                        <label
                          className="base-resource-checkbox"
                          style={{ marginTop: "8px" }}
                        >
                          <Checkbox
                            title="skip_blanks"
                            name="skip_blanks"
                            className="refrence-checkbox"
                            checked={
                              formData?.file_processing_attributes?.skip_blanks
                            }
                            disabled
                            sx={{
                              "&.Mui-checked": {
                                color: "#FFA500",
                              },
                            }}
                          />
                          <span>Skip Blanks</span>
                        </label>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Box>
          </form>
        </Box>
      )}
    </>
  );
};

export default ViewFileProcessingAttributes;
