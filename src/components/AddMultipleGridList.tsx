import { Dispatch, SetStateAction, useState } from "react";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import QueryBuilder from "./QueryBuilder/Index";
import { useToast } from "../services/utils";
import {
  Button,
  Grid,
  TextField,
  IconButton,
  Dialog,
  DialogActions,
} from "@mui/material";
import { Box } from "@mui/system";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import * as Yup from "yup";
import { useRuleResourceContext } from "../contexts/RuleResourceContext";
import { multipleGridListSchema } from "../schemas";
import { toast } from "react-toastify";
import { IconDeleteBlueSvg } from "../common/utils/icons";

interface AddMultipleGridListProps {
  gridValue: any;
  setGridValue: Dispatch<SetStateAction<any>>;
  isViewOnly: any;
  buttonName: string | undefined | null;
  errors: any;
  setErrors: Dispatch<SetStateAction<any>>;
  sampleQuery?: string;
}
const AddMultipleGridList = ({
  gridValue,
  setGridValue,
  isViewOnly,
  buttonName,
  errors,
  setErrors,
  sampleQuery,
}: AddMultipleGridListProps) => {
  const {
    globalVariables,
    setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    setQueryBuilderTempValue,
    setShowAggregatedColumns,
  } = useRuleResourceContext();
  const [showQryModal, setShowQryModal] = useState<any>(false);
  const [query, setQuery] = useState<any>(null);
  const [selectedId, setSelectedId] = useState<any>(null);
  const [queryError, setQueryError] = useState<{ [key: string]: string }>({});
  const { showToast } = useToast();

  const handleAddGrid = (e: any) => {
    if (gridValue.length > 0) {
      const isValidFilterRules = gridValue.every(
        (adhoc: { name: undefined; sql_query: undefined }) => {
          return adhoc.name !== "" && adhoc.sql_query !== "";
        }
      );
      if (!isValidFilterRules) {
        showToast(
          `Name and SQL Query cannot be empty in ${buttonName}!`,
          "warning"
        );
        return;
      }
    }
    e.stopPropagation();
    const newGrid = {
      id: Math.random(),
      name: "",
      sql_query: sampleQuery ? sampleQuery : "",
    };
    const updatedGridData = [...gridValue, newGrid];
    setGridValue && setGridValue(updatedGridData);
    checkMultipleGridValidation(updatedGridData);
  };
  const handleCloseQueryModal = () => {
    setShowQryModal(false);
    setQueryBuilderTempValue("");
    setQueryError({});
    setTempGlobalVariables({});
  };
  const handleSaveQuery = async () => {
    try {
      if (query) {
        const multipleGridSchema: any = {};
        Object.keys(tempGlobalVariables).map((key: any) => {
          if (tempGlobalVariables[key] === "") {
            multipleGridSchema[key] = Yup.string().required(
              `Please enter ${key}`
            );
          } else {
            return {
              name: key,
              sql_query: tempGlobalVariables[key],
            };
          }
        });
        const updatedGridData = gridValue.map((grid: { id: any }) =>
          grid.id === selectedId ? { ...grid, sql_query: query } : { ...grid }
        );
        await Yup.object()
          .shape({ ...multipleGridSchema })
          .validate(tempGlobalVariables, {
            abortEarly: false,
          });
        setGridValue && setGridValue(updatedGridData);
        setQueryError((prevError) => ({
          ...prevError,
          query: "",
        }));

        setGlobalVariables((prev: any) => ({
          ...Object.keys(tempGlobalVariables).reduce(
            (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
            prev
          ),
        }));
        setTempGlobalVariables({});
        checkMultipleGridValidation(updatedGridData);
      } else {
        setQueryError((prevError) => ({
          ...prevError,
          query: "Please enter query",
        }));
        return;
      }

      setQueryBuilderTempValue("");
      setShowQryModal(false);
    } catch (err: any) {
      err.inner.forEach((error: any) => {
        setQueryError((prevError) => ({
          ...prevError,
          [error.path]: error.message,
        }));
      });
      return;
    }
  };
  const handleChangeQuery = (qry: any, columns: any) => {
    setQuery(qry?.trim());
  };

  const handleNameChange = async (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    filterId: any,
    index: number
  ) => {
    const updatedFormData = gridValue.map((filter: { id: any }) =>
      filter.id === filterId ? { ...filter, name: e.target.value } : filter
    );
    setGridValue && setGridValue(updatedFormData);
    checkMultipleGridValidation(updatedFormData);
  };

  const checkMultipleGridValidation = async (
    updatedFormData: string | any[]
  ) => {
    const updatedErrors: { [x: string]: any }[] = [];

    for (let index = 0; index < updatedFormData.length; index++) {
      const element = updatedFormData[index];
      try {
        await multipleGridListSchema.validate(element, { abortEarly: false });
        updatedErrors[index] = { name: undefined, sql_query: undefined };
      } catch (validationErrors: any) {
        updatedErrors[index] = {};
        validationErrors.inner.forEach(
          (error: { path: string | number; message: any }) => {
            updatedErrors[index][error.path] = error.message;
          }
        );
      }
    }

    setErrors(updatedErrors);
  };
  return (
    <Grid
      item
      xs={12}
      sm={12}
      md={12}
      lg={12}
      xl={12}
      sx={{ textAlign: "right" }}
    >
      {!isViewOnly && (
        <Button
          variant="contained"
          color="secondary"
          onClick={(e: any) => handleAddGrid(e)}
          className="btn-orange btn-dark"
          sx={{ marginBottom: "12px" }}
        >
          <AddSharpIcon sx={{ marginRight: "4px" }} /> {buttonName || "Add"}
        </Button>
      )}

      {gridValue?.length > 0 && (
        <>
          <div className="table-filters-parent">
            <table className="table-filters white-bg">
              {isViewOnly && (
                <thead>
                  <tr>
                    <th className="no-left-border" style={{ width: "30%" }}>
                      Name:
                    </th>
                    <th>SQL Query(s):</th>
                  </tr>
                </thead>
              )}
              <tbody>
                {gridValue?.map((gridItem: any, idx: any) => {
                  return !isViewOnly ? (
                    <tr key={idx}>
                      <td width={"50"}>
                        <span className="number">{idx + 1}</span>
                      </td>
                      <td width={"250"}>
                        <TextField
                          type="text"
                          name="name"
                          disabled={isViewOnly}
                          fullWidth
                          variant="outlined"
                          className={`form-control-autocomplete ${
                            errors && errors[idx]?.name ? "has-error" : ""
                          }`}
                          onChange={(e) =>
                            handleNameChange(e, gridItem?.id, idx)
                          }
                          InputLabelProps={{
                            shrink: true,
                          }}
                          value={gridItem?.name || ""}
                          data-mtrsx={idx}
                          error={!!(errors && errors[idx]?.name)}
                          helperText={errors && errors[idx]?.name}
                        />
                      </td>
                      <td>
                        <TextField
                          type="text"
                          required
                          disabled={isViewOnly}
                          value={gridItem?.sql_query}
                          onClick={() => {
                            setShowQryModal(true);
                            setSelectedId(gridItem?.id);
                            if (gridItem?.sql_query) {
                              setQueryBuilderTempValue(gridItem?.sql_query);
                            }
                          }}
                          className={`form-control-autocomplete ${
                            errors && errors[idx]?.sql_query ? "has-error" : ""
                          }`}
                          InputProps={{
                            readOnly: true,
                          }}
                          error={!!(errors && errors[idx]?.sql_query)}
                          helperText={errors && errors[idx]?.sql_query}
                        />
                      </td>
                      <td width={"50"}>
                        {!isViewOnly && (
                          <IconButton
                            sx={{ color: "grey" }}
                            onClick={() => {
                              setGridValue &&
                                setGridValue(
                                  gridValue.filter(
                                    (grid: any) => grid.id !== gridItem?.id
                                  )
                                );
                            }}
                          >
                            <IconDeleteBlueSvg />
                          </IconButton>
                        )}
                      </td>
                    </tr>
                  ) : (
                    <tr key={idx}>
                      <td className={"bg-white"}>{gridItem?.name ?? ""}</td>
                      <td className={`bg-white valign-center`}>
                        <div>{gridItem?.sql_query}</div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </>
      )}
      <Dialog
        fullWidth
        maxWidth="lg"
        open={showQryModal}
        onClose={handleCloseQueryModal}
        className="main-dailog"
      >
        <Box sx={{ padding: 3 }}>
          <Grid container className="dailog-header">
            <label className="label-text pt-13">Add Query</label>
            <div className="close-icon" onClick={handleCloseQueryModal}></div>
          </Grid>
          <Box className="dailog-body pb-24">
            <Box>
              <Grid container rowSpacing={2.5} columnSpacing={6.25}>
                <QueryBuilder
                  handleChange={handleChangeQuery}
                  error={queryError}
                  setErrors={setQueryError}
                />
              </Grid>
            </Box>
          </Box>
          <DialogActions className="dailog-footer">
            <Button
              color="secondary"
              variant="contained"
              onClick={handleSaveQuery}
              className="btn-orange"
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </Grid>
  );
};

export default AddMultipleGridList;
