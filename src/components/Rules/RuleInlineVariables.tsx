import React from "react";
import { Grid } from "@mui/material";
import InlineVariables from "../../pages/Resource/InlineVariables";

interface RuleInlineVariablesProps {
  handleChangeVariable: any;
  inlineVariables: any;
  isReadOnly: boolean;
}

const RuleInlineVariables: React.FC<RuleInlineVariablesProps> = ({
  handleChangeVariable,
  inlineVariables,
  isReadOnly,
}) => {
  return (
    <Grid container rowSpacing={1.5} columnSpacing={2.5}>
      {inlineVariables?.resource_variables &&
        inlineVariables?.resource_variables.length > 0 && (
          <Grid item xs>
            {inlineVariables?.resource_variables.map((res: any) => (
              <div
                key={res.resource_id}
                className="text-box-card box-card-variables full-radius"
              >
                <h3>{res?.resource_name}</h3>
                <div className="inline-variables-parent">
                  <InlineVariables
                    handleChangeVariable={(event: any) =>
                      handleChangeVariable(
                        event,
                        "resource_variables",
                        res?.resource_id
                      )
                    }
                    inlineVariables={res.resource_vars}
                    isReadOnly={isReadOnly}
                  />
                </div>
              </div>
            ))}
          </Grid>
        )}

      {inlineVariables?.rule_variables?.filterRule &&
        Object.keys(inlineVariables?.rule_variables?.filterRule).length > 0 && (
          <Grid item xs>
            <div className="text-box-card box-card-variables full-radius">
              <h3>Rule Variables</h3>
              <div className="inline-variables-parent">
                <InlineVariables
                  handleChangeVariable={(event: any) =>
                    handleChangeVariable(event, "filterRule")
                  }
                  inlineVariables={inlineVariables.rule_variables.filterRule}
                  isReadOnly={isReadOnly}
                />
              </div>
            </div>
          </Grid>
        )}

      {inlineVariables?.rule_variables?.adhoc_queries &&
        Object.keys(inlineVariables?.rule_variables?.adhoc_queries).length >
          0 && (
          <Grid item xs>
            <div className="text-box-card box-card-variables full-radius">
              <h3>Adhoc Query</h3>
              <div className="inline-variables-parent">
                <InlineVariables
                  handleChangeVariable={(event: any) =>
                    handleChangeVariable(event, "adhoc_queries")
                  }
                  inlineVariables={inlineVariables.rule_variables.adhoc_queries}
                  isReadOnly={isReadOnly}
                />
              </div>
            </div>
          </Grid>
        )}
      {inlineVariables?.comparison_research_query_variables &&
        Object.keys(inlineVariables?.comparison_research_query_variables)
          .length > 0 && (
          <Grid item xs>
            <div className="text-box-card box-card-variables full-radius">
              <h3>Research Query Variables</h3>
              <div className="inline-variables-parent">
                <InlineVariables
                  handleChangeVariable={(event: any) =>
                    handleChangeVariable(
                      event,
                      "comparison_research_query_variables"
                    )
                  }
                  inlineVariables={
                    inlineVariables.comparison_research_query_variables
                  }
                  isReadOnly={isReadOnly}
                />
              </div>
            </div>
          </Grid>
        )}
    </Grid>
  );
};

export default RuleInlineVariables;
