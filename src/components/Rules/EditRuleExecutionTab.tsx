import React, { <PERSON><PERSON>atch, SetStateAction, useEffect, useState } from "react";
import { Box, Button, Checkbox, Grid, TextField } from "@mui/material";
import LinkedServices from "../Resource/LinkedServices";
import { isArray } from "lodash";
import { useResourceContext } from "../../contexts/ResourceContext";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import ConfirmationDialog from "../Dialogs/ConfirmationDialog";
import {
  aggregatedValidateSchema,
  basicAuthApiDefinitionSchema,
  blobLinkedServiceSchema,
  keyAuthApiDefinitionSchema,
  localLinkedServiceSchema,
  oAuthApiDefinitionSchema,
  sftpLinkedServiceSchema,
  sqlLinkedServiceSchema,
  tokenAuthApiDefinitionSchema,
  noAuthApiDefinitionSchema,
} from "../../schemas";
import { apiType, sqlDatabaseType } from "../../services/constants";
import { removeUndefinedProperties } from "../../services/utils";
import { updateResource } from "../../services/resourcesService";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useToast } from "../../services/utils";

interface IResourceData {
  resourceData: any;
  resourcesData: any;
  setResourcesData: Dispatch<SetStateAction<any>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  linkedServicesData: any;
  connectionKeysData: any;
  setSelectLinkedServiceId: Dispatch<SetStateAction<any>>;
  isAggregatedBaseResource: boolean;
  aggregatedResourceId: any;
  fileStreamIndex?: number;
  isAdditionalResource?: boolean;
  resource_id: number | null | undefined;
  isFromRuleExecution: boolean;
  resourcePath: string;
}

const EditRuleExecutionTab = ({
  resourcesData,
  resourceData,
  setIsLoading,
  setResourcesData,
  linkedServicesData,
  connectionKeysData,
  setSelectLinkedServiceId,
  isAggregatedBaseResource,
  aggregatedResourceId,
  fileStreamIndex,
  isAdditionalResource,
  resource_id,
  isFromRuleExecution,
  resourcePath,
}: IResourceData) => {
  const {
    globalVariables,
    setGlobalVariables,
    setIsResourceEdit,
    setViewInlineVariables,
  } = useRuleResourceContext();
  const { showToast } = useToast();

  const [formData, setFormData] = useState<any>({
    aggregation_type: "flat",
  });
  const [resourceId, setResourceId] = useState<any>(0);
  const [openResourceConfirmation, setOpenResourceConfirmation] =
    useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [queryParams, setQueryParams] = useState([]);
  const [urlParams, setUrlParams] = useState([]);
  const [type, setType] = useState("");
  const [allVariablesList, setAllVariablesList] = useState<any>({});

  const [currentResourceColumnId, setCurrentResourceColumnId] =
    useState<any>(null);
  const [cancelUploadFile, setCancelUploadFile] = useState<any>(false);
  const [isLoadingFile, setIsLoadingFile] = useState<boolean>(false);
  const [comment, setComment] = useState("");
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [initialBaseResourceColumns, setInitialBaseResourceColumns] =
    useState<any>([]);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  const [resourceColumnData] = useFetchResourceColumnsById({
    resourceColumnId: currentResourceColumnId,
    setIsLoading,
  });

  const { baseResourceColumns } = useResourceContext();

  useEffect(() => {
    if (resourceData) {
      setIsLoading(true);
      setResourceId(
        resourceData?.additional_properties?.aggregation_properties
          ?.base_resource_id ?? 0
      );
      setCurrentResourceColumnId(
        resourceData?.additional_properties?.resource_column_details_id
      );
      const newFormData = {
        ...resourceData,
        resource_definition:
          resourceData?.additional_properties?.resource_definition,
        base_resource_id:
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_id,
        base_resource_code:
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_code,
        resource_column_details_code:
          resourceData?.additional_properties?.resource_column_details_code,
        base_resource_validation:
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_validation,
        aggregation_query:
          resourceData?.additional_properties?.aggregation_properties
            ?.aggregation_query,
        base_resource_columns:
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_columns,
        isUpdateResource: false,
      };
      const linkedData = linkedServicesData?.find(
        (item: any) => item.id === resourceData.linked_service_id
      );
      newFormData.linked_service = linkedData;
      newFormData.additional_resource_data =
        resourceData?.additional_properties?.additional_resource_data || [];
      setFormData(newFormData);

      setInitialBaseResourceColumns(
        resourceData?.additional_properties?.aggregation_properties
          ?.base_resource_columns || []
      );

      setQueryParams(
        resourceData?.additional_properties?.resource_definition?.api_definition
          ?.query_params || {}
      );
      setUrlParams(
        resourceData?.additional_properties?.resource_definition?.api_definition
          ?.url_params || {}
      );
      setInitialFormData((prev: any) => ({
        ...prev,
        resource_definition: JSON.parse(
          JSON.stringify(
            newFormData?.additional_properties?.resource_definition
          )
        ),
        file_processing_id: newFormData?.file_processing_id,
        query_params:
          resourceData?.additional_properties?.resource_definition
            ?.api_definition?.query_params || {},
        url_params:
          resourceData?.additional_properties?.resource_definition
            ?.api_definition?.url_params || {},
      }));
      if (connectionKeysData) {
        //connectionKeyDetail for type api only
        const connectionKeyDetail = connectionKeysData?.find(
          (option: { id: any }) =>
            option.id ===
            resourceData?.additional_properties?.resource_definition
              ?.api_definition?.connection_key
        );
        setType(connectionKeyDetail?.type || " ");
      }
      setGlobalVariables(
        resourceData?.additional_properties?.inline_variables ?? {}
      );
      setAllVariablesList((prev: any) => ({
        ...prev,
        resource: {
          ...(resourceData?.additional_properties?.inline_variables ?? {}),
        },
        resourceColumn: {
          ...(resourceData?.additional_properties?.inline_variables ?? {}),
        },
      }));
      setIsLoading(false);
    }
  }, [resourceData, linkedServicesData]);

  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData?.resource_definition) !==
      JSON.stringify(initialFormData?.resource_definition);
    const fileProcessingIdChanged =
      JSON.stringify(formData?.file_processing_id) !==
      JSON.stringify(initialFormData?.file_processing_id);
    const baseResourceColumnsChanged =
      JSON.stringify(baseResourceColumns) !==
      JSON.stringify(initialBaseResourceColumns);

    setHasChanges(
      formChanged || fileProcessingIdChanged || baseResourceColumnsChanged
    );
  }, [
    formData,
    baseResourceColumns,
    initialFormData,
    initialBaseResourceColumns,
  ]);

  const updateResourceAtPath = (path: string, updatedResource: any) => {
    const update = (data: any) => {
      if (path === "root") {
        return updatedResource;
      }

      // Split path and handle array indices in format resources[1] or resources.1
      const pathParts = path.split(".").flatMap((part) => {
        // Handle array indices in format resources[1]
        const arrayMatch = part.match(/(\w+)\[(\d+)\]/);
        if (arrayMatch) {
          return [arrayMatch[1], arrayMatch[2]];
        }
        return [part];
      });

      // If the path starts with "resources", we need to handle it differently
      if (pathParts[0] === "resources") {
        // If we have an index after "resources"
        if (pathParts.length > 1 && !isNaN(parseInt(pathParts[1]))) {
          const index = parseInt(pathParts[1]);
          const newData = [...data];
          if (pathParts.length === 2) {
            // If we're just updating the entire resource at this index
            newData[index] = {
              ...newData[index],
              ...updatedResource,
            };
          } else {
            // If we're updating a property within the resource
            const remainingPath = pathParts.slice(2).join(".");
            const nestedUpdate = (data: any) => {
              const newData = { ...data };
              let current = newData;
              const remainingParts = remainingPath.split(".");

              for (let i = 0; i < remainingParts.length - 1; i++) {
                const part = remainingParts[i];
                if (Array.isArray(current)) {
                  const index = parseInt(part);
                  if (isNaN(index)) {
                    throw new Error(`Invalid array index: ${part}`);
                  }
                  current = current[index];
                } else {
                  current = current[part];
                }
              }

              const lastPart = remainingParts[remainingParts.length - 1];
              if (
                typeof current[lastPart] === "object" &&
                current[lastPart] !== null
              ) {
                current[lastPart] = {
                  ...current[lastPart],
                  ...updatedResource,
                };
              } else {
                current[lastPart] = updatedResource;
              }

              return newData;
            };
            newData[index] = nestedUpdate(newData[index]);
          }
          return newData;
        }
      }

      const newData = Array.isArray(data) ? [...data] : { ...data };
      let current = newData;

      // Skip "root" keyword
      let i = pathParts[0] === "root" ? 1 : 0;

      // Traverse until the second last part
      for (; i < pathParts.length - 1; i++) {
        const part = pathParts[i];

        // Handle array indices
        if (Array.isArray(current)) {
          const index = parseInt(part);
          if (isNaN(index)) {
            throw new Error(`Invalid array index: ${part}`);
          }
          current = current[index];
        } else {
          current = current[part];
        }
      }

      const lastPart = pathParts[pathParts.length - 1];

      // Handle the final update
      if (Array.isArray(current)) {
        const index = parseInt(lastPart);
        if (isNaN(index)) {
          throw new Error(`Invalid array index: ${lastPart}`);
        }
        current[index] = {
          ...current[index],
          ...updatedResource,
        };
      } else {
        if (
          typeof current[lastPart] === "object" &&
          current[lastPart] !== null
        ) {
          current[lastPart] = {
            ...current[lastPart],
            ...updatedResource,
          };
        } else {
          current[lastPart] = updatedResource;
        }
      }

      return newData;
    };
    const updateResourceData = update(resourcesData);
    setResourcesData(updateResourceData);
  };

  const onSaveResource = () => {
    const newResourceData: any = {};
    let uniqueBaseResourceColumns;
    if (baseResourceColumns && baseResourceColumns.length > 0) {
      const uniqueColumns = new Set(
        baseResourceColumns.map((column: { label: any }) => column.label)
      );
      uniqueBaseResourceColumns = Array.from(uniqueColumns);
    } else {
      uniqueBaseResourceColumns = null;
    }
    Object.assign(newResourceData, {
      ...formData,
      linked_service_id: formData?.linked_service?.id,
      linked_service_code: formData.linked_service?.code,
      is_active: true,
      file_processing_id: formData?.file_processing_id,
      file_processing_code: formData?.file_processing_code,
      additional_properties: {
        ...formData.additional_properties,
        resource_definition: {
          ...formData?.resource_definition,
          api_definition: {
            ...formData?.resource_definition?.api_definition,
            query_params: queryParams,
            url_params: urlParams,
            body:
              formData?.resource_definition?.api_definition?.method !== "get"
                ? formData?.resource_definition?.api_definition?.body
                : null,
          },
        },
        resource_column_details_id:
          parseInt(formData.additional_properties.resource_column_details_id) ||
          null,
        additional_resource_data: formData?.additional_resource_data || null,
        aggregation_properties:
          formData?.aggregation_type === "aggregated"
            ? {
                base_resource_id: formData.base_resource_id || null,
                base_resource_code: formData.base_resource_code || null,
                aggregation_query: formData.aggregation_query || null,
                base_resource_columns: uniqueBaseResourceColumns,
                base_resource_validation:
                  formData.base_resource_validation || false,
              }
            : null,
        inline_variables: globalVariables,
        resource_column_details_code:
          formData?.resource_column_details_code || null,
      },
    });
    [
      "base_resource_id",
      "base_resource_code",
      "aggregation_query",
      "base_resource_columns",
      "base_resource_validation",
    ].forEach((e) => delete newResourceData[e]);
    updateResourceAtPath(resourcePath, newResourceData);

    let updatedResourcesData: any;

    if (isAggregatedBaseResource) {
      updatedResourcesData = resourcesData.map((resource: { id: any }) => {
        if (resource.id === aggregatedResourceId) {
          let res = {
            ...resource,
            aggregation_base_resource_data: newResourceData,
          };
          return res;
        }
        return resource;
      });
      setResourcesData(updatedResourcesData);
    } else if (isAdditionalResource) {
      if (isArray(resourcesData)) {
        let updatedAdditionalResourceData: any;
        updatedResourcesData = resourcesData.map((resource) => {
          const isMatchingResource = resourceData?.isSecondaryResource
            ? resource.id === resource_id &&
              resource?.parentResId === resourceData?.parentResId
            : resource.id === resource_id;
          if (isMatchingResource) {
            if (resourceData?.isFromBaseResource) {
              updatedAdditionalResourceData =
                resource.aggregation_base_resource_data.additional_properties.updated_additional_resource_data.map(
                  (additionalResource: { id: any }) => {
                    if (additionalResource.id === resourceData?.id) {
                      return newResourceData;
                    }
                    return additionalResource;
                  }
                );
            } else {
              updatedAdditionalResourceData =
                resource.additional_properties.updated_additional_resource_data.map(
                  (additionalResource: { id: any }) => {
                    if (additionalResource.id === resourceData?.id) {
                      return newResourceData;
                    }
                    return additionalResource;
                  }
                );
            }
            return resourceData?.isFromBaseResource
              ? {
                  ...resource,
                  aggregation_base_resource_data: {
                    ...resource.aggregation_base_resource_data,
                    additional_properties: {
                      ...resource.aggregation_base_resource_data
                        .additional_properties,
                      updated_additional_resource_data:
                        updatedAdditionalResourceData,
                    },
                  },
                }
              : {
                  ...resource,
                  additional_properties: {
                    ...resource.additional_properties,
                    updated_additional_resource_data:
                      updatedAdditionalResourceData,
                  },
                };
          }
          return resource;
        });
        setResourcesData(updatedResourcesData);
      } else {
        updatedResourcesData =
          resourcesData?.additional_properties?.updated_additional_resource_data.map(
            (resource: { id: any; additional_properties: any }) => {
              if (resource.id === resourceData?.id) {
                return newResourceData;
              }
              return resource;
            }
          );
        setResourcesData((prev: { additional_properties: any }) => ({
          ...prev,
          additional_properties: {
            ...prev.additional_properties,
            updated_additional_resource_data: updatedResourcesData,
          },
        }));
      }
    } else {
      const updatedResourcesData = resourcesData.map(
        (resource: {
          id: any;
          isSecondaryResource: boolean;
          parentResId: any;
        }) => {
          if (
            resource.isSecondaryResource &&
            resource.id === resourceData?.id &&
            resource.parentResId === resourceData?.parentResId
          ) {
            return newResourceData;
          }

          if (
            !resource.isSecondaryResource &&
            resource.id === resourceData?.id
          ) {
            return newResourceData;
          }

          return resource;
        }
      );

      setResourcesData(updatedResourcesData);
    }
    // setIsEditResource(false);
    setIsResourceEdit("");
    if (formData?.isUpdateResource) {
      updateResourceData(newResourceData);
      setComment("");
    }
    setViewInlineVariables((prev: { resource_variables: any[] }) => {
      const resourceToUpdate = prev?.resource_variables?.find(
        (item: { resource_id: any }) => item?.resource_id === resourceData?.id
      );
      const updatedResource = {
        ...resourceToUpdate,
        resource_vars: globalVariables,
      };

      const newArr = JSON.parse(JSON.stringify(prev?.resource_variables));
      return {
        ...prev,
        resource_variables: newArr
          ? newArr.map((item: { resource_id: any }) =>
              item?.resource_id === resourceToUpdate?.resource_id
                ? { ...updatedResource }
                : item
            )
          : [...prev?.resource_variables, updatedResource],
      };
    });
  };
  const handleCancelResource = () => {
    setOpenResourceConfirmation(false);
    setComment("");
  };
  const handleSaveResource = () => {
    setOpenResourceConfirmation(false);
    onSaveResource();
  };
  const handleCommentChange = (e: any) => {
    setComment(e.target.value);
  };
  const saveResource = async () => {
    try {
      const type = formData?.linked_service?.sub_type;
      let uniqueBaseResourceColumns;
      if (baseResourceColumns && baseResourceColumns.length > 0) {
        const uniqueColumns = new Set(
          baseResourceColumns.map((column: { label: any }) => column.label)
        );
        uniqueBaseResourceColumns = Array.from(uniqueColumns);
      } else {
        uniqueBaseResourceColumns = null;
      }
      if (formData.aggregation_type === "flat") {
        if (type === "blob") {
          await blobLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "sftp") {
          await sftpLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "local") {
          await localLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (sqlDatabaseType.includes(type)) {
          await sqlLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "basic_auth_api") {
          await basicAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "token_auth_api") {
          await tokenAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "key_auth_api") {
          await keyAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "oauth_api") {
          await oAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "no_auth_api") {
          await noAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        }
      } else {
        const updatedFormData = {
          ...formData,
          base_resource_columns: uniqueBaseResourceColumns,
        };
        await aggregatedValidateSchema.validate(updatedFormData, {
          abortEarly: false,
        });
      }
      if (formData?.isUpdateResource) {
        setOpenResourceConfirmation(true);
      } else {
        onSaveResource();
        setIsResourceEdit("");
      }
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(
              /^resource_definition(\.api_definition)?\./,
              ""
            );
            newErrors[fieldName] = error.message;
          }
        );
      }
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const updateResourceData = (resourceData: any) => {
    const resourceId = resourceData?.id;
    let definitionType =
      resourceData?.additional_properties?.resource_definition?.type +
      "_definition";
    if (
      sqlDatabaseType.includes(
        resourceData?.additional_properties?.resource_definition?.type
      )
    ) {
      definitionType = "sql_definition";
    }
    const reqBody: any = {
      resource_name: resourceData.resource_name,
      resource_type: resourceData.resource_type,
      code: resourceData.code,
      resource_prefix: resourceData.resource_prefix,
      aggregation_type: resourceData.aggregation_type,
      domain_id: resourceData.domain_id,
      domain_name: resourceData.domain_name,
      domain_code: resourceData.domain_code,
      is_active: resourceData.is_active,
      linked_service_id: resourceData.linked_service_id,
      linked_service_code: resourceData.linked_service_code,
      current_url: resourceData.current_url,
      file_processing_id: resourceData.file_processing_id,
      file_processing_code: resourceData.file_processing_code,
      comment: comment,
      additional_properties: {
        resource_definition:
          resourceData.aggregation_type === "flat"
            ? {
                type: resourceData?.additional_properties?.resource_definition
                  ?.type,
                [definitionType]: {
                  resource_path:
                    resourceData?.additional_properties?.resource_definition
                      ?.resource_path,
                  file_name:
                    resourceData?.additional_properties?.resource_definition
                      ?.file_name,
                  column_delimiter:
                    resourceData?.additional_properties?.resource_definition
                      ?.column_delimiter,
                  connection_key:
                    resourceData?.additional_properties?.resource_definition
                      ?.connection_key,
                  connection_key_code: connectionKeysData?.find(
                    (option: { id: any }) =>
                      option.id ===
                      formData?.resource_definition?.connection_key
                  )?.code,
                  container_name:
                    resourceData?.additional_properties?.resource_definition
                      ?.container_name,
                  database_type:
                    resourceData?.additional_properties?.resource_definition
                      ?.database_type,
                  connection_string:
                    resourceData?.additional_properties?.resource_definition
                      ?.connection_string,
                  sql_query:
                    resourceData?.additional_properties?.resource_definition
                      ?.sql_query,
                  remote_directory:
                    resourceData?.additional_properties?.resource_definition
                      ?.remote_directory,

                  use_multi_thread_reader:
                    resourceData?.additional_properties?.resource_definition
                      ?.sql_definition?.use_multi_thread_reader || false,
                  column_name_to_partition_on_sql_query:
                    resourceData?.additional_properties?.resource_definition
                      ?.sql_definition?.column_name_to_partition_on_sql_query ||
                    "",
                },
                // api_definition:
                //   resourceData?.additional_properties?.resource_definition
                //     ?.api_definition,
                api_definition: apiType.includes(
                  resourceData?.additional_properties?.resource_definition?.type
                )
                  ? {
                      ...resourceData?.additional_properties
                        ?.resource_definition?.api_definition,
                      query_params: queryParams,
                      url_params: urlParams,
                      body:
                        resourceData?.additional_properties?.resource_definition
                          ?.api_definition?.method !== "get"
                          ? resourceData?.additional_properties
                              ?.resource_definition?.api_definition?.body
                          : null,
                    }
                  : null,
              }
            : null,
        aggregation_properties:
          resourceData.aggregation_type === "aggregated"
            ? resourceData?.additional_properties?.aggregation_properties
            : null,
        resource_column_details_id:
          resourceData?.additional_properties?.resource_column_details_id,
        additional_resource_data: resourceData?.additional_resource_data,
        filter_rules: resourceData?.additional_properties?.filter_rules,
        inline_variables: resourceData?.additional_properties?.inline_variables,
        resource_column_details_code:
          formData?.resource_column_details_code || null,
      },
    };
    if (formData?.linked_service?.type === "sql") {
      const keysToRemove = [
        "file_processing_code",
        "file_processing_id",
        "column_delimiter",
        "container_name",
        "file_name",
        "resource_path",
        "database_type",
        "connection_string",
      ];
      keysToRemove.forEach((key) => {
        if (key in reqBody) {
          delete reqBody[key];
        } else if (
          key in
          reqBody.additional_properties?.resource_definition?.sql_definition
        ) {
          delete reqBody.additional_properties.resource_definition
            .sql_definition[key];
        }
      });
    }
    const resourceObject = removeUndefinedProperties(reqBody);
    updateResource({ currentResourceId: resourceId, payload: resourceObject })
      .then((response: any) => {
        if (response) showToast("Resource updated successfully!", "success");
      })
      .catch((error: any) => {
        showToast(`Cannot update resource`, "error");
      });
  };
  return (
    <Box sx={{ marginBottom: "0px" }}>
      <Grid container rowSpacing={1.5} columnSpacing={2.5}>
        <LinkedServices
          formData={formData}
          setFormData={setFormData}
          connectionKeysData={connectionKeysData}
          linkedServicesData={linkedServicesData}
          setResourceId={setResourceId}
          resourceId={resourceId}
          setIsLoading={setIsLoading}
          setSelectLinkedServiceId={setSelectLinkedServiceId}
          fileStreamIndex={fileStreamIndex}
          errors={errors}
          setErrors={setErrors}
          queryParams={queryParams}
          setQueryParams={setQueryParams}
          urlParams={urlParams}
          setUrlParams={setUrlParams}
          type={type}
          setType={setType}
          setAllVariablesList={setAllVariablesList}
          allVariablesList={allVariablesList}
          resourceColumnData={resourceColumnData}
          setCancelUploadFile={setCancelUploadFile}
          cancelUploadFile={cancelUploadFile}
          setIsLoadingFile={setIsLoadingFile}
          isLoadingFile={isLoadingFile}
        />
        {formData?.linked_service?.type === "sql" && (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="base-resource-checkbox m-0">
                <Checkbox
                  checked={
                    formData?.resource_definition?.sql_definition
                      ?.use_multi_thread_reader || false
                  }
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        sql_definition: {
                          ...formData?.resource_definition?.sql_definition,
                          use_multi_thread_reader: event.target.checked,
                          column_name_to_partition_on_sql_query: event.target
                            .checked
                            ? formData?.resource_definition?.sql_definition
                                ?.column_name_to_partition_on_sql_query || ""
                            : "",
                        },
                      },
                    });
                  }}
                  sx={{
                    "&.Mui-checked": {
                      color: "#FFA500",
                    },
                  }}
                />
                <span>Use Multi Thread Reader</span>
              </label>
            </Grid>

            {formData?.resource_definition?.sql_definition
              ?.use_multi_thread_reader && (
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="Column Name to Partition On"
                  name="column_name_to_partition_on_sql_query"
                  fullWidth
                  label={
                    <span>
                      Column Name to Partition On
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.column_name_to_partition_on_sql_query
                      ? "has-error"
                      : ""
                  }`}
                  value={
                    formData?.resource_definition?.sql_definition
                      ?.column_name_to_partition_on_sql_query || ""
                  }
                  onChange={(e) => {
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        sql_definition: {
                          ...formData?.resource_definition?.sql_definition,
                          column_name_to_partition_on_sql_query: e.target.value,
                        },
                      },
                    });
                  }}
                  error={!!errors?.column_name_to_partition_on_sql_query}
                  helperText={
                    errors?.column_name_to_partition_on_sql_query || ""
                  }
                />
              </Grid>
            )}
          </>
        )}
        <Grid
          item
          xs
          sx={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "flex-end",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              columnGap: "8px",
              flexWrap: "wrap",
              rowGap: "16px",
            }}
          >
            <label className="base-resource-checkbox m-0">
              <Checkbox
                checked={formData.isUpdateResource || false}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  setFormData({
                    ...formData,
                    isUpdateResource: event.target.checked,
                  });
                }}
                sx={{
                  "&.Mui-checked": {
                    color: "#FFA500",
                  },
                }}
              />
              <span>Update Resource</span>
            </label>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                columnGap: "8px",
                flexWrap: "wrap",
              }}
            >
              <Button
                color="secondary"
                variant="contained"
                onClick={() => {
                  // setIsEditResource(false);
                  setIsResourceEdit("");
                  if (cancelUploadFile) {
                    cancelUploadFile.cancel();
                  }
                }}
                className="btn-orange btn-dark"
              >
                Cancel
              </Button>
              <Button
                color="secondary"
                variant="contained"
                onClick={() => saveResource()}
                className="btn-orange"
                disabled={!hasChanges}
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </Box>
          </Box>
        </Grid>
      </Grid>
      <ConfirmationDialog
        title={"Confirm updating Resource"}
        dialogContent={
          <>
            <p>
              Confirm changes before saving them for the
              {isFromRuleExecution ? " execution" : " validation"} step.
              {formData.isUpdateResource
                ? "This action will also update the Resource !"
                : ""}
            </p>
            <div>
              <p className="m-0 mb-2">
                Add a note before updating the Resource
              </p>
              <textarea
                value={comment}
                className={`form-control-1 max-60`}
                onChange={(e) => handleCommentChange(e)}
              />
            </div>
          </>
        }
        handleCancel={handleCancelResource}
        openConfirmation={openResourceConfirmation}
        handleConfirm={handleSaveResource}
      />
    </Box>
  );
};
export default EditRuleExecutionTab;
