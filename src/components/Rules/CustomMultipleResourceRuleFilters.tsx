import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  IconButton,
  Dialog,
  DialogActions,
} from "@mui/material";

import QueryBuilder from "../../components/QueryBuilder/Index";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useRuleContext } from "../../contexts/RuleContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import { extractColumnNamesFromQuery, useToast } from "../../services/utils";
import useFetchResourceColumnsByMultipleIds from "../../hooks/useFetchResourceColumnsByMultipleIds";

const CustomMultipleResourceRuleFilters = ({
  resourcesData,
  formData,
  setFormData,
  setViewInlineVariables,
  isEditFilters,
  setIsEditFilters,
}: any) => {
  const {
    setAllVariablesList,
    customComparisonRules,
    setCustomComparisonRules,
    selectedResourceColumns,
  } = useRuleContext();

  const {
    setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    setQueryBuilderTempValue,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    availColumns,
  } = useRuleResourceContext();

  //States

  const { showToast } = useToast();
  const [filterColumnsData, setFilterColumnsData] = useState<any>([]);
  const [filterColumnsId, setFilterColumnsId] = useState<string>("");
  const [showQryModal, setShowQryModal] = useState<any>(false);
  const [query, setQuery] = useState<any>(null);
  const [selectedIds, setSelectedIds] = useState<any>({
    resource: null,
    filter: null,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [filterErrors, setFilterErrors] = useState<any>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [currentResourceColumnId, setCurrentResourceColumnId] = useState<any>();
  const [resourceColumnsWithResourceId, setResourceColumnsWithResourceId] =
    useState<any[]>([]);
  const [resourceColIds, setResourceColIds] = useState<any[]>([]);
  const [tempFormData, setTempFormData] = useState<any>({});
  const [initialCustomComparisonRules, setInitialCustomComparisonRules] =
    useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  const [resourceColumnData] = useFetchResourceColumnsById({
    resourceColumnId: currentResourceColumnId,
    setIsLoading,
  });
  const [resourceColumns] = useFetchResourceColumnsByMultipleIds({
    resourceColIds,
    setIsLoading,
  });

  const handleFilterResourceChange = (value: any, id: any, filterId: any) => {
    setFilterColumnsId(value);
    const resourceName = resourcesData.find(
      (resourceItem: any) => resourceItem.id === value
    )?.resource_name;
    const resourceCode = resourcesData.find(
      (resourceItem: any) => resourceItem.id === value
    )?.code;
    const updatedRules = customComparisonRules.map((ruleItem: any) => {
      if (ruleItem.id === id) {
        return {
          ...ruleItem,
          resource_id: value,
          resource_code: resourceCode,
          name: resourceName,
          sql_query: "",
        };
      }
      return ruleItem;
    });

    setCustomComparisonRules(updatedRules);
  };

  useEffect(() => {
    if (formData?.filter_rules?.length) {
      const newFilter = formData?.filter_rules.map((item: any) => {
        return {
          id: Math.random(),
          name: item.name || "",
          resource_id: item.resource_id || "",
          resource_code: item.resource_code || "",
          sql_query: item.sql_query,
        };
      });
      setCustomComparisonRules(newFilter);
      newFilter &&
        setInitialCustomComparisonRules(JSON.parse(JSON.stringify(newFilter)));
    }

    setGlobalVariables(formData?.inline_variables || {});
  }, [resourcesData, selectedResourceColumns]);
  useEffect(() => {
    let resColIds: any[] = [];
    if (resourcesData && resourcesData.length > 0) {
      resourcesData.map((res: any) => {
        resColIds.push(
          res?.additional_properties?.resource_column_details_id ??
            res?.resource_column_details_id
        );
      });
      setResourceColIds(resColIds);
    }
  }, [resourcesData]);

  useEffect(() => {
    const formChanged =
      JSON.stringify(customComparisonRules) !==
      JSON.stringify(initialCustomComparisonRules);
    setHasChanges(formChanged);
  }, [customComparisonRules, initialCustomComparisonRules]);

  const handleChangeQuery = (qry: any, columns: any) => {
    setQuery(qry?.trim());
  };

  const handleSaveQuery = () => {
    const usedColumn = extractColumnNamesFromQuery(query);
    const isValid = usedColumn.every((columnName: any) =>
      availColumns.includes(columnName)
    );
    if (!isValid) {
      showToast(
        "The values enclosed in square brackets are reserved; only the available columns within the square brackets may be used.",
        "warning"
      );

      return;
    }
    const notValid = Object.keys(tempGlobalVariables).some(
      (key: any) => tempGlobalVariables[key] === ""
    );
    Object.keys(tempGlobalVariables).forEach((key: any) => {
      if (tempGlobalVariables[key] === "") {
        setErrors((prevError) => ({
          ...prevError,
          [key]: `Please enter ${key}`,
        }));
      } else {
        setErrors((prevError) => ({
          ...prevError,
          [key]: "",
        }));
      }
    });
    if (notValid) return;
    if (query) {
      const updatedRules = customComparisonRules.map((ruleItem: any) => {
        if (ruleItem.id === selectedIds?.resource) {
          const inlineVariables = Object.keys(tempGlobalVariables).map(
            (key: any) => {
              return {
                name: key,
                value: tempGlobalVariables[key],
              };
            }
          );

          return {
            ...ruleItem,
            sql_query: query,
            inline_variables: inlineVariables,
          };
        }
        return ruleItem;
      });

      setCustomComparisonRules(updatedRules);
      setErrors((prevError) => ({
        ...prevError,
        query: "",
      }));
    } else {
      setErrors((prevError) => ({
        ...prevError,
        query: "Please enter query",
      }));
      return;
    }
    // if (notValid) return;
    setSelectedIds({
      resource: null,
      filter: null,
    });
    setGlobalVariables((prev: any) => ({
      ...Object.keys(tempGlobalVariables).reduce(
        (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
        prev
      ),
    }));
    setAllVariablesList((prev: any) => ({
      ...prev,
      rule_Variables: {
        ...prev.rule_Variables,
        ...tempGlobalVariables,
      },
    }));
    setViewInlineVariables((prev: any) => ({
      ...prev,
      rule_variables: {
        ...prev.rule_Variables,
        filterRule: tempGlobalVariables,
      },
    }));
    setTempGlobalVariables({});
    setShowQryModal(false);
    setQuery(null);
    setQueryBuilderTempValue("");
  };

  const handleCloseQueryModal = () => {
    setShowQryModal(false);
    setQueryBuilderTempValue("");
    setErrors({});
    setTempGlobalVariables({});
  };

  useEffect(() => {
    const columns =
      resourceColumnData?.resource_column_properties?.resource_columns
        ?.map((columnItem: any) => {
          if (columnItem?.is_active) {
            return columnItem.column_name;
          }
        })
        .filter((filterItem: any) => filterItem);

    setAvailColumns(columns);
    setFilterColumnsData(columns);
  }, [resourceColumnData]);

  useEffect(() => {
    const updatedCustomComparisonRules = customComparisonRules.map(
      ({ id, ...rest }: any) => {
        return rest;
      }
    );
    // setFormData((prev: any) => ({
    //   ...prev,
    //   filter_rules: updatedCustomComparisonRules,
    // }));
    setTempFormData((prev: any) => ({
      ...prev,
      filter_rules: updatedCustomComparisonRules,
    }));
  }, [customComparisonRules]);
  useEffect(() => {
    const resourceColumnsById =
      resourceColumns &&
      resourceColumns.reduce((acc: any, resourceColumn: any) => {
        acc[resourceColumn.id] =
          resourceColumn.resource_column_properties.resource_columns;
        return acc;
      }, {});

    const resourceColumnsWithResourceId =
      resourcesData &&
      resourcesData.map((resource: any) => {
        return {
          resource_id: resource.id,
          resource_column_details_id:
            resource?.additional_properties?.resource_column_details_id ??
            resource?.resource_column_details_id,
          resource_columns:
            (resourceColumnsById &&
              resourceColumnsById[
                resource?.additional_properties?.resource_column_details_id ??
                  resource?.resource_column_details_id
              ]) ||
            [],
        };
      });
    setResourceColumnsWithResourceId(resourceColumnsWithResourceId);
  }, [resourceColumns]);

  const handleInvalidColumns = () => {
    const matchSqlQueryWithColumns = (
      filterRules: any,
      resourceColumnsWithResourceId: any
      //setErrors: (errors: any[]) => void
    ) => {
      let result: any[] = [];
      let errorsSet: any[] = [];

      // Iterate over filter rules
      filterRules.forEach((rule: any) => {
        // Find the corresponding resource by matching resource_id
        const matchedResource = resourceColumnsWithResourceId.find(
          (resource: any) => resource.resource_id === rule.resource_id
        );

        if (matchedResource) {
          // Extract column names for the current resource
          const availableColumns = matchedResource.resource_columns.map(
            (column: any) => column.column_name
          );
          // Extract the columns used in the SQL query
          const usedColumns = extractColumnNamesFromQuery(rule.sql_query);
          // Check if all used columns are present in the available columns
          const isValid = usedColumns.every((columnName: any) =>
            availableColumns.includes(columnName)
          );
          // If the rule is invalid, add an error message for the resource_id
          if (!isValid) {
            errorsSet.push({
              resource_id: rule.resource_id,
              errorMsg: `The columns used in SQL query for ${rule.name} are not exist in the available columns.`,
            });
          }

          // Push the result for this rule
          result.push({ ...rule, isValid });
        } else {
          // If no matching resource found, mark the rule as invalid and add an error
          result.push({ ...rule, isValid: false });
          errorsSet.push({
            resource_id: rule.resource_id,
            errorMsg: `Resource with ID ${rule.resource_id} not found.`,
          });
        }
      });
      // Update the errors using the setErrors function
      setFilterErrors(errorsSet);
      if (errorsSet.length > 0) {
        return;
      } else {
        setIsEditFilters((prev: any) => !prev);
      }
      return result;
    };
    const matchResult = matchSqlQueryWithColumns(
      formData.filter_rules,
      resourceColumnsWithResourceId
      //setErrors // This should be your setErrors function
    );
    setFormData(tempFormData);
  };

  return (
    <>
      <table className="table-filters" id="table-filters">
        <tbody>
          {customComparisonRules?.length > 0 &&
            customComparisonRules.map((item: any, index: any) => {
              const isValidationError = !item?.resource_id || !item?.sql_query;
              const error =
                filterErrors.length > 0 &&
                filterErrors?.find((err: any) => {
                  return err.resource_id === item?.resource_id;
                });
              return (
                <tr
                  key={index}
                  className={isValidationError ? "has-error" : ""}
                >
                  <td width={"50"}>
                    <span>{index + 1}</span>
                  </td>
                  <td width={"30%"}>
                    <select
                      value={item?.resource_id}
                      onChange={(e: any) =>
                        handleFilterResourceChange(
                          parseInt(e.target.value),
                          item?.id,
                          item?.resource_id
                        )
                      }
                      className="form-control-1 input-sm input-ellipsis"
                    >
                      {resourcesData?.length > 0 && (
                        <>
                          {resourcesData.map((resourceItem: any) => (
                            <option
                              key={resourceItem.id}
                              value={resourceItem.id}
                            >
                              {resourceItem.resource_name}
                            </option>
                          ))}
                        </>
                      )}
                    </select>
                  </td>
                  <td>
                    <input
                      type="text"
                      readOnly
                      value={item?.sql_query}
                      onClick={() => {
                        setShowQryModal(true);
                        setSelectedIds({
                          resource: item?.id,
                          filter: item?.id,
                        });
                        if (item?.sql_query) {
                          setQueryBuilderTempValue(item?.sql_query);
                        }
                        const resourceDetails = resourcesData.find(
                          (resourceItem: any) =>
                            resourceItem.id === item?.resource_id
                        );

                        setCurrentResourceColumnId(
                          resourceDetails?.additional_properties
                            ?.resource_column_details_id ??
                            resourceDetails?.resource_column_details_id
                        );

                        setAvailColumnsWithResourceDetail(null);
                      }}
                      className={`form-control-1 input-sm ${
                        isValidationError ? "has-error" : ""
                      }`}
                      tabIndex={showQryModal ? -1 : 0}
                    />
                    {error?.errorMsg && (
                      <p className="validation-error m-0">
                        {error?.errorMsg || "No error"}
                      </p>
                    )}
                  </td>
                </tr>
              );
            })}
          <tr>
            <td colSpan={3} className="align-right">
              {isEditFilters && (
                <Grid
                  item
                  xs
                  sx={{
                    display: "flex",
                    alignItems: "flex-end",
                    justifyContent: "flex-end",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      columnGap: "8px",
                      flexWrap: "wrap",
                    }}
                  >
                    <Button
                      color="secondary"
                      variant="contained"
                      onClick={() => {
                        setIsEditFilters((prev: any) => !prev);
                      }}
                      className="btn-orange btn-dark"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="button"
                      onClick={() => {
                        handleInvalidColumns();
                      }}
                      variant="contained"
                      color="secondary"
                      className="btn-orange"
                      title="Save Resource"
                      disabled={!hasChanges}
                    >
                      <SaveOutlinedIcon /> &nbsp; Save
                    </Button>
                  </Box>
                </Grid>
              )}
            </td>
          </tr>
        </tbody>
      </table>

      <Dialog
        fullWidth
        maxWidth="lg"
        open={showQryModal}
        onClose={handleCloseQueryModal}
        className="main-dailog"
      >
        <Box sx={{ padding: 3 }}>
          <Grid container className="dailog-header">
            <label className="label-text pt-13">Add Query</label>
            <div className="close-icon" onClick={handleCloseQueryModal}></div>
          </Grid>
          <Box className="dailog-body pb-24">
            <Box>
              <Grid container rowSpacing={2.5} columnSpacing={6.25}>
                <QueryBuilder
                  handleChange={handleChangeQuery}
                  error={errors}
                  setErrors={setErrors}
                />
              </Grid>
            </Box>
          </Box>
          <DialogActions className="dailog-footer">
            <Button
              color="secondary"
              variant="contained"
              onClick={handleSaveQuery}
              className="btn-orange"
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default CustomMultipleResourceRuleFilters;
