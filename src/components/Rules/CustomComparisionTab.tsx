import React, { useEffect, useState } from "react";
import FlexBetween from "../../components/FlexBetween";
import {
  Box,
  Input,
  Button,
  Grid,
  IconButton,
  Dialog,
  DialogActions,
  Tooltip,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import ArrowForwardIosSharpIcon from "@mui/icons-material/ArrowForwardIosSharp";
import MuiAccordion, { AccordionProps } from "@mui/material/Accordion";
import MuiAccordionSummary, {
  AccordionSummaryProps,
} from "@mui/material/AccordionSummary";
import MuiAccordionDetails from "@mui/material/AccordionDetails";
import { toast } from "react-toastify";
import CancelIcon from "@mui/icons-material/Cancel";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import QueryBuilder from "../../components/QueryBuilder/Index";
import ToleranceDialog from "../Dialogs/ToleranceDialog";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useRuleContext } from "../../contexts/RuleContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useToast } from "../../services/utils";
import { IconDeleteBlueSvg, IconEditBase } from "../../common/utils/icons";

const CustomComparisionTab = ({
  selectedResourceIds: resourcesData = [],
}: any) => {
  //ruleContext imports
  const {
    setAllVariablesList,
    customComparisonRules,
    setCustomComparisonRules,
    toleranceData,
    setToleranceData,
    selectedResourceColumns,
  } = useRuleContext();

  const {
    setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    setQueryBuilderTempValue,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
  } = useRuleResourceContext();
  const { showToast } = useToast();

  //States
  const [ruleName, setRuleName] = useState<any>("");
  const [expanded, setExpanded] = useState(null);
  const [filterColumnsData, setFilterColumnsData] = useState<any>([]);
  const [filterColumnsId, setFilterColumnsId] = useState<string>("");
  const [columnsData, setColumnsData] = useState<any>([]);
  const [showQryModal, setShowQryModal] = useState<any>(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [query, setQuery] = useState<any>(null);
  const [selectedIds, setSelectedIds] = useState<any>({
    resource: null,
    filter: null,
  });
  //const [tempQuery, setTempQuery] = useState(null);
  const [selectedRowId, setSelectedRowId] = useState<any>(null);
  const [openToleranceDialog, setOpenToleranceDialog] =
    useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const Accordion = styled((props: AccordionProps) => (
    <MuiAccordion disableGutters elevation={0} square {...props} />
  ))(({ theme }) => ({
    border: `1px solid ${theme.palette.divider}`,
    "&:not(:last-child)": {
      borderBottom: 0,
    },
    "&:before": {
      display: "none",
    },
  }));

  const AccordionSummary = styled((props: AccordionSummaryProps) => (
    <MuiAccordionSummary
      expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: "0.9rem" }} />}
      {...props}
    />
  ))(({ theme }) => ({
    backgroundColor:
      theme.palette.mode === "dark"
        ? "rgba(255, 255, 255, .05)"
        : "rgba(0, 0, 0, .03)",
    flexDirection: "row-reverse",
    "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
      transform: "rotate(90deg)",
    },
    "& .MuiAccordionSummary-content": {
      marginLeft: theme.spacing(1),
    },
  }));

  const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
    padding: theme.spacing(2),
    borderTop: "1px solid rgba(0, 0, 0, .125)",
  }));

  /**
   * Update rule name state when we write rule name on custom rule modal
   * @param e
   */
  const handleChangeRuleName = (e: any) => {
    setRuleName(e.target.value);
  };

  const handleAddRule = () => {
    if (!ruleName) {
      showToast("Please enter rule name", "error");
      return;
    }
    if (resourcesData?.length === 0) {
      showToast("Select resource", "error");
      return;
    }
    const newRule = {
      id: customComparisonRules.length + 1,
      name: ruleName,
    };
    setCustomComparisonRules([...customComparisonRules, newRule]);
    setRuleName("");
  };

  const handleAddResource = (e: any, id: any) => {
    e.stopPropagation();
    if (columnsData && columnsData.length === 0) {
      showToast("All resource columns are unique", "warning");
      return;
    }
    const newResource = {
      left_operand: {
        type: "column",
        value: "",
        resource_id: columnsData[0]?.id || "",
        resource_code: columnsData[0]?.code || "",
        column_name: columnsData[0]?.columns[0] || "",
      },
      right_operand: {
        type: "column",
        value: "",
        resource_id: columnsData[0]?.id || "",
        resource_code: columnsData[0]?.code || "",
        column_name: columnsData[0]?.columns[0] || "",
      },
      comparison_type: "equals",
    };

    const updatedRules = customComparisonRules.map((ruleItem: any) => {
      if (ruleItem.id === id) {
        return {
          ...ruleItem,
          resource: newResource,
        };
      }
      return ruleItem;
    });

    setCustomComparisonRules(updatedRules);
  };

  const handleAddFilter = (e: any, id: any) => {
    e.stopPropagation();
    const newFilter = {
      id: Math.random(),
      name: resourcesData[0].resource_name || "",
      resource_id: resourcesData[0].rId || "",
      resource_code: resourcesData[0].code || "",
      sql_query: "",
    };
    setFilterColumnsId(resourcesData[0]?.rId);
    const updatedRules = customComparisonRules.map((ruleItem: any) => {
      if (ruleItem.id === id) {
        return {
          ...ruleItem,
          filter_rules: [
            ...(ruleItem.filter_rules ? ruleItem.filter_rules : []),
            newFilter,
          ],
        };
      }
      return ruleItem;
    });
    setCustomComparisonRules(updatedRules);
  };

  const handleAddTolerance = (e: any, id: any) => {
    e.stopPropagation();
    setOpenToleranceDialog(true);
    setSelectedRowId(id);
    setToleranceData({
      tolerance_type: "percentage",
      tolerance_value: null,
      fallback_value: null,
    });
  };

  const handleAccordionChange = (panel: any) => (_: any, isExpanded: any) => {
    setExpanded(isExpanded ? panel : null);
  };

  const handleResourceChange = (value: any, id: any, type: any) => {
    const column_name = columnsData?.find(
      (res: { id: number }) => res.id === value
    )?.columns[0];
    const resource_code = columnsData?.find(
      (res: { id: number }) => res.id === value
    )?.code;
    const updatedRules = customComparisonRules.map((ruleItem: any) => {
      if (ruleItem.id === id) {
        return {
          ...ruleItem,
          resource: {
            ...ruleItem.resource,
            [type]: {
              ...ruleItem.resource[type],
              resource_id: value,
              column_name: column_name,
              resource_code: resource_code,
            },
          },
        };
      }
      return ruleItem;
    });

    setCustomComparisonRules(updatedRules);
  };

  const handleColumnChange = (value: any, id: any, type: any) => {
    const updatedRules = customComparisonRules.map((ruleItem: any) => {
      if (ruleItem.id === id) {
        return {
          ...ruleItem,
          resource: {
            ...ruleItem.resource,
            [type]: {
              ...ruleItem.resource[type],
              column_name: value,
            },
          },
        };
      }
      return ruleItem;
    });

    setCustomComparisonRules(updatedRules);
  };

  const handleOperatorChange = (value: any, id: any) => {
    const updatedRules = customComparisonRules.map((ruleItem: any) => {
      if (ruleItem.id === id) {
        return {
          ...ruleItem,
          resource: {
            ...ruleItem.resource,
            comparison_type: value,
          },
        };
      }
      return ruleItem;
    });

    setCustomComparisonRules(updatedRules);
  };

  const handleFilterResourceChange = (value: any, id: any, filterId: any) => {
    setFilterColumnsId(value);
    const resourceName = resourcesData.find(
      (resourceItem: any) => resourceItem.rId === value
    )?.resource_name;
    const resourceCode = resourcesData.find(
      (resourceItem: any) => resourceItem.rId === value
    )?.code;
    const updatedRules = customComparisonRules.map((ruleItem: any) => {
      if (ruleItem.id === id) {
        return {
          ...ruleItem,
          filter_rules: ruleItem.filter_rules.map((filterItem: any) => {
            if (filterItem.id === filterId) {
              return {
                ...filterItem,
                resource_id: value,
                resource_code: resourceCode,
                name: resourceName,
                sql_query: "",
              };
            }
            return filterItem;
          }),
        };
      }
      return ruleItem;
    });

    setCustomComparisonRules(updatedRules);
  };

  useEffect(() => {
    if (resourcesData?.length) {
      const updatedColumnsData = resourcesData.map((res: any) => {
        const resource_column =
          res.resource_column_properties?.resource_columns;
        let columns = resource_column
          ?.map((columnItem: any) => {
            if (columnItem?.is_active) {
              return columnItem.column_name;
            }
          })
          .filter((filterItem: any) => filterItem);

        columns = columns?.filter((columnItem: any) => {
          const filterData = selectedResourceColumns.find(
            (item: any) => item?.rId === res.rId
          );
          return !filterData?.columns.includes(columnItem);
        });

        return {
          id: res.rId,
          columns: columns || [],
          code: res.code || [],
        };
      });
      // setColumnsData(updatedColumnsData);
      // Remove "file_name" from columns
      updatedColumnsData.forEach((item: { columns: any[] }) => {
        item.columns = item.columns.filter(
          (col: string) => col !== "file_name"
        );
      });
      const filteredColumnsData = updatedColumnsData.filter(
        (data: any) => data.columns.length > 0
      );

      setColumnsData(filteredColumnsData);
    }
  }, [resourcesData, selectedResourceColumns, customComparisonRules]);

  const handleChangeQuery = (qry: any, columns: any) => {
    setQuery(qry?.trim());
  };

  const handleSaveQuery = () => {
    // setLocalVariables(tempLocalVariables);
    // setGlobalVariables(tempGlobalVariables);

    const notValid = Object.keys(tempGlobalVariables).some(
      (key: any) => tempGlobalVariables[key] === ""
    );
    Object.keys(tempGlobalVariables).forEach((key: any) => {
      if (tempGlobalVariables[key] === "") {
        setErrors((prevError) => ({
          ...prevError,
          [key]: `Please enter ${key}`,
        }));
      } else {
        setErrors((prevError) => ({
          ...prevError,
          [key]: "",
        }));
      }
    });
    if (notValid) return;
    if (query) {
      const updatedRules = customComparisonRules.map((ruleItem: any) => {
        if (ruleItem.id === selectedIds?.resource) {
          const inlineVariables = Object.keys(tempGlobalVariables).map(
            (key: any) => {
              return {
                name: key,
                value: tempGlobalVariables[key],
              };
            }
          );
          return {
            ...ruleItem,
            filter_rules: ruleItem.filter_rules.map((filterItem: any) => {
              if (filterItem.id === selectedIds?.filter) {
                return {
                  ...filterItem,
                  sql_query: query,
                  inline_variables: inlineVariables,
                };
              }
              return filterItem;
            }),
          };
        }
        return ruleItem;
      });

      setCustomComparisonRules(updatedRules);
      setErrors((prevError) => ({
        ...prevError,
        query: "",
      }));
    } else {
      setErrors((prevError) => ({
        ...prevError,
        query: "Please enter query",
      }));
      return;
    }
    // if (notValid) return;
    setSelectedIds({
      resource: null,
      filter: null,
    });
    setGlobalVariables((prev: any) => ({
      ...Object.keys(tempGlobalVariables).reduce(
        (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
        prev
      ),
    }));
    setAllVariablesList((prev: any) => ({
      ...prev,
      rule_Variables: {
        ...prev.rule_Variables,
        ...tempGlobalVariables,
      },
    }));
    setTempGlobalVariables({});
    setShowQryModal(false);
    setQuery(null);
    setQueryBuilderTempValue("");
  };

  const handleCloseQueryModal = () => {
    setShowQryModal(false);
    setSelectedRow(null);
    setQueryBuilderTempValue("");
    setErrors({});
    setTempGlobalVariables({});
    // setTempLocalVariables(localVariables);
  };

  const onRemoveRule = (e: any, id: any) => {
    e.stopPropagation();
    const updatedRules = customComparisonRules.filter(
      (ruleItem: any) => ruleItem.id !== id
    );
    setCustomComparisonRules(updatedRules);
  };

  const handleCloseToleranceDialog = () => {
    setOpenToleranceDialog(false);
    setToleranceData({});
    setSelectedRowId(null);
  };
  const onSaveToleranceDialog = () => {
    const updatedRules = customComparisonRules.map((ruleItem: any) => {
      if (ruleItem.id === selectedRowId) {
        return {
          ...ruleItem,
          tolerance_type: toleranceData?.tolerance_type,
          tolerance_value: toleranceData?.tolerance_value,
          fallback_value:
            toleranceData.tolerance_type === "percentage"
              ? toleranceData?.fallback_value
              : null,
        };
      }
      return ruleItem;
    });
    setCustomComparisonRules(updatedRules);
    setOpenToleranceDialog(false);
  };

  return (
    <>
      <Box className="text-box-card compact-text-box-card no-radius">
        <Grid container rowSpacing={2.5} columnSpacing={2}>
          <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
            <Input
              type="text"
              name="ruleName"
              // placeholder="Ex: Sample Rule Name"
              onChange={handleChangeRuleName}
              value={ruleName}
              className="form-control"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
            <Button
              variant="contained"
              color="secondary"
              onClick={handleAddRule}
              className="btn-orange"
            >
              Add
            </Button>
          </Grid>
        </Grid>
      </Box>
      <FlexBetween
        sx={{
          display: "flex",
          justifyContent: "flex-start",
          alignItem: "center",
          gap: "3rem",
          margin: "10px auto",
        }}
      ></FlexBetween>

      {customComparisonRules?.length > 0 &&
        customComparisonRules.map((item: any, index: any) => (
          <Accordion
            key={index}
            expanded={expanded === `panel${index}`}
            onChange={handleAccordionChange(`panel${index}`)}
            className="add-rule-accordion"
          >
            <AccordionSummary>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  width: "100%",
                  alignItems: "center",
                }}
              >
                <span>Rule Name: {item?.name}</span>
                <Box
                  sx={{
                    display: "flex",
                    columnGap: "20px",
                    alignItems: "center",
                  }}
                >
                  {item?.resource ? (
                    <>
                      {item?.tolerance_value ? null : (
                        <Button
                          variant="contained"
                          color="secondary"
                          onClick={(e: any) => handleAddTolerance(e, item?.id)}
                          className="btn-orange btn-dark"
                        >
                          <AddSharpIcon sx={{ marginRight: "4px" }} /> Tolerance
                        </Button>
                      )}
                      <Button
                        variant="contained"
                        color="secondary"
                        onClick={(e: any) => handleAddFilter(e, item?.id)}
                        className="btn-orange btn-dark"
                      >
                        <AddSharpIcon sx={{ marginRight: "4px" }} /> Filter
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      color="secondary"
                      onClick={(e: any) => handleAddResource(e, item?.id)}
                      className="btn-orange btn-dark"
                    >
                      <AddSharpIcon sx={{ marginRight: "4px" }} /> Resource
                    </Button>
                  )}
                  <Tooltip title="Delete Row" placement="top" arrow>
                    <IconButton
                      sx={{ color: "grey" }}
                      onClick={(e: any) => onRemoveRule(e, item?.id)}
                      className="datagrid-action-btn p-0"
                    >
                      <IconDeleteBlueSvg />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              {item?.resource ? (
                <>
                  <h3 className="rule-h3 mt-0">Resource:</h3>
                  <table className="table-filters mb-3">
                    <tbody>
                      <tr>
                        <td>
                          <Tooltip
                            title={
                              resourcesData.find((rData: any) => {
                                return (
                                  rData.rId ===
                                  item?.resource?.left_operand?.resource_id
                                );
                              })?.resource_name
                            }
                            placement="top"
                            arrow
                          >
                            <select
                              value={item?.resource?.left_operand?.resource_id}
                              onChange={(e: any) => {
                                e.target.value &&
                                  handleResourceChange(
                                    parseInt(e.target.value),
                                    item?.id,
                                    "left_operand"
                                  );
                              }}
                              className="form-control-1 input-sm"
                            >
                              {resourcesData?.length > 0 && (
                                <>
                                  {resourcesData.map((resourceItem: any) => (
                                    <option
                                      key={resourceItem.rId}
                                      value={resourceItem.rId}
                                    >
                                      {resourceItem.resource_name}
                                    </option>
                                  ))}
                                </>
                              )}
                            </select>
                          </Tooltip>
                        </td>
                        <td>
                          <Tooltip
                            title={item?.resource?.left_operand?.column_name}
                            placement="top"
                            arrow
                          >
                            <select
                              value={item?.resource?.left_operand?.column_name}
                              onChange={(e: any) => {
                                handleColumnChange(
                                  e.target.value,
                                  item?.id,
                                  "left_operand"
                                );
                              }}
                              className="form-control-1 input-sm"
                            >
                              {columnsData?.find(
                                (res: { id: number }) =>
                                  res.id ===
                                  item?.resource?.left_operand?.resource_id
                              )?.columns?.length > 0 ? (
                                columnsData
                                  ?.find(
                                    (res: { id: number }) =>
                                      res.id ===
                                      item?.resource?.left_operand?.resource_id
                                  )
                                  ?.columns?.filter(
                                    (item: any) => item !== "file_name"
                                  )
                                  .map((columnItem: any, idx: any) => (
                                    <option key={idx} value={columnItem}>
                                      {columnItem}
                                    </option>
                                  ))
                              ) : (
                                <option value="">No columns available</option>
                              )}
                            </select>
                          </Tooltip>
                        </td>
                        <td>
                          <select
                            value={item?.resource?.comparison_type}
                            onChange={(e: any) => {
                              handleOperatorChange(e.target.value, item?.id);
                            }}
                            className="form-control-1 input-sm"
                          >
                            <option value="equals">==</option>
                            <option value="not equals">!=</option>
                            <option value="less than">{"<"}</option>
                            <option value="greater than">{"> "}</option>
                            <option value="less than equals">{"<="}</option>
                            <option value="greater than equals">{">="}</option>
                          </select>
                        </td>
                        <td>
                          <Tooltip
                            title={
                              resourcesData.find((rData: any) => {
                                return (
                                  rData.rId ===
                                  item?.resource?.right_operand?.resource_id
                                );
                              })?.resource_name
                            }
                            placement="top"
                            arrow
                          >
                            <select
                              value={item?.resource?.right_operand?.resource_id}
                              onChange={(e: any) =>
                                handleResourceChange(
                                  parseInt(e.target.value),
                                  item?.id,
                                  "right_operand"
                                )
                              }
                              className="form-control-1 input-sm"
                            >
                              {resourcesData?.length > 0 && (
                                <>
                                  {resourcesData.map((resourceItem: any) => (
                                    <option
                                      key={resourceItem.rId}
                                      value={resourceItem.rId}
                                    >
                                      {resourceItem.resource_name}
                                    </option>
                                  ))}
                                </>
                              )}
                            </select>
                          </Tooltip>
                        </td>
                        <td>
                          <Tooltip
                            title={item?.resource?.right_operand?.column_name}
                            placement="top"
                            arrow
                          >
                            <select
                              value={item?.resource?.right_operand?.column_name}
                              onChange={(e: any) =>
                                handleColumnChange(
                                  e.target.value,
                                  item?.id,
                                  "right_operand"
                                )
                              }
                              className="form-control-1 input-sm"
                            >
                              {columnsData?.find(
                                (res: { id: number }) =>
                                  res.id ===
                                  item?.resource?.right_operand?.resource_id
                              )?.columns?.length > 0 ? (
                                columnsData
                                  ?.find(
                                    (res: { id: number }) =>
                                      res.id ===
                                      item?.resource?.right_operand?.resource_id
                                  )
                                  ?.columns?.filter(
                                    (item: any) => item !== "file_name"
                                  )
                                  ?.map((columnItem: any, idx: any) => (
                                    <option key={idx} value={columnItem}>
                                      {columnItem}
                                    </option>
                                  ))
                              ) : (
                                <option value="">No columns available</option>
                              )}
                            </select>
                          </Tooltip>
                        </td>
                        <td></td>
                        <td>
                          <Tooltip title="Delete Row" placement="top" arrow>
                            <IconButton
                              sx={{ color: "grey" }}
                              onClick={() => {
                                const updatedRules = customComparisonRules.map(
                                  (ruleItem: any) => {
                                    if (ruleItem.id === item?.id) {
                                      delete ruleItem.resource;
                                    }
                                    return ruleItem;
                                  }
                                );

                                setCustomComparisonRules(updatedRules);
                              }}
                            >
                              <CancelIcon />
                            </IconButton>
                          </Tooltip>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </>
              ) : null}
              {item?.tolerance_value && (
                <>
                  <h3 className="rule-h3">Tolerance:</h3>
                  <table className="table-filters  mb-3">
                    <tbody>
                      <tr>
                        <td width={"300"}>
                          Tolerance Type : {item?.tolerance_type}
                        </td>
                        <td width={"300"}>
                          Tolerance Value : {item?.tolerance_value}
                        </td>
                        {item?.fallback_value != null &&
                          item?.tolerance_type === "percentage" && (
                            <td width={"300"}>
                              Fallback Value : {item?.fallback_value}
                            </td>
                          )}
                        <td>
                          <Tooltip title="Edit Tolerance" placement="top" arrow>
                            <IconButton
                              sx={{ color: "grey" }}
                              onClick={() => {
                                setOpenToleranceDialog(true);
                                setSelectedRowId(item?.id);
                                setToleranceData({
                                  tolerance_type: item?.tolerance_type,
                                  tolerance_value: item?.tolerance_value,
                                  fallback_value: item?.fallback_value,
                                });
                              }}
                            >
                              <IconEditBase />
                            </IconButton>
                          </Tooltip>
                          <Tooltip
                            title="Delete Tolerance"
                            placement="top"
                            arrow
                          >
                            <IconButton
                              sx={{ color: "grey" }}
                              onClick={() => {
                                const updatedRules = customComparisonRules.map(
                                  (ruleItem: any) => {
                                    if (ruleItem.id === item?.id) {
                                      return {
                                        ...ruleItem,
                                        tolerance_value: null,
                                        tolerance_type: "",
                                        fallback_value: null,
                                      };
                                    }
                                    return ruleItem;
                                  }
                                );

                                setCustomComparisonRules(updatedRules);
                              }}
                            >
                              <IconDeleteBlueSvg />
                            </IconButton>
                          </Tooltip>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </>
              )}
              {item?.filter_rules?.length > 0 && (
                <>
                  <h3 className="rule-h3">Filters:</h3>
                  <table className="table-filters">
                    <tbody>
                      {item?.filter_rules?.map((filterItem: any, idx: any) => {
                        const isValidationError =
                          !filterItem?.resource_id || !filterItem?.sql_query;
                        return (
                          <tr
                            key={idx}
                            className={isValidationError ? "has-error" : ""}
                          >
                            <td width={"50"} className="align-middle">
                              <span>{idx + 1}</span>
                            </td>
                            <td width={"250"}>
                              <Tooltip
                                title={
                                  resourcesData?.find((rData: any) => {
                                    return (
                                      rData?.rId === filterItem?.resource_id
                                    );
                                  })?.resource_name
                                }
                                placement="top"
                                arrow
                              >
                                <select
                                  value={filterItem?.resource_id}
                                  onChange={(e: any) =>
                                    handleFilterResourceChange(
                                      parseInt(e.target.value),
                                      item?.id,
                                      filterItem?.id
                                    )
                                  }
                                  className="form-control-1 input-sm input-ellipsis"
                                >
                                  {resourcesData?.length > 0 && (
                                    <>
                                      {resourcesData.map(
                                        (resourceItem: any) => (
                                          <option
                                            key={resourceItem.rId}
                                            value={resourceItem.rId}
                                          >
                                            {resourceItem.resource_name}
                                          </option>
                                        )
                                      )}
                                    </>
                                  )}
                                </select>
                              </Tooltip>
                            </td>
                            <td width={"300"}>
                              <input
                                type="text"
                                readOnly
                                value={filterItem?.sql_query}
                                onFocus={() => {
                                  setShowQryModal(true);
                                  setSelectedIds({
                                    resource: item?.id,
                                    filter: filterItem?.id,
                                  });
                                  if (filterItem?.sql_query) {
                                    setQueryBuilderTempValue(
                                      filterItem?.sql_query
                                    );
                                  }
                                  const resourceDetails = resourcesData.find(
                                    (resourceItem: any) =>
                                      resourceItem.rId ===
                                      filterItem?.resource_id
                                  );

                                  console.log(
                                    resourceDetails,
                                    "resourceDetails ???"
                                  );

                                  const columns =
                                    resourceDetails?.resource_column_properties?.resource_columns
                                      ?.map((columnItem: any) => {
                                        if (columnItem?.is_active) {
                                          return columnItem.column_name;
                                        }
                                      })
                                      .filter((filterItem: any) => filterItem);
                                  setFilterColumnsData(columns);
                                  setAvailColumns(columns);
                                  setAvailColumnsWithResourceDetail(null);
                                }}
                                className={`form-control-1 input-sm ${
                                  isValidationError ? "has-error" : ""
                                }`}
                              />
                            </td>
                            <td>
                              <IconButton
                                sx={{ color: "grey" }}
                                onClick={() => {
                                  const updatedRules =
                                    customComparisonRules.map(
                                      (ruleItem: any) => {
                                        if (ruleItem.id === item?.id) {
                                          return {
                                            ...ruleItem,
                                            filter_rules:
                                              ruleItem.filter_rules.filter(
                                                (filItem: any) =>
                                                  filItem.id !== filterItem?.id
                                              ),
                                          };
                                        }
                                        return ruleItem;
                                      }
                                    );

                                  setCustomComparisonRules(updatedRules);
                                }}
                              >
                                <CancelIcon />
                              </IconButton>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </>
              )}
            </AccordionDetails>
          </Accordion>
        ))}
      <Dialog
        fullWidth
        maxWidth="lg"
        open={showQryModal}
        onClose={handleCloseQueryModal}
        className="main-dailog"
      >
        <Box sx={{ padding: 3 }}>
          <Grid container className="dailog-header">
            <label className="label-text pt-13">Add Query</label>
            <div className="close-icon" onClick={handleCloseQueryModal}></div>
          </Grid>
          <Box className="dailog-body pb-24">
            <Box>
              <Grid container rowSpacing={2.5} columnSpacing={6.25}>
                <QueryBuilder
                  handleChange={handleChangeQuery}
                  //tempQuery={queryBuilderTempValue ? queryBuilderTempValue : ""}
                  error={errors}
                  setErrors={setErrors}
                />
              </Grid>
            </Box>
          </Box>
          <DialogActions className="dailog-footer">
            <Button
              color="secondary"
              variant="contained"
              onClick={handleSaveQuery}
              className="btn-orange"
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <ToleranceDialog
        openToleranceDialog={openToleranceDialog}
        handleCloseToleranceDialog={handleCloseToleranceDialog}
        onSaveToleranceDialog={onSaveToleranceDialog}
      />
    </>
  );
};

export default CustomComparisionTab;
