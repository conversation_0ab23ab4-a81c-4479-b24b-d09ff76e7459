import { useState, useEffect } from "react";
import RulesListDataTable from "../DataGrids/RulesListDataGrid";
import CustomAccordion from "../Molecules/Accordian/CustomAccordion";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
} from "@mui/material";
import useFetchAdhocQueryRecordsByRuleResultId from "../../hooks/useFetchAdhocQueryRecordsByRuleResultId";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

interface IAdhocQueryDashboardTab {
  dashboardData?: any;
  adhocQueryData?: any;
}

interface INewAdhocQueryDetail {
  adhocQueryName: any;
  adhocDetailData: any;
  isLoading: boolean;
  setSelectedQueryName: React.Dispatch<React.SetStateAction<any>>;
  expandedAccordion: string | undefined;
  setExpandedAccordion: React.Dispatch<React.SetStateAction<any>>;
  pageN: number;
  setPageN: React.Dispatch<React.SetStateAction<number>>;
  pageS: number;
  setPageS: React.Dispatch<React.SetStateAction<number>>;
  totalPages: number;
}

const AdhocQueryDashboardTab = ({
  dashboardData,
  adhocQueryData,
}: IAdhocQueryDashboardTab) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [adhocData, setAdhocData] = useState<any>([]);
  const [selectedQueryName, setSelectedQueryName] = useState<any>("");
  const [expandedAccordion, setExpandedAccordion] = useState<
    string | undefined
  >("");

  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [pageN, setPageN] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pageS, setPageS] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  const [adhocQueryResults] = useFetchAdhocQueryRecordsByRuleResultId({
    setIsLoading,
    isAdhocQueryFetch: !dashboardData?.is_detailed_execution_data_available,
    currentRuleResultId: dashboardData?.id,
    pageN,
    pageS,
    queryName: selectedQueryName,
  });
  useEffect(() => {
    if (adhocQueryResults?.items?.length > 0) {
      setAdhocData(adhocQueryResults?.items);
    } else if (adhocQueryResults?.items?.length === 0) {
      setAdhocData([]);
    }
  }, [adhocQueryResults]);

  useEffect(() => {
    if (!dashboardData?.additional_properties?.adhoc_queries_result) return;
    if (dashboardData?.is_detailed_execution_data_available) {
      setAdhocData(dashboardData?.additional_properties?.adhoc_queries_result);
    }
    setSelectedQueryName(
      dashboardData?.additional_properties?.adhoc_queries_result[0]?.name
    );
    if (
      adhocQueryData?.additional_properties?.adhoc_queries_result?.length > 0
    ) {
      setAdhocData(adhocQueryData?.additional_properties?.adhoc_queries_result);
    }
  }, [dashboardData, adhocQueryData]);
  return (
    <div>
      {dashboardData?.additional_properties?.adhoc_queries_result?.length >
      0 ? (
        <>
          {dashboardData?.additional_properties?.adhoc_queries_result?.map(
            (item: any, index: any) => {
              return dashboardData?.is_detailed_execution_data_available ? (
                <AdhocQueryDetail
                  key={index}
                  adhocDetailData={item}
                  isLoading={isLoading}
                />
              ) : (
                <NewAdhocQueryDetail
                  key={index}
                  adhocQueryName={item?.name || ""}
                  adhocDetailData={adhocData}
                  isLoading={isLoading}
                  setSelectedQueryName={setSelectedQueryName}
                  expandedAccordion={expandedAccordion}
                  setExpandedAccordion={setExpandedAccordion}
                  pageN={pageN}
                  setPageN={setPageN}
                  pageS={pageS}
                  setPageS={setPageS}
                  totalPages={adhocQueryResults?.total}
                />
              );
            }
          )}
        </>
      ) : (
        <Box
          sx={{
            paddingTop: "24px",
            paddingBottom: "24px",
            display: "flex",
            justifyContent: "center",
          }}
        >
          No data available
        </Box>
      )}
    </div>
  );
};

export default AdhocQueryDashboardTab;

const AdhocQueryDetail = ({ adhocDetailData, isLoading }: any) => {
  const [columnsData, setColumnsData] = useState<any>([]);
  const [rowsData, setRowsData] = useState<any>([]);
  useEffect(() => {
    if (adhocDetailData) {
      const firstResult = adhocDetailData.result;
      if (firstResult && firstResult.length > 0) {
        const columns = Object.keys(firstResult[0]).map((key) => ({
          field: key,
          headerName: key,
          flex: 1,
          minWidth: 120,
        }));

        const rows = firstResult.map((item: any, index: any) => ({
          id: index,
          ...item,
        }));

        setColumnsData(columns);
        setRowsData(rows);
      }
    }
  }, [adhocDetailData]);

  return (
    <>
      <CustomAccordion
        key={adhocDetailData?.name}
        expandId={`ruleCol.name_+${adhocDetailData?.name.replace(" ", "")}`}
        title={adhocDetailData?.name}
        isEnabled={true}
      >
        <RulesListDataTable
          dataRows={rowsData}
          dataColumns={columnsData}
          loading={isLoading}
          dataListTitle="Rules Dashboard"
          className="dataTable no-radius pt-0 bdr-top-0"
          disableColumnFilter={true}
          tableHeight={180}
          singlePageMaxHeightDiff={362}
        />
      </CustomAccordion>
    </>
  );
};
const NewAdhocQueryDetail = ({
  adhocQueryName,
  adhocDetailData,
  isLoading,
  setSelectedQueryName,
  expandedAccordion,
  setExpandedAccordion,
  pageN,
  setPageN,
  pageS,
  setPageS,
  totalPages,
}: INewAdhocQueryDetail) => {
  const [columnsData, setColumnsData] = useState<any>([]);
  const [rowsData, setRowsData] = useState<any>([]);

  // Ensure proper handling of column data when the detail data is updated
  useEffect(() => {
    if (adhocDetailData?.length > 0) {
      const columns = Object.keys(
        adhocDetailData[0]?.adhoc_query_results?.result
      ).map((key) => ({
        field: key,
        headerName: key,
        flex: 1,
        minWidth: 120,
      }));

      const rows = adhocDetailData.map((item: any, index: any) => ({
        id: index,
        ...item?.adhoc_query_results?.result,
      }));

      setColumnsData(columns);
      setRowsData(rows);
    } else {
      setColumnsData([]);
      setRowsData([]);
    }
  }, [adhocDetailData]);

  // Handle accordion state change
  const handleChangeAccordion =
    (adhocQueryName: string | undefined) =>
    (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedAccordion(isExpanded ? adhocQueryName : "");
      setSelectedQueryName(adhocQueryName);
    };

  // Construct a unique ID for each panel
  const panelId = `ruleCol.name_+${adhocQueryName?.replace(" ", "")}`;

  return (
    <>
      <Box className="accordion-panel" sx={{ marginBottom: "8px" }}>
        <Accordion
          className="heading-bold box-shadow"
          expanded={expandedAccordion === adhocQueryName}
          onChange={handleChangeAccordion(adhocQueryName)}
          sx={{ marginTop: "8px" }}
        >
          <AccordionSummary
            aria-controls="panel1d-content"
            id={panelId}
            expandIcon={<ExpandMoreIcon />}
            className="min-header"
          >
            {adhocQueryName}
          </AccordionSummary>
          <AccordionDetails
            sx={{ paddingTop: "16px" }}
            style={{ display: "block" }}
          >
            <RulesListDataTable
              dataRows={rowsData}
              dataColumns={columnsData}
              loading={isLoading}
              dataListTitle="Rules Dashboard"
              className="dataTable no-radius pt-0 bdr-top-0"
              disableColumnFilter={true}
              tableHeight={180}
              disableColumnReorder={true}
              paginationMode="server"
              rowCount={totalPages || 0}
              pageSizeOptions={[25]}
              paginationModel={{
                page: pageN - 1,
                pageSize: pageS,
              }}
              onPaginationModelChange={(params: any) => {
                if (params.pageSize !== pageS || params.page !== pageN - 1) {
                  setPageN(params.page + 1);
                  setPageS(params.pageSize);
                }
              }}
              singlePageMaxHeightDiff={362}
            />
          </AccordionDetails>
        </Accordion>
      </Box>
    </>
  );
};
