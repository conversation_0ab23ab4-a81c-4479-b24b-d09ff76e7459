import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Tooltip,
  Button,
} from "@mui/material";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import useFetchConnectionKeyByLinkedServiceId from "../../../hooks/useFetchConnectionKeyByLinkedServiceId";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CustomAccordion from "../../Molecules/Accordian/CustomAccordion";
import FilterRules from "../../Resource/FilterRules";
import { useNavigate, useParams } from "react-router-dom";
import { IconBtnEditBase, IconViewSvgWhite } from "../../../common/utils/icons";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import { useResourceContext } from "../../../contexts/ResourceContext";
import { filterRulesNameSchema, filterRulesSqlSchema } from "../../../schemas";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import useFetchResourceColumnsById from "../../../hooks/useFetchResourceColumnsById";
import { useToast } from "../../../services/utils";
import ReRunEditRuleExecutionTab from "./ReRunEditRuleExecutionTab";
import ViewResourceData from "../../Resource/ReRun/ViewResourceData";

interface IResourceData {
  resourceData: any;
  resourcesData: any;
  isLoading: boolean;
  setResourcesData: Dispatch<SetStateAction<any>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  fileProcessingData: any;
  fileStreamIndex?: number;
  setViewInlineVariables?: any;
  viewInlineVariables: any;
  isReRun: any;
}
interface FilterFormData {
  filter_rules: any[];
}

const ReRunRuleExecutionTab = ({
  resourcesData,
  resourceData,
  isLoading,
  setIsLoading,
  setResourcesData,
  fileProcessingData,
  fileStreamIndex,
  setViewInlineVariables,
  viewInlineVariables,
  isReRun,
}: IResourceData) => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { domainId } = useParams();
  const {
    isResourceEdit,
    setIsResourceEdit,
    setGlobalVariables,
    linkedServicesData,
  } = useRuleResourceContext();
  const { setResourceColumnData, resourceColumnData, errors, setErrors } =
    useResourceContext();
  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<number>(0);
  const [selectBaseLinkedServiceId, setBaseSelectLinkedServiceId] =
    useState<number>(0);
  const [resourceId, setResourceId] = useState(0);
  const [resourceType, setResourceType] = useState("");
  const [expandedAccordion, setExpandedAccordion] = useState<any>("");
  const [isEditFilters, setIsEditFilters] = useState(false);
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [filterRules, setFilterRules] = useState<any>({
    filter_rules: [],
  });

  const [currentResourceColumnId, setCurrentResourceColumnId] =
    useState<any>(null);

  const [filterFormData, setFilterFormData] = useState<
    FilterFormData | undefined
  >({ filter_rules: [] });

  const [connectionKeysData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId,
    setIsLoading,
  });
  const [baseConnectionKeysData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId: selectBaseLinkedServiceId,
    setIsLoading,
  });

  const [resourceColumn] = useFetchResourceColumnsById({
    resourceColumnId: currentResourceColumnId,
    setIsLoading,
  });

  useEffect(() => {
    if (resourceColumn && Object.keys(resourceColumn)?.length) {
      setResourceColumnData(resourceColumn);
    }
  }, [resourceColumn]);

  useEffect(() => {
    if (resourceData) {
      setIsLoading(true);
      const aggregationType = resourceData?.aggregation_properties
        ?.base_resource_id
        ? "aggregated"
        : "flat";
      setResourceType(aggregationType);
      if (aggregationType === "aggregated") {
        setResourceId(resourceData?.resource_id);
      }
      setSelectLinkedServiceId((prev) => resourceData?.linked_service_id);
      setBaseSelectLinkedServiceId(
        resourceData?.aggregation_base_resource_data?.linked_service_id
      );
      setIsLoading(false);
    }
    setCurrentResourceColumnId(resourceData?.resource_column_details_id);
    const filter_rules =
      resourceData?.filter_rules?.length > 0
        ? resourceData?.filter_rules.map((rule: any) => ({
            ...rule,
            id: Math.random(),
          }))
        : [];
    setFilterRules({
      filter_rules: filter_rules,
    });
  }, [resourceData]);

  useEffect(() => {
    if (filterFormData) {
      setResourcesData((prevResourcesData: any) => {
        const updatedResources = prevResourcesData.map((resource: any) => {
          if (resource?.resource_id === resourceData?.resource_id) {
            return {
              ...resource,
              filter_rules: filterFormData?.filter_rules,
            };
          }
          return resource;
        });
        return updatedResources;
      });
    }
  }, [filterFormData]);

  const handleChangeAccordion =
    (panel: string) => (event: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
      setExpandedAccordion(isExpanded ? panel : null);
    };

  const handleViewResource = (
    domainId: any,
    resource: any,
    resourceName: any,
    e: any
  ) => {
    e.preventDefault();
    if (isResourceEdit !== "") {
      return;
    }
    navigate(`/resource/${domainId}/view/${resource}`);
  };
  const checkFilterRuleValidation = async (
    updatedFormData: any,
    schemaType: any
  ) => {
    const getSchemaType =
      schemaType === "name" ? filterRulesNameSchema : filterRulesSqlSchema;
    for (let index = 0; index < updatedFormData.filter_rules.length; index++) {
      const element = updatedFormData.filter_rules[index];
      try {
        await getSchemaType.validate(element, { abortEarly: false });
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] = undefined;
        } else {
          updatedJsonData.filter_rules.push({ [updateKey]: undefined });
        }
        setErrors(updatedJsonData);
      } catch (validationErrors: any) {
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] =
            validationErrors.message;
        } else {
          updatedJsonData.filter_rules.push({
            [updateKey]: validationErrors.message,
          });
        }
        setErrors(updatedJsonData);
      }
    }
  };

  useEffect(() => {
    setViewInlineVariables((prev: { resource_variables: any[] }) => {
      const resourceToUpdate = prev?.resource_variables?.find(
        (item: { resource_id: any }) => item?.resource_id === resourceData?.id
      );
      const updatedResource = {
        ...resourceToUpdate,
        resource_vars: {
          ...resourceToUpdate?.resource_vars,
          ...allVariablesList?.resource,
        },
      };

      const newArr = JSON.parse(JSON.stringify(prev?.resource_variables || []));
      return {
        ...prev,
        resource_variables: newArr
          ? newArr.map((item: { resource_id: any }) =>
              item?.resource_id === resourceToUpdate?.resource_id
                ? { ...updatedResource }
                : item
            )
          : [...prev?.resource_variables, updatedResource],
      };
    });
  }, [allVariablesList, setViewInlineVariables]);

  return (
    <>
      {isResourceEdit === "resource" ? (
        <ReRunEditRuleExecutionTab
          resourceData={resourceData}
          resourcesData={resourcesData}
          setIsLoading={setIsLoading}
          setResourcesData={setResourcesData}
          linkedServicesData={linkedServicesData}
          connectionKeysData={connectionKeysData}
          setSelectLinkedServiceId={setSelectLinkedServiceId}
          isAggregatedBaseResource={false}
          aggregatedResourceId={resourceId}
          fileStreamIndex={fileStreamIndex}
          isAdditionalResource={false}
          resource_id={resourceData?.resource_id}
          isFromRuleExecution={true}
        />
      ) : (
        <Grid container rowSpacing={2.5} columnSpacing={4}>
          <ViewResourceData
            resourceData={resourceData}
            resourcesData={resourcesData}
            linkedServicesData={linkedServicesData}
            connectionKeysData={connectionKeysData}
            setIsLoading={setIsLoading}
            showAdditionalResource={true}
            resource_id={resourceData?.resource_id}
            setResourcesData={setResourcesData}
            resourceCategory={"resource"}
            viewInlineVariables={viewInlineVariables}
            isReRun={isReRun}
          />
        </Grid>
      )}

      {resourceType === "aggregated" && (
        <Accordion
          expanded={expandedAccordion === "base-resource-data"}
          onChange={handleChangeAccordion("base-resource-data")}
          className="heading-bold mt-16"
        >
          <AccordionSummary
            aria-controls="panel1d-content"
            id="panel1d-header"
            expandIcon={<ExpandMoreIcon />}
          >
            <div className="custom-actions">
              <div className="heading">
                Base Resource Data -{" "}
                <span style={{ fontWeight: "normal" }}>
                  {resourceData?.aggregation_base_resource_data?.resource_name}
                </span>
              </div>
              <div className="action-btns">
                <Tooltip title="Navigate to view resource" placement="top">
                  <button
                    className="view-icon"
                    onClick={(e) =>
                      handleViewResource(
                        domainId,
                        resourceData?.aggregation_base_resource_data
                          ?.resource_id,
                        resourceData?.aggregation_base_resource_data
                          ?.resource_name,
                        e
                      )
                    }
                  >
                    <IconViewSvgWhite />
                  </button>
                </Tooltip>
              </div>
            </div>
          </AccordionSummary>
          <AccordionDetails sx={{ paddingTop: "16px" }}>
            {isResourceEdit === "aggregatedResource" ? (
              <>
                <ReRunEditRuleExecutionTab
                  resourceData={resourceData?.aggregation_base_resource_data}
                  resourcesData={resourcesData}
                  setIsLoading={setIsLoading}
                  setResourcesData={setResourcesData}
                  linkedServicesData={linkedServicesData}
                  connectionKeysData={baseConnectionKeysData}
                  setSelectLinkedServiceId={setBaseSelectLinkedServiceId}
                  isAggregatedBaseResource={true}
                  aggregatedResourceId={resourceId}
                  fileStreamIndex={fileStreamIndex}
                  resource_id={
                    resourceData?.aggregation_base_resource_data?.resource_id
                  }
                  isFromRuleExecution={true}
                />
              </>
            ) : (
              <Grid container rowSpacing={2.5} columnSpacing={4}>
                <ViewResourceData
                  resourceData={resourceData?.aggregation_base_resource_data}
                  resourcesData={resourcesData}
                  linkedServicesData={linkedServicesData}
                  connectionKeysData={baseConnectionKeysData}
                  // fileProcessingData={fileProcessingData}
                  setIsLoading={setIsLoading}
                  showAdditionalResource={true}
                  resource_id={resourceData?.resource_id}
                  setResourcesData={setResourcesData}
                  resourceCategory={"aggregatedResource"}
                />
              </Grid>
            )}
          </AccordionDetails>
        </Accordion>
      )}

      {filterRules?.filter_rules != null &&
        filterRules?.filter_rules?.length > 0 && (
          <CustomAccordion
            expandId={`panel-filter-${resourceData}-header`}
            title="Filter Rules"
            handleChangeAccordion={handleChangeAccordion}
            isEnabled={true}
          >
            <FilterRules
              formData={filterRules}
              isAddButtonHidden={true}
              setFormData={setFilterFormData}
              isViewOnly={!isEditFilters}
              resourceColumnData={resourceColumnData}
              checkFilterRuleValidation={checkFilterRuleValidation}
              setAllVariablesList={setAllVariablesList}
            />
            <Grid
              item
              xs={12}
              sm={12}
              md
              sx={{
                justifyContent: "flex-end",
                display: "flex",
                marginTop: "8px",
              }}
            >
              {!isEditFilters && (
                <button
                  className="btn-nostyle icon-btn-edit"
                  onClick={() => {
                    setIsEditFilters((prev) => !prev);
                    if (viewInlineVariables?.resource_variables?.length > 0) {
                      viewInlineVariables?.resource_variables.map(
                        (res: any) => {
                          if (res?.resource_id === resourceData.id) {
                            setGlobalVariables(res?.resource_vars ?? {});
                            setResourcesData((prevResourcesData: any) => {
                              const updatedResources = prevResourcesData.map(
                                (resource: any) => {
                                  if (res?.resource_id === resourceData.id) {
                                    return {
                                      ...resource,
                                      additional_properties: {
                                        ...resource.additional_properties,
                                        inline_variables: res?.resource_vars,
                                      },
                                    };
                                  }
                                  return resource;
                                }
                              );
                              return updatedResources;
                            });
                          }
                        }
                      );
                    }
                  }}
                >
                  <IconBtnEditBase />
                </button>
              )}

              {isEditFilters && (
                <Button
                  type="button"
                  onClick={() => {
                    setIsEditFilters((prev) => !prev);
                  }}
                  variant="contained"
                  color="secondary"
                  className="btn-orange"
                  title="Save Resource"
                >
                  <SaveOutlinedIcon /> &nbsp; Save
                </Button>
              )}
            </Grid>
          </CustomAccordion>
        )}
    </>
  );
};

export default ReRunRuleExecutionTab;
