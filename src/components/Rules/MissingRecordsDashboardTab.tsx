import { GridColumnGroupingModel } from "@mui/x-data-grid";
import React, { useEffect, useState } from "react";
import { calculateDynamicWidth, getSanatizedCode } from "../../services/utils";
import { GridAlignment } from "@mui/x-data-grid";
import RulesListDataTable from "../DataGrids/RulesListDataGrid";
import {
  Backdrop,
  Box,
  Button,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";
import { GRID_CHECKBOX_SELECTION_COL_DEF } from "@mui/x-data-grid-pro";
import {
  IconCheckCircleIconGreenSvg,
  IconCrossCircleIconSvg,
  IconFalsePositive,
  IconIncidentDetails,
  IconIncidentResources,
  IconStatusFail,
  IconStatusInprogress,
  IconStatusNew,
  IconStatusPass,
} from "../../common/utils/icons";
import useFetchMissingRecordsWithIssuesByExecutionId from "../../hooks/useFetchMissingRecordsWithIssuesByExecutionId";
import DashboardUserDetails from "../Molecules/Rule/DashboardUserDetails";
import IncidentUserandStatusDialog from "../Molecules/Rule/IncidentUserandStatusDialog";
import useFetchUsersList from "../../hooks/useFetchUsersList";
import Loader from "../Molecules/Loader/Loader";
import StatusCell from "../../common/utils/statusIcon";
import { processIssueAnalysisData } from "../../common/utils/commonFunctions";
import {
  CommentColumn,
  CurrentStatusColumn,
  UserColumn,
} from "../../common/utils/commonActionsFields";

interface MissingRecordsDashboardTabProps {
  dashboardData: any;
  setMissingColumns: any;
  userDialogData?: any;
  setUserDialogData?: any;
}

const MissingRecordsDashboardTab = ({
  dashboardData,
  setMissingColumns,
  userDialogData,
  setUserDialogData,
}: MissingRecordsDashboardTabProps) => {
  const [missingRowsData, setMissingRowsData] = useState<any>([]);
  const [missingColumnsData, setMissingColumnsData] = useState<any>([]);
  const [columnGroupingModel, setColumnGroupingModel] = useState<any>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [allUsersList, setallUsersList] = useState<any>([]);
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const [selectedIds, setSelectedIds] = useState<any>([]);
  const [keyColumns, setKeyColumns] = useState<any>([]);
  const [isAllColumnPinned, setIsAllColumnPinned] = useState(false);

  const [dashboardUserDetailsData, setDashboardUserDetailsData] = useState({
    status: false,
    assigned_user: "",
    issueId: null,
    isBackDropVisible: false,
  });
  const [usersList] = useFetchUsersList({
    setIsLoading,
  });
  useEffect(() => {
    setallUsersList(usersList);
  }, [usersList]);

  const [missingResultsRuleDashboardData] =
    useFetchMissingRecordsWithIssuesByExecutionId({
      setIsLoading,
      isMissingFetch: !dashboardData?.is_detailed_execution_data_available,
      currentRuleResultId: dashboardData?.id,
      page,
      pSize,
    });

  useEffect(() => {
    if (
      missingResultsRuleDashboardData?.items &&
      missingResultsRuleDashboardData?.items.length > 0
    ) {
      const keyColumns = Object.keys(
        missingResultsRuleDashboardData?.items[0]?.missing_record_details
          ?.comparison_keys
      ).map((key, value) => key);

      setKeyColumns(keyColumns);
      setIsLoading(false);
    }
  }, [missingResultsRuleDashboardData]);

  useEffect(() => {
    if (!missingResultsRuleDashboardData || !dashboardData) {
      return;
    }

    const columnGrouping: GridColumnGroupingModel = [
      {
        groupId: "Keys",
        headerName: "Keys",
        headerAlign: "center",
        children: [
          { field: "id" },
          ...(missingResultsRuleDashboardData &&
          missingResultsRuleDashboardData.items &&
          Array.isArray(missingResultsRuleDashboardData.items) &&
          missingResultsRuleDashboardData.items.length > 0
            ? (() => {
                const firstItem = missingResultsRuleDashboardData.items[0];
                const recordData =
                  firstItem?.missing_record_details?.record_snapshot
                    ?.record_data;
                const comparisonKeys =
                  firstItem?.missing_record_details?.comparison_keys;

                if (recordData) {
                  return Object.keys(recordData).map((key) => {
                    return { field: key };
                  });
                } else if (comparisonKeys) {
                  return Object.keys(comparisonKeys).map((key) => ({
                    field: key,
                  }));
                } else {
                  return [];
                }
              })()
            : []),
        ],
      },
      {
        groupId: "Resources",
        headerName: "Resources",
        headerAlign: "center",
        children: Array.isArray(
          dashboardData?.additional_properties?.resource_prefixes
        )
          ? dashboardData.additional_properties.resource_prefixes.map(
              (key: any) => ({
                field: key,
              })
            )
          : [],
      },
      {
        groupId: "Messages",
        headerName: "Messages",
        headerAlign: "center",
        children: [{ field: "message" }],
      },

      {
        groupId: "Actions",
        headerName: "Actions",
        headerAlign: "center",
        children: [
          { field: "action" },
          ...(!isAllColumnPinned
            ? [
                { field: "current_status" },
                { field: "username" },
                { field: "comment" },
              ]
            : []),
        ],
      },
    ];

    setColumnGroupingModel(columnGrouping);
  }, [dashboardData, missingResultsRuleDashboardData, isAllColumnPinned]);

  const getFormattedKeysPairs = (
    inputString: string | undefined,
    type?: "missing"
  ) => {
    const pairs: any = {};

    // Check if inputString is defined and is a string
    if (inputString && typeof inputString === "string") {
      inputString.split(",").forEach((pair) => {
        if (pair.trim() !== "None") {
          const [key, value] = pair.trim().split("=");
          pairs[key] = getSanatizedCode(value);
        }
      });
    }

    return pairs;
  };
  const getFormattedKeysPairs1 = (
    missing_in: any,
    false_positive_missings: any,
    resource_prefixes: any,
    type?: "missing"
  ) => {
    const pairs: any = {};
    if (!missing_in || !resource_prefixes) return {};

    resource_prefixes.forEach((prefix: string) => {
      pairs[prefix] = missing_in.includes(prefix)
        ? false_positive_missings[prefix]
          ? "falsepositive"
          : "falsenegative"
        : "truepositive";
    });

    return pairs;
  };

  const getFormattedKeysPairs2 = (inputString: string, type?: "missing") => {
    const pairs: any = {};

    if (inputString && typeof inputString === "string") {
      inputString.split(",").forEach((pair) => {
        const [key, value] = pair.trim().split("=");

        if (key && value !== undefined) {
          pairs[key] = getSanatizedCode(value, ";");
        }
      });
    }
    return pairs;
  };

  const processRows = (rows: any[]) => {
    if (!rows) return;
    return rows.map((row: any) => {
      const updatedRow = { ...row };

      Object.keys(updatedRow).forEach((key) => {
        if (typeof updatedRow[key] === "string") {
          if (
            /\.0$/.test(updatedRow[key]) &&
            !/\.0[1-9]/.test(updatedRow[key])
          ) {
            updatedRow[key] = updatedRow[key].slice(0, -2);
          }
        }
      });

      return updatedRow;
    });
  };
  const getMissingRows = (
    missing_records_details: any,
    resource_prefixes?: any
  ) => {
    const recordData =
      missing_records_details[0]?.missing_record_details?.record_snapshot
        ?.record_data;
    const rows: any = missing_records_details?.map((item: any, idx: any) => {
      return {
        id: item?.id,
        rowId: item?.id,
        assigned_user: item?.assigned_user,
        current_status: item?.current_status,
        comment: item?.comment,
        ...(recordData
          ? { ...item?.missing_record_details?.record_snapshot?.record_data }
          : { ...item?.missing_record_details?.comparison_keys }),
        ...getFormattedKeysPairs1(
          item?.missing_record_details?.missing_in,
          item?.missing_record_details?.false_positive_missings,
          resource_prefixes,
          "missing"
        ),
        message: item?.missing_record_details?.error_message,
        ...item?.missing_record_details?.file_names,
      };
    });
    const processedRows = processRows(rows);
    setMissingRowsData(processedRows);
  };
  const handleIncidentResources = (id: number) => {
    const prefix = dashboardData?.additional_properties?.resource_prefixes;
    const rowData = missingResultsRuleDashboardData?.items.find(
      (item: any) => item.id === id
    )?.missing_record_details?.record_snapshot?.record_data;

    const result = processIssueAnalysisData(rowData, prefix);

    setDashboardUserDetailsData((prev: any) => ({
      ...prev,
      issueAnalysis: [{ prefix: prefix }, { data: result }],
    }));
  };

  const getMissingColumns = (
    missing_records_details: any,
    resource_prefixes?: any
  ) => {
    let finalColumns;
    const recordData =
      missing_records_details[0]?.missing_record_details?.record_snapshot
        ?.record_data;
    const resourceKeys = resource_prefixes ? resource_prefixes : [];
    const resource_prifix_columns: any = resourceKeys.map((item: any) => {
      return {
        field: item,
        headerName: item,
        headerAlign: "center",
        align: "center",
        flex: 1,
        minWidth: 120,
        renderCell: (params: any) => {
          if (params.row[item] == "truepositive") {
            return <IconCheckCircleIconGreenSvg width={21} height={21} />;
          }
          if (params.row[item] == "falsepositive") {
            return (
              <>
                <IconFalsePositive />
              </>
            );
          }
          if (params.row[item] == "falsenegative") {
            return <IconCrossCircleIconSvg width={21} height={21} />;
          }
        },
      };
    });
    const message: any = {
      field: "message",
      headerName: "Message",
      flex: 1,
      minWidth: 350,
      headerAlign: "center",
      renderCell: (params: any) => {
        const regex = /;(?![\w\s])/g;
        return (
          <span className="text-ellipsis">
            <Tooltip
              title={
                <Typography sx={{ fontSize: "0.875rem" }}>
                  {params.value.replace(regex, "")}
                </Typography>
              }
              placement="top"
              className="tooltip-ellipsis"
            >
              {params.value.replace(regex, "")}
            </Tooltip>
          </span>
        );
      },
    };
    const resColumns: any = [
      CurrentStatusColumn({}),
      UserColumn({}),
      CommentColumn({}),
      {
        field: "action",
        headerName: "Action",
        align: "center",
        headerAlign: "center",
        minWidth: "130",
        pinned: "right",
        renderCell: (params: any, index: any) => {
          return (
            <>
              <Box sx={{ display: "inline-flex", columnGap: "12px" }}>
                <Tooltip
                  title={`Incident Details : ${params?.row?.id}`}
                  placement="top"
                  arrow
                >
                  <Button
                    className="btn-blue btn-orange btn-sm"
                    onClick={(event) => {
                      event.stopPropagation();
                      setDashboardUserDetailsData((prev: any) => ({
                        ...prev,
                        status: true,
                        incidentId: params.row.rowId,
                        assigned_user: params.row.assigned_user,
                        panelType: "incidentDetail",
                        isBackDropVisible: true,
                        comment: [
                          {
                            comment: params.row.comment,
                            create_date: params.row.create_date,
                            assigned_user: params.row?.assigned_user || "",
                          },
                        ],
                        current_status: params.row.current_status,
                        issueId: params.row.id,
                      }));
                    }}
                  >
                    <IconIncidentDetails />
                  </Button>
                </Tooltip>
                <Tooltip title="Issue Analysis" placement="top" arrow>
                  <Button
                    className="btn-blue btn-orange btn-sm"
                    onClick={(event) => {
                      event.stopPropagation();
                      setDashboardUserDetailsData((prev: any) => ({
                        ...prev,
                        status: true,
                        panelType: "incidentResource",
                        isBackDropVisible: true,
                      }));
                      handleIncidentResources(params?.row?.id);
                    }}
                  >
                    <IconIncidentResources />
                  </Button>
                </Tooltip>
              </Box>
            </>
          );
        },
      },
    ];
    const columns: any = Object.keys(
      missing_records_details[0]?.missing_record_details?.comparison_keys
    ).map((item: any) => {
      return {
        field: item,
        headerName: getSanatizedCode(item),
        flex: 1,
        minWidth: 100,
        headerAlign: "center",
        align: "center",
        renderCell: (params: any) => {
          const fileNames = Object.keys(params.row)
            .filter((key) => key.includes("_file_name"))
            .map((key) => params.row[key])
            .filter((fileName) => fileName);

          const fileNamesDisplay =
            fileNames.length > 0 ? (
              <div>
                <strong>File name(s):</strong>
                <br />
                {fileNames.map((fileName, index) => {
                  const tempElement = document.createElement("div");
                  tempElement.innerHTML = fileName;
                  const fileNamesText =
                    tempElement.textContent || tempElement.innerText;
                  const individualFileNames =
                    fileNamesText.split(/\.xlsx|\.xls/);
                  return individualFileNames.map((fileName, subIndex) => {
                    const trimmedFileName = fileName.trim();
                    if (trimmedFileName) {
                      return (
                        <li key={`${index}-${subIndex}`}>
                          {trimmedFileName}.xlsx
                        </li>
                      );
                    }
                    return null;
                  });
                })}
              </div>
            ) : null;
          return (
            <span className="text-ellipsis">
              <Tooltip
                title={
                  <Typography sx={{ fontSize: "0.875rem" }}>
                    <ul className="pl-20">{fileNamesDisplay}</ul>
                  </Typography>
                }
                placement="top"
                className="tooltip-ellipsis"
              >
                {params.value}
              </Tooltip>
            </span>
          );
        },
      };
    });

    if (recordData) {
      const snapshotColumns = recordData
        ? Object.keys(recordData).map((key) => {
            const dynamicWidth = calculateDynamicWidth(
              key,
              missing_records_details,
              "missing"
            );
            return {
              field: key,
              headerName: key,
              flex: 1,
              minWidth: dynamicWidth > 120 ? dynamicWidth : 120,
            };
          })
        : [];
      const comparisonKeys =
        missing_records_details[0]?.missing_record_details?.comparison_keys;
      const filtredSnapShot = snapshotColumns.filter(
        (column) => !Object.keys(comparisonKeys).includes(column.field)
      );
      finalColumns = [
        ...columns,
        ...resource_prifix_columns,
        message,
        ...filtredSnapShot,
        ...resColumns,
      ];
    } else {
      finalColumns = [
        ...columns,
        ...resource_prifix_columns,
        message,
        ...resColumns,
      ];
    }
    setMissingColumnsData(finalColumns);
  };

  useEffect(() => {
    // missing records
    if (
      missingResultsRuleDashboardData?.items?.length > 0 &&
      dashboardData?.additional_properties?.resource_prefixes
    ) {
      getMissingColumns(
        missingResultsRuleDashboardData?.items,
        dashboardData?.additional_properties?.resource_prefixes
      );
      getMissingRows(
        missingResultsRuleDashboardData?.items,
        dashboardData?.additional_properties?.resource_prefixes
      );
    }
  }, [missingResultsRuleDashboardData, dashboardData]);
  React.useEffect(() => {
    setUserDialogData((prev: any) => ({
      ...prev,
      selectedIds,
    }));
  }, [selectedIds]);
  const [pinnedColumns, setPinnedColumns] = useState({
    left: [GRID_CHECKBOX_SELECTION_COL_DEF.field],
    right: ["current_status", "username", "comment", "action"],
  });

  useEffect(() => {
    setPinnedColumns((prevState) => {
      const updatedLeft = [
        GRID_CHECKBOX_SELECTION_COL_DEF.field,
        ...keyColumns,
      ];
      const updatedRight = [...prevState.right];

      return {
        left: updatedLeft,
        right: updatedRight,
      };
    });
  }, [keyColumns]);

  const handleMoveColumns = () => {
    setPinnedColumns((prevState) => {
      const columnsToAdd = ["current_status", "username", "comment"];
      const isColumnsAvailable = columnsToAdd.every((item) =>
        prevState.right.includes(item)
      );
      const isKeyColumnAvailable = keyColumns.every((item: any) =>
        prevState.left.includes(item)
      );
      setIsAllColumnPinned(isColumnsAvailable);
      if (isColumnsAvailable && isKeyColumnAvailable) {
        return {
          left: prevState.left.filter((item) => !keyColumns.includes(item)),
          right: prevState.right.filter((item) => !columnsToAdd.includes(item)),
        };
      } else {
        return {
          left: [...prevState.left, ...keyColumns],
          right: [...columnsToAdd, ...prevState.right],
        };
      }
    });
  };

  return (
    <>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={dashboardUserDetailsData?.isBackDropVisible ?? false}
      ></Backdrop>
      <Loader isLoading={isLoading} />
      {keyColumns && (
        <>
          <Box className="position-relative">
            {missingRowsData.length > 0 && (
              <IconButton
                onClick={handleMoveColumns}
                className={`icon-pin-datagrid navbar-icon burger  ${
                  isAllColumnPinned ? "active" : ""
                }`}
              >
                <div className="bar bar1"></div>
                <div className="bar bar2"></div>
                <div className="bar bar3"></div>
              </IconButton>
            )}
            <RulesListDataTable
              selectedIds={selectedIds}
              setSelectedIds={setSelectedIds}
              dataRows={missingRowsData || []}
              dataColumns={missingColumnsData || []}
              loading={isLoading}
              dataListTitle="Rules List"
              disableColumnFilter={true}
              className="dataTable no-radius pt-0 bdr-top-0  datatable-column-sep checkbox-result-column pinnedColumnHeaders-bg-height"
              tableHeight={180}
              disableColumnReorder={true}
              columnGroupingModel={columnGroupingModel}
              setResultColumns={setMissingColumns}
              checkboxSelection={missingRowsData?.length > 0 ? true : false}
              paginationMode="server"
              rowCount={missingResultsRuleDashboardData?.total || 0}
              pageSizeOptions={[25]}
              paginationModel={{
                page: page - 1,
                pageSize: pSize,
              }}
              onPaginationModelChange={(params: any) => {
                if (params.pageSize !== pSize || params.page !== page - 1) {
                  setPage(params.page + 1);
                  setPSize(params.pageSize);
                }
              }}
              pinnedColumns={missingRowsData?.length > 0 ? pinnedColumns : {}}
              disableColumnPinning={false}
              singlePageMaxHeightDiff={262}
            />
          </Box>
        </>
      )}
      <DashboardUserDetails
        dashboardUserDetailsData={dashboardUserDetailsData}
        setDashboardUserDetailsData={setDashboardUserDetailsData}
        allUsersList={allUsersList}
        setIsLoading={setIsLoading}
        setDetailViewDataRows={setMissingRowsData}
      />
      <IncidentUserandStatusDialog
        userDialogData={userDialogData}
        setUserDialogData={setUserDialogData}
        allUsersList={allUsersList}
        setDetailViewDataRows={setMissingRowsData}
        setSelectedIds={setSelectedIds}
      />
    </>
  );
};

export default MissingRecordsDashboardTab;
