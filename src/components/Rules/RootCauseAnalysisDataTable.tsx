import { useState, useEffect } from "react";
import RootCauseAnalysisDataGrid from "../DataGrids/RootCauseAnalysisDataGrid";
import { Box, Tooltip } from "@mui/material";
interface IRootCauseAnalysisDataTableProps {
  rootCauseData: any;
  selectedColumns: any;
  isLoading: boolean;
  isFilterHide: boolean;
  resourceKeys: any;
  // resourceIds: any[];
}

const RootCauseAnalysisDataTable = ({
  rootCauseData,
  selectedColumns,
  isLoading,
  isFilterHide,
  resourceKeys,
  // resourceIds,
}: IRootCauseAnalysisDataTableProps) => {
  const [columnsData, setColumnsData] = useState<any>([]);
  const [rowsData, setRowsData] = useState<any>([]);

  useEffect(() => {
    const rows =
      rootCauseData?.result &&
      rootCauseData?.result.map((item: any, index: any) => ({
        id: index,
        ...item,
      }));
    let filteredRows = [];
    const columns = Object.keys(
      rootCauseData?.result &&
        rootCauseData?.result.length > 0 &&
        rootCauseData?.result[0]
    ).map((key) => ({
      field: key,
      headerName: (
        <Tooltip title={`${key}`} placement={"top"} arrow>
          <span>{`${key.slice(0, 15)}${key.length >= 15 ? "..." : ""} `}</span>
        </Tooltip>
      ),
      width: 160,
    }));

    // Get all unique values from base_columns in selectedColumns
    const baseColumns = resourceKeys?.base_columns;
    const valuesToMatch = new Set(
      selectedColumns
        .flatMap((item: { [x: string]: any }) =>
          baseColumns.map((col: string | number) => item[col])
        )
        .filter((val: undefined) => val !== undefined)
    );

    // Filter rows if any value from valuesToMatch is present in any column of the row, excluding 'id' column
    filteredRows = rows.filter(
      (row: { [s: string]: unknown } | ArrayLike<unknown>) =>
        Object.entries(row).some(
          ([key, value]) => key !== "id" && valuesToMatch.has(String(value))
        )
    );
    setColumnsData(columns);
    setRowsData(
      !selectedColumns || selectedColumns.length === 0 ? rows : filteredRows
    );
  }, [rootCauseData, selectedColumns, resourceKeys]);

  return (
    <RootCauseAnalysisDataGrid
      dataRows={rowsData}
      dataColumns={columnsData}
      loading={isLoading}
      // dataListTitle="Rules Dashboard"
      className="dataTable filterMenuBox no-radius pt-0 bdr-top-0"
      disableColumnFilter={true}
      isFilterHide={isFilterHide}
    />
  );
};

export default RootCauseAnalysisDataTable;
