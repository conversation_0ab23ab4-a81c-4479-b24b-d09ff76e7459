import {
  Box,
  Button,
  Dialog,
  DialogActions,
  Grid,
  IconButton,
  Input,
  MenuItem,
  Select,
  Tooltip,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import RulesDataTable from "../DataGrids/RulesDataGrid";
import FlexBetween from "../FlexBetween";
import QueryBuilder from "../QueryBuilder/Index";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useRuleContext } from "../../contexts/RuleContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useToast } from "../../services/utils";
import { IconDeleteBlueSvg } from "../../common/utils/icons";
interface ResourceFilterProps {
  isLoading: boolean;
}
const ResourceFilter = ({ isLoading }: ResourceFilterProps) => {
  //ruleContext imports
  const {
    customFilters,
    setCustomFilters,
    selectedResourceIds,
    setAllVariablesList,
    showQryModal,
    setShowQryModal,
  } = useRuleContext();

  const {
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    setQueryBuilderTempValue,
  } = useRuleResourceContext();

  const { showToast } = useToast();
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [query, setQuery] = useState<any>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const columns: any = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => (
        <IconButton
          className="rotate-arrow"
          size="small"
          tabIndex={-1}
          disabled
        >
          {params?.row?.tolerance_value != null ? (
            <ExpandMoreIcon fontSize="inherit" />
          ) : null}
        </IconButton>
      ),
    },
    {
      headerName: "Sr No",
      field: "id",
      flex: 0.5,
      renderCell: (params: any) => {
        return params.row.id + 1;
      },
    },
    {
      headerName: "Resource",
      field: "resource_id",
      minWidth: 210,
      flex: 1.5,
      renderCell: (params: any) => {
        const isResourceSelected =
          customFilters.find((item: { id: any }) => item.id === params.row.id)
            ?.resource_id !== "";
        const handleChange = (e: any) => {
          const value = e.target.value;
          const resource =
            selectedResourceIds.find((item: any) => item.rId === value) || {};
          const { resource_name: name, code: resource_code } = resource;

          // Find the index of the row to update
          const rowIndex = customFilters.findIndex(
            (item: any) => item.id === params.row.id
          );

          // Create a copy of the array to avoid mutating state directly
          const newCustomFilters = [...customFilters];

          // Update the values for the found row
          if (rowIndex !== -1) {
            newCustomFilters[rowIndex] = {
              ...newCustomFilters[rowIndex],
              resource_id: value,
              resource_code: resource_code,
              name: name,
              sql_query: "",
            };
          }

          setCustomFilters(newCustomFilters);
        };
        return (
          <Tooltip title={params?.row?.name} placement="top" arrow>
            <Select
              value={params.value}
              onChange={handleChange}
              fullWidth
              className={`white-space-nowrap width-250 ${
                !isResourceSelected ? "border-red-parent" : ""
              }`}
              displayEmpty
              input={<Input />}
              renderValue={(selected) => (
                <span>
                  {selectedResourceIds?.find(
                    (item: any) => item.rId === selected
                  )?.resource_name || "Select Resource"}
                </span>
              )}
            >
              {selectedResourceIds.map((item: any) => (
                <MenuItem key={item?.rId} value={item?.rId}>
                  <span style={{ textTransform: "capitalize" }}>
                    {item?.resource_name}
                  </span>
                </MenuItem>
              ))}
            </Select>
          </Tooltip>
        );
      },
    },
    {
      headerName: "SQL Expression",
      field: "sql_query",
      flex: 3,
      renderCell: (params: any) => {
        const rowIndex = customFilters.findIndex(
          (item: { id: any }) => item.id === params.row.id
        );
        const isSqlQuery =
          rowIndex !== -1 && customFilters[rowIndex]?.sql_query !== "";
        const handleChange = (e: any) => {
          const value = e.target.value;
          if (!params.row.resource_id) return;
          setShowQryModal(true);
          if (rowIndex !== -1) {
            setSelectedRow(rowIndex);
          }
          if (value) {
            console.log(value);
            setQueryBuilderTempValue(value);
          }
          const columnNamesSet = new Set();
          const selectedResource = selectedResourceIds.find(
            (item: any) => item.rId === params.row.resource_id
          );
          if (selectedResource?.resource_column_properties) {
            for (const column of selectedResource?.resource_column_properties
              ?.resource_columns) {
              if (column?.column_name && column?.is_active) {
                columnNamesSet.add(column?.column_name);
              }
            }
          }
          const columnNames: any = Array.from(columnNamesSet); // Convert Set back to an array
          setAvailColumns(columnNames);
          setAvailColumnsWithResourceDetail(null);
        };
        return (
          <Input
            onClick={handleChange}
            value={params.value}
            readOnly
            fullWidth
            className={!isSqlQuery ? "border-red-parent" : ""}
            // placeholder="Enter filter query"
          />
        );
      },
    },
    {
      headerName: "Action",
      field: "action",
      width: 100,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        return (
          <Tooltip title="Delete Row" placement="top" arrow>
            <IconButton
              sx={{ color: "grey" }}
              onClick={() => {
                const newCustomFilters = customFilters
                  .filter((item: any) => item.id !== params.row.id)
                  .map((item: any, index: number) => ({ ...item, id: index }));

                // Update id for the remaining items
                setCustomFilters(newCustomFilters);
              }}
              className="datagrid-action-btn"
            >
              <IconDeleteBlueSvg />
            </IconButton>
          </Tooltip>
        );
      },
    },
  ];
  // useEffect(() => {
  //   setTempGlobalVariables(globalVariables);
  // }, [globalVariables]);

  const handleAddFilter = () => {
    if (selectedResourceIds.length === 0) {
      showToast("Select Resource", "error");
      return;
    }
    const initialCustomFilters = customFilters || [];

    const newFilter = {
      key: initialCustomFilters.length,
      id: initialCustomFilters.length,
      name: "",
      resource_id: "",
      resource_code: "",
      sql_query: "",
    };

    setCustomFilters([...initialCustomFilters, newFilter]);
  };

  const handleChangeQuery = (qry: any, columns: any) => {
    setQuery(qry?.trim());
  };

  const handleSaveQuery = () => {
    // setLocalVariables(tempLocalVariables);
    // setGlobalVariables(tempGlobalVariables);

    const notValid = Object.keys(tempGlobalVariables).some(
      (key: any) => tempGlobalVariables[key] === ""
    );
    Object.keys(tempGlobalVariables).forEach((key: any) => {
      if (tempGlobalVariables[key] === "") {
        setErrors((prevError) => ({
          ...prevError,
          [key]: `Please enter ${key}`,
        }));
      } else {
        setErrors((prevError) => ({
          ...prevError,
          [key]: "",
        }));
      }
    });
    if (notValid) return;
    if (query) {
      const newCustomFilters = [...customFilters];
      newCustomFilters[selectedRow].sql_query = query;
      const inlineVariables = Object.keys(tempGlobalVariables).map(
        (key: any) => {
          return {
            name: key,
            value: tempGlobalVariables[key],
          };
        }
      );
      newCustomFilters[selectedRow].inline_variables = inlineVariables;
      setCustomFilters(newCustomFilters);
      setErrors((prevError) => ({
        ...prevError,
        query: "",
      }));
    } else {
      setErrors((prevError) => ({
        ...prevError,
        query: "Please enter query",
      }));
      return;
    }
    // if (notValid) return;
    setGlobalVariables((prev: any) => ({
      ...Object.keys(tempGlobalVariables).reduce(
        (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
        prev
      ),
    }));
    setAllVariablesList((prev: any) => ({
      ...prev,
      rule_Variables: {
        ...prev.rule_Variables,
        ...tempGlobalVariables,
      },
    }));
    setTempGlobalVariables({});
    setQueryBuilderTempValue("");
    setShowQryModal(false);
  };

  const handleCloseQueryModal = () => {
    setShowQryModal(false);
    setSelectedRow(null);
    setQuery(null);
    setQueryBuilderTempValue("");
    setErrors({});
    // setTempGlobalVariables(globalVariables);
    setTempGlobalVariables({});
    // setTempLocalVariables(localVariables);
  };

  return (
    <>
      <RulesDataTable
        dataColumns={columns}
        dataRows={customFilters?.length > 0 ? customFilters : []}
        className="dataTable no-radius"
        checkboxSelection={false}
        loading={isLoading}
        handleAddFilter={handleAddFilter}
        datagridType="ResourceFilter"
      />
      <FlexBetween
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItem: "center",
          gap: "3rem",
          margin: "10px auto",
        }}
      >
        {/* <Button
          variant="contained"
          color="secondary"
          onClick={handleAddFilter}
          className="btn-orange btn-dark"
        >
          <AddSharpIcon /> &nbsp; Filter
        </Button> */}

        <Dialog
          fullWidth
          maxWidth="lg"
          open={showQryModal}
          onClose={handleCloseQueryModal}
          className="main-dailog"
        >
          <Box sx={{ padding: 3 }}>
            <Grid container className="dailog-header">
              <label className="label-text pt-13">Add Query</label>
              <div className="close-icon" onClick={handleCloseQueryModal}></div>
            </Grid>
            <Box className="dailog-body pb-24">
              <Box>
                <Grid container rowSpacing={2.5} columnSpacing={6.25}>
                  <QueryBuilder
                    handleChange={handleChangeQuery}
                    //tempQuery={queryBuilderTempValue}
                    error={errors}
                    setErrors={setErrors}
                  />
                </Grid>
              </Box>
            </Box>
            <DialogActions className="dailog-footer">
              <Button
                color="secondary"
                variant="contained"
                onClick={handleCloseQueryModal}
                className="btn-orange btn-border"
              >
                Cancel
              </Button>
              <Button
                color="secondary"
                variant="contained"
                onClick={handleSaveQuery}
                className="btn-orange"
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </FlexBetween>
    </>
  );
};

export default ResourceFilter;
