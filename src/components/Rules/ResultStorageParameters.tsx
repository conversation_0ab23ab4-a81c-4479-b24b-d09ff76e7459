import {
  Autocomplete,
  Box,
  Button,
  Grid,
  MenuItem,
  <PERSON>Field,
  <PERSON>lt<PERSON>,
  Typography,
} from "@mui/material";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import useFetchConnectionKeyByLinkedServiceId from "../../hooks/useFetchConnectionKeyByLinkedServiceId";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import InfoIcon from "@mui/icons-material/Info";
import useFetchRunInstance from "../../hooks/useFetchRunInstance";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { IconBtnEditBase } from "../../common/utils/icons";

interface IResultStorageParameters {
  resultStorageParameters: any;
  setResultStorageParameters: Dispatch<SetStateAction<any>>;
  isEditResource: boolean;
  setIsEditResource: Dispatch<SetStateAction<boolean>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  isFromRule: boolean;
  isReRun?: boolean;
}
const ResultStorageParameters = ({
  resultStorageParameters,
  setResultStorageParameters,
  isEditResource,
  setIsEditResource,
  setIsLoading,
  isFromRule,
  isReRun,
}: IResultStorageParameters) => {
  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<any>(0);
  const [formData, setFormData] = useState<any>({});
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  const [linkedServicesData] = useFetchLinkedServices({ setIsLoading });
  // const [runInstance] = useFetchRunInstance({ setIsLoading });
  const [connectionKeysData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId,
    setIsLoading,
  });

  useEffect(() => {
    if (resultStorageParameters) {
      setSelectLinkedServiceId(resultStorageParameters?.linked_service_id);
      const newFormData: any = {
        linked_service_id: resultStorageParameters?.linked_service_id || null,
        connection_key_id: resultStorageParameters?.connection_key_id || null,
        file_path: resultStorageParameters?.file_path || null,
        rule_execution_report_name:
          resultStorageParameters?.rule_execution_report_name || null,
        validation_execution_report_name:
          resultStorageParameters?.validation_execution_report_name || null,
      };
      setFormData(newFormData);
      setInitialFormData(JSON.parse(JSON.stringify(newFormData)));
      setIsEditResource(false);
    }
  }, [resultStorageParameters]);

  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);

    setHasChanges(formChanged);
  }, [formData, initialFormData]);

  const handelOnChangeLinkedService = (e: any, value: any) => {
    setSelectLinkedServiceId(value?.id);
    setFormData((prevFormData: any) => ({
      ...prevFormData,
      linked_service_id: value?.id,
      connection_key_id: null,
    }));
  };
  const handelOnChangeConnectionKey = (e: any, value: any) => {
    setFormData((prevFormData: any) => ({
      ...prevFormData,
      connection_key_id: value?.id,
    }));
  };

  const handleChangeFileName = (name: string, value: any) => {
    setFormData((prevFormData: any) => ({
      ...prevFormData,
      [name]: value,
    }));
  };

  const saveResultStorageParameters = (event: any) => {
    event.preventDefault();
    const newResultStorageParameters = {
      linked_service_id: formData?.linked_service_id,
      linked_service_code: linkedServicesData?.find(
        (option: { id: any }) => option.id == formData?.linked_service_id
      )?.code,
      connection_key_id: formData?.connection_key_id,
      file_path: formData?.file_path,
      rule_execution_report_name: formData?.rule_execution_report_name,
      validation_execution_report_name:
        formData?.validation_execution_report_name,
    };
    setResultStorageParameters(newResultStorageParameters);
    setIsEditResource(false);
  };
  return (
    <>
      {!isEditResource ? (
        <Box>
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                <span className="position-relative">
                  Linked Service
                  {formData?.linked_service_id && (
                    <Tooltip
                      componentsProps={{
                        tooltip: { className: "wide-tooltip w-250" },
                      }}
                      title={
                        formData?.linked_service_id && (
                          <React.Fragment>
                            <Typography color="inherit">
                              Linked Service Code :{" "}
                              {
                                linkedServicesData?.find(
                                  (option: { id: any }) =>
                                    option.id == formData?.linked_service_id
                                )?.code
                              }
                            </Typography>
                          </React.Fragment>
                        )
                      }
                    >
                      <InfoIcon
                        sx={{
                          position: "absolute",
                          top: "50%",
                          transform: "translateY(-50%)",
                          right: "-24px",
                          width: "16px",
                        }}
                      />
                    </Tooltip>
                  )}
                </span>
              </label>
              <div className="form-control">
                {linkedServicesData?.find(
                  (option: { id: any }) =>
                    option.id == formData?.linked_service_id
                )?.name || "NA"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">Connection Key</label>
              <div className="form-control">
                {connectionKeysData?.find(
                  (option: { id: any }) =>
                    option.id == formData?.connection_key_id
                )?.name || "NA"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">File Path</label>
              <div className="form-control break-word">
                {formData?.file_path || "N/A"}
              </div>
            </Grid>
            {isFromRule && (
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">
                  Rule Execution Report Name
                </label>
                <div className="form-control">
                  {formData?.rule_execution_report_name || "N/A"}
                </div>
              </Grid>
            )}
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Validation Execution Report Name
              </label>
              <div className="form-control break-word">
                {formData?.validation_execution_report_name || "N/A"}
              </div>
            </Grid>
            {
              <Grid
                item
                xs={12}
                sm={12}
                md
                sx={{ justifyContent: "flex-end", display: "flex" }}
              >
                <button
                  className="btn-nostyle icon-btn-edit"
                  onClick={() => setIsEditResource(true)}
                >
                  <IconBtnEditBase />
                </button>
              </Grid>
            }
          </Grid>
        </Box>
      ) : (
        <>
          <form onSubmit={saveResultStorageParameters} autoComplete="off">
            <Grid container rowSpacing={1.5} columnSpacing={2.5}>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={linkedServicesData ?? []}
                  getOptionLabel={(option) => option.name || ""}
                  value={
                    linkedServicesData?.find(
                      (option: { id: any }) =>
                        option.id == formData?.linked_service_id
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="linked_service_id"
                      style={{ color: "#000000" }}
                      {...params}
                      label="Linked Service"
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      // required
                    />
                  )}
                  renderOption={(props, option, { selected, index }) => {
                    return (
                      <MenuItem
                        {...props}
                        key={index}
                        sx={{ justifyContent: "space-between" }}
                      >
                        {option?.name}
                      </MenuItem>
                    );
                  }}
                  loadingText="Loading..."
                  onChange={(event, value) =>
                    handelOnChangeLinkedService(event, value)
                  }
                  className="form-control-autocomplete"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={connectionKeysData || []}
                  getOptionLabel={(connectionData) => connectionData.name}
                  onChange={(event, value) =>
                    handelOnChangeConnectionKey(event, value)
                  }
                  value={
                    connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id == formData?.connection_key_id
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="connection_key_id"
                      style={{ color: "#000000" }}
                      {...params}
                      label="Connection Key"
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      // required
                    />
                  )}
                  loadingText="Loading..."
                  className="form-control-autocomplete"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="file_path"
                  name="file_path"
                  fullWidth
                  label="File Path"
                  variant="outlined"
                  className="form-control-autocomplete"
                  onChange={(e: any) =>
                    handleChangeFileName(e.target.name, e.target.value)
                  }
                  // placeholder="Ex: Sample File Path"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.file_path || ""}
                  // required
                />
              </Grid>
              {isFromRule && (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="rule_execution_report_name"
                    name="rule_execution_report_name"
                    fullWidth
                    label="Rule Execution Report Name"
                    variant="outlined"
                    className="form-control-autocomplete"
                    onChange={(e: any) =>
                      handleChangeFileName(e.target.name, e.target.value)
                    }
                    InputLabelProps={{
                      shrink: true,
                    }}
                    value={formData?.rule_execution_report_name || ""}
                  />
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="validation_execution_report_name"
                  name="validation_execution_report_name"
                  fullWidth
                  label="Validation Execution Report Name"
                  variant="outlined"
                  className="form-control-autocomplete"
                  onChange={(e: any) =>
                    handleChangeFileName(e.target.name, e.target.value)
                  }
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.validation_execution_report_name || ""}
                />
              </Grid>
              <Grid
                item
                xs
                sx={{
                  display: "flex",
                  alignItems: "flex-end",
                  justifyContent: "flex-end",
                  columnGap: "20px",
                }}
              >
                <Button
                  color="secondary"
                  variant="contained"
                  onClick={() => {
                    setFormData(JSON.parse(JSON.stringify(initialFormData)));
                    setIsEditResource(false);
                  }}
                  className="btn-orange btn-dark"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  color="secondary"
                  variant="contained"
                  className="btn-orange"
                  disabled={!hasChanges}
                >
                  <SaveOutlinedIcon /> &nbsp; Save
                </Button>
              </Grid>
            </Grid>
          </form>
        </>
      )}
    </>
  );
};

export default ResultStorageParameters;
