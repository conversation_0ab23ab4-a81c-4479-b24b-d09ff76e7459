import { useState, useEffect } from "react";
import RootCauseAnalysisDataGrid from "../DataGrids/RootCauseAnalysisDataGrid";
import { Box, Tooltip } from "@mui/material";
interface IRootCauseAnalysisPaginatedDataTableProps {
  rootCauseData: any;
  selectedColumns: any;
  isLoading: boolean;
  isFilterHide: boolean;
  resourceKeys: any;
  pageN: number;
  setPageN: any;
  pageS: number;
  setPageS: any;
  totalPages: number;
}

const RootCauseAnalysisPaginatedDataTable = ({
  rootCauseData,
  selectedColumns,
  isLoading,
  isFilterHide,
  resourceKeys,
  pageN,
  setPageN,
  pageS,
  setPageS,
  totalPages,
}: IRootCauseAnalysisPaginatedDataTableProps) => {
  const [columnsData, setColumnsData] = useState<any>([]);
  const [rowsData, setRowsData] = useState<any>([]);

  useEffect(() => {
    if (rootCauseData?.length > 0) {
      const rows = rootCauseData.map((item: any, index: any) => ({
        id: index,
        ...item?.research_query_results?.result,
      }));
      let filteredRows = [];
      const columns =
        rootCauseData[0]?.research_query_results?.result &&
        Object.keys(rootCauseData[0]?.research_query_results?.result).map(
          (key) => ({
            field: key,
            headerName: (
              <Tooltip title={`${key}`} placement={"top"} arrow>
                <span>{`${key.slice(0, 15)}${
                  key.length >= 15 ? "..." : ""
                } `}</span>
              </Tooltip>
            ),
            width: 160,
          })
        );

      // Get all unique values from base_columns in selectedColumns
      const baseColumns = resourceKeys?.base_columns;
      const valuesToMatch = new Set(
        selectedColumns
          .flatMap((item: { [x: string]: any }) =>
            baseColumns.map((col: string | number) => item[col])
          )
          .filter((val: undefined) => val !== undefined)
      );

      // Filter rows if any value from valuesToMatch is present in any column of the row, excluding 'id' column
      filteredRows =
        rows &&
        rows.filter((row: { [s: string]: unknown } | ArrayLike<unknown>) =>
          Object.entries(row).some(
            ([key, value]) => key !== "id" && valuesToMatch.has(String(value))
          )
        );
      setColumnsData(columns);
      setRowsData(
        !selectedColumns || selectedColumns.length === 0 ? rows : filteredRows
      );
    } else {
      setColumnsData([]);
      setRowsData([]);
    }
  }, [rootCauseData, selectedColumns, resourceKeys]);

  return (
    <RootCauseAnalysisDataGrid
      dataRows={rowsData || []}
      dataColumns={columnsData || []}
      loading={isLoading}
      className="dataTable filterMenuBox no-radius pt-0 bdr-top-0"
      disableColumnFilter={true}
      isFilterHide={isFilterHide}
      tableHeight={180}
      paginationMode="server"
      rowCount={totalPages || 0}
      pageSizeOptions={[25]}
      paginationModel={{
        page: pageN - 1,
        pageSize: pageS,
      }}
      onPaginationModelChange={(params: any) => {
        if (params.pageSize !== pageS || params.page !== pageN - 1) {
          setPageN(params.page + 1);
          setPageS(params.pageSize);
        }
      }}
    />
  );
};

export default RootCauseAnalysisPaginatedDataTable;
