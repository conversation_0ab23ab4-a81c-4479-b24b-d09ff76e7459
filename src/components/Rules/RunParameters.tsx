import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import {
  Box,
  Button,
  Checkbox,
  Grid,
  Input,
  MenuItem,
  Select,
  TextField,
  FormGroup,
  Autocomplete,
  Tooltip,
} from "@mui/material";
import { severity_level } from "../../services/constants";
import useFetchRunInstance from "../../hooks/useFetchRunInstance";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import {
  IconBtnEditBase,
  IconExistingRunName,
  IconNewRunName,
} from "../../common/utils/icons";
import { addRunInstance } from "../../services/runInstance";
import { addRunInstanceFromExecutionSchema } from "../../schemas";
import { useToast } from "../../services/utils";

interface IRunParameters {
  runParameters: any;
  setRunParameters: Dispatch<SetStateAction<any>>;
  isEditResource: boolean;
  setIsEditResource: Dispatch<SetStateAction<boolean>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  severityColumnNames: any;
  setSeverityColumnNames: Dispatch<SetStateAction<any>>;
  isShow: any;
  isReRun?: any;
}
const RunParameters = ({
  runParameters,
  setRunParameters,
  isEditResource,
  setIsEditResource,
  setIsLoading,
  severityColumnNames,
  setSeverityColumnNames,
  isReRun,
  isShow,
}: IRunParameters) => {
  const { showToast } = useToast();
  const [severityColumnFormData, setSeverityColumnFormData] = useState<any>({});
  const [formData, setFormData] = useState<any>({});
  const [runInstance] = useFetchRunInstance({ setIsLoading });
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [initialSeverityColumnFormData, setInitialSeverityColumnFormData] =
    useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [isOldRunName, setIsOldRunName] = useState<boolean>(true);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [newRunName, setNewRunName] = useState<any>("");
  const newRunNameRef = useRef<HTMLInputElement>(null);
  const [instanceData, setInstanceData] = useState<any>([]);
  useEffect(() => {
    setInstanceData(runInstance);
  }, [runInstance]);

  useEffect(() => {
    if (runParameters) {
      const newFormData: any = {
        no_of_errors_in_response: runParameters?.no_of_errors_in_response,
        no_of_errors_in_output_files:
          runParameters?.no_of_errors_in_output_files,
        skip_duplicate_records: runParameters?.skip_duplicate_records ?? true,
        summary_mode: runParameters?.summary_mode,
        generate_files: runParameters?.generate_files,
        skip_validation: runParameters?.skip_validation,
        validation_severity_level: runParameters?.validation_severity_level,
        run_instance: {
          run_id: runParameters?.run_instance?.run_id || null,
          run_name: runParameters?.run_instance?.run_name || null,
        },
        execute_comparison_research_query:
          runParameters?.execute_comparison_research_query,
        use_secondary_merge_resources:
          runParameters?.use_secondary_merge_resources ?? true,
        store_errors_snapshots_and_create_issues:
          runParameters?.store_errors_snapshots_and_create_issues ?? true,
        keep_downloaded_files: runParameters?.keep_downloaded_files ?? false,
        pull_new_files_from_server:
          runParameters?.pull_new_files_from_server ?? false,
        is_long_running_job: runParameters?.is_long_running_job ?? false,
        save_input_data_file: runParameters?.save_input_data_file,
        isCustomiseSeverity: runParameters?.isCustomiseSeverity,
      };
      setFormData(newFormData);
      setInitialFormData(JSON.parse(JSON.stringify(newFormData)));
      setIsEditResource(false);
    }
  }, [runParameters]);
  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);

    const severityColumnChanged =
      JSON.stringify(severityColumnFormData) !==
      JSON.stringify(initialSeverityColumnFormData);

    setHasChanges(formChanged || severityColumnChanged);
  }, [
    formData,
    severityColumnFormData,
    initialFormData,
    initialSeverityColumnFormData,
  ]);

  useEffect(() => {
    if (severityColumnNames) {
      const newSeverityColumn: any = {
        low: severityColumnNames?.low || null,
        medium: severityColumnNames?.medium || null,
        high: severityColumnNames?.high || null,
      };
      setSeverityColumnFormData(newSeverityColumn);
      setInitialSeverityColumnFormData(newSeverityColumn);
    }
  }, [severityColumnNames]);

  const saveRunParameters = () => {
    const newRunParameters = {
      no_of_errors_in_response: formData?.no_of_errors_in_response || 0,
      no_of_errors_in_output_files: formData?.no_of_errors_in_output_files || 0,
      skip_duplicate_records: formData?.skip_duplicate_records,
      summary_mode: formData?.summary_mode,
      generate_files: formData?.generate_files,
      skip_validation: formData?.skip_validation,
      validation_severity_level: formData?.validation_severity_level,
      run_instance: {
        run_id: formData?.run_instance?.run_id || 1,
        run_name: formData?.run_instance?.run_name || "Legacy",
      },
      save_input_data_file: formData?.save_input_data_file,
      execute_comparison_research_query:
        formData?.execute_comparison_research_query,
      use_secondary_merge_resources: formData?.use_secondary_merge_resources,
      store_errors_snapshots_and_create_issues:
        formData?.store_errors_snapshots_and_create_issues,
      keep_downloaded_files: formData?.keep_downloaded_files,
      pull_new_files_from_server: formData?.pull_new_files_from_server,
      is_long_running_job: formData?.is_long_running_job,
      isCustomiseSeverity: formData?.isCustomiseSeverity,
    };
    const newSeverityColumn: any = {
      low: severityColumnFormData?.low || null,
      medium: severityColumnFormData?.medium || null,
      high: severityColumnFormData?.high || null,
    };
    setSeverityColumnNames(newSeverityColumn);
    setRunParameters(newRunParameters);
    setIsEditResource(false);
  };
  const handleChangeRunInstance = (e: any, value: any) => {
    setFormData((prevFormData: any) => ({
      ...prevFormData,
      run_instance: {
        run_id: value?.id,
        run_name: value?.run_name,
      },
    }));
  };
  const handleChangeNewRunName = (e: any, value: any) => {
    setNewRunName(value);
    validateField(e.target.name, e.target.value);
  };
  const handleFocusTextField = () => {
    setTimeout(() => {
      if (newRunNameRef.current) {
        newRunNameRef.current.focus();
      }
    }, 50);
  };
  const validateField = async (name: any, value: any) => {
    try {
      const partialFormData = { ...newRunName, [name]: value };
      await addRunInstanceFromExecutionSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const handleSaveRunName = async (e: any) => {
    e.preventDefault();
    const reqBody = {
      run_name: newRunName,
      code: newRunName,
    };
    try {
      await addRunInstanceFromExecutionSchema.validate(reqBody, {
        abortEarly: false,
      });
      addRunInstance({
        payload: reqBody,
      })
        .then((response) => {
          if (response) {
            setFormData((prevFormData: any) => ({
              ...prevFormData,
              run_instance: {
                ...prevFormData.run_instance,
                run_name: response?.run_name,
                run_id: response?.id,
              },
            }));

            setInstanceData((prev: any) => [
              {
                code: response.code,
                created_by: response.created_by,
                created_on: response.created_on,
                description: response.description,
                id: response.id,
                is_active: response.is_active,
                modified_by: response.modified_by,
                run_name: response.run_name,
                updated_on: response.updated_on,
              },
              ...prev,
            ]);
            setIsOldRunName(true);
            showToast(`Run Instance created successfully!`, "success");
          }
        })
        .catch((error) => {
          console.error(`Cannot create  run instance`);
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  return (
    <>
      {!isEditResource ? (
        <Box>
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                No Of Errors In Response
              </label>
              <div className="form-control">
                {runParameters?.no_of_errors_in_response}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Validation Severity Level
              </label>
              <div className="form-control break-word">
                <div className="text-capitalize">
                  {runParameters?.validation_severity_level}
                </div>
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                No Of Errors In Output Files
              </label>
              <div className="form-control">
                {runParameters?.no_of_errors_in_output_files}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">Run Name</label>
              <div className="form-control">
                {runParameters?.run_instance?.run_name || "N/A"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">Summary Mode</label>
              <div className="form-control">
                {runParameters?.summary_mode ? "True" : "False"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Generate Output Files
              </label>
              <div className="form-control">
                {runParameters?.generate_files ? "True" : "False"}
              </div>
            </Grid>
            {isShow.isShowSkipValidation && (
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Skip Validation</label>
                <div className="form-control break-word">
                  {runParameters?.skip_validation ? "True" : "False"}
                </div>
              </Grid>
            )}
            {isShow.isShowExecuteResearchQueries && (
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">
                  Execute Research Queries
                </label>
                <div className="form-control">
                  {runParameters?.execute_comparison_research_query
                    ? "True"
                    : "False"}
                </div>
              </Grid>
            )}
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Generate Input Files
              </label>
              <div className="form-control">
                {runParameters?.save_input_data_file ? "True" : "False"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Keep Downloaded Files
              </label>
              <div className="form-control">
                {runParameters?.keep_downloaded_files ? "True" : "False"}
              </div>
            </Grid>

            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Pull New Files From Server
              </label>
              <div className="form-control">
                {runParameters?.pull_new_files_from_server ? "True" : "False"}
              </div>
            </Grid>

            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Skip Duplicate Records
              </label>
              <div className="form-control">
                {runParameters?.skip_duplicate_records ? "True" : "False"}
              </div>
            </Grid>
            {isShow.use_secondary_merge_resources && (
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">
                  Use Secondary Merge Resources
                </label>
                <div className="form-control">
                  {runParameters?.use_secondary_merge_resources
                    ? "True"
                    : "False"}
                </div>
              </Grid>
            )}
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Store Error Snapshots & Create Issues
              </label>
              <div className="form-control">
                {runParameters?.store_errors_snapshots_and_create_issues
                  ? "True"
                  : "False"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">Long Running Job</label>
              <div className="form-control">
                {runParameters?.is_long_running_job ? "True" : "False"}
              </div>
            </Grid>
            {runParameters?.isCustomiseSeverity ? (
              <Grid item xs={12} sm={6} md={6} lg={9} xl={9}>
                <Box className="label-text text-bold">
                  Severity Column Names
                </Box>
                <Grid
                  container
                  rowSpacing={{ lg: 3, md: 0, sm: 0, xs: 0 }}
                  columnSpacing={3}
                >
                  <Grid item xs={12} sm={12} md>
                    <label className="label-text">
                      <span className="text-bold">Low: </span>
                      {severityColumnFormData?.low || "N/A"}
                    </label>
                  </Grid>
                  <Grid item xs={12} sm={12} md>
                    <label className="label-text">
                      <span className="text-bold">Medium: </span>
                      {severityColumnFormData?.medium || "N/A"}
                    </label>
                  </Grid>
                  <Grid item xs={12} sm={12} md>
                    <label className="label-text">
                      <span className="text-bold">High: </span>
                      {severityColumnFormData?.high || "N/A"}
                    </label>
                  </Grid>
                </Grid>
              </Grid>
            ) : null}

            {
              <Grid
                item
                xs
                sx={{ justifyContent: "flex-end", display: "flex" }}
              >
                <button
                  className="btn-nostyle icon-btn-edit"
                  onClick={() => setIsEditResource(true)}
                >
                  <IconBtnEditBase />
                </button>
              </Grid>
            }
          </Grid>
        </Box>
      ) : (
        <>
          <Box>
            <Grid container rowSpacing={1.5} columnSpacing={2.5}>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <TextField
                  type="number"
                  name="no_of_errors_in_response"
                  fullWidth
                  label="No Of Errors In Response"
                  variant="outlined"
                  className="form-control-autocomplete"
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    const inputValue = event.target.value;
                    if (
                      inputValue === "" ||
                      (parseInt(inputValue) >= 0 &&
                        !isNaN(parseInt(inputValue)))
                    ) {
                      setFormData({
                        ...formData,
                        no_of_errors_in_response: inputValue,
                      });
                    }
                  }}
                  // placeholder="Enter No of errors in response"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.no_of_errors_in_response}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <Box
                  sx={{ display: { md: "block", sm: "none", xs: "none" } }}
                  className="label-text line-height16"
                >
                  Validation Severity Level
                </Box>
                <Select
                  className="white-space-nowrap form-control"
                  value={
                    formData?.validation_severity_level
                      ?.charAt(0)
                      .toUpperCase() +
                    formData?.validation_severity_level?.slice(1).toLowerCase()
                  }
                  placeholder="Select..."
                  style={{ width: "100%", height: 35 }}
                  onChange={(event) => {
                    setFormData({
                      ...formData,
                      validation_severity_level: event.target.value,
                    });
                  }}
                  displayEmpty
                  input={<Input />}
                  renderValue={(selected) => (
                    <span>{selected || "Severity Level"}</span>
                  )}
                >
                  {severity_level.map((severity: string) => (
                    <MenuItem key={severity} value={severity}>
                      <span>
                        {severity.charAt(0).toUpperCase() +
                          severity.slice(1).toLowerCase()}
                      </span>
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <TextField
                  type="number"
                  name="no_of_errors_in_output_files"
                  fullWidth
                  label="No Of Errors In Output Files"
                  variant="outlined"
                  className="form-control-autocomplete"
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    const inputValue = event.target.value;
                    if (
                      inputValue === "" ||
                      (parseInt(inputValue) >= 0 &&
                        !isNaN(parseInt(inputValue)))
                    ) {
                      setFormData({
                        ...formData,
                        no_of_errors_in_output_files: inputValue,
                      });
                    }
                  }}
                  // placeholder="Enter No of errors in response"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.no_of_errors_in_output_files}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <Box className="label-text line-height16">
                  {isOldRunName ? "Run Name" : "New Run Name"}{" "}
                </Box>
                <Box className="run-name-update">
                  <Box sx={{ flex: "1" }}>
                    {isOldRunName ? (
                      <Autocomplete
                        fullWidth
                        options={instanceData || []}
                        getOptionLabel={(runInstanceData) =>
                          runInstanceData.run_name
                        }
                        onChange={(event, value) =>
                          handleChangeRunInstance(event, value)
                        }
                        value={
                          instanceData?.find((option: { id: any }) => {
                            return (
                              option?.id === formData?.run_instance?.run_id
                            );
                          }) || null
                        }
                        renderInput={(params) => (
                          <TextField
                            name="run_instance"
                            style={{ color: "#000000" }}
                            {...params}
                            placeholder="Select..."
                            InputLabelProps={{
                              shrink: true,
                            }}
                          />
                        )}
                        loadingText="Loading..."
                        className="form-control-autocomplete"
                        disabled={isReRun}
                      />
                    ) : (
                      <TextField
                        type="text"
                        title="run_name"
                        name="run_name"
                        fullWidth
                        variant="outlined"
                        className={`form-control-autocomplete `}
                        onChange={(event: any) => {
                          handleChangeNewRunName(event, event.target.value);
                        }}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        value={newRunName || ""}
                        error={!!errors?.run_name}
                        helperText={errors?.run_name || ""}
                        inputRef={newRunNameRef}
                        disabled={isReRun}
                      />
                    )}
                  </Box>
                  <Box className="btn-group">
                    {isOldRunName ? (
                      <Button
                        color="secondary"
                        variant="contained"
                        onClick={() => {
                          setIsOldRunName(!isOldRunName);
                          handleFocusTextField();
                        }}
                        className="btn-orange btn-dark"
                        disabled={isReRun}
                      >
                        <Tooltip title="Add new run name" placement="top">
                          <Box>
                            <IconNewRunName />
                          </Box>
                        </Tooltip>
                      </Button>
                    ) : (
                      <>
                        <Button
                          type="submit"
                          color="secondary"
                          variant="contained"
                          className="btn-orange btn-dark"
                          onClick={(e) => handleSaveRunName(e)}
                        >
                          <Tooltip title="Save new run name" placement="top">
                            <Box>
                              <SaveOutlinedIcon />
                            </Box>
                          </Tooltip>
                        </Button>
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => setIsOldRunName(!isOldRunName)}
                          className="btn-orange btn-dark"
                        >
                          <Tooltip
                            title="Add existing run name"
                            placement="top"
                          >
                            <Box>
                              <IconExistingRunName />
                            </Box>
                          </Tooltip>
                        </Button>
                      </>
                    )}
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.summary_mode}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        summary_mode: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Summary Mode</span>
                </label>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.generate_files}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        generate_files: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Generate Output Files</span>
                </label>
              </Grid>
              {isShow.isShowSkipValidation && (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <label className="base-resource-checkbox">
                    <Checkbox
                      checked={formData?.skip_validation}
                      onChange={(
                        event: React.ChangeEvent<HTMLInputElement>
                      ) => {
                        setFormData({
                          ...formData,
                          skip_validation: event.target.checked,
                        });
                      }}
                      sx={{
                        "&.Mui-checked": {
                          color: "#196BB4",
                        },
                      }}
                    />
                    <span>Skip Validation</span>
                  </label>
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.isCustomiseSeverity}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData((prev: any) => ({
                        ...prev,
                        isCustomiseSeverity: event.target.checked,
                      }));
                      if (!event.target.checked) {
                        setSeverityColumnNames({
                          low: null,
                          medium: null,
                          high: null,
                        });
                      }
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Customize Severity Column Names</span>
                </label>
              </Grid>
              {isShow.isShowExecuteResearchQueries && (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <label className="base-resource-checkbox">
                    <Checkbox
                      checked={formData?.execute_comparison_research_query}
                      onChange={(
                        event: React.ChangeEvent<HTMLInputElement>
                      ) => {
                        setFormData({
                          ...formData,
                          execute_comparison_research_query:
                            event.target.checked,
                        });
                      }}
                      sx={{
                        "&.Mui-checked": {
                          color: "#196BB4",
                        },
                      }}
                    />
                    <span>Execute Research Queries</span>
                  </label>
                </Grid>
              )}
              {formData?.isCustomiseSeverity ? (
                <Grid item xs={12} sm={6} md={12} lg={12} xl={12}>
                  <Box
                    sx={{ display: { md: "block", sm: "none", xs: "none" } }}
                    className="label-text"
                  >
                    &nbsp;
                  </Box>
                  <Box
                    sx={{ display: { md: "block", sm: "none", xs: "none" } }}
                    className="label-text"
                  >
                    Severity Column Names
                  </Box>
                  <Grid container rowSpacing={3} columnSpacing={3}>
                    <Grid item xs>
                      <FormGroup row className="form-group-row">
                        <Button variant="contained" disableElevation>
                          <span>Low</span>
                        </Button>
                        <TextField
                          type="text"
                          name="low"
                          variant="outlined"
                          className="form-control-autocomplete"
                          onChange={(
                            event: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            setSeverityColumnFormData({
                              ...severityColumnFormData,
                              low: event.target.value,
                            });
                          }}
                          // placeholder="Ex: Sample severity column name"
                          InputLabelProps={{
                            shrink: true,
                          }}
                          value={severityColumnFormData?.low || ""}
                        />
                      </FormGroup>
                    </Grid>
                    <Grid item xs>
                      <FormGroup row className="form-group-row">
                        <Button variant="contained" disableElevation>
                          <span>Medium</span>
                        </Button>
                        <TextField
                          type="text"
                          name="medium"
                          fullWidth
                          variant="outlined"
                          className="form-control-autocomplete"
                          onChange={(
                            event: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            setSeverityColumnFormData({
                              ...severityColumnFormData,
                              medium: event.target.value,
                            });
                          }}
                          // placeholder="Ex: Sample severity column name"
                          InputLabelProps={{
                            shrink: true,
                          }}
                          value={severityColumnFormData?.medium || ""}
                        />
                      </FormGroup>
                    </Grid>
                    <Grid item xs>
                      <FormGroup row className="form-group-row">
                        <Button variant="contained" disableElevation>
                          <span>High</span>
                        </Button>
                        <TextField
                          type="text"
                          name="high"
                          fullWidth
                          variant="outlined"
                          className="form-control-autocomplete"
                          onChange={(
                            event: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            setSeverityColumnFormData({
                              ...severityColumnFormData,
                              high: event.target.value,
                            });
                          }}
                          // placeholder="Ex: Sample severity column name"
                          InputLabelProps={{
                            shrink: true,
                          }}
                          value={severityColumnFormData?.high || ""}
                        />
                      </FormGroup>
                    </Grid>
                  </Grid>
                </Grid>
              ) : null}

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <div className="downloadable-file">
                  <label
                    htmlFor="use_translation"
                    className="base-resource-checkbox"
                  >
                    <Checkbox
                      id="use_translation"
                      title="use_translation"
                      name="use_translation"
                      className="refrence-checkbox"
                      checked={formData?.save_input_data_file}
                      onChange={(e) => {
                        setFormData((prev: any) => ({
                          ...prev,
                          save_input_data_file: !formData?.save_input_data_file,
                        }));
                      }}
                      sx={{
                        "&.Mui-checked": {
                          color: "#196BB4",
                        },
                      }}
                    />
                    Generate Input Files
                  </label>
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.keep_downloaded_files}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        keep_downloaded_files: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Keep Downloaded Files</span>
                </label>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.pull_new_files_from_server}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        pull_new_files_from_server: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Pull New Files From Server</span>
                </label>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.skip_duplicate_records}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        skip_duplicate_records: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Skip Duplicate Records</span>
                </label>
              </Grid>
              {isShow.use_secondary_merge_resources && (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <label className="base-resource-checkbox">
                    <Checkbox
                      checked={formData?.use_secondary_merge_resources}
                      onChange={(
                        event: React.ChangeEvent<HTMLInputElement>
                      ) => {
                        setFormData({
                          ...formData,
                          use_secondary_merge_resources: event.target.checked,
                        });
                      }}
                      sx={{
                        "&.Mui-checked": {
                          color: "#196BB4",
                        },
                      }}
                    />
                    <span>Use Secondary Merge Resources</span>
                  </label>
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.store_errors_snapshots_and_create_issues}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        store_errors_snapshots_and_create_issues:
                          event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Store Error Snapshots & Create Issues</span>
                </label>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.is_long_running_job}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        is_long_running_job: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Long Running Job</span>
                </label>
              </Grid>
              <Grid
                item
                xs
                sx={{
                  display: "flex",
                  alignItems: "flex-end",
                  justifyContent: "flex-end",
                  columnGap: "20px",
                }}
              >
                <Button
                  color="secondary"
                  variant="contained"
                  onClick={() => {
                    setFormData(JSON.parse(JSON.stringify(initialFormData)));
                    setSeverityColumnFormData(
                      JSON.parse(JSON.stringify(initialSeverityColumnFormData))
                    );
                    setRunParameters(
                      JSON.parse(JSON.stringify(initialFormData))
                    );
                    setIsEditResource(false);
                  }}
                  className="btn-orange btn-dark"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  color="secondary"
                  variant="contained"
                  className="btn-orange"
                  onClick={saveRunParameters}
                  disabled={!hasChanges}
                >
                  <SaveOutlinedIcon /> &nbsp; Save
                </Button>
              </Grid>
            </Grid>
          </Box>
        </>
      )}
    </>
  );
};

export default RunParameters;
