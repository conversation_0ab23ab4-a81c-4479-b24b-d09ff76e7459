import React, { useEffect, useState } from "react";
import RulesListDataTable from "../DataGrids/RulesListDataGrid";
import { calculateDynamicWidth, getSanatizedCode } from "../../services/utils";

import { Backdrop, Box, Button, IconButton, Tooltip } from "@mui/material";
import { GRID_CHECKBOX_SELECTION_COL_DEF } from "@mui/x-data-grid-pro";
import {
  IconIncidentDetails,
  IconIncidentResources,
  IconStatusFail,
  IconStatusInprogress,
  IconStatusNew,
  IconStatusPass,
} from "../../common/utils/icons";
import DashboardUserDetails from "../Molecules/Rule/DashboardUserDetails";
import useFetchUsersList from "../../hooks/useFetchUsersList";
import { useParams } from "react-router-dom";
import useFetchMismatchRecordsWithIssuesByExecutionId from "../../hooks/useFetchMismatchRecordsWithIssuesByExecutionId";
import IncidentUserandStatusDialog from "../Molecules/Rule/IncidentUserandStatusDialog";
import Loader from "../Molecules/Loader/Loader";
import StatusCell from "../../common/utils/statusIcon";
import { processIssueAnalysisData } from "../../common/utils/commonFunctions";
import {
  CommentColumn,
  CurrentStatusColumn,
  UserColumn,
} from "../../common/utils/commonActionsFields";

interface MismatchedRecordsDashboardTabProps {
  dashboardData: any;
  setMismatchedColumns: any;
  userDialogData?: any;
  setUserDialogData: any;
}

const MismatchedRecordsDashboardTab = ({
  dashboardData,
  setMismatchedColumns,
  userDialogData,
  setUserDialogData,
}: MismatchedRecordsDashboardTabProps) => {
  const { ruleResultId }: any = useParams();
  const [mismatchedRowsData, setMismatchedRowsData] = useState<any>([]);
  const [mismatchedColumnsData, setMismatchedColumnsData] = useState<any>([]);
  const [allUsersList, setallUsersList] = useState<any>([]);
  const [currentExecutionId, setCurrentExecutionId] = useState<
    number | null | undefined
  >(null);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const [selectedIds, setSelectedIds] = useState<any>([]);
  const [keyColumns, setKeyColumns] = useState<any>([]);
  const [isAllColumnPinned, setIsAllColumnPinned] = useState(false);

  const [mismatchResults] = useFetchMismatchRecordsWithIssuesByExecutionId({
    setIsLoading,
    isMismatchFetch: !dashboardData?.is_detailed_execution_data_available,
    currentRuleResultId: dashboardData?.id,
    page,
    pSize,
  });
  useEffect(() => {
    if (mismatchResults?.items && mismatchResults?.items.length > 0) {
      const keyColumns = Object.keys(
        mismatchResults?.items[0]?.mismatched_record_details?.comparison_keys
      ).map((key, value) => key);

      setKeyColumns(keyColumns);
    }
  }, [mismatchResults]);

  const [dashboardUserDetailsData, setDashboardUserDetailsData] = useState({
    status: false,
    assigned_user: "",
    issueId: null,
    isBackDropVisible: false,
  });

  const [usersList] = useFetchUsersList({
    setIsLoading,
  });
  useEffect(() => {
    setCurrentExecutionId(ruleResultId);
  }, [ruleResultId]);

  useEffect(() => {
    setallUsersList(usersList);
  }, [usersList]);

  const getFormattedKeysPairs = (
    inputString: string | undefined,
    type?: "missing"
  ) => {
    const pairs: any = {};

    // Check if inputString is defined and is a string
    if (inputString && typeof inputString === "string") {
      inputString.split(",").forEach((pair) => {
        if (pair.trim() !== "None") {
          const [key, value] = pair.trim().split("=");
          pairs[key] = getSanatizedCode(value);
        }
      });
    }

    return pairs;
  };

  const getFormattedKeysPairs2 = (inputString: string, type?: "missing") => {
    const pairs: any = {};

    if (inputString && typeof inputString === "string") {
      inputString.split(",").forEach((pair) => {
        const [key, value] = pair.trim().split("=");

        if (key && value !== undefined) {
          pairs[key] = getSanatizedCode(value, ";");
        }
      });
    }
    return pairs;
  };

  const processRows = (rows: any[]) => {
    if (!rows) return;
    return rows.map((row: any) => {
      const updatedRow = { ...row };

      Object.keys(updatedRow).forEach((key) => {
        if (typeof updatedRow[key] === "string") {
          if (
            /\.0$/.test(updatedRow[key]) &&
            !/\.0[1-9]/.test(updatedRow[key])
          ) {
            updatedRow[key] = updatedRow[key].slice(0, -2);
          }
        }
      });

      return updatedRow;
    });
  };

  const getColumns = (comparison_rule_exec_details: any) => {
    let finalColumns;
    const firstColumns: any = Object.keys(
      comparison_rule_exec_details[0]?.mismatched_record_details
        ?.comparison_keys
    );
    const recordData =
      comparison_rule_exec_details[0]?.mismatched_record_details
        ?.record_snapshot?.record_data;
    const secondColumns: string[] =
      comparison_rule_exec_details
        ?.map((item: any) => item?.check_name)
        ?.filter(
          (value: any, index: any, self: string | any[]) =>
            self.indexOf(value) === index
        ) || [];

    const fileColumns: any = Object.keys(
      comparison_rule_exec_details[0]?.mismatched_record_details?.file_names
    );
    const column_values: any = Object.keys(
      comparison_rule_exec_details[0]?.mismatched_record_details?.column_values
    );
    const handleIncidentResources = (id: number) => {
      const prefix = dashboardData?.additional_properties?.resource_prefixes;
      const rowData = mismatchResults?.items.find((item: any) => item.id === id)
        ?.mismatched_record_details?.record_snapshot?.record_data;
      const result = processIssueAnalysisData(rowData, prefix);
      setDashboardUserDetailsData((prev: any) => ({
        ...prev,
        issueAnalysis: [{ prefix: prefix }, { data: result }],
      }));
    };

    const resColumns: any = [
      CurrentStatusColumn({}),
      UserColumn({}),
      CommentColumn({}),
      {
        field: "action",
        headerName: "Action",
        align: "center",
        headerAlign: "center",
        minWidth: "130",
        pinned: "right",
        renderCell: (params: any, index: any) => {
          return (
            <>
              <Box sx={{ display: "inline-flex", columnGap: "12px" }}>
                <Tooltip
                  title={`Incident Details : ${params?.row?.id}`}
                  placement="top"
                  arrow
                >
                  <Button
                    className="btn-blue btn-orange btn-sm"
                    onClick={(event) => {
                      event.stopPropagation();
                      setDashboardUserDetailsData((prev: any) => {
                        return {
                          ...prev,
                          status: true,
                          incidentId: params.row.rowId,
                          assigned_user: params.row.assigned_user,
                          panelType: "incidentDetail",
                          isBackDropVisible: true,
                          comment: [
                            {
                              comment: params.row.comment,
                              create_date: params.row.create_date,
                              assigned_user: params.row?.assigned_user || "",
                            },
                          ],
                          current_status: params.row.current_status,
                          issueId: params.row.rowId,
                        };
                      });
                    }}
                  >
                    <IconIncidentDetails />
                  </Button>
                </Tooltip>
                <Tooltip title="Issue Analysis" placement="top" arrow>
                  <Button
                    className="btn-blue btn-orange btn-sm"
                    onClick={(event) => {
                      event.stopPropagation();
                      setDashboardUserDetailsData((prev: any) => ({
                        ...prev,
                        status: true,
                        panelType: "incidentResource",
                        isBackDropVisible: true,
                      }));
                      handleIncidentResources(params?.row?.id);
                    }}
                  >
                    <IconIncidentResources />
                  </Button>
                </Tooltip>
              </Box>
            </>
          );
        },
      },
    ];
    const columnFirst: any = [...firstColumns].map((item: any) => {
      return {
        field: item,
        headerName: getSanatizedCode(item),
        flex: 1,
        minWidth: 110,
        renderCell: (params: any) => {
          return <div className="long-string-width">{params.value}</div>;
        },
      };
    });
    const column_values_data: any = [...column_values].map((item: any) => {
      return {
        field: item,
        headerName: `${item} values`,
        flex: 1,
        minWidth: 110,
        renderCell: (params: any) => {
          return <div className="long-string-width">{params.value}</div>;
        },
      };
    });
    const columnFile: any = [...fileColumns].map((item: any) => {
      const dynamicWidth = calculateDynamicWidth(
        item,
        comparison_rule_exec_details,
        "missmatched"
      );

      return {
        field: item,
        headerName: getSanatizedCode(item),
        flex: 1,
        minWidth: dynamicWidth > 120 ? dynamicWidth : 120,
        renderCell: (params: any) => (
          <div
            className="data-grid-cell word-break-all"
            dangerouslySetInnerHTML={{ __html: params.value }}
          />
        ),
      };
    });
    if (recordData) {
      const snapshotColumns = recordData
        ? Object.keys(recordData).map((key, value) => {
            const dynamicWidth = calculateDynamicWidth(
              key,
              comparison_rule_exec_details,
              "missmatched"
            );
            return {
              field: key,
              headerName: key,
              flex: 1,
              minWidth: dynamicWidth > 120 ? dynamicWidth : 120,
            };
          })
        : [];

      const comparisonKeys = [
        ...columnFirst,
        ...column_values_data,
        ...columnFile,
      ];

      const filtredSnapShot = snapshotColumns.filter(
        (column) =>
          !comparisonKeys.some(
            (comparisonColumn) => comparisonColumn.field === column.field
          )
      );

      finalColumns = [
        ...columnFirst,
        ...column_values_data,
        ...columnFile,
        ...filtredSnapShot,
        ...resColumns,
      ];
    } else {
      finalColumns = [
        ...columnFirst,
        ...column_values_data,
        ...columnFile,
        ...resColumns,
      ];
    }
    setMismatchedColumnsData(finalColumns);
  };
  const reverseFormattedKeysPairs = (inputObject: any) => {
    const desiredOutput = Object.entries(inputObject)
      .filter(([key]) => key !== "id")
      .map(([key, value]) => `${key}=${value}`)
      .join(", ");
    return desiredOutput;
  };

  const handleRowData = (comparison_rule_exec_details: any) => {
    const rows: any = [];
    const checkNames: any = [];
    const recordData =
      comparison_rule_exec_details[0]?.mismatched_record_details
        ?.record_snapshot?.record_data;

    comparison_rule_exec_details?.forEach((item: any) => {
      checkNames.push(item?.mismatched_record_details?.check_name);
      if (item) {
        const fileNames = Object.entries(
          item?.mismatched_record_details?.file_names
        ).reduce((acc: any, [key, value]: any) => {
          acc[key] = getSanatizedCode(value, ";");
          return acc;
        }, {});
        const row: any = {
          id: item?.id,
          rowId: item?.id,
          assigned_user: item?.assigned_user,
          current_status: item?.current_status,
          ...(recordData
            ? {}
            : {
                ...item?.mismatched_record_details?.comparison_keys,
                ...fileNames,
              }),
        };
        rows.push(row);
      }
    });
    let updatedRows: any = [];
    let newRow: any = {};

    comparison_rule_exec_details?.forEach((item: any) => {
      if (item) {
        const row = rows.find((row: any) => item?.id === row?.rowId);
        if (recordData) {
          newRow = {
            ...row,
            rowId: item?.id,
            assigned_user: item?.assigned_user,
            comment: item?.comment,
            current_status: item?.current_status,
            ...item?.mismatched_record_details?.column_values,
            ...item?.mismatched_record_details?.record_snapshot?.record_data,
          };
        } else {
          newRow = {
            ...row,
            rowId: item?.id,
            assigned_user: item?.assigned_user,
            comment: item?.comment,
            current_status: item?.current_status,
            ...item?.mismatched_record_details?.column_values,
          };
        }
        updatedRows.push(newRow);
      }
    });
    const commonKeys =
      updatedRows?.length > 0 &&
      Object.keys(updatedRows[0]).filter(
        (key) => !checkNames.includes(key) && key !== "id"
      );

    function mergeObjectsByKey(arr: any, keys: any) {
      const mergedMap = new Map();

      arr.forEach((obj: any) => {
        const key = keys.map((k: any) => obj[k]).join("-");
        if (mergedMap.has(key)) {
          Object.assign(mergedMap.get(key), obj);
        } else {
          mergedMap.set(key, { ...obj });
        }
      });

      return Array.from(mergedMap.values());
    }

    const mergedData = mergeObjectsByKey(updatedRows, commonKeys);
    const processedRows: any = processRows(mergedData);
    setMismatchedRowsData(processedRows);
  };

  useEffect(() => {
    // mismatched records
    if (mismatchResults?.items?.length > 0) {
      getColumns(mismatchResults?.items);
      handleRowData(mismatchResults?.items);
    }
  }, [mismatchResults]);

  React.useEffect(() => {
    setUserDialogData((prev: any) => ({
      ...prev,
      selectedIds,
    }));
  }, [selectedIds]);

  const [pinnedColumns, setPinnedColumns] = useState({
    left: [GRID_CHECKBOX_SELECTION_COL_DEF.field],
    right: ["current_status", "username", "comment", "action"],
  });

  useEffect(() => {
    setPinnedColumns((prevState) => {
      const updatedLeft = [
        GRID_CHECKBOX_SELECTION_COL_DEF.field,
        ...keyColumns,
      ];
      const updatedRight = [...prevState.right];

      return {
        left: updatedLeft,
        right: updatedRight,
      };
    });
  }, [keyColumns]);

  const handleMoveColumns = () => {
    setPinnedColumns((prevState) => {
      const columnsToAdd = ["current_status", "username", "comment"];
      const updatedLeft = [...prevState.left];
      const isColumnsAvailable = columnsToAdd.every((item) =>
        prevState.right.includes(item)
      );
      setIsAllColumnPinned(isColumnsAvailable);
      if (isColumnsAvailable) {
        return {
          left: updatedLeft,
          right: prevState.right.filter((item) => !columnsToAdd.includes(item)),
        };
      } else {
        return {
          left: updatedLeft,
          right: [...columnsToAdd, ...prevState.right],
        };
      }
    });
  };

  return (
    <>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={dashboardUserDetailsData?.isBackDropVisible ?? false}
      ></Backdrop>
      <Loader isLoading={isLoading} />
      {keyColumns && (
        <>
          <Box className="position-relative">
            {mismatchedRowsData.length > 0 && (
              <IconButton
                onClick={handleMoveColumns}
                className={`icon-pin-datagrid navbar-icon burger  ${
                  isAllColumnPinned ? "active" : ""
                }`}
              >
                <div className="bar bar1"></div>
                <div className="bar bar2"></div>
                <div className="bar bar3"></div>
              </IconButton>
            )}

            <RulesListDataTable
              selectedIds={selectedIds}
              setSelectedIds={setSelectedIds}
              dataRows={mismatchedRowsData || []}
              dataColumns={mismatchedColumnsData || []}
              loading={isLoading}
              dataListTitle="Rules Dashboard"
              className="dataTable no-radius pt-0 bdr-top-0 datatable-column-sep checkbox-result-column pinnedColumnHeaders-bg-height"
              disableColumnFilter={false}
              tableHeight={180}
              setResultColumns={setMismatchedColumns}
              checkboxSelection={mismatchedRowsData.length > 0 ? true : false}
              disableColumnReorder={true}
              paginationMode="server"
              rowCount={mismatchResults?.total || 0}
              pageSizeOptions={[25]}
              paginationModel={{
                page: page - 1,
                pageSize: pSize,
              }}
              onPaginationModelChange={(params: any) => {
                if (params.pageSize !== pSize || params.page !== page - 1) {
                  setPage(params.page + 1);
                  setPSize(params.pageSize);
                }
              }}
              singlePageMaxHeightDiff={262}
              pinnedColumns={
                mismatchedRowsData?.length > 0 ? pinnedColumns : {}
              }
              disableColumnPinning={false}
            />
          </Box>
        </>
      )}
      <DashboardUserDetails
        dashboardUserDetailsData={dashboardUserDetailsData}
        setDashboardUserDetailsData={setDashboardUserDetailsData}
        allUsersList={allUsersList}
        setIsLoading={setIsLoading}
        setDetailViewDataRows={setMismatchedRowsData}
      />
      <IncidentUserandStatusDialog
        userDialogData={userDialogData}
        setUserDialogData={setUserDialogData}
        allUsersList={allUsersList}
        setDetailViewDataRows={setMismatchedRowsData}
        setSelectedIds={setSelectedIds}
      />
    </>
  );
};

export default MismatchedRecordsDashboardTab;
