import { <PERSON>, Tabs, Tab, Grid, But<PERSON> } from "@mui/material";
import React, { useState } from "react";
import MismatchedRecordsDashboardTab from "./MismatchedRecordsDashboardTab";
import RootCauseAnalysis from "./RootCauseAnalysis";
import MissingRecordsDashboardTab from "./MissingRecordsDashboardTab";
import AdhocQueryDashboardTab from "./AdhocQueryDashboardTab";
import { useNavigate } from "react-router-dom";

const ComparisonDashboardResults = ({
  dashboardData,
  isLoading,
  setIsLoading,
}: any) => {
  const navigate = useNavigate();
  const [selectedTab, setSelectedTab] = useState<any>("missingRecords");
  const [missingColumns, setMissingColumns] = useState<any>([]);
  const [mismatchedColumns, setMismatchedColumns] = useState<any>([]);
  const [userDialogData, setUserDialogData] = useState<any>({
    status: false,
    dialogName: "",
    selectedIds: [],
  });

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
    setMismatchedColumns([]);
    setMissingColumns([]);
  };

  const tabContent: any = {
    missingRecords: (
      <>
        <div className="datatable-parent-container">
          <MissingRecordsDashboardTab
            dashboardData={dashboardData}
            setMissingColumns={setMissingColumns}
            userDialogData={userDialogData}
            setUserDialogData={setUserDialogData}
          />
        </div>
        {dashboardData?.comparison_research_query_results &&
          dashboardData?.comparison_research_query_results != null && (
            <RootCauseAnalysis
              selectedColumns={missingColumns}
              isLoading={isLoading}
              heading={"Research"}
              dashboardData={dashboardData}
            />
          )}
      </>
    ),
    mismatched: (
      <>
        <MismatchedRecordsDashboardTab
          dashboardData={dashboardData}
          setMismatchedColumns={setMismatchedColumns}
          setUserDialogData={setUserDialogData}
          userDialogData={userDialogData}
        />
        {dashboardData?.comparison_research_query_results &&
          dashboardData?.comparison_research_query_results != null && (
            <RootCauseAnalysis
              selectedColumns={mismatchedColumns}
              isLoading={isLoading}
              heading={"Research"}
              dashboardData={dashboardData}
            />
          )}
      </>
    ),

    adhocQuery: (
      <Box className="adhock-query-accordion-wrapper">
        {<AdhocQueryDashboardTab dashboardData={dashboardData} />}
      </Box>
    ),
  };
  return (
    <>
      <Box className="mui-tabs-title mt-8">Results</Box>
      <Grid container className="tabs-with-butttons">
        <Grid item xs>
          <Tabs
            value={selectedTab}
            onChange={handleChange}
            textColor="secondary"
            indicatorColor="secondary"
            className="mui-tabs no-t-lr-radius min-height-0 alternative-1"
          >
            <Tab value="missingRecords" label="Missing Records" />
            <Tab value="mismatched" label="Mismatched Rows" />
            <Tab value="adhocQuery" label="Adhoc Query" />
          </Tabs>
        </Grid>
        {selectedTab === "missingRecords" || selectedTab === "mismatched" ? (
          <Grid item className="buttons-group">
            <Button
              className="btn-blue btn-orange btn-sm"
              onClick={() => {
                setUserDialogData((prev: any) => ({
                  ...prev,
                  status: true,
                  dialogName: "Update Issue",
                }));
              }}
              disabled={userDialogData?.selectedIds?.length === 0}
            >
              Update Issue
            </Button>

            <Button
              className="btn-blue btn-orange btn-sm"
              onClick={() => {
                navigate("/incident-reporting-rerun-report");
              }}
            >
              Historical Trail
            </Button>
          </Grid>
        ) : null}
      </Grid>

      <Box>{tabContent[selectedTab]}</Box>
    </>
  );
};

export default ComparisonDashboardResults;
