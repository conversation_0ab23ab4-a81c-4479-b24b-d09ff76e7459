import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Tooltip,
  Button,
  CircularProgress,
  Backdrop,
} from "@mui/material";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import useFetchConnectionKeyByLinkedServiceId from "../../hooks/useFetchConnectionKeyByLinkedServiceId";
import ViewLinkedServices from "../Resource/ViewLinkedServices";
import EditRuleExecutionTab from "./EditRuleExecutionTab";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { sqlDatabaseType } from "../../services/constants";
import CustomAccordion from "../Molecules/Accordian/CustomAccordion";
import FilterRules from "../Resource/FilterRules";
import { useNavigate, useParams } from "react-router-dom";
import { IconBtnEditBase, IconViewSvgWhite } from "../../common/utils/icons";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useResourceContext } from "../../contexts/ResourceContext";
import { filterRulesNameSchema, filterRulesSqlSchema } from "../../schemas";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import { useToast } from "../../services/utils";
import { IconPencilGray, IconPlusGray } from "../../common/utils/icons";
import { IResourceDetail } from "../../types/resource";
import { getResourceDetail } from "../../services/resourcesService";

interface IResourceData {
  resourceData: any;
  resourcesData: any;
  isLoading: boolean;
  setResourcesData: Dispatch<SetStateAction<any>>;
  setOriginalResourcesData: Dispatch<SetStateAction<any>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  fileProcessingData: any;
  fileStreamIndex?: number;
  setViewInlineVariables?: any;
  viewInlineVariables: any;
  resource_type: string;
  resourcePath: string;
  setSecondaryMergeResourceDialog?: Dispatch<SetStateAction<any>>;
  setResourceDataWithSecMerge?: Dispatch<SetStateAction<any>>;
  setIsEditSecondaryResource?: Dispatch<SetStateAction<any>>;
  isFromQueryExecution?: boolean;
}
interface FilterFormData {
  filter_rules: any[];
}

const RuleExecutionTab = ({
  resourcesData,
  resourceData,
  isLoading,
  setIsLoading,
  setResourcesData,
  setOriginalResourcesData,
  fileProcessingData,
  fileStreamIndex,
  setViewInlineVariables,
  viewInlineVariables,
  resource_type,
  resourcePath,
  setSecondaryMergeResourceDialog,
  setResourceDataWithSecMerge,
  setIsEditSecondaryResource,
  isFromQueryExecution = false,
}: IResourceData) => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { domainId } = useParams();
  const {
    isResourceEdit,
    setIsResourceEdit,
    setGlobalVariables,
    linkedServicesData,
    fetchedAllConnectionKeys,
  } = useRuleResourceContext();
  const { setResourceColumnData, resourceColumnData, errors, setErrors } =
    useResourceContext();
  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<number>(0);
  const [resourceId, setResourceId] = useState(0);
  const [expandedAccordion, setExpandedAccordion] = useState<any>("");
  const [isEditFilters, setIsEditFilters] = useState(false);
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [filterRules, setFilterRules] = useState<any>({
    filter_rules: [],
  });
  const [currentResourceColumnId, setCurrentResourceColumnId] =
    useState<any>(null);

  const [filterFormData, setFilterFormData] = useState<
    FilterFormData | undefined
  >(undefined);
  const [isResourceLoading, setIsResourceLoading] = useState<boolean>(false);

  const [connectionKeysData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId,
    setIsLoading,
  });

  const [resourceColumn] = useFetchResourceColumnsById({
    resourceColumnId: currentResourceColumnId,
    setIsLoading,
  });

  useEffect(() => {
    if (resourceColumn && Object.keys(resourceColumn)?.length) {
      setResourceColumnData(resourceColumn);
    }
  }, [resourceColumn]);

  useEffect(() => {
    if (resourceData) {
      if (resourceData?.aggregation_type === "aggregated") {
        setResourceId(resourceData?.id);
      }
      setSelectLinkedServiceId(resourceData?.linked_service_id);
    }
    setCurrentResourceColumnId(
      resourceData?.additional_properties?.resource_column_details_id
    );
    const filter_rules = resourceData?.additional_properties?.filter_rules
      ? resourceData?.additional_properties?.filter_rules.map((rule: any) => ({
          ...rule,
          id: Math.random(),
        }))
      : [];
    setFilterRules({
      ...resourceData?.additional_properties,
      filter_rules: filter_rules,
    });
  }, [resourceData]);

  useEffect(() => {
    if (filterFormData) {
      setResourcesData((prevResourcesData: any) => {
        const updatedResources = prevResourcesData.map((resource: any) => {
          if (resource.id === resourceData.id) {
            return {
              ...resource,
              additional_properties: filterFormData,
            };
          }
          return resource;
        });
        return updatedResources;
      });
    }
  }, [filterFormData]);

  const getUpdatedResourceData = (resourceData: any) => {
    let updatedResourceData: any;

    if (resourceData) {
      const connectionKeyDetail = fetchedAllConnectionKeys?.find(
        (option: { id: any }) =>
          option.id ===
          resourceData?.additional_properties?.resource_definition
            ?.api_definition?.connection_key
      );
      if (
        resourceData.linked_service_id &&
        resourceData?.additional_properties?.resource_definition?.type
      ) {
        let updatedResourceDefinition =
          resourceData?.additional_properties?.resource_definition?.[
            `${resourceData?.additional_properties?.resource_definition?.type}_definition`
          ];
        if (
          sqlDatabaseType.includes(
            resourceData?.additional_properties?.resource_definition?.type
          )
        ) {
          updatedResourceDefinition =
            resourceData?.additional_properties?.resource_definition
              ?.sql_definition;
        }
        updatedResourceData = {
          ...resourceData,
          additional_properties: {
            ...resourceData.additional_properties,
            resource_definition: {
              ...resourceData.additional_properties.resource_definition,
              ...updatedResourceDefinition,
              api_definition: {
                ...resourceData?.additional_properties?.resource_definition
                  ?.api_definition,
                url: connectionKeyDetail?.api_url,
                request_timeout: 0,
              },
            },
          },
        };
      } else {
        updatedResourceData = resourceData;
      }
    }
    return updatedResourceData;
  };
  const updateResourceAtPath = (path: string, updatedResource: any) => {
    const update = (data: any) => {
      if (path === "root") {
        return updatedResource;
      }

      // Split path and handle array indices in format resources[1] or resources.1
      const pathParts = path.split(".").flatMap((part) => {
        // Handle array indices in format resources[1]
        const arrayMatch = part.match(/(\w+)\[(\d+)\]/);
        if (arrayMatch) {
          return [arrayMatch[1], arrayMatch[2]];
        }
        return [part];
      });

      // If the path starts with "resources", we need to handle it differently
      if (pathParts[0] === "resources") {
        // If we have an index after "resources"
        if (pathParts.length > 1 && !isNaN(parseInt(pathParts[1]))) {
          const index = parseInt(pathParts[1]);
          const newData = [...data];
          if (pathParts.length === 2) {
            // If we're just updating the entire resource at this index
            newData[index] = {
              ...newData[index],
              ...updatedResource,
            };
          } else {
            // If we're updating a property within the resource
            const remainingPath = pathParts.slice(2).join(".");
            const nestedUpdate = (data: any) => {
              const newData = { ...data };
              let current = newData;
              const remainingParts = remainingPath.split(".");

              for (let i = 0; i < remainingParts.length - 1; i++) {
                const part = remainingParts[i];
                if (Array.isArray(current)) {
                  const index = parseInt(part);
                  if (isNaN(index)) {
                    throw new Error(`Invalid array index: ${part}`);
                  }
                  current = current[index];
                } else {
                  current = current[part];
                }
              }

              const lastPart = remainingParts[remainingParts.length - 1];
              if (
                typeof current[lastPart] === "object" &&
                current[lastPart] !== null
              ) {
                current[lastPart] = {
                  ...current[lastPart],
                  ...updatedResource,
                };
              } else {
                current[lastPart] = updatedResource;
              }

              return newData;
            };
            newData[index] = nestedUpdate(newData[index]);
          }
          return newData;
        }
      }

      const newData = Array.isArray(data) ? [...data] : { ...data };
      let current = newData;

      // Skip "root" keyword
      let i = pathParts[0] === "root" ? 1 : 0;

      // Traverse until the second last part
      for (; i < pathParts.length - 1; i++) {
        const part = pathParts[i];

        // Handle array indices
        if (Array.isArray(current)) {
          const index = parseInt(part);
          if (isNaN(index)) {
            throw new Error(`Invalid array index: ${part}`);
          }
          current = current[index];
        } else {
          current = current[part];
        }
      }

      const lastPart = pathParts[pathParts.length - 1];

      // Handle the final update
      if (Array.isArray(current)) {
        const index = parseInt(lastPart);
        if (isNaN(index)) {
          throw new Error(`Invalid array index: ${lastPart}`);
        }
        current[index] = {
          ...current[index],
          ...updatedResource,
        };
      } else {
        if (
          typeof current[lastPart] === "object" &&
          current[lastPart] !== null
        ) {
          current[lastPart] = {
            ...current[lastPart],
            ...updatedResource,
          };
        } else {
          current[lastPart] = updatedResource;
        }
      }

      return newData;
    };

    setResourcesData((prev: any) => update(prev));
    setOriginalResourcesData((prev: any) => update(prev));
  };
  const handleChangeAccordion =
    (panel: string, resource_id?: any) =>
    async (event: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
      if (resourceData && resourceData?.id) {
        setExpandedAccordion(isExpanded ? panel : null);
        return;
      }

      setExpandedAccordion(isExpanded ? panel : null);
      if (resource_id && isExpanded) {
        try {
          setIsResourceLoading(true);
          const ResourceResult: IResourceDetail = await getResourceDetail({
            currentResourceId: resource_id,
          });

          const updatedResourceData = getUpdatedResourceData(ResourceResult);

          // Use the path-based update function
          updateResourceAtPath(resourcePath, updatedResourceData);
        } catch (error) {
          console.error("Error fetching resource details:", error);
          showToast("Failed to load resource details", "error");
        } finally {
          setIsResourceLoading(false);
        }
      }
    };

  const handleChangeFilterAccordion =
    (panel: string) => (event: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
    };

  const handleViewResource = (domainId: any, resource: any, e: any) => {
    e.preventDefault();
    if (isResourceEdit !== "") {
      return;
    }
    navigate(`/resource/${domainId || "all"}/view/${resource}`);
  };
  const checkFilterRuleValidation = async (
    updatedFormData: any,
    schemaType: any
  ) => {
    const getSchemaType =
      schemaType === "name" ? filterRulesNameSchema : filterRulesSqlSchema;
    for (let index = 0; index < updatedFormData.filter_rules.length; index++) {
      const element = updatedFormData.filter_rules[index];
      try {
        await getSchemaType.validate(element, { abortEarly: false });
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] = undefined;
        } else {
          updatedJsonData.filter_rules.push({ [updateKey]: undefined });
        }
        setErrors(updatedJsonData);
      } catch (validationErrors: any) {
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] =
            validationErrors.message;
        } else {
          updatedJsonData.filter_rules.push({
            [updateKey]: validationErrors.message,
          });
        }
        setErrors(updatedJsonData);
      }
    }
  };

  useEffect(() => {
    setViewInlineVariables((prev: { resource_variables: any[] }) => {
      const resourceToUpdate = prev?.resource_variables?.find(
        (item: { resource_id: any }) => item?.resource_id === resourceData?.id
      );
      const updatedResource = {
        ...resourceToUpdate,
        resource_vars: {
          ...resourceToUpdate?.resource_vars,
          ...allVariablesList?.resource,
        },
      };

      const newArr = JSON.parse(JSON.stringify(prev?.resource_variables));
      return {
        ...prev,
        resource_variables: newArr
          ? newArr.map((item: { resource_id: any }) =>
              item?.resource_id === resourceToUpdate?.resource_id
                ? { ...updatedResource }
                : item
            )
          : [...prev?.resource_variables, updatedResource],
      };
    });
  }, [allVariablesList, setViewInlineVariables]);

  return (
    <>
      <Accordion
        className={`mt-8 heading-bold box-shadow ${
          resourceData?.isSecondaryResource
            ? "execution-secondary-resource"
            : ""
        }`}
        key={`main-resource-${resourceData?.id}`}
        expanded={expandedAccordion}
        onChange={
          resource_type === "aggregated"
            ? handleChangeAccordion(
                resource_type,
                resourceData?.base_resource_id
              )
            : handleChangeAccordion(resource_type, resourceData?.resource_id)
        }
      >
        <AccordionSummary
          aria-controls={`panel-${resourceData?.id}-content`}
          id={`panel-${resourceData?.id}-header`}
          expandIcon={<ExpandMoreIcon />}
        >
          <div className="custom-actions">
            <div className="heading">
              {resource_type === `main-resource-${resourceData?.id}`
                ? resourceData?.resource_name
                : resource_type === "aggregated"
                ? `Base Resource Data-${resourceData?.base_resource_id}`
                : `Additional Resource Data-${resourceData?.resource_id}`}
            </div>
            {
              <div className="action-btns">
                {resource_type == `main-resource-${resourceData?.id}` &&
                  !isFromQueryExecution &&
                  !resourceData?.isSecondaryResource &&
                  (resourceData?.secondary_merge_resource?.secondaryResId ? (
                    <>
                      <Tooltip
                        title="Override secondary resource"
                        placement="top"
                      >
                        <Button
                          onClick={(e: any) => {
                            e.stopPropagation();

                            if (setSecondaryMergeResourceDialog) {
                              setSecondaryMergeResourceDialog(true);
                            }

                            if (setResourceDataWithSecMerge) {
                              setResourceDataWithSecMerge(resourceData);
                            }

                            if (setIsEditSecondaryResource) {
                              setIsEditSecondaryResource(true);
                            }
                          }}
                        >
                          <IconPencilGray />
                        </Button>
                      </Tooltip>
                    </>
                  ) : (
                    <Tooltip title="Add secondary resource" placement="top">
                      <Button
                        onClick={(e: any) => {
                          e.stopPropagation();

                          if (setSecondaryMergeResourceDialog) {
                            setSecondaryMergeResourceDialog(true);
                          }

                          if (setResourceDataWithSecMerge) {
                            setResourceDataWithSecMerge({
                              ...resourceData,
                              secondary_merge_resource: {
                                secondary_merge_resource: {
                                  resource_id: null,
                                  merge_on: [],
                                },
                              },
                            });
                          }

                          if (setIsEditSecondaryResource) {
                            setIsEditSecondaryResource(false);
                          }
                        }}
                      >
                        <IconPlusGray />
                      </Button>
                    </Tooltip>
                  ))}
                <Tooltip title="Navigate to view resource" placement="top">
                  <button
                    className="view-icon"
                    onClick={(e) =>
                      handleViewResource(
                        resourceData?.domain_id,
                        resource_type === "aggregated"
                          ? resourceData?.base_resource_id
                          : resource_type ===
                            `main-resource-${resourceData?.id}`
                          ? resourceData?.id
                          : resourceData?.resource_id,
                        e
                      )
                    }
                  >
                    <IconViewSvgWhite />
                  </button>
                </Tooltip>
              </div>
            }
          </div>
        </AccordionSummary>
        <AccordionDetails sx={{ paddingTop: "16px" }}>
          {isResourceEdit === resourcePath ? (
            <EditRuleExecutionTab
              resourceData={resourceData}
              resourcesData={resourcesData}
              setIsLoading={setIsLoading}
              setResourcesData={setResourcesData}
              linkedServicesData={linkedServicesData}
              connectionKeysData={connectionKeysData}
              setSelectLinkedServiceId={setSelectLinkedServiceId}
              isAggregatedBaseResource={false}
              aggregatedResourceId={resourceId}
              fileStreamIndex={fileStreamIndex}
              isAdditionalResource={false}
              resource_id={resourceData?.id}
              isFromRuleExecution={true}
              resourcePath={resourcePath}
            />
          ) : (
            <Grid
              container
              rowSpacing={2.5}
              columnSpacing={4}
              sx={{ position: "relative" }}
            >
              <ViewLinkedServices
                resourceData={resourceData}
                linkedServicesData={linkedServicesData}
                connectionKeysData={connectionKeysData}
                fileProcessingData={fileProcessingData}
              />
              <Backdrop
                sx={{
                  color: "#fff",
                  zIndex: 9999,
                  position: "absolute",
                  backgroundColor: "transparent",
                }}
                open={isResourceLoading}
              >
                <CircularProgress
                  sx={{ color: "#000000" }}
                  size={60}
                  thickness={4}
                />
              </Backdrop>
              <Grid
                item
                xs={12}
                sm={12}
                md
                sx={{
                  justifyContent: "flex-end",
                  display: "flex",
                  padding: "20px",
                }}
              >
                <button
                  className="btn-nostyle icon-btn-edit"
                  onClick={() => {
                    if (isResourceEdit === "") {
                      setIsResourceEdit(resourcePath);
                    } else {
                      showToast("Please save changes first!!", "warning");
                    }
                    if (viewInlineVariables?.resource_variables?.length > 0) {
                      viewInlineVariables?.resource_variables.map(
                        (res: any) => {
                          if (res?.resource_id === resourceData.id) {
                            setGlobalVariables(res?.resource_vars ?? {});
                            setResourcesData((prevResourcesData: any) => {
                              const updatedResources = prevResourcesData.map(
                                (resource: any) => {
                                  if (res?.resource_id === resourceData.id) {
                                    return {
                                      ...resource,
                                      additional_properties: {
                                        ...resource.additional_properties,
                                        inline_variables: res?.resource_vars,
                                      },
                                    };
                                  }
                                  return resource;
                                }
                              );
                              return updatedResources;
                            });
                          }
                        }
                      );
                    }
                  }}
                >
                  <IconBtnEditBase />
                </button>
              </Grid>
            </Grid>
          )}

          {resourceData?.additional_properties?.additional_resource_data
            ?.length > 0 &&
            resourceData?.additional_properties?.additional_resource_data.map(
              (additionalRes: any, index: any) => (
                <RuleExecutionTab
                  resourcesData={resourcesData}
                  resourceData={additionalRes}
                  isLoading={isLoading}
                  setResourcesData={setResourcesData}
                  setOriginalResourcesData={setOriginalResourcesData}
                  setIsLoading={setIsLoading}
                  fileProcessingData={fileProcessingData}
                  fileStreamIndex={index}
                  setViewInlineVariables={setViewInlineVariables}
                  viewInlineVariables={viewInlineVariables}
                  resource_type={"additional"}
                  resourcePath={`${resourcePath}.additional_properties.additional_resource_data.${index}`}
                />
              )
            )}
          {resourceData?.aggregation_type === "aggregated" && (
            <RuleExecutionTab
              resourcesData={resourcesData}
              resourceData={
                resourceData?.additional_properties?.aggregation_properties
              }
              isLoading={isLoading}
              setResourcesData={setResourcesData}
              setOriginalResourcesData={setOriginalResourcesData}
              setIsLoading={setIsLoading}
              fileProcessingData={fileProcessingData}
              fileStreamIndex={fileStreamIndex}
              setViewInlineVariables={setViewInlineVariables}
              viewInlineVariables={viewInlineVariables}
              resource_type={"aggregated"}
              resourcePath={`${resourcePath}.additional_properties.aggregation_properties`}
            />
          )}
          {filterRules?.filter_rules != null &&
            filterRules?.filter_rules?.length > 0 && (
              <CustomAccordion
                expandId={`panel-filter-${resourceData}-header`}
                title="Filter Rules"
                handleChangeAccordion={handleChangeFilterAccordion}
                isEnabled={true}
              >
                <FilterRules
                  formData={filterRules}
                  isAddButtonHidden={true}
                  setFormData={setFilterFormData}
                  isViewOnly={!isEditFilters}
                  resourceColumnData={resourceColumnData}
                  checkFilterRuleValidation={checkFilterRuleValidation}
                  setAllVariablesList={setAllVariablesList}
                />
                <Grid
                  item
                  xs={12}
                  sm={12}
                  md
                  sx={{
                    justifyContent: "flex-end",
                    display: "flex",
                    marginTop: "8px",
                  }}
                >
                  {!isEditFilters && (
                    <button
                      className="btn-nostyle icon-btn-edit"
                      onClick={() => {
                        setIsEditFilters((prev) => !prev);
                        if (
                          viewInlineVariables?.resource_variables?.length > 0
                        ) {
                          viewInlineVariables?.resource_variables.map(
                            (res: any) => {
                              if (res?.resource_id === resourceData.id) {
                                console.log(res);
                                setGlobalVariables(res?.resource_vars ?? {});
                                setResourcesData((prevResourcesData: any) => {
                                  const updatedResources =
                                    prevResourcesData.map((resource: any) => {
                                      if (
                                        res?.resource_id === resourceData.id
                                      ) {
                                        return {
                                          ...resource,
                                          additional_properties: {
                                            ...resource.additional_properties,
                                            inline_variables:
                                              res?.resource_vars,
                                          },
                                        };
                                      }
                                      return resource;
                                    });
                                  return updatedResources;
                                });
                              }
                            }
                          );
                        }
                      }}
                    >
                      <IconBtnEditBase />
                    </button>
                  )}

                  {isEditFilters && (
                    <Button
                      type="button"
                      onClick={() => {
                        setIsEditFilters((prev) => !prev);
                      }}
                      variant="contained"
                      color="secondary"
                      className="btn-orange"
                      title="Save Resource"
                    >
                      <SaveOutlinedIcon /> &nbsp; Save
                    </Button>
                  )}
                </Grid>
              </CustomAccordion>
            )}
        </AccordionDetails>
      </Accordion>
    </>
  );
};

export default RuleExecutionTab;
