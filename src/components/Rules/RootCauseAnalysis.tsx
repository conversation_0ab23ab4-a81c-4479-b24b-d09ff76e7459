import React, { useEffect, useState } from "react";
import { <PERSON>, IconButton, Tab, Ta<PERSON>, Tooltip } from "@mui/material";
import RootCauseAnalysisDataTable from "./RootCauseAnalysisDataTable";
import { IconFilterSvg } from "../../common/utils/icons";
import useFetchResearchQueryRecordsByRuleResultId from "../../hooks/useFetchResearchQueryRecordsByRuleResultId";
import RootCauseAnalysisPaginatedDataTable from "./RootCauseAnalysisPaginatedDataTable";

interface IRootCauseAnalysisProps {
  selectedColumns: any;
  isLoading: boolean;
  heading: string;
  dashboardData: any;
}

interface INewResearchQueryResults {
  rootCauseAnalysisData: any;
  selectedColumns: any;
  isLoading: boolean;
  isFilterHide: boolean;
  resourceKeys: any;
  setSelectedQueryName: React.Dispatch<React.SetStateAction<any>>;
  dashboardData: any;
  pageN: number;
  setPageN: React.Dispatch<React.SetStateAction<any>>;
  pageS: number;
  setPageS: React.Dispatch<React.SetStateAction<any>>;
  totalPages: number;
}

interface IResearchQueryResults {
  rootCauseAnalysisData: any;
  selectedColumns: any;
  isLoading: boolean;
  isFilterHide: boolean;
  resourceKeys: any;
}
const RootCauseAnalysis = ({
  selectedColumns,
  isLoading,
  heading,
  dashboardData,
}: IRootCauseAnalysisProps) => {
  const [rootCauseAnalysisData, setRootCauseAnalysisData] = useState<any>([]);
  const [resourceKeys, setResourceKeys] = useState<any>([]);
  const [isFilterHide, setIsFilterHide] = useState(true);
  const [isResearchQueryLoading, setIsResearchQueryLoading] =
    useState<boolean>(false);
  const [selectedQueryName, setSelectedQueryName] = useState<any>("");
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [pageN, setPageN] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pageS, setPageS] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  const [researchQueryResults] = useFetchResearchQueryRecordsByRuleResultId({
    setIsLoading: setIsResearchQueryLoading,
    isResearchQueryFetch: !dashboardData?.is_detailed_execution_data_available,
    currentRuleResultId: dashboardData?.id,
    pageN,
    pageS,
    queryName: selectedQueryName,
  });

  useEffect(() => {
    if (researchQueryResults?.items?.length > 0) {
      setRootCauseAnalysisData(researchQueryResults?.items);
    } else if (researchQueryResults?.items?.length === 0) {
      setRootCauseAnalysisData([]);
    }
  }, [researchQueryResults]);

  useEffect(() => {
    if (!dashboardData) return;
    if (
      dashboardData.comparison_research_query_results?.research_query_results &&
      dashboardData?.is_detailed_execution_data_available
    ) {
      setRootCauseAnalysisData(
        dashboardData.comparison_research_query_results.research_query_results
      );
    }

    if (dashboardData.comparison_research_query_results?.resource_keys) {
      setResourceKeys(
        dashboardData.comparison_research_query_results.resource_keys
      );
    }
    setSelectedQueryName(
      dashboardData?.comparison_research_query_results
        ?.research_query_results[0]?.name
    );
  }, [dashboardData]);
  const handleFilterBox = () => {
    setIsFilterHide(!isFilterHide);
  };

  return (
    <>
      <Box className="mui-tabs-title">
        <Tooltip
          title={`${isFilterHide ? "Show Filters" : "Hide Filters"} `}
          placement="top"
        >
          <IconButton className="" onClick={() => handleFilterBox()}>
            <IconFilterSvg />
          </IconButton>
        </Tooltip>
        {heading}
      </Box>
      {dashboardData?.is_detailed_execution_data_available ? (
        <ResearchQueryResults
          rootCauseAnalysisData={rootCauseAnalysisData}
          selectedColumns={selectedColumns}
          isLoading={isLoading}
          isFilterHide={isFilterHide}
          resourceKeys={resourceKeys}
        />
      ) : (
        <NewResearchQueryResults
          rootCauseAnalysisData={rootCauseAnalysisData}
          selectedColumns={selectedColumns}
          isLoading={isResearchQueryLoading}
          isFilterHide={isFilterHide}
          resourceKeys={resourceKeys}
          setSelectedQueryName={setSelectedQueryName}
          dashboardData={dashboardData}
          pageN={pageN}
          setPageN={setPageN}
          pageS={pageS}
          setPageS={setPageS}
          totalPages={researchQueryResults?.total}
        />
      )}
    </>
  );
};

const ResearchQueryResults = ({
  rootCauseAnalysisData,
  selectedColumns,
  isLoading,
  isFilterHide,
  resourceKeys,
}: IResearchQueryResults) => {
  const [selectedQueryTab, setSelectedQueryTab] = useState<any>(0);

  const handleChangeQueryTab = (
    event: React.SyntheticEvent,
    newValue: string
  ) => {
    setSelectedQueryTab(newValue);
  };
  return (
    <>
      <Tabs
        value={selectedQueryTab}
        onChange={handleChangeQueryTab}
        textColor="secondary"
        indicatorColor="secondary"
        className="mui-tabs no-t-lr-radius min-height-0 alternative-1"
        variant="scrollable"
        scrollButtons="auto"
      >
        {rootCauseAnalysisData.length > 0 &&
          rootCauseAnalysisData?.map((item: any, index: number) => (
            <Tab key={index} value={index} label={item.name} />
          ))}
      </Tabs>
      {rootCauseAnalysisData[selectedQueryTab] && (
        <RootCauseAnalysisDataTable
          rootCauseData={rootCauseAnalysisData[selectedQueryTab]}
          selectedColumns={selectedColumns}
          isLoading={isLoading}
          isFilterHide={isFilterHide}
          resourceKeys={resourceKeys}
        />
      )}
    </>
  );
};

const NewResearchQueryResults = ({
  rootCauseAnalysisData,
  selectedColumns,
  isLoading,
  isFilterHide,
  resourceKeys,
  setSelectedQueryName,
  dashboardData,
  pageN,
  setPageN,
  pageS,
  setPageS,
  totalPages,
}: INewResearchQueryResults) => {
  const [selectedQueryTab, setSelectedQueryTab] = useState<any>(0);
  const handleChangeQueryTab = (
    event: React.SyntheticEvent,
    newValue: string
  ) => {
    setSelectedQueryTab(newValue);
    setSelectedQueryName(
      dashboardData?.comparison_research_query_results?.research_query_results[
        newValue
      ]?.name
    );
  };
  return (
    <>
      <Tabs
        value={selectedQueryTab}
        onChange={handleChangeQueryTab}
        textColor="secondary"
        indicatorColor="secondary"
        className="mui-tabs no-t-lr-radius min-height-0 alternative-1"
        variant="scrollable"
        scrollButtons="auto"
      >
        {dashboardData?.comparison_research_query_results
          ?.research_query_results?.length > 0 &&
          dashboardData?.comparison_research_query_results?.research_query_results?.map(
            (item: any, index: number) => (
              <Tab key={index} value={index} label={item?.name} />
            )
          )}
      </Tabs>
      {
        <RootCauseAnalysisPaginatedDataTable
          rootCauseData={rootCauseAnalysisData}
          selectedColumns={selectedColumns}
          isLoading={isLoading}
          isFilterHide={isFilterHide}
          resourceKeys={resourceKeys}
          pageN={pageN}
          setPageN={setPageN}
          pageS={pageS}
          setPageS={setPageS}
          totalPages={totalPages}
        />
      }
    </>
  );
};

export default RootCauseAnalysis;
