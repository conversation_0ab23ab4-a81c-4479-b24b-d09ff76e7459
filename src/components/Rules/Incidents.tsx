import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  IconButton,
  TextareaAutosize,
  Tooltip,
} from "@mui/material";
import React, { useState } from "react";
import {
  AcknowledgeIconSvg,
  RefreshIncidentResolvedIconSvg,
  RefreshIncidentIconSvg,
} from "../../common/utils/icons";
import {
  getResolveIncident,
  postIncidentComments,
} from "../../services/userService";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { toast } from "react-toastify";
import { getFormattedDateTime } from "../../services/utils";
import { useToast } from "../../services/utils";

interface IncidentComment {
  UserName: string;
  Comment: string;
  CommentDate: string;
}

interface IncidentData {
  IncidentName: string;
  IncidentStatus: string;
  IncidentComments: IncidentComment[];
  IncidentId: number;
}

interface IncidentsProps {
  currentIncidentData: IncidentData[];
  currentResultId: any;
  setIsLoading: (loading: boolean) => void;
  setIsTriggereBtnPressed?: any;
  setIsCommentBtnPressed?: any;
}

const Incidents: React.FC<IncidentsProps> = ({
  currentIncidentData,
  currentResultId,
  setIsLoading,
  setIsTriggereBtnPressed,
  setIsCommentBtnPressed,
}) => {
  const { showToast } = useToast();
  const [comment, setComment] = useState("");
  const [errors, setErrors] = useState<any>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const userInfoString = localStorage.getItem("userInfo");
  const userInfo = userInfoString ? JSON.parse(userInfoString) : null;
  const handleChangeComment = (event: any) => {
    if (comment !== "") {
      setErrors((prevError: any) => ({
        ...prevError,
        comments: "",
      }));
    }
    setComment(() => event.target.value);
  };

  const handleAddComment = (event: any) => {
    if (comment === "") {
      setErrors((prevError: any) => ({
        ...prevError,
        comments: "Please Enter Comment",
      }));
      return;
    } else {
      setErrors((prevError: any) => ({
        ...prevError,
        comments: "",
      }));
    }

    const currentResultIdNumber = currentIncidentData[0]?.IncidentId; //Number(currentResultId);
    const reqBody1 = {
      id: 0,
      incidentId: currentResultIdNumber,
      commentDate: new Date().toISOString(),
      userName: userInfo?.username,
      comment: comment,
    };

    postIncidentComments({ payload: reqBody1 })
      .then((response) => {
        showToast("Comment added successfully!", "success");
      })
      .catch((error) => {
        showToast(`Cannot add comment`, "error");
      })
      .finally(() => {
        setComment("");
        setIsCommentBtnPressed((prev: any) => !prev);
      });
  };

  const handleResolveIncidents = async (status: string) => {
    if (isProcessing) return;
    setIsProcessing(true);
    try {
      setIsLoading(true);
      const result = await getResolveIncident(
        currentIncidentData[0]?.IncidentId,
        status
      );
      if (result.IsError) {
        showToast(
          result.ErrorMessage ||
            "An error occurred while resolving the incident.",
          "error"
        );
      } else {
        showToast(
          result.ErrorMessage || status === "Resolve"
            ? "Incident resolved successfully."
            : "Incident acknowledged successfully.",
          "success"
        );
      }
    } catch (err) {
      console.error(err);
      showToast("An unexpected error occurred.", "error");
    } finally {
      setIsLoading(false);
      setIsProcessing(false);
      setIsTriggereBtnPressed((prev: any) => !prev);
    }
  };

  if (currentIncidentData.length === 0) {
    return null; // Return null if there are no incidents to display
  }

  //currentIncidentData[0].IncidentStatus = "Triggered";

  //currentIncidentData[0].IncidentStatus = "Acknowledged";
  //currentIncidentData[0].IncidentStatus = "Resolve";

  return (
    <div
      className={`accordion-panel alternative mb-6px incident-box  ${
        currentIncidentData[0]?.IncidentStatus === "Triggered"
          ? "bg-triggered"
          : currentIncidentData[0]?.IncidentStatus === "Acknowledged"
          ? "bg-acknowledged"
          : ""
      }
        `}
      id="incidentTab"
    >
      <Accordion className="heading-bold">
        <AccordionSummary
          aria-controls="panel2d-content"
          id="panel2d-header"
          expandIcon={<ExpandMoreIcon />}
        >
          <div className="custom-actions">
            <div className="heading">Incidents</div>
            <div className="action-btns mr-15">
              {currentIncidentData[0]?.IncidentStatus === "Triggered" && (
                <Tooltip title="Acknowledge incidents" placement="top" arrow>
                  <>
                    <button
                      className="btn-orange btn-border incident-btns acknowledge"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleResolveIncidents("Acknowledged");
                      }}
                    >
                      <AcknowledgeIconSvg />
                      Acknowledge
                    </button>
                  </>
                </Tooltip>
              )}

              {currentIncidentData[0]?.IncidentStatus === "Acknowledged" && (
                <Tooltip title="Resolve incidents" placement="top" arrow>
                  <button
                    className="btn-orange btn-border incident-btns get-resolve"
                    onClick={(event) => {
                      event.stopPropagation();
                      handleResolveIncidents("Resolve");
                    }}
                  >
                    <RefreshIncidentIconSvg />
                    Resolve
                  </button>
                </Tooltip>
              )}
              {currentIncidentData[0]?.IncidentStatus === "Resolve" && (
                <Tooltip
                  title="Incidents already resolved "
                  placement="top"
                  arrow
                >
                  <button
                    onClick={(event) => {
                      event.stopPropagation();
                    }}
                    className="btn-orange btn-border incident-btns resolved"
                  >
                    <RefreshIncidentResolvedIconSvg />
                    Resolved
                  </button>
                </Tooltip>
              )}
            </div>
          </div>
        </AccordionSummary>
        <AccordionDetails sx={{ paddingTop: "16px" }}>
          <Box className="rule-domain-chart-box mb-3">
            {currentIncidentData?.length > 0 &&
              currentIncidentData.map((incident, index) => (
                <>
                  <Box key={index} className="incidents">
                    <Box className="cols">
                      <Box>
                        <strong>Incident Name</strong> :&nbsp;
                        {incident.IncidentName}
                      </Box>
                      <Box>
                        <strong>Incident Status</strong> :&nbsp;
                        {incident.IncidentStatus}
                      </Box>
                    </Box>
                    <Box className="cols col-gap8"></Box>
                  </Box>
                  <Box className="comment-section">
                    <Box>
                      <h3>Comments</h3>
                    </Box>

                    <Box className="comment-box">
                      <strong className="mb-1">Add a Comment</strong>

                      <Box className="mb-2">
                        <TextareaAutosize
                          className={`form-control-1 textarea ${
                            errors?.comments ? "has-error" : ""
                          }`}
                          minRows={3}
                          maxRows={6}
                          aria-label="minimum height"
                          placeholder="Add a comment"
                          value={comment}
                          name={"comment"}
                          onChange={handleChangeComment}
                        />
                        <Box className="required">{errors?.comments}</Box>
                      </Box>
                      <Box className="d-flex justify-content-end">
                        <Button
                          className="btn-orange btn-sm"
                          onClick={handleAddComment}
                        >
                          Add comment
                        </Button>
                      </Box>
                    </Box>

                    <ul>
                      {incident.IncidentComments.map(
                        (comment, commentIndex) => (
                          <li key={commentIndex}>
                            <Box>
                              <strong>User Name: </strong>
                              {comment.UserName}
                            </Box>
                            <Box>
                              <strong>Comment: </strong> {comment.Comment}
                            </Box>
                            <Box>
                              <strong>Date and time: </strong>
                              {getFormattedDateTime(comment.CommentDate)}
                            </Box>
                          </li>
                        )
                      )}
                    </ul>
                  </Box>
                </>
              ))}
          </Box>
        </AccordionDetails>
      </Accordion>
    </div>
  );
};

export default Incidents;
