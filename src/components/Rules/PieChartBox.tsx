import React from "react";
import { Pie } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, <PERSON>lt<PERSON>, Legend } from "chart.js";
import { LABELS } from "../../services/constants/Rules";
import { Grid } from "@mui/material";
import { IPieData } from "../../types/reports";
import { useNavigate } from "react-router-dom";

ChartJS.register(ArcElement, Tooltip, Legend);

interface IPieChartProps {
  dataType?: string;
  className?: string;
  pieData: IPieData;
  domainId?: number;
}

export const ColorBox = ({ color }: { color: string }) => (
  <label
    style={{
      width: "11px",
      height: "11px",
      backgroundColor: color,
      display: "inline-block",
    }}
  />
);
const MIN_PERCENTAGE = 0.5; // Minimum percentage for segment visibility

const PieChartComponent = ({ counts }: { counts: any }) => {
  const allCountsZero =
    counts.match === 0 &&
    counts.mismatch === 0 &&
    counts.missing === 0 &&
    counts.duplicate === 0;

  const total =
    counts.match + counts.mismatch + counts.missing + counts.duplicate;

  // Convert to percentages
  const matchPercentage = (counts.match / total) * 100 || 0;
  const mismatchPercentage = (counts.mismatch / total) * 100 || 0;
  const missingPercentage = (counts.missing / total) * 100 || 0;
  const duplicatePercentage = (counts.duplicate / total) * 100 || 0;
  const totalPercentage = 100 || 0;

  // Apply minimum threshold
  const adjustedMatchPercentage = {
    value:
      matchPercentage > 0 && matchPercentage < MIN_PERCENTAGE
        ? MIN_PERCENTAGE
        : matchPercentage,
    orignalValue: counts.match,
    label: "Match",
  };

  const adjustedMismatchPercentage = {
    value:
      mismatchPercentage > 0 && mismatchPercentage < MIN_PERCENTAGE
        ? MIN_PERCENTAGE
        : mismatchPercentage,
    orignalValue: counts.mismatch,
    label: "Mismatch",
  };

  const adjustedMissingPercentage = {
    value:
      missingPercentage > 0 && missingPercentage < MIN_PERCENTAGE
        ? MIN_PERCENTAGE
        : missingPercentage,
    orignalValue: counts.missing,
    label: "Missing",
  };

  const adjustedDuplicatePercentage = {
    value:
      duplicatePercentage > 0 && duplicatePercentage < MIN_PERCENTAGE
        ? MIN_PERCENTAGE
        : duplicatePercentage,
    orignalValue: counts.duplicate,
    label: "Duplicate",
  };

  const adjustedTotalPercentage = {
    value:
      totalPercentage > 0 && totalPercentage < MIN_PERCENTAGE
        ? MIN_PERCENTAGE
        : totalPercentage,
    orignalValue: [counts.total],
    label: "Total",
  };

  // If all segments are below the threshold, show a single segment for the total
  const dataValues = allCountsZero
    ? [adjustedTotalPercentage]
    : [
        adjustedMatchPercentage,
        adjustedMismatchPercentage,
        adjustedMissingPercentage,
        adjustedDuplicatePercentage,
      ];

  const data = {
    datasets: [
      {
        data: dataValues,
        backgroundColor: allCountsZero
          ? ["#B3B3B3"]
          : ["#74B856", "#E84336", "#add8e6", "#f7b84b"],
        borderColor: allCountsZero
          ? ["#B3B3B3"]
          : ["#74B856", "#E84336", "#add8e6", "#f7b84b"],
        borderWidth: 0,
      },
    ],
  };

  const options = {
    plugins: {
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const label = context.raw.label || "";
            const value = context.raw.orignalValue || 0;
            return `${label}: ${value}`;
          },
        },
      },
    },
  };

  return allCountsZero && counts.total === 0 ? (
    <svg
      width="95%"
      height="95%"
      viewBox="0 0 252 252"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="126" cy="126" r="126" fill="#B3B3B3" />
    </svg>
  ) : (
    <Pie data={data} options={options} />
  );
};

const PieChartBox: React.FC<IPieChartProps> = ({
  dataType,
  className,
  pieData,
  domainId,
}) => {
  const navigate = useNavigate();

  return (
    <>
      <div className={className}>
        <h3
          className="cursor-pointer"
          onClick={() => {
            navigate(`/resource/${domainId}/view/${pieData.resource_id}`);
          }}
        >
          {pieData.resource_name} ({pieData.resource_id})
        </h3>
        <Grid container columnSpacing={{ xl: 3, lg: 1, md: 1 }}>
          <Grid item xs={12}>
            <div className="legends">
              {LABELS.map((label, index) => (
                <span key={index}>
                  <ColorBox color={label.color} />
                  {pieData?.countsData[label.label.toLowerCase()]}
                </span>
              ))}
            </div>
          </Grid>
        </Grid>
        <Grid
          container
          alignItems={"center"}
          columnSpacing={{ xl: 3, lg: 1, md: 1 }}
        >
          <Grid item xs={9} md={9}>
            {/* <div className="data-name">{dataType} DATA</div> */}

            <div className="records">
              Total Records: {pieData.total_records}
            </div>
            <div className="records">
              Total Common Records: {pieData.total_common_records}
            </div>
            <div className="records">
              Total Unique Records: {pieData.total_unique_records}
            </div>
            <div className="records">
              Total Duplicate Records (removed):
              {pieData.total_duplicate_records}
            </div>
            <div className="records">
              Total Missing Records: {pieData.total_missing_records}
            </div>
            <div className="records">
              False Positive Missing Records Count:
              {pieData.false_positive_missing_records_count}
            </div>
            <div className="records">
              Filtered Records By Rule Filters:{" "}
              {pieData.filtered_records_by_rule_filters}
            </div>
            <div className="records">
              Filtered Records By Resource Filters:{" "}
              {pieData.filtered_records_by_resource_filters}
            </div>
          </Grid>
          <Grid item xs={3} md={3}>
            <PieChartComponent counts={pieData.countsData} />
          </Grid>
        </Grid>
      </div>
    </>
  );
};

export default PieChartBox;
