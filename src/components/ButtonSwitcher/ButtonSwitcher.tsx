import { Box } from "@mui/material";
import React, { useState } from "react";
import "../../styles/button-switcher.scss";

interface ButtonSwitherType {
  buttonsName: any;
  selectedPlan: any;
  setSelectedPlan: any;
  className: any;
}
const ButtonSwitcher = ({
  buttonsName,
  selectedPlan,
  setSelectedPlan,
  className,
}: ButtonSwitherType) => {
  const handleChange = (event: any) => {
    setSelectedPlan(event.target.value.toLowerCase());
  };
  return (
    <Box className={`switches-container ${className}`}>
      {buttonsName.map((item: any) => {
        return (
          <>
            <input
              type="radio"
              id={item.replace(" ", "").toLowerCase()}
              name="switchPlan"
              value={item.replace(" ", "").toLowerCase()}
              checked={selectedPlan.toLowerCase() === item.toLowerCase()}
              onChange={handleChange}
            />
            <label htmlFor={item.replace(" ", "").toLowerCase()}>{item}</label>
          </>
        );
      })}
      <Box className="switch-wrapper">
        <Box className="switch">
          {buttonsName.map((item: any) => {
            return (
              <>
                <Box
                  className={
                    selectedPlan.toLowerCase() ===
                    item.replace(" ", "").toLowerCase()
                      ? "selected"
                      : ""
                  }
                >
                  {item}
                </Box>
              </>
            );
          })}
        </Box>
      </Box>
    </Box>
  );
};

export default ButtonSwitcher;
