import React from "react";
import { useNotifications } from "../contexts/NotificationContext";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  Paper,
  Button,
  IconButton,
  Divider,
} from "@mui/material";
import { DoneAll, Delete } from "@mui/icons-material";

const Notifications: React.FC = () => {
  const {
    notifications,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    unreadCount,
  } = useNotifications();

  const handleMarkAsRead = (notificationId: string) => {
    markAsRead(notificationId);
  };

  return (
    <Paper elevation={3} sx={{ p: 2, maxWidth: 600, mx: "auto", mt: 2 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography variant="h5" gutterBottom>
          Notifications {unreadCount > 0 && `(${unreadCount} unread)`}
        </Typography>
        <Box>
          <Button
            size="small"
            startIcon={<DoneAll />}
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
            sx={{ mr: 1 }}
          >
            Mark All Read
          </Button>
          <Button
            size="small"
            color="error"
            startIcon={<Delete />}
            onClick={clearAllNotifications}
            disabled={notifications.length === 0}
          >
            Clear All
          </Button>
        </Box>
      </Box>
      <Divider sx={{ mb: 2 }} />

      {notifications.length === 0 ? (
        <Typography variant="body1" align="center" sx={{ py: 3 }}>
          No notifications to display
        </Typography>
      ) : (
        <List sx={{ maxHeight: 500, overflow: "auto" }}>
          {notifications.map((notification) => (
            <ListItem
              key={notification.id}
              alignItems="flex-start"
              sx={{
                borderLeft: notification.read ? "none" : "4px solid #1976d2",
                backgroundColor: notification.read
                  ? "transparent"
                  : "rgba(25, 118, 210, 0.08)",
                mb: 1,
                borderRadius: 1,
                transition: "all 0.2s ease",
              }}
            >
              <ListItemText
                primary={
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      variant="subtitle1"
                      component="div"
                      sx={{ fontWeight: notification.read ? "normal" : "bold" }}
                    >
                      {notification.message || "New Notification"}
                    </Typography>
                    {!notification.read && (
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsRead(notification.id);
                        }}
                        title="Mark as read"
                      >
                        <DoneAll fontSize="small" />
                      </IconButton>
                    )}
                  </Box>
                }
                secondary={
                  <>
                    <Typography
                      variant="body2"
                      component="span"
                      display="block"
                    >
                      {notification.message || "N/A"}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(notification.timestamp).toLocaleString()}
                    </Typography>
                  </>
                }
              />
            </ListItem>
          ))}
        </List>
      )}
    </Paper>
  );
};

export default Notifications;
