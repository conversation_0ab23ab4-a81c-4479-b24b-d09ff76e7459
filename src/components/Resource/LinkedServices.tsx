import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Grid,
  TextField,
  Autocomplete,
  TextareaAutosize,
  Tooltip,
  Typography,
  ListItemText,
  MenuItem,
  Select,
  Box,
} from "@mui/material";
// import useFetchFileProcessing from "../../hooks/useFetchFileProcessing";
import AggregatedResource from "./AggregatedResource";
import { formattedJson } from "../../services/utils";
import FileStreamUpload from "../Uploaders/FileStreamUpload";
import axios from "axios";
import InfoIcon from "@mui/icons-material/Info";
import { useToast } from "../../services/utils";
import {
  blobLinkedServiceSchema,
  localLinkedServiceSchema,
  sftpLinkedServiceSchema,
  sqlLinkedServiceSchema,
  basicAuthApiDefinitionSchema,
  tokenAuthApiDefinitionSchema,
  keyAuthApiDefinitionSchema,
  oAuthApiDefinitionSchema,
  noAuthApiDefinitionSchema,
} from "../../schemas";
import { contentType, methodType } from "../../services/constants";
import AddMultiGrid from "../AddMultiGrid";
import SqlQueryDialog from "../Dialogs/SqlQueryDialog";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { getChunkSize, uploadFile } from "../../services/uploadFileService";
interface ILinkedServices {
  formData: any;
  setFormData: Dispatch<SetStateAction<any>>;
  connectionKeysData: any;
  linkedServicesData: any;
  setResourceId: Dispatch<SetStateAction<any>>;
  resourceId: any;
  // allResourcesData: any;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  setSelectLinkedServiceId: Dispatch<SetStateAction<any>>;
  fileStreamIndex?: number;
  errors: any;
  setErrors: Dispatch<SetStateAction<any>>;
  queryParams: any;
  setQueryParams: Dispatch<SetStateAction<any>>;
  urlParams: any;
  setUrlParams: Dispatch<SetStateAction<any>>;
  type: any;
  setType: Dispatch<SetStateAction<any>>;
  allVariablesList: any;
  setAllVariablesList: Dispatch<SetStateAction<any>>;
  resourceColumnData: any;
  setCancelUploadFile: Dispatch<SetStateAction<any>>;
  cancelUploadFile: any;
  setIsLoadingFile: Dispatch<SetStateAction<any>>;
  isLoadingFile: any;
}

const LinkedServices = ({
  formData,
  setFormData,
  connectionKeysData,
  linkedServicesData,
  setResourceId,
  setIsLoading,
  resourceId,
  // allResourcesData,
  setSelectLinkedServiceId,
  fileStreamIndex,
  errors,
  setErrors,
  queryParams,
  setQueryParams,
  urlParams,
  setUrlParams,
  type,
  setType,
  allVariablesList,
  setAllVariablesList,
  resourceColumnData,
  setCancelUploadFile,
  cancelUploadFile,
  isLoadingFile,
  setIsLoadingFile,
}: ILinkedServices) => {
  const {
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    fileProcessingData,
  } = useRuleResourceContext();
  const { showToast } = useToast();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  // const [isLoadingFile, setIsLoadingFile] = useState<boolean>(false);
  // const [allVariablesList, setAllVariablesList] = useState({});
  const [tempGlobalVariables, setTempGlobalVariables] = useState<any>({});
  const [showQryModal, setShowQryModal] = useState<any>(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // const [fileProcessingData] = useFetchFileProcessing({ setIsLoading });

  const handelOnChangeLinkedService = (e: any, value: any) => {
    setSelectLinkedServiceId(value?.id);
    setFormData({
      ...formData,
      linked_service: value,
      resource_definition: {
        ...formData.resource_definition,
        connection_key: null,
        type: value?.sub_type,
        api_definition: {
          method: "get",
          content_type: "application/json",
          request_timeout: 0,
        },
      },
    });
    setType("");
    validateField("linked_service", value);
  };

  const handleResourceDefinition = (name: any, value: any) => {
    setFormData({
      ...formData,
      resource_definition: {
        ...formData.resource_definition,
        [name]: value,
      },
    });
    validateField(name, value);
  };

  const handleApiDefinition = (name: any, value: any) => {
    setFormData({
      ...formData,
      resource_definition: {
        ...formData.resource_definition,
        api_definition: {
          ...formData.resource_definition.api_definition,
          [name]: value,
        },
      },
    });
  };
  const handleFileUpload = async () => {
    if (!selectedFile) {
      alert("Please select a file before uploading.");
      return;
    }

    const allowedExtensions =
      /(\.xlsx|\.xls|\.csv|\.csv.rok|\.rok|\.tsv|\.psv|\.txt|\.gz|\.bz2|\.zip|\.xz|\.zst|\.tar|\.tar.gz|\.tar.xz|\.tar.bz2|\.json)$/i;
    if (!allowedExtensions.exec(selectedFile.name)) {
      showToast("Invalid file type", "error");
      return false;
    }

    setIsLoadingFile(true);

    try {
      // const token = localStorage.getItem("token");
      const response = await getChunkSize();
      // await axios.get(
      //   `${process.env.REACT_APP_CONFIG_API_HOST}/upload-file/chunk-size`,
      //   {
      //     headers: {
      //       Authorization: `Bearer ${token}`,
      //     },
      //   }
      // );

      const chunkSize = response?.data?.chunk_value_in_bytes
        ? parseInt(response.data.chunk_value_in_bytes)
        : 1024 * 1024; // Default to 1MB if the environment variable is not set
      const chunks = Math.ceil(selectedFile.size / chunkSize);
      const currentTimestamp = new Date().getTime();
      const cancelSource = axios.CancelToken.source();
      setCancelUploadFile(cancelSource);

      for (let i = 0; i < chunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, selectedFile.size);
        const chunk = selectedFile.slice(start, end);

        const fileFormData = new FormData();
        const nameWithDateTimeStamp = selectedFile.name
          .split(".")
          .join("_" + currentTimestamp + ".");
        fileFormData.append(
          "file",
          new Blob([chunk], { type: selectedFile.type }),
          nameWithDateTimeStamp
        );
        fileFormData.append("is_first_chunk", i === 0 ? "true" : "false");
        fileFormData.append(
          "is_last_chunk",
          i === chunks - 1 ? "true" : "false"
        );

        try {
          const result = await uploadFile(
            fileFormData,
            cancelSource,
            (progressEvent: { loaded: number }) => {
              const progress =
                ((i * chunkSize + progressEvent.loaded) / selectedFile.size) *
                100;
              const clampedProgress = Math.min(progress, 100);
              setUploadProgress(clampedProgress);
            }
          );
          // await axios.post(
          //   `${process.env.REACT_APP_CONFIG_API_HOST}/upload-file`,
          //   fileFormData,
          //   {
          //     headers: {
          //       Authorization: `Bearer ${token}`,
          //     },
          //     cancelToken: cancelSource.token,
          //     onUploadProgress: (progressEvent) => {
          //       const progress =
          //         ((i * chunkSize + progressEvent.loaded) /
          //           selectedFile.size) *
          //         100;
          //       setUploadProgress(progress);
          //     },
          //   }
          // );

          if (result?.status === 200) {
            const linkedService = linkedServicesData?.find(
              (service: { sub_type: string }) => service.sub_type === "local"
            );

            setFormData((prevFormData: { resource_definition: any }) => ({
              ...prevFormData,
              resource_definition: {
                ...prevFormData.resource_definition,
                type: linkedService?.sub_type,
                resource_path: result.data.folder_path || null,
                file_name: result.data.file_name || null,
              },
            }));

            if (i === chunks - 1) {
              setSelectedFile(null);
              showToast(
                `${nameWithDateTimeStamp} file uploaded successfully`,
                "success"
              );
            }
          }
        } catch (error) {
          if (axios.isCancel(error)) {
            showToast("File upload canceled", "warn");
          } else {
            showToast("Failed to upload chunk", "error");
            console.error("Failed to upload chunk:", error);
          }
          break; // Exit the loop if an error occurs
        }
      }
    } catch (error) {
      showToast("Error during file upload", "error");
      console.error("Error during file upload:", error);
    } finally {
      setIsLoadingFile(false);
      setUploadProgress(0);
    }
  };
  const validateField = async (name: any, value: any) => {
    try {
      let partialFormData;
      let validateFieldName;
      const type = formData?.linked_service?.sub_type;
      if (name === "linked_service" || name === "file_processing_id") {
        partialFormData = { ...formData, [name]: value };
        validateFieldName = name;
      } else {
        partialFormData = {
          resource_definition: {
            [name]: value,
          },
        };
        validateFieldName = `resource_definition.${name}`;
      }
      if (type === "blob") {
        await blobLinkedServiceSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "sftp") {
        await sftpLinkedServiceSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "local") {
        await localLinkedServiceSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else {
        await sqlLinkedServiceSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      }
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const validateApiDefinitionField = async (name: any, value: any) => {
    try {
      const partialFormData = {
        resource_definition: {
          api_definition: {
            [name]: value,
          },
        },
      };
      const validateFieldName = `resource_definition.api_definition.${name}`;
      const type = formData?.linked_service?.sub_type;
      if (type === "basic_auth_api") {
        await basicAuthApiDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "token_auth_api") {
        await tokenAuthApiDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "key_auth_api") {
        await keyAuthApiDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "oauth_api") {
        await oAuthApiDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      } else if (type === "no_auth_api") {
        await noAuthApiDefinitionSchema.validateAt(
          validateFieldName,
          partialFormData
        );
      }
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const onSaveQuery = () => {
    handleResourceDefinition("sql_query", queryBuilderTempValue);
    setAllVariablesList((prev: any) => ({
      ...prev,
      resource: {
        ...prev.resource,
        ...tempGlobalVariables,
      },
    }));

    validateField("sql_query", queryBuilderTempValue);
  };

  return (
    <>
      {formData?.aggregation_type === "flat" ? (
        <>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <label className="label-text line-height16">
              <span className="position-relative">
                Linked Service <span className="required-asterisk">*</span>
                {formData?.linked_service?.id && (
                  <Tooltip
                    componentsProps={{
                      tooltip: { className: "wide-tooltip w-250" },
                    }}
                    title={
                      formData?.linked_service?.id && (
                        <React.Fragment>
                          <Typography color="inherit">
                            Linked Service Code :{" "}
                            {
                              linkedServicesData?.find(
                                (item: any) =>
                                  item?.id === formData?.linked_service?.id
                              )?.code
                            }
                          </Typography>
                        </React.Fragment>
                      )
                    }
                  >
                    <InfoIcon
                      sx={{
                        position: "absolute",
                        top: "50%",
                        transform: "translateY(-50%)",
                        right: "-24px",
                        width: "16px",
                      }}
                    />
                  </Tooltip>
                )}
              </span>
            </label>
            <Autocomplete
              fullWidth
              options={linkedServicesData ?? []}
              getOptionLabel={(option) => option.name || ""}
              value={
                linkedServicesData?.find(
                  (option: { id: any }) =>
                    option.id === formData?.linked_service?.id
                ) || null
              }
              renderInput={(params) => (
                <TextField
                  name="linked_service"
                  style={{ color: "#000000" }}
                  {...params}
                  // label="Linked Service"
                  placeholder="Select..."
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.linked_service}
                  helperText={errors?.linked_service || ""}
                />
              )}
              renderOption={(params, item, { index }) => {
                return (
                  <li
                    {...params}
                    key={index}
                    style={{ paddingTop: "2px", paddingBottom: "2px" }}
                  >
                    <ListItemText>{item.name}</ListItemText>
                  </li>
                );
              }}
              loadingText="Loading..."
              onChange={(event, value) =>
                handelOnChangeLinkedService(event, value)
              }
              className={`form-control-autocomplete ${
                errors?.linked_service ? "has-error" : ""
              }`}
            />
          </Grid>

          {formData?.linked_service?.type === "file" && (
            <>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={connectionKeysData || []}
                  getOptionLabel={(connectionData) => connectionData.name}
                  onChange={(event, value) =>
                    handleResourceDefinition("connection_key", value?.id)
                  }
                  value={
                    connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id ===
                        formData?.resource_definition?.connection_key
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="connection_key"
                      style={{ color: "#000000" }}
                      {...params}
                      label={
                        <span>
                          Connection Key
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.connection_key}
                      helperText={errors?.connection_key}
                    />
                  )}
                  renderOption={(props, option, { selected, index }) => {
                    return (
                      <MenuItem
                        {...props}
                        key={index}
                        sx={{ justifyContent: "space-between" }}
                      >
                        {option?.name}
                      </MenuItem>
                    );
                  }}
                  loadingText="Loading..."
                  className={`form-control-autocomplete ${
                    errors?.connection_key ? "has-error" : ""
                  }`}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  <span className="position-relative">
                    File Processing Attribute
                    <span className="required-asterisk">*</span>
                    {formData?.file_processing_id && (
                      <Tooltip
                        componentsProps={{
                          tooltip: { className: "wide-tooltip w-380" },
                        }}
                        title={
                          <pre
                            style={{
                              whiteSpace: "pre-wrap",
                              margin: 0,
                              maxHeight: "200px",
                              overflowY: "auto",
                            }}
                          >
                            <React.Fragment>
                              <Typography color="inherit">
                                File Processing Detail
                              </Typography>
                              <Typography>
                                {formattedJson(
                                  JSON.stringify(
                                    fileProcessingData?.find(
                                      (file: any) =>
                                        file?.id ===
                                        formData?.file_processing_id
                                    )
                                  )
                                )}
                              </Typography>
                            </React.Fragment>
                          </pre>
                        }
                      >
                        <InfoIcon
                          sx={{
                            position: "absolute",
                            top: "50%",
                            transform: "translateY(-50%)",
                            right: "-24px",
                            width: "16px",
                          }}
                        />
                      </Tooltip>
                    )}
                  </span>
                </label>
                <Autocomplete
                  fullWidth
                  options={fileProcessingData}
                  getOptionLabel={(option) => option.resource_type || ""}
                  renderInput={(params) => (
                    <TextField
                      name="file_processing_id"
                      style={{ color: "#000000" }}
                      {...params}
                      // label="File Processing Attributes"
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.file_processing_id}
                      helperText={errors?.file_processing_id || ""}
                    />
                  )}
                  loadingText="Loading..."
                  onChange={(event, value) => {
                    setFormData({
                      ...formData,
                      file_processing_id: value?.id,
                      file_processing_code: value?.code,
                    });
                    validateField("file_processing_id", value?.id);
                  }}
                  value={
                    fileProcessingData?.find(
                      (option: { id: any }) =>
                        option.id === formData?.file_processing_id
                    ) || null
                  }
                  className={`form-control-autocomplete ${
                    errors?.file_processing_id ? "has-error" : ""
                  }`}
                />
              </Grid>

              {formData?.linked_service?.sub_type === "sftp" ? (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="remote_directory"
                    name="remote_directory"
                    fullWidth
                    label={
                      <span>
                        Remote Directory
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className="form-control-autocomplete"
                    onChange={(e: any) =>
                      handleResourceDefinition(e.target.name, e.target.value)
                    }
                    placeholder="Ex: Sample Remote Directory"
                    InputLabelProps={{
                      shrink: true,
                    }}
                    value={
                      formData?.resource_definition?.remote_directory || ""
                    }
                    error={!!errors?.remote_directory}
                    helperText={errors?.remote_directory || ""}
                  />
                </Grid>
              ) : (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <TextField
                    type="text"
                    title="resource_path"
                    name="resource_path"
                    fullWidth
                    label={
                      <span>
                        Resource Path
                        <span className="required-asterisk">*</span>
                      </span>
                    }
                    variant="outlined"
                    className={`form-control-autocomplete ${
                      errors?.resource_path ? "has-error" : ""
                    }`}
                    onChange={(e: any) =>
                      handleResourceDefinition(e.target.name, e.target.value)
                    }
                    placeholder="Ex: Sample Resource Path"
                    InputLabelProps={{
                      shrink: true,
                    }}
                    value={formData?.resource_definition?.resource_path || ""}
                    error={!!errors?.resource_path}
                    helperText={errors?.resource_path || ""}
                  />
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="column_delimiter"
                  name="column_delimiter"
                  fullWidth
                  label={
                    <span>
                      Column Delimiter
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.column_delimiter ? "has-error" : ""
                  }`}
                  onChange={(e: any) =>
                    handleResourceDefinition(e.target.name, e.target.value)
                  }
                  // placeholder="Ex: Sample Column Delimiter"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.resource_definition?.column_delimiter || ""}
                  error={!!errors?.column_delimiter}
                  helperText={errors?.column_delimiter || ""}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="file_name"
                  name="file_name"
                  fullWidth
                  label={
                    <span>
                      File Name<span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.file_name ? "has-error" : ""
                  }`}
                  onChange={(e: any) =>
                    handleResourceDefinition(e.target.name, e.target.value)
                  }
                  // placeholder="Ex: Sample File Name"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.resource_definition?.file_name || ""}
                  error={!!errors?.file_name}
                  helperText={errors?.file_name || ""}
                />
              </Grid>
              {(formData?.linked_service?.sub_type === "local" ||
                formData?.linked_service?.sub_type === "blob" ||
                formData?.linked_service?.sub_type === "sftp") &&
                formData?.resource_definition?.file_name &&
                /\.(xlsx|xls)$/i.test(
                  formData?.resource_definition?.file_name
                ) && (
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="excel_sheet_name"
                      name="excel_sheet_name"
                      fullWidth
                      label={<span>Excel Sheet Name</span>}
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.excel_sheet_name ? "has-error" : ""
                      }`}
                      onChange={(e: any) =>
                        handleResourceDefinition(e.target.name, e.target.value)
                      }
                      InputLabelProps={{
                        shrink: true,
                      }}
                      value={
                        formData?.resource_definition?.excel_sheet_name || ""
                      }
                      error={!!errors?.excel_sheet_name}
                      helperText={errors?.excel_sheet_name || ""}
                    />
                  </Grid>
                )}
            </>
          )}
          {formData?.linked_service?.type === "sql" && (
            <>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={connectionKeysData || []}
                  getOptionLabel={(connectionData) => connectionData.name}
                  onChange={(event, value) =>
                    handleResourceDefinition("connection_key", value?.id)
                  }
                  value={
                    connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id ===
                        formData?.resource_definition?.connection_key
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="connection_key"
                      style={{ color: "#000000" }}
                      {...params}
                      label={
                        <span>
                          Connection Key
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.connection_key}
                      helperText={errors?.connection_key || ""}
                    />
                  )}
                  renderOption={(props, option, { selected, index }) => {
                    return (
                      <MenuItem
                        {...props}
                        key={index}
                        sx={{ justifyContent: "space-between" }}
                      >
                        {option?.name}
                      </MenuItem>
                    );
                  }}
                  loadingText="Loading..."
                  className={`form-control-autocomplete ${
                    errors?.connection_key ? "has-error" : ""
                  }`}
                />
              </Grid>
              <Grid
                item
                xs={12}
                sm={6}
                md={4}
                lg={4}
                xl={4}
                style={{ display: "none" }}
              >
                <TextField
                  type="text"
                  title="Connection String"
                  name="connectionString"
                  fullWidth
                  label={
                    <span>
                      Connection String
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className="form-control-autocomplete"
                  value={formData?.resource_definition?.connection_string}
                  InputLabelProps={{
                    shrink: formData?.resource_definition?.connection_string
                      ? true
                      : false,
                  }}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  SQL definition <span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  value={formData?.resource_definition?.sql_query || ""}
                  onClick={() => {
                    setShowQryModal(true);
                    if (formData?.resource_definition?.sql_query) {
                      setQueryBuilderTempValue(
                        formData?.resource_definition?.sql_query
                      );
                    }
                    if (
                      resourceColumnData &&
                      Object.keys(resourceColumnData)?.length
                    ) {
                      const resourceColumns =
                        resourceColumnData?.resource_column_properties?.resource_columns
                          ?.map((columnItem: any) => {
                            if (columnItem?.is_active) {
                              return columnItem.column_name;
                            }
                          })
                          .filter((filterItem: any) => filterItem);
                      setAvailColumns(resourceColumns ?? []);
                      setAvailColumnsWithResourceDetail(null);
                    }
                  }}
                  className={`form-control-autocomplete ${
                    errors?.aggregation_query ? "has-error" : ""
                  }`}
                  InputProps={{
                    readOnly: true,
                  }}
                  error={!!errors?.aggregation_query}
                  helperText={errors?.aggregation_query}
                />
              </Grid>
            </>
          )}

          {formData?.linked_service?.sub_type === "blob" && (
            <>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="container_name"
                  name="container_name"
                  fullWidth
                  label={
                    <span>
                      Container Name<span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.container_name ? "has-error" : ""
                  }`}
                  onChange={(e: any) =>
                    handleResourceDefinition(e.target.name, e.target.value)
                  }
                  // placeholder="Ex: Sample Container Name"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.resource_definition?.container_name || ""}
                  error={!!errors?.container_name}
                  helperText={errors?.container_name || ""}
                />
              </Grid>
            </>
          )}
          {formData?.linked_service?.type === "api" && (
            <>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={connectionKeysData || []}
                  getOptionLabel={(connectionData) => connectionData.name}
                  onChange={(event, value) => {
                    const connectionKeyDetail = connectionKeysData?.find(
                      (option: { id: any }) => option.id === value?.id
                    );
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        api_definition: {
                          ...formData.resource_definition.api_definition,
                          ["connection_key"]: value?.id,
                          url: connectionKeyDetail?.api_url,
                        },
                      },
                    });
                    setType(value?.type);
                    validateApiDefinitionField("connection_key", value?.id);
                    validateApiDefinitionField(
                      "url",
                      connectionKeyDetail?.api_url
                    );
                  }}
                  value={
                    connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id ===
                        formData?.resource_definition?.api_definition
                          ?.connection_key
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="connection_key"
                      style={{ color: "#000000" }}
                      {...params}
                      label={
                        <span>
                          Connection Key
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.connection_key}
                      helperText={errors?.connection_key}
                    />
                  )}
                  renderOption={(props, option, { selected, index }) => {
                    return (
                      <MenuItem
                        {...props}
                        key={index}
                        sx={{ justifyContent: "space-between" }}
                      >
                        {option?.name}
                      </MenuItem>
                    );
                  }}
                  loadingText="Loading..."
                  className={`form-control-autocomplete ${
                    errors?.connection_key ? "has-error" : ""
                  }`}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Method</label>
                <Select
                  MenuProps={{
                    disableScrollLock: true,
                  }}
                  defaultValue="get"
                  title="method"
                  value={formData?.resource_definition?.api_definition?.method}
                  name="method"
                  style={{ width: "100%", height: 35 }}
                  onChange={(e) => {
                    handleApiDefinition(e.target.name, e.target.value);
                  }}
                  className={`form-control-autocomplete form-control-autocomplete-1`}
                >
                  {methodType.map((type: string) => (
                    <MenuItem key={type} value={type}>
                      <span style={{ textTransform: "capitalize" }}>
                        {type}
                      </span>
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Content Type</label>
                <Select
                  MenuProps={{
                    disableScrollLock: true,
                  }}
                  defaultValue="application/json"
                  title="content_type"
                  value={
                    formData?.resource_definition?.api_definition?.content_type
                  }
                  name="content_type"
                  style={{ width: "100%", height: 35 }}
                  onChange={(e) => {
                    handleApiDefinition(e.target.name, e.target.value);
                  }}
                  className={`form-control-autocomplete form-control-autocomplete-1`}
                >
                  {contentType.map((type: string) => (
                    <MenuItem key={type} value={type}>
                      <span style={{ textTransform: "capitalize" }}>
                        {type}
                      </span>
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="url"
                  name="url"
                  fullWidth
                  label={
                    <span>
                      Url
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.url ? "has-error" : ""
                  }`}
                  value={
                    formData?.resource_definition?.api_definition?.url || ""
                  }
                  onChange={(e) => {
                    handleApiDefinition(e.target.name, e.target.value);
                    validateApiDefinitionField(e.target.name, e.target.value);
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.url}
                  helperText={errors?.url || ""}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="number"
                  title="request_timeout"
                  name="request_timeout"
                  fullWidth
                  label={
                    <span>
                      Request Timeout(in seconds)
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.request_timeout ? "has-error" : ""
                  }`}
                  value={
                    formData?.resource_definition?.api_definition
                      ?.request_timeout || 0
                  }
                  onChange={(e) => {
                    handleApiDefinition(e.target.name, e.target.value);
                    validateApiDefinitionField(e.target.name, e.target.value);
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!errors?.request_timeout}
                  helperText={errors?.request_timeout || ""}
                />
              </Grid>
              {type === "basic_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="user_name"
                      name="user_name"
                      fullWidth
                      label={
                        <span>
                          Username <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.user_name ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.user_name || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.user_name}
                      helperText={errors?.user_name || ""}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="password"
                      title="password"
                      name="password"
                      fullWidth
                      label={
                        <span>
                          Password<span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.password ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.password || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.password}
                      helperText={errors?.password || ""}
                    />
                  </Grid>
                </>
              )}
              {type === "token_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="bearer_token"
                      name="bearer_token"
                      fullWidth
                      label={
                        <span>
                          Bearer Token{" "}
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.bearer_token ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.bearer_token || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.bearer_token}
                      helperText={errors?.bearer_token || ""}
                    />
                  </Grid>
                </>
              )}
              {type === "key_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="api_key"
                      name="api_key"
                      fullWidth
                      label={
                        <span>
                          Api Key <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.api_key ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.api_key || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.api_key}
                      helperText={errors?.api_key || ""}
                    />
                  </Grid>
                </>
              )}
              {type === "oauth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="oauth_client_id"
                      name="oauth_client_id"
                      fullWidth
                      label={
                        <span>
                          Oauth Client Id{" "}
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.oauth_client_id ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.oauth_client_id || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.oauth_client_id}
                      helperText={errors?.oauth_client_id || ""}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="oauth_client_secret"
                      name="oauth_client_secret"
                      fullWidth
                      label={
                        <span>
                          Oauth Client Secret
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.oauth_client_secret ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.oauth_client_secret || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.oauth_client_secret}
                      helperText={errors?.oauth_client_secret || ""}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <TextField
                      type="text"
                      title="oauth_url"
                      name="oauth_url"
                      fullWidth
                      label={
                        <span>
                          Oauth Url<span className="required-asterisk">*</span>
                        </span>
                      }
                      variant="outlined"
                      className={`form-control-autocomplete ${
                        errors?.oauth_url ? "has-error" : ""
                      }`}
                      value={
                        formData?.resource_definition?.api_definition
                          ?.oauth_url || ""
                      }
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                        validateApiDefinitionField(
                          e.target.name,
                          e.target.value
                        );
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.oauth_url}
                      helperText={errors?.oauth_url || ""}
                    />
                  </Grid>
                </>
              )}
            </>
          )}
          {formData?.linked_service?.type === "api" && (
            <>
              <Grid container item rowSpacing={2.5} columnSpacing={4}>
                {formData?.resource_definition?.api_definition?.method !==
                  "get" && (
                  <Grid item xs={12} sm={6} md={12} lg={12} xl={12}>
                    <label className="label-text">Request Body</label>
                    <TextareaAutosize
                      title="Request Body"
                      name="body"
                      minRows={3}
                      maxRows={6}
                      className={`form-control-1`}
                      onChange={(e) => {
                        handleApiDefinition(e.target.name, e.target.value);
                      }}
                      value={
                        formData?.resource_definition?.api_definition?.body
                      }
                    />
                  </Grid>
                )}
                <Grid item xs={6}>
                  <AddMultiGrid
                    gridValue={queryParams}
                    setGridValue={setQueryParams}
                    isViewOnly={false}
                    heading={"Query Params"}
                  />
                </Grid>
                <Grid item xs={6}>
                  <AddMultiGrid
                    gridValue={urlParams}
                    setGridValue={setUrlParams}
                    isViewOnly={false}
                    heading={"Url Params"}
                  />
                </Grid>
              </Grid>
            </>
          )}
          {formData?.linked_service?.sub_type === "local" && (
            <Grid item xs={12} sm={12} md={8} lg={4} xl={4}>
              <Box
                sx={{
                  display: { md: "block", sm: "none" },
                }}
                className="label-text line-height16"
              >
                &nbsp;
              </Box>
              <FileStreamUpload
                handleFileUpload={handleFileUpload}
                selectedFile={selectedFile}
                setSelectedFile={setSelectedFile}
                isLoadingFile={isLoadingFile}
                fileStreamIndex={fileStreamIndex}
                uploadProgress={uploadProgress}
                cancelUploadFile={cancelUploadFile}
              />
            </Grid>
          )}
        </>
      ) : (
        <AggregatedResource
          linkedFormData={formData}
          linkedResourceId={resourceId}
          // linkedAllResourcesData={allResourcesData}
          linkedSetResourceId={setResourceId}
          linkedSetFormData={setFormData}
          linkedSetIsLoading={setIsLoading}
          errors={errors}
          setErrors={setErrors}
          allVariablesList={allVariablesList}
          setAllVariablesList={setAllVariablesList}
          tempGlobalVariables={tempGlobalVariables}
          setTempGlobalVariables={setTempGlobalVariables}
          isShowRenderVariable={false}
          resourceColumnData={resourceColumnData}
        />
      )}
      <SqlQueryDialog
        showQryModal={showQryModal}
        setShowQryModal={setShowQryModal}
        onSaveQuery={onSaveQuery}
      />
    </>
  );
};

export default LinkedServices;
