import { Dispatch, SetStateAction, useState } from "react";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import QueryBuilder from "../../components/QueryBuilder/Index";
import { useToast } from "../../services/utils";
import {
  Button,
  Grid,
  TextField,
  IconButton,
  Dialog,
  DialogActions,
} from "@mui/material";
import { Box } from "@mui/system";
import { toast } from "react-toastify";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import * as Yup from "yup";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { extractColumnNamesFromQuery } from "../../services/utils";
import { IconDeleteBlueSvg } from "../../common/utils/icons";

interface FilterRulesProps {
  formData: any;
  setFormData?: Dispatch<SetStateAction<any>>;
  resourceColumnData?: any;
  errors?: any;
  checkFilterRuleValidation?: any;
  isViewOnly?: any;
  globalVariables?: any;
  setGlobalVariables?: any;
  setAllVariablesList?: any;
  isAddButtonHidden?: any;
  fileData?: any;
}
const FilterRules = ({
  formData,
  setFormData,
  resourceColumnData,
  errors,
  checkFilterRuleValidation,
  isViewOnly,
  setAllVariablesList,
  isAddButtonHidden,
  fileData,
}: FilterRulesProps) => {
  const {
    globalVariables,
    setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    setQueryBuilderTempValue,
    setAvailColumns,
    availColumns,
    setAvailColumnsWithResourceDetail,
    setShowAggregatedColumns,
  } = useRuleResourceContext();
  const { showToast } = useToast();
  const [showQryModal, setShowQryModal] = useState<any>(false);
  const [query, setQuery] = useState<any>(null);
  const [filterColumnsData, setFilterColumnsData] = useState<any>([]);
  const [selectedId, setSelectedId] = useState<any>(null);
  const [error, setError] = useState<{ [key: string]: string }>({});

  const handleAddFilter = (e: any) => {
    e.stopPropagation();
    if (resourceColumnData == null) {
      showToast(
        "Please select Resource Columns before applying filter rules!!",
        "warning"
      );
      return;
    }
    const newFilter = {
      id: Math.random(),
      name: "",
      sql_query: "",
    };
    const updatedFormData = {
      ...formData,
      filter_rules: [...formData.filter_rules, newFilter],
    };
    setFormData && setFormData(updatedFormData);
    checkFilterRuleValidation(updatedFormData, "name");
    checkFilterRuleValidation(updatedFormData, "sql_query");
  };
  const handleCloseQueryModal = () => {
    setShowQryModal(false);
    setQueryBuilderTempValue("");
    setError({});
    setTempGlobalVariables({});
  };
  const handleSaveQuery = async () => {
    const usedColumn = extractColumnNamesFromQuery(query);
    const isValid = usedColumn.every((columnName: any) =>
      availColumns.includes(columnName)
    );
    if (!isValid) {
      showToast(
        "The values enclosed in square brackets are reserved; only the available variables within the square brackets may be used.",
        "warning"
      );
      return;
    }
    if (query) {
      const filterRuleSchema: any = {};

      Object.keys(tempGlobalVariables).map((key: any) => {
        if (tempGlobalVariables[key] === "") {
          filterRuleSchema[key] = Yup.string().required(`Please enter ${key}`);
        } else {
          return {
            name: key,
            value: tempGlobalVariables[key],
          };
        }
      });
      const updatedFormData = {
        ...formData,
        filter_rules: formData.filter_rules.map((filter: { id: any }) =>
          filter.id === selectedId
            ? { ...filter, sql_query: query }
            : { ...filter }
        ),
      };
      try {
        await Yup.object()
          .shape({ ...filterRuleSchema })
          .validate(tempGlobalVariables, {
            abortEarly: false,
          });
      } catch (err: any) {
        err.inner.forEach((error: any) => {
          setError((prevError) => ({
            ...prevError,
            [error.path]: error.message,
          }));
        });
        return;
      }
      setFormData && setFormData(updatedFormData);
      setError((prevError) => ({
        ...prevError,
        query: "",
      }));
      checkFilterRuleValidation(updatedFormData, "sql_query");
    } else {
      setError((prevError) => ({
        ...prevError,
        query: "Please enter query",
      }));
      return;
    }
    setGlobalVariables((prev: any) => ({
      ...Object.keys(tempGlobalVariables).reduce(
        (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
        prev
      ),
    }));
    setAllVariablesList((prev: any) => ({
      ...prev,
      resource: {
        ...prev.resource,
        ...tempGlobalVariables,
      },
    }));
    setTempGlobalVariables({});
    setQueryBuilderTempValue("");
    setShowQryModal(false);
  };
  const handleChangeQuery = (qry: any, columns: any) => {
    setQuery(qry?.trim());
  };

  const handleFilterNameChange = async (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    filterId: any,
    index: number
  ) => {
    const updatedFormData = {
      ...formData,
      filter_rules: formData.filter_rules.map((filter: { id: any }) =>
        filter.id === filterId ? { ...filter, name: e.target.value } : filter
      ),
    };
    setFormData && setFormData(updatedFormData);

    checkFilterRuleValidation(updatedFormData, "name");
  };

  return (
    <Grid
      item
      xs={12}
      sm={12}
      md={12}
      lg={12}
      xl={12}
      sx={{ textAlign: "right" }}
    >
      {!isViewOnly && !isAddButtonHidden && (
        <Button
          variant="contained"
          color="secondary"
          onClick={(e: any) => handleAddFilter(e)}
          className="btn-orange btn-dark"
          sx={{ marginBottom: "12px" }}
        >
          <AddSharpIcon sx={{ marginRight: "4px" }} /> Filter
        </Button>
      )}

      {formData?.filter_rules?.length > 0 && (
        <>
          <table className="table-filters">
            {isViewOnly && (
              <thead>
                <tr>
                  <th style={{ width: "30%" }}>Name:</th>
                  <th>SQL Query(s):</th>
                </tr>
              </thead>
            )}
            <tbody>
              {formData?.filter_rules?.map((filterItem: any, idx: any) => {
                return !isViewOnly ? (
                  <tr key={idx}>
                    <td
                      width={"50"}
                      className={`${isViewOnly ? "bg-white" : ""}`}
                    >
                      <span>{idx + 1}</span>
                    </td>
                    <td
                      width="250"
                      className={`${isViewOnly ? "bg-white" : ""}`}
                    >
                      <TextField
                        type="text"
                        name="name"
                        disabled={isViewOnly}
                        fullWidth
                        variant="outlined"
                        className={`form-control-autocomplete ${
                          errors?.filter_rules &&
                          errors?.filter_rules[idx]?.name
                            ? "has-error"
                            : ""
                        }`}
                        onChange={(e) =>
                          handleFilterNameChange(e, filterItem?.id, idx)
                        }
                        InputLabelProps={{
                          shrink: true,
                        }}
                        value={filterItem?.name || ""}
                        data-mtrsx={idx}
                        error={
                          !!(
                            errors?.filter_rules &&
                            errors?.filter_rules[idx]?.name
                          )
                        }
                        helperText={
                          errors?.filter_rules &&
                          errors?.filter_rules[idx]?.name
                        }
                      />
                    </td>
                    <td
                      className={`${
                        isViewOnly ? "bg-white" : ""
                      } valign-center`}
                    >
                      <TextField
                        type="text"
                        // disabled={isViewOnly}
                        required
                        value={filterItem?.sql_query}
                        onClick={() => {
                          setShowQryModal(true);
                          setSelectedId(filterItem?.id);
                          if (filterItem?.sql_query) {
                            setQueryBuilderTempValue(filterItem?.sql_query);
                          }
                          if (resourceColumnData) {
                            let columns: any;
                            //added fileData because when we change column name form resource column table, available column not getting update
                            if (fileData) {
                              columns = fileData.map(
                                (item: any) => item?.column_name
                              );
                            } else {
                              columns =
                                resourceColumnData?.resource_column_properties?.resource_columns
                                  ?.map((columnItem: any) => {
                                    if (columnItem?.is_active) {
                                      return columnItem.column_name;
                                    }
                                  })
                                  .filter((filterItem: any) => filterItem) ??
                                [];
                            }
                            setFilterColumnsData(columns);
                            setAvailColumns((prev: any) => {
                              const updatedColumns = new Set([...columns]);
                              return Array.from(updatedColumns);
                            });
                            setAvailColumnsWithResourceDetail(null);
                          }
                        }}
                        className={`form-control-autocomplete ${
                          errors?.filter_rules &&
                          errors?.filter_rules[idx]?.sql_query
                            ? "has-error"
                            : ""
                        }`}
                        InputProps={{
                          readOnly: true,
                        }}
                        error={
                          !!(
                            errors?.filter_rules &&
                            errors?.filter_rules[idx]?.sql_query
                          )
                        }
                        helperText={
                          errors?.filter_rules &&
                          errors?.filter_rules[idx]?.sql_query
                        }
                      />
                    </td>
                    {!isAddButtonHidden && (
                      <td
                        width="50"
                        // className={`${isViewOnly ? "bg-white" : ""}`}
                      >
                        <IconButton
                          sx={{ color: "grey" }}
                          onClick={() => {
                            setFormData &&
                              setFormData(
                                (prevFormData: { filter_rules: any[] }) => {
                                  const updatedRules = {
                                    ...prevFormData,
                                    filter_rules:
                                      prevFormData.filter_rules.filter(
                                        (filItem: any) =>
                                          filItem.id !== filterItem?.id
                                      ),
                                  };
                                  return updatedRules;
                                }
                              );
                          }}
                        >
                          <IconDeleteBlueSvg />
                        </IconButton>
                      </td>
                    )}
                  </tr>
                ) : (
                  <tr key={idx}>
                    <td className={"bg-white"}>{filterItem?.name ?? ""}</td>
                    <td className={`bg-white valign-center`}>
                      <div className="word-break-all">
                        {filterItem?.sql_query}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </>
      )}
      <Dialog
        fullWidth
        maxWidth="lg"
        open={showQryModal}
        onClose={handleCloseQueryModal}
        className="main-dailog"
      >
        <Box sx={{ padding: 3 }}>
          <Grid container className="dailog-header">
            <label className="label-text pt-13">Add Query</label>
            <div className="close-icon" onClick={handleCloseQueryModal}></div>
          </Grid>
          <Box className="dailog-body pb-24">
            <Box>
              <Grid container rowSpacing={2.5} columnSpacing={6.25}>
                <QueryBuilder
                  handleChange={handleChangeQuery}
                  error={error}
                  setErrors={setError}
                />
              </Grid>
            </Box>
          </Box>
          <DialogActions className="dailog-footer">
            <Button
              color="secondary"
              variant="contained"
              onClick={handleSaveQuery}
              className="btn-orange"
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </Grid>
  );
};

export default FilterRules;
