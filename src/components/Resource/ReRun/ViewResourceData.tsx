import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Tooltip,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Box,
  TextareaAutosize,
} from "@mui/material";
import { formattedJson } from "../../../services/utils";
import InfoIcon from "@mui/icons-material/Info";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { IconBtnEditBase, IconViewSvgWhite } from "../../../common/utils/icons";
import { useNavigate, useParams } from "react-router-dom";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import { useToast } from "../../../services/utils";
import ValidateReRunAdditionalResource from "./ValidateReRunAdditionalResource";

interface ViewResourceDataProps {
  resourceData: any;
  linkedServicesData: any;
  connectionKeysData: any;
  // fileProcessingData: any;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  isForViewResource?: boolean;
  showAdditionalResource: boolean;
  setResourcesData: Dispatch<SetStateAction<any>>;
  resource_id: number | null | undefined;
  resourcesData: any;
  resourceCategory?: string;
  viewInlineVariables?: any;
  isReRun?: any;
}

const ViewResourceData = ({
  resourceData,
  linkedServicesData,
  connectionKeysData,
  // fileProcessingData,
  setIsLoading,
  isForViewResource,
  showAdditionalResource,
  resource_id,
  resourcesData,
  setResourcesData,
  resourceCategory,
  viewInlineVariables,
  isReRun,
}: ViewResourceDataProps) => {
  const navigate = useNavigate();
  const { domainId } = useParams();
  const { showToast } = useToast();
  const {
    isResourceEdit,
    setIsResourceEdit,
    setGlobalVariables,
    setAllResourcesData,
    allResourcesData,
    fileProcessingData,
  } = useRuleResourceContext();
  const [storageType, setStorageType] = useState("");
  const [resourceType, setResourceType] = useState("flat");
  const [expandedAccordion, setExpandedAccordion] = useState<any>("");
  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<number>(0);

  useEffect(() => {
    if (resourceData && linkedServicesData) {
      const aggregation_type: string = resourceData?.aggregation_properties
        ?.base_resource_id
        ? "aggregated"
        : "flat";
      setResourceType(aggregation_type ?? "flat");
      setSelectLinkedServiceId(resourceData?.linked_service_id);
      if (resourceData?.linked_service_id) {
        let type;
        type = linkedServicesData?.find(
          (option: { id: any }) => option.id === resourceData?.linked_service_id
        )?.sub_type;
        setStorageType(type);
      }
    }
  }, [resourceData, linkedServicesData]);

  const handleChangeAccordion =
    (panel: any) => (event: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
      setExpandedAccordion(isExpanded ? panel?.resource_id : null);
    };

  const handleChipClick = (domainId: number, option: any) => {
    window.open(
      `/resource/${domainId}/view/${option?.base_resource_id}`,
      "_blank"
    );
  };
  const handleViewResource = (
    domainId: any,
    resource: any,
    resourceName: any,
    e: any
  ) => {
    e.preventDefault();
    if (isResourceEdit !== "") {
      return;
    }
    navigate(`/resource/${domainId}/view/${resource}`);
  };
  return (
    <>
      {resourceType === "flat" && (
        <>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">
              <span className="position-relative">
                Linked Service
                {resourceData?.linked_service_id && (
                  <Tooltip
                    componentsProps={{
                      tooltip: { className: "wide-tooltip w-250" },
                    }}
                    title={
                      resourceData?.linked_service_id && (
                        <React.Fragment>
                          <Typography color="inherit">
                            Linked Service Code :{" "}
                            {
                              linkedServicesData?.find(
                                (item: any) =>
                                  item?.id === resourceData?.linked_service_id
                              )?.code
                            }
                          </Typography>
                        </React.Fragment>
                      )
                    }
                  >
                    <InfoIcon
                      sx={{
                        position: "absolute",
                        top: "50%",
                        transform: "translateY(-50%)",
                        right: "-24px",
                        width: "16px",
                      }}
                    />
                  </Tooltip>
                )}
              </span>
            </label>
            <div className="form-control">
              {linkedServicesData?.find(
                (item: any) => item?.id === resourceData?.linked_service_id
              )?.name || "N/A"}
            </div>
          </Grid>
          {["sql", "mariadb", "azure_sql"].includes(storageType) && (
            <>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Connection Key</label>
                <div className="form-control">
                  {connectionKeysData?.find(
                    (key: any) => key?.id === resourceData?.connection_key
                  )?.name || "N/A"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">SQL Query</label>
                <div className="form-control break-word">
                  {resourceData?.sql_query || "N/A"}
                </div>
              </Grid>
            </>
          )}
          {["blob", "sftp", "local"].includes(storageType) && (
            <>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Connection Key</label>
                <div className="form-control">
                  {connectionKeysData?.find(
                    (key: any) => key?.id === resourceData?.connection_key
                  )?.name || "N/A"}
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">
                  <span className="position-relative">
                    File Processing Attribute
                    {resourceData?.file_processing_id && (
                      <Tooltip
                        componentsProps={{
                          tooltip: { className: "wide-tooltip w-380" },
                        }}
                        title={
                          <pre
                            style={{
                              whiteSpace: "pre-wrap",
                              margin: 0,
                              maxHeight: "200px",
                              overflowY: "auto",
                            }}
                          >
                            <React.Fragment>
                              <Typography color="inherit">
                                File Processing Detail
                              </Typography>
                              <Typography>
                                {formattedJson(
                                  JSON.stringify(
                                    fileProcessingData?.find(
                                      (file: any) =>
                                        file?.id ===
                                        resourceData?.file_processing_id
                                    )
                                  )
                                )}
                              </Typography>
                            </React.Fragment>
                          </pre>
                        }
                      >
                        <InfoIcon
                          sx={{
                            position: "absolute",
                            top: "50%",
                            transform: "translateY(-50%)",
                            right: "-24px",
                            width: "16px",
                          }}
                        />
                      </Tooltip>
                    )}
                  </span>
                </label>
                <div className="form-control">
                  {fileProcessingData?.find(
                    (file: any) => file?.id === resourceData?.file_processing_id
                  )?.resource_type || "N/A"}
                </div>
              </Grid>
              {storageType === "sftp" ? (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <label className="label-text text-bold">
                    Remote Directory
                  </label>
                  <div className="form-control break-word">
                    {resourceData?.remote_directory || "N/A"}
                  </div>
                </Grid>
              ) : (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <label className="label-text text-bold">Resource Path</label>
                  <div className="form-control break-word">
                    {resourceData?.resource_path || "N/A"}
                  </div>
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Column Delimiter</label>
                <div className="form-control">
                  {resourceData?.column_delimiter || "N/A"}
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">File Name</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.file_name || "N/A"}
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Excel Sheet Name</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.excel_sheet_name || "N/A"}
                </div>
              </Grid>
              {storageType === "blob" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">
                      Container Name
                    </label>
                    <div className="form-control">
                      {resourceData?.container_name || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
            </>
          )}
          {linkedServicesData?.find(
            (option: { id: any }) =>
              option.id === resourceData?.linked_service_id
          )?.type === "api" && (
            <>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Connection Key</label>
                <div className="form-control">
                  {connectionKeysData?.find(
                    (key: any) =>
                      key?.id === resourceData?.api_definition?.connection_key
                  )?.name || "N/A"}
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Method</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.api_definition?.method || "get"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Content Type</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.api_definition?.content_type ||
                    "application/json"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Url</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.api_definition?.url || "N/A"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">
                  Request Timeout(in seconds)
                </label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.api_definition?.request_timeout || 0}
                </div>
              </Grid>

              {storageType === "basic_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">Username</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.api_definition?.user_name || "N/A"}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">Password</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.api_definition?.password || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
              {storageType === "token_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">Bearer token</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.api_definition?.bearer_token || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
              {storageType === "key_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">API key</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.api_definition?.api_key || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
              {storageType === "oauth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">
                      Oauth Client Id
                    </label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.api_definition?.oauth_client_id || "N/A"}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">
                      Oauth Client Secret
                    </label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.api_definition?.oauth_client_secret ||
                        "N/A"}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">Oauth Url</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.api_definition?.oauth_url || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
            </>
          )}
          {linkedServicesData?.find(
            (option: { id: any }) =>
              option.id === resourceData?.linked_service_id
          )?.type === "api" && (
            <>
              {resourceData?.api_definition?.method !== "get" && (
                <Grid item xs={12} sm={6} md={12} lg={12} xl={12}>
                  <label className="label-text text-bold">Request Body</label>
                  <div className="form-control word-wrap-break-word">
                    <TextareaAutosize
                      title="Request Body"
                      disabled={true}
                      name="body"
                      minRows={3}
                      maxRows={6}
                      className={`form-control-1`}
                      value={formattedJson(resourceData?.api_definition?.body)}
                    />
                  </div>
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Query params</label>
                <Box className="params-box">
                  <div className="avail-columns-group">
                    {resourceData?.api_definition?.query_params &&
                    Object.keys(resourceData?.api_definition?.query_params)
                      .length > 0
                      ? Object.keys(
                          resourceData?.api_definition?.query_params
                        ).map((key, index) => (
                          <Box key={index} className="avail-columns">
                            <button
                              type="button"
                              style={{
                                display: `${key === "" ? "none" : "block"}`,
                              }}
                            >
                              {key} :{" "}
                              {resourceData?.api_definition?.query_params[key]}
                            </button>
                          </Box>
                        ))
                      : "N/A"}
                  </div>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Url params</label>
                <Box className="params-box">
                  <div className="avail-columns-group">
                    {resourceData.api_definition?.url_params &&
                    Object.keys(resourceData?.api_definition?.url_params)
                      .length > 0
                      ? Object.keys(
                          resourceData?.api_definition?.url_params
                        ).map((key, index) => (
                          <Box key={index} className="avail-columns">
                            <button
                              type="button"
                              style={{
                                display: `${key === "" ? "none" : "block"}`,
                              }}
                            >
                              {key} :{" "}
                              {resourceData?.api_definition?.url_params[key]}
                            </button>
                          </Box>
                        ))
                      : "N/A"}
                  </div>
                </Box>
              </Grid>
            </>
          )}
        </>
      )}
      {resourceType === "aggregated" && (
        <>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">Base Resource</label>
            <div
              onClick={() =>
                handleChipClick(
                  resourceData?.domain_id ? resourceData.domain_id : "all",
                  resourceData?.aggregation_properties
                )
              }
              className="form-control btn-new-window"
            >
              {allResourcesData?.find(
                (item: any) =>
                  item?.id ===
                  resourceData?.aggregation_properties?.base_resource_id
              )?.resource_name || "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">
              Base Resource Columns
            </label>
            <div className="form-control">
              {resourceData?.aggregation_properties?.base_resource_columns !==
              null
                ? resourceData?.aggregation_properties?.base_resource_columns
                    ?.length > 0
                  ? resourceData?.aggregation_properties?.base_resource_columns.join(
                      ", "
                    )
                  : "N/A"
                : "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">
              Base Resource Validation
            </label>
            <div className="form-control">
              {resourceData?.aggregation_properties?.base_resource_validation
                ? "True"
                : "False"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">Aggregation Query</label>
            <div className="form-control word-wrap-break-word">
              {resourceData?.aggregation_properties?.aggregation_query || "N/A"}
            </div>
          </Grid>
        </>
      )}
      {!isForViewResource && showAdditionalResource ? (
        <Grid
          item
          xs={12}
          sm={12}
          md
          sx={{ justifyContent: "flex-end", display: "flex" }}
        >
          <button
            className="btn-nostyle icon-btn-edit"
            onClick={() => {
              if (isResourceEdit === "") {
                setIsResourceEdit(resourceCategory);
              } else {
                showToast("Please save changes first!!", "warning");
              }
              if (viewInlineVariables?.resource_variables?.length > 0) {
                viewInlineVariables?.resource_variables.map((res: any) => {
                  if (res?.resource_id === resourceData.id) {
                    setGlobalVariables(res?.resource_vars ?? {});
                    setResourcesData((prevResourcesData: any) => {
                      const updatedResources = prevResourcesData.map(
                        (resource: any) => {
                          if (res?.resource_id === resourceData.id) {
                            return {
                              ...resource,
                              additional_properties: {
                                ...resource.additional_properties,
                                inline_variables: res?.resource_vars,
                              },
                            };
                          }
                          return resource;
                        }
                      );
                      return updatedResources;
                    });
                  }
                });
              }
            }}
          >
            <IconBtnEditBase />
          </button>
        </Grid>
      ) : null}
      {showAdditionalResource &&
      resourceData?.additional_base_resource_data !== null &&
      resourceData?.additional_base_resource_data?.length > 0 &&
      !isForViewResource
        ? resourceData?.additional_base_resource_data.map(
            (additionalRes: any, index: any) => (
              <Grid item xs={12} sm={6} md={12} lg={12} xl={12} key={index}>
                <Accordion
                  expanded={expandedAccordion === additionalRes?.resource_id}
                  onChange={handleChangeAccordion(additionalRes)}
                  className="heading-bold"
                >
                  <AccordionSummary
                    aria-controls="panel1d-content"
                    id="panel1d-header"
                    expandIcon={<ExpandMoreIcon />}
                  >
                    <div className="custom-actions">
                      <div className="heading">
                        Additional Resource Data -{" "}
                        <span style={{ fontWeight: "normal" }}>
                          {additionalRes?.resource_name}
                        </span>
                      </div>
                      <div className="action-btns">
                        <Tooltip
                          title="Navigate to view resource"
                          placement="top"
                        >
                          <button
                            className="view-icon"
                            onClick={(e) =>
                              handleViewResource(
                                domainId,
                                additionalRes?.resource_id,
                                additionalRes?.resource_name,
                                e
                              )
                            }
                          >
                            <IconViewSvgWhite />
                          </button>
                        </Tooltip>
                      </div>
                    </div>
                  </AccordionSummary>
                  <AccordionDetails sx={{ paddingTop: "16px" }}>
                    <ValidateReRunAdditionalResource
                      additionalResource={additionalRes}
                      linkedServicesData={linkedServicesData}
                      // fileProcessingData={fileProcessingData}
                      setIsLoading={setIsLoading}
                      fileStreamIndex={index}
                      resource_id={resource_id}
                      resourcesData={resourcesData}
                      setResourcesData={setResourcesData}
                    />
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )
          )
        : null}
    </>
  );
};

export default ViewResourceData;
