import { Grid } from "@mui/material";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import useFetchConnectionKeyByLinkedServiceId from "../../../hooks/useFetchConnectionKeyByLinkedServiceId";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import { useToast } from "../../../services/utils";
import ViewResourceData from "./ViewResourceData";
import ReRunEditRuleExecutionTab from "../../Rules/ReRun/ReRunEditRuleExecutionTab";
import { IconBtnEditBase } from "../../../common/utils/icons";

interface ValidateAdditionalResourceProps {
  additionalResource: any;
  linkedServicesData: any;
  // fileProcessingData: any;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  fileStreamIndex: any;
  resource_id: number | null | undefined;
  resourcesData: any;
  setResourcesData: Dispatch<SetStateAction<any>>;
}
const ValidateReRunAdditionalResource = ({
  additionalResource,
  linkedServicesData,
  // fileProcessingData,
  setIsLoading,
  fileStreamIndex,
  resource_id,
  resourcesData,
  setResourcesData,
}: ValidateAdditionalResourceProps) => {
  const { isResourceEdit, setIsResourceEdit } = useRuleResourceContext();
  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<number>(0);
  const [resourceId, setResourceId] = useState(0);
  const { showToast } = useToast();

  const [additionalConnectionKeyData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId,
    setIsLoading,
  });
  // const { isEditAdditionalResource, setIsEditAdditionalResource } =
  //   useResourceContext();
  useEffect(() => {
    setSelectLinkedServiceId(additionalResource?.linked_service_id);
    if (additionalResource?.aggregation_type === "aggregated") {
      setResourceId(additionalResource?.resource_id);
    }
  }, [additionalResource, linkedServicesData]);

  return (
    <>
      {isResourceEdit === "additionalResource" ? (
        <ReRunEditRuleExecutionTab
          resourceData={additionalResource}
          resourcesData={resourcesData}
          setIsLoading={setIsLoading}
          setResourcesData={setResourcesData}
          linkedServicesData={linkedServicesData}
          connectionKeysData={additionalConnectionKeyData}
          setSelectLinkedServiceId={setSelectLinkedServiceId}
          isAggregatedBaseResource={false}
          aggregatedResourceId={resourceId}
          fileStreamIndex={fileStreamIndex}
          isAdditionalResource={true}
          resource_id={resource_id}
          isFromRuleExecution={false}
        />
      ) : (
        <Grid container rowSpacing={2.5} columnSpacing={4}>
          <ViewResourceData
            resourceData={additionalResource}
            linkedServicesData={linkedServicesData}
            connectionKeysData={additionalConnectionKeyData}
            // fileProcessingData={fileProcessingData}
            setIsLoading={setIsLoading}
            showAdditionalResource={false}
            resource_id={resource_id}
            resourcesData={resourcesData}
            setResourcesData={setResourcesData}
            resourceCategory={"additionalResource"}
          />
        </Grid>
      )}
      {isResourceEdit !== "additionalResource" && (
        <Grid
          item
          xs={12}
          sm={12}
          md
          sx={{ justifyContent: "flex-end", display: "flex" }}
        >
          <button
            className="btn-nostyle icon-btn-edit"
            onClick={() => {
              if (isResourceEdit === "") {
                setIsResourceEdit("additionalResource");
              } else {
                showToast("Please save changes first!!", "warning");
              }
            }}
          >
            <IconBtnEditBase />
          </button>
        </Grid>
      )}
    </>
  );
};

export default ValidateReRunAdditionalResource;
