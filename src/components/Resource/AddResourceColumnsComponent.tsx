import React, { useState, useEffect } from "react";
import FlexBetween from "../../components/FlexBetween";
import { saveAs } from "file-saver";
import { useToast } from "../../services/utils";
import {
  Box,
  Grid,
  Input,
  IconButton,
  Button,
  Tooltip,
  TextField,
  Backdrop,
  CircularProgress,
  NativeSelect,
  Tabs,
  Tab,
} from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import FileUploadButton from "../../components/Uploaders/FileUpload";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { read, utils } from "xlsx";
import {
  manageAvailableColumns,
  setDataType,
  setSeverityLevel,
} from "../../services/utils";
import DataTable from "../../components/DataGrids/DataGrid";
import {
  datatypes,
  dateTimeFormat,
  severity_level,
} from "../../services/constants";
import { useNavigate } from "react-router-dom";
import { IDomainsData } from "../../types/domain";
import DerivedColumnDialog from "../../components/Dialogs/DerivedColumnDialog";
import ReferenceDialog from "../../components/Dialogs/ReferenceDialog";
import ValidationDialog from "../../components/Dialogs/ValidationDialog";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import {
  defaultResourceCDMColumns,
  resourceCDMColumns,
} from "../../services/constants/resource";
import { addResourceCombinedColumnSchema } from "../../schemas";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { useResourceContext } from "../../contexts/ResourceContext";
import ConfirmDailog from "../../components/Dialogs/ConfirmDailog";
import InfoIcon from "@mui/icons-material/Info";
import CheckboxWithKeyboardEvent from "../../components/Molecules/Resource/CheckboxWithKeyboardEvent";
import { expectedResourceColumnHeaders } from "../../services/constants/fileHeaders";
import FileHeaderToastifyMsg from "../../components/Toastify/FileHeaderToastifyMsg";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import CrossFieldValidations from "../../pages/ResourceColumns/CrossFieldValidations";
import {
  IconAddRowBase,
  IconDeleteBlueSvg,
  IconEditBase,
  IconPlusBase,
} from "../../common/utils/icons";

interface AddResourceColumnsProps {
  isTableRequired: any;
  resourceColumnData?: any;
  useResourceColumns?: any;
  crossFieldValidations?: any;
  setCrossFieldValidations?: any;
  setResourceColumnsByUpload?: any;
}

const AddResourceColumnsComponent: React.FC<AddResourceColumnsProps> = ({
  isTableRequired,
  resourceColumnData,
  crossFieldValidations,
  setCrossFieldValidations,
  setResourceColumnsByUpload,
}: AddResourceColumnsProps) => {
  const {
    setIsDailogEdit,
    setDailogEditIndex,
    // SetCDMColumns,
    setSelectedRowId,
    setOpenValidationDialog,
    resourceColumnFileData,
    setResourceColumnFileData,
    setPageContext,
    formData,
    setFormData,

    errors,
    setErrors,
    setOpenDerivedColumnDialog,
    setDerivedModalType,
    setOpenReferenceDialog,
    setReferenceData,
  } = useResourceContext();
  const {
    availColumns,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,

    setQueryBuilderTempValue,
  } = useRuleResourceContext();

  const [selectedDomain, setSelectedDomain] = useState<IDomainsData>();
  const [domainsTableData, setDomainsTableData] = useState<any>([]);
  const [currentType, setCurrentType] = useState<any>("existing");
  const [backdropLoading, setBackdropLoading] = useState(true);
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [isFileUpload, setIsFileUpload] = useState<boolean>(false);
  const [isDailogOpen, setIsDailogOpen] = useState(false);
  const [allVariablesList, setAllVariablesList] = useState({
    resource: {},
    resourceColumn: {},
    additionalResource: {},
    baseResource: {},
  });

  const [oldColumnName, setOldColumnName] = useState("");
  const [activeTab, setActiveTab] = useState<string>("resourceColumns");

  useEffect(() => {
    setResourceColumnFileData([defaultResourceCDMColumns]);
    setPageContext("resourceColumn");
    setBackdropLoading(false);
  }, []);
  // hooks

  const { showToast } = useToast();

  const handleAddValidation = (id: any) => {
    setSelectedRowId(id);
    setOpenValidationDialog(true);
  };
  const handleEditValidationDialog = (id: any, index: number) => {
    setOpenValidationDialog(true);
    setIsDailogEdit(true);
    setSelectedRowId(id);
    setDailogEditIndex(index);
  };
  const handleDeleteValidation = (index: number, rowId: number) => {
    const currentIndex = resourceColumnFileData.findIndex(
      (item: any) => item.id === rowId
    );
    const updatedData = resourceColumnFileData[
      currentIndex
    ].custom_validations.filter((item: any, i: number) => i !== index);
    setResourceColumnFileData((prev: any) => {
      const newData = prev.map((item: any) => {
        if (item.id === rowId) {
          item.custom_validations = updatedData;
        }
        return item;
      });
      return newData;
    });
  };
  const handleEditReferenceDialog = (
    id: any,
    connectionKeys: any,
    linkedServices: any
  ) => {
    const currentIndex = resourceColumnFileData.findIndex(
      (item: any) => item.id === id
    );
    const referenceData = {
      source:
        resourceColumnFileData[currentIndex].reference_column_definition.source,
      source_type:
        resourceColumnFileData[currentIndex].reference_column_definition
          .source_type,
      use_translation:
        resourceColumnFileData[currentIndex].reference_column_definition
          .use_translation,
      connection_key: connectionKeys.find(
        (connectionKey: any) =>
          connectionKey?.id ===
          resourceColumnFileData[currentIndex].reference_column_definition
            ?.connection_key
      ),
      linked_service: linkedServices.find(
        (linkedServices: any) =>
          linkedServices?.id ===
          resourceColumnFileData[currentIndex].reference_column_definition
            ?.linked_service_id
      ),
      inline_variables:
        resourceColumnFileData[currentIndex].reference_column_definition
          ?.inline_variables,
    };
    setOpenReferenceDialog(true);
    setSelectedRowId(id);
    setReferenceData(referenceData);
  };
  const handleDeleteReference = (rowId: number) => {
    setResourceColumnFileData((prev: any) => {
      return prev.map((item: any) => {
        if (item.id === rowId) {
          item.is_reference = false;
          item.reference_column_definition = null;
        }
        return item;
      });
    });
  };
  const handleAddMoreRow = () => {
    let newRow = { ...resourceCDMColumns };
    const highestId = resourceColumnFileData.reduce(
      (maxId: any, item: any) => Math.max(maxId, item.id),
      -1
    );
    newRow.id = highestId + 1;
    const updatedData = [...resourceColumnFileData, newRow];
    setResourceColumnFileData(updatedData);
  };
  const handleDeleteAllRows = () => {
    if (resourceColumnFileData && resourceColumnFileData.length > 0) {
      setIsDailogOpen(true);
    }
  };
  const handleCancelDeleteAction = () => {
    setIsDailogOpen(false);
  };
  const handleConfirmDeleteAction = () => {
    setIsDailogOpen(false);
    setResourceColumnFileData([resourceColumnFileData[0]]);
    setAvailColumns([]);
    setAvailColumnsWithResourceDetail(null);
  };

  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
  };

  // Define table column names
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => (
        <IconButton
          size="small"
          tabIndex={-1}
          disabled={
            params?.row?.is_derived ||
            params?.row?.custom_validations?.length > 0 ||
            params?.row?.is_reference ||
            params?.row?.reference_column_definition != null
              ? false
              : true
          }
          onClick={() => {
            // toggle in array
            if (expandedRow.includes(params.row.id)) {
              setExpandedRow((prev: any) =>
                prev.filter((item: any) => item !== params.row.id)
              );
            } else {
              setExpandedRow((prev: any) => [...prev, params.row.id]);
            }
          }}
        >
          {params?.row?.is_derived ||
          params?.row?.custom_validations?.length > 0 ||
          params?.row?.is_reference ||
          params?.row?.reference_column_definition != null ? (
            <ExpandMoreIcon
              sx={{
                transform: `rotateZ(${
                  expandedRow.includes(params.row.id) ? 180 : 0
                }deg)`,
                transition: (theme) =>
                  theme.transitions.create("transform", {
                    duration: theme.transitions.duration.shortest,
                  }),
              }}
              fontSize="inherit"
            />
          ) : null}
        </IconButton>
      ),
    },
    {
      field: "column_name",
      headerName: "Resource Column",
      width: 120,
      minWidth: 150,
      flex: 1,
      renderCell: (params: any) => {
        const handleFocusIn = (e: React.FocusEvent<HTMLInputElement>) => {
          setOldColumnName(e.target.value);
        };
        const handleOnBlur = (e: React.FocusEvent<HTMLInputElement>) => {
          const updatedColumns = manageAvailableColumns(
            availColumns,
            oldColumnName,
            oldColumnName && oldColumnName.length > 0 ? "update" : "add",
            params.row.column_name
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
        };
        const valueField = resourceColumnFileData.find(
          (item: any) => item.id === params.row.id
        )?.column_name;
        const isNameFilled = valueField !== "";
        const handleChangeName = (e: any) => {
          const value = e.target.value;
          if (value.toLowerCase() === "file_name") {
            const isFileNameExist = resourceColumnFileData.some(
              (fileItem: any) =>
                fileItem.column_name.toLowerCase() === "file_name"
            );
            if (isFileNameExist) {
              showToast(
                `"file_name" is reserved key column, and already exist, please provide different name.`,
                "warning"
              );
              return;
            }
          }
          setResourceColumnFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.column_name = value;
              }
              return item;
            });
            return newData;
          });
        };

        return (
          <Tooltip title={valueField} placement="top">
            <input
              // placeholder="Ex: Sample Column Name"
              value={
                resourceColumnFileData.find(
                  (item: any) => item.id === params.row.id
                )?.column_name
              }
              onChange={handleChangeName}
              className={`form-control-1  input-ellipsis ${
                !isNameFilled ? "border-red" : ""
              }`}
              onKeyDown={(e: any) => {
                e.stopPropagation();
              }}
              onBlur={handleOnBlur}
              onFocus={handleFocusIn}
            />
          </Tooltip>
        );
      },
    },
    {
      field: "domain_column",
      headerName: "Domain Column",
      width: 120,
      minWidth: 130,
      flex: 1,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.domain_column = value;
              }
              return item;
            })
          );
        };

        const menuData =
          currentType === "existing"
            ? selectedDomain?.domain_properties?.columns
            : domainsTableData;

        return (
          <Tooltip title={params.value} placement="top">
            <NativeSelect
              value={params.value}
              className="white-space-nowrap form-control capitalize"
              onChange={handleChange}
            >
              <option key={"Select..."} value={""}>
                Select...
              </option>
              {menuData?.length > 0 &&
                menuData.map((domainColumnItem: any) => (
                  <option
                    key={domainColumnItem?.name}
                    value={domainColumnItem?.name}
                  >
                    {domainColumnItem?.name}
                  </option>
                ))}
            </NativeSelect>
          </Tooltip>
        );
      },
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 100,
      renderCell: (params) => {
        const dataTypeValue = resourceColumnFileData.find(
          (item: any) => item.id === params.row.id
        )?.datatype;
        const isDataTypeFilled = dataTypeValue !== "" && dataTypeValue !== null;
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.datatype = value;
                item.data_format = "";
              }
              return item;
            })
          );
        };

        return (
          <NativeSelect
            value={params?.value?.trim()}
            className={`white-space-nowrap format-arrow-pos capitalize ${
              !isDataTypeFilled ? "border-red-parent" : ""
            }`}
            onChange={handleChange}
          >
            <option key={"Select..."} value={""}>
              Select...
            </option>
            {datatypes.map((typeItem) => (
              <option key={typeItem} value={typeItem}>
                {typeItem}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "is_mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      align: "center",
      renderCell: (params) => {
        const cellValue =
          params.value && typeof params.value === "string"
            ? params.value.trim()
            : params.value;
        const handleChange = (event: any) => {
          const value = event.target.checked;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.is_mandatory = value;
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      align: "center",
      renderCell: (params) => {
        const cellValue =
          params.value && typeof params.value === "string"
            ? params.value.trim()
            : params.value;
        const handleChange = (event: any) => {
          const value = event.target.checked;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.key = value;
                if (params.row.is_active !== true && item.key === true) {
                  item.is_active = true;
                }
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "is_reference",
      headerName: "Reference",
      description: "Reference Data",
      maxWidth: 90,
      align: "center",
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.checked;
          if (value) {
            setOpenReferenceDialog(value);
            setSelectedRowId(params.row.id);
            setReferenceData({
              source: "internal",
              source_type: "",
              use_translation: false,
            });
          } else {
            setResourceColumnFileData((prev: any) =>
              prev.map((item: any) => {
                if (item.id === params.row.id) {
                  item.is_reference = value;
                  item.reference_column_definition = null;
                }
                return item;
              })
            );
          }
        };
        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "is_active",
      headerName: "Act./Inac.",
      maxWidth: 70,
      align: "center",
      renderCell: (params) => {
        const cellValue =
          params.value && typeof params.value === "string"
            ? params.value.trim()
            : params.value;
        const handleChange = async (event: any) => {
          const value = event.target.checked;

          const isTruthy = ["Y", "y", "Yes", "yes", true, "true"].includes(
            params.row.key
          );
          if (params.row.is_active === true && isTruthy) {
            showToast(
              `If you want to uncheck 'Active/Inactive,' please uncheck the 'Key' column first.`,
              "warning"
            );
            return;
          }
          const updatedColumns = manageAvailableColumns(
            availColumns,
            params.row.column_name,
            "toggle"
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.is_active = value;
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "column_length",
      headerName: "Length",
      width: 100,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                // item.column_length = parseInt(value, 10);
                const parsedValue = parseInt(value, 10);
                item.column_length = isNaN(parsedValue) ? null : parsedValue;
              }
              return item;
            })
          );
        };

        return (
          <Input
            // placeholder="Ex: Sample Length"
            value={(
              resourceColumnFileData
                ?.find((item: any) => item.id === params.row.id)
                ?.column_length?.toString() || ""
            ).replace(/ /g, "")}
            onChange={handleChange}
            onKeyDown={(e: any) => {
              e.stopPropagation();
            }}
            className="form-control input-ellipsis"
          />
        );
      },
    },
    {
      field: "severity_level",
      headerName: "Severity",
      width: 100,
      renderCell: (params) => {
        const severityLevelValue = resourceColumnFileData
          .find((item: any) => item.id === params.row.id)
          ?.severity_level?.toLowerCase();
        const handleChange = (event: any) => {
          const value = event.target.value as string;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                return { ...item, severity_level: value };
              }
              return item;
            })
          );
        };

        return (
          <NativeSelect
            value={severityLevelValue}
            className="white-space-nowrap capitalize"
            onChange={handleChange}
            style={{ width: "100%", height: 35 }}
            placeholder="Select..."
          >
            {severity_level.map((severity: string) => (
              <option key={severity} value={severity}>
                {severity}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "data_format",
      headerName: "Format",
      width: 120,
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        const cellValue =
          params.value && typeof params.value === "string"
            ? params.value.trim()
            : params.value;
        const handleChange = (event: { target: { value: any } }) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.data_format = value;
              }
              return item;
            })
          );
        };

        return (
          <>
            {!(
              resourceColumnFileData.find(
                (item: any) => item.id === params.row.id
              )?.datatype === "datetime"
            ) ? (
              <Input
                // placeholder="Ex: Sample Enter Format"
                value={
                  resourceColumnFileData?.find(
                    (item: any) => item.id === params.row.id
                  )?.data_format || ""
                }
                onChange={handleChange}
                onKeyDown={(e: any) => {
                  e.stopPropagation();
                }}
                className="form-control input-ellipsis"
              />
            ) : (
              <Tooltip
                title={dateTimeFormat.includes(cellValue) ? cellValue : ""}
                placement="top"
                arrow
              >
                <NativeSelect
                  value={dateTimeFormat.includes(cellValue) ? cellValue : ""}
                  className={`white-space-nowrap format-arrow-pos form-control ${
                    !dateTimeFormat.includes(params.value)
                      ? "border-red-parent"
                      : ""
                  }`}
                  style={{ width: "100%", height: 35 }}
                  onChange={handleChange}
                >
                  <option key={"Select..."} value={""}>
                    Select...
                  </option>
                  {dateTimeFormat.map((format: string, index: number) => (
                    <option key={format + index} value={format}>
                      {format}
                    </option>
                  ))}
                </NativeSelect>
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 165,
      renderCell: (params) => {
        const handleDelete = () => {
          setResourceColumnFileData((prev: any) =>
            prev.filter((item: any) => item.id !== params.row.id)
          );
          const updatedColumns = manageAvailableColumns(
            availColumns,
            params.row.column_name,
            "remove"
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
        };

        const handleAddNewRow = () => {
          let newRow = { ...resourceCDMColumns };
          const currentRowIdx = resourceColumnFileData.findIndex(
            (item: any) => item.id === params.row.id
          );
          const highestId = resourceColumnFileData.reduce(
            (maxId: any, item: any) => Math.max(maxId, item.id),
            -1
          );
          newRow.id = highestId + 1;
          const updatedData = [...resourceColumnFileData];
          updatedData.splice(currentRowIdx + 1, 0, newRow);
          setResourceColumnFileData(updatedData);
        };

        return (
          <>
            <Tooltip title="Add Validation" placement="top" arrow>
              <IconButton
                color="inherit"
                onClick={() => {
                  handleAddValidation(params.row.id);
                  //updateCDMColumns();
                }}
                className="datagrid-action-btn"
              >
                {/* <AddOutlined /> */}
                <IconPlusBase />
              </IconButton>
            </Tooltip>
            {params.row.is_derived && (
              <Tooltip title="Edit Derived Column" placement="top" arrow>
                <IconButton
                  color="inherit"
                  onClick={() => {
                    // onAddDerivedColumn();
                    setOpenDerivedColumnDialog(true);
                    setDerivedModalType("edit");
                    setSelectedRowId(params.row.id);
                  }}
                  className="datagrid-action-btn"
                >
                  <IconEditBase />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Add Column" placement="top" arrow>
              <Button
                color="secondary"
                onClick={handleAddNewRow}
                className="datagrid-action-btn min-width-40"
              >
                <IconAddRowBase />
              </Button>
            </Tooltip>
            <Tooltip title="Delete Row" placement="top" arrow>
              <IconButton
                color="inherit"
                onClick={handleDelete}
                className="datagrid-action-btn"
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  // Set forma data in state
  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (e.target.name === "name") validateField("name", e.target.value);
    if (e.target.name === "domain_code")
      validateField("domain_code", e.target.value);

    const { name, value } = e.target;
    if (name === "domain_name") {
      setErrors((prevError: any) => ({
        ...prevError,
        domain_name: value ? "" : "Please enter domain name",
        domain_id: "",
        code: "",
      }));
    }
  };
  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addResourceCombinedColumnSchema.validateAt(name, partialFormData);

      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const handleDownloadSampleFile = () => {
    const excelFilePath = require("../../assets/sample_files/sample_file.xlsx");
    saveAs(excelFilePath, "CDM_AllData_Sample.xlsx");
  };

  // handel event when we save a resource

  const handleFileUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = e.target?.result;
      const workbook = read(data, { type: "binary" });
      const firstSheet = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheet];
      const json: any = utils.sheet_to_json(worksheet, { defval: "" });
      const headers: string[] = Object.keys(json[0]).filter(
        (header) => !/^__EMPTY/.test(header)
      );
      const unmatchedHeaders = expectedResourceColumnHeaders.filter(
        (header: any) => !headers.includes(header)
      );
      if (unmatchedHeaders.length >= 1) {
        showToast(
          <FileHeaderToastifyMsg unmatchedHeaders={unmatchedHeaders} />,
          "warning"
        );
        setResourceColumnFileData([defaultResourceCDMColumns]);
        setAvailColumns([]);
        setAvailColumnsWithResourceDetail(null);
        return;
      } else {
        const hasReservedKey = json.some((fileItem: any) => {
          return fileItem["Column Name"].trim().toLowerCase() === "file_name";
        });
        const doaminsTableData: any = [];
        const cdmData: string[] = [];
        const updatedJson = json.filter(
          (fileItem: any) => fileItem["Column Name"].trim() !== "file_name"
        );
        const initialFileData = updatedJson.map(
          (fileItem: any, index: number) => {
            cdmData.push(fileItem["Column Name"].trim());
            doaminsTableData.push({
              name: fileItem["Column Name"].trim() ?? "",
            });
            const dataType: any =
              typeof fileItem["Data Type"] === "string"
                ? fileItem["Data Type"].trim().toLowerCase()
                : "";
            const isVarChar = dataType && dataType === "varchar";
            const severity =
              typeof fileItem["Severity Level"] === "string"
                ? fileItem["Severity Level"].toLowerCase()
                : "Low";
            return {
              id: index + 1, // changed because we need to add a default column with fileName
              domain_column:
                currentType === "new" ? fileItem["Column Name"].trim() : "",
              column_name: fileItem["Column Name"].trim() ?? "",
              datatype:
                isVarChar || datatypes.includes(dataType)
                  ? setDataType(dataType)
                  : "",
              is_mandatory:
                (typeof fileItem["Mandatory"] === "string"
                  ? fileItem["Mandatory"].trim()
                  : fileItem["Mandatory"] === 1
                  ? true
                  : false) ?? "",
              data_format: fileItem["Data Format"] ?? null,
              key:
                (typeof fileItem["Key"] === "string"
                  ? fileItem["Key"].trim()
                  : fileItem["Key"] === 1
                  ? true
                  : false) ?? "",
              is_active: fileItem["is_active"] ?? true,
              action: "",
              column_length: fileItem["Column Length"] ?? "",
              severity_level: severity_level.includes(severity)
                ? setSeverityLevel(severity)
                : "Low",
              reference: fileItem["Reference"] ?? "",
            };
          }
        );
        setDomainsTableData(doaminsTableData);
        cdmData.unshift("file_name");
        setAvailColumns(cdmData);
        setResourceColumnsByUpload((prev: any) => ({
          ...prev,
          resource_column_properties: {
            ...prev.resource_column_properties,
            resource_columns: cdmData.map((item: any) => {
              return { column_name: item, is_active: true };
            }),
          },
        }));
        setAvailColumnsWithResourceDetail(null);
        selectedDomain?.domain_properties?.columns.forEach((sdcItem: any) => {
          initialFileData.forEach((ifd: any) => {
            if (ifd.column_name === sdcItem.name) {
              ifd.domain_column = sdcItem.name;
            }
          });
        });
        initialFileData.unshift({ ...defaultResourceCDMColumns });
        setResourceColumnFileData([...initialFileData]);
        showToast(`${file.name} file uploaded successfully`, "success");
      }
    };
    reader.readAsBinaryString(file as Blob);
    setErrors((prevError: any) => ({
      ...prevError,
      uploadFile: "",
    }));
    setIsFileUpload(false);
  };

  const onAddDerivedColumn = () => {
    setDerivedModalType("add");
    setOpenDerivedColumnDialog(true);
    setSelectedRowId(null);
    setQueryBuilderTempValue("");
    //updateCDMColumns();
  };

  if (backdropLoading)
    return (
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={true}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    );

  const tabContent: any = {
    resourceColumns: (
      <Box>
        <FlexBetween gap="3rem" sx={{ marginTop: "-20px" }}>
          <DataTable
            dataColumns={columns}
            dataRows={resourceColumnFileData}
            checkboxSelection={false}
            dataListTitle="Resource Info"
            buttonText="Derived Column"
            buttonClass="btn-orange btn-dark btn-sm"
            buttonClick={onAddDerivedColumn}
            buttonIcon={<AddCircleOutlineIcon />}
            className="dataTable no-radius table-focused"
            handleEditValidationDialog={handleEditValidationDialog}
            handleDeleteValidation={handleDeleteValidation}
            handleEditReferenceDialog={handleEditReferenceDialog}
            handleDeleteReference={handleDeleteReference}
            handleAddMoreRow={handleAddMoreRow}
            handleDeleteAllRows={handleDeleteAllRows}
          />
        </FlexBetween>
      </Box>
    ),
    crossFieldValidations: (
      <CrossFieldValidations
        crossFieldValidations={crossFieldValidations}
        setCrossFieldValidations={setCrossFieldValidations}
        isViewOnly={false}
        errors={errors}
        setErrors={setErrors}
      />
    ),
  };

  return (
    <Box>
      <Grid
        container
        rowSpacing={1.5}
        columnSpacing={{ xl: 2.5, lg: 2.5, md: 2, sm: 2, xs: 1 }}
        justifyContent="space-between"
      >
        <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
          {!isTableRequired && (
            <Grid
              container
              rowSpacing={1.5}
              columnSpacing={{ xl: 2.5, lg: 2.5, md: 2, sm: 2, xs: 1 }}
            >
              {resourceColumnData?.currentResourceColType !== "existing" && (
                <>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">
                      Resource Column Details Name
                      <span className="required-asterisk">*</span>
                    </label>
                    <TextField
                      type="text"
                      // required
                      name="name"
                      // placeholder="Ex: Sample Resource Column Details Name"
                      onChange={handleFormData}
                      className={`form-control-autocomplete ${
                        errors?.name ? "has-error" : ""
                      }`}
                      value={formData.name || ""}
                      error={!!errors?.name}
                      helperText={errors?.name}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">
                      Resource Column Code
                      <span className="required-asterisk">*</span>
                    </label>
                    <TextField
                      type="text"
                      // required
                      name="resource_column_details_code"
                      // placeholder="Ex: Sample Resource Column Details Name"
                      onChange={(e: any) => {
                        handleFormData(e);
                        validateField(e.target.name, e.target.value);
                      }}
                      className={`form-control-autocomplete ${
                        errors?.resource_column_details_code ? "has-error" : ""
                      }`}
                      value={formData.resource_column_details_code || ""}
                      error={!!errors?.resource_column_details_code}
                      helperText={errors?.resource_column_details_code}
                    />
                  </Grid>
                </>
              )}

              {resourceColumnData?.currentResourceColType === "upload" && (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    <span className="position-relative inline-block">
                      Upload file
                      <Tooltip
                        title="Download Sample File for dummy resource columns"
                        placement="top"
                        arrow
                      >
                        <span className="upload-icon-info">
                          <InfoIcon onClick={handleDownloadSampleFile} />
                        </span>
                      </Tooltip>
                    </span>
                  </label>
                  <FileUploadButton
                    className={`fileUploadButton`}
                    onFileUpload={handleFileUpload}
                    isFileUpload={isFileUpload}
                    fileData={resourceColumnFileData}
                  />
                </Grid>
              )}
            </Grid>
          )}
          {isTableRequired && (
            <>
              <Tabs
                sx={{
                  mt: 1,
                }}
                value={activeTab}
                onChange={handleChangeTab}
                className="mui-tabs alternative-1"
                variant="scrollable"
              >
                <Tab label="Resource Columns" value="resourceColumns" />
                <Tab label="Adhoc Query" value="crossFieldValidations" />
              </Tabs>
              <Box>{tabContent[activeTab]}</Box>
            </>
          )}
        </Grid>
      </Grid>

      <ValidationDialog setAllVariablesList={setAllVariablesList} />
      <DerivedColumnDialog setAllVariablesList={setAllVariablesList} />
      <ReferenceDialog setAllVariablesList={setAllVariablesList} />

      <ConfirmDailog
        isDailogOpen={isDailogOpen}
        handleCancelAction={handleCancelDeleteAction}
        handleConfirmAction={handleConfirmDeleteAction}
        dailogTitle={"Confirm Delete"}
        dailogDescription={
          "This action will delete all rows, still want to continue?"
        }
      />
    </Box>
  );
};

export default AddResourceColumnsComponent;
