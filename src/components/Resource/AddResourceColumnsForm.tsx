import React, { useState } from "react";
import { <PERSON>rid, TextField, Tooltip } from "@mui/material";

import FileUploadButton from "../Uploaders/FileUpload";
import InfoIcon from "@mui/icons-material/Info";
import { addResourceCombinedColumnSchema } from "../../schemas";
import { useResourceContext } from "../../contexts/ResourceContext";
import { saveAs } from "file-saver";
import { read, utils } from "xlsx";
import { expectedResourceColumnHeaders } from "../../services/constants/fileHeaders";
import { useToast } from "../../services/utils";
import FileHeaderToastifyMsg from "../../components/Toastify/FileHeaderToastifyMsg";
import { defaultResourceCDMColumns } from "../../services/constants/resource";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { datatypes, severity_level } from "../../services/constants";
import { IDomainsData } from "../../types/domain";
import { setDataType, setSeverityLevel } from "../../services/utils";

interface AddResourceColumnsProps {
  resourceColumnData?: any;
  useResourceColumns?: any;
  crossFieldValidations?: any;
  setCrossFieldValidations?: any;
  setResourceColumnsByUpload?: any;
  domainsTableData?: any;
  setDomainsTableData?: any;
  currentDomainType?: any;
  setIsFileUpload?: any;
  isFileUpload?: any;
}

const AddResourceColumnsForm: React.FC<AddResourceColumnsProps> = ({
  resourceColumnData,
  setResourceColumnsByUpload,
  setDomainsTableData,
  currentDomainType,
  setIsFileUpload,
  isFileUpload,
}: AddResourceColumnsProps) => {
  const {
    resourceColumnFileData,
    setResourceColumnFileData,
    formData,
    setFormData,

    errors,
    setErrors,
  } = useResourceContext();
  const { setAvailColumns, setAvailColumnsWithResourceDetail } =
    useRuleResourceContext();

  const { showToast } = useToast();

  const [selectedDomain, setSelectedDomain] = useState<IDomainsData>();

  // Set forma data in state
  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (e.target.name === "name") validateField("name", e.target.value);
    if (e.target.name === "domain_code")
      validateField("domain_code", e.target.value);

    const { name, value } = e.target;
    if (name === "domain_name") {
      setErrors((prevError: any) => ({
        ...prevError,
        domain_name: value ? "" : "Please enter domain name",
        domain_id: "",
        code: "",
      }));
    }
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addResourceCombinedColumnSchema.validateAt(name, partialFormData);

      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleDownloadSampleFile = () => {
    const excelFilePath = require("../../assets/sample_files/sample_file.xlsx");
    saveAs(excelFilePath, "CDM_AllData_Sample.xlsx");
  };

  const handleFileUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = e.target?.result;
      const workbook = read(data, { type: "binary" });
      const firstSheet = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheet];
      const json: any = utils.sheet_to_json(worksheet, { defval: "" });
      const headers: string[] = Object.keys(json[0]).filter(
        (header) => !/^__EMPTY/.test(header)
      );
      const unmatchedHeaders = expectedResourceColumnHeaders.filter(
        (header: any) => !headers.includes(header)
      );
      if (unmatchedHeaders.length >= 1) {
        showToast(
          <FileHeaderToastifyMsg unmatchedHeaders={unmatchedHeaders} />,
          "warning"
        );
        setResourceColumnFileData([defaultResourceCDMColumns]);
        setAvailColumns([]);
        setAvailColumnsWithResourceDetail(null);
        return;
      } else {
        const hasReservedKey = json.some((fileItem: any) => {
          return fileItem["Column Name"].trim().toLowerCase() === "file_name";
        });
        const domainsTableData: any = [];
        const cdmData: string[] = [];
        const updatedJson = json.filter(
          (fileItem: any) => fileItem["Column Name"].trim() !== "file_name"
        );
        const initialFileData = updatedJson.map(
          (fileItem: any, index: number) => {
            cdmData.push(fileItem["Column Name"].trim());
            domainsTableData.push({
              name: fileItem["Column Name"].trim() ?? "",
            });
            const dataType: any =
              typeof fileItem["Data Type"] === "string"
                ? fileItem["Data Type"].trim().toLowerCase()
                : "";
            const isVarChar = dataType && dataType === "varchar";
            const severity =
              typeof fileItem["Severity Level"] === "string"
                ? fileItem["Severity Level"].toLowerCase()
                : "Low";
            return {
              id: index + 1, // changed because we need to add a default column with fileName
              domain_column: fileItem["Column Name"].trim() ?? "",
              column_name: fileItem["Column Name"].trim() ?? "",
              datatype:
                isVarChar || datatypes.includes(dataType)
                  ? setDataType(dataType)
                  : "",
              is_mandatory:
                (typeof fileItem["Mandatory"] === "string"
                  ? fileItem["Mandatory"].trim()
                  : fileItem["Mandatory"] === 1
                  ? true
                  : false) ?? "",
              data_format: fileItem["Data Format"] ?? null,
              key:
                (typeof fileItem["Key"] === "string"
                  ? fileItem["Key"].trim()
                  : fileItem["Key"] === 1
                  ? true
                  : false) ?? "",
              is_active: fileItem["is_active"] ?? true,
              action: "",
              column_length: fileItem["Column Length"] ?? "",
              severity_level: severity_level.includes(severity)
                ? setSeverityLevel(severity)
                : "Low",
              reference: fileItem["Reference"] ?? "",
            };
          }
        );
        setDomainsTableData(domainsTableData);
        cdmData.unshift("file_name");
        setAvailColumns(cdmData);
        setResourceColumnsByUpload((prev: any) => ({
          ...prev,
          resource_column_properties: {
            ...prev.resource_column_properties,
            resource_columns: cdmData.map((item: any) => {
              return { column_name: item, is_active: true };
            }),
          },
        }));
        setAvailColumnsWithResourceDetail(null);
        selectedDomain?.domain_properties?.columns.forEach((sdcItem: any) => {
          initialFileData.forEach((ifd: any) => {
            if (ifd.column_name === sdcItem.name) {
              ifd.domain_column = sdcItem.name;
            }
          });
        });
        initialFileData.unshift({ ...defaultResourceCDMColumns });
        setResourceColumnFileData([...initialFileData]);
        showToast(`${file.name} file uploaded successfully`, "success");
      }
    };
    reader.readAsBinaryString(file as Blob);
    setErrors((prevError: any) => ({
      ...prevError,
      uploadFile: "",
    }));
    setIsFileUpload(false);
  };

  return (
    <div>
      <Grid
        container
        rowSpacing={1.5}
        columnSpacing={{ xl: 2.5, lg: 2.5, md: 2, sm: 2, xs: 1 }}
      >
        {resourceColumnData?.currentResourceColType !== "existing" && (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                Resource Column Details Name
                <span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                // required
                name="name"
                // placeholder="Ex: Sample Resource Column Details Name"
                onChange={handleFormData}
                className={`form-control-autocomplete ${
                  errors?.name ? "has-error" : ""
                }`}
                value={formData.name || ""}
                error={!!errors?.name}
                helperText={errors?.name}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                Resource Column Code
                <span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                // required
                name="resource_column_details_code"
                // placeholder="Ex: Sample Resource Column Details Name"
                onChange={(e: any) => {
                  handleFormData(e);
                  validateField(e.target.name, e.target.value);
                }}
                className={`form-control-autocomplete ${
                  errors?.resource_column_details_code ? "has-error" : ""
                }`}
                value={formData.resource_column_details_code || ""}
                error={!!errors?.resource_column_details_code}
                helperText={errors?.resource_column_details_code}
              />
            </Grid>
          </>
        )}

        {resourceColumnData?.currentResourceColType === "upload" && (
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <label className="label-text">
              <span className="position-relative inline-block">
                Upload file
                <Tooltip
                  title="Download Sample File for dummy resource columns"
                  placement="top"
                  arrow
                >
                  <span className="upload-icon-info">
                    <InfoIcon onClick={handleDownloadSampleFile} />
                  </span>
                </Tooltip>
              </span>
            </label>
            <FileUploadButton
              className={`fileUploadButton`}
              onFileUpload={handleFileUpload}
              isFileUpload={isFileUpload}
              fileData={resourceColumnFileData}
            />
          </Grid>
        )}
      </Grid>
    </div>
  );
};

export default AddResourceColumnsForm;
