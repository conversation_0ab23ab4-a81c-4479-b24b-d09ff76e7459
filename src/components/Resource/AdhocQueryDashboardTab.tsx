import { useState, useEffect } from "react";
import RulesListDataTable from "../DataGrids/RulesListDataGrid";
import CustomAccordion from "../Molecules/Accordian/CustomAccordion";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import useFetchPaginatedCrossFieldValidations from "../../hooks/useFetchPaginatedCrossFieldValidations";

interface IAdhocQueryDashboardTab {
  validateResult?: any;
  adhocQueryData?: any;
}

interface INewAdhocQueryDetail {
  adhocQueryName: any;
  adhocDetailData: any;
  isLoading: boolean;
  expandedAccordion: string | undefined;
  setExpandedAccordion: React.Dispatch<React.SetStateAction<any>>;
  pageN: number;
  setPageN: React.Dispatch<React.SetStateAction<number>>;
  pageS: number;
  setPageS: React.Dispatch<React.SetStateAction<number>>;
  totalPages: number;
}

const AdhocQueryDashboardTab = ({
  validateResult,
  adhocQueryData,
}: IAdhocQueryDashboardTab) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [adhocData, setAdhocData] = useState<any>([]);
  const [expandedAccordion, setExpandedAccordion] = useState<
    string | undefined
  >("");

  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  const [crossFieldValidation] = useFetchPaginatedCrossFieldValidations({
    currentValidationResultId: validateResult?.id,
    setIsLoading,
    page,
    pSize,
    isDetailedExecutionDataAvailable:
      validateResult?.is_detailed_execution_data_available,
  });
  useEffect(() => {
    if (crossFieldValidation?.items?.length > 0) {
      setAdhocData(crossFieldValidation?.items);
    } else if (crossFieldValidation?.items?.length === 0) {
      setAdhocData([]);
    }
  }, [crossFieldValidation]);

  useEffect(() => {
    if (!validateResult?.additional_properties?.cross_field_validation_results)
      return;
    if (validateResult?.is_detailed_execution_data_available) {
      setAdhocData(
        validateResult?.additional_properties?.cross_field_validation_results
      );
    }
    if (
      adhocQueryData?.additional_properties?.cross_field_validation_results
        ?.length > 0
    ) {
      setAdhocData(
        adhocQueryData?.additional_properties?.cross_field_validation_results
      );
    }
  }, [validateResult, adhocQueryData]);
  return (
    <div>
      {validateResult?.additional_properties?.cross_field_validation_results
        ?.length > 0 ? (
        <>
          {validateResult?.additional_properties?.cross_field_validation_results?.map(
            (item: any, index: any) => {
              return validateResult?.is_detailed_execution_data_available ? (
                <AdhocQueryDetail
                  key={index}
                  adhocDetailData={item}
                  isLoading={isLoading}
                />
              ) : (
                <NewAdhocQueryDetail
                  key={index}
                  adhocQueryName={item?.name || ""}
                  adhocDetailData={adhocData}
                  isLoading={isLoading}
                  expandedAccordion={expandedAccordion}
                  setExpandedAccordion={setExpandedAccordion}
                  pageN={page}
                  setPageN={setPage}
                  pageS={pSize}
                  setPageS={setPSize}
                  totalPages={crossFieldValidation?.total}
                />
              );
            }
          )}
        </>
      ) : (
        <Box
          sx={{
            paddingTop: "24px",
            paddingBottom: "24px",
            display: "flex",
            justifyContent: "center",
          }}
        >
          No data available
        </Box>
      )}
    </div>
  );
};

export default AdhocQueryDashboardTab;

const AdhocQueryDetail = ({ adhocDetailData, isLoading }: any) => {
  const [columnsData, setColumnsData] = useState<any>([]);
  const [rowsData, setRowsData] = useState<any>([]);
  useEffect(() => {
    if (adhocDetailData) {
      const firstResult = adhocDetailData.result;
      if (firstResult && firstResult.length > 0) {
        const columns = Object.keys(firstResult[0]).map((key) => ({
          field: key,
          headerName: key,
          flex: 1,
          minWidth: 120,
        }));

        const rows = firstResult.map((item: any, index: any) => ({
          id: index,
          ...item,
        }));

        setColumnsData(columns);
        setRowsData(rows);
      }
    }
  }, [adhocDetailData]);

  return (
    <>
      <CustomAccordion
        key={adhocDetailData?.name}
        expandId={`ruleCol.name_+${adhocDetailData?.name.replace(" ", "")}`}
        title={adhocDetailData?.name}
        isEnabled={true}
      >
        <RulesListDataTable
          dataRows={rowsData}
          dataColumns={columnsData}
          loading={isLoading}
          dataListTitle="Rules Dashboard"
          className="dataTable no-radius pt-0 bdr-top-0"
          disableColumnFilter={true}
          tableHeight={180}
          singlePageMaxHeightDiff={362}
        />
      </CustomAccordion>
    </>
  );
};
const NewAdhocQueryDetail = ({
  adhocQueryName,
  adhocDetailData,
  isLoading,
  expandedAccordion,
  setExpandedAccordion,
  pageN,
  setPageN,
  pageS,
  setPageS,
  totalPages,
}: INewAdhocQueryDetail) => {
  const [columnsData, setColumnsData] = useState<any>([]);
  const [rowsData, setRowsData] = useState<any>([]);

  // Ensure proper handling of column data when the detail data is updated
  useEffect(() => {
    if (adhocDetailData?.length > 0) {
      const columns = Object.keys(
        adhocDetailData[0]?.cross_field_validation_results?.result
      ).map((key) => ({
        field: key,
        headerName: key,
        flex: 1,
        minWidth: 120,
      }));

      const rows = adhocDetailData.map((item: any, index: any) => ({
        id: index,
        ...item?.cross_field_validation_results?.result,
      }));

      setColumnsData(columns);
      setRowsData(rows);
    } else {
      setColumnsData([]);
      setRowsData([]);
    }
  }, [adhocDetailData]);

  // Handle accordion state change
  const handleChangeAccordion =
    (adhocQueryName: string | undefined) =>
    (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedAccordion(isExpanded ? adhocQueryName : "");
    };

  // Construct a unique ID for each panel
  const panelId = `ruleCol.name_+${adhocQueryName?.replace(" ", "")}`;

  return (
    <>
      <Box className="accordion-panel" sx={{ padding: "8px 0" }}>
        <Accordion
          className="heading-bold box-shadow"
          expanded={expandedAccordion === adhocQueryName}
          onChange={handleChangeAccordion(adhocQueryName)}
          sx={{ marginTop: "8px" }}
        >
          <AccordionSummary
            aria-controls="panel1d-content"
            id={panelId}
            expandIcon={<ExpandMoreIcon />}
            className="min-header"
          >
            {adhocQueryName}
          </AccordionSummary>
          <AccordionDetails
            sx={{ paddingTop: "16px" }}
            // style={{ display: "block" }}
          >
            <RulesListDataTable
              dataRows={rowsData}
              dataColumns={columnsData}
              loading={isLoading}
              dataListTitle="Rules Dashboard"
              className="dataTable no-radius pt-0 bdr-top-0"
              disableColumnFilter={true}
              tableHeight={180}
              disableColumnReorder={true}
              paginationMode="server"
              rowCount={totalPages || 0}
              pageSizeOptions={[25]}
              paginationModel={{
                page: pageN - 1,
                pageSize: pageS,
              }}
              onPaginationModelChange={(params: any) => {
                if (params.pageSize !== pageS || params.page !== pageN - 1) {
                  setPageN(params.page + 1);
                  setPageS(params.pageSize);
                }
              }}
              singlePageMaxHeightDiff={362}
            />
          </AccordionDetails>
        </Accordion>
      </Box>
    </>
  );
};
