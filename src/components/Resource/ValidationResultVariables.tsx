import React from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

interface IValidationResultVariables {
  resourceVariables: any;
}

const ValidationResultVariables = ({
  resourceVariables,
}: IValidationResultVariables) => {
  return (
    <Box className="accordion-panel" sx={{ marginBottom: "8px" }}>
      <Accordion className="heading-bold">
        <AccordionSummary
          aria-controls="panel1d-content"
          id="panel1d-header"
          expandIcon={<ExpandMoreIcon />}
        >
          Variables
        </AccordionSummary>
        <AccordionDetails sx={{ paddingTop: "16px" }}>
          <Grid container spacing={2.5}>
            {Object.keys(resourceVariables).length > 0 && (
              <Grid item xs>
                <table className="inline-variables-table">
                  <tbody>
                    {Object.keys(resourceVariables).map(
                      (key: string, index: number) => {
                        return (
                          <tr key={index}>
                            <td>
                              <div className="inner-column">
                                <div className="label">
                                  <strong>{key}</strong>
                                  <span className="required-asterisk">*</span>:
                                </div>
                                <input
                                  className="form-control-1 read-only"
                                  value={resourceVariables[key]}
                                  name={key}
                                  title={resourceVariables[key]}
                                  readOnly
                                />
                              </div>
                            </td>
                          </tr>
                        );
                      }
                    )}
                  </tbody>
                </table>
              </Grid>
            )}
          </Grid>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default ValidationResultVariables;
