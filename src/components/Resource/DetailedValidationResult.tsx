import React, { useEffect, useState } from "react";
import { Box, Grid, Icon<PERSON><PERSON>on, Tab, Tabs } from "@mui/material";

import ReadMoreLess from "../ReadMoreLess";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ValidationSummaryDataGrid from "../DataGrids/ValidationSummaryDataGrid";
import ResourceSupportDocuments from "../Organisms/ResourceSupportDocuments";
import AdhocQueryDashboardTab from "./AdhocQueryDashboardTab";
import Incidents from "../Rules/Incidents";
import Loader from "../Molecules/Loader/Loader";
import ValidationResultHeader from "../Molecules/Resource/ValidationResultHeader";
import ValidationResultResourceStatistics from "../Molecules/Resource/ValidationResultResourceStatistics";
import ValidationResultParameters from "../Molecules/Resource/ValidationResultParameters";
import ValidationResultVariables from "./ValidationResultVariables";

interface IDetailedValidationResult {
  validateResult: any;
  isLoading: boolean;
  setIsLoading: any;
  isBackdropLoading: boolean;
  currentIncidentData: any;
  setIsTriggereBtnPressed: any;
  setIsCommentBtnPressed: any;
  currentResourceResultId: any;
}

const DetailedValidationResult = ({
  validateResult,
  isLoading,
  setIsLoading,
  isBackdropLoading,
  currentIncidentData,
  setIsTriggereBtnPressed,
  setIsCommentBtnPressed,
  currentResourceResultId,
}: IDetailedValidationResult) => {
  const [rowsData, setRowsData] = useState<any>([]);
  const [columnsData, setColumnsData] = useState<any>([]);
  const [gridColumnLength, setGridColumnLength] = useState<number>(0);
  const [selectedRowID, setSelectedRowID] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<string>("validationErrorsSummary");

  const getFormattedKeysPairs = (inputString: string) => {
    const pairs: any = {};
    inputString.split(",").forEach((pair) => {
      const [key, value] = pair.trim().split("=");
      pairs[key] = value;
    });
    return pairs;
  };

  const handelRowData = (validation_errors: any) => {
    const rows: any = [];
    validation_errors?.forEach((item: any, idx: any) => {
      const { key_columns, ...rest } = item ?? {};
      const row: any = {
        id: idx,
        ...getFormattedKeysPairs(item?.key_columns),
        ...rest,
      };
      rows.push(row);
    });
    setRowsData(rows);
  };

  const handelSummaryRowData = (validation_errors: any) => {
    const { key_columns, key_column_values } = validation_errors || {};
    if (key_column_values?.length > 0 && key_columns?.length > 0) {
      const rowsData = key_column_values.map((values: any, index: any) => {
        const rowObject: any = {};
        key_columns.forEach((column: any, columnIndex: any) => {
          rowObject[column] = values[columnIndex];
        });
        rowObject.id = index + 1;
        return rowObject;
      });
      setRowsData(rowsData);
    }
  };

  const getColumns = (validation_errors: any) => {
    if (validation_errors && validation_errors.length > 0) {
      const firstColumns: any = Object.keys(
        getFormattedKeysPairs(validation_errors[0]?.key_columns)
      );
      const secondColumns: any = Object.keys(validation_errors[0]).filter(
        (item) => item !== "key_columns" && item !== "file_names"
      );
      const columns: any = [
        {
          ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
          renderCell: (params: any) => {
            const customValidationKeys = Object.keys(params?.row).filter(
              (key) => key.includes("_custom_validation")
            );
            const isCustomValidation = customValidationKeys.every(
              (key) =>
                params?.row.hasOwnProperty(key) &&
                (params?.row[key] === null || params?.row[key] === "")
            );
            return (
              <>
                {!isCustomValidation && (
                  <IconButton
                    size="small"
                    tabIndex={-1}
                    className="custom-validation-arrow"
                  >
                    <ExpandMoreIcon />
                  </IconButton>
                )}
              </>
            );
          },
        },

        ...firstColumns.map((item: any) => {
          let newItem = item.replace(/^['"]|['"]$/g, "");
          setGridColumnLength(firstColumns.length);
          return {
            field: item,
            headerName: newItem.toUpperCase(),
            flex: 1,
            maxWidth: 160,
          };
        }),
        ...secondColumns.map((item: any) => {
          let newItem = item.replace(/^['"]|['"]$/g, "");
          return {
            field: item,
            headerName: newItem.toUpperCase(),
            flex: 1,
            renderCell: (params: any) => {
              return (
                <>
                  {params.value && params.value.length > 100 ? (
                    <ReadMoreLess data={params?.value?.replace(/"/g, "")} />
                  ) : (
                    <Box className="word-break-all">
                      {params?.value?.replace(/"/g, "")}
                    </Box>
                  )}
                </>
              );
            },
          };
        }),
        {
          field: "file_names",
          headerName: "FILE_NAME(S)",
          flex: 1,
          renderCell: (params: any) => (
            <div
              className="data-grid-cell"
              dangerouslySetInnerHTML={{ __html: params.value }}
            />
          ),
        },
      ];
      setColumnsData(columns);
    }
  };

  const getSummaryColumns = (validation_errors: any) => {
    const { key_columns } = validation_errors || {};
    if (key_columns?.length > 0) {
      const columns: any = key_columns.map((item: any) => {
        return {
          field: item,
          headerName: item.toUpperCase(),
          flex: 1,
          maxWidth: 160,
        };
      });
      setColumnsData([
        {
          ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
          renderCell: (params: any) => {
            const customValidationKeys = Object.keys(params?.row).filter(
              (key) => key.includes("_custom_validation")
            );
            const isCustomValidation = customValidationKeys.every(
              (key) =>
                params?.row.hasOwnProperty(key) &&
                (params?.row[key] === null || params?.row[key] === "")
            );
            return (
              <>
                {!isCustomValidation && (
                  <IconButton
                    size="small"
                    tabIndex={-1}
                    className="custom-validation-arrow"
                  >
                    <ExpandMoreIcon />
                  </IconButton>
                )}
              </>
            );
          },
        },
        ...columns,
      ]);
    }
  };

  const validationColumns: any = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => {
        const rowId = params?.row?.id;

        const handleClick = () => {
          if (selectedRowID === rowId) {
            setSelectedRowID(null);
          } else {
            setSelectedRowID(rowId);
          }
        };
        return (
          <IconButton
            size="small"
            tabIndex={-1}
            className="custom-validation-arrow"
            onClick={handleClick}
          >
            <ExpandMoreIcon />
          </IconButton>
        );
      },
    },
    {
      field: "column_name",
      headerName: "Column Name",
      flex: 1,
      maxWidth: 280,
    },
    {
      field: "total_validation_errors",
      headerName: "Total Validation Errors",
      flex: 1,
      maxWidth: 280,
    },
    {
      field: "validation_severity",
      headerName: "Validation Severity",
      flex: 1,
      maxWidth: 280,
      renderCell: (params: any) => {
        const transformedValue = params.value
          .split("_")
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");

        return params.value;
      },
    },
  ];

  const validationRows: any =
    validateResult?.additional_properties?.validation_errors_summary?.map(
      (item: any, index: any) => {
        const sanitizedValidationErrors = item?.validation_errors?.map(
          (error: any) => {
            const newArr =
              error?.file_names &&
              error?.file_names?.replace(/'/g, "").split(",");
            return {
              ...error,
              file_names:
                newArr && newArr.length > 1
                  ? `<ul class="ul-inside-grid">${newArr
                      .map((arr: any) => `<li>${arr}</li>`)
                      .join("")}</ul>`
                  : `<div class="word-break-all">${
                      (newArr && newArr[0]) || ""
                    }<div>`,
            };
          }
        );

        return {
          id: index,
          ...item,
          validation_errors: sanitizedValidationErrors,
        };
      }
    );

  useEffect(() => {
    const validationErrorsData =
      validateResult?.additional_properties?.validation_errors_summary?.[
        selectedRowID
      ]?.validation_errors;

    if (validationErrorsData !== null) {
      if (validateResult?.additional_properties?.summary_mode) {
        getSummaryColumns(validationErrorsData);
        handelSummaryRowData(validationErrorsData);
      } else {
        getColumns(validationErrorsData);
        handelRowData(validationErrorsData);
      }
    }
  }, [validateResult, selectedRowID]);
  const {
    resource_name,
    file_name,
    linked_service_code,
    connection_key,
    resource_path,
    resource_id,
    ...otherProps
  } = validateResult?.validation_parameters || {};

  const tabContent: any = {
    validationErrorsSummary: (
      <>
        {validationRows?.length > 0 ? (
          <ValidationSummaryDataGrid
            dataColumns={validationColumns || []}
            dataRows={validationRows || []}
            loading={isLoading}
            setLoading={setIsLoading}
            isExportButtonRequired={true}
            className="dataTable no-radius"
            rowCount={rowsData?.total}
            pageSizeOptions={[25]}
            historyType={"Validation"}
            disableColumnFilter={false}
            validationErrorProps={{
              dataRows: rowsData,
              dataColumns: columnsData,
              loading: isLoading,
              className: "dataTable no-radius",
              gridColumnLength: gridColumnLength,
              dataListTitle: "",
              isHeight100: true,
            }}
          />
        ) : (
          <Box
            sx={{
              paddingTop: "24px",
              paddingBottom: "24px",
              display: "flex",
              justifyContent: "center",
            }}
            className="adhock-query-accordion-wrapper"
          >
            No data available
          </Box>
        )}
      </>
    ),
    crossFieldValidations: (
      <Box className="adhock-query-accordion-wrapper">
        <AdhocQueryDashboardTab
          validateResult={validateResult}
          adhocQueryData={
            validateResult?.additional_properties
              ?.cross_field_validation_results || []
          }
        />
      </Box>
    ),
  };
  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
    setSelectedRowID(null);
  };

  return (
    <>
      <Loader isLoading={isBackdropLoading} />
      <div className="validate-page">
        {/* Header */}
        <ValidationResultHeader validateResult={validateResult} />

        {/* Resource Statistics */}
        <ValidationResultResourceStatistics validateResult={validateResult} />

        {/* Incidents */}
        <Incidents
          currentResultId={currentResourceResultId || ""}
          currentIncidentData={currentIncidentData ?? []}
          setIsLoading={setIsLoading}
          setIsTriggereBtnPressed={setIsTriggereBtnPressed}
          setIsCommentBtnPressed={setIsCommentBtnPressed}
        />

        {/* Validation Parameters */}
        {validateResult?.complete_validation_params
          ?.validation_request_body && (
          <ValidationResultParameters validateResult={validateResult} />
        )}

        {/* Supporting Documents */}
        {validateResult?.additional_properties?.supporting_documents && (
          <div className="accordion-panel mt-8">
            <ResourceSupportDocuments
              title="Supporting Documents"
              documents={
                validateResult?.additional_properties?.supporting_documents
              }
            />
          </div>
        )}

        {/* Resource Variables */}
        {validateResult?.complete_validation_params?.validation_request_body
          ?.inline_variables && (
          <ValidationResultVariables
            resourceVariables={
              validateResult?.complete_validation_params
                ?.validation_request_body?.inline_variables
            }
          />
        )}

        <Tabs
          sx={{
            mt: 1,
          }}
          value={activeTab}
          onChange={handleChangeTab}
          className="mui-tabs alternative-1"
          variant="scrollable"
        >
          <Tab
            label="Validation Errors Summary"
            value="validationErrorsSummary"
          />
          <Tab label="Adhoc Query" value="crossFieldValidations" />
        </Tabs>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <Box>{tabContent[activeTab]}</Box>
        </Grid>
      </div>
    </>
  );
};

export default DetailedValidationResult;
