import React, { useEffect, useState } from "react";
import {
  Backdrop,
  Box,
  Button,
  Grid,
  IconButton,
  <PERSON>b,
  <PERSON><PERSON>,
  <PERSON>lt<PERSON>,
} from "@mui/material";

import ReadMoreLess from "../ReadMoreLess";
import {
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
} from "@mui/x-data-grid-pro";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ValidationSummaryDataGrid from "../DataGrids/ValidationSummaryDataGrid";
import ResourceSupportDocuments from "../Organisms/ResourceSupportDocuments";
import AdhocQueryDashboardTab from "./AdhocQueryDashboardTab";
import Incidents from "../Rules/Incidents";
import Loader from "../Molecules/Loader/Loader";
import ValidationResultHeader from "../Molecules/Resource/ValidationResultHeader";
import ValidationResultResourceStatistics from "../Molecules/Resource/ValidationResultResourceStatistics";
import ValidationResultParameters from "../Molecules/Resource/ValidationResultParameters";
import ValidationResultVariables from "./ValidationResultVariables";
import useFetchPaginatedValidationErrors from "../../hooks/useFetchPaginatedValidationErrors";
import DuplicateRecords from "./DuplicateRecords";
import NullKeyErrors from "./NullKeyErrors";
import {
  IconIncidentDetails,
  IconIncidentResources,
  IconStatusFail,
  IconStatusInprogress,
  IconStatusNew,
  IconStatusPass,
} from "../../common/utils/icons";
import DashboardUserDetails from "../Molecules/Rule/DashboardUserDetails";
import IncidentUserandStatusDialog from "../Molecules/Rule/IncidentUserandStatusDialog";
import useFetchUsersList from "../../hooks/useFetchUsersList";
import StatusCell from "../../common/utils/statusIcon";
import {
  CommentColumn,
  CurrentStatusColumn,
  UserColumn,
} from "../../common/utils/commonActionsFields";

interface ISummarizesValidationResults {
  validateResult: any;
  isLoading: boolean;
  setIsLoading: any;
  isBackdropLoading: boolean;
  currentIncidentData: any;
  setIsTriggereBtnPressed: any;
  setIsCommentBtnPressed: any;
  currentResourceResultId: any;
}
const initialDashboardData = {
  status: false,
  assigned_user: "",
  issueId: null,
  isBackDropVisible: false,
};

const SummarizesValidationResults = ({
  validateResult,
  isLoading,
  setIsLoading,
  isBackdropLoading,
  currentIncidentData,
  setIsTriggereBtnPressed,
  setIsCommentBtnPressed,
  currentResourceResultId,
}: ISummarizesValidationResults) => {
  const [rowsData, setRowsData] = useState<any>([]);
  const [columnsData, setColumnsData] = useState<any>([]);
  const [gridColumnLength, setGridColumnLength] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<string>("validationErrorsSummary");
  const [selectedColumnName, setSelectedColumnName] = useState<any>("");
  const [dashboardUserDetailsData, setDashboardUserDetailsData] =
    useState(initialDashboardData);
  useEffect(() => {
    if (dashboardUserDetailsData?.status === true) {
      setDashboardUserDetailsData(initialDashboardData);
    }
  }, [activeTab]);

  const [userDialogData, setUserDialogData] = useState<any>({
    status: false,
    dialogName: "",
    selectedIds: [],
  });
  const [allUsersList, setallUsersList] = useState<any>([]);
  const [selectedIds, setSelectedIds] = useState<any>([]);

  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  useEffect(() => {
    setUserDialogData((prev: any) => ({
      ...prev,
      selectedIds: selectedIds || [],
    }));
  }, [selectedIds]);

  const [validationErrorsData] = useFetchPaginatedValidationErrors({
    currentValidationResultId: validateResult?.id,
    setIsLoading,
    page,
    pSize,
    isDetailedExecutionDataAvailable:
      validateResult?.is_detailed_execution_data_available,
    selectedColumnName,
  });

  const [usersList] = useFetchUsersList({
    setIsLoading,
  });

  useEffect(() => {
    setallUsersList(usersList);
  }, [usersList]);

  const getFormattedKeysPairs = (inputString: string) => {
    const pairs: any = {};
    inputString.split(",").forEach((pair) => {
      const [key, value] = pair.trim().split("=");
      pairs[key] = value;
    });
    return pairs;
  };

  const handelRowData = (validationErrors: any) => {
    const rows: any =
      validationErrors?.map((item: any, idx: any) => {
        const { resource_keys, file_name, ...rest } =
          item?.validation_error_record_details ?? {};
        const newArr = file_name?.replace(/'/g, "").split(";") || [];

        return {
          id: idx,
          rowId: item?.id,
          assigned_user: item?.assigned_user,
          current_status: item?.current_status,
          assigned_by: item?.assigned_by,
          comment: item?.comment,
          ...item?.validation_error_record_details,
          ...resource_keys,
          ...rest,
          ...item?.validation_error_record_details?.record_snapshot
            ?.record_data,
          file_name:
            newArr.length > 1
              ? `<div class="word-break-all"><ul class="ul-inside-grid">${newArr
                  .map((arr: any) => `<li>${arr}</li>`)
                  .join("")}</ul></div>`
              : `<div class="word-break-all">${newArr[0] || ""}</div>`,
        };
      }) || [];
    setRowsData(rows);
  };

  const handelSummaryRowData = (validation_errors: any) => {
    const { resource_keys, key_column_values } = validation_errors || {};
    if (key_column_values?.length > 0 && resource_keys?.length > 0) {
      const rowsData = key_column_values.map((values: any, index: any) => {
        const rowObject: any = {};
        resource_keys.forEach((column: any, columnIndex: any) => {
          rowObject[column] = values[columnIndex];
        });
        rowObject.id = index + 1;
        return rowObject;
      });
      setRowsData(rowsData);
    }
  };

  const getColumns = (validation_errors: any) => {
    const recordData =
      validation_errors[0]?.validation_error_record_details?.record_snapshot
        ?.record_data;
    let columnsData: any = [];
    if (validation_errors && validation_errors.length > 0) {
      const firstColumns: any = Object.keys(
        validation_errors[0]?.validation_error_record_details?.resource_keys ||
          {}
      );
      const secondColumns: any = Object.keys(
        validation_errors[0]?.validation_error_record_details
      ).filter(
        (item) => item === "custom_validation_name" || item === "error_message"
      );

      setGridColumnLength(firstColumns.length);

      const columns: any = [
        ...firstColumns.map((item: any) => {
          setGridColumnLength(firstColumns.length);
          return {
            field: item,
            headerName: item.toUpperCase(),
            flex: 1,
            minWidth: 120,
          };
        }),

        ...secondColumns.map((item: any) => {
          let newItem = item.replace(/^['"]|['"]$/g, "");
          return {
            field: newItem,
            headerName: newItem.toUpperCase(),
            flex: 1,
            minWidth: 280,
            renderCell: (params: any) => (
              <>
                {params.value && params.value.length > 100 ? (
                  <ReadMoreLess data={params?.value?.replace(/"/g, "")} />
                ) : (
                  <Box className="word-break-all">
                    {typeof params?.value === "string"
                      ? params.value.replace(/"/g, "")
                      : params?.value}
                  </Box>
                )}
              </>
            ),
          };
        }),

        {
          field: "file_name",
          headerName: "FILE_NAME(S)",
          flex: 1,
          minWidth: 240,
          renderCell: (params: any) => (
            <div
              className="data-grid-cell word-break-all"
              dangerouslySetInnerHTML={{ __html: params.value }}
            />
          ),
        },
      ];
      const issueColumns: any = [
        CurrentStatusColumn({}),
        UserColumn({}),
        CommentColumn({}),
        {
          field: "action",
          headerName: "Action",
          align: "center",
          headerAlign: "center",
          minWidth: "130",
          pinned: "right",
          renderCell: (params: any, index: any) => {
            return (
              <>
                <Box sx={{ display: "inline-flex", columnGap: "12px" }}>
                  <Tooltip
                    title={`Incident Details : ${params?.row?.id}`}
                    placement="top"
                    arrow
                  >
                    <Button
                      className="btn-blue btn-orange btn-sm"
                      onClick={(event: { stopPropagation: () => void }) => {
                        event.stopPropagation();
                        setDashboardUserDetailsData((prev: any) => {
                          return {
                            ...prev,
                            status: true,
                            incidentId: params.row.rowId,
                            assigned_user: params.row.assigned_user,
                            panelType: "incidentDetail",
                            isBackDropVisible: true,
                            comment: [
                              {
                                comment: params.row.comment,
                                create_date: params.row.create_date,
                                assigned_user: params.row?.assigned_user || "",
                              },
                            ],
                            current_status: params.row.current_status,
                            issueId: params.row.rowId,
                          };
                        });
                      }}
                    >
                      <IconIncidentDetails />
                    </Button>
                  </Tooltip>
                </Box>
              </>
            );
          },
        },
      ];
      columnsData = [...columnsData, ...columns, ...issueColumns];

      if (recordData) {
        const snapshot_columns = recordData
          ? Object.keys(recordData)
              .filter(
                (key) =>
                  !firstColumns.includes(key) &&
                  !secondColumns.includes(key) &&
                  key !== "file_name" &&
                  key !== "ErrorMessage"
              )
              .map((key) => ({
                field: key,
                headerName: key,
                flex: 1,
                minWidth: 120,
              }))
          : [];

        columnsData = [...columnsData, ...snapshot_columns];
      }
      setColumnsData(columnsData);
    }
  };

  const getSummaryColumns = (validation_errors: any) => {
    const { resource_keys } = validation_errors || {};
    if (resource_keys?.length > 0) {
      const columns: any = resource_keys.map((item: any) => {
        return {
          field: item,
          headerName: item.toUpperCase(),
          flex: 1,
          maxWidth: 160,
        };
      });
      setColumnsData([
        {
          ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
          renderCell: (params: any) => {
            const customValidationKeys = Object.keys(params?.row).filter(
              (key) => key.includes("_custom_validation")
            );
            const isCustomValidation = customValidationKeys.every(
              (key) =>
                params?.row.hasOwnProperty(key) &&
                (params?.row[key] === null || params?.row[key] === "")
            );
            return (
              <>
                {!isCustomValidation && (
                  <IconButton
                    size="small"
                    tabIndex={-1}
                    className="custom-validation-arrow"
                  >
                    <ExpandMoreIcon />
                  </IconButton>
                )}
              </>
            );
          },
        },
        ...columns,
      ]);
    }
  };

  const validationColumns: any = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => {
        const columnName = params?.row?.column_name;

        const handleClick = () => {
          if (selectedColumnName === columnName) {
            setSelectedColumnName("");
          } else {
            setSelectedColumnName(columnName);
          }
        };
        return (
          <IconButton
            size="small"
            tabIndex={-1}
            className="custom-validation-arrow"
            onClick={handleClick}
          >
            <ExpandMoreIcon />
          </IconButton>
        );
      },
    },
    {
      field: "column_name",
      headerName: "Column Name",
      flex: 1,
      maxWidth: 280,
    },
    {
      field: "total_validation_errors",
      headerName: "Total Validation Errors",
      flex: 1,
      maxWidth: 280,
    },
    {
      field: "validation_severity",
      headerName: "Validation Severity",
      flex: 1,
      maxWidth: 280,
      renderCell: (params: any) => {
        const transformedValue = params.value
          .split("_")
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");

        return params.value;
      },
    },
  ];

  const validationRows =
    validateResult?.additional_properties?.validation_errors_summary?.map(
      (item: any, index: any) => ({
        id: index,
        ...item,
      })
    ) || [];

  useEffect(() => {
    if (validateResult && validationErrorsData?.items?.length > 0) {
      if (validateResult.additional_properties?.summary_mode) {
        getSummaryColumns(validationErrorsData);
        handelSummaryRowData(validationErrorsData.items);
      } else {
        getColumns(validationErrorsData.items);
        handelRowData(validationErrorsData.items);
      }
    }
  }, [validateResult, validationErrorsData]);
  const [isAllColumnPinned, setIsAllColumnPinned] = useState(false);
  const [pinnedColumns, setPinnedColumns] = useState({
    left: [GRID_CHECKBOX_SELECTION_COL_DEF.field],
    right: ["current_status", "username", "comment", "action"],
  });
  const handleMoveColumns = () => {
    setPinnedColumns((prevState) => {
      const columnsToAdd = ["current_status", "username", "comment"];
      const isColumnsAvailable = columnsToAdd.every((item) =>
        prevState.right.includes(item)
      );
      setIsAllColumnPinned(isColumnsAvailable);
      if (isColumnsAvailable) {
        return {
          left: prevState.left,
          right: prevState.right.filter((item) => !columnsToAdd.includes(item)),
        };
      } else {
        return {
          left: [...prevState.left],
          right: [...columnsToAdd, ...prevState.right],
        };
      }
    });
  };

  const tabContent: any = {
    validationErrorsSummary: (
      <>
        {validationRows && validationRows?.length !== 0 ? (
          <ValidationSummaryDataGrid
            dataColumns={validationColumns}
            dataRows={validationRows}
            loading={isLoading}
            setLoading={setIsLoading}
            isExportButtonRequired={true}
            className="dataTable no-radius"
            rowCount={rowsData?.total}
            pageSizeOptions={[25]}
            historyType={"Validation"}
            disableColumnFilter={false}
            validationErrorProps={{
              dataRows: rowsData,
              dataColumns: columnsData,
              loading: isLoading,
              className:
                "dataTable no-radius datatable-column-sep checkbox-result-column remove-pinnedColumnHeaders-bg pinnedColumnHeaders-bg-height",
              gridColumnLength: gridColumnLength,
              dataListTitle: "",
              isHeight100: true,

              pinnedColumns,
              handleMoveColumns,
              isAllColumnPinned,

              disableColumnPinning: false,
              validationCheckboxSelection: true,
              selectedIds: selectedIds,
              setSelectedIds: setSelectedIds,
              validationPaginationMode: "server",
              validationRowCount: validationErrorsData?.total || 0,
              validationPageSizeOptions: [25],
              validationPaginationModel: {
                page: page - 1,
                pageSize: pSize,
              },
              validationOnPaginationModelChange: (params: any) => {
                if (params.pageSize !== pSize || params.page !== page - 1) {
                  setPage(params.page + 1);
                  setPSize(params.pageSize);
                }
              },
            }}
          />
        ) : (
          <Box
            sx={{
              paddingTop: "24px",
              paddingBottom: "24px",
              display: "flex",
              justifyContent: "center",
            }}
            className="adhock-query-accordion-wrapper"
          >
            No data available
          </Box>
        )}
      </>
    ),
    crossFieldValidations: (
      <Box className="adhock-query-accordion-wrapper">
        <AdhocQueryDashboardTab
          validateResult={validateResult}
          adhocQueryData={
            validateResult?.additional_properties
              ?.cross_field_validation_results || []
          }
        />
      </Box>
    ),
    duplicateRecords: (
      <Box className="adhock-query-accordion-wrapper">
        <DuplicateRecords
          validateResult={validateResult}
          userDialogData={userDialogData}
          setUserDialogData={setUserDialogData}
          allUsersList={allUsersList}
        />
      </Box>
    ),
    nullKeyErrors: (
      <Box className="adhock-query-accordion-wrapper">
        <NullKeyErrors
          validateResult={validateResult}
          userDialogData={userDialogData}
          setUserDialogData={setUserDialogData}
          allUsersList={allUsersList}
        />
      </Box>
    ),
  };
  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
    setSelectedColumnName("");
    setPage(defaultPage ? parseInt(defaultPage) : 1);
    if (newValue !== "validationErrorsSummary" && selectedIds.length > 0) {
      setSelectedIds([]);
    }
  };

  return (
    <>
      <Loader isLoading={isBackdropLoading} />
      <div className="validate-page">
        {/* Header */}
        <ValidationResultHeader validateResult={validateResult} />

        {/* Resource Statistics */}
        <ValidationResultResourceStatistics validateResult={validateResult} />

        {/* Incidents */}
        <Incidents
          currentResultId={currentResourceResultId || ""}
          currentIncidentData={currentIncidentData ?? []}
          setIsLoading={setIsLoading}
          setIsTriggereBtnPressed={setIsTriggereBtnPressed}
          setIsCommentBtnPressed={setIsCommentBtnPressed}
        />

        {/* Validation Parameters */}
        {validateResult?.complete_validation_params
          ?.validation_request_body && (
          <ValidationResultParameters validateResult={validateResult} />
        )}

        {/* Supporting Documents */}
        {validateResult?.additional_properties?.supporting_documents && (
          <div className="accordion-panel mt-8">
            <ResourceSupportDocuments
              title="Supporting Documents"
              documents={
                validateResult?.additional_properties?.supporting_documents
              }
            />
          </div>
        )}

        {/* Resource Variables */}
        {validateResult?.complete_validation_params?.validation_request_body
          ?.inline_variables && (
          <ValidationResultVariables
            resourceVariables={
              validateResult?.complete_validation_params
                ?.validation_request_body?.inline_variables
            }
          />
        )}
        <Grid container className="tabs-with-butttons">
          <Grid item xs>
            <Tabs
              sx={{
                mt: 1,
              }}
              value={activeTab}
              onChange={handleChangeTab}
              className="mui-tabs alternative-1"
              variant="scrollable"
            >
              <Tab
                label="Validation Errors Summary"
                value="validationErrorsSummary"
              />
              <Tab label="Adhoc Query" value="crossFieldValidations" />
              <Tab label="Duplicate Records" value="duplicateRecords" />
              <Tab label="Null Key Errors" value="nullKeyErrors" />
            </Tabs>
          </Grid>
          <Grid item className="buttons-group">
            <Button
              className="btn-blue btn-orange btn-sm"
              onClick={() => {
                setUserDialogData((prev: any) => ({
                  ...prev,
                  status: true,
                  dialogName: "Update Issue",
                }));
              }}
              disabled={userDialogData?.selectedIds?.length === 0}
            >
              Update Issue
            </Button>
          </Grid>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <Box>{tabContent[activeTab]}</Box>
        </Grid>
      </div>
      {activeTab === "validationErrorsSummary" && (
        <>
          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={dashboardUserDetailsData?.isBackDropVisible ?? false}
          ></Backdrop>
          <DashboardUserDetails
            dashboardUserDetailsData={dashboardUserDetailsData}
            setDashboardUserDetailsData={setDashboardUserDetailsData}
            allUsersList={allUsersList}
            setIsLoading={setIsLoading}
            setDetailViewDataRows={setRowsData}
            issueFrom={"validation"}
          />
          <IncidentUserandStatusDialog
            userDialogData={userDialogData}
            setUserDialogData={setUserDialogData}
            allUsersList={allUsersList}
            setDetailViewDataRows={setRowsData}
            issueFrom={"validation"}
            setSelectedIds={setSelectedIds}
          />
        </>
      )}
    </>
  );
};

export default SummarizesValidationResults;
