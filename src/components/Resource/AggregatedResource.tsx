// import { CheckCircleRounded } from "@mui/icons-material";
import {
  Autocomplete,
  Box,
  Checkbox,
  Grid,
  MenuItem,
  // TextareaAutosize,
  TextField,
  Tooltip,
} from "@mui/material";
import React, {
  Dispatch,
  SetStateAction,
  useEffect,
  useState,
  // useRef,
} from "react";
import { useLocation } from "react-router-dom";
import useFetchResourceById from "../../hooks/useFetchResourceById";
import { aggregatedValidateSchema } from "../../schemas";
import { useResourceContext } from "../../contexts/ResourceContext";
import GroupedAutocomplete from "../Molecules/Resource/GroupedAutocomplete";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import SqlQueryDialog from "../Dialogs/SqlQueryDialog";
import useFetchResources from "../../hooks/useFetchResources";
import LinkIcon from "@mui/icons-material/Link";
import useFetchAllResources from "../../hooks/useFetchAllResources";
import useFetchAllResourcesWithoutDomain from "../../hooks/useFetchAllResourcesWithoutDomain";
import { IconEyeBase } from "../../common/utils/icons";

interface AggregatedResourceProps {
  linkedFormData?: any;
  linkedResourceId?: string;
  // linkedAllResourcesData?: any;
  linkedSetResourceId?: Dispatch<SetStateAction<any>>;
  linkedSetFormData?: Dispatch<SetStateAction<any>>;
  linkedSetIsLoading?: Dispatch<SetStateAction<boolean>>;
  errors?: any;
  setErrors?: any;
  globalVariables?: any;
  setGlobalVariables?: any;
  allVariablesList?: any;
  setAllVariablesList?: any;
  tempGlobalVariables?: any;
  setTempGlobalVariables?: any;
  isShowRenderVariable: boolean;
  resourceColumnData: any;
}

const AggregatedResource: React.FC<AggregatedResourceProps> = ({
  linkedFormData,
  linkedResourceId,
  // linkedAllResourcesData,
  linkedSetResourceId,
  linkedSetFormData,
  linkedSetIsLoading,
  errors,
  setErrors,
  allVariablesList,
  setAllVariablesList,
  tempGlobalVariables,
  // setTempGlobalVariables,
  // isShowRenderVariable,
  resourceColumnData,
}) => {
  const {
    formData,
    setFormData,
    // setResourceId,
    resourceId,
    allResourcesData,
    setAllResourcesData,
    setIsLoading,
    baseResourceColumns,
    setBaseResourceColumns,
    currentDomainId,
  } = useResourceContext();
  const {
    globalVariables,
    setGlobalVariables,
    setQueryBuilderTempValue,
    queryBuilderTempValue,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    setShowAggregatedColumns,
    setAggregatedAvailColumns,
  } = useRuleResourceContext();

  const [allResources] = useFetchAllResourcesWithoutDomain({
    setIsLoading,
    aggregation_type: "aggregated",
  });

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources]);

  const formDataToUse = linkedFormData ? linkedFormData : formData;
  const setFormDataToUse = linkedSetFormData ? linkedSetFormData : setFormData;
  const allResourcesDataToUse = allResourcesData;
  const setIsLoadingToUse = linkedSetIsLoading
    ? linkedSetIsLoading
    : setIsLoading;
  const [baseResourceColumnData, setBaseResourceColumnData] = useState<any>({});
  const [resourceIdToUse, setResourceIdToUse] = useState<any>(
    linkedResourceId ? linkedResourceId : resourceId
  );
  const [resourceData, resourceColumnDataObj] = useFetchResourceById({
    currentResourceId: resourceIdToUse,
    setIsLoading: setIsLoadingToUse,
  });
  // const [renderVariable, setRenderVariable] = useState("");
  const [showQryModal, setShowQryModal] = useState<any>(false);

  useEffect(() => {
    if (resourceData) {
      setAllVariablesList((prev: any) => ({
        ...prev,
        baseResource: resourceData?.additional_properties?.inline_variables,
      }));

      const resourceVariables =
        resourceData?.additional_properties?.inline_variables || {};

      setGlobalVariables((prev: any) =>
        mergeVariablesWithoutOverride(prev, resourceVariables)
      );
    }
  }, [resourceData]);
  const mergeVariablesWithoutOverride = (
    globalVariables: Record<string, any>,
    resourceVariables: Record<string, any>
  ): Record<string, any> => {
    const updatedGlobalVariables = { ...globalVariables };

    Object.keys(resourceVariables).forEach((key) => {
      if (!updatedGlobalVariables.hasOwnProperty(key)) {
        updatedGlobalVariables[key] = resourceVariables[key];
      }
    });

    return updatedGlobalVariables;
  };

  useEffect(() => {
    if (resourceColumnDataObj) {
      const unique_columns =
        resourceColumnDataObj.resource_column_properties.unique_columns;
      const derivedColumnNames =
        resourceColumnDataObj.resource_column_properties.resource_columns
          .filter(
            (column: { constraints: { is_derived: any } }) =>
              column.constraints.is_derived
          )
          .filter((column: any) => column.is_active === true)
          .map((column: { column_name: any }) => column.column_name);
      const otherColumnNames =
        resourceColumnDataObj.resource_column_properties.resource_columns
          .filter(
            (column: { constraints: { is_derived: any } }) =>
              !column.constraints.is_derived
          )
          .filter((column: any) => column.is_active === true)
          .map((column: { column_name: any }) => column.column_name)
          .filter((columnName: any) => !unique_columns.includes(columnName));

      const resource_columns = {
        unique_columns: unique_columns,
        derived_column: derivedColumnNames,
        other_column: otherColumnNames,
      };

      setBaseResourceColumnData(resource_columns);
    }
  }, [resourceColumnDataObj]);

  const handelOnChangeResource = (e: any, value: any) => {
    if (value?.id !== undefined) {
      setResourceIdToUse(value?.id || null);
      setFormDataToUse({
        ...formDataToUse,
        base_resource_id: value?.id || null,
        base_resource_columns: null,
        base_resource_code: value?.code,
      });
    } else {
      setResourceIdToUse(null);
      setBaseResourceColumnData({});
      setFormDataToUse({
        ...formDataToUse,
        base_resource_columns: null,
        base_resource_id: null,
        base_resource_code: "",
      });
    }
    setBaseResourceColumns([]);
    const missingKeys = allVariablesList?.baseResource
      ? Object.keys(allVariablesList?.baseResource).filter((key) => {
          // Check if the key exists in resourceColumn or resource objects

          return (allVariablesList?.resource &&
            Object.keys(allVariablesList?.resource).length > 0) ||
            (allVariablesList?.resourceColumn &&
              Object.keys(allVariablesList?.resourceColumn).length > 0)
            ? !Object.keys(allVariablesList?.resource).includes(key) &&
                !Object.keys(allVariablesList?.resourceColumn).includes(key)
            : [];
        })
      : [];
    //Seems after variable changes the below code is not required

    // const aggrKeys = Object.keys(tempGlobalVariables);
    // const filteredMissingKeys = missingKeys.filter(
    //   (key) => !aggrKeys.includes(key)
    // );
    const filteredGlobalVariable = Object.fromEntries(
      Object.entries(globalVariables).filter(
        ([key]) => !missingKeys.includes(key)
      )
    );
    setGlobalVariables(filteredGlobalVariable);

    validateField("base_resource_id", value?.id);
  };

  const validateField = async (name: any, value: any) => {
    try {
      const partialFormData = { ...formDataToUse, [name]: value };
      await aggregatedValidateSchema.validateAt(name, partialFormData);
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const location = useLocation();
  const pathnames = location.pathname
    .split("/")
    .filter((path: any) => path !== "")
    .filter((path: any) => isNaN(path));

  const onSaveQuery = () => {
    setFormDataToUse({
      ...formDataToUse,
      aggregation_query: queryBuilderTempValue,
    });
    setAllVariablesList((prev: any) => ({
      ...prev,
      resource: {
        ...prev.resource,
        ...tempGlobalVariables,
      },
    }));

    validateField("aggregation_query", queryBuilderTempValue);
  };

  return (
    <>
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
        <Box className="d-flex align-items-end cols-gap-6">
          <Autocomplete
            fullWidth
            defaultValue={resourceIdToUse || ""}
            options={
              allResourcesDataToUse && allResourcesDataToUse.length > 0
                ? allResourcesDataToUse.filter(
                    (option: any) => option?.id !== formDataToUse?.id
                  )
                : []
            }
            getOptionLabel={(option) => option.resource_name ?? ""}
            value={
              allResourcesDataToUse?.find(
                (option: { id: any }) => option.id === resourceIdToUse
              ) || null
            }
            renderInput={(params) => (
              <TextField
                name="base_resource_id"
                style={{ color: "#000000" }}
                {...params}
                label={
                  <span>
                    Base Resource <span className="required-asterisk">*</span>
                  </span>
                }
                placeholder="Select..."
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.base_resource_id}
                helperText={errors?.base_resource_id || ""}
              />
            )}
            renderOption={(props, option, { index }) => (
              <MenuItem
                {...props}
                key={option.resource_name + index}
                value={option.resource_name}
                sx={{ justifyContent: "space-between" }}
              >
                {option.resource_name}
              </MenuItem>
            )}
            loadingText="Loading..."
            onChange={(event, value) => handelOnChangeResource(event, value)}
            className={`form-control-autocomplete ${
              errors?.base_resource_id ? "has-error" : ""
            }`}
          />
          {formDataToUse?.base_resource_id && (
            <Box
              className="custom-chip"
              onClick={() => {
                const resource = allResourcesDataToUse?.find(
                  (option: { id: any }) => option.id === resourceIdToUse
                );
                if (resource) {
                  window.open(
                    `/resource/${resource?.domain_id}/view/${resource?.id}`,
                    "_blank"
                  );
                }
              }}
            >
              <Tooltip title={"Click to view base resource"} placement="top">
                <IconEyeBase />
              </Tooltip>
            </Box>
          )}
        </Box>
      </Grid>
      <GroupedAutocomplete
        data={baseResourceColumnData}
        selectedColumns={formDataToUse.base_resource_columns}
        label={"Base Resource Columns"}
        placeholder={"Select..."}
        selectedOptions={baseResourceColumns}
        setSelectedOptions={setBaseResourceColumns}
        errors={errors}
        setErrors={setErrors}
        id={formDataToUse?.id}
      />
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
        <Box
          sx={{ display: { md: "block", sm: "none" } }}
          className="label-text"
        >
          &nbsp;
        </Box>
        <label className="base-resource-checkbox">
          <Checkbox
            checked={formDataToUse.base_resource_validation || false}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
              setFormDataToUse({
                ...formDataToUse,
                base_resource_validation: event.target.checked,
              });
            }}
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
          />
          <span>Enable Base Resource Validation</span>
        </label>
      </Grid>

      <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
        <label className="label-text">
          Aggregation Query <span className="required-asterisk">*</span>
        </label>

        <TextField
          type="text"
          value={formDataToUse?.aggregation_query}
          onClick={() => {
            setShowAggregatedColumns(true);
            setAggregatedAvailColumns((prev: any) => {
              return baseResourceColumns.map((item: any) => item.label);
            });
            setShowQryModal(true);
            if (formDataToUse?.aggregation_query) {
              setQueryBuilderTempValue(formDataToUse?.aggregation_query);
            }
            let resourceColumns: any = [];
            let baseResourceColumnsList: any = [];
            if (resourceColumnData && Object.keys(resourceColumnData)?.length) {
              resourceColumns =
                resourceColumnData?.resource_column_properties?.resource_columns
                  ?.map((columnItem: any) => {
                    if (columnItem?.is_active) {
                      return columnItem.column_name;
                    }
                  })
                  .filter((filterItem: any) => filterItem);
            }

            if (baseResourceColumns && baseResourceColumns.length > 0) {
              baseResourceColumnsList = new Set(
                baseResourceColumns.map(
                  (column: { label: any }) => column.label
                )
              );
            }
            // setAvailColumns((prev: any) => {
            //   const updatedColumns = new Set([
            //     ...prev,
            //     ...resourceColumns,
            //     ...baseResourceColumnsList,
            //   ]);
            //   return Array.from(updatedColumns);
            // });
          }}
          className={`form-control-autocomplete ${
            errors?.aggregation_query ? "has-error" : ""
          }`}
          InputProps={{
            readOnly: true,
          }}
          error={!!errors?.aggregation_query}
          helperText={errors?.aggregation_query}
        />
        {/* </Box> */}
        {/* {errors?.aggregation_query && (
          <div className="error-text">{errors?.aggregation_query}</div>
        )} */}
      </Grid>
      {/* {isShowRenderVariable && (
        <Grid item xs={12}>
          <Box className={`available-box `}>
            <AvailableVariables
              error={errors}
              setErrors={setErrors}
              handleVariableSelect={handleVariableSelect}
              isFlex={true}
            />
            <RenderVariables
              // sqlQuery={formData?.resource_definition?.sql_definition?.sql_query}
              error={errors}
              setErrors={setErrors}
              // setFormData={setFormData}
              // editorRef={editorRef}
              // variables={tempGlobalVariables}
              // setVariables={setTempGlobalVariables}
              // globalVariables={globalVariables}
              // setGlobalVariables={setGlobalVariables}
            />
          </Box>
        </Grid>
      )} */}
      <SqlQueryDialog
        showQryModal={showQryModal}
        setShowQryModal={setShowQryModal}
        onSaveQuery={onSaveQuery}
      />
    </>
  );
};

export default AggregatedResource;
