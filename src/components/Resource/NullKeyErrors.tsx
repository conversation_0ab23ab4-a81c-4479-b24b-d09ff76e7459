import React, { useEffect, useState } from "react";
import useFetchPaginatedValidationDuplicateErrors from "../../hooks/useFetchPaginatedValidationDuplicateErrors";
import RulesListDataTable from "../DataGrids/RulesListDataGrid";
import useFetchPaginatedNullKeysValidations from "../../hooks/useFetchPaginatedNullKeysValidations";
import { Backdrop, Box, Button, IconButton, Tooltip } from "@mui/material";
import useFetchUsersList from "../../hooks/useFetchUsersList";
import {
  IconIncidentDetails,
  IconIncidentResources,
  IconStatusFail,
  IconStatusInprogress,
  IconStatusNew,
  IconStatusPass,
} from "../../common/utils/icons";
import { GRID_CHECKBOX_SELECTION_COL_DEF } from "@mui/x-data-grid-pro";
import DashboardUserDetails from "../Molecules/Rule/DashboardUserDetails";
import IncidentUserandStatusDialog from "../Molecules/Rule/IncidentUserandStatusDialog";
import StatusCell from "../../common/utils/statusIcon";
import {
  CommentColumn,
  CurrentStatusColumn,
  UserColumn,
} from "../../common/utils/commonActionsFields";

interface INullKeyErrors {
  validateResult: any;
  userDialogData: any;
  setUserDialogData: React.Dispatch<React.SetStateAction<any>>;
  allUsersList: any;
}

const NullKeyErrors = ({
  validateResult,
  userDialogData,
  setUserDialogData,
  allUsersList,
}: INullKeyErrors) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [columnsData, setColumnsData] = useState<any[]>([]);
  const [rowsData, setRowsData] = useState<any[]>([]);
  const [dashboardUserDetailsData, setDashboardUserDetailsData] = useState({
    status: false,
    assigned_user: "",
    issueId: null,
    isBackDropVisible: false,
  });
  const [selectedIds, setSelectedIds] = useState<any>([]);
  const [resultColumns, setResultColumns] = useState<any>([]);

  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  const [nullKeysValidation] = useFetchPaginatedNullKeysValidations({
    currentValidationResultId: validateResult?.id,
    setIsLoading,
    page,
    pSize,
    isDetailedExecutionDataAvailable:
      validateResult?.is_detailed_execution_data_available,
  });

  useEffect(() => {
    setUserDialogData((prev: any) => ({
      ...prev,
      selectedIds: selectedIds || [],
    }));
  }, [selectedIds]);

  useEffect(() => {
    if (
      nullKeysValidation?.items?.length > 0 &&
      nullKeysValidation?.items[0]?.null_key_error_record_details
    ) {
      const firstItem =
        nullKeysValidation?.items[0]?.null_key_error_record_details;
      const resourceKeys = firstItem?.resource_keys;
      const recordData = firstItem?.record_snapshot?.resource_data;
      let columns: any = [];
      const resource_keys_columns: any = resourceKeys
        ? Object.keys(resourceKeys).map((key) => ({
            field: key,
            headerName: key,
            flex: 1,
            minWidth: 120,
          }))
        : [];
      const fileNameColumn: any = [
        {
          field: "file_name",
          headerName: "File_Name(S)",
          flex: 1,
          minWidth: 240,
          renderCell: (params: any) => (
            <div
              className="data-grid-cell word-break-all"
              dangerouslySetInnerHTML={{ __html: params.value }}
            />
          ),
        },
      ];
      const errorMessageColumn: any = [
        {
          field: "error_message",
          headerName: "Error_Message",
          flex: 1,
          minWidth: 240,
        },
      ];
      const issueColumns: any = [
        CurrentStatusColumn({}),
        UserColumn({}),
        CommentColumn({}),
        {
          field: "action",
          headerName: "Action",
          align: "center",
          headerAlign: "center",
          minWidth: "130",
          pinned: "right",
          renderCell: (params: any, index: any) => {
            return (
              <>
                <Box sx={{ display: "inline-flex", columnGap: "12px" }}>
                  <Tooltip
                    title={`Incident Details : ${params?.row?.id}`}
                    placement="top"
                    arrow
                  >
                    <Button
                      className="btn-blue btn-orange btn-sm"
                      onClick={(event) => {
                        event.stopPropagation();
                        setDashboardUserDetailsData((prev: any) => {
                          return {
                            ...prev,
                            status: true,
                            incidentId: params.row.rowId,
                            assigned_user: params.row.assigned_user,
                            panelType: "incidentDetail",
                            isBackDropVisible: true,
                            comment: [
                              {
                                comment: params.row.comment,
                                create_date: params.row.create_date,
                                assigned_user: params.row?.assigned_user || "",
                              },
                            ],
                            current_status: params.row.current_status,
                            issueId: params.row.rowId,
                          };
                        });
                      }}
                    >
                      <IconIncidentDetails />
                    </Button>
                  </Tooltip>
                </Box>
              </>
            );
          },
        },
      ];
      columns = [
        ...resource_keys_columns,
        ...fileNameColumn,
        ...errorMessageColumn,
        ...issueColumns,
      ];
      if (recordData) {
        const snapshot_columns = recordData
          ? Object.keys(recordData)
              .filter(
                (key) =>
                  !resource_keys_columns.some(
                    (column: { field: string }) => column.field === key
                  ) &&
                  !fileNameColumn.some(
                    (column: { field: string }) => column.field === key
                  ) &&
                  key !== "Error_Message"
              )
              .map((key) => ({
                field: key,
                headerName: key,
                flex: 1,
                minWidth: 120,
              }))
          : [];

        columns = [...columns, ...snapshot_columns];
      }

      const rows = nullKeysValidation.items.map((item: any, index: any) => {
        const fileNamesArray = item?.null_key_error_record_details?.file_name
          ?.replace(/'/g, "")
          .split(";");
        const formattedFileNames =
          fileNamesArray && fileNamesArray.length > 1
            ? `<ul class="ul-inside-grid">${fileNamesArray
                .map((name: any) => `<li>${name}</li>`)
                .join("")}</ul>`
            : `<div class="word-break-all">${
                (fileNamesArray && fileNamesArray[0]) || ""
              }</div>`;

        return {
          id: item?.id,
          rowId: item?.id,
          ...item,
          error_message: item?.null_key_error_record_details?.error_message,
          ...item?.null_key_error_record_details?.resource_keys,
          file_name: formattedFileNames,
          ...item?.null_key_error_record_details?.record_snapshot
            ?.resource_data,
        };
      });
      setColumnsData(columns);
      setRowsData(rows);
    } else {
      setColumnsData([]);
      setRowsData([]);
    }
  }, [nullKeysValidation]);
  const [isAllColumnPinned, setIsAllColumnPinned] = useState(false);
  const [pinnedColumns, setPinnedColumns] = useState({
    left: [GRID_CHECKBOX_SELECTION_COL_DEF.field],
    right: ["current_status", "username", "comment", "action"],
  });
  const handleMoveColumns = () => {
    setPinnedColumns((prevState) => {
      const columnsToAdd = ["current_status", "username", "comment"];
      const isColumnsAvailable = columnsToAdd.every((item) =>
        prevState.right.includes(item)
      );

      setIsAllColumnPinned(isColumnsAvailable);
      if (isColumnsAvailable) {
        return {
          left: prevState.left,
          right: prevState.right.filter((item) => !columnsToAdd.includes(item)),
        };
      } else {
        return {
          left: [...prevState.left],
          right: [...columnsToAdd, ...prevState.right],
        };
      }
    });
  };
  return (
    <>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={dashboardUserDetailsData?.isBackDropVisible ?? false}
      ></Backdrop>
      <Box sx={{ padding: "16px" }}>
        <Box className="position-relative">
          {rowsData.length > 0 && (
            <IconButton
              onClick={handleMoveColumns}
              className={`icon-pin-datagrid navbar-icon burger  ${
                isAllColumnPinned ? "active" : ""
              }`}
            >
              <div className="bar bar1"></div>
              <div className="bar bar2"></div>
              <div className="bar bar3"></div>
            </IconButton>
          )}
          <RulesListDataTable
            selectedIds={selectedIds}
            setSelectedIds={setSelectedIds}
            setResultColumns={setResultColumns}
            dataRows={rowsData || []}
            dataColumns={columnsData || []}
            loading={isLoading}
            dataListTitle="Rules Dashboard"
            className="dataTable no-radius pt-0 bdr-top-0 datatable-column-sep checkbox-result-column remove-pinnedColumnHeaders-bg pinnedColumnHeaders-bg-height"
            disableColumnFilter={true}
            tableHeight={180}
            disableColumnReorder={true}
            paginationMode="server"
            rowCount={nullKeysValidation?.total || 0}
            pageSizeOptions={[25]}
            paginationModel={{
              page: page - 1,
              pageSize: pSize,
            }}
            onPaginationModelChange={(params: any) => {
              if (params.pageSize !== pSize || params.page !== page - 1) {
                setPage(params.page + 1);
                setPSize(params.pageSize);
              }
            }}
            singlePageMaxHeightDiff={300}
            pinnedColumns={rowsData?.length > 0 ? pinnedColumns : {}}
            disableColumnPinning={false}
            checkboxSelection={true}
          />
        </Box>
        <DashboardUserDetails
          dashboardUserDetailsData={dashboardUserDetailsData}
          setDashboardUserDetailsData={setDashboardUserDetailsData}
          allUsersList={allUsersList}
          setIsLoading={setIsLoading}
          setDetailViewDataRows={setRowsData}
          issueFrom={"validation"}
        />
        <IncidentUserandStatusDialog
          userDialogData={userDialogData}
          setUserDialogData={setUserDialogData}
          allUsersList={allUsersList}
          setDetailViewDataRows={setRowsData}
          issueFrom={"validation"}
          setSelectedIds={setSelectedIds}
        />
      </Box>
    </>
  );
};

export default NullKeyErrors;
