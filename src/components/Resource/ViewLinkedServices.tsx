import React, { <PERSON><PERSON><PERSON>, SetStateAction, useEffect, useState } from "react";
import {
  Tooltip,
  Typography,
  Grid,
  Box,
  TextareaAutosize,
} from "@mui/material";
import { formattedJson } from "../../services/utils";
import InfoIcon from "@mui/icons-material/Info";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";

interface ViewLinkedServicesProps {
  resourceData: any;
  linkedServicesData: any;
  connectionKeysData: any;
  fileProcessingData: any;
}

const ViewLinkedServices = ({
  resourceData,
  linkedServicesData,
  connectionKeysData,
  fileProcessingData,
}: ViewLinkedServicesProps) => {
  const { allResourcesData } = useRuleResourceContext();
  const [storageType, setStorageType] = useState("");
  const [resourceType, setResourceType] = useState("");
  useEffect(() => {
    if (resourceData && linkedServicesData) {
      setResourceType(resourceData?.aggregation_type);
      if (resourceData?.linked_service_id) {
        let type;
        type = linkedServicesData?.find(
          (option: { id: any }) => option.id === resourceData?.linked_service_id
        )?.sub_type;
        setStorageType(type);
      }
    }
  }, [resourceData, linkedServicesData]);

  const handleChipClick = (domainId: number, option: any) => {
    window.open(
      `/resource/${domainId}/view/${option?.base_resource_id}`,
      "_blank"
    );
  };
  return (
    <>
      {resourceType === "flat" && (
        <>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">
              <span className="position-relative">
                Linked Service
                {resourceData?.linked_service_id && (
                  <Tooltip
                    componentsProps={{
                      tooltip: { className: "wide-tooltip w-250" },
                    }}
                    title={
                      resourceData?.linked_service_id && (
                        <React.Fragment>
                          <Typography color="inherit">
                            Linked Service Code :{" "}
                            {
                              linkedServicesData?.find(
                                (item: any) =>
                                  item?.id === resourceData?.linked_service_id
                              )?.code
                            }
                          </Typography>
                        </React.Fragment>
                      )
                    }
                  >
                    <InfoIcon
                      sx={{
                        position: "absolute",
                        top: "50%",
                        transform: "translateY(-50%)",
                        right: "-24px",
                        width: "16px",
                      }}
                    />
                  </Tooltip>
                )}
              </span>
            </label>
            <div className="form-control">
              {linkedServicesData?.find(
                (item: any) => item?.id === resourceData?.linked_service_id
              )?.name || "N/A"}
            </div>
          </Grid>
          {["sql", "mariadb", "azure_sql"].includes(storageType) && (
            <>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Connection Key</label>
                <div className="form-control">
                  {connectionKeysData?.find(
                    (key: any) =>
                      key?.id ===
                      resourceData?.additional_properties?.resource_definition
                        ?.connection_key
                  )?.name || "N/A"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">SQL Query</label>
                <div className="form-control break-word">
                  {resourceData?.additional_properties?.resource_definition
                    ?.sql_query || "N/A"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">
                  Use Multi Thread Reader
                </label>
                <div className="form-control">
                  {resourceData?.additional_properties?.resource_definition
                    ?.use_multi_thread_reader
                    ? "Yes"
                    : "No"}
                </div>
              </Grid>

              {resourceData?.additional_properties?.resource_definition
                ?.use_multi_thread_reader && (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <label className="label-text text-bold">
                    Column Name to Partition On
                  </label>
                  <div className="form-control break-word">
                    {resourceData?.additional_properties?.resource_definition
                      .column_name_to_partition_on_sql_query || "N/A"}
                  </div>
                </Grid>
              )}
            </>
          )}
          {["blob", "sftp", "local"].includes(storageType) && (
            <>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Connection Key</label>
                <div className="form-control">
                  {connectionKeysData?.find(
                    (key: any) =>
                      key?.id ===
                      resourceData?.additional_properties?.resource_definition
                        ?.connection_key
                  )?.name || "N/A"}
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">
                  <span className="position-relative">
                    File Processing Attribute
                    {resourceData?.file_processing_id && (
                      <Tooltip
                        componentsProps={{
                          tooltip: { className: "wide-tooltip w-380" },
                        }}
                        title={
                          <pre
                            style={{
                              whiteSpace: "pre-wrap",
                              margin: 0,
                              maxHeight: "200px",
                              overflowY: "auto",
                            }}
                          >
                            <React.Fragment>
                              <Typography color="inherit">
                                File Processing Detail
                              </Typography>
                              <Typography>
                                {formattedJson(
                                  JSON.stringify(
                                    fileProcessingData?.find(
                                      (file: any) =>
                                        file?.id ===
                                        resourceData?.file_processing_id
                                    )
                                  )
                                )}
                              </Typography>
                            </React.Fragment>
                          </pre>
                        }
                      >
                        <InfoIcon
                          sx={{
                            position: "absolute",
                            top: "50%",
                            transform: "translateY(-50%)",
                            right: "-24px",
                            width: "16px",
                          }}
                        />
                      </Tooltip>
                    )}
                  </span>
                </label>
                <div className="form-control">
                  {fileProcessingData?.find(
                    (file: any) => file?.id === resourceData?.file_processing_id
                  )?.resource_type || "N/A"}
                </div>
              </Grid>
              {storageType === "sftp" ? (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <label className="label-text text-bold">
                    Remote Directory
                  </label>
                  <div className="form-control break-word">
                    {resourceData?.additional_properties?.resource_definition
                      ?.remote_directory || "N/A"}
                  </div>
                </Grid>
              ) : (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <label className="label-text text-bold">Resource Path</label>
                  <div className="form-control break-word">
                    {resourceData?.additional_properties?.resource_definition
                      ?.resource_path || "N/A"}
                  </div>
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Column Delimiter</label>
                <div className="form-control">
                  {resourceData?.additional_properties?.resource_definition
                    ?.column_delimiter || "N/A"}
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">File Name</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.additional_properties?.resource_definition
                    ?.file_name || "N/A"}
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Excel Sheet Name</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.additional_properties?.resource_definition
                    ?.excel_sheet_name || "N/A"}
                </div>
              </Grid>
              {storageType === "blob" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">
                      Container Name
                    </label>
                    <div className="form-control">
                      {resourceData?.additional_properties?.resource_definition
                        ?.container_name || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
            </>
          )}
          {linkedServicesData?.find(
            (option: { id: any }) =>
              option.id === resourceData?.linked_service_id
          )?.type === "api" && (
            <>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Connection Key</label>
                <div className="form-control">
                  {connectionKeysData?.find(
                    (key: any) =>
                      key?.id ===
                      resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.connection_key
                  )?.name || "N/A"}
                </div>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Method</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.additional_properties?.resource_definition
                    ?.api_definition?.method || "get"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Content Type</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.additional_properties?.resource_definition
                    ?.api_definition?.content_type || "application/json"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Url</label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.additional_properties?.resource_definition
                    ?.api_definition?.url || "N/A"}
                </div>
              </Grid>

              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">
                  Request Timeout(in seconds)
                </label>
                <div className="form-control word-wrap-break-word">
                  {resourceData?.additional_properties?.resource_definition
                    ?.api_definition?.request_timeout || 0}
                </div>
              </Grid>

              {storageType === "basic_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">Username</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.user_name || "N/A"}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">Password</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.password || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
              {storageType === "token_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">Bearer token</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.bearer_token || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
              {storageType === "key_auth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">API key</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.api_key || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
              {storageType === "oauth_api" && (
                <>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">
                      Oauth Client Id
                    </label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.oauth_client_id || "N/A"}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">
                      Oauth Client Secret
                    </label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.oauth_client_secret || "N/A"}
                    </div>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                    <label className="label-text text-bold">Oauth Url</label>
                    <div className="form-control word-wrap-break-word">
                      {resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.oauth_url || "N/A"}
                    </div>
                  </Grid>
                </>
              )}
            </>
          )}
          {linkedServicesData?.find(
            (option: { id: any }) =>
              option.id === resourceData?.linked_service_id
          )?.type === "api" && (
            <>
              {resourceData?.additional_properties?.resource_definition
                ?.api_definition?.method !== "get" && (
                <Grid item xs={12} sm={6} md={12} lg={12} xl={12}>
                  <label className="label-text text-bold">Request Body</label>
                  <div className="form-control word-wrap-break-word">
                    <TextareaAutosize
                      title="Request Body"
                      disabled={true}
                      name="body"
                      minRows={3}
                      maxRows={6}
                      className={`form-control-1`}
                      value={formattedJson(
                        resourceData?.additional_properties?.resource_definition
                          ?.api_definition?.body
                      )}
                    />
                  </div>
                </Grid>
              )}
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Query params</label>
                <Box className="params-box">
                  <div className="avail-columns-group">
                    {resourceData?.additional_properties?.resource_definition
                      ?.api_definition?.query_params &&
                    Object.keys(
                      resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.query_params
                    ).length > 0
                      ? Object.keys(
                          resourceData?.additional_properties
                            ?.resource_definition?.api_definition?.query_params
                        ).map((key, index) => (
                          <Box key={index} className="avail-columns">
                            <button
                              type="button"
                              style={{
                                display: `${key === "" ? "none" : "block"}`,
                              }}
                            >
                              {key} :{" "}
                              {
                                resourceData?.additional_properties
                                  ?.resource_definition?.api_definition
                                  ?.query_params[key]
                              }
                            </button>
                          </Box>
                        ))
                      : "N/A"}
                  </div>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Url params</label>
                <Box className="params-box">
                  <div className="avail-columns-group">
                    {resourceData?.additional_properties?.resource_definition
                      ?.api_definition?.url_params &&
                    Object.keys(
                      resourceData?.additional_properties?.resource_definition
                        ?.api_definition?.url_params
                    ).length > 0
                      ? Object.keys(
                          resourceData?.additional_properties
                            ?.resource_definition?.api_definition?.url_params
                        ).map((key, index) => (
                          <Box key={index} className="avail-columns">
                            <button
                              type="button"
                              style={{
                                display: `${key === "" ? "none" : "block"}`,
                              }}
                            >
                              {key} :{" "}
                              {
                                resourceData?.additional_properties
                                  ?.resource_definition?.api_definition
                                  ?.url_params[key]
                              }
                            </button>
                          </Box>
                        ))
                      : "N/A"}
                  </div>
                </Box>
              </Grid>
            </>
          )}
        </>
      )}
      {resourceType === "aggregated" && (
        <>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">Base Resource</label>
            <div
              onClick={() =>
                handleChipClick(
                  resourceData?.domain_id,
                  resourceData?.additional_properties?.aggregation_properties
                )
              }
              className="form-control btn-new-window"
            >
              {allResourcesData?.find(
                (item: any) =>
                  item?.id ===
                  resourceData?.additional_properties?.aggregation_properties
                    ?.base_resource_id
              )?.resource_name || "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">
              Base Resource Columns
            </label>
            <div className="form-control">
              {Array.isArray(
                resourceData?.additional_properties?.aggregation_properties
                  ?.base_resource_columns
              )
                ? resourceData.additional_properties.aggregation_properties.base_resource_columns.join(
                    ", "
                  )
                : "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">
              Base Resource Validation
            </label>
            <div className="form-control">
              {resourceData?.additional_properties?.aggregation_properties
                ?.base_resource_validation
                ? "True"
                : "False"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">Aggregation Query</label>
            <div className="form-control word-wrap-break-word">
              {resourceData?.additional_properties?.aggregation_properties
                ?.aggregation_query || "N/A"}
            </div>
          </Grid>
        </>
      )}
    </>
  );
};

export default ViewLinkedServices;
