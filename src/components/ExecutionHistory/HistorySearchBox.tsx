import {
  Box,
  Button,
  Grid,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from "@mui/material";
import { DatePicker, pickersLayoutClasses } from "@mui/x-date-pickers";
import dayjs, { Dayjs } from "dayjs";
import { rulesSearchType } from "../../services/constants";
import { useState } from "react";
import Loader from "../Molecules/Loader/Loader";
import {
  //executionHistorySearchDateSchema,
  executionHistorySearchSchema,
} from "../../schemas";
import InfoIcon from "@mui/icons-material/Info";

const HistorySearchBox = ({
  handleExecutionList,
  executionList,
  searchData,
  setSearchData,
  setSearchSubmitData,
  searchType,
}: any) => {
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [ruleSearchDate, setRuleSearchDate] = useState<Dayjs | null>(
    dayjs().subtract(0, "day")
  );

  const handleSearchChange = (e: any) => {
    const { name, value } = e.target;
    setSearchData({
      ...searchData,
      [name]: value,
    });
    validateField(name, value);
  };

  const handleResetSearch = () => {
    setIsBackdropLoading(true);
    setTimeout(() => {
      handleExecutionList(executionList);
      setSearchData({
        search_by: "domain_id",
        search_query: "",
        search_date: dayjs().format("YYYY-MM-DD"),
      });
      setSearchSubmitData({
        search_by: "",
        search_query: "",
        search_date: "",
      });

      setRuleSearchDate(dayjs().subtract(1, "day"));
      setIsBackdropLoading(false);
    }, 100);
  };

  const handleChangeDateSearch = (value: Dayjs | null) => {
    if (value) {
      const formattedDate = value.format("YYYY-MM-DD");
      setRuleSearchDate(value);
      setSearchData((prevFormData: any) => ({
        ...prevFormData,
        search_date: formattedDate,
      }));
    }
  };

  const handleSubmitSearch = async () => {
    setIsBackdropLoading(true);
    try {
      if (searchData.search_by !== "from_date") {
        await executionHistorySearchSchema.validate(searchData, {
          abortEarly: false,
        });
      }

      setSearchSubmitData(searchData);
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors(newErrors);
    } finally {
      setIsBackdropLoading(false);
    }
  };

  const validateField = async (name: string, value: any) => {
    try {
      const partialReferenceData = { ...searchData, [name]: value };
      await executionHistorySearchSchema.validateAt(name, partialReferenceData);
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  return (
    <>
      {isBackdropLoading && <Loader isLoading={isBackdropLoading} />}
      <Box className="text-box-card">
        <Grid container rowSpacing={2.5} columnSpacing={2.5}>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <label className="label-text">Search by</label>
            <Select
              MenuProps={{
                disableScrollLock: true,
              }}
              title="Aggregation type"
              value={searchData.search_by}
              name="search_by"
              style={{ width: "100%", height: 35 }}
              onChange={handleSearchChange}
              className={`form-control-autocomplete form-control-autocomplete-1`}
              required={true}
              error={!!errors.search_by}
            >
              {searchType.map((type: string) => (
                <MenuItem key={type} value={type}>
                  <span style={{ textTransform: "capitalize" }}>
                    {type.replace("_", " ")}
                  </span>
                </MenuItem>
              ))}
            </Select>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <label className="label-text">
              <span className="position-relative inline-block">
                Search value <span className="required-asterisk">*</span>
                {searchData?.search_by === "from_date" && (
                  <Tooltip
                    title="Search Results are from selected date to till today"
                    placement="top"
                    arrow
                  >
                    <span className="upload-icon-info">
                      <InfoIcon sx={{ fontSize: 16 }} />
                    </span>
                  </Tooltip>
                )}
              </span>
            </label>
            {searchData?.search_by === "from_date" ? (
              <>
                <Box className="datepicker-container-extra-icon">
                  <DatePicker
                    value={ruleSearchDate}
                    onChange={handleChangeDateSearch}
                    minDate={dayjs().subtract(6, "month")}
                    maxDate={dayjs()}
                    format="YYYY-MM-DD"
                    className="form-control-autocomplete date-picker"
                    slotProps={{
                      actionBar: { actions: ["today"] },
                      layout: {
                        sx: {
                          [`.${pickersLayoutClasses.actionBar}`]: {
                            gridColumn: 2,
                            color: "#000000",
                          },
                        },
                      },
                    }}
                  />
                </Box>
              </>
            ) : (
              <>
                {searchData?.search_by === "domain_id" ||
                searchData?.search_by === "resource_id" ||
                searchData?.search_by === "run_id" ||
                searchData?.search_by === "rule_id" ? (
                  <TextField
                    type="text"
                    name="search_query"
                    value={searchData?.search_query}
                    onChange={handleSearchChange}
                    className={`form-control ${
                      errors?.search_query ? "has-error" : ""
                    }`}
                    required={true}
                    error={!!errors.search_query}
                    helperText={errors.search_query || ""}
                    inputProps={{
                      pattern: "[0-9]*",
                      inputMode: "numeric", // Helps mobile users with numeric keyboards
                      onKeyDown: (e) => {
                        // Allow only numeric keys
                        if (
                          !/[\d]/.test(e.key) &&
                          ![
                            "Backspace",
                            "Tab",
                            "ArrowLeft",
                            "ArrowRight",
                          ].includes(e.key)
                        ) {
                          e.preventDefault();
                        }
                      },
                      onPaste: (e) => {
                        const pastedText = e.clipboardData.getData("text");
                        if (!/^\d*$/.test(pastedText)) {
                          e.preventDefault();
                        }
                      },
                      onDrop: (e) => {
                        e.preventDefault();
                      },
                    }}
                  />
                ) : (
                  <TextField
                    type="text"
                    name="search_query"
                    value={searchData?.search_query}
                    onChange={handleSearchChange}
                    className={`form-control ${
                      errors?.search_query ? "has-error" : ""
                    }`}
                    required={true}
                    error={!!errors.search_query}
                    helperText={errors.search_query || ""}
                  />
                )}
              </>
            )}
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <label className="label-text">&nbsp;</label>
            <Box
              sx={{
                display: "flex",
                columnGap: "16px",
                justifyContent: "flex-end",
              }}
            >
              <Button
                variant="contained"
                color="secondary"
                onClick={() => {
                  handleSubmitSearch();
                }}
                className="btn-orange"
              >
                Search
              </Button>

              <Button
                variant="contained"
                color="secondary"
                onClick={() => {
                  handleResetSearch();
                }}
                className="btn-orange btn-border"
              >
                Clear Search
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default HistorySearchBox;
