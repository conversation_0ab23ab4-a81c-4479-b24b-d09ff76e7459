import React from 'react';
import { QueryBuilderDnD } from '@react-querybuilder/dnd';
import { QueryBuilder } from "react-querybuilder";
import ValueEditorCustom from '../ValueEditorCustom';
import 'react-querybuilder/dist/query-builder.scss'; 
import '../../../styles/styles.scss';

interface QueryBuilderComponentProps {
  fields: any;
  query: any;
  handleQueryChange: (query: any) => void;
  defaultValidator: any;
  newOperators: any;
}

const QueryBuilderComponent: React.FC<QueryBuilderComponentProps> = ({ fields, query, handleQueryChange, defaultValidator, newOperators }) => {
  return (
    <QueryBuilderDnD>
      <QueryBuilder
     
        addRuleToNewGroups
        fields={fields}
        query={query}
        onQueryChange={handleQueryChange}
        debugMode
        listsAsArrays
        resetOnOperatorChange
        showCloneButtons
        showCombinatorsBetweenRules
        showLockButtons
        showNotToggle
        validator={defaultValidator}
        controlClassnames={{ queryBuilder: 'queryBuilder-branches' }}
        operators={newOperators}
        controlElements={{
          valueEditor: (props: any) => <ValueEditorCustom {...props} />,
        }}
      />
    </QueryBuilderDnD>
  );
};

export default QueryBuilderComponent;
