import React, { useState } from 'react';
import { Parser } from 'node-sql-parser';

const validateSQL = (query: string): boolean => {
  try {
    const parser = new Parser();
    parser.astify(query);
    return true;
  } catch (error) {
    return false;
  }
};

const SQLValidator: React.FC = () => {
  const [query, setQuery] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(true);

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const inputQuery = event.target.value;
    setQuery(inputQuery);

    // Perform validation
    const isValidQuery = validateSQL(inputQuery);
    setIsValid(isValidQuery);
  };

  return (
    <div>
      <textarea value={query} onChange={handleInputChange} />
      {isValid ? <p>SQL query is valid.</p> : <p>SQL query is invalid.</p>}
    </div>
  );
};

export default SQLValidator;
