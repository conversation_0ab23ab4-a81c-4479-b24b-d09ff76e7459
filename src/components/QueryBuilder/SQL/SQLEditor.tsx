import React, { useState, useRef, useEffect } from "react";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-sql";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-noconflict/ext-language_tools";
import { Box, Button, Grid } from "@mui/material";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import AvailableVariables from "../../Molecules/Variables/AvailableVariables";
import ResourceColumnsChips from "../../Organisms/ResourceColumnsChips";

interface SQLEditorProps {
  error?: any;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  isQueryColumns?: any;
  availableVariableMarginGap?: number;
  mergeQuerycolumns?: any;
  isFinalQuery?: any;
  dialogType?: string;
}

const SQLEditor: React.FC<SQLEditorProps> = ({
  error,
  setErrors,
  isQueryColumns,
  availableVariableMarginGap,
  mergeQuerycolumns,
  isFinalQuery,
  dialogType,
}) => {
  const {
    availColumns,
    availColumnsWithResourceDetail,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    editorRef,
    showAggregatedColumns,
    aggregatedAvailColumns,
  } = useRuleResourceContext();

  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);

  const handleSqlChange = (newSqlQuery: string) => {
    setQueryBuilderTempValue(newSqlQuery);
    if (newSqlQuery) {
      setErrors((prevError) => ({
        ...prevError,
        query: "",
      }));
    } else {
      setErrors((prevError) => ({
        ...prevError,
        query: "Please enter query",
      }));
    }
  };

  const handleColumnSelect = (column: string) => {
    const editor = editorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      `[${column}]` +
      " " +
      currentLine.slice(currentColumn);

    setQueryBuilderTempValue(updatedLine);
    setSelectedColumns([...selectedColumns, column]);
    editor.session.insert(currentPosition, `[${column}]`);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + column.length + 2,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  const handleColumnSelectWithPrefix = (
    column: string,
    resourceCode: string
  ) => {
    const editor = editorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      column +
      " " +
      currentLine.slice(currentColumn);

    setQueryBuilderTempValue(updatedLine);
    setSelectedColumns([...selectedColumns, `[${resourceCode}.${column}]`]);
    editor.session.insert(currentPosition, `[${resourceCode}.${column}]`);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + column.length + 1,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  const handleVariableSelect = (variable: string) => {
    const editor = editorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      `$$${variable}$$` +
      " " +
      currentLine.slice(currentColumn);

    setQueryBuilderTempValue(updatedLine);
    editor.session.insert(currentPosition, `$$${variable}$$ `);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + variable.length + 4,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  const editorOptions = {
    enableLiveAutocompletion: true,
    enableBasicAutocompletion: true,
    enableSnippets: false,
    showLineNumbers: true,
    tabSize: 2,
    fontSize: 20,
  };
  return (
    <>
      <Grid container columnSpacing={1.5} rowSpacing={2.5}>
        <Grid item md={12}>
          <Box className="label-text">
            SQL query
            <br />
          </Box>
          <Box>
            <AceEditor
              mode="sql"
              theme="github"
              onChange={handleSqlChange}
              value={queryBuilderTempValue || ""}
              width="100%"
              height="100px"
              editorProps={{ $blockScrolling: true }}
              setOptions={editorOptions}
              ref={editorRef}
              showGutter
              aria-label="editor"
              name="editor"
              fontSize={16}
              minLines={10}
              maxLines={10}
              showPrintMargin={false}
              className={`sql-editor mb-0 mt-0 ${
                error?.query ? "has-error" : ""
              }`}
            />
            <span className="validation-error ml-0">
              {error?.query && <span className="ml-14">{error?.query}</span>}
            </span>
          </Box>
        </Grid>

        <Grid item md={isQueryColumns ? 12 : 6}>
          <Box
            sx={{
              marginBottom: availableVariableMarginGap
                ? `${availableVariableMarginGap}px`
                : "16px",
            }}
          >
            <Box className={`available-box `}>
              <AvailableVariables
                error={error}
                setErrors={setErrors}
                handleVariableSelect={handleVariableSelect}
              />
            </Box>
          </Box>
        </Grid>
        {showAggregatedColumns ? (
          // If `showAggregatedColumns` is true
          <Grid item md={isQueryColumns ? 12 : 6}>
            <Box sx={{ marginBottom: 3 }} className="available-box box-alt avc">
              <h3 className="mb-0 mb-8">Available Columns :</h3>
              <div className="avail-columns-group">
                {aggregatedAvailColumns.map((column: any, index: any) => (
                  <button
                    className="avail-columns"
                    key={index}
                    onClick={() => handleColumnSelect(column)}
                  >
                    {column}
                  </button>
                ))}
              </div>
            </Box>
          </Grid>
        ) : availColumnsWithResourceDetail &&
          availColumnsWithResourceDetail?.data?.length > 0 ? (
          // If `availColumnsWithResourceDetail` exists and has data
          availColumnsWithResourceDetail?.queryType === "ADHOCQUERY" ? (
            // If `queryType` is "ADHOCQUERY"
            availColumnsWithResourceDetail?.data?.map((detail, index) => (
              <Grid item md={isQueryColumns ? 12 : 6} key={index}>
                <Box
                  sx={{ marginBottom: 3 }}
                  className="available-box box-alt avcrd"
                >
                  <h3 className="mb-0 mb-8">
                    Available Columns for Resource: {detail?.resourceName}
                  </h3>
                  <div className="avail-columns-group">
                    {detail?.rcolums?.map((column: any, idx: any) => (
                      <button
                        className="avail-columns"
                        key={idx}
                        onClick={() =>
                          handleColumnSelectWithPrefix(
                            column,
                            detail?.resourceCode
                          )
                        }
                      >
                        {column}
                      </button>
                    ))}
                  </div>
                </Box>
              </Grid>
            ))
          ) : (
            // If `queryType` is not "ADHOCQUERY"
            <Grid item md={isQueryColumns ? 12 : 6}>
              <Box
                sx={{ marginBottom: 3 }}
                className="available-box box-alt avc"
              >
                <h3 className="mb-0 mb-8">Available Columns:</h3>
                <div className="avail-columns-group">
                  {availColumnsWithResourceDetail?.data?.map(
                    (column, index) => (
                      <button
                        className="avail-columns"
                        key={index}
                        onClick={() => handleColumnSelect(column)}
                      >
                        {column}
                      </button>
                    )
                  )}
                </div>
              </Box>
            </Grid>
          )
        ) : availColumns?.length > 0 ? (
          // If `availColumns` exists and has elements
          <Grid item md={isQueryColumns ? 12 : 6}>
            <Box sx={{ marginBottom: 3 }} className="available-box box-alt avc">
              <h3 className="mb-0 mb-8">Available Columns:</h3>
              <div className="avail-columns-group">
                {availColumns.map((column, index) => (
                  <button
                    className="avail-columns"
                    key={index}
                    onClick={() => handleColumnSelect(column)}
                  >
                    {column}
                  </button>
                ))}
              </div>
            </Box>
          </Grid>
        ) : // If none of the above conditions are true
        null}
        {mergeQuerycolumns ? (
          <Grid item md={isQueryColumns ? 12 : 6}>
            <ResourceColumnsChips
              setEditorValue={setQueryBuilderTempValue}
              editorRef={editorRef}
              columns={mergeQuerycolumns}
              isFinalQuery={isFinalQuery}
              dialogType={dialogType ?? ""}
            />
          </Grid>
        ) : (
          showAggregatedColumns === false &&
          availColumns?.length === 0 && <p>No columns available.</p>
        )}
      </Grid>
    </>
  );
};

export default SQLEditor;
