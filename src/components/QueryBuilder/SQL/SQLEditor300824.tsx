import React, { useState, useRef } from "react";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-sql";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-noconflict/ext-language_tools";
import { Box, Button, Grid, TextField } from "@mui/material";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import AvailableVariables from "../../Molecules/Variables/AvailableVariables";

interface SQLEditorProps {
  error?: any;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
}

const SQLEditor: React.FC<SQLEditorProps> = ({ error, setErrors }) => {
  const {
    availColumns,
    availColumnsWithResourceDetail,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    editorRef,
  } = useRuleResourceContext();

  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);

  // const editorRef = useRef<AceEditor | null>(null);

  const handleSqlChange = (newSqlQuery: string) => {
    setQueryBuilderTempValue(newSqlQuery);
    if (newSqlQuery) {
      setErrors((prevError) => ({
        ...prevError,
        query: "",
      }));
    } else {
      setErrors((prevError) => ({
        ...prevError,
        query: "Please enter query",
      }));
    }
  };

  const handleColumnSelect = (column: string) => {
    const editor = editorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      `"${column}"` +
      " " +
      currentLine.slice(currentColumn);

    setQueryBuilderTempValue(updatedLine);
    setSelectedColumns([...selectedColumns, column]);
    editor.session.insert(currentPosition, `"${column}"`);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + column.length + 2,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };
  const handleVariableSelect = (variable: string) => {
    const editor = editorRef.current?.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;
    const updatedLine =
      currentLine.slice(0, currentColumn) +
      `$$${variable}$$` +
      " " +
      currentLine.slice(currentColumn);

    setQueryBuilderTempValue(updatedLine);
    editor.session.insert(currentPosition, `$$${variable}$$ `);

    // Calculate the new cursor position
    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + variable.length + 4,
    };

    // Set the new cursor position
    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  const editorOptions = {
    enableLiveAutocompletion: true,
    enableBasicAutocompletion: true,
    enableSnippets: false,
    showLineNumbers: true,
    tabSize: 2,
    fontSize: 20,
  };

  return (
    <>
      {availColumns && availColumns.length > 0 && (
        <Box sx={{ marginBottom: 3 }} className="available-box box-alt">
          <h3 className="mb-0 mb-8">Available Columns:</h3>
          <div className="avail-columns-group">
            {availColumns.map((column, index) => (
              <button
                className="avail-columns"
                key={index}
                onClick={() => handleColumnSelect(column)}
              >
                {column}
              </button>
            ))}
          </div>
        </Box>
      )}

      <Box sx={{ marginBottom: 3 }}>
        <Box className={`available-box `}>
          <AvailableVariables
            error={error}
            setErrors={setErrors}
            handleVariableSelect={handleVariableSelect}
          />
        </Box>
      </Box>
      <Box className="label-text">
        Write a custom SQL query<span className="required-asterisk">*</span>
        <br />
        {/* {"(SELECT * FROM <RES_A> WHERE [A1] IN [##MISSING## A_A1])"} */}
      </Box>
      <Box>
        <AceEditor
          mode="sql"
          theme="github"
          onChange={handleSqlChange}
          value={queryBuilderTempValue || ""}
          width="100%"
          height="100px"
          editorProps={{ $blockScrolling: true }}
          setOptions={editorOptions}
          ref={editorRef}
          showGutter
          // placeholder="Write your query here..."
          // id="editor"
          aria-label="editor"
          name="editor"
          fontSize={16}
          minLines={8}
          maxLines={8}
          showPrintMargin={false}
          className={`sql-editor ${error?.query ? "has-error" : ""}`}
        />
        <span className="validation-error">
          {error?.query && <span>{error?.query}</span>}
        </span>
      </Box>
    </>
  );
};

export default SQLEditor;
