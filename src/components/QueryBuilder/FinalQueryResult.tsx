import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import RenderVariables from "../Molecules/Resource/RenderVariables";

interface Props {
  sqlQry: string;
  error?: any;
  setErrors: React.Dispatch<React.SetStateAction<any>>;
}

const FinalQueryResult: React.FC<Props> = ({
  sqlQry,
  error,
  setErrors,
}: any) => {
  // Use a regular expression to find variables wrapped with $$
  const {
    globalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    queryBuilderTempValue,
  } = useRuleResourceContext();

  const [variables, setVariables] = useState<any>([]);

  useEffect(() => {
    const queryVariables = (
      (queryBuilderTempValue &&
        queryBuilderTempValue?.match(/\$\$(.*?)\$\$/g)) ||
      []
    ).map((match: any) => match.slice(2, -2).toLowerCase());

    const getUniqueValues = (arr: any) => {
      return arr.filter((v: any, i: any, a: any) => a.indexOf(v) === i);
    };
    setVariables(getUniqueValues(queryVariables) ?? []);
  }, [queryBuilderTempValue]);

  useEffect(() => {
    if (variables?.length > 0 && globalVariables) {
      const globalVars: any = {};
      variables.forEach((variable: any) => {
        if (globalVariables[variable] !== undefined) {
          globalVars[variable] = globalVariables[variable];
        } else if (tempGlobalVariables[variable] !== undefined) {
          globalVars[variable] = tempGlobalVariables[variable];
        } else {
          globalVars[variable] = "";
        }
      });
      // Compare the current tempLocalVariables with the new ones to avoid unnecessary updates
      if (JSON.stringify(tempGlobalVariables) !== JSON.stringify(globalVars)) {
        // setTempLocalVariables(globalVars);
        setTempGlobalVariables(globalVars);
      }
    } else {
      setTempGlobalVariables({});
    }
  }, [variables]);

  return (
    <>
      <RenderVariables error={error} setErrors={setErrors} />
    </>
  );
};

export default React.memo(FinalQueryResult);
