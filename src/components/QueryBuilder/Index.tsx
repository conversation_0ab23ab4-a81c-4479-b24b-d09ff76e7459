import React, { useEffect, useState } from "react";
import "react-querybuilder/dist/query-builder.scss";
import "../../styles/styles.scss";
import { Box, Grid } from "@mui/material";
import SQLFormat from "./SQL/SQLFormat";
import FinalQueryResult from "./FinalQueryResult";
import { customRules } from "../../services/constants/Rules";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";

interface Rule {
  field: string;
  operator: string;
  value: string | number | boolean;
}

interface Query {
  combinator: string;
  rules: Rule[];
}

interface MyComponentProps {
  handleChange?: any;
  isMarginToTop?: boolean;
  error?: any;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  isDerivedStyles?: boolean;
  mergeQuerycolumns?: any;
  isFinalQuery?: any;
  dialogType?: string;
  queryBuilderType?: string;
}

const initialQuery: Query = { combinator: "and", rules: [] };

const QueryBuilder: React.FC<MyComponentProps> = (props: any) => {
  const {
    handleChange,
    isMarginToTop,
    error,
    setErrors,
    isDerivedStyles,
    mergeQuerycolumns,
    isFinalQuery,
    dialogType,
    queryBuilderType,
  } = props;
  const { queryBuilderTempValue } = useRuleResourceContext();
  const [query, setQuery] = useState<Query>(initialQuery);
  const [sqlQry, setSqlQry] = useState<string>("");
  const [conditions, setConditions] = useState<string>("");
  const [groupBy, setGroupBy] = useState<string>("");

  useEffect(() => {
    const handelBuildQry = () => {
      const allconditions = query?.rules
        .filter((rule) => rule.field && rule.value)
        .map((rule) => customRules(rule))
        .filter(Boolean)
        .join(" and ");
      setConditions(allconditions);

      const groupByAll = query?.rules
        .filter((rule) => rule.operator === "group_by")
        .map((rule) => rule.field);
      setGroupBy(groupByAll.join(", "));

      const orderByAll = query?.rules
        .filter((rule) => rule.operator === "order_by")
        .map((rule) => rule.field);

      // let qry = `SELECT * FROM my_table `;
      let qry = "";
      let qryGroupBy = "";
      let qryOrderBY = "";
      let qryCondition = "";

      if (conditions.length > 0) {
        qryCondition = `WHERE (${allconditions}) `;
      }

      if (groupBy.length > 0) {
        qryGroupBy = `GROUP BY ${groupBy} `;
      }

      if (orderByAll.length > 0) {
        qryOrderBY = `ORDER BY ${orderByAll.join(", ")} `;
      }

      return `${qry} ${qryCondition} ${qryGroupBy} ${qryOrderBY}`;
    };
  }, [query, conditions, groupBy]);

  useEffect(() => {
    if (queryBuilderTempValue !== undefined)
      handleChange(queryBuilderTempValue, []);
  }, [queryBuilderTempValue]);

  return (
    <>
      <Grid
        item
        xs={12}
        sm={12}
        md={12}
        lg={12}
        xl={12}
        className="cell cell-1"
      >
        <Box
          className={`modal-left-column ${
            isMarginToTop ? "negative-margin" : ""
          } ${isDerivedStyles ? "negative-margin-1" : ""}  `}
        >
          <Box className="sql-textarea-box">
            <SQLFormat
              error={error}
              setErrors={setErrors}
              mergeQuerycolumns={mergeQuerycolumns}
              isFinalQuery={isFinalQuery}
              dialogType={dialogType ?? ""}
              queryBuilderType={queryBuilderType ?? ""}
            />
          </Box>
          {/* convert query builder expression in query condition */}
          {/* <Box>{formatQuery(query, "sql")}</Box> */}
          <Box className="sql-result-box mb-0">
            {/* Final query result */}

            <FinalQueryResult
              sqlQry={sqlQry}
              error={error}
              setErrors={setErrors}
            />

            {/* custom SQL Query editor */}
          </Box>
        </Box>
      </Grid>
    </>
  );
};

export default QueryBuilder;
