import { <PERSON>, Button, Grid, TextField } from "@mui/material";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { IResearchQuery } from "../../types/researchQuery";
import SQLEditor from "../QueryBuilder/SQL/SQLEditor";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import RenderVariables from "../Molecules/Resource/RenderVariables";
import {
  extractVariables,
  replaceQueryToDomainMapped,
} from "../../services/utils";
import { mergeQuerySchema } from "../../schemas";
import * as Yup from "yup";
import QueryBuilderDialog from "./QueryBuilderDialog";

interface ResourceQueryTabProps {
  checked: boolean;
  handleTransition: any;
  researchQueryData: IResearchQuery[];
  setResearchQueryData: Dispatch<SetStateAction<IResearchQuery[]>>;
  columns: any;
  setColumns: Dispatch<SetStateAction<any>>;
  isEditQuery: any;
  setIsEditQuery: any;
  editFormData: any;
  setQueryType: any;
  setEditFormData: any;
  handleSaveResearchQuery?: (researchQuery: IResearchQuery[]) => void;
}

const MergeQueryTab = ({
  handleSaveResearchQuery,
  checked,
  handleTransition,
  researchQueryData,
  setResearchQueryData,
  columns,
  setColumns,
  isEditQuery,
  setIsEditQuery,
  editFormData,
  setQueryType,
  setEditFormData,
}: ResourceQueryTabProps) => {
  const [openQueryDialogIndex, setOpenQueryDialogIndex] = useState<
    number | null
  >(null);
  const [queryError, setQueryError] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState<any>({
    query: "SELECT * FROM <MERGED_DATA> WHERE [] in",
  });
  const [mergedQueryColumns, setMergedQueryColumns] = useState<any>({
    missingColumns: [],
  });
  const [variables, setVariables] = useState<any>([]);

  const {
    editorRef,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    globalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    setGlobalVariables,
  } = useRuleResourceContext();

  // useEffect(() => {
  //   // if (queryBuilderTempValue.length < 0)
  //   setQueryBuilderTempValue("SELECT * FROM <MERGED_DATA> WHERE [] in ");
  // }, []);
  useEffect(() => {
    const queryVariables: any = extractVariables(queryBuilderTempValue);
    setVariables(queryVariables ?? []);
  }, [queryBuilderTempValue]);

  useEffect(() => {
    if (variables?.length > 0 && globalVariables) {
      const globalVars: any = {};
      variables.forEach((variable: any) => {
        if (globalVariables[variable] !== undefined) {
          globalVars[variable] = globalVariables[variable];
        } else if (tempGlobalVariables[variable] !== undefined) {
          globalVars[variable] = tempGlobalVariables[variable];
        } else {
          globalVars[variable] = "";
        }
      });
      // Compare the current tempLocalVariables with the new ones to avoid unnecessary updates
      if (JSON.stringify(tempGlobalVariables) !== JSON.stringify(globalVars)) {
        // setTempLocalVariables(globalVars);
        setTempGlobalVariables(globalVars);
      }
    } else {
      setTempGlobalVariables({});
    }
  }, [variables]);
  const handleChangeQuery = (event: any) => {
    const { name, value } = event.target;
    setFormData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
    validateField(name, value);
  };

  const handleSaveQuery = async () => {
    try {
      const researchQuery: any = [...researchQueryData];

      // MergeQuery validation
      const updatedFormData = {
        ...formData,
        query: queryBuilderTempValue,
      };

      await mergeQuerySchema.validate(updatedFormData, {
        abortEarly: false,
      });

      // Variable validations
      const variableSchema: any = {};
      Object.keys(tempGlobalVariables).forEach((key: any) => {
        if (tempGlobalVariables[key] === "") {
          variableSchema[key] = Yup.string().required(`Please enter ${key}`);
          setQueryError((prevErrors) => ({
            ...prevErrors,
            [key]: `Please enter ${key}`,
          }));
        }
      });

      const mergeQueryVariableSchema = Yup.object().shape(variableSchema);
      await mergeQueryVariableSchema.validate(tempGlobalVariables, {
        abortEarly: false,
      });

      setGlobalVariables((prev: any) => ({
        ...Object.keys(tempGlobalVariables).reduce(
          (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
          prev
        ),
      }));

      const updatedQuery: string = replaceQueryToDomainMapped(
        queryBuilderTempValue,
        columns?.missingColumns
      );

      if (isEditQuery) {
        const index = researchQuery.findIndex(
          (item: any) => item.id === formData.id
        );

        if (index !== -1) {
          researchQuery[index] = {
            id: formData.id,
            name: formData.name,
            query: queryBuilderTempValue,
            updatedQuery: updatedQuery,
            source: {
              type: "Merged_Data",
            },
          };
        }
      } else {
        researchQuery.push({
          id: researchQuery.length > 0 ? researchQuery.length + 1 : 1,
          name: formData?.name,
          query: queryBuilderTempValue,
          updatedQuery: updatedQuery,
          source: {
            type: "Merged_Data",
          },
        });
      }

      // ✅ Update state with latest researchQuery
      setResearchQueryData([...researchQuery]);

      setFormData({ name: "" });
      setTempGlobalVariables({});
      setQueryBuilderTempValue("SELECT * FROM <MERGED_DATA> WHERE [] in ");
      handleTransition(false);
      setQueryType("");

      // ✅ Pass updated researchQuery to handleSaveResearchQuery
      if (handleSaveResearchQuery) {
        handleSaveResearchQuery(researchQuery);
      }
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(/^source?\./, "");
            newErrors[fieldName] = error.message;
          }
        );
      }
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await mergeQuerySchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: undefined,
      }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  useEffect(() => {
    if (isEditQuery) {
      setFormData(editFormData);
    }
  }, [editFormData]);

  return (
    <>
      <Grid item xl={6} lg={6} md={6} sm>
        <TextField
          name="name"
          label={
            <span>
              Query Name
              <span className="required-asterisk">*</span>
            </span>
          }
          type="text"
          fullWidth
          variant="outlined"
          // className="form-control-autocomplete"
          value={formData?.name}
          onChange={handleChangeQuery}
          error={!!queryError?.name}
          helperText={queryError?.name || ""}
          className={`form-control-autocomplete
                            ${queryError?.name ? "has-error" : ""}
                          `}
        />
      </Grid>
      <QueryBuilderDialog
        columns={columns}
        index={15}
        openQueryDialogIndex={openQueryDialogIndex}
        setOpenQueryDialogIndex={setOpenQueryDialogIndex}
        isTextBoxRequied={true}
        queryBuilderType="rule_research_query"
      />
      <Grid
        item
        xs={12}
        sm={12}
        md={12}
        lg={12}
        xl={12}
        sx={{
          display: "flex",
          columnGap: "8px",
          textAlign: "right",
          justifyContent: "flex-end",
        }}
      >
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange btn-dark"
          onClick={() => {
            setFormData({
              name: "",
            });
            setQueryBuilderTempValue(
              "SELECT * FROM <MERGED_DATA> WHERE [] in "
            );
            setQueryType("");
            setEditFormData({
              name: "",
            });
            handleTransition(false);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange"
          onClick={handleSaveQuery}
        >
          <SaveOutlinedIcon /> Save
        </Button>
      </Grid>
    </>
  );
};

export default MergeQueryTab;
