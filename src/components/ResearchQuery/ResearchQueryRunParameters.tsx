import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import {
  Box,
  Button,
  Checkbox,
  Grid,
  TextField,
  Autocomplete,
  Tooltip,
} from "@mui/material";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useToast } from "../../services/utils";
import useFetchRunInstance from "../../hooks/useFetchRunInstance";
import {
  IconBtnEditBase,
  IconExistingRunName,
  IconNewRunName,
} from "../../common/utils/icons";
import { addRunInstance } from "../../services/runInstance";
import { addRunInstanceFromExecutionSchema } from "../../schemas";

interface IResearchQueryRunParameters {
  runParameters: any;
  setRunParameters: Dispatch<SetStateAction<any>>;
  isEditResource: boolean;
  setIsEditResource: Dispatch<SetStateAction<boolean>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  isShow?: {
    isShowReportName?: boolean;
  };
  runInstance?: any;
}

const ResearchQueryRunParameters = ({
  runParameters,
  setRunParameters,
  isEditResource,
  setIsEditResource,
  setIsLoading,
  isShow = {
    isShowReportName: true,
  },
  // runInstance is not used directly but is included in the interface for future use
  runInstance: _runInstance,
}: IResearchQueryRunParameters) => {
  const { showToast } = useToast();
  const [formData, setFormData] = useState<any>({});
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [runInstanceData] = useFetchRunInstance({ setIsLoading });
  const [isOldRunName, setIsOldRunName] = useState<boolean>(true);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [newRunName, setNewRunName] = useState<any>("");
  const newRunNameRef = useRef<HTMLInputElement>(null);
  const [instanceData, setInstanceData] = useState<any>([]);

  useEffect(() => {
    setInstanceData(runInstanceData);
  }, [runInstanceData]);

  useEffect(() => {
    if (runParameters) {
      const newFormData: any = {
        no_of_records_in_response:
          runParameters?.no_of_records_in_response || 0,
        no_of_records_in_output_files:
          runParameters?.no_of_records_in_output_files || 0,
        no_of_records_in_filter_rule_output_files:
          runParameters?.no_of_records_in_filter_rule_output_files || 0,
        generate_output_files: runParameters?.generate_output_files ?? true,
        pull_new_files_from_server:
          runParameters?.pull_new_files_from_server ?? true,
        pull_latest_files: runParameters?.pull_latest_files ?? false,
        keep_downloaded_files: runParameters?.keep_downloaded_files ?? false,
        save_input_data_files: runParameters?.save_input_data_files ?? false,
        report_name: runParameters?.report_name || "",
        run_instance: {
          run_id: runParameters?.run_instance?.run_id || null,
          run_name: runParameters?.run_instance?.run_name || null,
        },
      };
      setFormData(newFormData);
      setInitialFormData(JSON.parse(JSON.stringify(newFormData)));
      setIsEditResource(false);
    }
  }, [runParameters, setIsEditResource]);

  // Update hasChanges when formData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);
    setHasChanges(formChanged);
  }, [formData, initialFormData]);

  const handleChangeRunInstance = (_: any, value: any) => {
    setFormData((prevFormData: any) => ({
      ...prevFormData,
      run_instance: {
        run_id: value?.id,
        run_name: value?.run_name,
      },
    }));
  };

  const handleChangeNewRunName = (e: any, value: any) => {
    setNewRunName(value);
    validateField(e.target.name, e.target.value);
  };

  const handleFocusTextField = () => {
    setTimeout(() => {
      if (newRunNameRef.current) {
        newRunNameRef.current.focus();
      }
    }, 50);
  };

  const validateField = async (name: any, value: any) => {
    try {
      const partialFormData = { ...newRunName, [name]: value };
      await addRunInstanceFromExecutionSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleSaveRunName = async (e: any) => {
    e.preventDefault();
    const reqBody = {
      run_name: newRunName,
      code: newRunName,
    };
    try {
      await addRunInstanceFromExecutionSchema.validate(reqBody, {
        abortEarly: false,
      });
      addRunInstance({
        payload: reqBody,
      })
        .then((response) => {
          if (response) {
            setFormData((prevFormData: any) => ({
              ...prevFormData,
              run_instance: {
                ...prevFormData.run_instance,
                run_name: response?.run_name,
                run_id: response?.id,
              },
            }));

            setInstanceData((prev: any) => [
              {
                code: response.code,
                created_by: response.created_by,
                created_on: response.created_on,
                description: response.description,
                id: response.id,
                is_active: response.is_active,
                modified_by: response.modified_by,
                run_name: response.run_name,
                updated_on: response.updated_on,
              },
              ...prev,
            ]);
            setIsOldRunName(true);
            showToast(`Run Instance created successfully!`, "success");
          }
        })
        .catch((err) => {
          console.error(`Cannot create run instance`, err);
        });
    } catch (err) {
      console.error("Validation failed:", err);
    }
  };

  const saveRunParameters = () => {
    const newRunParameters = {
      no_of_records_in_response: formData?.no_of_records_in_response || 0,
      no_of_records_in_output_files:
        formData?.no_of_records_in_output_files || 0,
      no_of_records_in_filter_rule_output_files:
        formData?.no_of_records_in_filter_rule_output_files || 0,
      generate_output_files: formData?.generate_output_files,
      pull_new_files_from_server: formData?.pull_new_files_from_server,
      pull_latest_files: formData?.pull_latest_files,
      keep_downloaded_files: formData?.keep_downloaded_files,
      save_input_data_files: formData?.save_input_data_files,
      report_name: formData?.report_name || "",
      run_instance: {
        run_id: formData?.run_instance?.run_id || 1,
        run_name: formData?.run_instance?.run_name || "Legacy",
      },
    };
    setRunParameters(newRunParameters);
    setIsEditResource(false);
    showToast("Run parameters saved successfully!", "success");
  };

  return (
    <>
      {!isEditResource ? (
        <Box>
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                No Of Records In Response
              </label>
              <div className="form-control">
                {runParameters?.no_of_records_in_response}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                No Of Records In Output Files
              </label>
              <div className="form-control">
                {runParameters?.no_of_records_in_output_files}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                No Of Records In Filter Rule Output Files
              </label>
              <div className="form-control">
                {runParameters?.no_of_records_in_filter_rule_output_files}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">Run Name</label>
              <div className="form-control">
                {runParameters?.run_instance?.run_name || "N/A"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Generate Output Files
              </label>
              <div className="form-control">
                {runParameters?.generate_output_files ? "True" : "False"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Pull New Files From Server
              </label>
              <div className="form-control">
                {runParameters?.pull_new_files_from_server ? "True" : "False"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">Pull Latest Files</label>
              <div className="form-control">
                {runParameters?.pull_latest_files ? "True" : "False"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Keep Downloaded Files
              </label>
              <div className="form-control">
                {runParameters?.keep_downloaded_files ? "True" : "False"}
              </div>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
              <label className="label-text text-bold">
                Save Input Data Files
              </label>
              <div className="form-control">
                {runParameters?.save_input_data_files ? "True" : "False"}
              </div>
            </Grid>
            {isShow.isShowReportName && (
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="label-text text-bold">Report Name</label>
                <div className="form-control">
                  {runParameters?.report_name || "N/A"}
                </div>
              </Grid>
            )}
            <Grid item xs sx={{ justifyContent: "flex-end", display: "flex" }}>
              <button
                className="btn-nostyle icon-btn-edit"
                onClick={() => setIsEditResource(true)}
              >
                <IconBtnEditBase />
              </button>
            </Grid>
          </Grid>
        </Box>
      ) : (
        <>
          <Box>
            <Grid container rowSpacing={1.5} columnSpacing={2.5}>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <TextField
                  type="number"
                  name="no_of_records_in_response"
                  fullWidth
                  label="No Of Records In Response"
                  variant="outlined"
                  className="form-control-autocomplete"
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    const inputValue = event.target.value;
                    if (
                      inputValue === "" ||
                      (parseInt(inputValue) >= 0 &&
                        !isNaN(parseInt(inputValue)))
                    ) {
                      setFormData({
                        ...formData,
                        no_of_records_in_response: inputValue,
                      });
                    }
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.no_of_records_in_response}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <TextField
                  type="number"
                  name="no_of_records_in_output_files"
                  fullWidth
                  label="No Of Records In Output Files"
                  variant="outlined"
                  className="form-control-autocomplete"
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    const inputValue = event.target.value;
                    if (
                      inputValue === "" ||
                      (parseInt(inputValue) >= 0 &&
                        !isNaN(parseInt(inputValue)))
                    ) {
                      setFormData({
                        ...formData,
                        no_of_records_in_output_files: inputValue,
                      });
                    }
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.no_of_records_in_output_files}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <TextField
                  type="number"
                  name="no_of_records_in_filter_rule_output_files"
                  fullWidth
                  label="No Of Records In Filter Rule Output Files"
                  variant="outlined"
                  className="form-control-autocomplete"
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    const inputValue = event.target.value;
                    if (
                      inputValue === "" ||
                      (parseInt(inputValue) >= 0 &&
                        !isNaN(parseInt(inputValue)))
                    ) {
                      setFormData({
                        ...formData,
                        no_of_records_in_filter_rule_output_files: inputValue,
                      });
                    }
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  value={formData?.no_of_records_in_filter_rule_output_files}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <Box className="label-text line-height16">
                  {isOldRunName ? "Run Name" : "New Run Name"}{" "}
                </Box>
                <Box className="run-name-update">
                  <Box sx={{ flex: "1" }}>
                    {isOldRunName ? (
                      <Autocomplete
                        fullWidth
                        options={instanceData || []}
                        getOptionLabel={(runInstanceData) =>
                          runInstanceData.run_name
                        }
                        onChange={(event, value) =>
                          handleChangeRunInstance(event, value)
                        }
                        value={
                          instanceData?.find((option: { id: any }) => {
                            return (
                              option?.id === formData?.run_instance?.run_id
                            );
                          }) || null
                        }
                        renderInput={(params) => (
                          <TextField
                            name="run_instance"
                            style={{ color: "#000000" }}
                            {...params}
                            placeholder="Select..."
                            InputLabelProps={{
                              shrink: true,
                            }}
                          />
                        )}
                        loadingText="Loading..."
                        className="form-control-autocomplete"
                      />
                    ) : (
                      <TextField
                        type="text"
                        title="run_name"
                        name="run_name"
                        fullWidth
                        variant="outlined"
                        className={`form-control-autocomplete `}
                        onChange={(event: any) => {
                          handleChangeNewRunName(event, event.target.value);
                        }}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        value={newRunName || ""}
                        error={!!errors?.run_name}
                        helperText={errors?.run_name || ""}
                        inputRef={newRunNameRef}
                      />
                    )}
                  </Box>
                  <Box className="btn-group">
                    {isOldRunName ? (
                      <Button
                        color="secondary"
                        variant="contained"
                        onClick={() => {
                          setIsOldRunName(!isOldRunName);
                          handleFocusTextField();
                        }}
                        className="btn-orange btn-dark"
                      >
                        <Tooltip title="Add new run name" placement="top">
                          <Box>
                            <IconNewRunName />
                          </Box>
                        </Tooltip>
                      </Button>
                    ) : (
                      <>
                        <Button
                          type="submit"
                          color="secondary"
                          variant="contained"
                          className="btn-orange btn-dark"
                          onClick={(e) => handleSaveRunName(e)}
                        >
                          <Tooltip title="Save new run name" placement="top">
                            <Box>
                              <SaveOutlinedIcon />
                            </Box>
                          </Tooltip>
                        </Button>
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => setIsOldRunName(!isOldRunName)}
                          className="btn-orange btn-dark"
                        >
                          <Tooltip
                            title="Add existing run name"
                            placement="top"
                          >
                            <Box>
                              <IconExistingRunName />
                            </Box>
                          </Tooltip>
                        </Button>
                      </>
                    )}
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.generate_output_files}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        generate_output_files: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Generate Output Files</span>
                </label>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.pull_new_files_from_server}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        pull_new_files_from_server: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Pull New Files From Server</span>
                </label>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.pull_latest_files}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        pull_latest_files: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Pull Latest Files</span>
                </label>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.keep_downloaded_files}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        keep_downloaded_files: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Keep Downloaded Files</span>
                </label>
              </Grid>
              <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                <label className="base-resource-checkbox">
                  <Checkbox
                    checked={formData?.save_input_data_files}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        save_input_data_files: event.target.checked,
                      });
                    }}
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                    }}
                  />
                  <span>Save Input Data Files</span>
                </label>
              </Grid>
              {isShow.isShowReportName && (
                <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
                  <TextField
                    type="text"
                    name="report_name"
                    fullWidth
                    label="Report Name"
                    variant="outlined"
                    className="form-control-autocomplete"
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                      setFormData({
                        ...formData,
                        report_name: event.target.value,
                      });
                    }}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    value={formData?.report_name || ""}
                  />
                </Grid>
              )}
              <Grid
                item
                xs
                sx={{
                  display: "flex",
                  alignItems: "flex-end",
                  justifyContent: "flex-end",
                  columnGap: "20px",
                }}
              >
                <Button
                  color="secondary"
                  variant="contained"
                  onClick={() => setIsEditResource(false)}
                  className="btn-orange btn-dark"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  color="secondary"
                  variant="contained"
                  className="btn-orange"
                  onClick={saveRunParameters}
                  disabled={!hasChanges}
                >
                  <SaveOutlinedIcon /> &nbsp; Save
                </Button>
              </Grid>
            </Grid>
          </Box>
        </>
      )}
    </>
  );
};

export default ResearchQueryRunParameters;
