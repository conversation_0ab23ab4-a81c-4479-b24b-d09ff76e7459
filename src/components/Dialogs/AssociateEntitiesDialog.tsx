import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
} from "@mui/material";

import { useState } from "react";

const AssociateEntitiesDialog = ({
  openAssociateEntitiesDialog,
  setOpenAssociateEntitiesDialog,
  associateEntityId,
  downloadExportFile,
  setIsDownloadLoading,
  header,
}: any) => {
  const [isAssociateEntities, setIsAssociateEntities] = useState("with"); // Change the default value to "with" for export with associate entities

  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };

  const handleAssociateEntitiesSubmit = () => {
    if (associateEntityId !== null) {
      const status = isAssociateEntities === "with";
      downloadExportFile(associateEntityId, status, setIsDownloadLoading);
    }
    setOpenAssociateEntitiesDialog(false);
    setIsAssociateEntities("with");
  };

  const handleCloseDialog = () => {
    setOpenAssociateEntitiesDialog(false);
    setIsAssociateEntities("with");
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsAssociateEntities(event.target.value);
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={openAssociateEntitiesDialog}
      onClose={(event, reason) => handleClose(reason)}
      className="modal-dialog-1"
    >
      <DialogTitle className="dialog-title">
        <span>{`Export ${header} Json`}</span>
        <button className="close-icon" onClick={handleCloseDialog}></button>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Box
          sx={{
            paddingTop: "7px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <FormControl component="fieldset" sx={{ marginLeft: 1 }}>
            <RadioGroup
              row
              aria-label="entities"
              name="entities"
              value={isAssociateEntities}
              onChange={handleRadioChange}
            >
              <FormControlLabel
                value="with"
                control={
                  <Radio
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                      "& .MuiSvgIcon-root": {
                        backgroundColor: "transparent",
                      },
                    }}
                  />
                }
                label="Export with associate component"
              />
              <FormControlLabel
                value="without"
                control={
                  <Radio
                    sx={{
                      "&.Mui-checked": {
                        color: "#196BB4",
                      },
                      "& .MuiSvgIcon-root": {
                        backgroundColor: "transparent",
                      },
                    }}
                  />
                }
                label="Export without associate component"
              />
            </RadioGroup>
          </FormControl>
        </Box>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          type="button"
          color="secondary"
          variant="contained"
          onClick={handleAssociateEntitiesSubmit}
          className="btn-orange"
        >
          Download File
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AssociateEntitiesDialog;
