import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  Select,
  // Typography,
  Grid,
  TextField,
  Autocomplete,
  ListItemText,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import useFetchConnectionKeyByLinkedServiceId from "../../hooks/useFetchConnectionKeyByLinkedServiceId";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import {
  internalReferenceSchema,
  externalReferenceSchema,
} from "../../schemas";
import { useResourceContext } from "../../contexts/ResourceContext";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { IconClearSvg } from "../../common/utils/icons";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useToast } from "../../services/utils";

const ReferenceDialog = ({ setAllVariablesList }: any) => {
  const {
    openReferenceDialog,
    // setOpenReferenceDialog,
    handleCloseReferenceDialog,
    referenceData,
    setReferenceData,
    onSaveReferenceDialog,
  } = useResourceContext();

  const { globalVariables, setGlobalVariables } = useRuleResourceContext();

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  // const [varErrors, setVarErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<any>(0);
  const [variables, setVariables] = useState<any>([]);
  const [referenceGlobalVariables, setReferenceGlobalVariables] =
    useState(globalVariables);
  const [referenceLocalVariables, setReferenceLocalVariables] = useState<any>(
    {}
  );

  useEffect(() => {
    setReferenceGlobalVariables(globalVariables);
  }, [globalVariables]);

  const [linkedServicesData] = useFetchLinkedServices({ setIsLoading });
  const [connectionKeysData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId,
    setIsLoading,
  });
  useEffect(() => {
    setSelectLinkedServiceId(referenceData?.linked_service?.id);
  }, [referenceData?.linked_service]);

  const extractVariables = (query: any) => {
    const matches = query?.match(/\$\$(.*?)\$\$/g);
    if (!matches) return [];
    return matches.map((match: any) => match.slice(2, -2).toLowerCase());
  };

  useEffect(() => {
    if (referenceData) {
      // Use a regular expression to find variables wrapped with $$
      const refVariables: any = extractVariables(referenceData?.source_type);

      const getUniqueValues = (arr: any) => {
        return arr.filter((v: any, i: any, a: any) => a.indexOf(v) === i);
      };

      const variables = getUniqueValues([...refVariables]);
      setVariables(variables);
    }
  }, [referenceData]);

  const validateField = async (name: any, value: any) => {
    try {
      const partialReferenceData = { ...referenceData, [name]: value };
      if (referenceData?.source === "internal") {
        await internalReferenceSchema.validateAt(name, partialReferenceData);
      } else {
        await externalReferenceSchema.validateAt(name, partialReferenceData);
      }
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      const referenceSchema: any = {};
      Object.keys(referenceLocalVariables).map((key: any) => {
        if (referenceLocalVariables[key] === "") {
          referenceSchema[key] = Yup.string().required(`Please enter ${key}`);
          setErrors((prevErrors) => ({
            ...prevErrors,
            [key]: `Please enter ${key}`,
          }));
        } else {
          return {
            name: key,
            value: referenceLocalVariables[key],
          };
        }
      });
      if (referenceData?.source === "internal") {
        const internalSchema = Yup.object().shape({
          ...referenceSchema,
          source_type: Yup.string().required("Source Type is required"),
        });
        await internalSchema.validate(referenceData, {
          abortEarly: false,
        });
      } else {
        const externalSchema = Yup.object().shape({
          ...referenceSchema,
          source_type: Yup.string().required("Source Type is required"),
          connection_key: Yup.object().required("Connection key is required"),
          linked_service: Yup.object().required("Linked service is required"),
        });
        await externalSchema.validate(referenceData, {
          abortEarly: false,
        });
      }
      // try {
      //   await referenceSchema.validate(referenceData, {
      //     abortEarly: false,
      //   });
      // } catch (validationErrors: any) {
      //   const newErrors: { [key: string]: string } = {};
      //   validationErrors.inner.forEach(
      //     (error: { path: string | number; message: string }) => {
      //       newErrors[error.path] = error.message;
      //     }
      //   );
      //   setErrors(newErrors);
      // }
      setGlobalVariables(referenceGlobalVariables);
      setAllVariablesList((prev: any) => ({
        ...prev,
        resource: {
          ...prev.resource,
          ...referenceLocalVariables,
        },
        resourceColumn: {
          ...prev.resourceColumn,
          ...referenceLocalVariables,
        },
      }));
      onSaveReferenceDialog();
      setVariables([]);
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors?.inner?.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors(newErrors);
    }
  };
  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };

  const handleCloseDialog = () => {
    setErrors({
      source_type: "",
    });
    handleCloseReferenceDialog();
    setVariables([]);
    setReferenceGlobalVariables(globalVariables);
    setReferenceLocalVariables({});
  };
  const handleChangeTranslation = (name: any, value: any) => {
    setReferenceData({
      ...referenceData,
      [name]: value,
    });
  };
  const handleChangeReference = (name: any, value: any) => {
    if (name === "source_type") {
      const queryVariables = (value?.match(/\$\$(.*?)\$\$/g) || []).map(
        (match: any) => match.slice(2, -2).toLowerCase()
      );
      const getUniqueValues = (arr: any) => {
        return arr.filter((v: any, i: any, a: any) => a.indexOf(v) === i);
      };

      const variablesArr = getUniqueValues(queryVariables);
      if (variablesArr.length) {
        setVariables(variablesArr);
      } else {
        setVariables(variablesArr);
      }
    }

    if (name === "source") {
      setReferenceData({
        ...referenceData,
        [name]: value,
        linked_service: null,
        connection_key: null,
      });
      setErrors({
        ...errors,
        linked_service: "",
        connection_key: "",
      });
    } else {
      setReferenceData({
        ...referenceData,
        [name]: value,
      });
    }

    validateField(name, value);
  };

  const handelOnChangeLinkedService = (e: any, value: any) => {
    setSelectLinkedServiceId(value?.id);
    setReferenceData({
      ...referenceData,
      linked_service: value,
      connection_key: null,
    });
    validateField("linked_service", value);
  };

  const handelOnChangeConnectionKey = (e: any, value: any) => {
    setReferenceData({
      ...referenceData,
      connection_key: value,
    });
    validateField("connection_key", value);
  };

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={openReferenceDialog}
      onClose={(event, reason) => handleClose(reason)}
      className="modal-dialog-1"
    >
      <DialogTitle className="dialog-title">
        <span>Reference</span>
        <button className="close-icon" onClick={handleCloseDialog}></button>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Box>
          <Grid container rowSpacing={3} columnSpacing={3}>
            <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
              <label className="label-text">Location</label>
              <Select
                defaultValue="internal"
                value={referenceData.source}
                name="source"
                className="form-control"
                onChange={(e) =>
                  handleChangeReference(e.target.name, e.target.value)
                }
                sx={{
                  "& .MuiSelect-select": {
                    height: "36px",
                  },
                }}
              >
                <MenuItem value="internal">Internal</MenuItem>
                <MenuItem value="external">External</MenuItem>
              </Select>
            </Grid>
            <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
              <label className="label-text">
                Enter Source Type or Select...
              </label>
              <Autocomplete
                freeSolo
                disableClearable
                options={Object.keys(globalVariables)}
                groupBy={(option) => {
                  return "Available Variables";
                }}
                value={referenceData.source_type}
                className={`form-control-autocomplete ${
                  errors?.source_type ? "has-error" : ""
                }`}
                onChange={(event, value) => {
                  setReferenceData({
                    ...referenceData,
                    source_type: `$$${value}$$`,
                  });
                  validateField("source_type", `$$${value}$$`);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    name="source_type"
                    InputProps={{
                      ...params.InputProps,
                      type: "search",
                    }}
                    onChange={(e) =>
                      handleChangeReference(e.target.name, e.target.value)
                    }
                    error={!!errors?.source_type}
                    helperText={errors?.source_type || ""}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6} lg={4} xl={4} sx={{}}>
              <Box
                sx={{ display: { md: "block", sm: "none" } }}
                className="label-text"
              >
                &nbsp;
              </Box>
              <Box
                sx={{
                  paddingTop: "7px",
                  display: "flex",
                  alignContent: "center",
                }}
              >
                Use Translation:
                <Checkbox
                  title="use_translation"
                  name="use_translation"
                  className="refrence-checkbox"
                  checked={referenceData.use_translation}
                  onChange={(e) =>
                    handleChangeTranslation(e.target.name, e.target.checked)
                  }
                  sx={{
                    "&.Mui-checked": {
                      color: "#196BB4",
                    },
                  }}
                />
              </Box>
            </Grid>
            {referenceData?.source === "external" && (
              <>
                <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
                  <Autocomplete
                    fullWidth
                    options={linkedServicesData ?? []}
                    getOptionLabel={(option) => option.name || ""}
                    value={
                      linkedServicesData?.find(
                        (option: { id: any }) =>
                          option.id === referenceData?.linked_service?.id
                      ) || null
                    }
                    renderInput={(params) => (
                      <TextField
                        name="linked_service"
                        style={{ color: "#000000" }}
                        {...params}
                        // label="Linked Service"
                        placeholder="Select..."
                        InputLabelProps={{
                          shrink: true,
                        }}
                        required
                        error={!!errors?.linked_service}
                        helperText={errors?.linked_service || ""}
                      />
                    )}
                    renderOption={(params, item, { index }) => (
                      <li
                        {...params}
                        key={item.name + index}
                        style={{ paddingTop: "2px", paddingBottom: "2px" }}
                      >
                        <ListItemText>{item.name}</ListItemText>
                      </li>
                    )}
                    loadingText="Loading..."
                    onChange={(event, value) =>
                      handelOnChangeLinkedService(event, value)
                    }
                    className={`form-control-autocomplete button-container ${
                      errors?.linked_service ? "has-error" : ""
                    }`}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <Autocomplete
                    fullWidth
                    clearIcon={<IconClearSvg />}
                    options={
                      referenceData?.linked_service && connectionKeysData
                        ? connectionKeysData
                        : []
                    }
                    getOptionLabel={(connectionData) => connectionData.name}
                    onChange={(event, value) =>
                      handelOnChangeConnectionKey(event, value)
                    }
                    value={
                      connectionKeysData?.find(
                        (option: { id: any }) =>
                          option.id === referenceData?.connection_key?.id
                      ) || null
                    }
                    renderInput={(params) => (
                      <TextField
                        name="connection_key"
                        style={{ color: "#000000" }}
                        {...params}
                        // label="Connection Key"
                        placeholder="Select..."
                        InputLabelProps={{
                          shrink: true,
                        }}
                        required
                        error={!!errors?.connection_key}
                        helperText={errors?.connection_key || ""}
                      />
                    )}
                    loadingText="Loading..."
                    className={`form-control-autocomplete button-container ${
                      errors?.connection_key ? "has-error" : ""
                    }`}
                  />
                </Grid>
              </>
            )}
            <RenderVariables
              variables={variables}
              globalVariables={referenceGlobalVariables}
              setGlobalVariables={setReferenceGlobalVariables}
              localVariables={referenceLocalVariables}
              setLocalVariables={setReferenceLocalVariables}
              error={errors}
              setErrors={setErrors}
            />
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          type="button"
          color="secondary"
          variant="contained"
          onClick={handleSubmit}
          className="btn-orange"
        >
          <SaveOutlinedIcon /> &nbsp; Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReferenceDialog;

const RenderVariables = ({
  variables,
  globalVariables,
  setGlobalVariables,
  localVariables,
  setLocalVariables,
  error,
  setErrors,
}: any) => {
  const { showToast } = useToast();
  useEffect(() => {
    if (variables?.length > 0 && globalVariables) {
      const globalVars: any = {};
      variables.forEach((variable: any) => {
        if (globalVariables[variable] !== undefined) {
          globalVars[variable] = globalVariables[variable];
        } else {
          globalVars[variable] = "";
        }
      });
      setLocalVariables(globalVars);
    }
  }, [variables, globalVariables]);

  const isValidName = (name: string) => /^[a-zA-Z0-9_]+$/.test(name);

  const handleAddColumn = (e: any) => {
    if (e.target.name === "") {
      showToast("Please enter Variable Name", "warning");
      return;
    }
    if (!isValidName(e.target.name)) {
      showToast(
        "Variable Name can only contain alphabets, numbers, and underscores.",
        "warning"
      );
      return;
    }
    setGlobalVariables((prevGlobalVariables: any) => ({
      ...prevGlobalVariables,
      [e.target.name]: e.target.value,
    }));
    setLocalVariables((prevLocalVariables: any) => ({
      ...prevLocalVariables,
      [e.target.name]: e.target.value,
    }));
    setErrors((prevErrors: any) => ({
      ...prevErrors,
      [e.target.name]: e.target.value ? "" : `Please enter ${e.target.name}`,
    }));
  };

  return (
    <>
      {variables.map((variable: any, index: any) => (
        <>
          <Grid item xs={12} sx={{ padding: "0 !important" }}></Grid>
          <Grid item>
            <label className="label-text pt-13">
              {variable} : <span className="required-asterisk">*</span>
            </label>
          </Grid>
          <Grid item xs>
            <TextField
              fullWidth
              type="text"
              // placeholder={`Enter ${variable} value`}
              name={variable}
              className={`form-control  ${
                error?.[variable] ? "has-error" : ""
              }`}
              onChange={(e) => handleAddColumn(e)}
              sx={{ width: "100%" }}
              value={
                globalVariables &&
                typeof globalVariables === "object" &&
                globalVariables[variable] !== undefined
                  ? globalVariables[variable]
                  : ""
              }
              error={!!error?.[variable] || !!error?.duplicateColumn}
              helperText={error?.[variable] || error?.duplicateColumn || ""}
            />
          </Grid>
          <Grid item xs={12} sx={{ padding: "0 !important" }}></Grid>
        </>
      ))}
    </>
  );
};
