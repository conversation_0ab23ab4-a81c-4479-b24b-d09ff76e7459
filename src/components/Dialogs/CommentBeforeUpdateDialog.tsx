import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Box,
} from "@mui/material";
interface CommentBeforeUpdateDialogProps {
  title: string;
  dialogContent: React.ReactNode;

  openConfirmation: boolean;
  handleSaveComment: React.Dispatch<React.SetStateAction<any>>;
  handleCancel: React.Dispatch<React.SetStateAction<any>>;
}
const CommentBeforeUpdateDialog = ({
  title,
  dialogContent,

  openConfirmation,
  handleSaveComment,
  handleCancel,
}: CommentBeforeUpdateDialogProps) => {
  return (
    <Dialog
      fullWidth
      open={openConfirmation}
      className="modal-dialog-1"
      maxWidth="sm"
      onClose={handleCancel}
    >
      <DialogTitle className="dialog-title">
        <span>{title}</span>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Box sx={{ fontSize: "16px" }}>{dialogContent}</Box>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          onClick={handleCancel}
          className="btn-orange btn-dark"
          sx={{ marginRight: "8px" }}
        >
          Cancel
        </Button>
        <Button onClick={handleSaveComment} className="btn-orange">
          Update
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CommentBeforeUpdateDialog;
