import React, { useCallback, useEffect, useState } from "react";
import {
  Autocomplete,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  Grid,
  MenuItem,
  <PERSON><PERSON>,
  <PERSON>Field,
  Typography,
} from "@mui/material";
import QueryBuilder from "../QueryBuilder/Index";
import iconClose from "../../assets/svgs/icon-remove-orange.svg";
import { derivedColumnSchema } from "../../schemas";
import { useResourceContext } from "../../contexts/ResourceContext";
import { CheckCircleRounded } from "@mui/icons-material";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useToast } from "../../services/utils";
import * as Yup from "yup";
import { fields } from "../QueryBuilder/fields";
import QueryBuilderComponent from "../QueryBuilder/SQL/QueryBuilder.component";
import { customOperators } from "../../services/constants/Operators";
import {
  defaultOperators,
  defaultValidator,
  formatQuery,
} from "react-querybuilder";
import {
  extractColumnNamesFromQuery,
  manageAvailableColumns,
} from "../../services/utils";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
interface Rule {
  field: string;
  operator: string;
  value: string | number | boolean;
}
interface Query {
  combinator: string;
  rules: Rule[];
}
const initialQuery: Query = { combinator: "and", rules: [] };

const DerivedColumnDialog = ({ setAllVariablesList }: any) => {
  const {
    openDerivedColumnDialog,
    handleCloseDerivedColumnDialog,
    derivedColumnData,
    setDerivedColumnData,
    derivedModalType,
    setDerivedModalType,
    onSaveDerivedColumn,
    fileData,
    selectedRowId,
    pageContext,
    resourceColumnFileData,
  } = useResourceContext();

  const {
    availColumns,
    setAvailColumns,
    availColumnsWithResourceDetail,
    setAvailColumnsWithResourceDetail,
    globalVariables,
    setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    // tempLocalVariables,
    // setTempLocalVariables,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
  } = useRuleResourceContext();

  const getCurrentData = () => {
    return pageContext === "resourceColumn" ? resourceColumnFileData : fileData;
  };
  const currentData = getCurrentData();
  const { showToast } = useToast();
  const tempDerivedData = currentData?.find(
    (file: any) => file.id === selectedRowId
  );
  //const [tempQuery, setTempQuery] = useState("");
  const [tempName, setTempName] = useState("");
  const [requiredColumns, setRequiredColumns] = useState<any[]>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [existingColumn, setExistingColumn] = useState<boolean>(false);
  const [checkExistingValue, setCheckExistingValue] = useState("");
  const myCustomOperators = [...defaultOperators, ...customOperators];
  const [query, setQuery] = useState<Query>(initialQuery);

  // useEffect(() => {
  //   setTempGlobalVariables(globalVariables);
  // }, [globalVariables]);

  const handleChangeDerivedColumn = (qry: any, columns: any) => {
    setDerivedColumnData((prevData: any) => ({
      ...prevData,
      query: qry.replace(";", "").trim(),
      column_name: tempName,
      columns: requiredColumns,
    }));
  };
  useEffect(() => {
    setCheckExistingValue(tempDerivedData?.column_name);
  }, [tempDerivedData]);

  useEffect(() => {
    if (tempDerivedData && derivedModalType === "edit") {
      setTempName(tempDerivedData?.column_name);
      setQueryBuilderTempValue(
        tempDerivedData?.derived_column_definition?.sql_expression
      );
      setRequiredColumns(
        tempDerivedData?.derived_column_definition?.required_columns ?? []
      );
      setDerivedColumnData({
        query: queryBuilderTempValue,
        column_name: tempName,
        columns: requiredColumns,
      });
    } else if (derivedModalType === "add") {
      setTempName("");
      setQueryBuilderTempValue("");
      setRequiredColumns([]);
    }
  }, [derivedModalType]);

  const handleColumnBlur = () => {
    if (checkExistingValue?.toLowerCase() === tempName?.toLowerCase()) {
      return;
    }
    const checkDuplicate =
      currentData.length > 0 &&
      currentData.some(
        (item: any) => item.column_name.toLowerCase() === tempName.toLowerCase()
      );
    setExistingColumn(checkDuplicate);
  };

  useEffect(() => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      duplicateColumn: existingColumn ? "column name is duplicate" : "",
    }));
  }, [existingColumn]);

  const handleAddColumn = (e: any) => {
    setErrors((prevErrors) => ({ ...prevErrors, duplicateColumn: "" }));
    setDerivedColumnData({
      ...derivedColumnData,
      [e.target.name]: e.target.value,
    });
    setTempName(e.target.value);
    validateField(e.target.name, e.target.value);
  };

  const handleSaveDerivedColumn = async () => {
    const usedColumn = extractColumnNamesFromQuery(derivedColumnData?.query);
    const isValid = usedColumn.every((columnName: any) =>
      availColumns.includes(columnName)
    );
    if (!isValid) {
      showToast(
        "The values enclosed in square brackets are reserved; only the available variables within the square brackets may be used.",
        "warning"
      );
      return;
    }
    if (
      existingColumn &&
      !(checkExistingValue?.toLowerCase() === tempName?.toLowerCase())
    ) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        duplicateColumn: "column name is duplicate",
      }));
      return;
    } else {
      try {
        const derivedSchema: any = {
          column_name: Yup.string().required("Please enter column name"),
          query: Yup.string().required("Please enter query"),
        };
        // setGlobalVariables(tempGlobalVariables);
        Object.keys(tempGlobalVariables).map((key: any) => {
          if (tempGlobalVariables[key] === "") {
            derivedSchema[key] = Yup.string().required(`Please enter ${key}`);
            setErrors((prevErrors) => ({
              ...prevErrors,
              [key]: `Please enter ${key}`,
            }));
          } else {
            return {
              name: key,
              value: tempGlobalVariables[key],
            };
          }
        });
        const derivedColumnSchema = Yup.object().shape(derivedSchema);
        await derivedColumnSchema.validate(derivedColumnData, {
          abortEarly: false,
        });
        let updatedColumns;
        if (!checkExistingValue?.toLowerCase()) {
          updatedColumns = manageAvailableColumns(
            availColumns,
            "",
            "add",
            derivedColumnData?.column_name
          );
        } else {
          updatedColumns = manageAvailableColumns(
            availColumns,
            checkExistingValue,
            "update",
            derivedColumnData?.column_name
          );
        }
        setGlobalVariables((prev: any) => ({
          ...Object.keys(tempGlobalVariables).reduce(
            (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
            prev
          ),
        }));
        setAllVariablesList((prev: any) => ({
          ...prev,
          resource: {
            ...prev.resource,
            ...tempGlobalVariables,
          },
          resourceColumn: {
            ...prev.resourceColumn,
            ...tempGlobalVariables,
          },
        }));
        setTempGlobalVariables({});
        setAvailColumns(updatedColumns);
        setAvailColumnsWithResourceDetail(null);
        setQueryBuilderTempValue("");
        onSaveDerivedColumn();
      } catch (validationErrors: any) {
        const newErrors: { [key: string]: string } = {};
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            newErrors[error.path] = error.message;
          }
        );
        setErrors(newErrors);
      }
    }
    setDerivedModalType("");
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...derivedColumnData, [name]: value };
      await derivedColumnSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const onCloseDerivedColumnDialog = () => {
    // setTempGlobalVariables(globalVariables);
    setTempGlobalVariables({});
    //setTempLocalVariables({}); // removed because its getting removed linked service local variables
    handleCloseDerivedColumnDialog();
    setDerivedModalType("");
    setQueryBuilderTempValue("");
    setErrors({});
  };
  const onSelectResourcesColumn = (columns: any) => {
    const requiredColumns = columns.map((col: any) => col.column_name);
    setRequiredColumns(requiredColumns ?? []);
    setDerivedColumnData({
      ...derivedColumnData,
      columns: requiredColumns,
    });
  };
  const renderRequiredColumns = useCallback((props: any) => {
    return (
      <Popper
        {...props}
        anchorEl={document.getElementById("autocomplete-required-columns")}
      />
    );
  }, []);
  const handleQueryChange = (query: Query) => {
    setQuery(query);
  };

  return (
    <Dialog
      fullWidth
      maxWidth="lg"
      open={openDerivedColumnDialog}
      onClose={onCloseDerivedColumnDialog}
      className="main-dailog"
    >
      <Box sx={{ padding: 3 }}>
        <Box>
          <Grid container className="dailog-header">
            <label className="label-text">Derived Column</label>
            <div
              className="close-icon"
              onClick={onCloseDerivedColumnDialog}
            ></div>
          </Grid>
          <Box className="dailog-body pb-24">
            <Grid container rowSpacing={2.5} columnSpacing={5.25}>
              <Grid item xs={12} sm={12} md={12} lg={12} xl={12} className="">
                <Grid item>
                  <label className="label-text">
                    Column Name<span className="required-asterisk">*</span>
                  </label>
                </Grid>
                <Grid item xs>
                  <TextField
                    fullWidth
                    type="text"
                    // placeholder="Ex: Sample Column Name"
                    name="column_name"
                    className={`form-control ${
                      errors?.column_name || errors?.duplicateColumn
                        ? "has-error"
                        : ""
                    }`}
                    onChange={(e) => handleAddColumn(e)}
                    onBlur={handleColumnBlur}
                    sx={{ width: "100%" }}
                    value={tempName}
                    error={!!errors?.column_name || !!errors?.duplicateColumn}
                    helperText={
                      errors?.column_name || errors?.duplicateColumn || ""
                    }
                  />
                </Grid>
              </Grid>
              {/* Add Query builder component */}
              <QueryBuilder
                handleChange={handleChangeDerivedColumn}
                //tempQuery={queryBuilderTempValue}
                isMarginToTop={true}
                error={errors}
                setErrors={setErrors}
                isDerivedStyles={true}
              />

              {/* <Grid item xs={12} sm={12} md={12} lg={12} xl={12} className="">
                <Grid item>
                  <label className="label-text">Required Columns</label>
                </Grid>
                <Grid item xs>
                  <Autocomplete
                    fullWidth={true}
                    id={"autocomplete-required-columns"}
                    PopperComponent={renderRequiredColumns}
                    className="form-control-autocomplete derived-autocomplete"
                    multiple
                    options={
                      currentData
                        ? currentData.filter(
                            (option: { is_derived: any }) => !option.is_derived
                          )
                        : []
                    }
                    getOptionLabel={(option: { column_name: string }) =>
                      option.column_name ?? " "
                    }
                    disableCloseOnSelect
                    onChange={(key: any, value: any) =>
                      onSelectResourcesColumn(value)
                    }
                    renderInput={(params) => (
                      <>
                        <div className="autocomplete-chips-direction">
                          <TextField
                            {...params}
                            variant="outlined"
                            placeholder="Select..."
                            InputLabelProps={{ shrink: true }}
                          />
                        </div>
                      </>
                    )}
                    renderTags={(value, getTagProps) => {
                      return (
                        <>
                          <div className="vertical-scroll">
                            {value.map((option, index) => (
                              <Chip
                                {...getTagProps({ index })}
                                key={index}
                                label={option.column_name}
                                className="derived-chips"
                              />
                            ))}
                          </div>
                        </>
                      );
                    }}
                    renderOption={(props, option, { selected }) => (
                      <MenuItem
                        {...props}
                        key={option.column_name}
                        value={option.column_name}
                        sx={{ justifyContent: "space-between" }}
                      >
                        {option.column_name}
                        {selected ? <CheckCircleRounded color="info" /> : null}
                      </MenuItem>
                    )}
                    value={
                      requiredColumns && currentData
                        ? currentData.filter(
                            (option: { column_name: string }) =>
                              requiredColumns.includes(option.column_name)
                          )
                        : []
                    }
                  />
                </Grid>
                <Box className="sql-query-box">
                  <Typography className="sub-title">
                    Filter results using SQL query
                  </Typography>
                   
                  <QueryBuilderComponent
                    fields={fields}
                    query={query}
                    handleQueryChange={handleQueryChange}
                    defaultValidator={defaultValidator}
                    newOperators={myCustomOperators}
                  />
                </Box> 
              </Grid>*/}
            </Grid>
          </Box>
        </Box>
        <DialogActions className="dailog-footer">
          <Button
            color="secondary"
            variant="contained"
            onClick={handleSaveDerivedColumn}
            className="btn-orange"
          >
            <SaveOutlinedIcon /> &nbsp; Save
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default DerivedColumnDialog;
