import {
  Box,
  Button,
  Grid,
  Autocomplete,
  TextField,
  InputAdornment,
  Checkbox,
  InputBase,
  IconButton,
} from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { DataGridPro } from "@mui/x-data-grid-pro";

import SearchIcon from "@mui/icons-material/Search";

const TagsDialog = ({
  setShowDialog,
  selectedTags,
  setTagColResult,
  tagColResult,
  setPossibleMergeColumns,
  possibleMergeColumns,
}: any) => {
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (selectedTags && tagColResult) {
      const matchedTag = tagColResult.find(
        (tag: { id: any; key: any }) =>
          tag.id === selectedTags.id && tag.key === selectedTags.key
      );

      // Set the selectedRows with the values from the matched tag
      if (matchedTag) {
        setSelectedRows(matchedTag.values);
      }
    }
  }, [selectedTags, tagColResult]);

  const updatePossibleMergeColumns = (
    updatedTagColResult: any,
    possibleMergeColumns: any
  ) => {
    // Iterate through updatedTagColResult
    updatedTagColResult.forEach((tag: any) => {
      // Find corresponding entry in possibleMergeColumns based on 'rId'
      let matchingEntry = possibleMergeColumns.find(
        (entry: any) => entry.rId === tag.id
      );

      // If matching entry is found, update its 'columns' array
      if (matchingEntry) {
        matchingEntry.columns.forEach((column: any) => {
          if (column.key === tag.key) {
            // Add the values to the column's array (assuming it's unique)
            column[tag.key] = tag.values;
            // Array.from(
            //   new Set([...column[tag.key], ...tag.values])
            // );
          }
        });
      }
    });

    return possibleMergeColumns;
  };

  const handleSubmit = async () => {
    const updatedTagColResult = tagColResult.map((tag: any) => {
      if (tag.key === selectedTags.key && tag.id === selectedTags.id) {
        return {
          ...tag,
          values: Array.from(new Set([...selectedRows])),
        };
      }
      return tag;
    });
    const keyExists = tagColResult.some(
      (tag: any) => tag.key === selectedTags.key && tag.id === selectedTags.id
    );
    if (!keyExists) {
      updatedTagColResult.push({
        key: selectedTags.key,
        values: selectedRows,
        id: selectedTags.id,
      });
    }
    setTagColResult(updatedTagColResult);

    const updatedPossibleMergeColumns = updatePossibleMergeColumns(
      updatedTagColResult,
      possibleMergeColumns
    );

    setPossibleMergeColumns(updatedPossibleMergeColumns);

    handleCloseDialog();
  };

  const handleCloseDialog = () => {
    setShowDialog(false);
    setSelectedRows([]);
  };
  const reorderSelectedRows = (selectedRows: any, updatedRows: any) => {
    return selectedRows.sort((a: any, b: any) => {
      return updatedRows.indexOf(a) - updatedRows.indexOf(b);
    });
  };

  const handleRowChange = (params: any) => {
    const { row, targetIndex, oldIndex } = params;

    // Find the index of the row in the selectedRows array
    const selectedIndex = filteredData.indexOf(row);

    // If the row is not found in selectedRows, do nothing
    if (selectedIndex === -1) return;

    const updatedRows = [...filteredData];
    const [removed] = updatedRows.splice(selectedIndex, 1);
    updatedRows.splice(targetIndex, 0, removed);
    const orderedSelectedRows = reorderSelectedRows(selectedRows, updatedRows);
    setSelectedRows(orderedSelectedRows);
  };
  const handleCheckboxChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    id: any
  ) => {
    const isChecked = event.target.checked;
    if (isChecked) {
      setSelectedRows((prev) => {
        const updated = [...prev, id];
        return selectedTags.values.filter((row: any) => updated.includes(row));
      });
    } else {
      setSelectedRows((prev) => {
        const updated = prev.filter((row) => row !== id);
        return selectedTags.values.filter((row: any) => updated.includes(row));
      });
    }
  };

  const filteredData = useMemo(() => {
    return selectedTags?.values?.filter((row: any) => {
      return row.toLowerCase().includes(searchQuery?.toLowerCase());
    });
  }, [selectedTags?.values, searchQuery]);

  return (
    <div className="">
      <Box>
        <Grid container rowSpacing={3} columnSpacing={3}>
          <Grid item xs={12}>
            <div className="autocomplete-wrapper">
              <InputBase
                placeholder="Search..."
                className="form-control-autocomplete search-box"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value || "")}
                onKeyDown={(event) => {
                  if (event.key === "Enter") {
                    event.preventDefault();
                  }
                }}
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton>
                      <SearchIcon />
                    </IconButton>
                  </InputAdornment>
                }
              />
            </div>
          </Grid>
        </Grid>

        <Grid
          container
          rowSpacing={3}
          columnSpacing={3}
          sx={{ marginBottom: 3 }}
        >
          <Grid item xs={12} sx={{ minHeight: 200 }}>
            {/* <Typography variant="h6">Selected Columns</Typography> */}
            <DataGridPro
              sx={{
                ".MuiDataGrid-virtualScrollerContent": {
                  maxHeight: "200px",
                  overflowY: "auto",
                },
                "& .Mui-checked": {
                  color: "var(--dark-blue) !important",
                },
              }}
              className="table-draggable"
              columns={[
                {
                  field: "__check__",
                  minWidth: 32,
                  maxWidth: 32,
                  renderCell: (params) => (
                    <Checkbox
                      checked={selectedRows.includes(params.id)}
                      onChange={(event) =>
                        handleCheckboxChange(event, params.id)
                      }
                    />
                  ),
                },
                {
                  field: "name",
                  headerName: "Name",
                  flex: 1,
                  valueGetter: (params) => params.row,
                },
              ]}
              slots={{
                columnHeaders: () => null,
              }}
              //rows={selectedRows || []}
              rows={filteredData || []}
              checkboxSelection
              onRowOrderChange={handleRowChange}
              getRowId={(row) => row}
              onRowSelectionModelChange={(params) => {
                setSelectedRows(params);
              }}
              rowReordering
              hideFooter
            />
          </Grid>
        </Grid>
        <Grid container rowSpacing={3} columnSpacing={3}>
          <Grid item xs={6} columnGap={1} sx={{ display: "flex" }}>
            <Button
              color="secondary"
              variant="contained"
              className="btn-orange"
              onClick={handleSubmit}
            >
              Add
            </Button>
            <Button
              color="secondary"
              variant="contained"
              className="btn-orange btn-border"
              onClick={handleCloseDialog}
            >
              Cancel
            </Button>
          </Grid>
          <Grid
            item
            xs={6}
            sx={{ display: "flex", justifyContent: "flex-end" }}
          >
            <Button
              color="secondary"
              variant="contained"
              className="btn-orange btn-border"
              onClick={() => setSelectedRows([])}
            >
              Clear All
            </Button>
          </Grid>
        </Grid>
      </Box>
    </div>
  );
};

export default TagsDialog;
