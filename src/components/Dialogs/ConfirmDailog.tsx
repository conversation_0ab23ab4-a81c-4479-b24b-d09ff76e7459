import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@mui/material";
import React from "react";

interface ConfirmDailogProps {
  dailogTitle?: string;
  dailogDescription?: string;
  isDailogOpen: any;
  handleCancelAction: () => void;
  handleConfirmAction: () => void;
}

const ConfirmDailog: React.FC<ConfirmDailogProps> = ({
  isDailogOpen,
  handleCancelAction,
  handleConfirmAction,
  dailogTitle,
  dailogDescription,
}) => {
  return (
    <Dialog
      open={isDailogOpen}
      onClose={handleCancelAction}
      className="modal-dialog-1"
    >
      <DialogTitle className="dialog-title">
        <span>{dailogTitle}</span>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <DialogContentText sx={{ fontSize: "16px" }}>
          {dailogDescription}
        </DialogContentText>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          onClick={handleCancelAction}
          className="btn-orange btn-dark"
          sx={{ marginRight: "8px" }}
        >
          Cancel
        </Button>
        <Button onClick={handleConfirmAction} className="btn-orange">
          Continue
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDailog;
