import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  Grid,
  TextField,
} from "@mui/material";
import React, { useState } from "react";
import { cloneResourceColumnSchema } from "../../schemas";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
interface CloneResourceColumnDialogProps {
  cloneResourceColumnDialog: boolean;
  handleCloseCloneResourceColumn: any;
  cloneResourceColumnData: any;
  setCloneResourceColumnData: React.Dispatch<React.SetStateAction<any>>;
  onSaveCloneResourceColumn: any;
}
const CloneResourceColumnDialog = ({
  cloneResourceColumnDialog,
  handleCloseCloneResourceColumn,
  cloneResourceColumnData,
  setCloneResourceColumnData,
  onSaveCloneResourceColumn,
}: CloneResourceColumnDialogProps) => {
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const validateField = async (name: any, value: any) => {
    try {
      const partialReferenceData = {
        ...cloneResourceColumnData,
        [name]: value,
      };
      await cloneResourceColumnSchema.validateAt(name, partialReferenceData);
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      await cloneResourceColumnSchema.validate(cloneResourceColumnData, {
        abortEarly: false,
      });
      onSaveCloneResourceColumn();
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors(newErrors);
    }
  };
  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };

  const handleCloseDialog = () => {
    setErrors({});
    handleCloseCloneResourceColumn();
  };
  const handleChangeCloneResource = (name: any, value: any) => {
    setCloneResourceColumnData({
      ...cloneResourceColumnData,
      [name]: value,
    });
    validateField(name, value);
  };

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={cloneResourceColumnDialog}
      onClose={(event, reason) => handleClose(reason)}
      className="modal-dialog-1"
    >
      <DialogTitle className="dialog-title">
        <span>Clone Resource Column</span>
        <button className="close-icon" onClick={handleCloseDialog}></button>
      </DialogTitle>
      <DialogContent>
        <Box>
          <Grid container rowSpacing={3} columnSpacing={3}>
            <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
              <TextField
                type="text"
                name="name"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                fullWidth
                variant="outlined"
                label={
                  <span>
                    Resource Column Details Name
                    <span className="required-asterisk">*</span>
                  </span>
                }
                className={`form-control-autocomplete ${
                  errors?.name ? "has-error" : ""
                }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.name}
                helperText={errors?.name || ""}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
              <TextField
                type="text"
                name="code"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                fullWidth
                variant="outlined"
                label={
                  <span>
                    Code
                    <span className="required-asterisk">*</span>
                  </span>
                }
                className={`form-control-autocomplete ${
                  errors?.code ? "has-error" : ""
                }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.code}
                helperText={errors?.code || ""}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          type="button"
          color="secondary"
          variant="contained"
          onClick={handleSubmit}
          className="btn-orange"
        >
          <SaveOutlinedIcon /> &nbsp; Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CloneResourceColumnDialog;
