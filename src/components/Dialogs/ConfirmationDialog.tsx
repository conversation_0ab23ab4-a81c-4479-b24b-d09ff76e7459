import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
interface ConfirmationDialogProps {
  title: string;
  dialogContent: React.ReactNode;
  handleCancel: React.Dispatch<React.SetStateAction<any>>;
  openConfirmation: boolean;
  handleConfirm: React.Dispatch<React.SetStateAction<any>>;
  isShowClassMb0?: boolean;
}
const ConfirmationDialog = ({
  title,
  dialogContent,
  handleCancel,
  openConfirmation,
  handleConfirm,
  isShowClassMb0 = true,
}: ConfirmationDialogProps) => {
  return (
    <Dialog
      open={openConfirmation}
      onClose={handleCancel}
      className="modal-dialog-1"
    >
      <DialogTitle className={`dialog-title ${isShowClassMb0 ? "mb-0" : ""}`}>
        <span>{title}</span>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <DialogContentText sx={{ fontSize: "16px" }}>
          {dialogContent}
        </DialogContentText>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          onClick={handleCancel}
          className="btn-orange btn-dark"
          sx={{ marginRight: "8px" }}
        >
          Cancel
        </Button>
        <Button onClick={handleConfirm} className="btn-orange">
          Continue
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationDialog;
