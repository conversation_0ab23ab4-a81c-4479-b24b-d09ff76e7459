import { Dialog, Box, DialogActions, Button, Grid } from "@mui/material";
import { useEffect, useState } from "react";
import QueryBuilder from "../MultiQueryBuilder/Index";
import { useResourceContext } from "../../contexts/ResourceContext";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import * as Yup from "yup";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";

interface ValidationDialogProps {
  globalVariables?: any;
  setGlobalVariables?: any;
  setAllVariablesList?: any;
}
const ValidationDialog = ({ setAllVariablesList }: ValidationDialogProps) => {
  const {
    openValidationDialog,
    handleCloseValidationDialog,
    setValidationData,
    validationData,
    onSaveCustomValidation,
    isDailogEdit,
    dailogEditIndex,
    fileData,
    selectedRowId,
    pageContext,
    resourceColumnFileData,
  } = useResourceContext();

  const {
    globalVariables,
    setGlobalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    // tempLocalVariables,
    // setTempLocalVariables,
    setQueryBuilderTempValue,
  } = useRuleResourceContext();

  // const [modifiedTempQuery, setModifiedTempQuery] = useState({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // useEffect(() => {
  //   console.log("called>>", globalVariables);
  //   setTempGlobalVariables((prev: any) => ({
  //     ...prev,
  //     ...globalVariables,
  //   }));
  // }, []);

  const getCurrentData = () => {
    return pageContext === "resourceColumn" ? resourceColumnFileData : fileData;
  };
  const currentData = getCurrentData();
  const tempQuery = currentData?.find(
    (file: any) => file.id === selectedRowId
  )?.custom_validations;

  useEffect(() => {
    if (tempQuery) {
      if (isDailogEdit) {
        setQueryBuilderTempValue(tempQuery[dailogEditIndex]);
        setValidationData(tempQuery[dailogEditIndex]);
      } else {
        setQueryBuilderTempValue("");
        setValidationData({
          name: "",
          filter_rule: "",
          expression: "",
        });
      }
      setErrors({});
    }
  }, [tempQuery]);
  const handleChangeValidation = (qry: any) => {
    if (Object.keys(qry).length !== 0) {
      setValidationData({
        name: qry.name,
        expression: qry.query,
        filter_rule: qry.filter,
      });
    }
  };
  const handleSaveCustomValidation = async () => {
    try {
      const customValSchema: any = {
        name: Yup.string().required("Please enter name"),
        expression: Yup.string()
          .required("Please enter query")
          .test(
            "contains-only-spaces",
            "Query cannot contain only spaces",
            (value) => {
              if (value === "") {
                return true;
              } else {
                const containsOnlySpaces = !/\S/.test(value); // Check for only spaces
                return !containsOnlySpaces;
              }
            }
          ),
      };
      // setGlobalVariables(tempGlobalVariables);

      Object.keys(tempGlobalVariables).map((key: any) => {
        if (tempGlobalVariables[key] === "") {
          customValSchema[key] = Yup.string().required(`Please enter ${key}`);
          setErrors((prevErrors) => ({
            ...prevErrors,
            [key]: `Please enter ${key}`,
          }));
        } else {
          return {
            name: key,
            value: tempGlobalVariables[key],
          };
        }
      });
      const derivedColumnSchema = Yup.object().shape(customValSchema);
      const updatedFormData = {
        ...validationData,
        name: validationData.name || "",
        expression: validationData.expression || "",
        filter_rule: validationData.filter || "",
      };
      await derivedColumnSchema.validate(updatedFormData, {
        abortEarly: false,
      });
      setGlobalVariables((prev: any) => ({
        ...Object.keys(tempGlobalVariables).reduce(
          (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
          prev
        ),
      }));
      setAllVariablesList((prev: any) => ({
        ...prev,
        resource: {
          ...prev.resource,
          ...tempGlobalVariables,
        },
        resourceColumn: {
          ...prev.resourceColumn,
          ...tempGlobalVariables,
        },
      }));
      setQueryBuilderTempValue("");
      setTempGlobalVariables({});
      onSaveCustomValidation();
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors && validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            newErrors[error.path] = error.message;
          }
        );
      }
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const onCloseValidationDialog = () => {
    handleCloseValidationDialog();
    setErrors({});
    setTempGlobalVariables({});
    setQueryBuilderTempValue("");
    //setTempLocalVariables({}); // removed because its getting removed linked service local variables
  };
  return (
    <Dialog
      fullWidth
      maxWidth="lg"
      open={openValidationDialog}
      onClose={onCloseValidationDialog}
      className="main-dailog"
    >
      <Box sx={{ padding: 3 }}>
        <Grid container className="dailog-header">
          <label className="label-text pt-13">Add Validation</label>
          <div className="close-icon" onClick={onCloseValidationDialog}></div>
        </Grid>
        <Box className="dailog-body">
          <Box>
            <QueryBuilder
              handleChange={handleChangeValidation}
              //tempQuery={queryBuilderTempValue}
              errors={errors}
              setErrors={setErrors}
              isDailogEdit={isDailogEdit}
            />
          </Box>
        </Box>
        <DialogActions className="dailog-footer">
          <Button
            color="secondary"
            variant="contained"
            onClick={handleSaveCustomValidation}
            className="btn-orange"
          >
            <SaveOutlinedIcon /> &nbsp; Save
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default ValidationDialog;
