import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  Select,
  Typography,
  Grid,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { toleranceSchema, toleranceWithFallbackSchema } from "../../schemas";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useRuleContext } from "../../contexts/RuleContext";

const ToleranceDialog = ({
  openToleranceDialog,
  handleCloseToleranceDialog,

  onSaveToleranceDialog,
}: any) => {
  //ruleContext imports
  const { toleranceData, setToleranceData } = useRuleContext();
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateField = async (name: any, value: any) => {
    try {
      const partialToleranceData = { ...toleranceData, [name]: value };
      await toleranceSchema.validateAt(name, partialToleranceData);
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      await toleranceSchema.validate(toleranceData, { abortEarly: false });
      onSaveToleranceDialog();
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors(newErrors);
    }
  };
  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };

  const handleCloseDialog = () => {
    setErrors({
      source_type: "",
    });
    handleCloseToleranceDialog();
  };
  const handleChangeReference = (name: any, value: any) => {
    const correctedValue = value === "" ? null : value;
    setToleranceData({
      ...toleranceData,
      [name]: correctedValue,
    });
    validateField(name, correctedValue);
  };
  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={openToleranceDialog}
      onClose={(event, reason) => handleClose(reason)}
      className="modal-dialog-1"
    >
      <DialogTitle className="dialog-title">
        <span>Tolerance Data</span>
        <button className="close-icon" onClick={handleCloseDialog}></button>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Box>
          <Grid container rowSpacing={3} columnSpacing={3}>
            <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
              <label className="label-text">
                Tolerance Type<span className="required-asterisk">*</span>
              </label>
              <Select
                defaultValue="percentage"
                value={toleranceData.tolerance_type}
                name="tolerance_type"
                className="form-control-1 tolrence-data-box"
                onChange={(e) =>
                  handleChangeReference(e.target.name, e.target.value)
                }
              >
                <MenuItem value="percentage">Percentage</MenuItem>
                <MenuItem value="absolute">Absolute</MenuItem>
              </Select>
            </Grid>
            <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
              <label className="label-text">
                Tolerance Value<span className="required-asterisk">*</span>
              </label>
              <TextField
                type="number"
                name="tolerance_value"
                onChange={(e) =>
                  handleChangeReference(e.target.name, e.target.value)
                }
                value={toleranceData?.tolerance_value}
                className={`form-control ${
                  errors?.tolerance_value ? "has-error" : ""
                }`}
                error={!!errors?.tolerance_value}
                helperText={errors?.tolerance_value || ""}
              />
            </Grid>
            {toleranceData.tolerance_type === "percentage" && (
              <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
                <label className="label-text">Fallback Value</label>
                <TextField
                  type="number"
                  name="fallback_value"
                  onChange={(e) =>
                    handleChangeReference(e.target.name, e.target.value)
                  }
                  value={toleranceData?.fallback_value}
                  className={`form-control`}
                />
              </Grid>
            )}
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          type="button"
          color="secondary"
          variant="contained"
          onClick={handleSubmit}
          className="btn-orange"
        >
          <SaveOutlinedIcon /> &nbsp; Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ToleranceDialog;
