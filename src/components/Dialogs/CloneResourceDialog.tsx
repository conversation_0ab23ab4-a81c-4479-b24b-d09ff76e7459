import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  Grid,
  TextField,
} from "@mui/material";
import React, { useState } from "react";
import { cloneResourceSchema } from "../../schemas";
import { useResourceContext } from "../../contexts/ResourceContext";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
const CloneResourceDialog = () => {
  const {
    cloneResourceDialog,
    handleCloseCloneResourceDialog,
    cloneResourceData,
    setCloneResourceData,
    onSaveCloneResource,
  } = useResourceContext();
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const validateField = async (name: any, value: any) => {
    try {
      const partialReferenceData = { ...cloneResourceData, [name]: value };
      await cloneResourceSchema.validateAt(name, partialReferenceData);
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      await cloneResourceSchema.validate(cloneResourceData, {
        abortEarly: false,
      });
      onSaveCloneResource();
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors(newErrors);
    }
  };
  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };

  const handleCloseDialog = () => {
    setErrors({});
    handleCloseCloneResourceDialog();
  };
  const handleChangeCloneResource = (name: any, value: any) => {
    setCloneResourceData({
      ...cloneResourceData,
      [name]: value,
    });
    validateField(name, value);
  };

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={cloneResourceDialog}
      onClose={(event, reason) => handleClose(reason)}
      className="modal-dialog-1"
    >
      <DialogTitle className="dialog-title">
        <span>Clone Resource</span>
        <button className="close-icon" onClick={handleCloseDialog}></button>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Box>
          <Grid container rowSpacing={3} columnSpacing={3}>
            <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
              <TextField
                type="text"
                name="resource_name"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                fullWidth
                variant="outlined"
                label={
                  <span>
                    Resource name<span className="required-asterisk">*</span>
                  </span>
                }
                className={`form-control-autocomplete ${
                  errors?.resource_name ? "has-error" : ""
                }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.resource_name}
                helperText={errors?.resource_name || ""}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
              <TextField
                type="text"
                name="resource_type"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                label={
                  <span>
                    System<span className="required-asterisk">*</span>
                  </span>
                }
                className={`form-control-autocomplete ${
                  errors?.resource_type ? "has-error" : ""
                }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.resource_type}
                helperText={errors?.resource_type || ""}
              />
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={6}
              lg={6}
              xl={6}
              sx={{ display: "flex", alignItems: "flex-start" }}
            >
              <TextField
                type="text"
                name="resource_prefix"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                label={
                  <span>
                    Resource Prefix<span className="required-asterisk">*</span>
                  </span>
                }
                className={`form-control-autocomplete ${
                  errors?.resource_prefix ? "has-error" : ""
                }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.resource_prefix}
                helperText={errors?.resource_prefix || ""}
              />
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={6}
              lg={6}
              xl={6}
              sx={{ display: "flex", alignItems: "flex-start" }}
            >
              <TextField
                type="text"
                name="code"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                label={
                  <span>
                    Code<span className="required-asterisk">*</span>
                  </span>
                }
                className={`form-control-autocomplete ${
                  errors?.code ? "has-error" : ""
                }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.code}
                helperText={errors?.code || ""}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          type="button"
          color="secondary"
          variant="contained"
          onClick={handleSubmit}
          className="btn-orange"
        >
          <SaveOutlinedIcon /> &nbsp; Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CloneResourceDialog;
