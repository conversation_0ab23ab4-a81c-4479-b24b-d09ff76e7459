import { Dialog, Box, DialogActions, Grid, Button } from "@mui/material";
import { diff as DiffEditor } from "react-ace";
import "ace-builds/src-noconflict/mode-json";
import "ace-builds/src-noconflict/theme-github";
import "ace-builds/src-min-noconflict/ext-language_tools";
import "ace-builds/src-min-noconflict/ext-searchbox";
import { useEffect, useState } from "react";

interface AuditDialogProps {
  openDialog: boolean;
  value: string[];
  handleDialogClose?: () => void;
}
function countJsonLines(json: any) {
  try {
    const formattedJson = JSON.stringify(JSON.parse(json), null, 2);
    return formattedJson.split("\n").length;
  } catch (error) {
    console.error("Invalid JSON provided");
    return 0;
  }
}
function getJsonWithMoreLines(json1: any, json2: any) {
  const linesInJson1 = countJsonLines(json1);
  const linesInJson2 = countJsonLines(json2);

  if (linesInJson1 >= linesInJson2) {
    return { json: json1, lines: linesInJson1 };
  } else {
    return { json: json2, lines: linesInJson2 };
  }
}

const DiffAceEditorDialog = ({
  openDialog,
  value,
  handleDialogClose,
}: AuditDialogProps) => {
  const diffValue =
    Array.isArray(value) && value.length === 2
      ? [JSON.stringify(value[0], null, 2), JSON.stringify(value[1], null, 2)]
      : ["{}", "{}"];
  const [isVisible, setIsVisible] = useState(false);
  const [editorHeight, setEditorHeight] = useState<number | null>(null);
  useEffect(() => {
    if (openDialog) {
      const calculatedResult = getJsonWithMoreLines(diffValue[0], diffValue[1]);
      const calculatedHeight = calculateHeight(calculatedResult.json);
      setEditorHeight(calculatedHeight);
      setIsVisible(true);
    }
  }, [openDialog, value]);
  const calculateHeight = (jsonString: string) => {
    const lines = jsonString.split("\n");
    const lineHeight = 24;
    const maxWidth = 600;
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    if (!context) {
      return lines.length * lineHeight + 50;
    }
    context.font = "16px Inter";
    let totalLines = 0;
    lines.forEach((line) => {
      const width = context.measureText(line).width;
      const wrappedLines = Math.ceil(width / maxWidth);
      totalLines += wrappedLines;
    });
    return totalLines * lineHeight + 50;
  };
  if (editorHeight === null) {
    return null;
  }
  return (
    <Dialog
      fullWidth
      maxWidth="xl"
      open={openDialog}
      onClose={() => handleDialogClose && handleDialogClose()}
      className="main-dailog main-dailog-audit"
    >
      <Box sx={{ padding: 3, visibility: isVisible ? "visible" : "hidden" }}>
        <Grid
          container
          className="dailog-header"
          justifyContent="space-between"
        >
          <label className="label-text pt-13">Compare History</label>
          <div
            className="close-icon"
            onClick={() => handleDialogClose && handleDialogClose()}
          ></div>
        </Grid>
        <Box
          className="dailog-body"
          sx={{ height: "330px", overflowY: "auto" }}
        >
          <DiffEditor
            value={diffValue}
            mode="json"
            theme="github"
            enableBasicAutocompletion
            enableLiveAutocompletion
            highlightActiveLine
            showGutter
            showPrintMargin
            wrapEnabled
            readOnly
            width="100%"
            height={`${editorHeight}px`}
            fontSize={18}
            setOptions={{
              useWorker: false,
              enableSnippets: true,
              showLineNumbers: true,
              tabSize: 2,
            }}
          />
        </Box>
        <DialogActions className="dailog-footer">
          <Button
            onClick={() => handleDialogClose && handleDialogClose()}
            className="btn-orange"
          >
            Close
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default DiffAceEditorDialog;
