import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

import Zoom from "@mui/material/Zoom";
import { ZoomProps } from "@mui/material/Zoom";
import {
  Button,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogActions,
  Box,
} from "@mui/material";

import { useIdleTimer } from "react-idle-timer";
import { useAuth } from "../../contexts/AuthContext";
import IdleTimeIcon from "../../assets/clock.svg";

const IdleTimerDialog = () => {
  const navigate = useNavigate();

  const [openConfirmation, setOpenConfirmation] = useState(false);

  const handleOnIdle = () => {
    // localStorage.removeItem("token");
    localStorage.setItem("lastVisitedPath", window.location.pathname);
    setOpenConfirmation(true);
  };

  const { setIsAuthenticated } = useAuth();

  const { reset, pause, resume } = useIdleTimer({
    timeout: process.env.REACT_APP_TIMEOUT_DURATION
      ? parseInt(process.env.REACT_APP_TIMEOUT_DURATION)
      : undefined,
    onIdle: handleOnIdle,
    debounce: 100,
  });

  useEffect(() => {
    reset();
  }, [reset]);

  const handleIdleDailog = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("notifications");
    navigate("/login");
    setIsAuthenticated(false);
    setOpenConfirmation(false);
  };

  const Transition = React.forwardRef(function Transition(
    props: ZoomProps & { children?: React.ReactElement },
    ref: React.Ref<unknown>
  ) {
    return <Zoom timeout={500} ref={ref} {...props} />;
  });
  return (
    <>
      <Dialog
        TransitionComponent={Transition}
        open={openConfirmation}
        onClose={handleIdleDailog}
        className="idle-window-dialog"
      >
        <DialogContent className="idle-window">
          <Box sx={{ fontSize: "16px" }}>
            <img src={IdleTimeIcon} alt="" className="warning-icon" />
            <br />
            <p>Your session has expired</p>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleIdleDailog} className="btn-orange btn-dark">
            Login Again
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default IdleTimerDialog;
