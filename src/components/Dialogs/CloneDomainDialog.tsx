import {
  <PERSON>,
  <PERSON>ton,
  Dialog,
  <PERSON>alogA<PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  Typography,
  Grid,
  TextField,
} from "@mui/material";
import React, { useState } from "react";
import { cloneDomainSchema } from "../../schemas";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";

interface CloneDomainDialogProps {
  cloneDomainDialog: boolean;
  handleCloseCloneDomain: any;
  cloneDomainData: any;
  setCloneDomainData: React.Dispatch<React.SetStateAction<any>>;
  onSaveCloneDomain: any;
}
const CloneDomainDialog = ({
  cloneDomainDialog,
  handleCloseCloneDomain,
  cloneDomainData,
  setCloneDomainData,
  onSaveCloneDomain,
}: CloneDomainDialogProps) => {
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const validateField = async (name: any, value: any) => {
    try {
      const partialReferenceData = { ...cloneDomainData, [name]: value };
      await cloneDomainSchema.validateAt(name, partialReferenceData);
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      await cloneDomainSchema.validate(cloneDomainData, { abortEarly: false });
      onSaveCloneDomain();
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors(newErrors);
    }
  };
  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };

  const handleCloseDialog = () => {
    setErrors({});
    handleCloseCloneDomain();
  };
  const handleChangeCloneResource = (name: any, value: any) => {
    setCloneDomainData({
      ...cloneDomainData,
      [name]: value,
    });
    validateField(name, value);
  };

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={cloneDomainDialog}
      onClose={(event, reason) => handleClose(reason)}
      className="modal-dialog-1"
    >
      <DialogTitle className="dialog-title" component="span">
        <span>Clone Domain</span>
        <button className="close-icon" onClick={handleCloseDialog}></button>
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Box>
          <Grid container rowSpacing={3} columnSpacing={3}>
            <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
              <TextField
                type="text"
                // placeholder="Ex: Sample Domain name"
                name="domain_name"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                fullWidth
                variant="outlined"
                label={
                  <span>
                    Domain name<span className="required-asterisk">*</span>
                  </span>
                }
                className={`form-control-autocomplete ${errors?.domain_name ? "has-error" : ""
                  }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.domain_name}
                helperText={errors?.domain_name || " "}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6} lg={4} xl={4}>
              <TextField
                type="text"
                // placeholder="Ex: Sample Domain Code"
                name="domain_code"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                label={
                  <span>
                    Domain Code<span className="required-asterisk">*</span>
                  </span>
                }
                className={`form-control-autocomplete ${errors?.domain_code ? "has-error" : ""
                  }`}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!errors?.domain_code}
                helperText={errors?.domain_code || ""}
              />
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={6}
              lg={4}
              xl={4}
              sx={{ display: "flex", alignItems: "flex-start" }}
            >
              <TextField
                type="text"
                // placeholder="Ex: Sample Domain Description"
                name="domain_desc"
                onChange={(e) =>
                  handleChangeCloneResource(e.target.name, e.target.value)
                }
                label={<span>Domain Description</span>}
                className={`form-control-autocomplete`}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions className="dialog-footer">
        <Button
          type="button"
          color="secondary"
          variant="contained"
          onClick={handleSubmit}
          className="btn-orange"
        >
          <SaveOutlinedIcon /> &nbsp; Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CloneDomainDialog;
