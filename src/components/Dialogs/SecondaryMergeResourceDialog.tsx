import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  TextField,
  Autocomplete,
  MenuItem,
  Chip,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { secondaryMergeResourceSchema } from "../../schemas";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { CheckCircleRounded } from "@mui/icons-material";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import {
  processResourcesData,
  sortResources,
} from "../../services/utils/processRuleExecutionResourcesData";
import Loader from "../Molecules/Loader/Loader";
import { useToast } from "../../services/utils";

interface ISecondaryMergeResourceDialog {
  secondaryMergeResourceDialog: boolean;
  handleSecondaryMergeResourceDialog: any;
  resourceDataWithSecMerge: any;
  setResourceDataWithSecMerge: any;
  onSaveSecondaryMergeResource: any;
  fetchedResourcesData: any;
  setIsLoading: any;
  isEditSecondaryResource: boolean;
  mergeColumnsLength: number;
  fetchedConnectionKeys: any;
  secondaryResourceData: any;
  setSecondaryResourceData: React.Dispatch<React.SetStateAction<any>>;
  setResourcesData: any;
  resourcesData: any;
  setOriginalResourcesData?: React.Dispatch<React.SetStateAction<any>>;
}
const SecondaryMergeResourceDialog = ({
  secondaryMergeResourceDialog,
  handleSecondaryMergeResourceDialog,
  resourceDataWithSecMerge,
  setResourceDataWithSecMerge,
  onSaveSecondaryMergeResource,
  fetchedResourcesData,
  // setIsLoading,
  isEditSecondaryResource,
  mergeColumnsLength,
  fetchedConnectionKeys,
  secondaryResourceData,
  setSecondaryResourceData,
  setResourcesData,
  resourcesData,
  setOriginalResourcesData,
}: ISecondaryMergeResourceDialog) => {
  const { showToast } = useToast();
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [resourceColumnId, setResourceColumnId] = useState(null);
  const [previousSecResourceId, setPreviousSecResourceId] = useState(null);
  const [resourceColumn, setResourceColumn] = useState<any>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [resourceColumnData] = useFetchResourceColumnsById({
    resourceColumnId,
    setIsLoading,
  });

  useEffect(() => {
    if (resourceColumnData) {
      setResourceColumn(resourceColumnData);
    }
  }, [resourceColumnData]);

  useEffect(() => {
    if (isEditSecondaryResource && resourceDataWithSecMerge) {
      setResourceColumnId(
        resourceDataWithSecMerge?.additional_properties
          ?.resource_column_details_id
      );
    }
  }, [resourceDataWithSecMerge, isEditSecondaryResource]);

  const validateField = async (name: any, value: any) => {
    try {
      const partialReferenceData = {
        ...resourceDataWithSecMerge?.secondary_merge_resource
          ?.secondary_merge_resource,
        [name]: value,
      };
      await secondaryMergeResourceSchema.validateAt(name, partialReferenceData);
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      let UpdatedResourcesData: any = resourcesData;
      // Validate the secondary merge resource data
      await secondaryMergeResourceSchema.validate(
        resourceDataWithSecMerge?.secondary_merge_resource
          ?.secondary_merge_resource,
        { abortEarly: false }
      );

      // Find the relevant resource data
      const resourceData: any = fetchedResourcesData?.find(
        (option: any) =>
          option.id ===
          resourceDataWithSecMerge?.secondary_merge_resource
            ?.secondary_merge_resource?.resource_id
      );

      if (!resourceData) {
        throw new Error("Resource data not found.");
      }

      if (
        resourceDataWithSecMerge?.secondary_merge_resource
          ?.secondary_merge_resource?.merge_on?.length !== mergeColumnsLength
      ) {
        showToast(
          `The number of merge columns should be same for the respective secondary merge resource`,
          "error"
        );
        return;
      }

      // If editing and the resource ID hasn't changed, save and exit early
      if (
        isEditSecondaryResource &&
        previousSecResourceId === resourceData?.id
      ) {
        setResourceColumn([]);
        onSaveSecondaryMergeResource();
        return;
      }

      // Prepare the updated resource
      const updatedResource = {
        ...resourceData,
        isSecondaryResource: true,
        parentResId:
          resourceDataWithSecMerge?.secondary_merge_resource?.parentResId,
      };

      const secResource: any[] = [
        ...secondaryResourceData,
        resourceDataWithSecMerge?.secondary_merge_resource,
      ];

      // Handle resource data updates for edit case
      if (isEditSecondaryResource) {
        // Update the resource data
        UpdatedResourcesData = resourcesData
          .map((resource: { id: any; parentResId: any }) => {
            // Filter out matching resources based on ID and parentResId
            if (
              resource?.id === previousSecResourceId &&
              resource?.parentResId ===
                resourceDataWithSecMerge?.secondary_merge_resource?.parentResId
            ) {
              return null; // Mark for removal
            }
            return resource;
          })
          .filter(Boolean);
      }

      UpdatedResourcesData = UpdatedResourcesData.map(
        (resource: { id: any }) => {
          if (
            resource?.id ===
            resourceDataWithSecMerge?.secondary_merge_resource?.parentResId
          ) {
            return {
              ...resource,
              secondary_merge_resource:
                resourceDataWithSecMerge?.secondary_merge_resource,
            };
          }
          return resource;
        }
      );

      // Process the updated resource and combine it with existing data
      const secResourceData = await processResourcesData({
        allRepeatedResources: updatedResource,
        fetchedConnectionKeys,
        secondaryResourceData: secResource,
      });

      const allResourcesData = [...UpdatedResourcesData, ...secResourceData];
      const allSortedResourceData = await sortResources(allResourcesData);

      // Update the state with sorted resources
      setResourcesData(allSortedResourceData);
      setOriginalResourcesData &&
        setOriginalResourcesData(allSortedResourceData);
      setSecondaryResourceData(secResource);

      setResourceColumn([]);
      setResourceColumnId(null);

      // Save the secondary merge resource
      onSaveSecondaryMergeResource();
    } catch (error: any) {
      console.error("Error during handleSubmit:", error);

      // Handle validation errors
      if (error?.inner) {
        const newErrors: { [key: string]: string } = {};
        error.inner.forEach(
          ({ path, message }: { path: string; message: string }) => {
            newErrors[path] = message;
          }
        );
        setErrors(newErrors);
      }
    }
  };

  const handleClose = (reason: string) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") return;
  };

  const handleCloseDialog = () => {
    setErrors({});
    setResourceColumn([]);
    handleSecondaryMergeResourceDialog();
  };

  return (
    <>
      <Loader isLoading={isLoading} zIndex={999999} />
      <Dialog
        fullWidth
        maxWidth="md"
        open={secondaryMergeResourceDialog}
        onClose={(event, reason) => handleClose(reason)}
        className="modal-dialog-1"
      >
        <DialogTitle className="dialog-title">
          <span>{`${
            isEditSecondaryResource ? "Edit" : "Add"
          } Secondary Merge Resources`}</span>
          <button className="close-icon" onClick={handleCloseDialog}></button>
        </DialogTitle>
        <DialogContent className="dialog-content">
          <Grid item xs={11}>
            <Grid container rowSpacing={1.5} columnSpacing={2.5}>
              <Grid item xs={6}>
                <Box
                  className="label-text"
                  sx={{ paddingTop: "10px", paddingRight: "16px" }}
                >
                  Select Secondary Merge Resource
                </Box>
                <Autocomplete
                  fullWidth
                  className={`form-control-autocomplete  ${
                    errors?.[`resource_id`] ? "has-error" : ""
                  }`}
                  options={fetchedResourcesData || []}
                  getOptionLabel={(option: any) => option?.resource_name || ""}
                  onChange={(event, value) => {
                    setResourceColumnId(
                      value?.additional_properties?.resource_column_details_id
                    );
                    if (isEditSecondaryResource) {
                      setPreviousSecResourceId(
                        resourceDataWithSecMerge?.secondary_merge_resource
                          ?.secondaryResId
                      );
                      setResourceDataWithSecMerge((prev: any) => ({
                        ...prev,
                        secondary_merge_resource: {
                          ...prev?.secondary_merge_resource,
                          secondaryResId: value?.id,
                          secondary_merge_resource: {
                            resource_id: value?.id,
                            resource_code: value?.code,
                            merge_on: [],
                          },
                        },
                      }));
                    } else {
                      setResourceDataWithSecMerge((prev: any) => ({
                        ...prev,
                        secondary_merge_resource: {
                          parentResId: prev?.id,
                          secondaryResId: value?.id,
                          secondary_merge_resource: {
                            resource_id: value?.id,
                            resource_code: value?.code,
                            merge_on: [],
                          },
                        },
                      }));
                    }
                    validateField("resource_id", value?.id);
                  }}
                  renderInput={(params) => (
                    <div className="autocomplete-chips-direction">
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder="Select..."
                        InputLabelProps={{
                          shrink: true,
                        }}
                        error={Boolean(!!errors?.[`resource_id`])}
                        helperText={errors?.[`resource_id`] ?? ""}
                      />
                    </div>
                  )}
                  value={
                    fetchedResourcesData?.find((option: any) => {
                      return (
                        option.id ===
                        resourceDataWithSecMerge?.secondary_merge_resource
                          ?.secondary_merge_resource?.resource_id
                      );
                    }) || null
                  }
                  renderOption={(props, option, state) => (
                    <li
                      {...props}
                      key={
                        option?.id || `${option?.resource_name}-${state.index}`
                      }
                    >
                      {option?.resource_name}
                    </li>
                  )}
                  classes={{
                    endAdornment: "no-button-bg",
                  }}
                />
              </Grid>
              <Grid item xs={6}>
                <Box
                  className="label-text"
                  sx={{ paddingTop: "10px", paddingRight: "16px" }}
                >
                  Select Resource Column for Secondary Merge Resource
                </Box>
                <Autocomplete
                  fullWidth={true}
                  className={`form-control-autocomplete derived-autocomplete ${
                    errors?.[`merge_on`] ? "has-error" : ""
                  }`}
                  multiple
                  options={
                    (resourceColumn?.resource_column_properties
                      ?.resource_columns?.length > 0 &&
                      resourceColumn?.resource_column_properties?.resource_columns
                        .filter((item: any) => item.column_name !== "file_name")
                        .map((item: any) => item.column_name)) ||
                    []
                  }
                  getOptionLabel={(option) => option}
                  disableCloseOnSelect
                  onChange={(event, value) => {
                    setResourceDataWithSecMerge((prev: any) => ({
                      ...prev,
                      secondary_merge_resource: {
                        ...prev?.secondary_merge_resource,
                        secondary_merge_resource: {
                          ...prev?.secondary_merge_resource
                            ?.secondary_merge_resource,
                          merge_on: value,
                        },
                      },
                    }));
                    validateField("merge_on", value);
                  }}
                  value={
                    resourceDataWithSecMerge?.secondary_merge_resource
                      ?.secondary_merge_resource?.merge_on
                  }
                  renderInput={(params: any) => (
                    <>
                      <div className="autocomplete-chips-direction">
                        <TextField
                          {...params}
                          variant="outlined"
                          placeholder="Select..."
                          InputLabelProps={{
                            shrink: true,
                          }}
                          error={Boolean(!!errors?.[`merge_on`])}
                          helperText={errors?.[`merge_on`] ?? ""}
                        />
                      </div>
                    </>
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        {...getTagProps({ index })}
                        label={option}
                        className="chips"
                        color="primary"
                      />
                    ))
                  }
                  renderOption={(
                    props: any,
                    option: any,
                    { selected }: any
                  ) => (
                    <MenuItem
                      {...props}
                      key={option}
                      value={option}
                      sx={{ justifyContent: "space-between" }}
                    >
                      {option}
                      {selected ? <CheckCircleRounded color="info" /> : null}
                    </MenuItem>
                  )}
                  classes={{
                    endAdornment: "no-button-bg",
                  }}
                />
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions className="dialog-footer">
          <Button
            type="button"
            color="secondary"
            variant="contained"
            onClick={handleSubmit}
            className="btn-orange"
          >
            <SaveOutlinedIcon /> &nbsp; Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SecondaryMergeResourceDialog;
