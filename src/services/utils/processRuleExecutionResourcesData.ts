import { getResourceDetail } from "../../services/resourcesService";
import { sqlDatabaseType } from "../../services/constants";


const findConnectionKeyDetail = (
    item: any,
    fetchedConnectionKeys: any[]
) => {
    return fetchedConnectionKeys?.find(
        (option) =>
            option.id ===
            item?.additional_properties?.resource_definition?.api_definition
                ?.connection_key
    );
};

const getUpdatedResourceDefinition = (
    item: any
) => {
    let updatedResourceDefinition =
        item?.additional_properties?.resource_definition?.[
        `${item?.additional_properties?.resource_definition?.type}_definition`
        ];

    if (
        sqlDatabaseType.includes(
            item?.additional_properties?.resource_definition?.type
        )
    ) {
        updatedResourceDefinition =
            item?.additional_properties?.resource_definition?.sql_definition;
    }

    return updatedResourceDefinition;
};

const updateResourceData = (
    item: any,
    connectionKeyDetail: any
) => {
    const updatedResourceDefinition = getUpdatedResourceDefinition(
        item
    );

    return {
        ...item,
        additional_properties: {
            ...item.additional_properties,
            resource_definition: {
                ...item.additional_properties.resource_definition,
                ...updatedResourceDefinition,
                api_definition: {
                    ...item?.additional_properties?.resource_definition?.api_definition,
                    url: connectionKeyDetail?.api_url,
                    request_timeout: 0,
                },
            },
        },
    };
};

export const addSecondaryResourceDetails = (
    resource: any,
    secondaryResourceData: any[]
) => {
    const parentMatch = secondaryResourceData?.find(
        (secondaryItem) => secondaryItem?.parentResId === resource?.id
    );
    const secondaryMatch = secondaryResourceData?.find(
        (secondaryItem) => secondaryItem?.secondaryResId === resource?.id && secondaryItem?.parentResId === resource?.parentResId
    );

    let updatedResource = { ...resource };

    if (parentMatch) {
        updatedResource = {
            ...updatedResource,
            secondary_merge_resource: parentMatch,
        };
    }

    if (secondaryMatch) {
        updatedResource = {
            ...updatedResource,
            isSecondaryResource: true,
            parentResId: secondaryMatch?.parentResId,
        };
    }

    return updatedResource;
};

const fetchBaseResourceDetails = async (
    resource: any
) => {
    if (resource?.aggregation_type === "aggregated") {
        const baseResourceId =
            resource?.additional_properties?.aggregation_properties
                ?.base_resource_id;
        return await getResourceDetail({ currentResourceId: baseResourceId });
    }
    return null;
};

const addAggregationDetails = (
    resource: any,
    matchingBaseResource: any
) => {
    if (matchingBaseResource) {
        const updatedBaseResource = updateResourceData(
            matchingBaseResource,
            null
        );
        return {
            ...resource,
            aggregation_base_resource_data: updatedBaseResource,
        };
    }

    return resource;
};

export const sortResources = (resources: any[]) => {
    const sortedResources: any[] = [];
    const addedKeys = new Set();
    resources.forEach((resource) => {
        const key = `${resource.id}_${resource.parentResId || 'root'}`;
        if (!addedKeys.has(key)) {
            sortedResources.push(resource);
            addedKeys.add(key);

            const secondaryResources = resources.filter(
                (secondaryResource) => secondaryResource?.parentResId === resource.id
            );

            secondaryResources.forEach((secondaryResource) => {
                const secondaryKey = `${secondaryResource.id}_${secondaryResource.parentResId}`;
                if (!addedKeys.has(secondaryKey)) {
                    sortedResources.push(secondaryResource);
                    addedKeys.add(secondaryKey);
                }
            });
        }
    });

    return sortedResources;
};

export const processResourcesData = async ({
    allRepeatedResources,
    fetchedConnectionKeys,
    secondaryResourceData,
}: {
    allRepeatedResources: any | any[];
    fetchedConnectionKeys: any[];
    secondaryResourceData: any[];
}): Promise<any[]> => {
    try {
        // Normalize allRepeatedResources into an array
        const normalizedResources = Array.isArray(allRepeatedResources)
            ? allRepeatedResources
            : [allRepeatedResources];

        if (!normalizedResources || normalizedResources.length === 0) {
            return [];
        }

        // Step 1: Update all resources with additional details
        const updatedData = normalizedResources.map((item) => {
            const connectionKeyDetail = findConnectionKeyDetail(
                item,
                fetchedConnectionKeys
            );

            let updatedResourceData = item;

            if (
                item?.linked_service_id &&
                item?.additional_properties?.resource_definition?.type
            ) {
                updatedResourceData = updateResourceData(
                    item,
                    connectionKeyDetail
                );
            }

            return addSecondaryResourceDetails(updatedResourceData, secondaryResourceData);
        });

        // Step 2: Fetch base resources for aggregated resources
        const baseResources = await Promise.all(
            normalizedResources.map((resource) =>
                fetchBaseResourceDetails(resource)
            )
        );

        // Step 3: Add aggregation details to resources
        const finalData = updatedData.map((resource) => {
            if (
                resource?.aggregation_type === "aggregated" &&
                resource?.additional_properties?.aggregation_properties
                    ?.base_resource_id
            ) {
                const baseResourceId =
                    resource?.additional_properties?.aggregation_properties
                        ?.base_resource_id;
                const matchingBaseResource = baseResources.find(
                    (br) => br && br.id === baseResourceId
                );

                return addAggregationDetails(resource, matchingBaseResource);
            }
            return resource;
        });

        // Step 4: Sort and organize resources
        return sortResources(finalData);
    } catch (error) {
        console.error("Error processing resources data:", error);
        return [];
    }
};

export const getDifferentValues = (obj1: any, obj2: any): any => {
    // Handle null/undefined cases
    if (!obj1) return null;
    if (!obj2) return obj1;

    // Handle arrays
    if (Array.isArray(obj1)) {
      if (!Array.isArray(obj2) || obj1.length !== obj2.length) {
        return obj1;
      }
      // Compare array elements recursively
      const differentArray: any[] = obj1.map((item: any, index: number) =>
        getDifferentValues(item, obj2[index])
      );
      // Only return the array if it has any non-null elements
      return differentArray.some((item) => item !== null)
        ? differentArray:null;
    }

    // Handle objects
    if (typeof obj1 === "object") {
      const result: Record<string, any> = {};
      let hasDifferences = false;

      // Include resource_id only if it's from a resource
      if ("resource_id" in obj1) {
        result.resource_id = obj1.resource_id;
        hasDifferences = true;
      }

      // Compare each key in obj1 with obj2
      Object.keys(obj1).forEach((key) => {
        // Skip resource_id as it's already handled
        if (key === "resource_id") return;

        // If key doesn't exist in obj2, include the entire value
        if (!(key in obj2)) {
          result[key] = obj1[key];
          hasDifferences = true;
        } else {
          // For boolean values, compare directly
          if (
            typeof obj1[key] === "boolean" ||
            typeof obj2[key] === "boolean"
          ) {
            if (obj1[key] !== obj2[key]) {
              result[key] = obj1[key];
              hasDifferences = true;
            }
          } else {
            // Recursively compare other values
            const diffValue = getDifferentValues(obj1[key], obj2[key]);
            if (diffValue !== null) {
              result[key] = diffValue;
              hasDifferences = true;
            }
          }
        }
      });

      // Return the object if it has any differences
      // For rules (not from resource), we don't require resource_id to be present
      return hasDifferences ? result : null;
    }

    // Handle primitive values
    return obj1 !== obj2 ? obj1 : null;
  };
