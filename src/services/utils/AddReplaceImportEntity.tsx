import {
  addResource,
  addResourceColumns,
  updateResource,
  updateResourceColumn,
} from "../../services/resourcesService";
import { toast } from "react-toastify";
import { addDomain, updateDomain } from "../../services/domainsService";
import {
  addConnectionKey,
  updateConnectionKey,
} from "../../services/connectionKeysService";
import {
  addLinkedService,
  updateLinkedService,
} from "../../services/linkedService";
import {
  addFileProcessing,
  updateFileProcessing,
} from "../../services/fileProcessingService";
import { addRunInstance, updateRunInstance } from "../../services/runInstance";
import { IConnectionKeyResponse } from "../../types/ConnectionKey";
import { IDomainResponse } from "../../types/domain";
import { IResourceResponse } from "../../types/resource";
import { ILinkedServiceResponse } from "../../types/LinkedServices";
import { IResourceColumnDetailsResponse } from "../../types/ResourceColumnDetails";
import { IRuleResponse } from "../../types/rules";
import { createRule, updateRule } from "../rulesService";
import { associatedData } from "../constants/ImportJSON";

interface AssociatedEntities {
  linked_services?: ILinkedServiceResponse[] | null;
  connection_keys?: IConnectionKeyResponse[] | null;
  file_processing_attributes?: any[] | null;
  domains?: IDomainResponse[] | null;
  resource?: IResourceResponse[] | null;
  resources?: IResourceResponse[] | null;
  resource_column_details?: IResourceColumnDetailsResponse[] | null;
}

/**
 * Return steps status
 * @param status
 * @returns
 */
export const getStepStatus = (status: any) => {
  if (status === "success") return "success";
  if (status === "failed") return "failed";
  return "not_proceed";
};

/**
 * Add or update Import items according to action type
 * @param entity
 * @param actionType
 * @param entityType
 */

export const onAddReplaceEntity = (
  entity: any,
  actionType: string,
  entityType: string,
  assosiateEntity: AssociatedEntities,
  showToast: any
): any => {
  // assosiateEntity = associatedData;
  let updateData: any = {};
  switch (entityType) {
    case "domain":
    case "domains":
      if (actionType === "add") {
        return addDomain(entity)
          .then((response) => {
            if (response) {
              showToast(
                `Domain ${entity?.domain_code} created successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      } else if (actionType === "replace") {
        return updateDomain({
          currentDomainId: entity?.details?.entity_id_in_target_env,
          payload: entity,
        })
          .then((response) => {
            if (response) {
              showToast(
                `Domain ${entity?.domain_code} updated successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      }
      break;
    case "connection_key":
    case "connection_keys":
      if (actionType === "add") {
        return addConnectionKey({
          payload: entity,
        })
          .then((response) => {
            if (response) {
              showToast(
                `Connection Key ${entity?.code} created successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      } else if (actionType === "replace") {
        return updateConnectionKey({
          currentConnectionKeyId: entity?.details?.entity_id_in_target_env,
          payload: entity,
        })
          .then((response) => {
            if (response) {
              showToast(
                `Connection Key ${entity?.code} updated successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      }
      break;
    case "linked_service":
    case "linked_services":
      if (assosiateEntity && Object.keys(assosiateEntity).length > 0) {
        updateData = updateLinkedServiceConnectionKeys(entity, assosiateEntity);
      } else {
        updateData = entity;
      }
      if (actionType === "add") {
        return addLinkedService({
          currentLinkedServiceId: updateData?.details?.entity_id_in_target_env,
          payload: updateData,
        })
          .then((response) => {
            if (response) {
              showToast(
                `Linked Service ${entity?.code} created successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      } else if (actionType === "replace") {
        return updateLinkedService({
          currentLinkedServiceId: entity?.details?.entity_id_in_target_env,
          payload: entity,
        })
          .then((response) => {
            if (response) {
              showToast(
                `Linked Service ${entity?.code} updated successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      }
      break;
    case "file_processing_attributes":
      if (actionType === "add") {
        return addFileProcessing({
          currentFileProcessingId: entity?.details?.entity_id_in_target_env,
          payload: entity,
        })
          .then((response) => {
            if (response) {
              showToast(
                `File Processing Attribute ${entity?.code} created successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      } else if (actionType === "replace") {
        return updateFileProcessing({
          currentFileProcessingId: entity?.details?.entity_id_in_target_env,
          payload: entity,
        })
          .then((response) => {
            if (response) {
              showToast(
                `File Processing Attribute ${entity?.code} updated successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      }
      break;
    case "run_instance":
    case "run_instances":
      if (actionType === "add") {
        return addRunInstance({
          currentRunInstanceId: entity?.details?.entity_id_in_target_env,
          payload: entity,
        })
          .then((response) => {
            if (response) {
              showToast(
                `Run Instance ${entity?.code} created successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      } else if (actionType === "replace") {
        return updateRunInstance({
          currentRunInstanceId: entity?.details?.entity_id_in_target_env,
          payload: entity,
        })
          .then((response) => {
            if (response) {
              showToast(
                `Run Instance ${entity?.code} updated successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      }
      break;
    case "resource_column_detail":
    case "resource_column_details":
    case "resource column details":
      if (assosiateEntity && Object.keys(assosiateEntity).length > 0) {
        updateData = updateResourceColumnDetails(entity, assosiateEntity);
      } else {
        updateData = entity;
      }
      if (actionType === "add") {
        return addResourceColumns(updateData)
          .then((response) => {
            if (response) {
              showToast(
                `Resource Column Detail ${entity?.code} created successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      } else if (actionType === "replace") {
        return updateResourceColumn({
          resourceColumnId: updateData?.details?.entity_id_in_target_env,
          payload: updateData,
        })
          .then((response) => {
            if (response) {
              if (response) {
                showToast(
                  `Resource Column Detail ${entity?.code} updated successfully!`,
                  "success"
                );
                return response;
              }
            }
          })
          .catch((err) => {});
      }
      break;
    case "resource":
    case "resources":
      if (assosiateEntity && Object.keys(assosiateEntity).length > 0) {
        updateData = updateResourceEntities(entity, assosiateEntity);
      } else {
        updateData = entity;
      }
      if (actionType === "add") {
        return addResource(updateData)
          .then((response) => {
            if (response) {
              showToast(
                `Resource ${entity?.code} created successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error) => {});
      } else if (actionType === "replace") {
        return updateResource({
          currentResourceId: updateData?.details?.entity_id_in_target_env,
          payload: updateData,
        })
          .then((response: any) => {
            if (response) {
              showToast(
                `Resource ${entity?.code} updated successfully!`,
                "success"
              );
              return response;
            }
          })
          .catch((error: any) => {});
      }
      break;
    case "rule":
    case "rules":
      if (assosiateEntity && Object.keys(assosiateEntity).length > 0) {
        updateData = updateRuleWithAssociatedEntities(entity, assosiateEntity);
      } else {
        updateData = entity;
      }
      if (actionType === "add") {
        return createRule(updateData)
          .then((response) => {
            if (response) {
              showToast(`Rule ${entity?.code} create successfully`, "success");
              return response;
            }
          })
          .catch((err) => {
            // console.log("err", err);
          });
      } else if (actionType === "replace") {
        return updateRule({
          currentRuleId: updateData?.details?.entity_id_in_target_env,
          payload: updateData,
        })
          .then((response) => {
            if (response) {
              showToast(`Rule ${entity?.code} updated successfully`, "success");
              return response;
            }
          })
          .catch((err) => {
            // console.log("err", err);
          });
      }
      break;
    default:
      console.error(`Unknown item type: ${entityType}`);
  }
};

/**
 * Add Associate entites in linked services
 * @param associatedEntites
 * @param ConnectionKeysList
 *
 */

export const updateLinkedServiceConnectionKeys = (
  linkedService: ILinkedServiceResponse,
  associatedEntities: AssociatedEntities
): ILinkedServiceResponse => {
  const newConnectionKeys: any[] = [];
  linkedService?.connection_details?.connection_keys_details?.forEach(
    (keyDetail: { connection_key_code: string; connection_key_id: number }) => {
      const associatedKey = associatedEntities?.connection_keys?.find(
        (key) => key.code === keyDetail.connection_key_code
      );
      if (associatedKey) {
        keyDetail.connection_key_id =
          associatedKey.details.entity_id_in_target_env;
        newConnectionKeys.push(associatedKey.details.entity_id_in_target_env);
      } else {
        newConnectionKeys.push(keyDetail.connection_key_id);
      }
    }
  );
  linkedService.connection_details.connection_keys = newConnectionKeys;
  return linkedService;
};

export const updateResourceColumnDetails = (
  resource_column_details: IResourceColumnDetailsResponse,
  associated_entities: AssociatedEntities
): IResourceColumnDetailsResponse => {
  // Update domain id based on domain code match
  const domain = associated_entities?.domains?.find(
    (d) => d.domain_code === resource_column_details.domain_code
  );
  if (domain) {
    resource_column_details.domain_id = domain.details.entity_id_in_target_env;
  }

  // Function to find linked service entity id
  const getLinkedServiceId = (
    linked_service_code: string | null
  ): number | null => {
    if (!linked_service_code) return null;
    const linkedService = associated_entities?.linked_services?.find(
      (service) => service.code === linked_service_code
    );
    return linkedService ? linkedService.details.entity_id_in_target_env : null;
  };

  // Function to find connection key entity id
  const getConnectionKeyId = (
    connection_key_code: string | null
  ): number | null => {
    if (!connection_key_code) return null;
    const connectionKey = associated_entities?.connection_keys?.find(
      (key) => key.code === connection_key_code
    );
    return connectionKey ? connectionKey.details.entity_id_in_target_env : null;
  };

  // Update resource columns for external references
  resource_column_details.resource_column_properties.resource_columns.forEach(
    (column) => {
      if (
        column.constraints.is_reference &&
        column.constraints.reference_column_definition?.source === "external"
      ) {
        const linkedServiceId = getLinkedServiceId(
          column.constraints.reference_column_definition.linked_service_code
        );
        if (linkedServiceId !== null) {
          column.constraints.reference_column_definition.linked_service_id =
            linkedServiceId;
        }

        const connectionKeyId = getConnectionKeyId(
          column.constraints.reference_column_definition.connection_key_code
        );
        if (connectionKeyId !== null) {
          column.constraints.reference_column_definition.connection_key =
            connectionKeyId;
        }
      }
    }
  );

  return resource_column_details;
};

export const updateRuleWithAssociatedEntities = (
  rule: any,
  associated_entities: any
) => {
  // Helper function to find matching resource ID based on resource code
  const findMatchingResourceId = (
    resource_code: string,
    oldRId: number | string
  ) => {
    const resource = associated_entities.resources.find(
      (r: any) => r.code === resource_code
    );
    return resource ? resource.details.entity_id_in_target_env : oldRId;
  };

  // 1. Update domain_id in rule based on domain_code match
  const matchingDomain = associated_entities.domains.find(
    (domain: any) => domain.domain_code === rule.domain_code
  );
  if (matchingDomain) {
    rule.domain_id = matchingDomain.details.entity_id_in_target_env;
  }

  // 2. Update primary_dataset resource_id
  rule.rule_schema.merge_rule.primary_dataset.resource_id =
    findMatchingResourceId(
      rule.rule_schema.merge_rule.primary_dataset.resource_code,
      rule.rule_schema.merge_rule.primary_dataset.resource_id
    );

  // 3. Update secondary_datasets resource_id
  rule.rule_schema.merge_rule?.secondary_datasets.forEach((dataset: any) => {
    dataset.resource_id = findMatchingResourceId(
      dataset.resource_code,
      dataset.resource_id
    );
  });

  // 4. Update filter_rules resource_id
  rule.rule_schema?.filter_rules.forEach((filterRule: any) => {
    filterRule.resource_id = findMatchingResourceId(
      filterRule.resource_code,
      filterRule.resource_id
    );
  });

  // 5. Update custom_comparison_rules left_operand resource_id
  rule.rule_schema?.custom_comparison_rules.forEach((customRule: any) => {
    customRule.left_operand.resource_id = findMatchingResourceId(
      customRule.left_operand.resource_code,
      customRule.left_operand.resource_id
    );
  });

  // 6. Update custom_comparison_rules right_operand resource_id
  rule.rule_schema?.custom_comparison_rules.forEach((customRule: any) => {
    customRule.right_operand.resource_id = findMatchingResourceId(
      customRule.right_operand.resource_code,
      customRule.right_operand.resource_id
    );
  });

  // 7. Update custom_comparison_rules filter_rules resource_id
  rule.rule_schema?.custom_comparison_rules.forEach((customRule: any) => {
    customRule?.filter_rules.forEach((filterRule: any) => {
      filterRule.resource_id = findMatchingResourceId(
        filterRule.resource_code,
        filterRule.resource_id
      );
    });
  });

  // 8. Update resources array
  rule.rule_schema.resources = rule.rule_schema.resources.map(
    (resourceId: number) => {
      const resource = associated_entities.resources.find(
        (r: any) => r.id === resourceId
      );
      return resource ? resource.details.entity_id_in_target_env : resourceId;
    }
  );

  // 9. Update resources_detailed_list resource_id
  rule.rule_schema?.resources_detailed_list.forEach((resourceDetail: any) => {
    resourceDetail.resource_id = findMatchingResourceId(
      resourceDetail.resource_code,
      resourceDetail.resource_id
    );
  });

  return rule;
};

export const updateResourceEntities = (
  resource: IResourceResponse,
  associated_entities: AssociatedEntities
): IResourceResponse => {
  // 1. Update domain_id based on domain_code match
  const domain = associated_entities?.domains?.find(
    (d) => d.domain_code === resource.domain_code
  );
  if (domain) {
    resource.domain_id = domain.details.entity_id_in_target_env;
  }

  // 2. Update linked_service_id based on linked_service_code match
  const linkedService = associated_entities?.linked_services?.find(
    (ls) => ls.code === resource.linked_service_code
  );
  if (linkedService) {
    resource.linked_service_id = linkedService.details.entity_id_in_target_env;
  }

  // 3. Update connection_key_id based on resource_definition type
  const resourceDefinition = resource.additional_properties.resource_definition;
  const connectionKey = associated_entities.connection_keys?.find(
    (ck) => ck.code === getConnectionKeyCode(resourceDefinition)
  );

  if (connectionKey) {
    const newConnectionKey = connectionKey.details.entity_id_in_target_env;

    if (
      isSqlDefinition(resourceDefinition.type) &&
      resourceDefinition.sql_definition
    ) {
      resourceDefinition.sql_definition.connection_key = newConnectionKey;
    } else if (
      isApiDefinition(resourceDefinition.type) &&
      resourceDefinition.api_definition
    ) {
      resourceDefinition.api_definition.connection_key = newConnectionKey;
    } else if (
      resourceDefinition.type === "local_definition" &&
      resourceDefinition.local_definition
    ) {
      resourceDefinition.local_definition.connection_key = newConnectionKey;
    } else if (
      resourceDefinition.type === "blob" &&
      resourceDefinition.blob_definition
    ) {
      resourceDefinition.blob_definition.connection_key = newConnectionKey;
    } else if (
      resourceDefinition.type === "sftp" &&
      resourceDefinition.sftp_definition
    ) {
      resourceDefinition.sftp_definition.connection_key = newConnectionKey;
    }
  }

  // 4. Update resource_column_details_id based on resource_column_details_code match
  const resourceColumnDetails =
    associated_entities?.resource_column_details?.find(
      (rcd) =>
        rcd.code ===
        resource?.additional_properties?.resource_column_details_code
    );
  if (resourceColumnDetails) {
    resource.additional_properties.resource_column_details_id =
      resourceColumnDetails.details.entity_id_in_target_env;
  }

  // 5. Update additional_resource_data based on resource_code match
  resource?.additional_properties?.additional_resource_data?.forEach((ard) => {
    const associatedResource = associated_entities?.resources?.find(
      (res) => res.code === ard.resource_code
    );
    if (associatedResource) {
      ard.resource_id = associatedResource?.details?.entity_id_in_target_env;
    }
  });

  // 6. Update aggregation_properties resource data based on resource_code match

  const aggregatedResource = associated_entities?.resources?.find(
    (res) =>
      res.code ===
      resource?.additional_properties?.aggregation_properties
        ?.base_resource_code
  );
  if (aggregatedResource) {
    resource.additional_properties.aggregation_properties.base_resource_id =
      aggregatedResource?.details?.entity_id_in_target_env;
  }

  return resource;
};

// Helper functions to determine the type and get the connection_key_code
const getConnectionKeyCode = (resourceDefinition: any): string | undefined => {
  if (isSqlDefinition(resourceDefinition?.type)) {
    return resourceDefinition?.sql_definition?.connection_key_code;
  } else if (isApiDefinition(resourceDefinition?.type)) {
    return resourceDefinition?.api_definition?.connection_key_code;
  } else if (resourceDefinition?.type === "local_definition") {
    return resourceDefinition.local_definition?.connection_key_code;
  } else if (resourceDefinition?.type === "blob") {
    return resourceDefinition?.blob_definition?.connection_key_code;
  } else if (resourceDefinition?.type === "sftp") {
    return resourceDefinition?.sftp_definition?.connection_key_code;
  } else {
    return "";
  }
};

const isSqlDefinition = (type: string): boolean => {
  return ["mariadb", "azure_sql", "oracle_sql", "snowflake_sql"].includes(type);
};

const isApiDefinition = (type: string): boolean => {
  return [
    "no_auth_api",
    "basic_auth_api",
    "token_auth_api",
    "key_auth_api",
    "oauth_api",
  ].includes(type);
};
export const entity_name = [
  {
    entity: "domain",
    display_name: "domain_name",
  },
  {
    entity: "domains",
    display_name: "domain_name",
  },
  {
    entity: "resource",
    display_name: "resource_name",
  },
  {
    entity: "resources",
    display_name: "resource_name",
  },
  {
    entity: "resource column details",
    display_name: "name",
  },
  {
    entity: "resource_column_details",
    display_name: "name",
  },
  {
    entity: "rule",
    display_name: "name",
  },
  {
    entity: "connection_keys",
    display_name: "name",
  },
  {
    entity: "linked_services",
    display_name: "name",
  },
  {
    entity: "linked_services",
    display_name: "name",
  },
];
export const handleNavigateToEntity = (entity: any, entityType: string) => {
  console.log(entityType);
  let url = "";
  switch (entityType) {
    case "domains":
    case "domain":
      url = `/domain/view/${entity?.details.entity_id_in_target_env}`;
      window.open(url, "_blank");
      break;
    case "resources":
    case "resource":
      url = `/resource/${entity?.domain_id}/view/${entity?.details.entity_id_in_target_env}`;
      window.open(url, "_blank");
      break;
    case "resource_column_details":
    case "resource column details":
      url = `/resource-columns/${entity?.domain_id}/view/${entity?.details.entity_id_in_target_env}`;
      window.open(url, "_blank");
      break;
    case "rule":
      url = `/rules/${entity?.domain_id}/view/${entity?.details.entity_id_in_target_env}`;
      window.open(url, "_blank");
      break;
    default:
      break;
  }
};
