/**
 * Compares two arrays of filter rule objects and returns only the objects that have changed.
 *
 * @param originalFilterRules - The original array of filter rule objects
 * @param updatedFilterRules - The updated array of filter rule objects
 * @returns An array containing only the filter rule objects that have changed
 */
export const compareFilterRules = (
  originalFilterRules: any[] | null | undefined,
  updatedFilterRules: any[] | null | undefined
): any[] => {
  // Handle null/undefined cases
  if (!updatedFilterRules) return [];
  if (!originalFilterRules) return updatedFilterRules;

  // Process the updated rules to only include those that have changed
  // and only include the essential fields
  const changedRules = updatedFilterRules.reduce((result: any[], updatedRule) => {
    // If the rule doesn't have an id, skip it (it's invalid)
    if (!updatedRule || updatedRule.id === undefined) return result;

    // Find the matching original rule by id
    const matchingOriginalRule = originalFilterRules.find(originalRule =>
      originalRule.id === updatedRule.id
    );

    // If no matching original rule found, include it (it's new)
    if (!matchingOriginalRule) {
      // Include only the essential fields
      result.push({
        id: updatedRule.id,
        name: updatedRule.name,
        sql_query: updatedRule.sql_query
      });
      return result;
    }

    // Compare name and sql_query fields
    const nameChanged = updatedRule.name !== matchingOriginalRule.name;
    const queryChanged = updatedRule.sql_query !== matchingOriginalRule.sql_query;

    // Include the rule if either name or sql_query has changed
    if (nameChanged || queryChanged) {
      // Include only the essential fields
      result.push({
        id: updatedRule.id,
        name: updatedRule.name,
        sql_query: updatedRule.sql_query
      });
    }

    return result;
  }, []);

  return changedRules;
};

/**
 * Compares two arrays of filter rule objects and returns only the objects that have changed,
 * preserving the resource_id field which is required for the API.
 *
 * @param originalFilterRules - The original array of filter rule objects
 * @param updatedFilterRules - The updated array of filter rule objects
 * @returns An array containing only the filter rule objects that have changed
 */
export const compareFilterRulesWithResourceId = (
  originalFilterRules: any[] | null | undefined,
  updatedFilterRules: any[] | null | undefined
): any[] => {
  // Handle null/undefined cases
  if (!updatedFilterRules) return [];
  if (!originalFilterRules) return updatedFilterRules;

  // Process the updated rules to only include those that have changed
  // and only include the essential fields
  const changedRules = updatedFilterRules.reduce((result: any[], updatedRule) => {
    // If the rule doesn't have a resource_id or name, skip it (it's invalid)
    if (!updatedRule || updatedRule.resource_id === undefined || !updatedRule.name) return result;

    // Find the matching original rule by resource_id AND name
    const matchingOriginalRule = originalFilterRules.find(originalRule =>
      originalRule.resource_id === updatedRule.resource_id &&
      originalRule.name === updatedRule.name
    );

    // If no matching original rule found, include it (it's new)
    if (!matchingOriginalRule) {
      // Include only the essential fields
      result.push({
        name: updatedRule.name,
        resource_id: updatedRule.resource_id,
        resource_code: updatedRule.resource_code,
        sql_query: updatedRule.sql_query
      });
      return result;
    }

    // Compare sql_query field
    const queryChanged = updatedRule.sql_query !== matchingOriginalRule.sql_query;

    // Include the rule if sql_query has changed
    if (queryChanged) {
      // Include only the essential fields
      result.push({
        name: updatedRule.name,
        resource_id: updatedRule.resource_id,
        resource_code: updatedRule.resource_code,
        sql_query: updatedRule.sql_query
      });
    }

    return result;
  }, []);

  return changedRules;
};
