import { getDifferentValues } from "./processRuleExecutionResourcesData";
import { getResourceReqBody } from "./processResearchQueryResourceData";

/**
 * Processes research query data for execution
 * @param differentResearchQueryData - The research query data
 * @param resourcesData - The resources data
 * @param originalResourcesData - The original resources data for comparison
 * @param resultStorageParameters - The result storage parameters
 * @param runParameters - The run parameters
 * @param inlineVariables - The global variables
 * @param executionName - The execution name
 * @returns The processed payload for research query execution
 */
export const processResearchQueryExecutionData = (
  researchQueryDetailData: any,
  resourcesData: any[],
  originalResourcesData: any[],
  resultStorageParameters: any,
  runParameters: any,
  inlineVariables: any,
  executionName: string
): any => {
  try {
    
    // Process resource data using getResourceReqBody
    let processedResourceData = resourcesData.map((resource: any) => {
      // Use the getResourceReqBody function to create a resource object that matches the schema
      let resourceObject: any = getResourceReqBody(resource);

      // Ensure the resource object is not null
      if (!resourceObject) {
        return null;
      }

      return resourceObject;
    }).filter(Boolean); // Remove null values

    // Process original resource data for comparison
    const processedOriginalResourceData = originalResourcesData.map((resource: any) => {
      // Use the getResourceReqBody function to create a resource object that matches the schema
      let resourceObject: any = getResourceReqBody(resource);

      // Ensure the resource object is not null
      if (!resourceObject) {
        return null;
      }

      return resourceObject;
    }).filter(Boolean); // Remove null values

    // Get only the different values between resourceData and originalResourceData
    if (processedOriginalResourceData.length > 0) {
      processedResourceData = getDifferentValues(processedResourceData, processedOriginalResourceData);
    }
    // Create base payload according to the schema
    const basePayload = {
      research_queries: researchQueryDetailData?.research_queries || [],
      resource_data: processedResourceData || [],
      output_storage_params: {
        output_storage_linked_service_id: resultStorageParameters?.linked_service_id || null,
        output_storage_linked_service_code: resultStorageParameters?.linked_service_code || null,
        output_storage_connection_key: resultStorageParameters?.connection_key_id || null,
        output_storage_base_file_path: resultStorageParameters?.file_path || null,
      },
      run_instance: {
        run_id: runParameters?.run_instance?.run_id || 1,
        run_name: runParameters?.run_instance?.run_name || "Legacy",
      },
      query_params: {
    no_of_records_in_response: runParameters?.no_of_records_in_response|| 0,
    no_of_records_in_output_files: runParameters?.no_of_records_in_output_files|| 0,
    no_of_records_in_filter_rule_output_files: runParameters?.no_of_records_in_filter_rule_output_files|| 0,
    generate_output_files: runParameters?.generate_output_files || true,
    pull_new_files_from_server: runParameters?.pull_new_files_from_server || true,
    pull_latest_files:runParameters?.pull_latest_files || false,
    keep_downloaded_files: runParameters?. keep_downloaded_files || false,
    save_input_data_files: runParameters?.save_input_data_files || false,
    report_name:  runParameters?.report_name || ""
  },
      inline_variables: inlineVariables || {},
      execution_name: executionName || "",
    };
    return basePayload;
  } catch (error) {
    console.error("Error processing research query execution data:", error);
    return null;
  }
};
