interface ApiRequest {
  user_name?: string | null;
  password?: string | null;
  bearer_token?: string | null;
  api_key?: string | null;
  oauth_client_id?: string | null;
  oauth_client_secret?: string | null;
  oauth_url?: string | null;
  method?: string;
  content_type?: string;
  body?: any;
  query_params?: any;
  url_params?: any;
  url?: string | null;
  request_timeout?: number;
}

export const transformPayloadToResourceData = (payload: any) => {
  if (!payload?.resource_data || !Array.isArray(payload.resource_data)) {
    return [];
  }

  return payload.resource_data.map((resource: any) => {
    // Create the base resource definition based on the payload data
    let resourceDefinition: any = {
      type: determineResourceType(resource),
    };

    // Handle different resource types
    if (resourceDefinition.type === "blob") {
      resourceDefinition = {
        ...resourceDefinition,
        connection_key: resource.connection_key,
        container_name: resource.container_name,
        resource_path: resource.resource_path,
        file_name: resource.file_name,
        column_delimiter: resource.column_delimiter,
      };
    } else if (resourceDefinition.type === "sftp" || resourceDefinition.type === "local") {
      resourceDefinition = {
        ...resourceDefinition,
        connection_key: resource.connection_key,
        resource_path: resource.resource_path,
        file_name: resource.file_name,
        column_delimiter: resource.column_delimiter,
      };
    } else if (isApiType(resourceDefinition.type)) {
      resourceDefinition = {
        ...resourceDefinition,
        api_definition: transformApiRequest(resource.api_request),
      };
    } else if (isSqlType(resourceDefinition.type)) {
      resourceDefinition = {
        ...resourceDefinition,
        connection_key: resource.connection_key,
        sql_query: resource.sql_query,
      };
    }

    // Construct the full resource object
    return {
      id: resource.resource_id,
      linked_service_id: resource.linked_service_id,
      linked_service_code: resource.linked_service_code,
      file_processing_id: resource.file_pre_processing?.id,
      aggregation_type: resource.aggregation_properties ? "aggregated" : "flat",
      additional_properties: {
        resource_definition: resourceDefinition,
        filter_rules: resource.filter_rules || null,
        aggregation_properties: resource.aggregation_properties,
        updated_additional_resource_data: resource.additional_base_resource_data,
      },
      secondary_merge_resource: resource.secondary_merge_resource || null,
    };
  });
};

// Helper functions
const determineResourceType = (resource: any): string => {
  if (resource.container_name) return "blob";
  if (resource.api_request?.bearer_token) return "token_auth_api";
  if (resource.api_request?.api_key) return "key_auth_api";
  if (resource.api_request?.oauth_client_id) return "oauth_api";
  if (resource.api_request?.user_name && resource.api_request?.password) return "basic_auth_api";
  if (resource.sql_query) return "sql";
  if (resource.remote_directory) return "sftp";
  return "local";
};

const isApiType = (type: string): boolean => {
  return ["basic_auth_api", "token_auth_api", "key_auth_api", "oauth_api"].includes(type);
};

const isSqlType = (type: string): boolean => {
  return ["sql", "mysql", "postgresql", "oracle"].includes(type);
};

const transformApiRequest = (apiRequest: ApiRequest | undefined) => {
  if (!apiRequest) return null;
  
  return {
    user_name: apiRequest.user_name || null,
    password: apiRequest.password || null,
    bearer_token: apiRequest.bearer_token || null,
    api_key: apiRequest.api_key || null,
    oauth_client_id: apiRequest.oauth_client_id || null,
    oauth_client_secret: apiRequest.oauth_client_secret || null,
    oauth_url: apiRequest.oauth_url || null,
    method: apiRequest.method || "get",
    content_type: apiRequest.content_type || "application/json",
    body: apiRequest.body || null,
    query_params: apiRequest.query_params || null,
    url_params: apiRequest.url_params || null,
    url: apiRequest.url || null,
    request_timeout: apiRequest.request_timeout || 0,
  };
}; 