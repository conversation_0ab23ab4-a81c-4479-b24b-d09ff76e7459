
import { Typography } from "@mui/material";

import { <PERSON> } from "react-router-dom";

export const domainColumns: any = [
    {
      title: "Domains",
      dataIndex: "domain_name",
      key: "domains",
      render: (text: any, record: any) => (
        <Link to={`/resource?domain=${record.id}`}>{text}</Link>
      ),
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      responsive: ["lg"],
      render: (text: any) => {
        return (
          <span>
           
          </span>
        );
      },
    },
  ];