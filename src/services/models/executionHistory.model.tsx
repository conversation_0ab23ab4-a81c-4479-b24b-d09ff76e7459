import { getFormatedDate } from "../utils";





export const executionHitoryColumns: any = [
  {
    title: "Rule Name",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "Domain",
    dataIndex: "domain_name",
    key: "domain_name",
    responsive: ["lg"],
  },
  {
    title: "Execution Date",
    dataIndex: "execution_time",
    key: "execution_time",
    responsive: ["lg"],
    render: (text: any) => {
      return getFormatedDate(text);
    },
  },
  {
    title: "Execution Status",
    dataIndex: "is_success",
    key: "is_success",
    responsive: ["lg"],
    render: (text: any) => {
      return (
        <span>
          {/* {text ? (
            <Button
            style={{ borderColor: "#28a745", color: "#28a745" }}
            danger
          >
            Success
          </Button>
            
          ) : (
            <Button danger>Fail</Button>
          )} */}
        </span>
      );
    },
  },
  {
    title: "View Result",
    dataIndex: "execution_report",
    key: "execution_report",
    render: (text: any) => {
      // const filePath = `${process.env.REACT_APP_HOST}/${text}`;
      const relativePath = (text !== '')?`${process.env.REACT_APP_BASE_FILE_PATH}${text}`:"javscript:void(0)";
      return (
        <a href={relativePath} rel="noreferrer" download target={"_blank"}>
        EYE  
        </a>
      );
    },
  },
];