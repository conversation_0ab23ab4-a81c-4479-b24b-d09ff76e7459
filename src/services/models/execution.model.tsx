export const defaultRunParameters = {
  no_of_errors_in_response: 0,
  no_of_errors_in_output_files: 0,
  skip_duplicate_records: true,
  summary_mode: false,
  generate_files: true,
  skip_validation: false,
  validation_severity_level: "Low",
  run_instance: {
    run_id: 1,
    run_name: "Legacy",
  },
  save_input_data_file: false,
  execute_comparison_research_query: true,
  use_secondary_merge_resources: true,
  store_errors_snapshots_and_create_issues: true,
  keep_downloaded_files: false,
  pull_new_files_from_server: false,
  is_long_running_job: false,
  isCustomiseSeverity: false,
};

export const defaultResultStorageParameters = {
  linked_service_id:
    process.env.REACT_APP_OUTPUT_STORAGE_LINKED_SERVICE_ID || null,
  linked_service_code:
    process.env.REACT_APP_OUTPUT_STORAGE_LINKED_SERVICE_CODE || null,
  connection_key_id:
    process.env.REACT_APP_OUTPUT_STORAGE_CONNECTION_KEY || null,
  file_path: process.env.REACT_APP_OUTPUT_FILES_BASE_PATH || null,
};

export const latestCompleteExecutionParams = {
  rule_execution_request: {
    request_body: {
      resource_data: [
        {
          resource_id: 1,
          resource_prefix: "SamplePrefix1",
          linked_service_id: 101,
          linked_service_code: "ServiceCode1",
          connection_key: "ConnectionKey1",
          sql_query: "SELECT * FROM SampleTable1",
          container_name: "Container1",
          resource_path: "/path/to/resource1",
          file_name: "SampleFile1.xlsx",
          column_delimiter: ",",
          file_pre_processing: {
            file_format: "xlsx",
            schema_definition: "string",
            field_delimiter: "|",
            custom_attributes: {
              type: "string",
              store_name: "SampleStore1",
              store_label: "SampleLabel1",
            },
            has_footer: true,
            footer_lines: 1,
            has_header: true,
            header_lines: 1,
            skip_rows: 0,
            has_multiple_sheets: false,
            skip_blanks: true,
            compression: null,
            compression_codec: null,
            line_terminator: "\n",
            has_comments: "false",
            comments_marker: "#",
            encoding: "utf-8",
            bad_lines: "skip",
          },
          filter_rules: null,
          api_request: null,
          aggregation_properties: null,
          aggregation_base_resource_data: null,
          additional_base_resource_data: null,
          secondary_merge_resource: null,
          pre_processing_request: null,
        },
        {
          resource_id: 2,
          resource_prefix: "SamplePrefix2",
          linked_service_id: 102,
          linked_service_code: "ServiceCode2",
          connection_key: "ConnectionKey2",
          sql_query: "SELECT * FROM SampleTable2",
          container_name: "Container2",
          resource_path: "/path/to/resource2",
          file_name: "SampleFile2.xlsx",
          column_delimiter: ",",
          file_pre_processing: {
            file_format: "xlsx",
            schema_definition: "string",
            field_delimiter: "|",
            custom_attributes: {
              type: "string",
              store_name: "SampleStore2",
              store_label: "SampleLabel2",
            },
            has_footer: true,
            footer_lines: 1,
            has_header: true,
            header_lines: 1,
            skip_rows: 0,
            has_multiple_sheets: false,
            skip_blanks: true,
            compression: null,
            compression_codec: null,
            line_terminator: "\n",
            has_comments: "false",
            comments_marker: "#",
            encoding: "utf-8",
            bad_lines: "skip",
          },
          filter_rules: null,
          api_request: null,
          aggregation_properties: null,
          aggregation_base_resource_data: null,
          additional_base_resource_data: null,
          secondary_merge_resource: null,
          pre_processing_request: null,
        },
      ],
      output_storage_params: null,
      run_instance: null,
      validation_severity_level: null,
      severity_column_names: null,
      filter_rules: null,
      rule_execution_report_name: null,
      validation_execution_report_name: null,
      inline_variables: null,
      pivot_results: null,
      pull_latest_files: false,
      query_params: null,
      latest_execution_id: null,
      is_rerun: false,
    },
    no_of_errors_in_response: 0,
    no_of_sample_validation_errors: 0,
    no_of_errors_in_output_files: 0,
    no_of_errors_in_filter_record_output_files: 0,
    summary_mode: false,
    generate_files: true,
    skip_validation: false,
    save_input_data_file: false,
    execute_comparison_research_query: false,
    skip_duplicate_records: true,
    keep_downloaded_files: false,
    use_secondary_merge_resources: false,
    use_merge_on_keys_as_unique_keys: true,
    store_errors_snapshots_and_create_issues: true,
    additional_info_for_all_resources: [
      {
        file_names: ["SampleFile1.xlsx"],
        resource_id: 1,
        resource_column_details_version: 1,
        resource_version: 1,
        domain_version: 1,
      },
      {
        file_names: ["SampleFile2.xlsx"],
        resource_id: 2,
        resource_column_details_version: 1,
        resource_version: 1,
        domain_version: 1,
      },
    ],
  },
  rule_id: 1231,
  rule_version: "1",
};
