export const addrule = {
  name: "",
  description: "",
  domain_id: null,
  domain_name: "",
  rule_schema: {
    merge_rule: {
      primary_dataset: {
        resource_id: 2,
        merge_on: ["Portfolio Table", "Fisher Sec ID"],
      },
      secondary_datasets: [
        {
          resource_id: 3,
          merge_on: ["ACCT_CD", "EXT_SEC_ID"],
          merge_type: "outer", // Not required (Optional)
        },
      ],
    },
    comparison_rules: [
      {
        conditions: [
          {
            left_operand: {
              type: "column", // constant/column
              value: "",
              resource_id: null,
              column_name: "",
            },
            right_operand: {
              type: "column", // constant/column
              value: "",
              resource_id: null,
              column_name: "",
            },
            comparison_type: "", // equals/not equals/less than/greater than/less than equal to/greater than equal to
          },
        ],
      },
    ],
  },
};
