import {
  getPaginatedRunInstanceUrl,
  getActiveRunInstanceUrl,
  getRunInstanceDetailUrl,
  runInstanceUrl,
} from "./constants";
import base from "../middlewares/interceptors";

export const getActiveRunInstance = async () => {
  try {
    const response = await base.get(getActiveRunInstanceUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getPaginatedRunInstance = async ({
  page,
  pSize,
}: any) => {
  try {
    const defaultPage = process.env.REACT_APP_DEFAULT_PAGE || '1';
    const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE || '25';

    const pageN = page || defaultPage;
    const pageSize = pSize || defaultPageSize;

    const params: { [key: string]: any } = {
      page: pageN,
      size: pageSize,
    };

    const response = await base.get(getPaginatedRunInstanceUrl, { params });
    return response.data;
  } catch (error) {
    console.error(error);
    // Handle error as needed, you might want to throw it or log it to a monitoring service
    throw error;
  }
};

export const getRunInstanceDetail = async ({
  currentRunInstanceId,
}: any) => {
  try {
    const response = await base.get(
      `${getRunInstanceDetailUrl}/${currentRunInstanceId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const addRunInstance = async ({ payload }: any) => {
  try {
    const response = await base.post(runInstanceUrl, payload);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const updateRunInstance = async ({
  currentRunInstanceId,
  payload,
}: any) => {
  try {
    const response = await base.put(
      `${runInstanceUrl}/${currentRunInstanceId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const deleteRunInstance = async (currentRunInstanceId: number) => {
  try {
    const response = await base.delete(
      `${runInstanceUrl}/${currentRunInstanceId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
