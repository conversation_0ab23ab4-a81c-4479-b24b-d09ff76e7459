import {
  addDomainLinkageUrl,
  getDomainLinkageAllListUrl,
  getDomainLinkageDetailUrl,
  getDomainLinkagePaginatedListUrl,
} from "./constants";
import base from "../middlewares/interceptors";

export const getDomainLinkagePaginatedList = async ({
  payload,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    const params = new URLSearchParams({
      page: pageN,
      size: pageS,
      ...Object.entries(payload || {}).reduce((acc: any, [key, value]: any) => {
        if (Array.isArray(value)) {
          if (value.length === 0) {
            // Skip empty array values
            return acc;
          } else {
            // Handle non-empty arrays
            acc[key] = value.map((item) => encodeURIComponent(item)).join(",");
          }
        } else if (value) {
          // Handle non-array values
          acc[key] = encodeURIComponent(value);
        }
        return acc;
      }, {}),
    });

    const response = await base.get(
      `${getDomainLinkagePaginatedListUrl}?${params.toString()}`
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const getDomainLinkageAllList = async ({
  payload,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    const params = new URLSearchParams({
      page: pageN,
      size: pageS,
      ...Object.entries(payload || {}).reduce((acc: any, [key, value]: any) => {
        if (Array.isArray(value)) {
          if (value.length === 0) {
            return acc;
          } else {
            acc[key] = value
              .map((item) => encodeURIComponent(item).replace(/%20/g, " "))
              .join(",");
          }
        } else if (value) {
          acc[key] = encodeURIComponent(value).replace(/%20/g, " "); // Replace '%20' with space
        }
        return acc;
      }, {}),
    });

    const response = await base.get(
      `${getDomainLinkageAllListUrl}?${params.toString()}`
    );

    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
export const addDomainLinkage = async (reqBody: any) => {
  try {
    const response = await base.post(addDomainLinkageUrl, reqBody);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
export const updateDomainLinkage = async (
  payload: any,
  domainLinkageId: any
) => {
  try {
    const response = await base.put(
      `${addDomainLinkageUrl}/${domainLinkageId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getDomainLinkageDetail = async ({
  currentDomainLinkageId,
}: any) => {
  try {
    const response = await base.get(
      `${getDomainLinkageDetailUrl}?domain_linkage_filter_ids=${currentDomainLinkageId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
