import {
  addFileProcessingUrl,
  getFileProcessingUrl,
  updateFileProcessingById,
} from "./constants";
import base from "../middlewares/interceptors";

export const getFileProcessing = async () => {
  try {
    const response = await base.get(getFileProcessingUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getFileProcessingDetail = async ({
  currentFileProcessingId,
}: any) => {
  try {
    const response = await base.get(
      `${getFileProcessingUrl}/${currentFileProcessingId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const addFileProcessing = async ({ payload }: any) => {
  try {
    const response = await base.post(addFileProcessingUrl, payload);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const updateFileProcessing = async ({
  currentFileProcessingId,
  payload,
}: any) => {
  try {
    const response = await base.put(
      `${updateFileProcessingById}/${currentFileProcessingId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const deleteFileProcessing = async (currentFileProcessingId: number) => {
  try {
    const response = await base.delete(
      `${updateFileProcessingById}/${currentFileProcessingId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
