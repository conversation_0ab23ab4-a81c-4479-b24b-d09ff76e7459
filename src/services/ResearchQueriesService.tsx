import {
  deleteExecutionResultByExecutionIdUrl,
  deleteResearchQueryByResearchQueryIdUrl,
  getGenericResearchQueriesList,
  getGenericResearchQueryDetailByIdURL,
  getGenericResearchQueryURL,
  getResearchQueryByExecutionIdUrl,
  getResearchQueryHistoryURL,
} from "./constants";
import base from "../middlewares/interceptors";
import { IGetGenericResearchQueriesProp } from "../types/ResearchQueries";

export interface IResearchQueryHistoryData {
  page: number;
  pSize: number;
  domainId?: number | null;
  ruleId?: number | null;
  fromDate?: string | undefined;
  searchFilterData: any;
}

export const getResearchQueriesList = async ({
  page,
  pSize,
}: IGetGenericResearchQueriesProp) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getGenericResearchQueriesList}/pages?page=${pageN}&size=${pageS}`;

    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const addGenericResearchQuery = async (reqBody: any) => {
  try {
    const response = await base.post(getGenericResearchQueryURL, reqBody);
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};

export const getGenericResearchQueryDetail = async (
  currentResearchQueryId: number
) => {
  try {
    const response = await base.get(
      `${getGenericResearchQueryDetailByIdURL}/${currentResearchQueryId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const updateGenericResearchQuery = async ({
  currentResearchQueryId,
  payload,
}: any) => {
  try {
    const response = await base.put(
      `${getGenericResearchQueryURL}/${currentResearchQueryId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResearchQueryHistory = async ({
  page,
  pSize,
  domainId,
  ruleId,
  fromDate,
  searchFilterData,
}: IResearchQueryHistoryData) => {
  const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
  const pageS = pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
  try {
    let url = `${getResearchQueryHistoryURL}?`;
    //correct below data asap
    if (domainId) {
      url += `domain_id=${encodeURIComponent(domainId)}&`;
    }
    if (ruleId) {
      url += `rule_id=${encodeURIComponent(ruleId)}&`;
    }
    if (fromDate) {
      url += `date=${encodeURIComponent(fromDate)}&`;
    }
    searchFilterData &&
      Object.keys(searchFilterData).length > 0 &&
      Object.entries(searchFilterData).map(([key, value]: any) => {
        if (key && value) {
          const trimmedValue = value?.trim();
          if (key === "domain_id") {
            url += `domain_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "domain_name") {
            url += `domain_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "rule_id") {
            url += `rule_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "rule_name") {
            url += `rule_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_id") {
            url += `run_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_name") {
            url += `run_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "date" && trimmedValue) {
            url += `date=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "generic_research_query_id" && trimmedValue) {
            url += `generic_research_query_id=${encodeURIComponent(
              trimmedValue
            )}&`;
          }
          if (key === "generic_research_query_code" && trimmedValue) {
            url += `generic_research_query_code=${encodeURIComponent(
              trimmedValue
            )}&`;
          }
          if (key === "execution_status" && trimmedValue) {
            url += `execution_status=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "generic_execution_id" && trimmedValue) {
            url += `ids=${encodeURIComponent(trimmedValue)}&`;
          }
        }
      });

    url += `page=${pageN}&size=${pageS}`;
    const response = await base.get(url);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResearchQueryByExecutionId = async ({
  currentResearchQueryResultId,
}: any) => {
  try {
    let requestUrl;
    if (currentResearchQueryResultId && currentResearchQueryResultId != 0) {
      requestUrl = `${getResearchQueryByExecutionIdUrl}/${currentResearchQueryResultId}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const deleteGenericResearchQueryById = async (
  currentExecutionId: any
) => {
  try {
    const response = await base.delete(
      `${deleteResearchQueryByResearchQueryIdUrl}/${currentExecutionId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const deleteExecutionResultByExecutionId = async (
  currentExecutionId: any
) => {
  try {
    const response = await base.delete(
      `${deleteExecutionResultByExecutionIdUrl}/${currentExecutionId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
