import {
  getConnectionKeysUrl,
  getLinkedServicesUrl,
  getAllConnectionKeysUrl,
  updateConnectionKeyById,
  addConnectionKeyUrl,
  testConnectionKeyUrl,
  getConnectionKeyBackupUrl,
} from "./constants";
import base from "../middlewares/interceptors";

interface ConnectionKey {
  id: string;
  name: string;
  code: string;
}
export const getConnectionKeys = async (
  type?: string,
  id?: string
): Promise<ConnectionKey[]> => {
  try {
    let response;
    if (type) {
      response = await base.get(`${getConnectionKeysUrl}?type=${type}`);
    } else if (id) {
      response = await base.get(
        `${getConnectionKeysUrl}?connection_key_filter_ids=${id}`
      );
    } else {
      response = await base.get(getConnectionKeysUrl);
    }
    return response.data;
  } catch (error) {
    console.error("Error fetching connection keys:", error);
    return [];
  }
};
export const getConnectionKeysPaginatedServices = async (
  page: any,
  pSize: any
) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getConnectionKeysUrl}/pages?page=${pageN}&size=${pageS}`;
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getAllPaginatedConnectionKeys = async (page: any, pSize: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getAllConnectionKeysUrl}/pages?page=${pageN}&size=${pageS}`;
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getAllConnectionKeys = async (connectionKeyIds: any) => {
  try {
    const connectionKeyIdsQueryParam = connectionKeyIds.join(",");
    const apiUrl = `${getAllConnectionKeysUrl}?connection_key_filter_ids=${connectionKeyIdsQueryParam}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getAllConnectionKeysWithoutId = async () => {
  try {
    const apiUrl = `${getAllConnectionKeysUrl}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getConnectionKeysByLinkedService = async (
  linked_service_id: number
) => {
  try {
    const response = await base.get(
      `${getLinkedServicesUrl}/${linked_service_id}/connection-keys-details`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getConnectionKeyDetail = async ({
  currentConnectionKeyId,
}: any) => {
  try {
    const response = await base.get(
      `${getConnectionKeysUrl}/${currentConnectionKeyId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const addConnectionKey = async ({ payload }: any) => {
  try {
    const response = await base.post(addConnectionKeyUrl, payload);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const updateConnectionKey = async ({
  currentConnectionKeyId,
  payload,
}: any) => {
  try {
    const response = await base.put(
      `${updateConnectionKeyById}/${currentConnectionKeyId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const deleteConnectionKey = async (currentConnectionKeyId: number) => {
  try {
    const response = await base.delete(
      `${updateConnectionKeyById}/${currentConnectionKeyId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const testConnectionKey = async ({ payload }: any) => {
  try {
    const response = await base.post(testConnectionKeyUrl, payload);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
export const getConnectionKeyBackup = async ({
  connectionKeyId,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    let requestUrl;
    if (connectionKeyId && connectionKeyId != 0) {
      requestUrl = `${getConnectionKeyBackupUrl}?connection_key_backup_filter_ids=${connectionKeyId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
