import {
  getExecutionRulesHistory,
  getDownloadRulesHistory,
  getDownloadExecutionsHistory,
  getDownloadSummaryOutput,
  getDownloadDetailedOutput,
  getDownloadValidateSummaryOutput,
  getDownloadValidateDetailedOutput,
} from "./constants";
import base from "../middlewares/interceptors";
import { downloadExcelFileFromUTF } from "./utils";

export interface IExecutionHistoryData {
  page: number;
  pSize: number;
  domainId?: number | null;
  ruleId?: number | null;
  fromDate?: string | undefined;
  searchFilterData: any;
}

export const getExecutionHistory = async ({
  page,
  pSize,
  domainId,
  ruleId,
  fromDate,
  searchFilterData,
}: IExecutionHistoryData) => {
  const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
  const pageS = pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
  try {
    let url = `${getExecutionRulesHistory}?`;
    if (domainId) {
      url += `domain_id=${encodeURIComponent(domainId)}&`;
    }
    if (ruleId) {
      url += `rule_id=${encodeURIComponent(ruleId)}&`;
    }
    if (fromDate) {
      url += `date=${encodeURIComponent(fromDate)}&`;
    }
    searchFilterData &&
      Object.keys(searchFilterData).length > 0 &&
      Object.entries(searchFilterData).map(([key, value]: any) => {
        if (key && value) {
          const trimmedValue = value?.trim();
          if (key === "domain_id") {
            url += `domain_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "domain_name") {
            url += `domain_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "rule_id") {
            url += `rule_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "rule_name") {
            url += `rule_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_id") {
            url += `run_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_name") {
            url += `run_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "date" && trimmedValue) {
            url += `date=${encodeURIComponent(trimmedValue)}&`;
          }
        }
      });

    url += `page=${pageN}&size=${pageS}`;
    const response = await base.get(url);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const downloadRuleHistory = async ({ searchFilterData }: any) => {
  try {
    let url = `${getDownloadRulesHistory}?`;
    searchFilterData &&
      Object.keys(searchFilterData).length > 0 &&
      Object.entries(searchFilterData).map(([key, value]: any) => {
        if (key && value) {
          const trimmedValue = value?.trim();
          if (key === "domain_id") {
            url += `domain_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "domain_name") {
            url += `domain_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "rule_id") {
            url += `rule_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "rule_name") {
            url += `rule_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_id") {
            url += `run_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_name") {
            url += `run_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "date" && trimmedValue) {
            url += `date=${encodeURIComponent(trimmedValue)}&`;
          }
        }
      });
    const response = await base.get(url, {
      responseType: "blob",
    });
    const fileName = "rule_history_output";
    if (response.status >= 200 && response.status < 300) {
      downloadExcelFileFromUTF(response, fileName);
    } else {
      console.error(`HTTP error! Status: ${response.status}`);
    }
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};

// this function is used for execution history download button
export const downloadExecutionHistory = async ({ searchFilterData }: any) => {
  try {
    let url = `${getDownloadExecutionsHistory}?`;
    searchFilterData &&
      Object.keys(searchFilterData).length > 0 &&
      Object.entries(searchFilterData).map(([key, value]: any) => {
        if (key && value) {
          const trimmedValue = value?.trim();
          if (key === "domain_id") {
            url += `domain_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "domain_name") {
            url += `domain_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "resource_id") {
            url += `resource_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "resource_name") {
            url += `resource_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_id") {
            url += `run_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_name") {
            url += `run_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "date" && trimmedValue) {
            url += `date=${encodeURIComponent(trimmedValue)}&`;
          }
        }
      });

    const response = await base.get(url, {
      responseType: "blob",
    });
    const fileName = "validation_history_output";
    if (response.status >= 200 && response.status < 300) {
      downloadExcelFileFromUTF(response, fileName);
    } else {
      console.error(`HTTP error! Status: ${response.status}`);
    }
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};

// this function is used for summary output download button
export const downloadSummaryOutput = async (
  run_name: string | null,
  run_date: string | null
) => {
  try {
    const response = await base.get(
      `${getDownloadSummaryOutput}/${run_name}/${run_date}`,
      { responseType: "blob" }
    );
    const fileName = "summary_output_history";
    if (response.status >= 200 && response.status < 300) {
      downloadExcelFileFromUTF(response, fileName);
    } else {
      console.error(`HTTP error! Status: ${response.status}`);
    }
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};

// this function is used for detailed output download button
export const downloadDetailedOutput = async (
  run_name: string | null,
  run_date: string | null
) => {
  try {
    const response = await base.get(
      `${getDownloadDetailedOutput}/${run_name}/${run_date}`,
      { responseType: "blob" }
    );
    const fileName = "detailed_output_history";
    if (response.status >= 200 && response.status < 300) {
      downloadExcelFileFromUTF(response, fileName);
    } else {
      console.error(`HTTP error! Status: ${response.status}`);
    }
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};

// this function is used for validate summary output download button
export const downloadValidateSummaryOutput = async (
  run_name: string | null,
  run_date: string | null
) => {
  try {
    const response = await base.get(
      `${getDownloadValidateSummaryOutput}/${run_name}/${run_date}`,
      { responseType: "blob" }
    );
    const fileName = "validation_summary_output_history";
    if (response.status >= 200 && response.status < 300) {
      downloadExcelFileFromUTF(response, fileName);
    } else {
      console.error(`HTTP error! Status: ${response.status}`);
    }
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};

// this function is used for validate detailed output download button
export const downloadValidationDetailedOutput = async (
  run_name: string | null,
  run_date: string | null
) => {
  try {
    const response = await base.get(
      `${getDownloadValidateDetailedOutput}/${run_name}/${run_date}`,
      { responseType: "blob" }
    );
    const fileName = "validation_detailed_output_history";
    if (response.status >= 200 && response.status < 300) {
      downloadExcelFileFromUTF(response, fileName);
    } else {
      console.error(`HTTP error! Status: ${response.status}`);
    }
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};
