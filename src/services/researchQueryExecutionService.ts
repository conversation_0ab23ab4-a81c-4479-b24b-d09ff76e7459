import base from "../middlewares/interceptors";
import {
  executeResearchQueryURL
} from "./constants";

export const executeResearchQueryById = async (
  researchQueryId: number,
  payload: any,
) => {
  try {
    const response = await base.post(
      `${executeResearchQueryURL}/${researchQueryId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
