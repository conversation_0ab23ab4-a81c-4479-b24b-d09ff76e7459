import base from "../middlewares/interceptors";
import {
  getIssuesDetailPaginatedURl,
  getIssuesDetailURl,
  getIssuesHistoryURl,
  getValidationIssuesDetailPaginatedURl,
  getValidationIssuesDetailURl,
  getValidationIssuesHistoryURl,
} from "./constants";

export const getIssuesDetail = async (payload: any) => {
  try {
    const response = await base.post(`${getIssuesDetailURl}`, payload);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
export const getPaginatedIssuesDetail = async ({
  payload,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    const params = new URLSearchParams({
      page: pageN,
      size: pageS,
      ...Object.entries(payload || {}).reduce((acc: any, [key, value]: any) => {
        if (value) acc[key] = encodeURIComponent(value);
        return acc;
      }, {}),
    });

    const response = await base.get(
      `${getIssuesDetailPaginatedURl}?${params.toString()}`
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const getPaginatedValidationIssuesDetail = async ({
  payload,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    const params = new URLSearchParams({
      page: pageN,
      size: pageS,
      ...Object.entries(payload || {}).reduce((acc: any, [key, value]: any) => {
        if (value) acc[key] = encodeURIComponent(value);
        return acc;
      }, {}),
    });

    const response = await base.get(
      `${getValidationIssuesDetailPaginatedURl}?${params.toString()}`
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
export const getValidationIssuesDetail = async (payload: any) => {
  try {
    const response = await base.post(
      `${getValidationIssuesDetailURl}`,
      payload
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const upadateComparisonIssues = async (
  issueId: string,
  payload: any
) => {
  try {
    const response = await base.post(
      `${getIssuesDetailURl}/${issueId}`,
      payload
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const massComparisonIssuesUpadate = async (payload: any) => {
  try {
    const response = await base.post(
      `${getIssuesDetailURl}/mass_update/`,
      payload
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
export const getIssueHistory = async (
  issueHistoryId: string,
  issueFrom: string
) => {
  try {
    let response;
    if (issueFrom === "comparison") {
      response = await base.get(`${getIssuesHistoryURl}/${issueHistoryId}`);
    } else {
      response = await base.get(
        `${getValidationIssuesHistoryURl}/${issueHistoryId}`
      );
    }
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const upadateValidationIssues = async (
  issueId: string,
  payload: any
) => {
  try {
    const response = await base.post(
      `${getValidationIssuesDetailURl}/${issueId}`,
      payload
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const massValidationIssuesUpadate = async (payload: any) => {
  try {
    const response = await base.post(
      `${getValidationIssuesDetailURl}/mass_update/`,
      payload
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
