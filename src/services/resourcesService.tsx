import {
  addResourceUrl,
  getResourceListByDomainId,
  validateResourceFileByFileName,
  getFilesByResourceId,
  getAllResources,
  addResourceColumnsUrl,
  getResourceColumnsUrl,
  getResourceDetailById,
  getResourceColumnsDetailById,
  updateResourceById,
  updateResourceColumnById,
  getResourceColumnsUrlByDomain,
  getResourcesByIds,
  validateResource,
  getResourcesList,
  getResourcesColumnByDomain,
  cloneResourceUrl,
  cloneResourceColumnUrl,
  getResourceBackupUrl,
  getResourceColumnBackupUrl,
  validateImportedResourceUrl,
  validateImportedResourceColumnDetailUrl,
  getResourceResultByResourceIdUrl,
  getResourceColumnsByIds,
  autoGenerateDataTypesUrl,
  getValidationsErrorsListByResultIdUrl,
  getValidationsDuplicateErrorsListByResultIdUrl,
  getValidationsCrossFieldsListByResultIdUrl,
  getValidationsNullKeysListByResultIdUrl,
  validateResourceReRun,
  deleteResourceValidationbyIdUrl,
  getValidateResourceReRun,
  backgroundValidateResource,
  backgroundValidateResourceReRun,
  abortResourceValidationbyIdUrl,
} from "./constants";
import {
  IGetResourceColumns,
  IGetResourceFilesList,
  IGetResourceProp,
} from "../types/resource";
import base from "../middlewares/interceptors";

export const getResourceList = async ({
  currentDomainId,
}: IGetResourceProp) => {
  try {
    const response = await base.get(
      `${getResourceListByDomainId}/${currentDomainId}/resources`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const addResource = async (reqBody: any) => {
  try {
    const response = await base.post(addResourceUrl, reqBody);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const addResourceColumns = async (reqBody: any, reqParam?: any) => {
  try {
    const url = `${addResourceColumnsUrl}${
      reqParam ? `?need_domain_creation=${encodeURIComponent(reqParam)}` : ""
    }`;
    const response = await base.post(url, reqBody);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const getResourceFilesList = async ({
  currentResourceId,
}: IGetResourceFilesList) => {
  try {
    const response = await base.get(
      `${getFilesByResourceId}/${currentResourceId}/files`
    );
    return response.data;
  } catch (error) {}
};

export const validateResourceDataFile = async (reqBody: any) => {
  try {
    const fileApiUrl = `${validateResourceFileByFileName}/${reqBody.resourceId}?file_name=${reqBody.fileName}`;
    const response = await base.post(fileApiUrl, { data: "" });
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const getAllResourceList = async () => {
  try {
    const response = await base.get(getAllResources);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const getResourceColumnsList = async ({
  currentDomainId,
}: IGetResourceProp) => {
  try {
    const response = await base.get(`${getResourceColumnsUrl}`);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResourceDetail = async ({
  currentResourceId,
}: IGetResourceFilesList) => {
  try {
    const response = await base.get(
      `${getResourceDetailById}/${currentResourceId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResourceColumnsDetail = async ({
  resourceColumnDetailId,
}: IGetResourceColumns) => {
  try {
    const response = await base.get(
      `${getResourceColumnsDetailById}/${resourceColumnDetailId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getResourceColumnsDetailWithLoading = async ({
  resourceColumnDetailId,
  setIsLoading,
}: any) => {
  setIsLoading(true);
  try {
    const response = await base.get(
      `${getResourceColumnsDetailById}/${resourceColumnDetailId}`
    );

    return response.data;
  } catch (error) {
    console.error(error);
  } finally {
    setIsLoading(false);
  }
};

export const updateResource = async ({ currentResourceId, payload }: any) => {
  try {
    const requestUrl =
      payload?.comment && payload?.comment.trim().length > 0
        ? `${updateResourceById}/${currentResourceId}?comment=${encodeURIComponent(
            payload?.comment
          )}`
        : `${updateResourceById}/${currentResourceId}`;
    const response = await base.put(requestUrl, payload);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const updateResourceColumn = async ({
  resourceColumnId,
  payload,
}: any) => {
  try {
    const requestUrl =
      payload?.comment && payload?.comment.trim().length > 0
        ? `${updateResourceColumnById}/${resourceColumnId}?comment=${encodeURIComponent(
            payload?.comment
          )}`
        : `${updateResourceColumnById}/${resourceColumnId}`;
    const response = await base.put(requestUrl, payload);

    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const deleteResource = async (currentResourceId: any) => {
  try {
    const response = await base.delete(
      `${updateResourceById}/${currentResourceId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResourceColumnsListByDomainId = async ({
  currentDomainId,
}: IGetResourceProp) => {
  try {
    const response = await base.get(
      `${getResourceColumnsUrlByDomain}/${currentDomainId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResourcesByMultipleIds = async (resourceIds: any) => {
  try {
    const resourceIdsQueryParam = resourceIds.join(",");
    const apiUrl = `${getResourcesByIds}?resource_filter_ids=${resourceIdsQueryParam}`;
    const response = await base.get(`${apiUrl}`);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getResourceColumnsByMultipleIds = async (resourceColIds: any) => {
  try {
    const resourceColIdsQueryParam = resourceColIds.join(",");
    const apiUrl = `${getResourceColumnsByIds}?resource_column_filter_ids=${resourceColIdsQueryParam}`;
    const response = await base.get(`${apiUrl}`);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
const buildQueryString = (params: any) => {
  return Object.keys(params)
    .filter(
      (key) =>
        key !== "validation_severity_level" &&
        key !== "run_instance" &&
        key !== "skip_validation"
    )
    .map(
      (key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
    )
    .join("&");
};

export const ResourceValidation = async (reqBody: any) => {
  try {
    // const queryString = buildQueryString(runParameters);
    const response = await base.post(`${validateResource}`, reqBody);
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};
export const ReRunResourceValidation = async (reqBody: any) => {
  try {
    const response = await base.post(`${validateResourceReRun}`, reqBody);
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};
export const backgroundResourceValidation = async (reqBody: any) => {
  try {
    // const queryString = buildQueryString(runParameters);
    const response = await base.post(`${backgroundValidateResource}`, reqBody);
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};
export const backgroundReRunResourceValidation = async (reqBody: any) => {
  try {
    const response = await base.post(
      `${backgroundValidateResourceReRun}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};
export const getResourcePaginatedList = async ({
  currentDomainId,
  page,
  pSize,
}: IGetResourceProp) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getResourcesList}?page=${pageN}&size=${pageS}`;
    if (currentDomainId && currentDomainId != 0) {
      requestUrl = `${getResourcesList}?domain_id=${currentDomainId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getResourceColumnsPaginatedList = async ({
  currentDomainId,
  page,
  pSize,
}: IGetResourceProp) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getResourceColumnsUrl}/pages?page=${pageN}&size=${pageS}`;
    if (currentDomainId && currentDomainId != 0) {
      requestUrl = `${getResourceColumnsUrl}?domain_id=${currentDomainId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResourceColumnsPaginatedListByDomainId = async ({
  currentDomainId,
  page,
  pSize,
}: IGetResourceProp) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getResourceColumnsUrl}/pages?page=${pageN}&size=${pageS}`;
    if (currentDomainId && currentDomainId != 0) {
      requestUrl = `${getResourcesColumnByDomain}?domain_id=${currentDomainId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const deleteResourceColumn = async (currentResourceId: any) => {
  try {
    const response = await base.delete(
      `${updateResourceColumnById}/${currentResourceId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const cloneResource = async (reqBody: any, isExisting?: any) => {
  try {
    const response = await base.post(
      `${cloneResourceUrl}/${reqBody.resource_id}${
        isExisting === false
          ? "?use_existing_resource_column_details=false"
          : ""
      }`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
export const autoGenerateDataTypes = async (fileFormData: any) => {
  try {
    base.interceptors.request.use(
      (config: { data: any; headers: { [x: string]: any } }) => {
        if (config.data instanceof FormData) {
          delete config.headers["Content-Type"];
        }
        return config;
      }
    );
    const token = localStorage.getItem("token");
    const response = await base.post(
      `${autoGenerateDataTypesUrl}`,
      fileFormData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response;
  } catch (error) {
    if (base.isCancel(error)) {
      throw new Error("Upload canceled");
    }
    throw error;
  }
};

export const cloneResourceColumn = async (reqBody: any) => {
  try {
    const response = await base.post(
      `${cloneResourceColumnUrl}/${reqBody.resource_column_details_id}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const getResourceBackup = async ({ resourceId, page, pSize }: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    let requestUrl;
    if (resourceId && resourceId != 0) {
      requestUrl = `${getResourceBackupUrl}?resource_backup_filter_ids=${resourceId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getResourceColumnBackup = async ({
  resourceColumnId,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    let requestUrl;
    if (resourceColumnId && resourceColumnId != 0) {
      requestUrl = `${getResourceColumnBackupUrl}?resource_column_backup_filter_ids=${resourceColumnId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const ValidateImportedResource = async (reqBody: any) => {
  try {
    const response = await base.post(
      `${validateImportedResourceUrl}/${reqBody?.entity_description?.id}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const ValidateImportedResourceColumnDetails = async (reqBody: any) => {
  try {
    const response = await base.post(
      `${validateImportedResourceColumnDetailUrl}/${reqBody?.entity_description?.id}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const getResourceResultByResourceId = async ({
  currentResourceResultId,
}: any) => {
  try {
    let requestUrl;
    if (currentResourceResultId && currentResourceResultId != 0) {
      requestUrl = `${getResourceResultByResourceIdUrl}/${currentResourceResultId}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

/**
 * Validation Errors List
 * @param currentValidationResultId
 * @param page
 * @param pSize
 * @returns
 */
export const getPaginatedValidationErrorsList = async ({
  currentValidationResultId,
  selectedColumnName,
  page,
  pSize,
}: {
  currentValidationResultId: number;
  selectedColumnName: string;
  page?: number;
  pSize?: number;
}) => {
  try {
    const pageN =
      page ?? parseInt(process.env.REACT_APP_DEFAULT_PAGE || "1", 10);
    const pageS =
      pSize ?? parseInt(process.env.REACT_APP_DEFAULT_PAGE_SIZE || "25", 10);
    let requestUrl = `${getValidationsErrorsListByResultIdUrl}`;

    const params: URLSearchParams = new URLSearchParams();
    if (pageN) params.append("page", pageN.toString());
    if (pageS) params.append("size", pageS.toString());
    if (currentValidationResultId)
      params.append("execution_id", currentValidationResultId.toString());
    if (selectedColumnName) params.append("name", selectedColumnName);

    requestUrl += `?${params.toString()}`;

    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

/**
 * Validation Duplicate Error List
 * @param currentValidationResultId
 * @param page
 * @param pSize
 * @returns
 */
export const getPaginatedValidationDuplicateErrorsList = async ({
  currentValidationResultId,
  resourceId,
  page,
  pSize,
}: {
  currentValidationResultId: number;
  resourceId: number;
  page?: number;
  pSize?: number;
}) => {
  try {
    const pageN =
      page ?? parseInt(process.env.REACT_APP_DEFAULT_PAGE || "1", 10);
    const pageS =
      pSize ?? parseInt(process.env.REACT_APP_DEFAULT_PAGE_SIZE || "25", 10);
    let requestUrl = `${getValidationsDuplicateErrorsListByResultIdUrl}`;

    const params: URLSearchParams = new URLSearchParams();
    if (currentValidationResultId)
      params.append("execution_id", currentValidationResultId.toString());
    if (resourceId) params.append("resource_id", resourceId.toString());
    if (pageN) params.append("page", pageN.toString());
    if (pageS) params.append("size", pageS.toString());

    requestUrl += `?${params.toString()}`;

    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

/**
 * Validation Cross Field List
 * @param currentValidationResultId
 * @param page
 * @param pSize
 * @returns
 */
export const getPaginatedValidationCrossFieldsList = async ({
  currentValidationResultId,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl;
    if (currentValidationResultId && currentValidationResultId != 0) {
      requestUrl = `${getValidationsCrossFieldsListByResultIdUrl}/${currentValidationResultId}?page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

/**
 * Validation Null keys
 * @param currentValidationResultId
 * @param page
 * @param pSize
 * @returns
 */
export const getPaginatedValidationNullKeysList = async ({
  currentValidationResultId,
  page,
  pSize,
}: {
  currentValidationResultId: number;
  page?: number;
  pSize?: number;
}) => {
  try {
    const pageN =
      page ?? parseInt(process.env.REACT_APP_DEFAULT_PAGE || "1", 10);
    const pageS =
      pSize ?? parseInt(process.env.REACT_APP_DEFAULT_PAGE_SIZE || "25", 10);
    let requestUrl = `${getValidationsNullKeysListByResultIdUrl}`;

    const params: URLSearchParams = new URLSearchParams();
    if (pageN) params.append("page", pageN.toString());
    if (pageS) params.append("size", pageS.toString());
    if (currentValidationResultId)
      params.append("execution_id", currentValidationResultId.toString());

    requestUrl += `?${params.toString()}`;

    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

/**
 * Gets validation rerun result for a given execution ID
 * @param resourceId - The ID of the execution to get results for
 * @returns The validation rerun result data
 * @throws Error if request fails
 */
export const getValidationRerunResultByResourceId = async (
  resourceId: number,
  runId?: number | null,
  runName?: string | null
): Promise<any> => {
  if (!resourceId) {
    throw new Error("Execution ID is required");
  }

  const url = `${getValidateResourceReRun}/${resourceId}?run_id=${runId}&run_name=${runName}`;

  try {
    const response = await base.get(url);
    return response.data;
  } catch (error) {
    console.error("Failed to get validation rerun result:", error);
    throw error;
  }
};

export const deleteValidationExecutionbyID = async (
  currentExecutionId: any
) => {
  try {
    const response = await base.delete(
      `${deleteResourceValidationbyIdUrl}/${currentExecutionId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const abortValidationExecutionbyID = async (currentExecutionId: any) => {
  try {
    const response = await base.post(
      `${abortResourceValidationbyIdUrl}/${currentExecutionId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
