import {
  addNewRule,
  executeRule,
  getExecutionRulesHistory,
  getRulesListByDomainIdUrl,
  getRulesListUrl,
  getRuleDetailById,
  updateRuleById,
  getPaginatedRulesListUrl,
  getRuleBackupUrl,
  validateImportedRuleUrl,
  getRuleResultByRuleIdUrl,
  getResearchQueryDetailByRuleId,
  addNewResearchQuery,
  updateResearchQueryById,
  getMissingResultByRuleResultIdUrl,
  getMismatchResultByRuleResultIdUrl,
  getAdhocQueryResultByRuleResultIdUrl,
  getResearchQueryResultByRuleResultIdUrl,
  getMismatchRecordsWithIssuesByExecutionIdUrl,
  getMissingRecordsWithIssuesByExecutionIdUrl,
  executeRuleReRunUrl,
  deleteRuleExecutionbyIdUrl,
  abortExecutionbyIdUrl,
  getLatestExecutionPayload,
  backroundExecuteRule,
  backgroundExecuteRuleReRunUrl,
  getTableColumnsUrl,
} from "./constants";
import { IExecuteRuleData } from "../types/rules";
import base from "../middlewares/interceptors";
import { IPaginatedProps } from "../types";

export const getRulesList = async () => {
  try {
    const response = await base.get(getRulesListUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getRulesListByDomainId = async (domainId: number) => {
  try {
    const response = await base.get(
      `${getRulesListByDomainIdUrl}/${domainId}/rules`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const createRule = async (reqBody: any) => {
  try {
    const response = await base.post(addNewRule, reqBody);
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};

const buildQueryString = (params: any) => {
  return Object.keys(params)
    .filter(
      (key) => key !== "validation_severity_level" && key !== "run_instance"
    )
    .map(
      (key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
    )
    .join("&");
};

export const executeRuleById = async (
  ruleId: number,
  reqBody: IExecuteRuleData
) => {
  try {
    // const queryString = buildQueryString(runParameters);
    const response = await base.post(`${executeRule}/${ruleId}`, reqBody);
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};
export const executeRuleReRun = async (reqBody: any) => {
  try {
    const response = await base.post(`${executeRuleReRunUrl}`, reqBody);
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};

export const backgroundexecuteRuleById = async (
  ruleId: number,
  reqBody: IExecuteRuleData
) => {
  try {
    // const queryString = buildQueryString(runParameters);
    const response = await base.post(
      `${backroundExecuteRule}/${ruleId}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};
export const backgroundExecuteRuleReRun = async (reqBody: any) => {
  try {
    const response = await base.post(
      `${backgroundExecuteRuleReRunUrl}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};

export const executionHistory = async () => {
  try {
    const response = await base.get(getExecutionRulesHistory);
    return response.data;
  } catch (error) {
    console.log(error);
  }
};

export const deleteRule = async (currentRuleId: number) => {
  try {
    const response = await base.delete(`${updateRuleById}/${currentRuleId}`);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getRuleDetail = async (currentRuleId: number) => {
  try {
    const response = await base.get(`${getRuleDetailById}/${currentRuleId}`);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const updateRule = async ({ currentRuleId, payload }: any) => {
  try {
    const requestUrl =
      payload?.comment && payload?.comment.trim().length > 0
        ? `${updateRuleById}/${currentRuleId}?comment=${encodeURIComponent(
            payload?.comment
          )}`
        : `${updateRuleById}/${currentRuleId}`;
    const response = await base.put(requestUrl, payload);

    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getPaginatedRulesListt = async ({
  currentDomainId,
  page,
  pSize,
}: IPaginatedProps) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getPaginatedRulesListUrl}?page=${pageN}&size=${pageS}`;
    if (currentDomainId && currentDomainId != 0) {
      requestUrl = `${getPaginatedRulesListUrl}?domain_id=${currentDomainId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getPaginatedRulesList = async ({
  currentDomainId,
  page,
  pSize,
}: any) => {
  try {
    const defaultPage = process.env.REACT_APP_DEFAULT_PAGE || "1";
    const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE || "25";

    const pageN = page || defaultPage;
    const pageSize = pSize || defaultPageSize;

    const params: { [key: string]: any } = {
      page: pageN,
      size: pageSize,
    };

    if (currentDomainId && currentDomainId !== 0) {
      params["domain_id"] = currentDomainId; // Assuming domain_id can be an empty string if not provided
    }

    const response = await base.get(getPaginatedRulesListUrl, { params });
    return response.data;
  } catch (error) {
    console.error(error);
    // Handle error as needed, you might want to throw it or log it to a monitoring service
    throw error;
  }
};

export const getRuleBackup = async ({ ruleId, page, pSize }: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    let requestUrl;
    if (ruleId && ruleId != 0) {
      requestUrl = `${getRuleBackupUrl}?rule_backup_filter_ids=${ruleId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const ValidateImportedRule = async (reqBody: any) => {
  try {
    const response = await base.post(
      `${validateImportedRuleUrl}/${reqBody?.entity_description?.id}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const getRuleResultByRuleId = async ({ currentRuleResultId }: any) => {
  try {
    let requestUrl;
    if (currentRuleResultId && currentRuleResultId != 0) {
      requestUrl = `${getRuleResultByRuleIdUrl}/${currentRuleResultId}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResearchQueryDetail = async (currentRuleId: number) => {
  try {
    const response = await base.get(
      `${getResearchQueryDetailByRuleId}/${currentRuleId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const addResearchQuery = async (reqBody: any) => {
  try {
    const response = await base.post(addNewResearchQuery, reqBody);
    return response.data;
  } catch (error) {
    console.log(error);
    return Promise.reject(error);
  }
};

export const updateResearchQuery = async ({
  currentResearchQueryId,
  payload,
}: any) => {
  try {
    const response = await base.put(
      `${updateResearchQueryById}/${currentResearchQueryId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getTableColumns = async ({ payload }: any) => {
  try {
    const response = await base.post(`${getTableColumnsUrl}`, payload);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

/**
 * For missing records
 */

export const getMissingRuleResultByRuleResultId = async ({
  currentRuleResultId,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl;
    if (currentRuleResultId && currentRuleResultId != 0) {
      requestUrl = `${getMissingResultByRuleResultIdUrl}/${currentRuleResultId}?page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getMissingRecordsWithIssuesByExecutionId = async ({
  currentRuleResultId,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl;
    if (currentRuleResultId && currentRuleResultId != 0) {
      requestUrl = `${getMissingRecordsWithIssuesByExecutionIdUrl}/pages?execution_id=${currentRuleResultId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

/**
 * For Mismatch records
 */

export const getMismatchRuleResultByRuleResultId = async ({
  currentRuleResultId,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl;
    if (currentRuleResultId && currentRuleResultId != 0) {
      requestUrl = `${getMismatchResultByRuleResultIdUrl}/${currentRuleResultId}?page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getMismatchRecordsWithIssuesByExecutionId = async ({
  currentRuleResultId,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl;
    if (currentRuleResultId && currentRuleResultId != 0) {
      requestUrl = `${getMismatchRecordsWithIssuesByExecutionIdUrl}/pages?execution_id=${currentRuleResultId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

/**
 * For Adhoc query records
 */

export const getAdhocQueryResultByRuleResultId = async ({
  currentRuleResultId,
  pageN,
  pageS,
  queryName,
}: any) => {
  try {
    let requestUrl;
    if (currentRuleResultId && currentRuleResultId != 0) {
      requestUrl = `${getAdhocQueryResultByRuleResultIdUrl}/${currentRuleResultId}?name=${queryName}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

/**
 * For Research Query Records
 */

export const getResearchQueryRuleResultByRuleResultId = async ({
  currentRuleResultId,
  pageN,
  pageS,
  queryName,
}: any) => {
  try {
    let requestUrl;
    if (currentRuleResultId && currentRuleResultId != 0) {
      requestUrl = `${getResearchQueryResultByRuleResultIdUrl}/${currentRuleResultId}?name=${queryName}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

/**
 * Gets comparison rerun result for a given execution ID
 * @param ruleId - The ID of the execution to get results for
 * @returns The comparison rerun result data
 * @throws Error if request fails
 */
export const getComparisonRerunResultByRuleId = async (
  ruleId: number,
  runId?: number | null,
  runName?: string | null
): Promise<any> => {
  if (!ruleId) {
    throw new Error("Execution ID is required");
  }

  const url = `${getLatestExecutionPayload}/${ruleId}?run_id=${runId}&run_name=${runName}`;

  try {
    const response = await base.get(url);
    return response.data;
  } catch (error) {
    console.error("Failed to get comparison rerun result:", error);
    throw error;
  }
};
export const deleteComparisonExecutionbyID = async (
  currentExecutionId: any
) => {
  try {
    const response = await base.delete(
      `${deleteRuleExecutionbyIdUrl}/${currentExecutionId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const abortExecution = async (currentExecutionId: any) => {
  try {
    const response = await base.post(
      `${abortExecutionbyIdUrl}/${currentExecutionId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
