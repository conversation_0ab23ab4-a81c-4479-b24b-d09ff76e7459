import {
  getReportDetailOutputUrl,
  getReportSummaryOutputUrl,
  getValidationDetailReportOutputUrl,
  getValidationRulesHistory,
  getValidationSummaryReportOutputUrl,
} from "./constants";
import { IgetReportsByRunInstanceProps } from "../types/reports";
import base from "../middlewares/interceptors";

export const getReportsSummaryByRunInstance = async ({
  runName,
  page,
  pSize,
  runDate,
  formattedDate,
}: IgetReportsByRunInstanceProps) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    const response = await base.get(
      `${getReportSummaryOutputUrl}/${runName}/${formattedDate}?page=${pageN}&size=${pageS}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getReportsDetailsByRunInstance = async ({
  runName,
  page,
  pSize,
  runDate,
  formattedDate,
}: IgetReportsByRunInstanceProps) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    const response = await base.get(
      `${getReportDetailOutputUrl}/${runName}/${formattedDate}?page=${pageN}&size=${pageS}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getValidationReportsByRunInstanceAndRunDate = async ({
  runName,
  runDate,
  page,
  pSize,
  formattedDate,
}: IgetReportsByRunInstanceProps) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    const response = await base.get(
      `${getValidationDetailReportOutputUrl}/${runName}/${formattedDate}?page=${pageN}&size=${pageS}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getValidationSummaryReportsByRunInstanceAndRunDate = async ({
  runName,
  runDate,
  page,
  pSize,
  formattedDate,
}: IgetReportsByRunInstanceProps) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    const response = await base.get(
      `${getValidationSummaryReportOutputUrl}/${runName}/${formattedDate}?page=${pageN}&size=${pageS}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export interface IExecutionHistoryData {
  page: number;
  pSize: number;
  domainId?: number | null;
  resourceId?: number | null;
  fromDate?: string | undefined;
  searchFilterData?: any;
}

export const getValidationHistory = async ({
  page,
  pSize,
  domainId,
  resourceId,
  fromDate,
  searchFilterData,
}: IExecutionHistoryData) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let url = `${getValidationRulesHistory}?`;
    //alert("");
    if (domainId) {
      url += `domain_id=${encodeURIComponent(domainId)}&`;
    }
    if (resourceId) {
      url += `resource_id=${encodeURIComponent(resourceId)}&`;
    }
    if (fromDate) {
      url += `date=${encodeURIComponent(fromDate)}&`;
    }
    searchFilterData &&
      Object.keys(searchFilterData).length > 0 &&
      Object.entries(searchFilterData).map(([key, value]: any) => {
        if (key && value) {
          const trimmedValue = value?.trim();
          if (key === "domain_id") {
            url += `domain_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "domain_name") {
            url += `domain_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "resource_id") {
            url += `resource_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "resource_name") {
            url += `resource_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_id") {
            url += `run_id=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "run_name") {
            url += `run_name=${encodeURIComponent(trimmedValue)}&`;
          }
          if (key === "date" && trimmedValue) {
            url += `date=${encodeURIComponent(trimmedValue)}&`;
          }
        }
      });
    url += `page=${pageN}&size=${pageS}`;

    const response = await base.get(url);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
