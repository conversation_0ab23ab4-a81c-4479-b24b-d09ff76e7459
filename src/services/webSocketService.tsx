import { useEffect, useRef, useCallback } from 'react';
import { useNotifications } from '../contexts/NotificationContext';

const RETRY_DELAY = Number(process.env.REACT_APP_RETRY_DELAY || 4000); // 4 seconds
const PING_TIMEOUT = 30000; // 30 seconds - time to wait for a ping before considering connection dead

export const useWebSocketService = () => {
  const { addNotification } = useNotifications();
  const socketRef = useRef<WebSocket | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isManuallyClosed = useRef<boolean>(false);
  const lastPingTimeRef = useRef<number>(Date.now());
  const pingCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const connectWebSocket = useCallback(() => {
    const userId = Number(localStorage.getItem("userId")) || 0;
    const token = localStorage.getItem("token") || "";
    if (!userId || !token) return;

    if (socketRef.current?.readyState === WebSocket.OPEN) {
      console.log("WebSocket already connected. Skipping reconnection.");
      return;
    }

    const baseUrl = process.env.REACT_APP_ENGINE_SOCKET_HOST;
    const url = `${baseUrl}/notifications/register_notifications?token=${token}`;
    console.log("Connecting to WebSocket:", url);

    socketRef.current = new WebSocket(url);

    socketRef.current.onopen = () => {
      console.log("Connected to WebSocket");
    };

    socketRef.current.onmessage = (event) => {
      try {
        console.log("Received WebSocket message:", event.data);
        // Check if the message is a ping message
        if (event.data === "ping") {
          // Update the last ping time
          lastPingTimeRef.current = Date.now();
          return;
        }

        // For all other messages, try to parse as JSON
        const data = JSON.parse(event.data);

        const newNotification = {
          id: `notification-${Date.now()}-${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          message: JSON.stringify(data),
          read: false,
          timestamp: Date.now(),
          ...data,
        };

        // Add the notification using the context
        addNotification(newNotification);
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    socketRef.current.onerror = (error) => {
      console.error("WebSocket Error:", error);
    };

    socketRef.current.onclose = () => {
      if (!isManuallyClosed.current) {
        console.warn(
          `WebSocket disconnected. Reconnecting in ${
            RETRY_DELAY / 1000
          } seconds...`
        );
        retryTimeoutRef.current = setTimeout(connectWebSocket, RETRY_DELAY);
      } else {
        console.log("WebSocket connection closed manually.");
      }
    };
  }, [addNotification]);

  // Setup ping check interval to monitor connection health
  useEffect(() => {
    // Start a ping check interval to monitor the WebSocket connection
    pingCheckIntervalRef.current = setInterval(() => {
      const currentTime = Date.now();
      const timeSinceLastPing = currentTime - lastPingTimeRef.current;

      // If we haven't received a ping in PING_TIMEOUT ms and the socket is open,
      // we'll consider the connection as potentially stale but won't close it
      if (timeSinceLastPing > PING_TIMEOUT) {
        console.warn(
          `No ping received in ${
            PING_TIMEOUT / 1000
          } seconds, but keeping connection open`
        );
      }
    }, 10000); // Check every 10 seconds

    return () => {
      if (pingCheckIntervalRef.current) {
        clearInterval(pingCheckIntervalRef.current);
        pingCheckIntervalRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    isManuallyClosed.current = false;
    connectWebSocket();

    return () => {
      // We only manually close the connection if the app is shutting down
      // or if we haven't received a ping in a long time
      const timeSinceLastPing = Date.now() - lastPingTimeRef.current;

      // Only set manually closed if we're truly shutting down the app
      // or if we haven't received a ping in a very long time (3x the timeout)
      if (timeSinceLastPing > PING_TIMEOUT * 3) {
        isManuallyClosed.current = true;
        if (socketRef.current) {
          socketRef.current.close();
          socketRef.current = null;
        }
      }

      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [connectWebSocket]);

  return {
    isConnected: socketRef.current?.readyState === WebSocket.OPEN,
  };
};

export default useWebSocketService;
