import { loginUrl, signupUrl, refreshTokenUrl } from "./constants";
import base from "../middlewares/interceptors";

const config = {
  headers: {
    // 'Authorization': 'Bearer my-token',
    "Content-Type": "application/json",
  },
};

export const login = async (payload: any) => {
  try {
    const response = await base.post(loginUrl, payload);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const signup = async (payload: any) => {
  try {
    const response = await base.post(signupUrl, payload);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const refreshToken = async (payload: any) => {
  try {
    const response = await base.post(refreshTokenUrl, payload);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
