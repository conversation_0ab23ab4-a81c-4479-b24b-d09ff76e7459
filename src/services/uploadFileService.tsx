import base from "../middlewares/interceptors";

const config = {
  headers: {
    "Content-Type": "application/json",
  },
};
export const getChunkSize = async () => {
  try {
    const response = await base.get(
      `${process.env.REACT_APP_CONFIG_API_HOST}/upload-file/chunk-size`,
      config
    );
    return response;
  } catch (error) {
    console.error(error);
  }
};

export const uploadFile = async (
  fileFormData: any,
  cancelSource: { token: any },
  onUploadProgress: any
) => {
  try {
    base.interceptors.request.use(
      (config: { data: any; headers: { [x: string]: any } }) => {
        if (config.data instanceof FormData) {
          delete config.headers["Content-Type"]; // Let axios set it automatically
        }
        return config;
      }
    );
    const token = localStorage.getItem("token");
    const response = await base.post(
      `${process.env.REACT_APP_CONFIG_API_HOST}/upload-file`,
      fileFormData,

      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        cancelToken: cancelSource.token,
        onUploadProgress,
      }
    );
    return response;
  } catch (error) {
    if (base.isCancel(error)) {
      throw new Error("Upload canceled");
    }
    throw error;
  }
};
