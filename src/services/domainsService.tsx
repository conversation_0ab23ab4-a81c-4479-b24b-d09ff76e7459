import {
  addDomainUrl,
  cloneDomainUrl,
  getDomainBackupUrl,
  getDomainDetailById,
  getDomain<PERSON>ist,
  getDomainListUrl,
  updateDomainById,
  validateImportedDomainUrl,
} from "./constants";
import base from "../middlewares/interceptors";
import { IGetDomainProp } from "../types/resource";

const config = {
  headers: {
    // 'Authorization': 'Bearer my-token',
    "Content-Type": "application/json",
  },
};

export const getDomains = async () => {
  try {
    const response = await base.get(getDomainListUrl, config);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getDomainDetail = async ({ currentDomainId }: any) => {
  try {
    const response = await base.get(
      `${getDomainDetailById}/${currentDomainId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const addDomain = async (reqBody: any) => {
  try {
    const response = await base.post(addDomainUrl, reqBody);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const updateDomain = async ({ currentDomainId, payload }: any) => {
  try {
    const requestUrl =
      payload?.comment && payload?.comment.trim().length > 0
        ? `${updateDomainById}/${currentDomainId}?comment=${encodeURIComponent(
            payload?.comment
          )}`
        : `${updateDomainById}/${currentDomainId}`;
    const response = await base.put(requestUrl, payload);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const deleteDomain = async (currentDomainId: number) => {
  try {
    const response = await base.delete(
      `${updateDomainById}/${currentDomainId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const cloneDomain = async (reqBody: any) => {
  try {
    const response = await base.post(
      `${cloneDomainUrl}/${reqBody.domain_id}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const getDomainPaginatedList = async ({
  page,
  pSize,
}: IGetDomainProp) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getDomainList}?page=${pageN}&size=${pageS}`;
    requestUrl = `${getDomainList}?page=${pageN}&size=${pageS}`;
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getDomainBackup = async ({ domainId, page, pSize }: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    let requestUrl;
    if (domainId && domainId != 0) {
      requestUrl = `${getDomainBackupUrl}?domain_backup_filter_ids=${domainId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const ValidateImportedDomain = async (reqBody: any) => {
  try {
    const response = await base.post(
      `${validateImportedDomainUrl}/${reqBody?.entity_description?.id}`,
      reqBody
    );
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};
