export const constants = {
    JIRA_ISSUE_COLLECTOR_SCRIPT: `<html>
      <head>
      <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
      <Script
      type="text/javascript"
      src="YOUR_GENERATED_JIRA_ISSUE_COLLECTOR_URL">
      </script>
      
      </head>
      <body>
      <script type="text/javascript" src=https://vis-nyc.atlassian.net/s/d41d8cd98f00b204e9800998ecf8427e-T/-3ddrgv/b/7/c95134bc67d3a521bb3f4331beb9b804/_/download/batch/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector.js?locale=en-US&collectorId=71fd5f09></script>
      </body>
      </html>`,
};