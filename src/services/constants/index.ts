export const baseFilePath = `assets/files/`;
export const apiBaseUrlConfig = `${process.env.REACT_APP_CONFIG_API_HOST}`;
export const apiBaseUrlEngine = `${process.env.REACT_APP_ENGINE_API_HOST}`;
export const apiBaseUrlPagerduty = `${process.env.REACT_APP_PAGER_DUTY_HOST}`;

export const datatypes = ["string", "integer", "datetime", "decimal"];
export const dateTimeFormat = [
  "yyyy-mm-dd HH:MM:SS.ff",
  "yyyy-mm-ddTHH:MM:SS",
  "yyyymmdd HH:MM:SS",
  "mm/dd/yyyy",
  "mm/dd/yy",
  "dd/mm/yyyy",
  "dd/mm/yy",
  "dd-mm-yyyy",
  "dd-mm-yy",
  "mm-dd-yyyy",
  "mm-dd-yy",
  "yyyy-mm-dd",
  "yy-mm-dd",
  "m/d/yyyy",
  "m/d/yy",
  "d/m/yyyy",
  "d/m/yy",
  "m-d-yyyy",
  "m-d-yy",
  "d-m-yyyy",
  "d-m-yy",
  "bbb d, yyyy",
  "BB dd, yyyy",
  "bbb d, yy",
  "BB d, yy",
  "yyyy-mm-dd HH:MM:SS",
  "HH:MM:SS",
  "yyyy-mm-dd hh:MM:SS tt",
  "yyyy-mm-dd HH:MM:SS",
  "HH:MM",
  "hh:MM tt",
  "H:MM",
  "h:MM tt",
  "HH:MM:SS.f",
  "hh:MM:SS.f tt",
  "HH:MM:SS.f",
  "aaa bbb dd yyyy",
  "aaa, BB dd yyyy",
  "aaa, BB dd yyyy HH:MM:SS",
  "bbb dd yyyy, aaa",
  "bbb dd yyyy, aaa hh:MM tt",
  "AA, bbb dd yyyy",
  "AA, BB d yyyy h:MM tt",
  "aaa, bbb d yyyy h:MM tt",
  "yyyymmdd",
  "ddmmyyyy",
  "mmddyyyy",
  "yyyymmddhhMMSStt",
  "yyyymmddHHMMSS",
];
export const aggregationType = ["flat", "aggregated"];
export const severity_level = ["high", "medium", "low"];
export const mergeType = ["outer", "inner", "left", "right"];
export const methodType = ["get", "put", "post"];
export const ruleQueriesType = ["Resource", "External", "Merged_Data", "Mixed"];
export const ruleSubQueriesType = ["Resource", "External", "Merged_Data"];
export const genericSubQueriesType = ["Resource", "External"];
export const resourceQueriesType = ["Resource", "External"];
export const issueStatus = ["New", "In Progress", "Acknowledged", "Closed"];
export const GenericQueriesType = ["Resource", "External", "Mixed"];

export const rulesSearchType = [
  "domain_id",
  "domain_name",
  "rule_id",
  "rule_name",
  "run_id",
  "run_name",
  "from_date",
];
export const resourceSearchType = [
  "domain_id",
  "domain_name",
  "resource_id",
  "resource_name",
  "run_id",
  "run_name",
  "from_date",
];

export const contentType = [
  "application/json",
  "json",
  "application/xml",
  "xml",
];
export const sqlDatabaseType = [
  "sql",
  "mariadb",
  "azure_sql",
  "oracle_sql",
  "snowflake_sql",
];

export const apiType = [
  "basic_auth_api",
  "token_auth_api",
  "key_auth_api",
  "oauth_api",
  "no_auth_api",
];

export const paginatedResponseFormat = {
  items: [],
  total: 0,
  page: 0,
  size: 0,
  pages: 0,
};

export const keysToRemove = ["id", "domain_code", "code"];

//Domains
export const getDomainListUrl = `${apiBaseUrlConfig}/domains`;
export const addDomainUrl = `${apiBaseUrlConfig}/domains`;
export const getDomainDetailById = `${apiBaseUrlConfig}/domains`;
export const updateDomainById = `${apiBaseUrlConfig}/domains`;
export const cloneDomainUrl = `${apiBaseUrlConfig}/domains/clone`;
export const getDomainList = `${apiBaseUrlConfig}/domains/pages`;
export const getDomainBackupUrl = `${apiBaseUrlConfig}/domain-backup/pages`;
export const validateImportedDomainUrl = `${apiBaseUrlConfig}/import-entities/validate-domain`;
export const getDomainExportJsonUrl = `${apiBaseUrlConfig}/export-entities/export-domain`;
//Domains Linkage
export const getDomainLinkagePaginatedListUrl = `${apiBaseUrlConfig}/domain-linkage/pages`;
export const getDomainLinkageAllListUrl = `${apiBaseUrlConfig}/domain-linkage/all`;
export const addDomainLinkageUrl = `${apiBaseUrlConfig}/domain-linkage`;
export const getDomainLinkageDetailUrl = `${apiBaseUrlConfig}/domain-linkage`;

//Resources
export const addResourceUrl = `${apiBaseUrlConfig}/resources`;
export const getResourceListByDomainId = `${apiBaseUrlConfig}/domains`;
export const getFilesByResourceId = `${apiBaseUrlConfig}/resources`;
export const validateResourceFileByFileName = `${apiBaseUrlEngine}/validations/validate`;
export const getFilesListByResourceId = `${apiBaseUrlConfig}/resources`;
export const getAllResources = `${apiBaseUrlConfig}/resources`;
export const getResourceExportJsonUrl = `${apiBaseUrlConfig}/export-entities/export-resource`;
export const addResourceColumnsUrl = `${apiBaseUrlConfig}/resources-columns`;
export const getResourceColumnsUrl = `${apiBaseUrlConfig}/resources-columns`;
export const getResourceColumnsUrlByDomain = `${apiBaseUrlConfig}/resources-columns/domain`;
export const getResourceDetailById = `${apiBaseUrlConfig}/resources`;
export const updateResourceById = `${apiBaseUrlConfig}/resources`;
export const updateResourceColumnById = `${apiBaseUrlConfig}/resources-columns`;
export const getResourceColumnsDetailById = `${apiBaseUrlConfig}/resources-columns`;
export const getResourcesByIds = `${apiBaseUrlConfig}/resources`;
export const validateResource = `${apiBaseUrlEngine}/validations/validate-with-in-body-query-params`;
export const validateResourceReRun = `${apiBaseUrlEngine}/validations/rerun`;
export const backgroundValidateResource = `${apiBaseUrlEngine}/validations/validate-with-in-body-query-params-in-backgroud`;
export const backgroundValidateResourceReRun = `${apiBaseUrlEngine}/validations/rerun-in-backgroud`;
export const getValidateResourceReRun = `${apiBaseUrlConfig}/validation-results/get-latest-complete-validation-params`;
export const getResourcesList = `${apiBaseUrlConfig}/domains/resources`;
export const getResourcesColumnByDomain = `${apiBaseUrlConfig}/domains/resourceColumnDetails`;
export const cloneResourceUrl = `${apiBaseUrlConfig}/resources/clone`;
export const cloneResourceColumnUrl = `${apiBaseUrlConfig}/resources-columns/clone`;
export const getResourceBackupUrl = `${apiBaseUrlConfig}/resource-backup/pages`;
export const getResourceColumnBackupUrl = `${apiBaseUrlConfig}/resource-columns-backup/pages`;
export const validateImportedResourceUrl = `${apiBaseUrlConfig}/import-entities/validate-resource`;
export const autoGenerateDataTypesUrl = `${apiBaseUrlConfig}/autogenerate-datatypes`;
export const getValidationsCrossFieldsListByResultIdUrl = `${apiBaseUrlConfig}/validation-results/validation-cross-field-results-pages`;

export const deleteResourceValidationbyIdUrl = `${apiBaseUrlEngine}/validations/delete-validation-execution-data`;
export const abortResourceValidationbyIdUrl = `${apiBaseUrlEngine}/validations/abort-validation-with-execution-id`;

export const getValidationsErrorsListByResultIdUrl = `${apiBaseUrlConfig}/issues/validation-errors-records/pages`;
export const getValidationsDuplicateErrorsListByResultIdUrl = `${apiBaseUrlConfig}/issues/duplicate-records/pages`;
export const getValidationsNullKeysListByResultIdUrl = `${apiBaseUrlConfig}/issues/null-key-errors-records/pages`;

//Resource Column Details
export const validateImportedResourceColumnDetailUrl = `${apiBaseUrlConfig}/import-entities/validate-resource-col-details`;
export const getResourceColumnExportJsonUrl = `${apiBaseUrlConfig}/export-entities/export-resource-column-details`;
export const getResourceResultByResourceIdUrl = `${apiBaseUrlConfig}/validation-results/history/by-validation-result-id`;
export const getResourceColumnsByIds = `${apiBaseUrlConfig}/resources-columns`;

//Rules
export const getRulesListUrl = `${apiBaseUrlConfig}/rules`;
export const addNewRule = `${apiBaseUrlConfig}/rules`;
export const executeRule = `${apiBaseUrlEngine}/rules/execute-with-in-body-query-params`;
export const executeRuleReRunUrl = `${apiBaseUrlEngine}/rules/rerun`;
export const backroundExecuteRule = `${apiBaseUrlEngine}/rules/execute-with-in-body-query-params-in-backgroud`;
export const backgroundExecuteRuleReRunUrl = `${apiBaseUrlEngine}/rules/rerun-in-backgroud`;
export const getLatestExecutionPayload = `${apiBaseUrlConfig}/rule-results/get-latest-complete-execution-params`;
export const getRulesListByDomainIdUrl = `${apiBaseUrlConfig}/domains`;
export const getExecutionRulesHistory = `${apiBaseUrlConfig}/rule-results/history/pages`;
export const updateRuleById = `${apiBaseUrlConfig}/rules`;
export const getRuleDetailById = `${apiBaseUrlConfig}/rules`;
export const getDownloadRulesHistory = `${apiBaseUrlConfig}/rule-results/history/download`;
export const getDownloadExecutionsHistory = `${apiBaseUrlConfig}/validation-results/history/download`;
export const getDownloadSummaryOutput = `${apiBaseUrlEngine}/reports/summary_output/download`;
export const getDownloadDetailedOutput = `${apiBaseUrlEngine}/reports/detailed_output/download`;
export const getDownloadValidateSummaryOutput = `${apiBaseUrlEngine}/reports/validation_summary_output/download`;
export const getDownloadValidateDetailedOutput = `${apiBaseUrlEngine}/reports/validation_detailed_output/download`;
export const getPaginatedRulesListUrl = `${apiBaseUrlConfig}/domains/rules`;
export const getRuleBackupUrl = `${apiBaseUrlConfig}/rule-backup/pages`;
export const validateImportedRuleUrl = `${apiBaseUrlConfig}/import-entities/validate-rule`;
export const getRuleExportJsonUrl = `${apiBaseUrlConfig}/export-entities/export-rule`;
export const getRuleResultByRuleIdUrl = `${apiBaseUrlConfig}/rule-results/history/by-rule-result-id`;
export const getResearchQueryDetailByRuleId = `${apiBaseUrlConfig}/comparison-research-queries/get-by-rule-id`;
export const addNewResearchQuery = `${apiBaseUrlConfig}/comparison-research-queries`;
export const updateResearchQueryById = `${apiBaseUrlConfig}/comparison-research-queries`;
export const getTableColumnsUrl = `${apiBaseUrlEngine}/utils/get-table-columns`;

export const getMissingResultByRuleResultIdUrl = `${apiBaseUrlConfig}/rule-results/missing-record-pages`;
export const getMismatchResultByRuleResultIdUrl = `${apiBaseUrlConfig}/rule-results/mismatched-record-pages`;
export const getAdhocQueryResultByRuleResultIdUrl = `${apiBaseUrlConfig}/rule-results/adhoc-query-record-pages`;
export const getResearchQueryResultByRuleResultIdUrl = `${apiBaseUrlConfig}/rule-results/research-query-record-pages`;
export const deleteRuleExecutionbyIdUrl = `${apiBaseUrlEngine}/rules/delete-comparison-execution-data`;
export const abortExecutionbyIdUrl = `${apiBaseUrlEngine}/rules/abort-rule-with-execution-id`;

//Linked Services
export const getLinkedServicesUrl = `${apiBaseUrlConfig}/linked-service`;
export const getAllLinkedServicesUrl = `${apiBaseUrlConfig}/linked-service/all`;
export const addLinkedServiceUrl = `${apiBaseUrlConfig}/linked-service`;
export const updateLinkedServiceById = `${apiBaseUrlConfig}/linked-service`;
export const getLinkedServiceExportJsonUrl = `${apiBaseUrlConfig}/export-entities/linked-service`;
export const getLinkedServicesBackupUrl = `${apiBaseUrlConfig}/linked-service-backup/pages`;

//Connection Key
export const getConnectionKeysUrl = `${apiBaseUrlConfig}/connection-key`;
export const getAllConnectionKeysUrl = `${apiBaseUrlConfig}/connection-key/all`;
export const addConnectionKeyUrl = `${apiBaseUrlConfig}/connection-key`;
export const updateConnectionKeyById = `${apiBaseUrlConfig}/connection-key`;
export const testConnectionKeyUrl = `${apiBaseUrlConfig}/connection-key/test-connection`;
export const getConnectionKeyExportJsonUrl = `${apiBaseUrlConfig}/export-entities/connection-key`;
export const getConnectionKeyBackupUrl = `${apiBaseUrlConfig}/connection-key-backup/pages`;

//File Processing
export const getFileProcessingUrl = `${apiBaseUrlConfig}/file-processing-attributes`;
export const addFileProcessingUrl = `${apiBaseUrlConfig}/file-processing-attributes`;
export const updateFileProcessingById = `${apiBaseUrlConfig}/file-processing-attributes`;
export const getFileProcessingAttributesExportJsonUrl = `${apiBaseUrlConfig}/export-entities/file-processing-attributes`;

//Run Instance
export const getActiveRunInstanceUrl = `${apiBaseUrlConfig}/run-instance`;
export const getRunInstanceDetailUrl = `${apiBaseUrlConfig}/run-instance`;
export const runInstanceUrl = `${apiBaseUrlConfig}/run-instance`;
export const getPaginatedRunInstanceUrl = `${apiBaseUrlConfig}/run-instance/pages`;
export const getRunInstanceExportJsonUrl = `${apiBaseUrlConfig}/export-entities/run-instance`;

// Reports
export const getReportSummaryOutputUrl = `${apiBaseUrlEngine}/reports/summary_output`;
export const getReportDetailOutputUrl = `${apiBaseUrlEngine}/reports/detailed_output`;
export const getValidationDetailReportOutputUrl = `${apiBaseUrlEngine}/reports/validation_detailed_output`;
export const getValidationSummaryReportOutputUrl = `${apiBaseUrlEngine}/reports/validation_summary_output`;
export const getValidationRulesHistory = `${apiBaseUrlConfig}/validation-results/history/pages`;

// auth
export const loginUrl = `${apiBaseUrlConfig}/basic-auth/token`;
export const logoutUrl = `${apiBaseUrlConfig}/auth/logout`;
export const signupUrl = `${apiBaseUrlConfig}/basic-auth/signup`;
export const refreshTokenUrl = `${apiBaseUrlConfig}/basic-auth/refresh-token`;

// users
export const getUserProfileUrl = `${apiBaseUrlConfig}/users/profile`;

//user-role
export const getUserRoleUrl = `${apiBaseUrlConfig}/user-role`;
//users list
// export const getUsersListUrl = `${apiBaseUrlConfig}/users`;
export const getUsersListUrl = `${apiBaseUrlConfig}/user`;

//get incident
export const getResolveIncidentByIdUrl = `${apiBaseUrlConfig}/incidents/update`;
export const getIncidentByExecutionIdsUrl = `${apiBaseUrlConfig}/incidents`;
export const postIncidentCommentsUrl = `${apiBaseUrlConfig}/incidents/update/post-comment`;

//get BlobLists

export const getBlobListsUrl = `${apiBaseUrlEngine}/utils/listblobs`;

//Issue Management
export const getIssuesDetailURl = `${apiBaseUrlConfig}/issues`;
export const getIssuesDetailPaginatedURl = `${apiBaseUrlConfig}/issues/pages`;

export const getIssuesHistoryURl = `${apiBaseUrlConfig}/issues/history`;
export const getMismatchRecordsWithIssuesByExecutionIdUrl = `${apiBaseUrlConfig}/issues/mismatched-records`;
export const getMissingRecordsWithIssuesByExecutionIdUrl = `${apiBaseUrlConfig}/issues/missing-records`;

//Issue Manement for Validation

export const getValidationIssuesDetailURl = `${apiBaseUrlConfig}/issues/validation`;
export const getValidationIssuesHistoryURl = `${apiBaseUrlConfig}/issues/validation/history`;
export const getValidationIssuesDetailPaginatedURl = `${apiBaseUrlConfig}/issues/pages/validation`;

// generic research queries
export const getGenericResearchQueriesList = `${apiBaseUrlConfig}/generic-research-queries/all`;
export const getGenericResearchQueryURL = `${apiBaseUrlConfig}/generic-research-queries`;
export const getGenericResearchQueryDetailByIdURL = `${apiBaseUrlConfig}/generic-research-queries`;
export const executeResearchQueryURL = `${apiBaseUrlEngine}/generic-research-queries/execute`;
export const backgroundExecuteResearchQueryURL = `${apiBaseUrlEngine}/research-queries/execute-in-background`;
export const getResearchQueryHistoryURL = `${apiBaseUrlConfig}/generic-research-query-results`;
export const getResearchQueryByExecutionIdUrl = `${apiBaseUrlConfig}/generic-research-query-results`;
export const deleteResearchQueryByResearchQueryIdUrl = `${apiBaseUrlConfig}/generic-research-queries`;
export const deleteExecutionResultByExecutionIdUrl = `${apiBaseUrlEngine}/generic-research-queries`;
