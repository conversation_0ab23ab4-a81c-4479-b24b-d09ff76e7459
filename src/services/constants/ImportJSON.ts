export const associatedData: any = {
  linked_services: [
    {
      id: 27,
      name: "Local_Service",
      type: "file",
      code: "localFile1",
      sub_type: "local",
      is_active: true,
      connection_details: {
        connection_keys: [28],
        connection_keys_details: [
          {
            connection_key_id: 28,
            connection_key_code: "connection_key_28",
          },
        ],
      },
      created_by: null,
      modified_by: null,
      created_on: null,
      updated_on: null,
      created_by_user: null,
      modified_by_user: null,
      details: {
        entity_type: "linked_services",
        entity_id: 27,
        entity_code: "localFile1",
        is_already_exist: true,
        is_active_if_exist: true,
        entity_name_in_target_env: "Local_Service",
        entity_id_in_target_env: 1127,
        status: "not_started",
      },
    },
  ],
  connection_keys: [
    {
      id: 28,
      name: "LocalService",
      type: "local",
      code: "connection_key_28",
      ip_address: null,
      user_name: null,
      password: null,
      is_active: true,
      container_name: null,
      connection_string: null,
      api_url: null,
      created_by: null,
      modified_by: null,
      created_on: null,
      updated_on: null,
      created_by_user: null,
      modified_by_user: null,
      azure_client_id: null,
      azure_client_secret: null,
      azure_tenant_id: null,
      details: {
        entity_type: "connection_keys",
        entity_id: 28,
        entity_code: "connection_key_28",
        is_already_exist: true,
        is_active_if_exist: true,
        entity_name_in_target_env: "LocalService",
        entity_id_in_target_env: 1128,
        status: "not_started",
      },
    },
  ],
  file_processing_attributes: [
    {
      id: 4,
      resource_type: "FP_XLSX_WithHeader",
      code: "file_processing_4",
      is_active: true,
      file_processing_attributes: {
        file_format: "xlsx",
        schema_definition: "string",
        field_delimiter: "|",
        custom_attributes: {
          type: "string",
          store_name: "string",
          store_label: "string",
        },
        has_footer: false,
        footer_lines: 0,
        has_header: true,
        header_lines: 0,
        skip_rows: 0,
        has_multiple_sheets: false,
        skip_blanks: true,
        compression: null,
        compression_codec: "",
        line_terminator: "\n",
        has_comments: "false",
        comments_marker: "#",
        encoding: "utf-8",
        bad_lines: "skip",
      },
      created_by: null,
      modified_by: null,
      created_on: null,
      updated_on: null,
      created_by_user: null,
      modified_by_user: null,
      details: {
        entity_type: "file_processing_attributes",
        entity_id: 4,
        entity_code: "file_processing_4",
        is_already_exist: true,
        is_active_if_exist: true,
        entity_name_in_target_env: null,
        entity_id_in_target_env: 114,
        status: "not_started",
      },
    },
  ],
  domains: [
    {
      id: 100,
      domain_name: "Portfolio",
      domain_code: "Portfolio",
      domain_desc: "Portfolio",
      domain_properties: {
        columns: [
          {
            name: "ACCT_CD",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "ACCT_NAME",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "ACCT_SHT_NAME",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ACCT_TYP_CD",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PARENT_CHILD_FLAG",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CRRNCY_CD",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "STATE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CNTRY_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MKT_VAL",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "NET_ASSETS",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "TOT_ASSETS",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "FUND_SHRS_OUTST",
            datatype: "decimal",
            mandatory: false,
            format: "(18,3)",
            reference_data: null,
          },
          {
            name: "TOT_COST",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "NET_CASH",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "TOT_INVSTMNTS",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "NET_FUNDS_AVAIL",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "LIABILITIES",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "AMRTZD_COST",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "AVG_COST",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "OTH_ASSET",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "DIV_RECEIVED",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "INT_RECEIVED",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "CNTRBS",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "PMNTS",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "TRANSFERS",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "CASH_BAL_SOD",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "RECVB_SEC_SOLD",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "PAYBL_SEC_PURCH",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "RECVB_FUND_SHRS_SOLD",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "PAYBL_FUND_SHRS_LIQD",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "DIV_RCVBLE",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "ACCRUED_INT_INCM",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "ACCRUED_EXP_INCM",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "DIV_PAYBL",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "OTH_LIAB",
            datatype: "decimal",
            mandatory: false,
            format: "(17,2)",
            reference_data: null,
          },
          {
            name: "EQTY_ACCT",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SYST_OF_REFERENCE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "NEXPERT_INST",
            datatype: "decimal",
            mandatory: false,
            format: "(4,0)",
            reference_data: null,
          },
          {
            name: "THEME_1",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "THEME_2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "THEME_3",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "LOT_SIZE",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "ALLOW_BUY",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "ALLOW_SELL",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "ALLOW_NEG_CASH",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "MANAGER",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MODEL_PRIV",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEFAULT_MODEL_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SHAW_CLASS_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ERISA_ELIGIBLE",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "COMPLIANCE_TOLERANCE",
            datatype: "decimal",
            mandatory: false,
            format: "(5,2)",
            reference_data: null,
          },
          {
            name: "APPLY_GUIDELINES",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "GAIN_LOSS_SENSITIVE",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "SHORT_SHORT_GAINS",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "GROSS_INCOME",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "TAX_LOT_SELL_CNVTN",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEFAULT_GRP_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CUSTODIAN_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "BANK_ACCT_NUM",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INCL_ACCRD_INTEREST",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CUR_MKT_VAL",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "CUR_INVESTED",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "LAST_REPRICE_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DISC_ACCRUAL_CNVTN",
            datatype: "decimal",
            mandatory: false,
            format: "(5,0)",
            reference_data: null,
          },
          {
            name: "ACTIVE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INTERESTED_PARTY1_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INTERESTED_PARTY2_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INTERESTED_PARTY3_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEFAULT_TMP_ACCT",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INACTIVE",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "USR_CLASS_CD_1",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "USR_CLASS_CD_2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "USR_CLASS_CD_3",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "USR_CLASS_CD_4",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MKT_VAL_SOD",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "OASYS_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "FUND_ACCT",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "START_BATCH_VIOL",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "CRD_LOCK",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "INDEX_SEC_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "OMNIBUS_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ALLOW_AUTOFX",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ROTATION_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ROTATION_SEQ",
            datatype: "decimal",
            mandatory: false,
            format: "(7,0)",
            reference_data: null,
          },
          {
            name: "AUTH_GROUP",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "AUTH_NUM",
            datatype: "decimal",
            mandatory: true,
            format: "(7,0)",
            reference_data: null,
          },
          {
            name: "DEFAULT_SPECIAL_INST",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "COMP_BID_REQ",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "MAX_ISSUER_HOLDING",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "INC_TAX_RATE",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "CG_TAX_RATE",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT1",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT2",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT3",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT4",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE1",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE2",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR1",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR3",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR4",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEFAULT_BENCHMARK_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "YTD_GL_SHORT_TERM",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "YTD_GL_LONG_TERM",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT5",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT6",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT7",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT8",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT9",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT10",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT11",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT12",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT13",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT14",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT15",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT16",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT17",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT18",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT19",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE3",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE4",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE5",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE6",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE7",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE8",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE9",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE10",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE11",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_DATE12",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR5",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR6",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR7",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR8",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR9",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR10",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR11",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR12",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR13",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR14",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR15",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR16",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR17",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR18",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR19",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT1_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT2_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT3_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT4_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT5_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT6_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT7_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT8_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT9_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT10_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT11_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT12_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT13_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT14_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT15_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT16_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT17_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT18_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FLOAT19_PREV",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MKT_VAL_PREV",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "NET_ASSETS_PREV",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "TOT_ASSETS_PREV",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "TOT_COST_PREV",
            datatype: "decimal",
            mandatory: false,
            format: "(25,4)",
            reference_data: null,
          },
          {
            name: "GROSS_INCOME_PREV",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "CATEGORY1",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CATEGORY2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CATEGORY3",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_TYP_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_BENCH_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_BENCH_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_HEDGE_MDL_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_HEDGE_BENCH_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_MDL_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "DEF_BENCH_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "DEF_HEDGE_MDL_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "DEF_HEDGE_BENCH_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "MAG_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "CFWD_CLOSE_CPARTY_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "CFWD_NEW_CPARTY_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "AUTO_CFWD_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "USE_SETTLE_DATE_BAL_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_MDL_TYPE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_BENCH_TYPE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_HEDGE_MDL_TYPE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_HEDGE_BENCH_TYPE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DM_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_FWD_DAYS_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_FWD_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_FWD_DAYS",
            datatype: "decimal",
            mandatory: false,
            format: "(5,0)",
            reference_data: null,
          },
          {
            name: "DEF_MDL_REM_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "VIEW_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "SLEEVE_TYPE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CROSS_EXT_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CROSS_GRP_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CROSS_INT_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CROSS_MAG_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_QTY_BASED_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "MULT_MDL_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "MULT_MDL_NORM_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "EXCH_LIST_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INV_CLASS_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "BLOCK_ORDER_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "TEN_MIN_CHECK_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "GRP_TYPE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "TAX_LOT_PROC_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "FEE_PAYMT_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "BUNDLED_FEE_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_FOOTNOTE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_INCPTN_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_ACCT_TOT_RET_APPROX",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_ANALYTIC_TYPE",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_COMMENT",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_ATTR_METHOD_EQUITY",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "REPRICE_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_DATA_FREQ",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_MGMT_FEE_BASIS",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "REBAL_FREQ_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "NEXT_REBAL_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "REBAL_APPROVAL_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "REBAL_APPROVED_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "LAST_UPD_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "LAST_UPD_USER",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "COMMENTS",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "BASKET_TYP_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "NOTNL_FREEZE_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "CREATE_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CLOSE_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "FUT_BROKER_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_ATTR_METHOD_FI",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_TAX_BASIS",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "EX_ANTE_CALCS_METHOD_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "FACTOR_MODEL",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "CASH_RSRV_TYP",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "CASH_RSRV_AMT",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "CASH_RSRV_PCT",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "SERVICE_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "SERVICE_NAME",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_CAT_WEIGHT_ONLY_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "HOUSE_ACCT_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "UMA_TYPE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "YEARS_TO_USE_IN_VAR_CALCS",
            datatype: "decimal",
            mandatory: false,
            format: "(3,0)",
            reference_data: null,
          },
          {
            name: "UDF_CHAR20",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR21",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR22",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR23",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR24",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR25",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR26",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR27",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR28",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR29",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "RISK_VAR_CONFIDENCE_LEVEL",
            datatype: "decimal",
            mandatory: false,
            format: "(3,1)",
            reference_data: null,
          },
          {
            name: "MDL_TREE_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "EXT_MDL_TREE_ID",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXCLUDE_FROM_DRIFT_SUM_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXCLUDE_FROM_REBAL_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "EXT_DEF_MDL_ID",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXT_DEF_HEDGE_MDL_ID",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXT_DEF_BENCH_ID",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXT_DEF_HEDGE_BENCH_ID",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_THRESHOLD_DAYS",
            datatype: "decimal",
            mandatory: false,
            format: "(4,0)",
            reference_data: null,
          },
          {
            name: "PRIN_INC_CASH_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PS_INCPTN_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PS_ENABLED_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "HOUSEHOLD_TYPE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "BOD_DURATION",
            datatype: "decimal",
            mandatory: false,
            format: "(18,9)",
            reference_data: null,
          },
          {
            name: "IBOR_TAX_LOT_ENABLED_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "YTD_GL_SHORT_TERM_PREV",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "YTD_GL_LONG_TERM_PREV",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "EXCLUDE_DRIFT_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MODEL_STATUS_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ORIG_MODEL_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SMA_TYPE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ORDER_GEN_STAGGER_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "REG_DFA_US_ENTITY_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ENFORCE_WASH_SALE_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "WASH_SALE_BUY_BEHAVIOR_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXCL_WASH_SALE_FROM_DRIFT_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_MDL_TYPE2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_BENCH_TYPE2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEFAULT_MODEL_CD2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_BENCH_CD2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_MDL_ID2",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "DEF_BENCH_ID2",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "EXCLUDE_FROM_DRIFT_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "TOT_GL_TAX_BUDGET",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "ST_GL_TAX_BUDGET",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "LT_GL_TAX_BUDGET",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "GL_TAX_BUDGET_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ST_UNREAL_GAIN_BOD",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "ST_UNREAL_LOSS_BOD",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "LT_UNREAL_GAIN_BOD",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "LT_UNREAL_LOSS_BOD",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "AIMFD_REGULATED_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_IBOR_TRANSITION_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXT_DEF_MDL_ID2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXT_DEF_BENCH_ID2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_TRANSITION_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "REG_BENEFICIARY_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "FX_MGMT_METHOD_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "AMRTZD_COST_ENABLED_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "TXN_RECON_MODE_CD",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_FREEZE_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_COMP_INCL_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_COMP_EXCL_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_CLOSE_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "VOTING_RIGHTS_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INV_RIGHTS_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXT_REG_BENEFICIARY_ID",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UMA_HOLDING_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "WH_MANAGER_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "WH_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR30",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR31",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR32",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR33",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR34",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR35",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR36",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR37",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR38",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR39",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR40",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR41",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR42",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR43",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR44",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR45",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR46",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR47",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR48",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR49",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR50",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR51",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR52",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR53",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR54",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR55",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR56",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR57",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR58",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR59",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_CHAR60",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INV_OBJECTIVE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ACCT_DESC_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "TAXABILITY_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "BUSINESS_LINE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DISCRETION_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PRODUCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "STRATEGY_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SUB_STRATEGY_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SOCIALLY_RESP_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_HOLD_REPORTING_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "PMA_TRANSACTIONS_REQUIRED_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "IBOR_RECON_STATUS_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "IBOR_RECON_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "IBOR_PROMOTION_TIMESTAMP",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXT_INDEX_SEC_ID",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SETTLE_CRRNCY",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "AAM_OVERLAY_TYPE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "JURISDICTION_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "BRANCH_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "OWNER_INSTITUTION_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ALLOW_FX_NET_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "IN_TOL_POS_EVAL_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SLEEVE_STATUS_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "LAST_HARVEST_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DIRECT_VOTING_RIGHTS_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "LAST_REBAL_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MEDIAN_DRIFT_PCT",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "MAX_DRIFT_PCT",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "LAST_DRIFT_CALC_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "HEDGE_CRRNCY_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "START_BATCH_VIOL_PREV",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "CPL_LAST_RUN_TIME",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MODEL_AVAILABILITY_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SCHEDULE_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "REBAL_ONCE_DAILY_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "TAX_LOT_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXTERNAL_SHELL_MODEL_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_TOL_VALUE",
            datatype: "decimal",
            mandatory: false,
            format: "(18,9)",
            reference_data: null,
          },
          {
            name: "MDL_TOL_MULTI_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "ORDER_GEN_SEQUENCE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FUND_ASSIGN_CHAR1",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FUND_ASSIGN_CHAR2",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UDF_FUND_ASSIGN_CHAR3",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MODEL_FLEX_CASH_TIER_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_MINOR_CHANGE_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "LAST_ORDER_GEN_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_QTY_LOCK_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "EXCL_SEG_QTY_ORDER_GEN_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "INDEX_TYPE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_LIABILITY_BENCH_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEFAULT_OPTIMIZER_PROFILE_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "TBA_ROLL_REQUIRED_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ACCT_ID",
            datatype: "decimal",
            mandatory: true,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "INTRADAY_GL_SHORT_TERM",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "INTRADAY_GL_LONG_TERM",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "UPDATE_IN_PROGRESS",
            datatype: "decimal",
            mandatory: false,
            format: "(5,0)",
            reference_data: null,
          },
          {
            name: "UPDATE_SEQNO",
            datatype: "decimal",
            mandatory: false,
            format: "(10,0)",
            reference_data: null,
          },
          {
            name: "CPL_BATCH_RUNTIME",
            datatype: "number",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ALLOW_TRANSFER_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "MDL_TACTICAL_CHANGE_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "RECON_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ACCT_CASH_DRIFT_PCT",
            datatype: "decimal",
            mandatory: false,
            format: "(18,9)",
            reference_data: null,
          },
          {
            name: "MODEL_CASH_DRIFT_PCT",
            datatype: "decimal",
            mandatory: false,
            format: "(18,9)",
            reference_data: null,
          },
          {
            name: "FEE_DIVEST_MODE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "RATER_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "TRADE_PLAN_SORT_ORDER",
            datatype: "decimal",
            mandatory: false,
            format: "(8,0)",
            reference_data: null,
          },
          {
            name: "WHT_PREFERRED_RATE_SOURCE",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SLV_TAX_LOT_LOC_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SLV_RECON_LOC_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "UMA_MIXED_RECON_IND",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "ESMA_SHORT_SELL_UNDISCL_IND",
            datatype: "string",
            mandatory: true,
            format: "",
            reference_data: null,
          },
          {
            name: "FXBLK_GRP_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "WHT_CATEGORY",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "SCENARIO_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
          {
            name: "INCOME_ACCT_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "TARGET_DURATION",
            datatype: "decimal",
            mandatory: false,
            format: "(18,9)",
            reference_data: null,
          },
          {
            name: "FACTOR_MODEL_HIERARCHY_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "BLOCK_MONITOR_TYPE_CD",
            datatype: "string",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "TARGET_CASH_PCT",
            datatype: "decimal",
            mandatory: false,
            format: "(18,4)",
            reference_data: null,
          },
          {
            name: "LAST_MDL_CHANGE_PROCESS_DATE",
            datatype: "datetime",
            mandatory: false,
            format: "",
            reference_data: null,
          },
          {
            name: "DEF_STRATEGY_CHAIN_ID",
            datatype: "decimal",
            mandatory: false,
            format: "(18,0)",
            reference_data: null,
          },
        ],
        unique_columns: ["ACCT_CD"],
      },
      created_by: null,
      modified_by: null,
      created_on: null,
      updated_on: null,
      is_active: true,
      created_by_user: null,
      modified_by_user: null,
      version: 4,
      details: {
        entity_type: "domains",
        entity_id: 100,
        entity_code: "Portfolio",
        is_already_exist: true,
        is_active_if_exist: true,
        entity_name_in_target_env: "Portfolio",
        entity_id_in_target_env: 11100,
        status: "not_started",
      },
    },
  ],
  resources: [
    {
      id: 1660,
      resource_name: "Test_Resource@2",
      resource_type: "Test_Resource@2",
      resource_prefix: "Test_Resource@2",
      code: "resource_1660",
      aggregation_type: "flat",
      domain_id: 100,
      domain_name: "Date Validation",
      domain_code: "Portfolio",
      is_active: true,
      linked_service_id: 27,
      linked_service_code: "localFile1",
      current_url: null,
      file_processing_id: 4,
      file_processing_code: "file_processing_4",
      additional_properties: {
        resource_definition: {
          type: "local",
          local_definition: {
            resource_path:
              "C:\\Users\\<USER>\\Desktop\\drive\\SampleFiles\\CRD",
            file_name: "Position.xlsx",
            column_delimiter: ",",
            connection_key: 28,
            connection_key_code: "connection_key_28",
          },
          blob_definition: null,
          sql_definition: null,
          sftp_definition: null,
          api_definition: null,
        },
        aggregation_properties: null,
        resource_column_details_id: 1289,
        resource_column_details_code: "resource_1660",
        additional_resource_data: [],
        filter_rules: [],
        inline_variables: {},
      },
      version: "15",
      created_by: null,
      modified_by: null,
      created_on: null,
      updated_on: null,
      created_by_user: null,
      modified_by_user: null,
      details: {
        entity_type: "resources",
        entity_id: 1660,
        entity_code: "resource_1660",
        is_already_exist: true,
        is_active_if_exist: true,
        entity_name_in_target_env: "Test_Resource@2",
        entity_id_in_target_env: 111660,
        status: "not_started",
      },
    },
  ],
  resource_column_details: [
    {
      id: 320,
      name: "CRD-prod",
      code: "resource_513",
      domain_id: 100,
      domain_code: "Portfolio",
      is_active: true,
      resource_column_properties: {
        unique_columns: ["Account_Id"],
        resource_columns: [
          {
            is_active: true,
            column_name: "Account_Id",
            domain_column: "",
            constraints: {
              datatype: "string",
              is_mandatory: true,
              data_format: "",
              column_length: 1,
              is_derived: null,
              derived_column_definition: null,
              is_reference: null,
              reference_column_definition: null,
              custom_validations: null,
            },
            severity_level: 3,
          },
          {
            is_active: true,
            column_name: "sum_qty",
            domain_column: null,
            constraints: {
              datatype: "decimal",
              is_mandatory: false,
              data_format: "",
              column_length: 2,
              is_derived: null,
              derived_column_definition: null,
              is_reference: null,
              reference_column_definition: null,
              custom_validations: null,
            },
            severity_level: 3,
          },
          {
            is_active: true,
            column_name: "sum_mkt",
            domain_column: null,
            constraints: {
              datatype: "decimal",
              is_mandatory: true,
              data_format: "",
              column_length: 2,
              is_derived: null,
              derived_column_definition: null,
              is_reference: null,
              reference_column_definition: null,
              custom_validations: null,
            },
            severity_level: 3,
          },
        ],
        cross_field_validations: [],
        inline_variables: {},
      },
      version: 23,
      created_by: null,
      modified_by: null,
      created_on: null,
      updated_on: null,
      created_by_user: null,
      modified_by_user: null,
      details: {
        entity_type: "resource_column_details",
        entity_id: 320,
        entity_code: "resource_513",
        is_already_exist: true,
        is_active_if_exist: true,
        entity_name_in_target_env: "CRD-prod",
        entity_id_in_target_env: 11320,
        status: "not_started",
      },
    },
    {
      id: 1289,
      name: "ACCT_SOD_PCG.FUND",
      code: "resource_1660",
      domain_id: 100,
      domain_code: "Portfolio",
      is_active: true,
      resource_column_properties: {
        unique_columns: ["Account"],
        resource_columns: [
          {
            is_active: true,
            column_name: "Account",
            domain_column: null,
            constraints: {
              datatype: "string",
              is_mandatory: false,
              data_format: null,
              column_length: 1,
              is_derived: null,
              derived_column_definition: null,
              is_reference: null,
              reference_column_definition: null,
              custom_validations: null,
            },
            severity_level: 3,
          },
          {
            is_active: true,
            column_name: "mktval",
            domain_column: null,
            constraints: {
              datatype: "decimal",
              is_mandatory: true,
              data_format: "",
              column_length: 1,
              is_derived: null,
              derived_column_definition: null,
              is_reference: null,
              reference_column_definition: null,
              custom_validations: null,
            },
            severity_level: 3,
          },
          {
            is_active: true,
            column_name: "qty",
            domain_column: "",
            constraints: {
              datatype: "decimal",
              is_mandatory: false,
              data_format: "",
              column_length: null,
              is_derived: null,
              derived_column_definition: null,
              is_reference: null,
              reference_column_definition: null,
              custom_validations: null,
            },
            severity_level: 3,
          },
        ],
        cross_field_validations: [],
        inline_variables: {},
      },
      version: 6,
      created_by: null,
      modified_by: null,
      created_on: null,
      updated_on: null,
      created_by_user: null,
      modified_by_user: null,
      details: {
        entity_type: "resource_column_details",
        entity_id: 1289,
        entity_code: "resource_1660",
        is_already_exist: true,
        is_active_if_exist: true,
        entity_name_in_target_env: "ACCT_SOD_PCG.FUND",
        entity_id_in_target_env: 111289,
        status: "not_started",
      },
    },
  ],
};
