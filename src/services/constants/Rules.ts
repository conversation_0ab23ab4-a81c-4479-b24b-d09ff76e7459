export const customRules = (rule: any) => {
  let str = "";
  switch (rule.operator) {
    case "case":
      str = `CASE WHEN (${rule.value?.when}) THEN '${rule.value?.then}' ELSE '${rule.value?.else}' END`;
      break;
    case "in":
      str = `${rule.field} in (${rule.value})`;
      break;
    default:
      str = `${rule.field} ${rule.operator} ${rule.value}`;
  }
  return str;
};

export const LABELS = [
  {
    label: "Match",
    color: "#74B856",
  },
  {
    label: "Mismatch",
    color: "#E84336",
  },
  {
    label: "Duplicate",
    color: "#f7b84b",
  },
  {
    label: "Missing",
    color: "#add8e6",
  },
  {
    label: "Total",
    color: "#B3B3B3",
  },
];

export const comparisonRerunResultMockData = {
  resource_details: {
    rule_exec_parameters: {
      resource_data: [
        {
          resource_id: 509,
          resource_prefix: null,
          linked_service_id: 27,
          linked_service_code: "localFile1",
          connection_key: 28,
          sql_query: null,
          container_name: null,
          resource_path: "E:\\Data_Files",
          file_name: "GWP_Position_202212124_Change.xlsx",
          column_delimiter: "|",
          file_pre_processing: {
            file_format: "xlsx",
            schema_definition: "string",
            field_delimiter: "|",
            custom_attributes: {
              type: "string",
              store_name: "string",
              store_label: "string",
            },
            has_footer: false,
            footer_lines: 0,
            has_header: true,
            header_lines: 0,
            skip_rows: 0,
            has_multiple_sheets: false,
            skip_blanks: true,
            compression: null,
            compression_codec: null,
            line_terminator: "\n",
            has_comments: "false",
            comments_marker: "#",
            encoding: "utf-8",
            bad_lines: "skip",
          },
          filter_rules: [
            {
              name: "FR1",
              sql_query: "[Quantity] > 200",
            },
          ],
          api_request: {
            url: null,
            method: "get",
            content_type: "application/json",
            body: null,
            query_params: null,
            url_params: null,
            user_name: null,
            password: null,
            bearer_token: null,
            api_key: null,
            oauth_client_id: null,
            oauth_client_secret: null,
            oauth_url: null,
            request_timeout: 0,
          },
          aggregation_properties: null,
          aggregation_base_resource_data: null,
          additional_base_resource_data: null,
          secondary_merge_resource: null,
          pre_processing_request: null,
        },
        {
          resource_id: 510,
          resource_prefix: null,
          linked_service_id: 27,
          linked_service_code: "localFile1",
          connection_key: 28,
          sql_query: null,
          container_name: null,
          resource_path: "E:\\Data_Files",
          file_name: "CRD_Position_202212124_Change.xlsx",
          column_delimiter: "|",
          file_pre_processing: {
            file_format: "xlsx",
            schema_definition: "string",
            field_delimiter: "|",
            custom_attributes: {
              type: "string",
              store_name: "string",
              store_label: "string",
            },
            has_footer: false,
            footer_lines: 0,
            has_header: true,
            header_lines: 0,
            skip_rows: 0,
            has_multiple_sheets: false,
            skip_blanks: true,
            compression: null,
            compression_codec: null,
            line_terminator: "\n",
            has_comments: "false",
            comments_marker: "#",
            encoding: "utf-8",
            bad_lines: "skip",
          },
          filter_rules: [],
          api_request: {
            url: null,
            method: "get",
            content_type: "application/json",
            body: null,
            query_params: null,
            url_params: null,
            user_name: null,
            password: null,
            bearer_token: null,
            api_key: null,
            oauth_client_id: null,
            oauth_client_secret: null,
            oauth_url: null,
            request_timeout: 0,
          },
          aggregation_properties: null,
          aggregation_base_resource_data: null,
          additional_base_resource_data: null,
          secondary_merge_resource: null,
          pre_processing_request: null,
        },
        {
          resource_id: 511,
          resource_prefix: null,
          linked_service_id: 27,
          linked_service_code: "localFile1",
          connection_key: 28,
          sql_query: null,
          container_name: null,
          resource_path: "E:\\Data_Files",
          file_name: "FactSet_Position_202212124_Change.xlsx",
          column_delimiter: "|",
          file_pre_processing: {
            file_format: "xlsx",
            schema_definition: "string",
            field_delimiter: "|",
            custom_attributes: {
              type: "string",
              store_name: "string",
              store_label: "string",
            },
            has_footer: false,
            footer_lines: 0,
            has_header: true,
            header_lines: 0,
            skip_rows: 0,
            has_multiple_sheets: false,
            skip_blanks: true,
            compression: null,
            compression_codec: null,
            line_terminator: "\n",
            has_comments: "false",
            comments_marker: "#",
            encoding: "utf-8",
            bad_lines: "skip",
          },
          filter_rules: [],
          api_request: {
            url: null,
            method: "get",
            content_type: "application/json",
            body: null,
            query_params: null,
            url_params: null,
            user_name: null,
            password: null,
            bearer_token: null,
            api_key: null,
            oauth_client_id: null,
            oauth_client_secret: null,
            oauth_url: null,
            request_timeout: 0,
          },
          aggregation_properties: null,
          aggregation_base_resource_data: null,
          additional_base_resource_data: null,
          secondary_merge_resource: null,
          pre_processing_request: null,
        },
      ],
      output_storage_params: null,
      run_instance: {
        run_id: 1,
        run_name: "Legacy",
      },
      validation_severity_level: 3,
      severity_column_names: {},
      filter_rules: [
        {
          name: "J_DEMO_GWP",
          resource_id: 509,
          resource_code: "resource_509",
          sql_query: "[Quantity] > 0",
        },
        {
          name: "J_DEMO_CRD",
          resource_id: 510,
          resource_code: "resource_column_318",
          sql_query: "[QTY_SOD] > $$Var_variable$$",
        },
      ],
      rule_execution_report_name: null,
      validation_execution_report_name: null,
      inline_variables: {
        rule_variables: {
          var_variable: "10",
        },
        resource_variables: [
          {
            resource_id: 509,
            resource_vars: {
              var1: "3",
              var2: "200",
            },
          },
          {
            resource_id: 510,
            resource_vars: {
              var1: "1",
            },
          },
          {
            resource_id: 511,
            resource_vars: {
              var1: "1",
            },
          },
        ],
        comparison_research_query_variables: {
          test3: "7",
          newtestvar: "7",
        },
      },
      pivot_results: null,
      pull_latest_files: false,
      query_params: null,
      latest_execution_id: null,
      is_rerun: false,
    },
    no_of_errors_in_response: 0,
    no_of_sample_validation_errors: 0,
    no_of_errors_in_output_files: 0,
    no_of_errors_in_filter_record_output_files: 0,
    summary_mode: false,
    generate_files: true,
    skip_validation: false,
    save_input_data_file: false,
    execute_comparison_research_query: false,
    skip_duplicate_records: true,
    keep_downloaded_files: false,
    use_secondary_merge_resources: false,
    use_merge_on_keys_as_unique_keys: true,
    store_errors_snapshots_and_create_issues: true,
    additional_info: [
      {
        file_names: null,
        resource_id: 509,
        resource_column_details_version: 100,
        resource_version: 86,
        domain_version: 90,
      },
      {
        file_names: null,
        resource_id: 510,
        resource_column_details_version: 9,
        resource_version: 6,
        domain_version: 90,
      },
      {
        file_names: null,
        resource_id: 511,
        resource_column_details_version: 25,
        resource_version: 10,
        domain_version: 90,
      },
    ],
  },
  rule_id: 337,
  rule_version: "60",
  inline_variables: {
    rule_variables: null,
    resource_variables: null,
    comparison_research_query_variables: null,
  },
};
