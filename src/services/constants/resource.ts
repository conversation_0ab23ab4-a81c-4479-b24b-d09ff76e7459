export const validationReRunMockDataOld: any = {
  validation_parameters: {
    resource_data: {
      resource_id: 913,
      linked_service_id: 27,
      linked_service_code: "localFile1",
      connection_key: 28,
      resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      column_delimiter: "|",
      aggregation_base_resource_data: null,
      additional_base_resource_data: [
        {
          resource_id: 1408,
          linked_service_id: 27,
          linked_service_code: "localFile1",
          connection_key: 28,
          resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
          file_name: "AdditionalResource_3.xlsx",
          column_delimiter: ",",
          file_pre_processing: {
            file_format: "xlsx",
            schema_definition: "string",
            field_delimiter: "|",
            custom_attributes: {
              type: "string",
              store_name: "string",
              store_label: "string",
            },
            has_footer: false,
            footer_lines: 0,
            has_header: true,
            header_lines: 0,
            skip_rows: 0,
            has_multiple_sheets: false,
            skip_blanks: true,
            compression: null,
            compression_codec: null,
            line_terminator: "\n",
            has_comments: "false",
            comments_marker: "#",
            encoding: "utf-8",
            bad_lines: "skip",
          },
          api_request: {
            user_name: null,
            password: null,
            bearer_token: null,
            api_key: null,
            oauth_client_id: null,
            oauth_client_secret: null,
            oauth_url: null,
            method: "get",
            content_type: "application/json",
            body: null,
            query_params: null,
            url_params: null,
            url: null,
            request_timeout: 0,
          },
        },
      ],
      file_pre_processing: {
        file_format: "xlsx",
        schema_definition: "string",
        field_delimiter: "|",
        custom_attributes: {
          type: "string",
          store_name: "string",
          store_label: "string",
        },
        has_footer: false,
        footer_lines: 0,
        has_header: true,
        header_lines: 0,
        skip_rows: 0,
        has_multiple_sheets: false,
        skip_blanks: true,
        compression: null,
        compression_codec: null,
        line_terminator: "\n",
        has_comments: "false",
        comments_marker: "#",
        encoding: "utf-8",
        bad_lines: "skip",
      },
      filter_rules: [
        {
          name: "Total cost base filter",
          sql_query: "[$$column1$$] > $$var5$$",
          id: 0.8538673327511133,
        },
      ],
      api_request: {
        user_name: null,
        password: null,
        bearer_token: null,
        api_key: null,
        oauth_client_id: null,
        oauth_client_secret: null,
        oauth_url: null,
        method: "get",
        content_type: "application/json",
        body: null,
        query_params: null,
        url_params: null,
        url: null,
        request_timeout: 0,
      },
    },
    sampling_validation: null,
    sample_size: null,
    output_storage_params: {
      output_storage_linked_service_id: "106",
      output_storage_linked_service_code: "apitokenauth",
      output_storage_connection_key: "2",
      output_storage_base_file_path:
        "C:\\inetpub\\wwwroot\\VIS\\download-files123",
    },
    run_instance: null,
    severity_level: null,
    severity_column_names: null,
    validation_execution_report_name: "test",
    inline_variables: null,
    pull_latest_files: false,
    query_params: null,
    latest_execution_id: 59151,
    is_rerun: true,
  },
  no_of_errors_in_response: 10,
  no_of_sample_validation_errors: 0,
  no_of_errors_in_output_files: 0,
  no_of_errors_in_filter_record_output_files: 0,
  summary_mode: false,
  generate_files: true,
  save_input_data_file: false,
  skip_duplicate_records: true,
  keep_downloaded_files: false,
  file_names: [],
  resource_id: 913,
  resource_column_details_version: 37,
  resource_version: 31,
  domain_version: 90,
  store_errors_snapshots_and_create_issues: true,
  inline_variables: {},
};
export const validationReRunMockData: any = {
  validation_request_body: {
    resource_data: {
      resource_id: 913,
      linked_service_id: 27,
      linked_service_code: "localFile1",
      connection_key: 28,
      resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      column_delimiter: "|",
      resource_prefix: null,
      sql_query: null,
      container_name: null,
      file_pre_processing: {
        file_format: "xlsx",
        schema_definition: "string",
        field_delimiter: "|",
        custom_attributes: {
          type: "string",
          store_name: "string",
          store_label: "string",
        },
        has_footer: false,
        footer_lines: 0,
        has_header: true,
        header_lines: 0,
        skip_rows: 0,
        has_multiple_sheets: false,
        skip_blanks: true,
        compression: null,
        compression_codec: null,
        line_terminator: "\n",
        has_comments: "false",
        comments_marker: "#",
        encoding: "utf-8",
        bad_lines: "skip",
      },
      filter_rules: [
        {
          name: "Total cost base filter",
          sql_query: "[$$column1$$] > $$var5$$",
        },
      ],
      api_request: null,
      aggregation_properties: null,
      aggregation_base_resource_data: null,
      additional_base_resource_data: [
        {
          resource_id: 1408,
          linked_service_id: 27,
          linked_service_code: "localFile1",
          connection_key: 28,
          resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
          file_name: "AdditionalResource_3.xlsx",
          column_delimiter: ",",
          resource_prefix: null,
          sql_query: null,
          container_name: null,
          file_pre_processing: null,
          filter_rules: null,
          api_request: null,
          aggregation_properties: null,
          aggregation_base_resource_data: null,
          additional_base_resource_data: null,
          secondary_merge_resource: null,
          pre_processing_request: null,
        },
      ],
      secondary_merge_resource: null,
      pre_processing_request: null,
    },
    sampling_validation: null,
    sample_size: null,
    output_storage_params: {
      output_storage_linked_service_id: 27,
      output_storage_linked_service_code: null,
      output_storage_connection_key: 28,
      output_storage_base_file_path: "/home/<USER>/Desktop/VIS-Results2123",
    },
    run_instance: null,
    severity_level: null,
    severity_column_names: null,
    validation_execution_report_name: null,
    inline_variables: {
      var5: 1500,
      column1: "Total Cost Base",
    },
    pull_latest_files: false,
    query_params: null,
    latest_execution_id: null,
    is_rerun: false,
  },
  no_of_errors_in_response: 100000,
  no_of_sample_validation_errors: 0,
  no_of_errors_in_output_files: 0,
  no_of_errors_in_filter_record_output_files: 0,
  summary_mode: false,
  generate_files: true,
  save_input_data_file: false,
  skip_duplicate_records: true,
  keep_downloaded_files: false,
  file_names: ["GWP_Position_202212124_Change_int.xlsx"],
  resource_id: 913,
  resource_column_details_version: 37,
  resource_version: 31,
  domain_version: 90,
  store_errors_snapshots_and_create_issues: true,
};
export const reRunReqBody: any = {
  resource_data: [
    {
      aggregation_type: "flat",
      file_processing_code: null,
      domain_id: 5,
      additional_properties: {
        resource_definition: {
          type: "local",
          local_definition: {
            resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
            file_name: "GWP_Position_202212124_Change_int.xlsx",
            column_delimiter: "|",
            connection_key: 28,
            connection_key_code: "connection_key_28",
          },
          blob_definition: null,
          sql_definition: null,
          sftp_definition: null,
          api_definition: {
            request_timeout: 0,
          },
          resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          column_delimiter: "|",
          connection_key: 28,
          connection_key_code: "connection_key_28",
        },
        aggregation_properties: null,
        resource_column_details_id: 602,
        resource_column_details_code: "resource_913",
        additional_resource_data: [
          {
            resource_id: 1408,
            resource_code: "resource_1408",
            base_column_names: ["Security Code"],
            add_on_column_names: ["FishSecID"],
            merge_type: "outer",
            generate_derived_columns_before_merge_additional_resource: null,
          },
        ],
        filter_rules: [
          {
            name: "Total cost base filter",
            sql_query: "[$$column1$$] > $$var5$$",
          },
        ],
        inline_variables: {
          VAr5: 1500,
          COlumn1: "Total Cost Base",
          Var1: 1600,
          var2: 1700,
          var3: 1000,
          var4: 50,
          column4: "Market Value Base",
          colUMN5: "Quantity",
          column6: "Unit Cost Base",
          source_type: "GWPPOSTYP",
          custom_vAl_col: "FishIndustry",
          var10: 4,
        },
        updated_additional_resource_data: [
          {
            aggregation_type: "flat",
            file_processing_code: "file_processing_4",
            domain_id: 5,
            additional_properties: {
              resource_definition: {
                type: "local",
                local_definition: {
                  resource_path:
                    "E:\\Data_Files\\SampleFiles\\Research_query_files",
                  file_name: "AdditionalResource_3.xlsx",
                  column_delimiter: ",",
                  connection_key: 28,
                  connection_key_code: "connection_key_28",
                },
                blob_definition: null,
                sql_definition: null,
                sftp_definition: null,
                api_definition: null,
                resource_path:
                  "E:\\Data_Files\\SampleFiles\\Research_query_files",
                file_name: "AdditionalResource_3.xlsx",
                column_delimiter: ",",
                connection_key: 28,
                connection_key_code: "connection_key_28",
              },
              aggregation_properties: null,
              resource_column_details_id: 1090,
              resource_column_details_code: "resource_column_1090",
              additional_resource_data: [],
              filter_rules: [],
              inline_variables: {
                custom_val_col: "FishIndustry",
                var10: 4,
              },
            },
            domain_code: "POS!",
            is_active: true,
            resource_name: "DEMO2_ADDITIONAL_RES",
            domain_name: "Position",
            created_on: "2024-02-14T17:04:13",
            id: 1408,
            linked_service_id: 27,
            updated_on: "2024-11-25T13:15:31",
            resource_type: "DEMO_Add_Resource",
            linked_service_code: "localFile1",
            created_by: 24,
            resource_prefix: "DEMO_Add_Resource",
            current_url: null,
            modified_by: 132,
            code: "resource_1408",
            file_processing_id: 4,
            version: 14,
            created_by_user: {
              username: "RK",
              id: 24,
              email: "<EMAIL>",
              firstname: "R",
              lastname: "K",
            },
            modified_by_user: {
              username: "jitendra_12",
              id: 132,
              email: "<EMAIL>",
              firstname: "JItendra",
              lastname: "Sankhla",
            },
          },
        ],
      },
      domain_code: "POS!",
      is_active: true,
      resource_name: "DEMO_GWP",
      domain_name: "Position!",
      created_on: "2022-09-20T00:00:00",
      id: 913,
      linked_service_id: 27,
      updated_on: "2024-11-26T05:55:51",
      resource_type: "GWP@",
      linked_service_code: "localFile1",
      created_by: 3,
      resource_prefix: "GWP@",
      current_url: null,
      modified_by: 132,
      code: "resource_913",
      file_processing_id: 4,
      version: 31,
      created_by_user: {
        username: "vis123",
        id: 3,
        email: "<EMAIL>",
        firstname: "Vis",
        lastname: "test",
      },
      modified_by_user: {
        username: "jitendra_12",
        id: 132,
        email: "<EMAIL>",
        firstname: "JItendra",
        lastname: "Sankhla",
      },
    },
    {
      aggregation_type: "flat",
      file_processing_code: null,
      domain_id: 5,
      additional_properties: {
        resource_definition: {
          type: "local",
          local_definition: {
            resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
            file_name: "CRD_Position_202212124_Change_int.xlsx",
            column_delimiter: "|",
            connection_key: 28,
            connection_key_code: "connection_key_28",
          },
          blob_definition: null,
          sql_definition: null,
          sftp_definition: null,
          api_definition: {
            request_timeout: 0,
          },
          resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
          file_name: "CRD_Position_202212124_Change_int.xlsx",
          column_delimiter: "|",
          connection_key: 28,
          connection_key_code: "connection_key_28",
        },
        aggregation_properties: null,
        resource_column_details_id: 603,
        resource_column_details_code: "resource_column_603",
        additional_resource_data: [],
        filter_rules: [],
        inline_variables: {
          VAr5: 1500,
          COlumn1: "Total Cost Base",
          Var1: 1600,
          var2: 1700,
          var3: 1000,
          var4: 50,
          column4: "Market Value Base",
          colUMN5: "Quantity",
          column6: "Unit Cost Base",
          source_type: "GWPPOSTYP",
          custom_vAl_col: "FishIndustry",
          variable_value1: "MKT_VAL_SOD",
          var10: 4,
        },
      },
      domain_code: "POS!",
      is_active: true,
      resource_name: "DEMO_CRD",
      domain_name: "Position!",
      created_on: "2022-09-21T00:00:00",
      id: 914,
      linked_service_id: 27,
      updated_on: "2024-12-23T09:55:25",
      resource_type: "CRD",
      linked_service_code: "localFile1",
      created_by: 3,
      resource_prefix: "CRD",
      current_url: null,
      modified_by: 132,
      code: "resource_914",
      file_processing_id: 4,
      version: 11,
      created_by_user: {
        username: "vis123",
        id: 3,
        email: "<EMAIL>",
        firstname: "Vis",
        lastname: "test",
      },
      modified_by_user: {
        username: "jitendra_12",
        id: 132,
        email: "<EMAIL>",
        firstname: "JItendra",
        lastname: "Sankhla",
      },
    },
    {
      aggregation_type: "flat",
      file_processing_code: null,
      domain_id: 5,
      additional_properties: {
        resource_definition: {
          type: "local",
          local_definition: {
            resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
            file_name: "FactSet_Position_202212124_Change_int.xlsx",
            column_delimiter: "|",
            connection_key: 28,
            connection_key_code: "connection_key_28",
          },
          blob_definition: null,
          sql_definition: null,
          sftp_definition: null,
          api_definition: {
            request_timeout: 0,
          },
          resource_path: "E:\\Data_Files\\SampleFiles\\Research_query_files",
          file_name: "FactSet_Position_202212124_Change_int.xlsx",
          column_delimiter: "|",
          connection_key: 28,
          connection_key_code: "connection_key_28",
        },
        aggregation_properties: null,
        resource_column_details_id: 604,
        resource_column_details_code: "resource_column_604",
        additional_resource_data: [],
        filter_rules: [],
        inline_variables: {
          VAr5: 1500,
          COlumn1: "Total Cost Base",
          Var1: 1600,
          var2: 1700,
          var3: 1000,
          var4: 50,
          column4: "Market Value Base",
          colUMN5: "Quantity",
          column6: "Unit Cost Base",
          source_type: "GWPPOSTYP",
          custom_vAl_col: "FishIndustry",
          var10: 4,
        },
      },
      domain_code: "POS!",
      is_active: true,
      resource_name: "DEMO_FACTSET",
      domain_name: "Position!",
      created_on: "2022-09-22T00:00:00",
      id: 915,
      linked_service_id: 27,
      updated_on: "2024-12-10T06:03:19",
      resource_type: "Factset",
      linked_service_code: "localFile1",
      created_by: 3,
      resource_prefix: "Factset",
      current_url: null,
      modified_by: 50,
      code: "resource_915",
      file_processing_id: 4,
      version: 7,
      created_by_user: {
        username: "vis123",
        id: 3,
        email: "<EMAIL>",
        firstname: "Vis",
        lastname: "test",
      },
      modified_by_user: {
        username: "kirarnaveen@",
        id: 50,
        email: "<EMAIL>",
        firstname: "Naveen",
        lastname: "Kirar",
      },
    },
    {
      aggregation_type: "flat",
      file_processing_code: "file_processing_4",
      domain_id: 5,
      additional_properties: {
        resource_definition: {
          type: "local",
          local_definition: {
            resource_path: "D:\\Uploads",
            file_name: "ExternalResource_1735543455078.xlsx",
            column_delimiter: "|",
            connection_key: 28,
            connection_key_code: "connection_key_28",
          },
          blob_definition: null,
          sql_definition: null,
          sftp_definition: null,
          api_definition: {
            request_timeout: 0,
          },
          resource_path: "D:\\Uploads",
          file_name: "ExternalResource_1735543455078.xlsx",
          column_delimiter: "|",
          connection_key: 28,
          connection_key_code: "connection_key_28",
        },
        aggregation_properties: null,
        resource_column_details_id: 602,
        resource_column_details_code: "resource_913",
        additional_resource_data: [
          {
            resource_id: 1408,
            resource_code: "resource_1408",
            base_column_names: ["Security Code"],
            add_on_column_names: ["FishSecID"],
            merge_type: "outer",
            generate_derived_columns_before_merge_additional_resource: null,
          },
        ],
        filter_rules: [
          {
            name: "Total cost base filter",
            sql_query: "[$$column1$$] > $$var5$$",
          },
        ],
        inline_variables: {
          VAr5: 1500,
          COlumn1: "Total Cost Base",
          Var1: 1600,
          var2: 1700,
          var3: 1000,
          var4: 50,
          column4: "Market Value Base",
          colUMN5: "Quantity",
          column6: "Unit Cost Base",
          source_type: "GWPPOSTYP",
          custom_vAl_col: "FishIndustry",
          var10: 4,
        },
        updated_additional_resource_data: [
          {
            aggregation_type: "flat",
            file_processing_code: "file_processing_4",
            domain_id: 5,
            additional_properties: {
              resource_definition: {
                type: "local",
                local_definition: {
                  resource_path:
                    "E:\\Data_Files\\SampleFiles\\Research_query_files",
                  file_name: "AdditionalResource_3.xlsx",
                  column_delimiter: ",",
                  connection_key: 28,
                  connection_key_code: "connection_key_28",
                },
                blob_definition: null,
                sql_definition: null,
                sftp_definition: null,
                api_definition: null,
                resource_path:
                  "E:\\Data_Files\\SampleFiles\\Research_query_files",
                file_name: "AdditionalResource_3.xlsx",
                column_delimiter: ",",
                connection_key: 28,
                connection_key_code: "connection_key_28",
              },
              aggregation_properties: null,
              resource_column_details_id: 1090,
              resource_column_details_code: "resource_column_1090",
              additional_resource_data: [],
              filter_rules: [],
              inline_variables: {
                custom_val_col: "FishIndustry",
                var10: 4,
              },
            },
            domain_code: "POS!",
            is_active: true,
            resource_name: "DEMO2_ADDITIONAL_RES",
            domain_name: "Position",
            created_on: "2024-02-14T17:04:13",
            id: 1408,
            linked_service_id: 27,
            updated_on: "2024-11-25T13:15:31",
            resource_type: "DEMO_Add_Resource",
            linked_service_code: "localFile1",
            created_by: 24,
            resource_prefix: "DEMO_Add_Resource",
            current_url: null,
            modified_by: 132,
            code: "resource_1408",
            file_processing_id: 4,
            version: 14,
            created_by_user: {
              username: "RK",
              id: 24,
              email: "<EMAIL>",
              firstname: "R",
              lastname: "K",
            },
            modified_by_user: {
              username: "jitendra_12",
              id: 132,
              email: "<EMAIL>",
              firstname: "JItendra",
              lastname: "Sankhla",
            },
          },
        ],
      },
      domain_code: "POS!",
      is_active: true,
      resource_name: "DEMO_GWP_clone",
      domain_name: "Position!",
      created_on: "2024-08-26T08:02:14",
      id: 2052,
      linked_service_id: 27,
      updated_on: "2024-12-30T07:24:22",
      resource_type: "GWP@",
      linked_service_code: "localFile1",
      created_by: 24,
      resource_prefix: "GWP@_clone",
      current_url: "https://172.176.208.22/resource/5/view/2052",
      modified_by: 32,
      code: "resource_913_clone",
      file_processing_id: 4,
      version: 4,
      created_by_user: {
        username: "RK",
        id: 24,
        email: "<EMAIL>",
        firstname: "R",
        lastname: "K",
      },
      modified_by_user: {
        username: "jitendra.sankhla",
        id: 32,
        email: "<EMAIL>",
        firstname: "Jitendra",
        lastname: "Sankhla",
      },
    },
  ],
  output_storage_params: {
    linked_service_id: "27",
    linked_service_code: "localFile1",
    connection_key_id: "28",
    file_path: "C:\\inetpub\\wwwroot\\VIS\\download-files",
  },
  severity_column_names: {
    low: null,
    medium: null,
    high: null,
  },
  run_instance: {
    no_of_errors_in_response: 0,
    no_of_errors_in_output_files: 0,
    skip_duplicate_records: true,
    summary_mode: false,
    generate_files: true,
    skip_validation: false,
    validation_severity_level: "Low",
    run_instance: {
      run_id: 1,
      run_name: "Legacy",
    },
    save_input_data_file: false,
    execute_comparison_research_query: false,
    use_secondary_merge_resources: false,
    store_errors_snapshots_and_create_issues: true,
  },
  filter_rules: {
    merge_rule: {
      primary_dataset: {
        resource_id: 913,
        resource_code: "resource_913",
        merge_on: ["Portfolio Code", "Security Code"],
        possible_merge_on_columns: {
          "Portfolio Code": null,
          "Security Code": null,
        },
        secondary_merge_resource: null,
      },
      secondary_datasets: [
        {
          resource_id: 915,
          resource_code: "resource_915",
          merge_on: ["Portfolio Table", "Fisher Sec ID"],
          possible_merge_on_columns: {
            "Portfolio Table": null,
            "Fisher Sec ID": null,
          },
          secondary_merge_resource: null,
          merge_type: "outer",
        },
        {
          resource_id: 914,
          resource_code: "resource_914",
          merge_on: ["ACCT_CD", "EXT_SEC_ID"],
          possible_merge_on_columns: {
            ACCT_CD: null,
            EXT_SEC_ID: null,
          },
          secondary_merge_resource: null,
          merge_type: "outer",
        },
      ],
    },
    filter_rules: [
      {
        name: "DEMO_FACTSET",
        resource_id: 915,
        resource_code: "resource_915",
        sql_query: "[Cost] >200",
      },
      {
        name: "filter rule 2",
        resource_id: 913,
        resource_code: "resource_913",
        sql_query: "[Total Cost Base] > 1700",
      },
    ],
    domain_comparison_rules: [
      {
        name: "Quantity",
        column_name: "Quantity",
        comparison_type: "equals",
        tolerance_type: null,
        tolerance_value: null,
      },
    ],
    custom_comparison_rules: [
      {
        name: "Quantity Comparison",
        left_operand: {
          type: "column",
          value: null,
          resource_id: 913,
          resource_code: "resource_913",
          column_name: "Quantity",
        },
        right_operand: {
          type: "column",
          value: null,
          resource_id: 914,
          resource_code: "resource_914",
          column_name: "Quantity",
        },
        comparison_type: "equals",
        filter_rules: [],
        tolerance_type: null,
        tolerance_value: null,
      },
      {
        name: "market value Comparison",
        left_operand: {
          type: "column",
          value: null,
          resource_id: 914,
          resource_code: "resource_914",
          column_name: "MKT_VAL_SOD",
        },
        right_operand: {
          type: "column",
          value: null,
          resource_id: 915,
          resource_code: "resource_915",
          column_name: "MKT_VAL_SOD",
        },
        comparison_type: "equals",
        filter_rules: [],
        tolerance_type: null,
        tolerance_value: null,
      },
    ],
    adhoc_queries: [
      {
        name: "Unrealized gain loss limit",
        sql_query:
          "SELECT * FROM <TABLE_NAME> WHERE [Factset.UNREALIZED_GAIN_LOSS] > 2500",
      },
      {
        name: "Cost Limit",
        sql_query: "SELECT * FROM <TABLE_NAME> WHERE [CRD.COST] > 2000",
      },
    ],
    resources: [915, 914, 913],
    resources_detailed_list: [
      {
        resource_id: 913,
        resource_code: "resource_913",
      },
      {
        resource_id: 915,
        resource_code: "resource_915",
      },
      {
        resource_id: 914,
        resource_code: "resource_914",
      },
    ],
    overriden_prefix: null,
    inline_variables: {},
  },
  inline_variables: {
    rule_variables: {
      filterRule: {},
      adhoc_queries: {},
    },
    resource_variables: [
      {
        resource_id: 913,
        resource_vars: {
          VAr5: 1500,
          COlumn1: "Total Cost Base",
          Var1: 1600,
          var2: 1700,
          var3: 1000,
          var4: 50,
          column4: "Market Value Base",
          colUMN5: "Quantity",
          column6: "Unit Cost Base",
          source_type: "GWPPOSTYP",
          custom_vAl_col: "FishIndustry",
          var10: 4,
        },
        resource_name: "DEMO_GWP",
      },
      {
        resource_id: 914,
        resource_vars: {
          VAr5: 1500,
          COlumn1: "Total Cost Base",
          Var1: 1600,
          var2: 1700,
          var3: 1000,
          var4: 50,
          column4: "Market Value Base",
          colUMN5: "Quantity",
          column6: "Unit Cost Base",
          source_type: "GWPPOSTYP",
          custom_vAl_col: "FishIndustry",
          variable_value1: "MKT_VAL_SOD",
          var10: 4,
        },
        resource_name: "DEMO_CRD",
      },
      {
        resource_id: 915,
        resource_vars: {
          VAr5: 1500,
          COlumn1: "Total Cost Base",
          Var1: 1600,
          var2: 1700,
          var3: 1000,
          var4: 50,
          column4: "Market Value Base",
          colUMN5: "Quantity",
          column6: "Unit Cost Base",
          source_type: "GWPPOSTYP",
          custom_vAl_col: "FishIndustry",
          var10: 4,
        },
        resource_name: "DEMO_FACTSET",
      },
      {
        resource_id: 2052,
        resource_vars: {
          VAr5: 1500,
          COlumn1: "Total Cost Base",
          Var1: 1600,
          var2: 1700,
          var3: 1000,
          var4: 50,
          column4: "Market Value Base",
          colUMN5: "Quantity",
          column6: "Unit Cost Base",
          source_type: "GWPPOSTYP",
          custom_vAl_col: "FishIndustry",
          var10: 4,
        },
        resource_name: "DEMO_GWP_clone",
      },
    ],
    comparison_research_query_variables: {
      vars1: "Portfolio Code",
    },
  },
};

export const newResourceData: any = [
  {
    resource_name: "Fact",
    resource_path:
      "C:\\Users\\<USER>\\Desktop\\VIS\\DATA\\FactSet_Position_202212124.xlsx",
    domain_id: 1,
    domain_resource_mapping: {
      columns: [
        {
          domain_column_name: "AccountCode",
          resource_column_name: "Portfolio Table",
        },
        {
          domain_column_name: "SecurityID",
          resource_column_name: "Fisher Sec ID",
        },
        {
          domain_column_name: "LongShortCode",
          resource_column_name: "L/S",
        },
        {
          domain_column_name: "Quantity",
          resource_column_name: "Quantity",
        },
        {
          domain_column_name: "MarketValueBase",
          resource_column_name: "MKT_VAL_SOD",
        },
      ],
    },
    resource_type: "Local",
    id: 1,
    resource_prefix: "fact",
  },
  {
    resource_name: "CRD",
    resource_path:
      "C:\\Users\\<USER>\\Desktop\\VIS\\DATA\\CDR_Position_202212124.xlsx",
    domain_id: 1,
    domain_resource_mapping: {
      columns: [
        {
          domain_column_name: "AccountCode",
          resource_column_name: "ACCT_CD",
        },
        {
          domain_column_name: "SecurityID",
          resource_column_name: "EXT Sec ID",
        },
        {
          domain_column_name: "LongShortCode",
          resource_column_name: "L/S",
        },
        {
          domain_column_name: "Quantity",
          resource_column_name: "QTY SOD",
        },
        {
          domain_column_name: "MarketValueBase",
          resource_column_name: "MKT_VAL_SOD",
        },
      ],
    },
    resource_type: "Local",
    id: 2,
    resource_prefix: "fact",
  },
  {
    resource_name: "GWP",
    resource_path:
      "C:\\Users\\<USER>\\Desktop\\VIS\\DATA\\GWP_Position_202212124.xlsx",
    domain_id: 1,
    domain_resource_mapping: {
      columns: [
        {
          domain_column_name: "AccountCode",
          resource_column_name: "Portfolio Code",
        },
        {
          domain_column_name: "SecurityID",
          resource_column_name: "Security Code",
        },
        {
          domain_column_name: "LongShortCode",
          resource_column_name: "L/S",
        },
        {
          domain_column_name: "Quantity",
          resource_column_name: "Quantity",
        },
        {
          domain_column_name: "MarketValueBase",
          resource_column_name: "Market value base",
        },
      ],
    },
    resource_type: "Local",
    id: 3,
    resource_prefix: "fact",
  },
];

const result = [
  {
    resource_name: "Fact_1",
    resource_path: "D:\\VIS\\SampleFiles",
    domain_id: 3,
    domain_resource_mapping: {
      columns: [
        {
          domain_column_name: "AccountCode",
          resource_column_name: "Portfolio Table",
        },
        {
          domain_column_name: "SecurityID",
          resource_column_name: "Fisher Sec ID",
        },
        {
          domain_column_name: "LongShortCode",
          resource_column_name: "L/S",
        },
        {
          domain_column_name: "Quantity",
          resource_column_name: "Quantity",
        },
        {
          domain_column_name: "MarketValueBase",
          resource_column_name: "MKT_VAL_SOD",
        },
      ],
    },
    id: 2,
    resource_type: "Local",
    resource_prefix: "Fact",
  },
  {
    resource_name: "CRD",
    resource_path: "D:\\VIS\\SampleFiles",
    domain_id: 3,
    domain_resource_mapping: {
      columns: [
        {
          domain_column_name: "AccountCode",
          resource_column_name: "ACCT_CD",
        },
        {
          domain_column_name: "SecurityID",
          resource_column_name: "EXT_SEC_ID",
        },
        {
          domain_column_name: "LongShortCode",
          resource_column_name: "LONG_SHORT_IND",
        },
        {
          domain_column_name: "Quantity",
          resource_column_name: "QTY_SOD",
        },
        {
          domain_column_name: "MarketValueBase",
          resource_column_name: "MKT_VAL_SOD",
        },
      ],
    },
    id: 3,
    resource_type: "Local",
    resource_prefix: "crd",
  },
  {
    resource_name: "resource1",
    resource_path: "r1path",
    domain_id: 3,
    domain_resource_mapping: {
      columns: [
        {
          domain_column_name: "AccountCode",
          resource_column_name: "AccountCode",
        },
        {
          domain_column_name: "SecurityID",
          resource_column_name: "Security Code",
        },
        {
          domain_column_name: "Quantity",
          resource_column_name: "Alt Security Code",
        },
        {
          domain_column_name: "LongShortCode",
          resource_column_name: "LongShortCode",
        },
        {
          domain_column_name: "Quantity",
          resource_column_name: "Quantity",
        },
        {
          domain_column_name: "MarketValue",
          resource_column_name: "MarketValue",
        },
        {
          domain_column_name: "AccruedIncomeBase",
          resource_column_name: "OriginalFace",
        },
        {
          domain_column_name: "MarketValueBase",
          resource_column_name: "MarketValueBase",
        },
        {
          domain_column_name: "AccruedIncomeBase",
          resource_column_name: "AccruedIncomeBase",
        },
        {
          domain_column_name: "AccruedIncomeLocal",
          resource_column_name: "AccruedIncomeLocal",
        },
        {
          domain_column_name: "UnitCost",
          resource_column_name: "UnitCost",
        },
        {
          domain_column_name: "BaseUnitCost",
          resource_column_name: "BaseUnitCost",
        },
        {
          domain_column_name: "TotalCostBase",
          resource_column_name: "TotalCostBase",
        },
        {
          domain_column_name: "UnrealizedTotalGain",
          resource_column_name: "UnrealizedTotalGain",
        },
        {
          domain_column_name: "PositionEffectiveDate",
          resource_column_name: "PositionEffectiveDate",
        },
      ],
    },
    id: 4,
    resource_type: "local",
    resource_prefix: "r1p",
  },
  {
    resource_name: "FACTSET_DEMO",
    resource_path: "D:\\Projects\\vis-sample\\frontend\\public",
    domain_id: 3,
    domain_resource_mapping: {
      columns: [
        {
          domain_column_name: "AccountCode",
          resource_column_name: "AccountCode",
        },
        {
          domain_column_name: "SecurityID",
          resource_column_name: "SecurityID",
        },
        {
          domain_column_name: "LongShortCode",
          resource_column_name: "LongShortCode",
        },
        {
          domain_column_name: "Quantity",
          resource_column_name: "Quantity",
        },
        {
          domain_column_name: "MarketValueBase",
          resource_column_name: "MarketValueBase",
        },
        {
          domain_column_name: "AccruedIncomeBase",
          resource_column_name: "AccruedIncomeBase",
        },
        {
          domain_column_name: "BaseUnitCost",
          resource_column_name: "BaseUnitCost",
        },
        {
          domain_column_name: "TotalCostBase",
          resource_column_name: "TotalCostBase",
        },
        {
          domain_column_name: "UnrealizedTotalGain",
          resource_column_name: "UnrealizedTotalGain",
        },
        {
          domain_column_name: "PositionEffectiveDate",
          resource_column_name: "PositionEffectiveDate",
        },
        { domain_column_name: "", resource_column_name: "FXRate" },
        { domain_column_name: "", resource_column_name: "CUSIP" },
      ],
    },
    id: 16,
    resource_type: "local",
    resource_prefix: "FACT_DEMO",
  },
];

export const resourceCDMColumns = {
  id: 0,
  domain_column_name: "",
  column_name: "",
  datatype: "string",
  mandatory: "",
  data_format: "",
  key: "",
  is_active: true,
  severity_level: "Low",
  action: "",
  column_length: "",
  reference: "",
};
export const defaultResourceCDMColumns = {
  id: 0,
  domain_column: "",
  column_name: "file_name",
  datatype: "string",
  is_mandatory: false,
  data_format: "",
  key: false,
  is_active: true,
  action: "",
  column_length: "",
  severity_level: "Low",
  reference: false,
};
export const defaultEditResourceCDMColumns = {
  id: 0,
  domain_column: "",
  column_name: "file_name",
  datatype: "string",
  is_mandatory: false,
  data_format: "",
  key: false,
  is_active: true,
  action: "",
  column_length: "",
  severity_level: "Low",
  is_reference: null,
  reference_column_definition: null,
  custom_validations: null,
  is_derived: null,
  derived_column_definition: null,
};

export const editResourceCDMColumns = {
  id: 0,
  column_name: "",
  domain_column: "",
  datatype: "string",
  is_mandatory: false,
  data_format: "",
  column_length: null,
  is_derived: null,
  derived_column_definition: null,
  is_reference: null,
  reference_column_definition: null,
  custom_validations: null,
  key: false,
  is_active: true,
  severity_level: "Low",
};

export const validationErrorsMockData = {
  items: [
    {
      id: 592,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 1,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 1,
        "Security Code": "SEC1",
      },
      custom_validation_name: "",
      error_message:
        " Length of FishIndustry column should be less than or equals to 10",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 592,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 1,
        record_data: {
          ErrorMessage:
            " Length of FishIndustry column should be less than or equals to 10",
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 1,
          "Security Code": "SEC1",
          CustomValidationName: null,
          Quantity: 890,
          "Market Value Base": 2300,
          "Unit Cost Base": 20,
          "Total Cost Base": 9000,
          "Unrealized GainLoss": "14000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 593,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 2,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 1,
        "Security Code": "SEC1",
      },
      custom_validation_name: "cv1",
      error_message: 'cv1:Validation failed for LENGTH("FishIndustry") == 1',
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 593,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 2,
        record_data: {
          ErrorMessage: 'cv1:Validation failed for LENGTH("FishIndustry") == 1',
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 1,
          "Security Code": "SEC1",
          CustomValidationName: "cv1",
          Quantity: 890,
          "Market Value Base": 2300,
          "Unit Cost Base": 20,
          "Total Cost Base": 9000,
          "Unrealized GainLoss": "14000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 594,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 3,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 1,
        "Security Code": "SEC1",
      },
      custom_validation_name: "",
      error_message:
        " Data in FishIndustry can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 594,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 3,
        record_data: {
          ErrorMessage:
            " Data in FishIndustry can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 1,
          "Security Code": "SEC1",
          CustomValidationName: null,
          Quantity: 890,
          "Market Value Base": 2300,
          "Unit Cost Base": 20,
          "Total Cost Base": 9000,
          "Unrealized GainLoss": "14000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 595,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 4,
      column_name: "Quantity",
      resource_keys: {
        "Portfolio Code": 1,
        "Security Code": "SEC1",
      },
      custom_validation_name: "cv12",
      error_message: 'cv12:Validation failed for "Quantity" > 1600',
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 595,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 4,
        record_data: {
          ErrorMessage: 'cv12:Validation failed for "Quantity" > 1600',
          Severity: "high_severity",
          "Invalid Column": "Quantity",
          "Portfolio Code": 1,
          "Security Code": "SEC1",
          CustomValidationName: "cv12",
          Quantity: 890,
          "Market Value Base": 2300,
          "Unit Cost Base": 20,
          "Total Cost Base": 9000,
          "Unrealized GainLoss": "14000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 596,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 5,
      column_name: "FishSecID",
      resource_keys: {
        "Portfolio Code": 1,
        "Security Code": "SEC1",
      },
      custom_validation_name: "",
      error_message:
        " Length of FishSecID column should be less than or equals to 10",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 596,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 5,
        record_data: {
          ErrorMessage:
            " Length of FishSecID column should be less than or equals to 10",
          Severity: "high_severity",
          "Invalid Column": "FishSecID",
          "Portfolio Code": 1,
          "Security Code": "SEC1",
          CustomValidationName: null,
          Quantity: 890,
          "Market Value Base": 2300,
          "Unit Cost Base": 20,
          "Total Cost Base": 9000,
          "Unrealized GainLoss": "14000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 597,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 6,
      column_name: "FishSecID",
      resource_keys: {
        "Portfolio Code": 1,
        "Security Code": "SEC1",
      },
      custom_validation_name: "",
      error_message:
        " Data in FishSecID can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 597,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 6,
        record_data: {
          ErrorMessage:
            " Data in FishSecID can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "FishSecID",
          "Portfolio Code": 1,
          "Security Code": "SEC1",
          CustomValidationName: null,
          Quantity: 890,
          "Market Value Base": 2300,
          "Unit Cost Base": 20,
          "Total Cost Base": 9000,
          "Unrealized GainLoss": "14000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 598,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 7,
      column_name: "FishSecID",
      resource_keys: {
        "Portfolio Code": 2,
        "Security Code": "SEC2",
      },
      custom_validation_name: "",
      error_message:
        " Length of FishSecID column should be less than or equals to 10",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 598,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 7,
        record_data: {
          ErrorMessage:
            " Length of FishSecID column should be less than or equals to 10",
          Severity: "high_severity",
          "Invalid Column": "FishSecID",
          "Portfolio Code": 2,
          "Security Code": "SEC2",
          CustomValidationName: null,
          Quantity: null,
          "Market Value Base": 1000,
          "Unit Cost Base": 40,
          "Total Cost Base": 5000,
          "Unrealized GainLoss": "4000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 599,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 8,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 2,
        "Security Code": "SEC2",
      },
      custom_validation_name: "cv1",
      error_message: 'cv1:Validation failed for LENGTH("FishIndustry") == 1',
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 599,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 8,
        record_data: {
          ErrorMessage: 'cv1:Validation failed for LENGTH("FishIndustry") == 1',
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 2,
          "Security Code": "SEC2",
          CustomValidationName: "cv1",
          Quantity: null,
          "Market Value Base": 1000,
          "Unit Cost Base": 40,
          "Total Cost Base": 5000,
          "Unrealized GainLoss": "4000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 600,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 9,
      column_name: "Quantity",
      resource_keys: {
        "Portfolio Code": 2,
        "Security Code": "SEC2",
      },
      custom_validation_name: "",
      error_message:
        " Data in Quantity can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 600,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 9,
        record_data: {
          ErrorMessage:
            " Data in Quantity can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "Quantity",
          "Portfolio Code": 2,
          "Security Code": "SEC2",
          CustomValidationName: null,
          Quantity: null,
          "Market Value Base": 1000,
          "Unit Cost Base": 40,
          "Total Cost Base": 5000,
          "Unrealized GainLoss": "4000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 601,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 10,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 2,
        "Security Code": "SEC2",
      },
      custom_validation_name: "",
      error_message:
        " Data in FishIndustry can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 601,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 10,
        record_data: {
          ErrorMessage:
            " Data in FishIndustry can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 2,
          "Security Code": "SEC2",
          CustomValidationName: null,
          Quantity: null,
          "Market Value Base": 1000,
          "Unit Cost Base": 40,
          "Total Cost Base": 5000,
          "Unrealized GainLoss": "4000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 602,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 11,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 2,
        "Security Code": "SEC2",
      },
      custom_validation_name: "",
      error_message:
        " Length of FishIndustry column should be less than or equals to 10",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 602,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 11,
        record_data: {
          ErrorMessage:
            " Length of FishIndustry column should be less than or equals to 10",
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 2,
          "Security Code": "SEC2",
          CustomValidationName: null,
          Quantity: null,
          "Market Value Base": 1000,
          "Unit Cost Base": 40,
          "Total Cost Base": 5000,
          "Unrealized GainLoss": "4000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 603,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 12,
      column_name: "FishSecID",
      resource_keys: {
        "Portfolio Code": 2,
        "Security Code": "SEC2",
      },
      custom_validation_name: "",
      error_message:
        " Data in FishSecID can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 603,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 12,
        record_data: {
          ErrorMessage:
            " Data in FishSecID can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "FishSecID",
          "Portfolio Code": 2,
          "Security Code": "SEC2",
          CustomValidationName: null,
          Quantity: null,
          "Market Value Base": 1000,
          "Unit Cost Base": 40,
          "Total Cost Base": 5000,
          "Unrealized GainLoss": "4000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 604,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 13,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 4,
        "Security Code": "SEC4",
      },
      custom_validation_name: "",
      error_message:
        " Length of FishIndustry column should be less than or equals to 10",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 604,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 13,
        record_data: {
          ErrorMessage:
            " Length of FishIndustry column should be less than or equals to 10",
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 4,
          "Security Code": "SEC4",
          CustomValidationName: null,
          Quantity: 900,
          "Market Value Base": 5000,
          "Unit Cost Base": 50,
          "Total Cost Base": 4000,
          "Unrealized GainLoss": "5000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 605,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 14,
      column_name: "Quantity",
      resource_keys: {
        "Portfolio Code": 4,
        "Security Code": "SEC4",
      },
      custom_validation_name: "cv12",
      error_message: 'cv12:Validation failed for "Quantity" > 1600',
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 605,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 14,
        record_data: {
          ErrorMessage: 'cv12:Validation failed for "Quantity" > 1600',
          Severity: "high_severity",
          "Invalid Column": "Quantity",
          "Portfolio Code": 4,
          "Security Code": "SEC4",
          CustomValidationName: "cv12",
          Quantity: 900,
          "Market Value Base": 5000,
          "Unit Cost Base": 50,
          "Total Cost Base": 4000,
          "Unrealized GainLoss": "5000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 606,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 15,
      column_name: "FishSecID",
      resource_keys: {
        "Portfolio Code": 4,
        "Security Code": "SEC4",
      },
      custom_validation_name: "",
      error_message:
        " Data in FishSecID can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 606,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 15,
        record_data: {
          ErrorMessage:
            " Data in FishSecID can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "FishSecID",
          "Portfolio Code": 4,
          "Security Code": "SEC4",
          CustomValidationName: null,
          Quantity: 900,
          "Market Value Base": 5000,
          "Unit Cost Base": 50,
          "Total Cost Base": 4000,
          "Unrealized GainLoss": "5000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 607,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 16,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 4,
        "Security Code": "SEC4",
      },
      custom_validation_name: "cv1",
      error_message: 'cv1:Validation failed for LENGTH("FishIndustry") == 1',
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 607,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 16,
        record_data: {
          ErrorMessage: 'cv1:Validation failed for LENGTH("FishIndustry") == 1',
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 4,
          "Security Code": "SEC4",
          CustomValidationName: "cv1",
          Quantity: 900,
          "Market Value Base": 5000,
          "Unit Cost Base": 50,
          "Total Cost Base": 4000,
          "Unrealized GainLoss": "5000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 608,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 17,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 4,
        "Security Code": "SEC4",
      },
      custom_validation_name: "",
      error_message:
        " Data in FishIndustry can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 608,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 17,
        record_data: {
          ErrorMessage:
            " Data in FishIndustry can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 4,
          "Security Code": "SEC4",
          CustomValidationName: null,
          Quantity: 900,
          "Market Value Base": 5000,
          "Unit Cost Base": 50,
          "Total Cost Base": 4000,
          "Unrealized GainLoss": "5000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 609,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 18,
      column_name: "FishSecID",
      resource_keys: {
        "Portfolio Code": 4,
        "Security Code": "SEC4",
      },
      custom_validation_name: "",
      error_message:
        " Length of FishSecID column should be less than or equals to 10",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 609,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 18,
        record_data: {
          ErrorMessage:
            " Length of FishSecID column should be less than or equals to 10",
          Severity: "high_severity",
          "Invalid Column": "FishSecID",
          "Portfolio Code": 4,
          "Security Code": "SEC4",
          CustomValidationName: null,
          Quantity: 900,
          "Market Value Base": 5000,
          "Unit Cost Base": 50,
          "Total Cost Base": 4000,
          "Unrealized GainLoss": "5000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 610,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 19,
      column_name: "Quantity",
      resource_keys: {
        "Portfolio Code": 5,
        "Security Code": "SEC5",
      },
      custom_validation_name: "cv12",
      error_message: 'cv12:Validation failed for "Quantity" > 1600',
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 610,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 19,
        record_data: {
          ErrorMessage: 'cv12:Validation failed for "Quantity" > 1600',
          Severity: "high_severity",
          "Invalid Column": "Quantity",
          "Portfolio Code": 5,
          "Security Code": "SEC5",
          CustomValidationName: "cv12",
          Quantity: 400,
          "Market Value Base": 7000,
          "Unit Cost Base": 100,
          "Total Cost Base": 7000,
          "Unrealized GainLoss": "20000",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 611,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 20,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 5,
        "Security Code": "SEC5",
      },
      custom_validation_name: "cv1",
      error_message: 'cv1:Validation failed for LENGTH("FishIndustry") == 1',
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 611,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 20,
        record_data: {
          ErrorMessage: 'cv1:Validation failed for LENGTH("FishIndustry") == 1',
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 5,
          "Security Code": "SEC5",
          CustomValidationName: "cv1",
          Quantity: 400,
          "Market Value Base": 7000,
          "Unit Cost Base": 100,
          "Total Cost Base": 7000,
          "Unrealized GainLoss": "20000",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 612,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 21,
      column_name: "FishIndustry",
      resource_keys: {
        "Portfolio Code": 5,
        "Security Code": "SEC5",
      },
      custom_validation_name: "",
      error_message:
        " Length of FishIndustry column should be less than or equals to 10",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 612,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 21,
        record_data: {
          ErrorMessage:
            " Length of FishIndustry column should be less than or equals to 10",
          Severity: "high_severity",
          "Invalid Column": "FishIndustry",
          "Portfolio Code": 5,
          "Security Code": "SEC5",
          CustomValidationName: null,
          Quantity: 400,
          "Market Value Base": 7000,
          "Unit Cost Base": 100,
          "Total Cost Base": 7000,
          "Unrealized GainLoss": "20000",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 613,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 22,
      column_name: "FishSecID",
      resource_keys: {
        "Portfolio Code": 5,
        "Security Code": "SEC5",
      },
      custom_validation_name: "",
      error_message:
        " Length of FishSecID column should be less than or equals to 10",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 613,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 22,
        record_data: {
          ErrorMessage:
            " Length of FishSecID column should be less than or equals to 10",
          Severity: "high_severity",
          "Invalid Column": "FishSecID",
          "Portfolio Code": 5,
          "Security Code": "SEC5",
          CustomValidationName: null,
          Quantity: 400,
          "Market Value Base": 7000,
          "Unit Cost Base": 100,
          "Total Cost Base": 7000,
          "Unrealized GainLoss": "20000",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 614,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 23,
      column_name: "Position Effective Date",
      resource_keys: {
        "Portfolio Code": 5,
        "Security Code": "SEC5",
      },
      custom_validation_name: "",
      error_message: " Does not match the supplied datetime format mm/dd/yyyy",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 614,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 23,
        record_data: {
          ErrorMessage:
            " Does not match the supplied datetime format mm/dd/yyyy",
          Severity: "high_severity",
          "Invalid Column": "Position Effective Date",
          "Portfolio Code": 5,
          "Security Code": "SEC5",
          CustomValidationName: null,
          Quantity: 400,
          "Market Value Base": 7000,
          "Unit Cost Base": 100,
          "Total Cost Base": 7000,
          "Unrealized GainLoss": "20000",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 615,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 24,
      column_name: "Position Effective Date",
      resource_keys: {
        "Portfolio Code": 5,
        "Security Code": "SEC5",
      },
      custom_validation_name: "",
      error_message:
        " Data in Position Effective Date can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 615,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 24,
        record_data: {
          ErrorMessage:
            " Data in Position Effective Date can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "Position Effective Date",
          "Portfolio Code": 5,
          "Security Code": "SEC5",
          CustomValidationName: null,
          Quantity: 400,
          "Market Value Base": 7000,
          "Unit Cost Base": 100,
          "Total Cost Base": 7000,
          "Unrealized GainLoss": "20000",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
    {
      id: 616,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 25,
      column_name: "FishSecID",
      resource_keys: {
        "Portfolio Code": 5,
        "Security Code": "SEC5",
      },
      custom_validation_name: "",
      error_message:
        " Data in FishSecID can not be null or empty since it is a mandatory column",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 616,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 25,
        record_data: {
          ErrorMessage:
            " Data in FishSecID can not be null or empty since it is a mandatory column",
          Severity: "high_severity",
          "Invalid Column": "FishSecID",
          "Portfolio Code": 5,
          "Security Code": "SEC5",
          CustomValidationName: null,
          Quantity: 400,
          "Market Value Base": 7000,
          "Unit Cost Base": 100,
          "Total Cost Base": 7000,
          "Unrealized GainLoss": "20000",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          "Derived L/S": "S",
          file_name: "GWP_Position_202212124_Change_int.xlsx",
        },
      },
    },
  ],
  total: 70,
  page: 1,
  size: 25,
  pages: 3,
};

export const validationDuplicateErrorsMockData = {
  items: [
    {
      id: 30,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 1,
      resource_keys: {
        "Portfolio Code": 11,
        "Security Code": "SEC11",
      },
      error_message: "Duplicate Record",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 30,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 1,
        record_data: {
          "Portfolio Code": 11,
          "Security Code": "SEC11",
          Quantity: 1100,
          "Market Value Base": 2500,
          "Unit Cost Base": 130,
          "Total Cost Base": 3000,
          "Unrealized GainLoss": "1200",
          "Position Effective Date": "09-14-2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
          ColumnName: "",
          SeverityValue: "low_severity",
          ErrorMessage: "Duplicate Record",
        },
      },
    },
    {
      id: 31,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 2,
      resource_keys: {
        "Portfolio Code": 11,
        "Security Code": "SEC11",
      },
      error_message: "Duplicate Record",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 31,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 2,
        record_data: {
          "Portfolio Code": 11,
          "Security Code": "SEC11",
          Quantity: 1100,
          "Market Value Base": 2500,
          "Unit Cost Base": 130,
          "Total Cost Base": 3000,
          "Unrealized GainLoss": "1200",
          "Position Effective Date": "09-14-2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
          ColumnName: "",
          SeverityValue: "low_severity",
          ErrorMessage: "Duplicate Record",
        },
      },
    },
    {
      id: 32,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 3,
      resource_keys: {
        "Portfolio Code": 11,
        "Security Code": "SEC11",
      },
      error_message: "Duplicate Record",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 32,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 3,
        record_data: {
          "Portfolio Code": 11,
          "Security Code": "SEC11",
          Quantity: 1100,
          "Market Value Base": 2500,
          "Unit Cost Base": 130,
          "Total Cost Base": 3000,
          "Unrealized GainLoss": "1200",
          "Position Effective Date": "09-14-2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
          ColumnName: "",
          SeverityValue: "low_severity",
          ErrorMessage: "Duplicate Record",
        },
      },
    },
  ],
  total: 3,
  page: 1,
  size: 25,
  pages: 1,
};

export const crossFieldValidationsMockData = {
  items: [
    {
      id: 154,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 5,
          "Security Code": "SEC5",
          Quantity: 400,
          "Market Value Base": 7000,
          "Unit Cost Base": 100,
          "Total Cost Base": 7000,
          "Unrealized GainLoss": "20000",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "S",
        },
        resource_id: 132,
      },
    },
    {
      id: 155,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 6,
          "Security Code": "SEC6",
          Quantity: null,
          "Market Value Base": 2100,
          "Unit Cost Base": 60,
          "Total Cost Base": 6000,
          "Unrealized GainLoss": "-1000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "S",
        },
        resource_id: 132,
      },
    },

    {
      id: 156,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 7,
          "Security Code": "SEC7",
          Quantity: 800,
          "Market Value Base": 7000,
          "Unit Cost Base": 90,
          "Total Cost Base": 1700,
          "Unrealized GainLoss": "1100",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "S",
        },
        resource_id: 132,
      },
    },
    {
      id: 157,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 10,
          "Security Code": "SEC10",
          Quantity: 700,
          "Market Value Base": 1700,
          "Unit Cost Base": 80,
          "Total Cost Base": 1800,
          "Unrealized GainLoss": "2000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "S",
        },
        resource_id: 132,
      },
    },
    {
      id: 158,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 11,
          "Security Code": "SEC11",
          Quantity: 1100,
          "Market Value Base": 2500,
          "Unit Cost Base": 130,
          "Total Cost Base": 3000,
          "Unrealized GainLoss": "1200",
          "Position Effective Date": "09-14-2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
        },
        resource_id: 132,
      },
    },
    {
      id: 159,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 12,
          "Security Code": "SEC12",
          Quantity: 1700,
          "Market Value Base": 3100,
          "Unit Cost Base": 200,
          "Total Cost Base": 2100,
          "Unrealized GainLoss": "2700",
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
        },
        resource_id: 132,
      },
    },
    {
      id: 160,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 13,
          "Security Code": "SEC13",
          Quantity: null,
          "Market Value Base": 3000,
          "Unit Cost Base": 150,
          "Total Cost Base": 2000,
          "Unrealized GainLoss": "9000",
          "Position Effective Date": "09-14-2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "S",
        },
        resource_id: 132,
      },
    },
    {
      id: 161,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 11,
          "Security Code": "SEC11",
          Quantity: 1100,
          "Market Value Base": 2500,
          "Unit Cost Base": 130,
          "Total Cost Base": 3000,
          "Unrealized GainLoss": "1200",
          "Position Effective Date": "09-14-2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
        },
        resource_id: 132,
      },
    },
    {
      id: 162,
      execution_id: 24782,
      cross_field_validation_results: {
        name: "Cost Limit",
        result: {
          "Portfolio Code": 11,
          "Security Code": "SEC11",
          Quantity: 1100,
          "Market Value Base": 2500,
          "Unit Cost Base": 130,
          "Total Cost Base": 3000,
          "Unrealized GainLoss": "1200",
          "Position Effective Date": "09-14-2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
        },
        resource_id: 132,
      },
    },
  ],
  total: 9,
  page: 1,
  size: 25,
  pages: 1,
};

export const nullKeysValidationsMockData = {
  items: [
    {
      id: 221,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 1,
      error_message: "Unique Keys are Null",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 221,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 1,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: 500,
          "Market Value Base": 1200,
          "Unit Cost Base": 70,
          "Total Cost Base": 1300,
          "Unrealized GainLoss": "7000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 222,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 2,
      error_message: "Unique Keys are Null",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 222,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 2,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: 500,
          "Market Value Base": 61200,
          "Unit Cost Base": 70,
          "Total Cost Base": 1300,
          "Unrealized GainLoss": "67000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 223,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 3,
      error_message: "Unique Keys are Null",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 223,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 3,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: 1500,
          "Market Value Base": 61200,
          "Unit Cost Base": 670,
          "Total Cost Base": 41300,
          "Unrealized GainLoss": "7000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
        },
      },
    },
    {
      id: 224,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 4,
      error_message: "Unique Keys are Null",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 224,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 4,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: 5500,
          "Market Value Base": 1200,
          "Unit Cost Base": 70,
          "Total Cost Base": 31300,
          "Unrealized GainLoss": "7000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
        },
      },
    },
    {
      id: 225,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 5,
      error_message: "Unique Keys are Null",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 225,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 5,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: 6500,
          "Market Value Base": 1200,
          "Unit Cost Base": 70,
          "Total Cost Base": 1300,
          "Unrealized GainLoss": "7000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "L",
        },
      },
    },
    {
      id: 226,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 6,
      error_message: "Unique Keys are Null",
      file_name: "GWP_Position_202212124_Change_int.xlsx",
      record_snapshot: {
        id: 226,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 6,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: 500,
          "Market Value Base": 1200,
          "Unit Cost Base": 70,
          "Total Cost Base": 1300,
          "Unrealized GainLoss": "7000",
          "Position Effective Date": "09/14/2023",
          "Accrued Interest Base": null,
          FishIndustry: null,
          FishSecID: null,
          FishValue: null,
          file_name: "GWP_Position_202212124_Change_int.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 227,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 7,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 227,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 7,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "14",
          FishSecID: "SEC149",
          FishValue: "3",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 228,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 8,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 228,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 8,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "8",
          FishSecID: "SEC89",
          FishValue: "3",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 229,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 9,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 229,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 9,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "9",
          FishSecID: "SEC99",
          FishValue: "4",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 230,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 10,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 230,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 10,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "13",
          FishSecID: "SEC139",
          FishValue: "2",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 231,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 11,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 231,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 11,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "2",
          FishSecID: "SEC29",
          FishValue: "2",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 232,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 12,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 232,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 12,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "3",
          FishSecID: "SEC39",
          FishValue: "3",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 233,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 13,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 233,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 13,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "5",
          FishSecID: "SEC59",
          FishValue: "4",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 234,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 14,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 234,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 14,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "7",
          FishSecID: "SEC79",
          FishValue: "1",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 235,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 15,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 235,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 15,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "1",
          FishSecID: "SEC19",
          FishValue: "2",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 236,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 16,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 236,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 16,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "6",
          FishSecID: "SEC69",
          FishValue: "2",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 237,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 17,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 237,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 17,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "11",
          FishSecID: "SEC119",
          FishValue: "2",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 238,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 18,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 238,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 18,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "12",
          FishSecID: "SEC129",
          FishValue: "3",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 239,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 19,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 239,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 19,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "4",
          FishSecID: "SEC49",
          FishValue: "3",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
    {
      id: 240,
      execution_id: 24782,
      resource_id: 132,
      snapshot_sequence_id: 20,
      error_message: "Unique Keys are Null",
      file_name: "AdditionalResource_3.xlsx",
      record_snapshot: {
        id: 240,
        execution_id: 24782,
        resource_id: 132,
        sequence_id: 20,
        resource_data: {
          "Portfolio Code": null,
          "Security Code": null,
          Error_Message: "Unique Keys are Null",
          Quantity: null,
          "Market Value Base": null,
          "Unit Cost Base": null,
          "Total Cost Base": null,
          "Unrealized GainLoss": null,
          "Position Effective Date": null,
          "Accrued Interest Base": null,
          FishIndustry: "10",
          FishSecID: "SEC109",
          FishValue: "3",
          file_name: "AdditionalResource_3.xlsx",
          "Derived L/S": "S",
        },
      },
    },
  ],
  total: 20,
  page: 1,
  size: 25,
  pages: 1,
};
