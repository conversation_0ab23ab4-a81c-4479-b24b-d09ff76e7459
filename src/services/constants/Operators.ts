export const DerivedColumnOperators: any[] = [
  { name: '+', label: '+' },
  { name: '-', label: '-' },
  { name: '*', label: '*' },
  { name: '/', label: '/' },
  { name: '%', label: '%' },
  {
    name: 'concat',
    label: 'Concatenate',
    type: 'string',
    options: [],
    valueSources: ['field', 'value', 'func', 'constant', 'placeholder'],
    sqlFormatOp: (field: string, op: string, value: string) => `${field} = CONCAT(${field}, ${value})`,
  },
  {
    name: 'uppercase',
    label: 'Uppercase',
    type: 'string',
    options: [],
    valueSources: ['value'],
    sqlFormatOp: (field: string, op: string, value: string) => `${field} = UPPER(${value})`,
  },
  {
    name: 'sum',
    label: 'Sum',
    type: 'number',
    options: [],
    valueSources: ['value'],
    sqlFormatOp: (field: string, op: string, value: string) => `SUM(${field}) = ${value}`,
  },
  {
    name: 'case',
    label: 'CASE',
    type: 'string',
    options: [],
    valueSources: ['value', 'field'],
    sqlFormatOp: (field: string, op: string, value: any) =>
      `CASE WHEN (${value?.when}) THEN '${value?.then}' ELSE '${value?.else}' END`,
  },
];

export const customOperators: any[] = [
  { name: '+', label: '+' },
  { name: '-', label: '-' },
  { name: '*', label: '*' },
  { name: '/', label: '/' },
  { name: '%', label: '%' },
  { name: 'group_by', label: 'GROUP BY' },
  {
    name: 'order_by',
    label: 'ORDER BY',
    type: 'string',
    options: [],
    valueSources: ['field', 'value', 'func', 'constant', 'placeholder'],
    sqlFormatOp: (field: string, op: string, value: string) => `ORDER BY(${field})`,
  },
  {
    name: 'case',
    label: 'CASE',
    type: 'string',
    options: [],
    valueSources: ['value', 'field'],
    sqlFormatOp: (field: string, op: string, value: any) => `CASE WHEN (${value?.when}) THEN '${value?.then}' ELSE '${value?.else}' END`,
  },
];

export const defaultOperators: any = {
    'equals': '==',
    'not equals': '!=',
    'contains': 'contains',
    'not contains': 'not contains',
    'starts with': 'starts with',
    'ends with': 'ends with',
}

// related to linked services
export const localDefinition: any = {
  file_processing_id: "",
    local_definition: {
      resource_path: "",
      file_name: "",
      column_delimiter: "",
      connection_key: "",
    },
}
export const blobDefinition: any = {
  file_processing_id: "",
    local_definition: {
      connection_key: "",
      container_name: "",
      resource_path: "",
      file_name: "",
    },
}
export const sftpDefinition: any = {
  file_processing_id: "",
    local_definition: {
      connection_key: "",
      remote_directory: "",
      file_name: "",
    },
}
export const sqlDefinition: any = {
    sql_definition: {
      connection_key: "",
      sql_query: "",
    },
}