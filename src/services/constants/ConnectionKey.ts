export const CONNECTION_KEY_SERVICES_TYPES: any = [
  "mariadb",
  "azure_sql",
  "oracle_sql",
  "snowflake_sql",
  "local",
  "blob",
  "sftp",
  "no_auth_api",
  "basic_auth_api",
  "token_auth_api",
  "key_auth_api",
  "oauth_api",
];

export const CONNECTION_KEY_SERVICES_TYPE: any = {
  mariadb: "Mariadb",
  azure_sql: "Azure sql",
  oracle_sql: "Oracle sql",
  snowflake_sql: "Snowflake sql",
  local: "Local",
  blob: "Blob",
  sftp: "SFTP",
  no_auth_api: "No Auth Api",
  basic_auth_api: "Basic Auth Api",
  token_auth_api: "Token Auth Api",
  key_auth_api: "Key Auth Api",
  oauth_api: "Oauth Api",
};

export const CONNECTION_KEY_SERVICES_GROUPED = {
  SQL: {
    mariadb: "Mariadb",
    azure_sql: "Azure sql",
    oracle_sql: "Oracle sql",
    snowflake_sql: "Snowflake sql",
    azure_sql_service_principal: "Azure sql service principal",
  },
  File: {
    local: "Local",
    blob: "Blob",
    sftp: "SFTP",
  },
  API: {
    no_auth_api: "No Auth Api",
    basic_auth_api: "Basic Auth Api",
    token_auth_api: "Token Auth Api",
    key_auth_api: "Key Auth Api",
    oauth_api: "Oauth Api",
  },
};
