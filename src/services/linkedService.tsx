import {
  getLinkedServicesUrl,
  getAllLinkedServicesUrl,
  addLinkedServiceUrl,
  updateLinkedServiceById,
  getLinkedServicesBackupUrl,
} from "./constants";
import base from "../middlewares/interceptors";

export const getLinkedServices = async (sub_type?: any) => {
  try {
    let requestUrl: any;
    if (sub_type) {
      requestUrl = `${getLinkedServicesUrl}?sub_type=${sub_type}`;
    } else {
      requestUrl = getLinkedServicesUrl;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getLinkedPaginatedServices = async (page: any, pSize: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getLinkedServicesUrl}/pages?page=${pageN}&size=${pageS}`;
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getAllLinkedPaginatedServices = async (page: any, pSize: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;
    let requestUrl = `${getAllLinkedServicesUrl}/pages?page=${pageN}&size=${pageS}`;
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getAllLinkedServices = async (linkedServiceIds: any) => {
  try {
    const linkedServiceIdsQueryParam = linkedServiceIds.join(",");

    const apiUrl = `${getAllLinkedServicesUrl}?linked_service_filter_ids=${linkedServiceIdsQueryParam}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getLinkedServiceDetail = async ({
  currentLinkedServiceId,
}: any) => {
  try {
    const response = await base.get(
      `${getLinkedServicesUrl}/${currentLinkedServiceId}/connection-keys-details`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const addLinkedService = async ({ payload }: any) => {
  try {
    const response = await base.post(addLinkedServiceUrl, payload);
    return response.data;
  } catch (error) {
    console.error(error);
    return Promise.reject(error);
  }
};

export const updateLinkedService = async ({
  currentLinkedServiceId,
  payload,
}: any) => {
  try {
    const response = await base.put(
      `${updateLinkedServiceById}/${currentLinkedServiceId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const deleteLinkedService = async (currentLinkedServiceId: number) => {
  try {
    const response = await base.delete(
      `${updateLinkedServiceById}/${currentLinkedServiceId}`
    );
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getLinkedServicesBackup = async ({
  linkedServicesId,
  page,
  pSize,
}: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    let requestUrl;
    if (linkedServicesId && linkedServicesId != 0) {
      requestUrl = `${getLinkedServicesBackupUrl}?linked_service_backup_filter_ids=${linkedServicesId}&page=${pageN}&size=${pageS}`;
    }
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
