import {
  getUserProfileUrl,
  getUserRoleUrl,
  getIncidentByExecutionIdsUrl,
  getResolveIncidentByIdUrl,
  postIncidentCommentsUrl,
  getUsersListUrl,
} from "./constants";
import base from "../middlewares/interceptors";

const config = {
  headers: {
    // 'Authorization': 'Bearer my-token',
    "Content-Type": "application/json",
  },
};

export const getUserProfile = async () => {
  try {
    const response = await base.get(getUserProfileUrl, config);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const getUserRole = async () => {
  try {
    const response = await base.get(getUserRoleUrl, config);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
export const getPaginatedUsersList = async ({ page, pSize }: any) => {
  try {
    const pageN = page == null ? process.env.REACT_APP_DEFAULT_PAGE : page;
    const pageS =
      pSize == null ? process.env.REACT_APP_DEFAULT_PAGE_SIZE : pSize;

    const requestUrl = `${getUsersListUrl}/pages?page=${pageN}&size=${pageS}`;

    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
export const getUsersList = async () => {
  try {
    const requestUrl = `${getUsersListUrl}`;
    const response = await base.get(requestUrl);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getUsersDetails = async () => {
  try {
    const response = await base.get(getUsersListUrl, config);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const getIncidentByExecutionIds = async (data: any) => {
  try {
    const formattedData = Array.isArray(data) ? data.join(",\n") : data;
    const apiUrl = `${getIncidentByExecutionIdsUrl}?execution_ids=${formattedData}`;
    const response = await base.get(apiUrl);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const getResolveIncident = async (data: any, status: string) => {
  try {
    const response = await base.patch(
      `${getResolveIncidentByIdUrl}/${data}/${status}`,
      config
    );
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};

export const postIncidentComments = async ({ payload }: any) => {
  try {
    const response = await base.post(`${postIncidentCommentsUrl}`, payload);
    return response?.data;
  } catch (error) {
    console.error(error);
  }
};
