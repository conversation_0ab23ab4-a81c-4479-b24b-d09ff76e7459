export const processIssueAnalysisData = (
  rowData: { [key: string]: any },
  prefix: string[]
): { [key: string]: any[] } => {
  const result: { [key: string]: any[] } = {};

  prefix.forEach((p) => {
    result[p] = [];
  });

  prefix.forEach((p) => {
    for (const [key, value] of Object.entries(rowData)) {
      if (key.startsWith(p)) {
        const newKey = key.replace(`${p}`, `${p.toUpperCase()}`);
        result[p].push({ [newKey]: value });
      }
      if (key.startsWith("Key_")) {
        result[p].push({ [key]: value });
      }
    }
  });

  return result;
};
