import React from "react";
import { Box, Tooltip, Typography } from "@mui/material";
import { GridAlignment, GridColDef } from "@mui/x-data-grid";
import StatusCell from "./statusIcon";

interface ColumnProps {
  field?: string;
  headerName?: string;
  minWidth?: string;
  align?: GridAlignment;
  headerAlign?: GridAlignment;
}

export const UserColumn = ({
  field = "username",
  headerName = "User Name",
  minWidth = "140",
  align = "center",
  headerAlign = "center",
}: ColumnProps): GridColDef => {
  const column: GridColDef = {
    field: field,
    headerName: headerName,
    headerAlign: headerAlign,
    align: align,
    minWidth: parseInt(minWidth),
    renderCell: (params: any) => (
      <Tooltip title={params?.row?.assigned_user} placement="top" arrow>
        <Box className="word-break-all">{`${
          params.row?.assigned_user || ""
        }`}</Box>
      </Tooltip>
    ),
  };
  return column;
};

export const CurrentStatusColumn = ({
  field = "current_status",
  headerName = "Status",
  minWidth = "80",
  align = "center",
  headerAlign = "center",
}: ColumnProps): GridColDef => {
  const column: GridColDef = {
    field: field,
    headerName: headerName,
    headerAlign: headerAlign,
    align: align,
    minWidth: parseInt(minWidth),
    renderCell: (params: any) => (
      <StatusCell status={params?.row?.current_status} />
    ),
  };
  return column;
};

export const CommentColumn = ({
  field = "comment",
  headerName = "Comment",
  minWidth = "140",
  align = "center",
  headerAlign = "center",
}: ColumnProps): GridColDef => {
  const column: GridColDef = {
    field: field,
    headerName: headerName,
    headerAlign: headerAlign,
    align: align,
    minWidth: parseInt(minWidth),
    renderCell: (params: any) => (
      <Tooltip
        title={
          <Typography sx={{ fontSize: "0.875rem" }}>
            {params?.row?.comment || ""}
          </Typography>
        }
        placement="top"
        arrow
      >
        <Box className="word-break-all max-width100">
          {params?.row?.comment && params?.row?.comment.length > 49
            ? `${params?.row?.comment.substring(0, 50)}...`
            : params?.row?.comment || ""}
        </Box>
      </Tooltip>
    ),
  };
  return column;
};
