export const IconEditSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="mask0_70_35"
      maskUnits="userSpaceOnUse"
      x="2"
      y="2"
      width="20"
      height="20"
    >
      <path d="M22 2H2V22H22V2Z" fill="" />
    </mask>
    <g mask="url(#mask0_70_35)">
      <path
        d="M16.6168 3.48006C17.0983 2.99854 17.7514 2.72803 18.4324 2.72803C19.1134 2.72803 19.7664 2.99854 20.248 3.48006C20.7295 3.96158 21 4.61467 21 5.29564C21 5.97661 20.7295 6.62969 20.248 7.11121L7.99283 19.3663L3 20.728L4.36168 15.7352L16.6168 3.48006Z"
        stroke="#0967B1"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
  </svg>
);
export const IconDeleteSvg = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 4.6001H19"
      stroke=""
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17 4.6V17.2C17 17.6774 16.7893 18.1352 16.4142 18.4728C16.0391 18.8104 15.5304 19 15 19H5C4.46957 19 3.96086 18.8104 3.58579 18.4728C3.21071 18.1352 3 17.6774 3 17.2V4.6M6 4.6V2.8C6 2.32261 6.21071 1.86477 6.58579 1.52721C6.96086 1.18964 7.46957 1 8 1H12C12.5304 1 13.0391 1.18964 13.4142 1.52721C13.7893 1.86477 14 2.32261 14 2.8V4.6"
      stroke=""
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 9.1001V14.5001"
      stroke=""
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 9.1001V14.5001"
      stroke=""
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const IconGlobalSvg = () => (
  <svg
    width="27"
    height="29"
    viewBox="0 0 27 29"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.0667 0.410156C5.85019 0.410156 0 6.67822 0 14.4102C0 22.1421 5.85019 28.4102 13.0667 28.4102C20.2831 28.4102 26.1333 22.1421 26.1333 14.4102C26.1333 6.67822 20.2831 0.410156 13.0667 0.410156ZM11.3594 3.21672L11.4313 3.12703C11.6134 2.90172 11.8196 2.64666 11.8506 2.20828C11.8776 1.84384 11.7575 1.58659 11.6277 1.37834C12.1001 1.31884 12.5795 1.28516 13.0667 1.28516C13.6649 1.28516 14.2521 1.33328 14.8274 1.42209C14.9344 1.56516 15.0749 1.71434 15.3027 1.81453C15.5444 1.92609 15.8299 2.01447 16.1892 1.92347C16.2954 1.89634 16.3778 1.84822 16.4489 1.79572C16.7012 1.87316 16.9507 1.95716 17.1965 2.05122C17.2827 2.31766 17.4481 2.63528 17.8515 2.68997C18.0785 2.72453 18.2684 2.68647 18.4346 2.60991C21.8952 4.42072 24.4379 7.92684 25.1272 12.1185C25.0602 12.2699 25.0504 12.4187 25.0484 12.5127C25.0476 12.539 25.0496 12.5657 25.0517 12.5727C25.037 12.5972 25.0182 12.6098 24.9271 12.6567C24.823 12.7105 24.6809 12.784 24.5498 12.9318C24.4008 13.0968 24.3322 13.26 24.277 13.3912C24.2342 13.4936 24.2097 13.5492 24.1652 13.5968C24.1366 13.6266 24.081 13.6445 23.9937 13.6717C23.823 13.7242 23.5653 13.8033 23.3922 14.1039C23.2276 14.3878 23.2415 14.6503 23.2513 14.842C23.2525 14.8638 23.2542 14.8862 23.255 14.9089C23.2235 14.9255 23.1852 14.9426 23.1451 14.9566C23.1688 14.8183 23.1721 14.6272 23.0565 14.4307C22.9006 14.1757 22.6944 14.1372 22.5857 14.1372C22.3783 14.1372 22.2219 14.2649 22.1215 14.3695C22.1235 14.1577 22.119 13.9626 22.0867 13.8165C22.0565 13.6695 22.0757 13.5877 22.1402 13.3505L22.1627 13.2674C22.1917 13.1545 22.2493 13.074 22.3285 12.962C22.4375 12.8089 22.5731 12.6186 22.6376 12.3246C22.7099 12.0043 22.6886 11.7681 22.6678 11.5393C22.6601 11.4583 22.6523 11.373 22.6486 11.2746L22.6409 11.0432C22.6327 10.771 22.6262 10.5361 22.5637 10.1931L22.5355 10.0404C22.4726 9.69609 22.4179 9.39903 22.1884 9.02409C21.992 8.70953 21.8368 8.48859 21.4926 8.29259C21.4114 8.24491 21.3268 8.20641 21.2407 8.17753C21.1014 8.12766 20.974 8.11059 20.8507 8.09353L20.7233 8.07516C20.5057 8.04103 20.3097 8.08172 20.1655 8.11322C20.0488 8.13859 19.9744 8.15916 19.916 8.12459C19.8577 8.09047 19.8299 8.05459 19.7576 7.95659C19.7192 7.90453 19.6788 7.84984 19.6253 7.78641C19.5714 7.72428 19.5347 7.66653 19.4991 7.61184C19.4289 7.50466 19.3419 7.37078 19.1888 7.23822C19.0255 7.09603 18.9009 6.99628 18.6666 6.90834C18.2496 6.76047 17.8662 6.87072 17.5355 6.99628C17.2643 7.09691 17.099 7.24303 16.9524 7.37166C16.8883 7.42853 16.8205 7.48891 16.7547 7.53616C16.6902 7.57641 16.6322 7.60441 16.5775 7.63241C16.4485 7.69672 16.3019 7.77022 16.1398 7.92466C16.0271 8.03053 15.6445 8.29784 15.5559 8.32191C15.5163 8.32016 15.4771 8.30309 15.4056 8.27028C15.3133 8.22741 15.1876 8.17009 15.0422 8.15741L14.9536 8.15172C14.9262 8.15172 14.898 8.15347 14.8772 8.15566C14.6918 8.17053 14.5616 8.23528 14.4656 8.28209C14.428 8.30047 14.3897 8.32147 14.3415 8.33416C14.2002 8.37178 14.0409 8.36916 13.8809 8.35997C13.6249 8.34597 13.3737 8.35384 13.1434 8.42384C12.9544 8.48116 12.8176 8.58003 12.7179 8.65266C12.6106 8.73053 12.5848 8.74409 12.544 8.74453C12.5028 8.74453 12.4734 8.72616 12.3594 8.62991C12.2467 8.53453 12.092 8.40372 11.8637 8.33591C11.6992 8.28866 11.582 8.27334 11.4578 8.25716L11.3447 8.24228C11.1304 8.20903 10.9772 8.19547 10.8037 8.18016C10.7232 8.17316 10.6383 8.16616 10.5391 8.15566L10.4239 8.15347L10.4052 8.16134C10.3917 8.16222 10.3758 8.16309 10.3582 8.16397L10.3537 8.16091C10.223 8.06334 10.0879 7.96316 9.88942 7.85991C9.74324 7.78291 9.62442 7.72428 9.49375 7.67397C9.48803 7.51778 9.4717 7.30953 9.42801 7.16953C9.41862 7.13759 9.40882 7.10959 9.39616 7.07459C9.40065 7.05447 9.40882 7.02078 9.42474 6.96959C9.47782 6.80509 9.56031 6.68172 9.59624 6.63228C9.61829 6.61609 9.63871 6.60209 9.65831 6.58809C9.73467 6.53559 9.82899 6.46997 9.92617 6.35709C10.1985 6.04822 10.3223 5.75028 10.3533 5.32591C10.3778 4.94703 10.1806 4.69241 10.0499 4.52353C10.0123 4.47584 9.95435 4.40103 9.94618 4.39141C9.96374 4.31747 9.99437 4.28553 10.165 4.16434C10.2581 4.09828 10.3553 4.02784 10.448 3.94078C10.5534 3.84322 10.6546 3.78153 10.7714 3.71022C10.9544 3.59866 11.1614 3.47222 11.3594 3.21672ZM1.79912 19.5661C1.33443 18.4019 1.02328 17.1507 0.890575 15.8425C0.990617 15.6413 1.11189 15.4462 1.27686 15.2309C1.39487 15.08 1.52023 14.9885 1.66478 14.8831C1.84077 14.7545 2.04044 14.6083 2.2295 14.355C2.45653 14.0588 2.53085 13.743 2.59087 13.4888C2.65539 13.2162 2.69214 13.0806 2.82689 12.9804C3.01472 12.843 3.16336 12.8282 3.43286 12.801C3.52963 12.7918 3.63212 12.7809 3.74523 12.7638L3.8955 12.7402C4.42552 12.6567 4.76403 12.619 5.20462 12.752C5.50352 12.8417 5.68767 12.9913 5.92083 13.1803C6.07437 13.305 6.24832 13.4459 6.47045 13.582C6.64277 13.6874 6.78854 13.8038 6.92942 13.9162C7.24424 14.1669 7.56968 14.4259 8.11073 14.5108C8.42106 14.5567 8.68239 14.4539 8.88043 14.3738C8.97966 14.3332 9.07358 14.2951 9.13197 14.2951C9.14544 14.2951 9.16259 14.2964 9.19036 14.3126C9.45782 14.4718 9.51743 14.604 9.52846 14.6945C9.47129 14.7116 9.3884 14.7269 9.32756 14.7387C9.0405 14.7925 8.5064 14.8927 8.44474 15.482C8.41371 15.7664 8.50477 15.9445 8.58766 16.0433C8.80163 16.3019 9.20057 16.2743 9.65014 16.1658C9.84369 16.119 10.0442 16.07 10.2047 16.07C10.2777 16.07 10.321 16.0805 10.3406 16.088C10.459 16.1339 10.5869 16.1676 10.7114 16.1991C10.7661 16.2127 10.8392 16.231 10.9021 16.2494C10.8559 16.2586 10.8086 16.2678 10.7686 16.2752C10.5803 16.3098 10.3676 16.3492 10.1728 16.4485C10.0193 16.525 9.8882 16.5977 9.76529 16.6663C9.51458 16.8055 9.31613 16.9157 9.01396 17.0037C8.92168 17.0299 8.78162 17.0076 8.63421 16.9809C8.51743 16.9595 8.39697 16.9376 8.26957 16.9376C7.96822 16.9376 7.73097 17.0658 7.56601 17.316C7.18871 17.8813 7.41247 18.4723 7.52027 18.7572C7.66687 19.1435 8.01232 19.6042 8.82163 19.6042C9.20261 19.6042 9.6432 19.5048 10.2079 19.2922C10.5975 19.1443 10.8257 18.877 11.0087 18.6618C11.1565 18.4877 11.2733 18.3503 11.4619 18.2602L11.6698 18.1587C12.0977 17.9473 12.4109 17.7968 12.7474 17.9666C12.8376 18.0121 12.8768 18.0987 12.9589 18.3157C13.054 18.5686 13.1847 18.9151 13.5485 19.1225C13.8797 19.3106 14.2933 19.2423 14.5955 19.1496C14.7768 19.094 14.9221 19.0463 15.0275 19.1124C15.1381 19.1828 15.1839 19.2782 15.268 19.4698C15.3431 19.6409 15.4362 19.8535 15.6163 20.0557C15.6702 20.1165 15.7208 20.1755 15.7694 20.2324C16.0679 20.5828 16.3505 20.9136 16.9095 21.0553C16.9899 21.0755 17.072 21.0855 17.1537 21.0855C17.4395 21.0855 17.6694 20.9635 17.854 20.865C18.0124 20.781 18.1345 20.718 18.2341 20.7513C18.651 20.8974 18.7376 20.9976 18.7768 21.1761C18.8111 21.3192 18.6698 21.4395 18.1463 21.7103C18.063 21.7536 17.9801 21.7965 17.9013 21.8402C17.62 21.9955 17.4093 22.0148 17.0904 22.0441C16.963 22.0555 16.827 22.0682 16.6804 22.0896C16.3844 22.1294 16.1169 22.1981 15.858 22.2646C15.5595 22.3416 15.2774 22.4138 15.032 22.4138C14.7866 22.4138 14.5906 22.3416 14.4011 22.1823C14.2459 22.0493 14.1741 21.908 14.0744 21.7129C14.0022 21.5712 13.9205 21.4106 13.7915 21.2277C13.6775 21.0737 13.5795 20.8523 13.4848 20.6375C13.2757 20.1637 13.0156 19.574 12.424 19.574C12.3231 19.574 12.2182 19.5932 12.112 19.6317C11.6522 19.7975 11.4991 20.1922 11.3872 20.48C11.339 20.6038 11.2937 20.7207 11.2324 20.8169C11.0793 21.051 11.0021 21.2995 10.9335 21.5187C10.8188 21.8866 10.7829 21.9299 10.6914 21.9443C10.544 21.9671 10.4513 21.9002 10.232 21.7304C10.1667 21.6797 10.0989 21.6267 10.027 21.576C9.88575 21.4753 9.80245 21.3638 9.69669 21.222C9.57868 21.0641 9.44475 20.8847 9.22548 20.7158C8.9817 20.5233 8.76161 20.431 8.56765 20.3492C8.41085 20.2836 8.27528 20.2267 8.12583 20.1204C8.06907 20.0797 8.01885 20.0115 7.96046 19.9318C7.82775 19.7516 7.62726 19.479 7.23117 19.4637L7.18993 19.4628C6.89144 19.4628 6.47453 19.5766 6.18298 20.1178C6.07723 20.3138 6.00332 20.5785 5.9241 20.8585C5.79588 21.3157 5.61989 21.9408 5.34427 21.9408H5.34386C5.05884 21.9203 4.21972 21.1332 3.81628 20.7548C3.52718 20.4835 3.27728 20.2495 3.09721 20.1217L2.96164 20.0215C2.65335 19.7918 2.33567 19.5552 1.9159 19.5552C1.87752 19.5552 1.83832 19.5617 1.79912 19.5661ZM13.0667 27.5352C8.37737 27.5352 4.30465 24.7107 2.24624 20.5632C2.32464 20.6143 2.40672 20.6734 2.49451 20.7386L2.64233 20.8475C2.77953 20.9451 3.02126 21.1713 3.27728 21.4115C4.12662 22.2077 4.7677 22.7743 5.28996 22.8123C6.21279 22.8775 6.50924 21.8057 6.70524 21.1092C6.7669 20.8891 6.83142 20.6616 6.8894 20.5531C6.99761 20.3527 7.10092 20.3361 7.20463 20.3357C7.2275 20.3488 7.28181 20.4232 7.31774 20.4722C7.40186 20.5868 7.50721 20.7294 7.67177 20.8471C7.89513 21.0059 8.09317 21.089 8.26875 21.1625C8.43698 21.233 8.58235 21.2942 8.74323 21.422C8.87594 21.5235 8.96006 21.6368 9.05806 21.7676C9.18423 21.936 9.32633 22.1263 9.5746 22.304C9.63748 22.3486 9.69547 22.3937 9.75182 22.437C10.0164 22.6435 10.3214 22.8819 10.8053 22.8097C11.4199 22.7152 11.5926 22.1618 11.7069 21.7956C11.7629 21.6171 11.8155 21.4487 11.9033 21.3135C12.0144 21.1393 12.0814 20.9674 12.1397 20.816C12.2447 20.5465 12.2794 20.4936 12.3717 20.4603C12.4003 20.4498 12.4158 20.4468 12.4264 20.4485C12.5093 20.4765 12.6628 20.8243 12.7453 21.0112C12.8543 21.2583 12.9781 21.5383 13.1447 21.7632C13.2316 21.8862 13.2921 22.0056 13.357 22.1316C13.4738 22.3613 13.6065 22.622 13.8927 22.8679C14.2304 23.151 14.6024 23.2888 15.0312 23.2888C15.3733 23.2888 15.7012 23.2048 16.0475 23.1155C16.2847 23.0543 16.5306 22.9913 16.7858 22.9572C16.9213 22.9375 17.0438 22.9265 17.1586 22.9156C17.5138 22.8837 17.8491 22.8526 18.2754 22.6172C18.3481 22.577 18.424 22.5376 18.5008 22.4982C19.0091 22.2348 19.7776 21.8372 19.569 20.9674C19.4105 20.2438 18.8099 20.0329 18.484 19.9192C18.3811 19.8842 18.2754 19.8662 18.1696 19.8662C17.8948 19.8662 17.6702 19.9857 17.4897 20.0819C17.3329 20.1655 17.2019 20.2298 17.0953 20.204C16.7825 20.1243 16.6277 19.9428 16.3717 19.6427C16.3199 19.5814 16.2656 19.5188 16.2076 19.4528C16.1186 19.3526 16.0667 19.2345 16.0067 19.0975C15.9038 18.863 15.776 18.5712 15.4444 18.3599C15.1112 18.1477 14.6808 18.2125 14.3684 18.3092C14.1884 18.3647 14.043 18.4137 13.9319 18.3498C13.8388 18.2973 13.7968 18.2046 13.7159 17.9889C13.6179 17.7286 13.484 17.372 13.0952 17.1756C12.8796 17.0671 12.6653 17.0142 12.4407 17.0142C12.0364 17.0142 11.6918 17.1848 11.3268 17.3655L11.1295 17.4622C10.7845 17.6267 10.5742 17.8743 10.4047 18.0733C10.2463 18.2597 10.132 18.394 9.93638 18.468C9.46843 18.6443 9.10379 18.7305 8.82204 18.7305C8.44311 18.7305 8.33408 18.5773 8.27732 18.429C8.13931 18.0646 8.15238 17.9443 8.26998 17.8143C8.34143 17.8143 8.41779 17.8305 8.49742 17.845C8.71057 17.8835 8.98742 17.9167 9.22507 17.8507C9.61462 17.7378 9.87064 17.5952 10.1418 17.4442C10.2553 17.3808 10.3757 17.3138 10.5203 17.2417C10.6163 17.1931 10.7645 17.1655 10.907 17.1393C11.2243 17.0807 11.8854 16.9595 11.8372 16.2275C11.7878 15.573 11.2312 15.433 10.8984 15.349C10.8049 15.3254 10.7089 15.3027 10.6171 15.2668C10.5011 15.2226 10.3745 15.1998 10.2316 15.1972C10.2912 15.1027 10.3325 14.9855 10.3439 14.8385C10.3786 14.303 10.1242 13.8694 9.58644 13.5496C9.44352 13.4652 9.29081 13.4223 9.13278 13.4223C8.92372 13.4223 8.74691 13.4945 8.59092 13.5583C8.45046 13.6152 8.32796 13.6625 8.22792 13.6472C7.89308 13.5947 7.69341 13.4358 7.41778 13.2162C7.26507 13.0946 7.09152 12.9563 6.87674 12.8242C6.69993 12.7166 6.56192 12.6042 6.41533 12.4856C6.1495 12.2699 5.87469 12.0463 5.42716 11.9116C4.85753 11.7401 4.37243 11.7821 3.77708 11.8766L3.63049 11.8998C3.53331 11.9147 3.44348 11.9234 3.35854 11.9317C3.04943 11.9628 2.73012 11.9947 2.36507 12.2616C1.97021 12.5556 1.87629 12.955 1.80034 13.2762C1.75216 13.4805 1.7101 13.6568 1.59577 13.8068C1.48021 13.9617 1.35893 14.0501 1.20581 14.1621C1.08535 14.25 0.953867 14.348 0.819117 14.4832C0.8183 14.4574 0.816667 14.4342 0.816667 14.4102C0.816667 8.01741 5.08375 2.69434 10.7322 1.52622C10.7776 1.60147 10.8241 1.67453 10.8686 1.74059C11.0107 1.95059 11.0458 2.01753 11.0364 2.14091C11.0262 2.28572 10.9748 2.35528 10.8139 2.55391L10.7339 2.65453C10.6277 2.79103 10.5187 2.85797 10.3672 2.95028C10.2332 3.03253 10.0809 3.12528 9.91352 3.28016C9.84696 3.34228 9.77877 3.38953 9.71425 3.43503C9.51948 3.57328 9.25243 3.76316 9.15443 4.17922C9.05479 4.60928 9.28468 4.90634 9.42229 5.08397C9.46639 5.14172 9.54112 5.23797 9.53948 5.26378C9.52315 5.48691 9.47946 5.59322 9.3296 5.76253C9.30061 5.79666 9.27529 5.81459 9.2169 5.85528C9.17076 5.88678 9.12217 5.92091 9.06827 5.96509L9.01886 6.01322C8.99436 6.04078 8.77957 6.29453 8.65217 6.69178C8.5554 7.00459 8.55132 7.16778 8.63421 7.39091L8.65217 7.44253C8.66687 7.49547 8.68076 7.68184 8.68157 7.80216L8.64727 8.33503L8.97966 8.42384C9.19812 8.48203 9.30877 8.53059 9.53213 8.64872C9.67546 8.72266 9.76978 8.79309 9.88902 8.88191L10.0091 8.96897C10.1691 9.07134 10.3317 9.05341 10.5044 9.03284C10.5905 9.04203 10.6665 9.04816 10.7388 9.05472C10.896 9.06784 11.0348 9.08009 11.2324 9.11072L11.3607 9.12778C11.4603 9.14091 11.5395 9.15009 11.6498 9.18159C11.7126 9.20041 11.7661 9.24416 11.8547 9.31853C12.0058 9.44628 12.2128 9.62084 12.5509 9.62084C12.8466 9.61866 13.0377 9.47953 13.1773 9.37759C13.2582 9.31897 13.3068 9.28484 13.3672 9.26691C13.5093 9.22359 13.6718 9.22534 13.8372 9.23453C14.0757 9.24897 14.317 9.24372 14.5408 9.18422C14.6449 9.15622 14.7278 9.11772 14.807 9.07834C14.8764 9.04422 14.9021 9.03241 14.9107 9.03066L14.9401 9.03153L14.9613 9.02891C14.9883 9.03109 15.0303 9.05034 15.0834 9.07441C15.1884 9.12253 15.3317 9.18903 15.5481 9.19691C15.9887 9.19691 16.6714 8.58878 16.6796 8.58134C16.7555 8.50959 16.8188 8.47809 16.9242 8.42516C16.9985 8.38797 17.0765 8.34816 17.1839 8.28122C17.2966 8.20116 17.3864 8.12328 17.4714 8.04847C17.5914 7.94303 17.6702 7.87303 17.8062 7.82228C18.0908 7.71378 18.2264 7.67353 18.4028 7.73741C18.5036 7.77503 18.5445 7.80522 18.6747 7.91853C18.7384 7.97366 18.7723 8.02616 18.8295 8.11322C18.885 8.19853 18.9438 8.28691 19.0234 8.37791C19.059 8.42034 19.0884 8.46016 19.1161 8.49822C19.2051 8.61853 19.3158 8.76903 19.5232 8.89153C19.7817 9.04203 20.0982 9.02016 20.3309 8.96984C20.4355 8.94709 20.5167 8.92609 20.6037 8.94053L20.7482 8.96153C20.8479 8.97509 20.9197 8.98516 20.9896 9.00966C21.0247 9.02103 21.0614 9.03678 21.1055 9.06216C21.2844 9.16409 21.3513 9.25509 21.5085 9.50709C21.6482 9.73547 21.6788 9.90347 21.7348 10.2088L21.7638 10.365C21.8132 10.6341 21.8185 10.817 21.8254 11.0698L21.8336 11.3109C21.8381 11.4268 21.8466 11.5283 21.8556 11.6242C21.8732 11.8158 21.8846 11.9427 21.8442 12.122C21.8201 12.2314 21.7715 12.3036 21.6801 12.4322C21.5763 12.5779 21.4473 12.7595 21.3779 13.0312L21.3575 13.1055C21.2836 13.3768 21.2133 13.6332 21.2921 14.0125C21.3105 14.0969 21.3068 14.345 21.304 14.5261C21.2962 15.0213 21.2917 15.3486 21.4853 15.5595C21.5739 15.6562 21.6915 15.7091 21.8164 15.7091L21.943 15.6999L22.0263 15.657C22.1427 15.5975 22.2435 15.5122 22.3342 15.4177C22.3375 15.4256 22.3403 15.433 22.3432 15.4409C22.4555 15.7095 22.6956 15.864 23.0026 15.864C23.3391 15.864 24.0202 15.6085 24.0668 15.1355C24.0778 15.0108 24.0733 14.898 24.0676 14.7917C24.059 14.6381 24.0619 14.6083 24.0892 14.5615C24.1051 14.5484 24.1737 14.527 24.2191 14.5134C24.362 14.4692 24.5568 14.4097 24.7372 14.2229C24.8953 14.0549 24.9659 13.886 25.0227 13.7508C25.0651 13.6493 25.0896 13.5938 25.1395 13.5387C25.1689 13.5054 25.2089 13.4835 25.2816 13.4463C25.302 13.7648 25.3167 14.0855 25.3167 14.4102C25.3167 21.6587 19.8319 27.5352 13.0667 27.5352Z"
      fill="none"
    />
    <path
      d="M18.2234 12.7297C17.7742 12.8684 17.6125 13.2359 17.4941 13.5045C17.4308 13.6489 17.3757 13.7731 17.285 13.8628C17.1249 14.023 16.9702 14.1113 16.7905 14.2137C16.5913 14.3275 16.3654 14.4561 16.1319 14.6897C16.0486 14.7724 15.953 14.8437 15.862 14.9133C15.561 15.1421 15.1057 15.4881 15.2393 16.14C15.3266 16.5491 15.6202 16.7932 16.0241 16.7932C16.2915 16.7932 16.5819 16.693 16.8632 16.5959C17.002 16.5482 17.1396 16.4996 17.265 16.4699C17.9804 16.3023 18.47 16.1015 19.0057 15.5249C19.0645 15.4623 19.1258 15.3998 19.1878 15.3359C19.6509 14.8634 20.35 14.149 19.8302 13.3221C19.4806 12.7599 18.8612 12.5333 18.2234 12.7297ZM18.6239 14.7024C18.5578 14.7702 18.492 14.8376 18.4279 14.9058C18.0265 15.3381 17.6905 15.4741 17.0898 15.615C16.9408 15.65 16.7766 15.706 16.6121 15.7629C16.4071 15.8333 16.1756 15.9138 16.0347 15.9178C16.0433 15.8644 16.1082 15.7992 16.3348 15.6268C16.4455 15.5424 16.5717 15.4466 16.688 15.3302C16.844 15.1749 16.9967 15.0874 17.1739 14.9863C17.3753 14.8713 17.6039 14.7413 17.8403 14.5042C18.0461 14.3012 18.1495 14.0667 18.2323 13.8781C18.3369 13.6415 18.3691 13.5955 18.4496 13.5706C18.5284 13.5461 18.6052 13.5338 18.6778 13.5338C18.8783 13.5338 19.0376 13.6257 19.1515 13.809C19.2569 13.9766 19.1384 14.177 18.6239 14.7024Z"
      fill="none"
    />
    <path
      d="M13.239 23.6328C13.1447 23.6328 13.0491 23.6486 12.9503 23.6809C12.355 23.8866 12.3831 24.5271 12.3978 24.8714C12.4035 24.9978 12.4084 25.1173 12.3974 25.2214C12.388 25.3163 12.3635 25.4073 12.3419 25.4948C12.2692 25.7888 12.1598 26.2324 12.5203 26.6131C12.6902 26.7881 12.9046 26.8808 13.141 26.8808C13.4918 26.8808 13.8866 26.6634 14.1998 26.2972C14.6792 25.7254 14.6849 24.8897 14.2174 24.2702C13.9993 23.9732 13.6918 23.6328 13.239 23.6328ZM13.5942 25.7092C13.4285 25.9039 13.2406 26.0058 13.141 26.0058C13.1165 26.0058 13.1042 26.0006 13.0846 25.9826C13.0761 25.9424 13.11 25.8081 13.1316 25.7188C13.1606 25.6002 13.1941 25.4655 13.2088 25.3189C13.2276 25.1479 13.2198 24.9794 13.2137 24.8302C13.2092 24.7283 13.2031 24.5804 13.2149 24.5104C13.2835 24.4973 13.3639 24.5231 13.5783 24.8154C13.7984 25.1072 13.8041 25.4594 13.5942 25.7092Z"
      fill="none"
    />
  </svg>
);

export const IconDomainLinkage = () => (
  <svg
    width="32"
    height="33"
    viewBox="0 0 32 33"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_422_4)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0 16C0 7.1635 7.16351 0 16.0001 0C24.8365 0 32 7.1635 32 16C32 16.3358 31.9897 16.6693 31.9693 17H30.9673C30.989 16.6695 31.0001 16.336 31.0001 16C31.0001 15.629 30.9821 15.2625 30.9571 14.8985C30.8681 14.941 30.8191 14.9659 30.7831 15.004C30.722 15.067 30.692 15.1305 30.6401 15.2465L30.6392 15.2484C30.5698 15.4026 30.4833 15.5948 30.2905 15.7861C30.0708 15.9983 29.8338 16.0668 29.6591 16.1172L29.6561 16.1181C29.6005 16.1335 29.5165 16.1581 29.497 16.173C29.4636 16.2265 29.46 16.2605 29.4706 16.436C29.4775 16.5575 29.4831 16.6865 29.4696 16.829C29.4632 16.8891 29.4473 16.9461 29.4234 17H26.1361C26.076 16.7794 26.0804 16.4954 26.0866 16.1325C26.09 15.9255 26.0945 15.6419 26.072 15.5455C25.9757 15.113 26.0612 14.8204 26.1514 14.5112L26.1521 14.509L26.1771 14.424C26.262 14.1135 26.42 13.9061 26.5471 13.7395C26.659 13.5925 26.7185 13.5101 26.748 13.385C26.7975 13.18 26.7835 13.035 26.762 12.816C26.751 12.7065 26.7406 12.5905 26.7351 12.4581L26.725 12.1825C26.7166 11.8935 26.7101 11.6845 26.6496 11.377L26.6141 11.1985L26.6136 11.1963C26.5453 10.8487 26.5077 10.657 26.337 10.3965C26.1445 10.1085 26.0626 10.0045 25.8435 9.888C25.7895 9.85899 25.7446 9.84101 25.7016 9.828C25.616 9.8 25.5281 9.7885 25.406 9.77299L25.2291 9.74899C25.1316 9.7339 25.04 9.75272 24.927 9.77595L24.895 9.7825C24.6101 9.84 24.2225 9.865 23.906 9.693C23.6534 9.55375 23.5179 9.38285 23.4093 9.24573L23.4075 9.2435C23.3736 9.2 23.3376 9.1545 23.294 9.106C23.1965 9.002 23.1245 8.90101 23.0566 8.8035L23.0522 8.79733C22.9848 8.7015 22.9434 8.64269 22.867 8.58101C22.7076 8.4515 22.6575 8.417 22.5341 8.374C22.3181 8.30099 22.152 8.347 21.8035 8.47099C21.6388 8.52836 21.5426 8.60726 21.3983 8.72558L21.3936 8.7295C21.2895 8.81499 21.1795 8.904 21.0415 8.9955C20.91 9.072 20.8145 9.1175 20.7235 9.16L20.7202 9.16156C20.5931 9.22113 20.5162 9.25721 20.424 9.3385C20.414 9.34701 19.578 10.042 19.0385 10.042C18.7758 10.0331 18.6011 9.95831 18.4728 9.90343L18.4695 9.902L18.4654 9.90027C18.4023 9.87359 18.3523 9.85245 18.32 9.85L18.294 9.85299L18.258 9.852C18.2475 9.854 18.216 9.8675 18.131 9.9065C18.0341 9.9515 17.9326 9.9955 17.8051 10.0275C17.531 10.0955 17.2356 10.1015 16.9435 10.085C16.741 10.0745 16.542 10.0725 16.368 10.122C16.2941 10.1425 16.2346 10.1815 16.1355 10.2485C15.9646 10.365 15.7306 10.524 15.3685 10.5265C14.9545 10.5265 14.701 10.327 14.516 10.181C14.4075 10.096 14.342 10.046 14.2651 10.0245C14.1388 9.99083 14.0457 9.97947 13.9345 9.96587L13.9111 9.96301L13.754 9.9435C13.512 9.9085 13.3421 9.8945 13.1496 9.8795L13.0797 9.8737L13.0786 9.87361C13.0112 9.86807 12.9402 9.86223 12.8625 9.8545C12.6511 9.878 12.452 9.8985 12.2561 9.7815L12.109 9.682L12.1065 9.68024C11.9617 9.57957 11.8465 9.49951 11.672 9.4155C11.3985 9.2805 11.263 9.22499 10.9955 9.1585L10.5885 9.05701L10.6305 8.448C10.6295 8.3105 10.6125 8.0975 10.5945 8.03699L10.5725 7.978C10.471 7.72301 10.476 7.5365 10.5945 7.17899C10.7505 6.72499 11.0135 6.43501 11.0435 6.4035L11.104 6.3485C11.17 6.298 11.2295 6.25901 11.286 6.22301C11.3575 6.1765 11.3885 6.156 11.424 6.11701C11.6075 5.9235 11.661 5.802 11.681 5.54699C11.6829 5.51898 11.6005 5.41835 11.5459 5.35176L11.5375 5.3415C11.369 5.1385 11.0875 4.79899 11.2095 4.3075C11.3295 3.832 11.6565 3.61501 11.895 3.45701L11.9067 3.44934C11.9822 3.39967 12.0615 3.34751 12.139 3.28C12.3411 3.10544 12.5253 2.99995 12.6877 2.90692L12.6945 2.90301C12.8801 2.7975 13.0135 2.72101 13.1436 2.56501L13.2415 2.45C13.4385 2.22301 13.5015 2.1435 13.514 1.978C13.5255 1.83701 13.4825 1.7605 13.3085 1.5205C13.254 1.44499 13.1971 1.3615 13.1415 1.2755C6.22501 2.6105 1 8.694 1 16C1 16.0176 1.00082 16.0348 1.00168 16.0527L1.003 16.0835C1.168 15.929 1.329 15.817 1.4765 15.7165L1.47975 15.7142C1.66571 15.5873 1.81332 15.4865 1.95401 15.3105C2.094 15.139 2.1455 14.9375 2.2045 14.704L2.20499 14.7021C2.29791 14.3354 2.41335 13.8799 2.89601 13.5445C3.34296 13.2396 3.73392 13.203 4.11239 13.1676L4.12261 13.1666C4.22361 13.1573 4.33037 13.1474 4.4455 13.131L4.625 13.1045C5.354 12.9965 5.948 12.9485 6.64551 13.1445C7.19228 13.2982 7.5285 13.5528 7.85333 13.7988L7.85552 13.8005L7.86635 13.8086C8.04184 13.9412 8.20837 14.0669 8.42051 14.1875C8.6835 14.3385 8.89601 14.4965 9.08301 14.6355C9.42051 14.8865 9.66501 15.068 10.075 15.128C10.1975 15.1455 10.3475 15.0915 10.5195 15.0265C10.7105 14.9535 10.927 14.871 11.183 14.871C11.3765 14.871 11.5635 14.92 11.7385 15.0165C12.397 15.3821 12.7085 15.8775 12.666 16.4895C12.6521 16.6575 12.6015 16.7915 12.5285 16.8995C12.7035 16.9025 12.8585 16.9285 13.0005 16.979C13.1014 17.0158 13.2064 17.0405 13.3097 17.0647L13.345 17.073C13.7526 17.1691 14.4341 17.3296 14.4945 18.077C14.5535 18.9125 13.746 19.0517 13.3569 19.1187L13.3555 19.119C13.181 19.149 12.9996 19.1805 12.882 19.236C12.705 19.3185 12.5575 19.395 12.4185 19.4675L12.4173 19.4682C12.0857 19.6404 11.7724 19.8031 11.296 19.932C11.005 20.0075 10.666 19.9695 10.405 19.9255L10.3655 19.9187C10.2823 19.9043 10.2021 19.8905 10.1265 19.8905C9.98252 20.039 9.96651 20.1765 10.1355 20.593C10.205 20.7625 10.3385 20.9375 10.8025 20.9375C11.1475 20.9375 11.594 20.839 12.167 20.6375C12.4065 20.553 12.5465 20.3995 12.7405 20.1865L12.7419 20.1849C12.9492 19.9576 13.2065 19.6756 13.628 19.488L13.8696 19.3775C14.3165 19.171 14.7385 18.976 15.2335 18.976C15.5085 18.976 15.771 19.0365 16.035 19.1605C16.5106 19.3848 16.6747 19.7918 16.7947 20.0893C16.8748 20.2884 16.924 20.3966 17 20.4622V21.5102C16.8582 21.4926 16.719 21.4539 16.59 21.3855C16.1446 21.1485 15.9845 20.7525 15.8681 20.4635C15.7675 20.2155 15.7195 20.1165 15.6091 20.0645C15.197 19.8705 14.8135 20.0425 14.2896 20.284L14.035 20.4C13.8041 20.503 13.661 20.66 13.4801 20.859C13.256 21.105 12.9765 21.4105 12.4995 21.5795C11.808 21.8225 11.2685 21.936 10.802 21.936C9.81102 21.936 9.38801 21.4095 9.2085 20.968C9.0765 20.6425 8.80253 19.9669 9.26451 19.321C9.46651 19.035 9.75702 18.8885 10.126 18.8885C10.282 18.8885 10.4295 18.9135 10.5725 18.9379C10.753 18.9685 10.9245 18.9941 11.0375 18.964C11.4066 18.8638 11.6493 18.7381 11.9553 18.5797L11.9575 18.5785C12.108 18.5 12.2685 18.417 12.4565 18.3295C12.695 18.216 12.9555 18.171 13.1861 18.1315C13.235 18.123 13.293 18.1125 13.3495 18.1019C13.2725 18.081 13.183 18.06 13.116 18.0445C12.9636 18.0085 12.807 17.9699 12.662 17.9175C12.638 17.909 12.585 17.897 12.4956 17.897C12.3016 17.897 12.0599 17.9516 11.8257 18.0044L11.8165 18.0065C11.266 18.1305 10.7775 18.1621 10.5155 17.8665C10.414 17.7535 10.3025 17.5501 10.3405 17.225C10.416 16.5515 11.07 16.437 11.4215 16.3755L11.4484 16.3707C11.5191 16.3579 11.6056 16.3422 11.6675 16.325C11.654 16.2215 11.581 16.0705 11.2535 15.8885C11.2195 15.8701 11.1985 15.8685 11.182 15.8685C11.1105 15.8685 10.9955 15.912 10.874 15.9585C10.6315 16.0499 10.3115 16.1675 9.93152 16.115C9.26901 16.0179 8.87051 15.7221 8.48501 15.4355C8.31251 15.307 8.13401 15.1741 7.92301 15.0535C7.65101 14.8981 7.43801 14.737 7.25 14.5945L7.24817 14.5931C6.96354 14.3778 6.73823 14.2073 6.37301 14.105C5.83351 13.953 5.41901 13.996 4.77001 14.0915L4.586 14.1185C4.4475 14.1379 4.322 14.1505 4.20351 14.161C3.87376 14.1919 3.69141 14.2091 3.4615 14.3661C3.2965 14.4805 3.2515 14.6355 3.1725 14.947L3.17212 14.9485C3.09869 15.2387 3.00753 15.5991 2.73 15.937C2.4985 16.2265 2.25401 16.3935 2.03851 16.5405C1.86184 16.6608 1.70779 16.7657 1.5635 16.9379C1.3615 17.184 1.213 17.407 1.0905 17.637C1.253 19.132 1.634 20.5619 2.20301 21.8925C2.21555 21.8912 2.22809 21.8897 2.24062 21.8883C2.27602 21.8841 2.31129 21.88 2.346 21.88C2.86 21.88 3.24899 22.1505 3.62648 22.413L3.79251 22.5275C4.013 22.6735 4.319 22.941 4.673 23.251C5.16701 23.6835 6.19501 24.583 6.54401 24.6065C6.88151 24.6065 7.097 23.892 7.25401 23.3695L7.25947 23.3515C7.35443 23.0381 7.44395 22.7428 7.57101 22.523C7.92801 21.9045 8.43851 21.7745 8.80401 21.7745L8.85451 21.7755C9.33941 21.793 9.58491 22.1043 9.74741 22.3104C9.81891 22.4014 9.88051 22.4795 9.95001 22.5259C10.133 22.6475 10.299 22.7124 10.4909 22.7875L10.4951 22.7891C10.7316 22.8821 10.9997 22.9878 11.2965 23.2065C11.565 23.3995 11.729 23.6045 11.8735 23.785C12.003 23.947 12.105 24.0745 12.278 24.1895C12.3619 24.2449 12.4413 24.3024 12.5178 24.3579L12.529 24.3659C12.7975 24.56 12.911 24.6365 13.0915 24.6105C13.2036 24.5941 13.2475 24.5445 13.388 24.124L13.3898 24.1187C13.4733 23.8695 13.5678 23.5876 13.754 23.3221C13.8285 23.2128 13.8837 23.0805 13.9422 22.9401L13.9435 22.937C14.0805 22.608 14.2684 22.1569 14.831 21.9675C14.9611 21.9235 15.0895 21.9015 15.2131 21.9015C15.9373 21.9015 16.2558 22.5751 16.5118 23.1165C16.6278 23.362 16.748 23.6155 16.8876 23.7915C16.9287 23.8459 16.9659 23.8986 17 23.9496V25.6568C16.6575 25.3785 16.497 25.0841 16.3555 24.8245L16.3462 24.8076C16.2702 24.6698 16.1978 24.5385 16.0956 24.4035C15.8916 24.1465 15.74 23.8265 15.6065 23.544L15.6061 23.543C15.5049 23.3293 15.3174 22.933 15.216 22.901C15.203 22.899 15.1841 22.9025 15.149 22.9145C15.036 22.9525 14.9935 23.013 14.865 23.321C14.7936 23.4939 14.7115 23.6905 14.5755 23.8895C14.4685 24.0432 14.4042 24.2345 14.336 24.4374L14.335 24.4405C14.195 24.859 13.9835 25.4916 13.231 25.5995C12.6391 25.682 12.2657 25.41 11.942 25.1742L11.941 25.1735L11.9178 25.1568C11.8561 25.1125 11.7924 25.0668 11.724 25.0215C11.421 24.8192 11.2472 24.6025 11.0931 24.4104L11.0915 24.4085L11.0868 24.4025C10.9688 24.2556 10.8664 24.128 10.706 24.0135C10.509 23.8675 10.331 23.7975 10.125 23.717C9.91002 23.633 9.66752 23.5381 9.39402 23.3565C9.19251 23.2221 9.06351 23.059 8.96051 22.928L8.95815 22.925C8.91417 22.869 8.84952 22.7867 8.82201 22.772C8.69501 22.7725 8.56851 22.7915 8.43601 23.0205C8.36501 23.1445 8.28601 23.4045 8.21051 23.656L8.20658 23.669C7.96654 24.4653 7.60136 25.6766 6.47751 25.6025C5.83801 25.559 5.05301 24.9115 4.013 24.0015C3.69951 23.727 3.40351 23.4685 3.23551 23.357L3.05451 23.2325C2.94701 23.1581 2.8465 23.0905 2.7505 23.032C5.27101 27.772 10.258 31 16.0001 31C16.3361 31 16.6695 30.989 17 30.9672V31.9693C16.6693 31.9897 16.3359 32 16.0001 32C7.16351 32 0 24.8365 0 16ZM13.9975 3.10501L13.9095 3.2075C13.667 3.4995 13.4136 3.644 13.1895 3.7715L13.188 3.77233C13.0456 3.85349 12.9221 3.92388 12.7935 4.03499C12.68 4.1345 12.561 4.21501 12.447 4.2905C12.238 4.42901 12.2005 4.4655 12.179 4.55C12.1848 4.55633 12.2107 4.58735 12.2405 4.62292L12.306 4.70101C12.466 4.89401 12.7075 5.18517 12.6775 5.618C12.6396 6.10301 12.488 6.4435 12.1545 6.7965C12.0355 6.9255 11.92 7.0005 11.8265 7.0605L11.8004 7.07771C11.7844 7.08822 11.7679 7.09909 11.7505 7.11101C11.7065 7.1675 11.6055 7.3085 11.5405 7.4965C11.521 7.55501 11.511 7.5935 11.5055 7.6165C11.521 7.6565 11.533 7.6885 11.5445 7.72499C11.598 7.88499 11.618 8.12301 11.625 8.3015C11.785 8.35901 11.9305 8.426 12.1095 8.514C12.3525 8.632 12.518 8.7465 12.678 8.858L12.6835 8.8615C12.7051 8.8605 12.7245 8.8595 12.7411 8.8585L12.764 8.8495L12.905 8.852C13.009 8.86227 13.1001 8.86961 13.186 8.87652L13.229 8.88C13.4415 8.8975 13.6291 8.91301 13.8915 8.95101L14.03 8.968C14.1821 8.9865 14.3256 9.004 14.527 9.058C14.8065 9.1355 14.996 9.28501 15.134 9.394C15.2736 9.504 15.3096 9.52499 15.36 9.52499C15.41 9.5245 15.4416 9.50901 15.573 9.42C15.695 9.33701 15.8626 9.224 16.094 9.1585C16.376 9.0785 16.6836 9.0695 16.997 9.0855C17.193 9.096 17.388 9.09899 17.561 9.056C17.6114 9.04363 17.6529 9.02435 17.6926 9.00592L17.713 8.9965L17.7187 8.9939C17.8357 8.94064 17.9936 8.86873 18.217 8.852C18.2425 8.8495 18.277 8.8475 18.3106 8.8475L18.419 8.854C18.5971 8.8685 18.751 8.934 18.864 8.98299C18.9516 9.0205 18.9996 9.04 19.0481 9.042C19.1566 9.0145 19.625 8.70901 19.763 8.588C19.9615 8.4115 20.141 8.3275 20.299 8.254L20.3309 8.23884C20.3885 8.21159 20.4494 8.18276 20.516 8.144C20.595 8.09106 20.6763 8.02369 20.7535 7.95982L20.7581 7.956L20.7622 7.95258C20.9407 7.80641 21.1426 7.64111 21.4721 7.52699C21.877 7.3835 22.3465 7.2575 22.8571 7.4265C23.144 7.52699 23.2966 7.64101 23.4965 7.8035C23.6821 7.9535 23.7884 8.10496 23.8739 8.22685L23.8765 8.2305L23.8841 8.24149C23.9253 8.30072 23.9689 8.36317 24.031 8.43C24.0965 8.5025 24.146 8.56501 24.193 8.6245C24.2815 8.7365 24.3156 8.7775 24.387 8.8165C24.4527 8.85283 24.5351 8.83587 24.659 8.81036L24.6925 8.8035C24.8691 8.7675 25.1091 8.72101 25.3755 8.76L25.5315 8.78101L25.5396 8.78205C25.6881 8.80121 25.8415 8.82102 26.0091 8.877C26.1145 8.91 26.2181 8.954 26.3175 9.0085C26.739 9.2325 26.929 9.48499 27.1695 9.8445C27.4505 10.273 27.5175 10.6125 27.5945 11.006L27.6291 11.1805C27.7047 11.5682 27.7134 11.8351 27.7233 12.1418L27.7236 12.152L27.733 12.4165C27.7375 12.529 27.7471 12.6265 27.7565 12.719C27.782 12.9805 27.8081 13.2505 27.7195 13.6165C27.6406 13.9524 27.4746 14.1699 27.3412 14.3449L27.3373 14.35C27.2423 14.4754 27.1731 14.5667 27.138 14.6941L27.1105 14.789C27.0315 15.06 27.008 15.1535 27.045 15.3215C27.0845 15.4885 27.09 15.7115 27.0876 15.9535C27.2105 15.8339 27.402 15.688 27.656 15.688C27.7891 15.688 28.0416 15.732 28.2325 16.0235C28.374 16.248 28.37 16.4665 28.341 16.6245C28.3901 16.6085 28.437 16.589 28.4755 16.5699C28.4749 16.5528 28.4738 16.5359 28.4727 16.5191L28.471 16.4935L28.4709 16.4918C28.4589 16.2729 28.4425 15.9737 28.6435 15.6499C28.8555 15.3066 29.1709 15.216 29.3799 15.156C29.4868 15.125 29.5551 15.1045 29.5901 15.0705C29.6446 15.016 29.6746 14.9525 29.727 14.8355C29.7946 14.6854 29.8786 14.4989 30.061 14.3105C30.2215 14.1415 30.3955 14.0575 30.523 13.996C30.6346 13.9425 30.6576 13.928 30.6756 13.9C30.673 13.892 30.6706 13.8615 30.6715 13.8315C30.674 13.724 30.686 13.5539 30.768 13.381C29.924 8.5905 26.8105 4.5835 22.573 2.514C22.3695 2.6015 22.137 2.64499 21.859 2.6055C21.365 2.54299 21.1625 2.18 21.057 1.8755C20.756 1.768 20.4505 1.672 20.1415 1.5835C20.0545 1.6435 19.9536 1.6985 19.8235 1.7295C19.3836 1.8335 19.034 1.7325 18.738 1.60501C18.4591 1.4905 18.287 1.32 18.156 1.1565C17.4516 1.055 16.7326 1 16.0001 1C15.4035 1 14.8165 1.0385 14.238 1.1065C14.397 1.3445 14.544 1.6385 14.511 2.05501C14.473 2.5559 14.2204 2.84754 13.9975 3.10501ZM25.4985 20.3425C26.1895 19.6383 27.1237 19.2412 28.1019 19.2501C29.08 19.2591 30.0071 19.6733 30.686 20.3895C31.3637 21.1045 31.7417 22.0636 31.7499 23.0585C31.7581 24.0533 31.396 25.0189 30.7306 25.7457L30.7218 25.7553L28.9635 27.61C28.5926 28.0014 28.1435 28.307 27.6455 28.5029C27.1473 28.699 26.6134 28.7802 26.0808 28.74C25.5482 28.6997 25.0319 28.5392 24.5671 28.2713C24.1024 28.0035 23.701 27.6353 23.3882 27.194C23.1486 26.8561 23.2283 26.388 23.5662 26.1484C23.9041 25.9088 24.3722 25.9885 24.6118 26.3264C24.8024 26.5952 25.0432 26.8144 25.3161 26.9717C25.5888 27.1289 25.8881 27.2211 26.1938 27.2442C26.4994 27.2673 26.8069 27.2209 27.0962 27.1071C27.3855 26.9933 27.6515 26.8138 27.8747 26.5783L29.6283 24.7283C30.028 24.2894 30.2551 23.695 30.2499 23.0708C30.2448 22.4445 30.0063 21.8529 29.5974 21.4214C29.1896 20.9913 28.6467 20.7552 28.0881 20.7501C27.5312 20.745 26.9864 20.9697 26.5725 21.3896L25.5685 22.4427C25.2826 22.7425 24.8079 22.7538 24.5081 22.4679C24.2083 22.1821 24.197 21.7074 24.4828 21.4076L25.4909 20.3503L25.4985 20.3425ZM23.9192 23.26C23.3866 23.2198 22.8527 23.301 22.3545 23.4971C21.8565 23.693 21.4075 23.9985 21.0366 24.3899L19.2783 26.2448L19.2694 26.2543C18.604 26.9811 18.2419 27.9467 18.2501 28.9415C18.2583 29.9364 18.6363 30.8955 19.314 31.6105C19.9929 32.3267 20.92 32.7409 21.8981 32.7499C22.8763 32.7588 23.8105 32.3617 24.5015 31.6575L24.5106 31.6481L25.5128 30.5908C25.7978 30.2902 25.7851 29.8155 25.4844 29.5306C25.1838 29.2456 24.7091 29.2583 24.4242 29.5589L23.4268 30.6111C23.013 31.0305 22.4685 31.255 21.9119 31.2499C21.3533 31.2448 20.8104 31.0087 20.4026 30.5786C19.9937 30.1471 19.7552 29.5555 19.7501 28.9292C19.7449 28.305 19.972 27.7106 20.3717 27.2717L22.1252 25.4218C22.3484 25.1863 22.6145 25.0067 22.9038 24.8929C23.1931 24.7791 23.5006 24.7327 23.8062 24.7558C24.1119 24.7789 24.4112 24.8711 24.6839 25.0283C24.9568 25.1856 25.1976 25.4048 25.3882 25.6736C25.6278 26.0115 26.0959 26.0912 26.4338 25.8516C26.7717 25.612 26.8514 25.1439 26.6118 24.806C26.299 24.3647 25.8976 23.9965 25.4329 23.7287C24.9681 23.4608 24.4518 23.3003 23.9192 23.26Z"
        fill="black"
      />
    </g>
    <defs>
      <clipPath id="clip0_422_4">
        <rect width="32" height="33" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconUserSvg = () => (
  <svg
    width="27"
    height="26"
    viewBox="0 0 27 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.1265 18.2045C8.86216 18.2221 5.29831 21.1722 4.38179 25.1248L4.37023 25.1847C4.36045 25.227 4.35512 25.2754 4.35512 25.3257C4.35512 25.6903 4.65381 25.9863 5.02184 25.9863C5.3392 25.9863 5.605 25.7661 5.67167 25.471L5.67256 25.4666C6.4504 22.0443 9.49243 19.5259 13.1274 19.5259C16.7624 19.5259 19.8044 22.0443 20.5725 25.4164L20.5822 25.4666C20.6498 25.7661 20.9147 25.9863 21.233 25.9863C21.2836 25.9863 21.3334 25.9802 21.3805 25.9696L21.3761 25.9705C21.6783 25.9035 21.9006 25.6401 21.9006 25.3248C21.9006 25.2746 21.8952 25.2261 21.8846 25.1794L21.8855 25.1838C20.9565 21.1723 17.3918 18.2221 13.1301 18.2036H13.1283L13.1265 18.2045ZM7.04956 8.23811C9.34487 8.23811 11.2064 6.3944 11.2064 4.11905C11.2064 1.84371 9.34575 0 7.04956 0C4.75426 0 2.89278 1.84371 2.89278 4.11905C2.89544 6.39264 4.75604 8.23546 7.04956 8.23811ZM7.04956 1.32134C8.6088 1.32134 9.87291 2.57397 9.87291 4.11905C9.87291 5.66414 8.6088 6.91677 7.04956 6.91677C5.49032 6.91677 4.22622 5.66414 4.22622 4.11905C4.228 2.57485 5.4921 1.3231 7.04956 1.32134ZM19.2835 8.23811C21.5788 8.23811 23.4403 6.3944 23.4403 4.11905C23.4403 1.84371 21.5797 0 19.2835 0C16.9882 0 15.1267 1.84371 15.1267 4.11905C15.1293 6.39264 16.9899 8.23546 19.2835 8.23811ZM19.2835 1.32134C20.8427 1.32134 22.1068 2.57397 22.1068 4.11905C22.1068 5.66414 20.8427 6.91677 19.2835 6.91677C17.7242 6.91677 16.4601 5.66414 16.4601 4.11905C16.4619 2.57485 17.7251 1.3231 19.2835 1.32134ZM26.3027 16.3758C25.5222 12.9456 22.4731 10.4218 18.8301 10.4218C18.4541 10.4218 18.0843 10.4491 17.7224 10.5002L17.7633 10.495C17.7198 10.5055 17.6816 10.5196 17.6451 10.5364L17.6487 10.5346C16.9811 8.65917 15.2085 7.3396 13.1247 7.3396C11.0499 7.3396 9.28353 8.64772 8.61947 10.4765L8.60881 10.5099C8.59369 10.5064 8.58214 10.4976 8.56703 10.495C8.24789 10.4483 7.87986 10.421 7.50471 10.421H7.49849C3.86086 10.4368 0.821495 12.9526 0.0383187 16.3238L0.0285402 16.3749C0.0187616 16.4172 0.0134277 16.4656 0.0134277 16.5159C0.0134277 16.8805 0.312119 17.1765 0.68015 17.1765C0.997509 17.1765 1.26331 16.9563 1.32998 16.6612L1.33087 16.6568C1.98604 13.8318 4.49558 11.7546 7.4976 11.7423H7.49938C7.79718 11.7423 8.08965 11.7634 8.37501 11.804L8.34212 11.8004C8.33501 11.9053 8.31011 12.0048 8.31011 12.1123C8.31545 14.7435 10.4694 16.8744 13.1256 16.8744C15.7818 16.8744 17.9358 14.7435 17.9402 12.1123C17.9402 12.0066 17.9154 11.9061 17.9082 11.8013C17.9225 11.7996 17.9358 11.8057 17.95 11.8022C18.2114 11.7634 18.5136 11.7423 18.8212 11.7423C18.8248 11.7423 18.8274 11.7423 18.831 11.7423C21.8348 11.7537 24.3443 13.8309 24.9906 16.6145L24.9986 16.6568C25.0662 16.9563 25.332 17.1756 25.6493 17.1765C25.7 17.1765 25.7489 17.1704 25.796 17.1598L25.7916 17.1607C26.0938 17.0937 26.316 16.8303 26.316 16.515C26.316 16.4648 26.3107 16.4163 26.3 16.3696L26.3009 16.374L26.3027 16.3758ZM13.1265 15.5618C11.2037 15.5618 9.64356 14.0176 9.64356 12.1114C9.64356 10.2051 11.2028 8.66093 13.1256 8.66093C15.0484 8.66093 16.6077 10.206 16.6077 12.1114C16.605 14.0159 15.0485 15.5592 13.1265 15.5618Z"
      fill="#1E1E1E"
    />
  </svg>
);

export const IconListSvg = () => (
  <svg
    width="25"
    height="17"
    viewBox="0 0 25 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.03027 0.798828H23.7172"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.03027 8.5H23.7172"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.03027 16.2031H23.7172"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M0.612305 0.798828H0.624153"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M0.612305 8.5H0.624153"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M0.612305 16.2031H0.624153"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const IconExecutionSvg = () => (
  <svg
    width="24"
    height="26"
    viewBox="0 0 24 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M23.1514 17.9138V8.06876C23.1509 7.63715 23.037 7.21324 22.821 6.83956C22.605 6.46589 22.2945 6.15558 21.9207 5.93977L13.3063 1.01725C12.9322 0.801226 12.5077 0.6875 12.0757 0.6875C11.6436 0.6875 11.2192 0.801226 10.8451 1.01725L2.23063 5.93977C1.85684 6.15558 1.54638 6.46589 1.33038 6.83956C1.11438 7.21324 1.00044 7.63715 1 8.06876V17.9138C1.00044 18.3454 1.11438 18.7693 1.33038 19.143C1.54638 19.5167 1.85684 19.827 2.23063 20.0428L10.8451 24.9653C11.2192 25.1814 11.6436 25.2951 12.0757 25.2951C12.5077 25.2951 12.9322 25.1814 13.3063 24.9653L21.9207 20.0428C22.2945 19.827 22.605 19.5167 22.821 19.143C23.037 18.7693 23.1509 18.3454 23.1514 17.9138Z"
      stroke="#1E1E1E"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.33203 6.79004L12.0754 13.0047L22.8189 6.79004"
      stroke="#1E1E1E"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.0762 25.396V12.9912"
      stroke="#1E1E1E"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const IconValidationHistorySvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_171_4)">
      <path
        d="M21.5 0H2.5C1.122 0 0 1.121 0 2.5V24H16.707L24 16.707V2.5C24 1.121 22.878 0 21.5 0ZM1 2.5C1 1.673 1.673 1 2.5 1H21.5C22.327 1 23 1.673 23 2.5V16H16V23H1V2.5ZM22.293 17L17 22.293V17H22.293ZM4 6C4 5.448 4.448 5 5 5C5.552 5 6 5.448 6 6C6 6.552 5.552 7 5 7C4.448 7 4 6.552 4 6ZM6 12C6 12.552 5.552 13 5 13C4.448 13 4 12.552 4 12C4 11.448 4.448 11 5 11C5.552 11 6 11.448 6 12ZM6 18C6 18.552 5.552 19 5 19C4.448 19 4 18.552 4 18C4 17.448 4.448 17 5 17C5.552 17 6 17.448 6 18Z"
        fill="#1E1E1E"
      />
    </g>
    <defs>
      <clipPath id="clip0_171_4">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconValidationResultSvg = () => (
  <svg
    width="22"
    height="23"
    viewBox="0 0 22 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.22461 11.46V22.2285H21.105V0.691406H1.22461V4.72961M4.95219 6.07568H17.3774M4.95219 11.46H17.3774M4.95219 16.8442H12.4073"
      stroke="#1E1E1E"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const IconConsolidatedSvg = () => (
  <svg
    width="20"
    height="23"
    viewBox="0 0 20 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.7399 0H3.03027C1.71942 0 0.6521 1.06732 0.6521 2.37817V22.8305H19.6775V6.93761L12.7399 0ZM13.0186 1.62382L18.0537 6.65889H13.0186V1.62382ZM1.60337 21.8792V2.37817C1.60337 1.59147 2.24357 0.951269 3.03027 0.951269H12.0673V7.61016H18.7262V21.8792H1.60337ZM5.40845 15.2203H6.35972V19.0254H5.40845V15.2203ZM13.0186 13.3178H13.9699V19.0254H13.0186V13.3178ZM9.21352 10.464H10.1648V19.0254H9.21352V10.464Z"
      fill="black"
    />
  </svg>
);
export const IconDownloadSvg = () => (
  <svg
    width="20"
    height="18"
    viewBox="0 0 20 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 7.72852V16.7285H19V7.72852"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="stroke"
    />
    <path
      d="M10.75 1.5C10.75 1.08579 10.4142 0.75 10 0.75C9.58579 0.75 9.25 1.08579 9.25 1.5H10.75ZM9.46967 13.8838C9.76256 14.1767 10.2374 14.1767 10.5303 13.8838L15.3033 9.11087C15.5962 8.81798 15.5962 8.34311 15.3033 8.05021C15.0104 7.75732 14.5355 7.75732 14.2426 8.05021L10 12.2929L5.75736 8.05021C5.46447 7.75732 4.98959 7.75732 4.6967 8.05021C4.40381 8.34311 4.40381 8.81798 4.6967 9.11087L9.46967 13.8838ZM9.25 1.5V13.3535H10.75V1.5H9.25Z"
      className="fill"
    />
  </svg>
);

export const IconWatchSvg = () => (
  <svg
    width="24"
    height="26"
    viewBox="0 0 24 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12.251" cy="14.4102" r="11" stroke="#1E1E1E" />
    <rect x="9.25098" y="1.41016" width="6" height="2" stroke="#1E1E1E" />
    <path d="M12.251 7.91016V14.9102L16.751 19.4102" stroke="#1E1E1E" />
  </svg>
);

export const IconDownloadSvgBlue = () => (
  <svg
    width="38"
    height="38"
    viewBox="0 0 38 38"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="0.750977" y="1.41016" width="36" height="36" rx="4" fill="white" />
    <rect
      x="0.750977"
      y="1.41016"
      width="36"
      height="36"
      rx="4"
      stroke="#0967B1"
    />
    <path
      d="M25.751 26.9102H26.251V26.4102V19.9102H27.251V26.4102C27.251 27.234 26.5748 27.9102 25.751 27.9102H11.751C10.9271 27.9102 10.251 27.234 10.251 26.4102V19.9102H11.251V26.4102V26.9102H11.751H25.751ZM19.251 20.0802V21.284L20.1038 20.4344L22.3403 18.2066L23.0439 18.9102L18.751 23.203L14.4581 18.9102L15.1617 18.2066L17.3981 20.4344L18.251 21.284V20.0802V10.9102H19.251V20.0802Z"
      fill="#F48400"
      stroke="#0967B1"
    />
  </svg>
);

export const IconViewSvgWhite = () => (
  <svg
    width="27"
    height="20"
    viewBox="0 0 27 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 10C1 10 5.50062 1 13.3745 1C21.2485 1 25.7491 10 25.7491 10C25.7491 10 21.2497 19 13.3745 19C5.49938 19 1 10 1 10Z"
      stroke="none"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.3759 13.6016C15.3644 13.6016 16.9764 11.9896 16.9764 10.0011C16.9764 8.01263 15.3644 6.40063 13.3759 6.40063C11.3874 6.40063 9.77539 8.01263 9.77539 10.0011C9.77539 11.9896 11.3874 13.6016 13.3759 13.6016Z"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const IconResourceColumnSvg = () => (
  <svg
    width="22"
    height="23"
    viewBox="0 0 22 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.1648 1.41797H19.1535C19.7588 1.41797 20.3394 1.65844 20.7674 2.08649C21.1955 2.51454 21.436 3.09511 21.436 3.70046V19.6779C21.436 20.2833 21.1955 20.8638 20.7674 21.2919C20.3394 21.7199 19.7588 21.9604 19.1535 21.9604H11.1648M11.1648 1.41797H3.17605C2.57069 1.41797 1.99013 1.65844 1.56208 2.08649C1.13403 2.51454 0.893555 3.09511 0.893555 3.70046V19.6779C0.893555 20.2833 1.13403 20.8638 1.56208 21.2919C1.99013 21.7199 2.57069 21.9604 3.17605 21.9604H11.1648M11.1648 1.41797V21.9604"
      stroke=""
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const IconReportsSvg = () => (
  <svg
    width="19"
    height="24"
    viewBox="0 0 19 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.94092 1.39453H11.371L17.5017 7.76108V21.087C17.5017 21.4994 17.3437 21.8895 17.0708 22.1728C16.7989 22.4552 16.4363 22.6084 16.0645 22.6084H2.94092C2.56906 22.6084 2.20644 22.4552 1.93453 22.1728C1.6617 21.8895 1.50366 21.4994 1.50366 21.087V2.91591C1.50366 2.50348 1.6617 2.11336 1.93453 1.83003C2.20644 1.54767 2.56906 1.39453 2.94092 1.39453Z"
      style={{ stroke: "var(--dark-blue)" }}
      strokeWidth="0.75"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <mask id="path-2-inside-1_2177_2476" fill="white">
      <path d="M11.6899 0.644531V7.45868H18.2517" />
    </mask>
    <path
      d="M12.4399 0.644531C12.4399 0.230318 12.1042 -0.105469 11.6899 -0.105469C11.2757 -0.105469 10.9399 0.230318 10.9399 0.644531H12.4399ZM11.6899 7.45868H10.9399C10.9399 7.87289 11.2757 8.20868 11.6899 8.20868V7.45868ZM18.2517 8.20868C18.6659 8.20868 19.0017 7.87289 19.0017 7.45868C19.0017 7.04446 18.6659 6.70868 18.2517 6.70868V8.20868ZM10.9399 0.644531V7.45868H12.4399V0.644531H10.9399ZM11.6899 8.20868H18.2517V6.70868H11.6899V8.20868Z"
      style={{ fill: "var(--dark-blue)" }}
      mask="url(#path-2-inside-1_2177_2476)"
    />
    <path
      d="M13.8772 13.1367H5.12817"
      style={{ stroke: "var(--dark-blue)" }}
      strokeWidth="0.75"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.50263 8.39453H5.12817"
      style={{ stroke: "var(--dark-blue)" }}
      strokeWidth="0.75"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.8772 17.6816H5.12817"
      style={{ stroke: "var(--dark-blue)" }}
      strokeWidth="0.75"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const IconLinkedServicesSvg = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_214_10)">
      <path
        d="M8.7041 11.9336C9.13355 12.5077 9.68146 12.9827 10.3107 13.3265C10.9398 13.6703 11.6356 13.8747 12.3508 13.9259C13.0659 13.9771 13.7837 13.8739 14.4554 13.6233C15.1272 13.3728 15.7372 12.9806 16.2441 12.4736L19.2441 9.47356C20.1549 8.53055 20.6589 7.26754 20.6475 5.95655C20.6361 4.64557 20.1102 3.39151 19.1832 2.46447C18.2562 1.53743 17.0021 1.01158 15.6911 1.00019C14.3801 0.988796 13.1171 1.49277 12.1741 2.40356L10.4541 4.11356"
        stroke="black"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.7043 9.93284C12.2749 9.35871 11.727 8.88366 11.0978 8.5399C10.4686 8.19615 9.77279 7.99173 9.05765 7.94051C8.3425 7.88929 7.62471 7.99248 6.95295 8.24306C6.28119 8.49365 5.67118 8.88577 5.1643 9.39284L2.1643 12.3928C1.25351 13.3359 0.749539 14.5989 0.760931 15.9098C0.772323 17.2208 1.29816 18.4749 2.22521 19.4019C3.15225 20.329 4.40631 20.8548 5.71729 20.8662C7.02828 20.8776 8.29129 20.3736 9.2343 19.4628L10.9443 17.7528"
        stroke="black"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_214_10">
        <rect width="22" height="22" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconConnectionKeySvg = () => (
  <svg
    width="22"
    height="21"
    viewBox="0 0 22 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_214_2)">
      <path
        d="M19.6702 0.886719L17.6702 2.88672M17.6702 2.88672L20.6702 5.88672L17.1702 9.38672L14.1702 6.38672M17.6702 2.88672L14.1702 6.38672M14.1702 6.38672L10.0602 10.4967M10.0602 10.4967C10.5765 11.0062 10.987 11.6128 11.2679 12.2815C11.5489 12.9503 11.6948 13.668 11.6972 14.3934C11.6996 15.1187 11.5586 15.8374 11.2821 16.508C11.0056 17.1786 10.5992 17.7879 10.0863 18.3009C9.57342 18.8138 8.96412 19.2202 8.2935 19.4966C7.62288 19.7731 6.90419 19.9142 6.17883 19.9117C5.45346 19.9093 4.73574 19.7634 4.06699 19.4825C3.39823 19.2015 2.79166 18.7911 2.28219 18.2747C1.28032 17.2374 0.725958 15.8481 0.738489 14.406C0.75102 12.9639 1.32945 11.5845 2.34919 10.5647C3.36894 9.54497 4.74841 8.96654 6.19049 8.95401C7.63257 8.94148 9.02188 9.49585 10.0592 10.4977L10.0602 10.4967Z"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_214_2">
        <rect width="22" height="21" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconFileProcessingSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_214_4)">
      <path
        d="M11.7041 14.9121C13.361 14.9121 14.7041 13.569 14.7041 11.9121C14.7041 10.2553 13.361 8.91211 11.7041 8.91211C10.0472 8.91211 8.7041 10.2553 8.7041 11.9121C8.7041 13.569 10.0472 14.9121 11.7041 14.9121Z"
        stroke="black"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.1041 14.9121C18.971 15.2137 18.9313 15.5483 18.9901 15.8727C19.0489 16.1971 19.2036 16.4964 19.4341 16.7321L19.4941 16.7921C19.6801 16.9779 19.8276 17.1984 19.9282 17.4412C20.0289 17.684 20.0807 17.9443 20.0807 18.2071C20.0807 18.4699 20.0289 18.7302 19.9282 18.973C19.8276 19.2158 19.6801 19.4364 19.4941 19.6221C19.3084 19.8081 19.0878 19.9556 18.845 20.0562C18.6022 20.1569 18.3419 20.2087 18.0791 20.2087C17.8163 20.2087 17.556 20.1569 17.3132 20.0562C17.0704 19.9556 16.8498 19.8081 16.6641 19.6221L16.6041 19.5621C16.3684 19.3316 16.0691 19.1769 15.7447 19.1181C15.4203 19.0593 15.0857 19.099 14.7841 19.2321C14.4883 19.3589 14.2361 19.5694 14.0584 19.8376C13.8807 20.1059 13.7854 20.4203 13.7841 20.7421V20.9121C13.7841 21.4425 13.5734 21.9512 13.1983 22.3263C12.8232 22.7014 12.3145 22.9121 11.7841 22.9121C11.2537 22.9121 10.745 22.7014 10.3699 22.3263C9.99482 21.9512 9.7841 21.4425 9.7841 20.9121V20.8221C9.77636 20.4911 9.66922 20.1701 9.47661 19.9008C9.284 19.6315 9.01484 19.4264 8.7041 19.3121C8.40249 19.179 8.06791 19.1393 7.74351 19.1981C7.41912 19.2569 7.11978 19.4116 6.8841 19.6421L6.8241 19.7021C6.63836 19.8881 6.41778 20.0356 6.17498 20.1362C5.93219 20.2369 5.67193 20.2887 5.4091 20.2887C5.14627 20.2887 4.88602 20.2369 4.64322 20.1362C4.40042 20.0356 4.17985 19.8881 3.9941 19.7021C3.80815 19.5164 3.66063 19.2958 3.55998 19.053C3.45933 18.8102 3.40753 18.5499 3.40753 18.2871C3.40753 18.0243 3.45933 17.764 3.55998 17.5212C3.66063 17.2784 3.80815 17.0579 3.9941 16.8721L4.0541 16.8121C4.28464 16.5764 4.43929 16.2771 4.49811 15.9527C4.55692 15.6283 4.51722 15.2937 4.3841 14.9921C4.25734 14.6963 4.04686 14.4441 3.77857 14.2664C3.51028 14.0887 3.19589 13.9934 2.8741 13.9921H2.7041C2.17367 13.9921 1.66496 13.7814 1.28989 13.4063C0.914815 13.0312 0.704102 12.5225 0.704102 11.9921C0.704102 11.4617 0.914815 10.953 1.28989 10.5779C1.66496 10.2028 2.17367 9.99211 2.7041 9.99211H2.7941C3.1251 9.98437 3.44611 9.87723 3.7154 9.68462C3.9847 9.49201 4.18982 9.22284 4.3041 8.91211C4.43722 8.61049 4.47692 8.27592 4.41811 7.95152C4.35929 7.62713 4.20464 7.32779 3.9741 7.09211L3.9141 7.03211C3.72815 6.84636 3.58063 6.62579 3.47998 6.38299C3.37933 6.14019 3.32753 5.87994 3.32753 5.61711C3.32753 5.35428 3.37933 5.09402 3.47998 4.85123C3.58063 4.60843 3.72815 4.38786 3.9141 4.20211C4.09985 4.01616 4.32042 3.86864 4.56322 3.76799C4.80602 3.66734 5.06627 3.61554 5.3291 3.61554C5.59193 3.61554 5.85219 3.66734 6.09498 3.76799C6.33778 3.86864 6.55836 4.01616 6.7441 4.20211L6.8041 4.26211C7.03978 4.49265 7.33912 4.64729 7.66351 4.70611C7.98791 4.76493 8.32249 4.72522 8.6241 4.59211H8.7041C8.99987 4.46535 9.25212 4.25486 9.42979 3.98657C9.60747 3.71828 9.70282 3.4039 9.7041 3.08211V2.91211C9.7041 2.38168 9.91482 1.87297 10.2899 1.4979C10.665 1.12282 11.1737 0.912109 11.7041 0.912109C12.2345 0.912109 12.7432 1.12282 13.1183 1.4979C13.4934 1.87297 13.7041 2.38168 13.7041 2.91211V3.00211C13.7054 3.3239 13.8007 3.63828 13.9784 3.90657C14.1561 4.17487 14.4083 4.38535 14.7041 4.51211C15.0057 4.64522 15.3403 4.68493 15.6647 4.62611C15.9891 4.56729 16.2884 4.41265 16.5241 4.18211L16.5841 4.12211C16.7698 3.93616 16.9904 3.78864 17.2332 3.68799C17.476 3.58734 17.7363 3.53554 17.9991 3.53554C18.2619 3.53554 18.5222 3.58734 18.765 3.68799C19.0078 3.78864 19.2284 3.93616 19.4141 4.12211C19.6001 4.30786 19.7476 4.52843 19.8482 4.77123C19.9489 5.01402 20.0007 5.27428 20.0007 5.53711C20.0007 5.79994 19.9489 6.06019 19.8482 6.30299C19.7476 6.54579 19.6001 6.76636 19.4141 6.95211L19.3541 7.01211C19.1236 7.24779 18.9689 7.54713 18.9101 7.87152C18.8513 8.19592 18.891 8.53049 19.0241 8.83211V8.91211C19.1509 9.20788 19.3613 9.46013 19.6296 9.6378C19.8979 9.81548 20.2123 9.91083 20.5341 9.91211H20.7041C21.2345 9.91211 21.7432 10.1228 22.1183 10.4979C22.4934 10.873 22.7041 11.3817 22.7041 11.9121C22.7041 12.4425 22.4934 12.9512 22.1183 13.3263C21.7432 13.7014 21.2345 13.9121 20.7041 13.9121H20.6141C20.2923 13.9134 19.9779 14.0087 19.7096 14.1864C19.4413 14.3641 19.2309 14.6163 19.1041 14.9121Z"
        stroke="black"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_214_4">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconRunInstanceSvg = () => (
  <svg
    width="19"
    height="21"
    viewBox="0 0 19 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_214_7)">
      <path
        d="M1.31934 16.8719L9.03363 9.15764L1.31934 1.44336"
        stroke="black"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.80322 19.4434H18.0889"
        stroke="black"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_214_7">
        <rect width="19" height="21" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const IconDownloadBlueSvg = () => (
  <svg
    className="download-svg-icon"
    focusable="false"
    viewBox="0 0 24 24"
    data-testid="SaveAltIcon"
  >
    <path d="M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"></path>
  </svg>
);
export const IconClearSvg = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.9496 0.950054C10.6458 0.646313 10.1534 0.646313 9.84962 0.950054L6.00089 4.79878L2.15078 0.948673C1.84704 0.644932 1.35458 0.644931 1.05084 0.948672C0.747098 1.25241 0.747098 1.74487 1.05084 2.04862L4.90095 5.89873L1.05007 9.74961C0.74633 10.0533 0.74633 10.5458 1.05007 10.8495C1.35381 11.1533 1.84627 11.1533 2.15001 10.8495L6.00089 6.99867L9.85039 10.8482C10.1541 11.1519 10.6466 11.1519 10.9503 10.8482C11.2541 10.5444 11.2541 10.052 10.9503 9.74822L7.10084 5.89873L10.9496 2.05C11.2533 1.74626 11.2533 1.2538 10.9496 0.950054Z"
      fill="#555B69"
    />
  </svg>
);
export const IconAddTolranceDataSvg = () => (
  <svg
    id="Layer_2"
    data-name="Layer 2"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 130.32 115.17"
  >
    <g id="Layer_1-2" data-name="Layer 1">
      <g>
        <g>
          <path
            fill="#0967B1"
            className="cls-1"
            d="M19.06,65.27c10.53-18.23,21.05-36.45,31.58-54.68,1.97-3.44,5.13-4.76,8.47-3.57,1.76.65,2.92,1.94,3.83,3.51,4.64,8.08,9.3,16.13,13.96,24.19,4.92,8.51,9.83,17.02,14.75,25.55,2.11-.83,4.36-1.4,6.69-1.68-6-10.45-12.03-20.87-18.05-31.3-3.93-6.81-7.84-13.65-11.81-20.44C65.49,1.79,60.99-.54,55.1.11c-4.54.51-7.84,3-10.09,6.93-7.58,13.15-15.19,26.28-22.77,39.43-6.73,11.63-13.45,23.28-20.18,34.91-1.84,3.16-2.57,6.5-1.7,10.11,1.52,6.32,6.77,10.39,13.43,10.39h64.1c-1.3-2.05-2.35-4.29-3.08-6.65H13.95c-3.85,0-6.56-2.15-7.19-5.73-.32-1.72.2-3.28,1.05-4.76,3.75-6.48,7.5-12.97,11.26-19.47Z"
          />
          <path
            fill="#0967B1"
            className="cls-1"
            d="M60.05,66.39h-6.52v-33.13h6.52v33.13Z"
          />
          <path
            fill="#0967B1"
            className="cls-1"
            d="M61.23,77.57c-.02,2.43-2.04,4.41-4.48,4.39-2.43-.02-4.41-2.04-4.39-4.48.02-2.44,2.03-4.41,4.48-4.39,2.44.02,4.41,2.03,4.39,4.47Z"
          />
        </g>
        <g>
          <path
            fill="#0967B1"
            className="cls-1"
            d="M101.92,58.36c-1.2,0-2.41.08-3.57.24-2.33.28-4.58.85-6.69,1.68-10.6,4.13-18.13,14.45-18.13,26.5,0,2.94.45,5.79,1.28,8.45.73,2.37,1.78,4.6,3.08,6.65,5.04,7.98,13.92,13.29,24.03,13.29,15.66,0,28.4-12.74,28.4-28.4s-12.74-28.42-28.4-28.42ZM101.92,109.25c-6.58,0-12.5-2.84-16.61-7.37-1.76-1.94-3.2-4.19-4.21-6.65-1.07-2.61-1.66-5.47-1.66-8.45,0-9.85,6.36-18.25,15.21-21.29,2.19-.75,4.54-1.16,6.97-1.2h.3c12.4,0,22.47,10.09,22.47,22.49s-10.07,22.47-22.47,22.47Z"
          />
          <polygon
            fill="#0967B1"
            className="cls-1"
            points="119.14 81.54 113.1 85.49 106.82 89.58 98.17 95.23 93.29 98.41 90.69 95.23 84.01 87.05 88.61 83.3 94.52 90.53 105.12 83.62 110.67 80 115.9 76.59 119.14 81.54"
          />
        </g>
      </g>
    </g>
  </svg>
);

export const IconCloseSvgBlue = () => (
  <svg
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="18.9614"
      y="18.0386"
      width="1.30543"
      height="16.9706"
      rx="0.652715"
      transform="rotate(135 18.9614 18.0386)"
      fill="#0967B1"
    />
    <rect
      x="6.96143"
      y="18.9614"
      width="1.30543"
      height="16.9706"
      rx="0.652715"
      transform="rotate(-135 6.96143 18.9614)"
      fill="#0967B1"
    />
  </svg>
);
export const IconCloseSvgWhite = () => (
  <svg
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="18.9614"
      y="18.0386"
      width="1.30543"
      height="16.9706"
      rx="0.652715"
      transform="rotate(135 18.9614 18.0386)"
      fill="#fff"
    />
    <rect
      x="6.96143"
      y="18.9614"
      width="1.30543"
      height="16.9706"
      rx="0.652715"
      transform="rotate(-135 6.96143 18.9614)"
      fill="#fff"
    />
  </svg>
);
export const IconOpenNewWindow = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 3C3.90694 3 3 3.90694 3 5V19C3 20.0931 3.90694 21 5 21H19C20.0931 21 21 20.0931 21 19V12H19V19H5V5H12V3H5ZM14 3V5H17.5859L8.29297 14.293L9.70703 15.707L19 6.41406V10H21V3H14Z"
      fill="#1e1e1e"
    />
  </svg>
);

export const IconAudit = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_264_2)">
      <mask
        id="mask0_264_2"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="22"
        height="22"
      >
        <path d="M22 0H0V22H22V0Z" fill="white" />
      </mask>
      <g mask="url(#mask0_264_2)">
        <path
          d="M21.8662 21.2172L18.865 18.216C19.6772 17.2572 20.1676 16.0178 20.1676 14.6667C20.1676 11.6334 17.7008 9.16667 14.6676 9.16667C11.6343 9.16667 9.16758 11.6334 9.16758 14.6667C9.16758 17.6999 11.6343 20.1667 14.6676 20.1667C16.0197 20.1667 17.259 19.6763 18.2169 18.8641L21.2181 21.8653C21.3079 21.9551 21.4252 21.9991 21.5426 21.9991C21.6599 21.9991 21.7772 21.9542 21.8671 21.8653C22.0458 21.6865 22.0449 21.3959 21.8662 21.2172ZM14.6667 19.25C12.1394 19.25 10.0833 17.1939 10.0833 14.6667C10.0833 12.1394 12.1394 10.0833 14.6667 10.0833C17.1939 10.0833 19.25 12.1394 19.25 14.6667C19.25 17.1939 17.1939 19.25 14.6667 19.25ZM10.5417 21.0833H4.125C2.35583 21.0833 0.916667 19.6442 0.916667 17.875V4.125C0.916667 2.35583 2.35583 0.916667 4.125 0.916667H9.18042C9.48658 0.916667 9.78908 0.944167 10.0833 0.996417V5.95833C10.0833 7.2215 11.1118 8.25 12.375 8.25H17.7036C17.8447 8.25 17.9786 8.18492 18.0657 8.07308C18.1527 7.96125 18.1821 7.8155 18.1473 7.678C17.8823 6.64217 17.3433 5.69525 16.5871 4.93992L13.3925 1.74533C12.2668 0.619667 10.7708 0 9.1795 0H4.12408C1.85075 0 0 1.85075 0 4.125V17.875C0 20.1493 1.85075 22 4.125 22H10.5417C10.7947 22 11 21.7947 11 21.5417C11 21.2887 10.7947 21.0833 10.5417 21.0833ZM11 1.254C11.6472 1.50333 12.2402 1.88833 12.7453 2.39342L15.9399 5.588C16.4367 6.08483 16.8236 6.68067 17.0766 7.33333H12.375C11.6169 7.33333 11 6.71642 11 5.95833V1.254ZM8.70833 10.0833H4.125C3.872 10.0833 3.66667 9.878 3.66667 9.625C3.66667 9.372 3.872 9.16667 4.125 9.16667H8.70833C8.96133 9.16667 9.16667 9.372 9.16667 9.625C9.16667 9.878 8.96133 10.0833 8.70833 10.0833ZM3.66667 13.2917C3.66667 13.0387 3.872 12.8333 4.125 12.8333H6.875C7.128 12.8333 7.33333 13.0387 7.33333 13.2917C7.33333 13.5447 7.128 13.75 6.875 13.75H4.125C3.872 13.75 3.66667 13.5447 3.66667 13.2917ZM3.66667 16.9583C3.66667 16.7053 3.872 16.5 4.125 16.5H7.33333C7.58633 16.5 7.79167 16.7053 7.79167 16.9583C7.79167 17.2113 7.58633 17.4167 7.33333 17.4167H4.125C3.872 17.4167 3.66667 17.2113 3.66667 16.9583ZM17.7375 13.4227C17.9135 13.6052 17.9089 13.8948 17.7265 14.0708L15.2487 16.467C14.9187 16.7924 14.4833 16.9565 14.047 16.9565C13.6107 16.9565 13.1771 16.7942 12.8443 16.4688L11.594 15.2359C11.4134 15.0581 11.4116 14.7684 11.5894 14.5878C11.7672 14.4072 12.0587 14.4063 12.2375 14.5833L13.4869 15.8153C13.7958 16.1178 14.2982 16.1168 14.608 15.8116L17.0885 13.4127C17.2709 13.2367 17.5615 13.2422 17.7366 13.4237L17.7375 13.4227Z"
          style={{ fill: "var(--dark-blue)" }}
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_264_2">
        <rect width="22" height="22" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconMenus = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask x="0" y="0" width="24" height="24">
      <rect width="24" height="24" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_276_11)">
      <circle cx="12.5" cy="5.5" r="1" style={{ stroke: "var(--dark-blue)" }} />
      <circle cx="12.5" cy="9.5" r="1" style={{ stroke: "var(--dark-blue)" }} />
      <circle
        cx="12.5"
        cy="13.5"
        r="1"
        style={{ stroke: "var(--dark-blue)" }}
      />
      <circle
        cx="12.5"
        cy="17.5"
        r="1"
        style={{ stroke: "var(--dark-blue)" }}
      />
    </g>
  </svg>
);

export const IconExportIconBlue = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.55221 17.9681C2.20705 17.9584 1.86737 17.8794 1.55332 17.7358C1.23928 17.5923 0.957288 17.3871 0.724088 17.1325C0.490888 16.8778 0.311248 16.5789 0.195828 16.2535C0.0804083 15.928 0.0315683 15.5827 0.0522083 15.2381C0.0402083 13.0851 0.0522083 10.9321 0.0522083 8.77905C0.0522083 8.64635 0.104888 8.51925 0.198648 8.42545C0.292418 8.33165 0.419598 8.27905 0.552208 8.27905C0.684818 8.27905 0.811988 8.33165 0.905758 8.42545C0.999528 8.51925 1.05221 8.64635 1.05221 8.77905C1.05221 10.9791 1.02021 13.1791 1.05221 15.3791C1.06821 16.4861 1.90021 16.9681 2.89021 16.9681H15.3532C15.668 16.9758 15.9776 16.8872 16.2406 16.7142C16.5037 16.5413 16.7076 16.2921 16.8252 16.0001C16.9211 15.6551 16.955 15.2959 16.9252 14.9391V8.77905C16.9252 8.64635 16.9779 8.51925 17.0717 8.42545C17.1654 8.33165 17.2926 8.27905 17.4252 8.27905C17.5578 8.27905 17.685 8.33165 17.7788 8.42545C17.8726 8.51925 17.9252 8.64635 17.9252 8.77905C17.9252 11.0031 18.0102 13.2441 17.9252 15.4661C17.9166 15.8063 17.8404 16.1415 17.7011 16.452C17.5617 16.7626 17.362 17.0423 17.1136 17.275C16.8651 17.5076 16.5728 17.6885 16.2538 17.8072C15.9348 17.9258 15.5953 17.9798 15.2552 17.9661L2.55221 17.9681Z"
      style={{ fill: "var(--dark-blue)" }}
    />
    <path
      d="M9.3372 0.176026C9.2552 0.0915361 9.1438 0.0420761 9.0262 0.0380261C9.0112 0.0380261 8.9982 0.0380263 8.9832 0.0320263C8.9682 0.0260263 8.9562 0.0320261 8.9422 0.0380261C8.8242 0.0420261 8.7124 0.0914761 8.6302 0.176026L4.96116 3.84503C4.87008 3.93933 4.81968 4.06563 4.82082 4.19673C4.82196 4.32783 4.87454 4.45323 4.96725 4.54594C5.05995 4.63864 5.18536 4.69123 5.31646 4.69237C5.44756 4.69351 5.57386 4.64311 5.66816 4.55203L8.4842 1.73703V12.479C8.4842 12.6116 8.5368 12.7388 8.6306 12.8326C8.7244 12.9264 8.8516 12.979 8.9842 12.979C9.1168 12.979 9.2439 12.9264 9.3377 12.8326C9.4315 12.7388 9.4842 12.6116 9.4842 12.479V1.73703L12.3002 4.55203C12.3945 4.64311 12.5208 4.69351 12.6519 4.69237C12.783 4.69123 12.9084 4.63864 13.0011 4.54594C13.0938 4.45323 13.1464 4.32783 13.1475 4.19673C13.1486 4.06563 13.0982 3.93933 13.0072 3.84503L9.3372 0.176026Z"
      style={{ fill: "var(--dark-blue)" }}
    />
  </svg>
);
export const IconDeleteBlueSvg = () => (
  <svg
    width="20"
    height="24"
    viewBox="0 0 20 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask x="0" y="0" width="20" height="24">
      <path d="M20 0H0V24H20V0Z" style={{ fill: "var(--dark-blue)" }} />
    </mask>
    <g mask="url(#mask0_302_24)">
      <path d="M1 6.60059H19" style={{ stroke: "var(--dark-blue)" }} />
      <path
        d="M17 6.6V19.2C17 19.6774 16.7893 20.1352 16.4142 20.4728C16.0391 20.8104 15.5304 21 15 21H5C4.46957 21 3.96086 20.8104 3.58579 20.4728C3.21071 20.1352 3 19.6774 3 19.2V6.6M6 6.6V4.8C6 4.32261 6.21071 3.86477 6.58579 3.52721C6.96086 3.18964 7.46957 3 8 3H12C12.5304 3 13.0391 3.18964 13.4142 3.52721C13.7893 3.86477 14 4.32261 14 4.8V6.6"
        style={{ stroke: "var(--dark-blue)" }}
      />
      <path d="M8 11.1006V16.5006" style={{ stroke: "var(--dark-blue)" }} />
      <path d="M12 11.1006V16.5006" style={{ stroke: "var(--dark-blue)" }} />
    </g>
  </svg>
);

export const IconSvgUploadOrange = () => (
  <svg
    width="69"
    height="61"
    viewBox="0 0 69 61"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M36.028 15.5896L36.1203 15.6171L36.1243 15.6125C36.5619 15.6918 36.9961 15.4298 37.1247 14.9957C38.2963 11.0589 41.9874 8.30879 46.0998 8.30879C46.5867 8.30879 46.9816 7.91391 46.9816 7.42705C46.9816 6.94018 46.5867 6.54531 46.0998 6.54531C41.0457 6.54531 36.7985 9.9104 35.4348 14.4931C35.2956 14.9599 35.5615 15.4504 36.028 15.5896Z"
      style={{ fill: "var(--dark-blue)", stroke: "var(--dark-blue)" }}
      strokeWidth="0.3"
    />
    <path
      d="M56.3438 43.2822H51.9534C51.5494 43.2822 51.2217 42.9545 51.2217 42.5504C51.2217 42.1464 51.5494 41.8187 51.9534 41.8187H56.3438C62.3956 41.8187 67.3197 36.8946 67.3197 30.8427C67.3197 24.7909 62.3956 19.8668 56.3438 19.8668H56.2382C56.026 19.8668 55.8242 19.7748 55.6852 19.6143C55.5462 19.4539 55.4834 19.2412 55.5138 19.031C55.5791 18.5753 55.612 18.1174 55.612 17.6716C55.612 12.4267 51.3444 8.15906 46.0995 8.15906C44.059 8.15906 42.1131 8.79671 40.4719 10.0035C40.1112 10.2685 39.599 10.1509 39.3905 9.75422C34.7425 0.903449 22.6023 -0.285119 16.3082 7.41428C13.6568 10.6579 12.615 14.8774 13.4498 18.9898C13.5418 19.444 13.1942 19.8674 12.7327 19.8674H12.4395C6.3876 19.8674 1.46353 24.7914 1.46353 30.8433C1.46353 36.8952 6.3876 41.8193 12.4395 41.8193H16.8298C17.2338 41.8193 17.5615 42.147 17.5615 42.551C17.5615 42.955 17.2338 43.2827 16.8298 43.2827H12.4395C5.5805 43.2827 0 37.7022 0 30.8433C0 24.1767 5.27155 18.7179 11.8651 18.4169C11.2457 14.1503 12.4301 9.8467 15.1751 6.48812C21.9138 -1.75585 34.828 -0.831809 40.2871 8.36082C42.0287 7.26897 44.0215 6.69619 46.0992 6.69619C52.4538 6.69619 57.4892 12.1047 57.0486 18.4237C63.5813 18.7901 68.7829 24.22 68.7829 30.8427C68.7829 37.7022 63.2024 43.2822 56.3434 43.2822L56.3438 43.2822Z"
      style={{ fill: "var(--dark-blue)" }}
    />
    <path
      d="M15.85 42.1372C15.85 52.3072 24.1237 60.5807 34.2935 60.5807C44.4634 60.5807 52.737 52.307 52.737 42.1372C52.737 31.9673 44.4634 23.6937 34.2935 23.6937C24.1235 23.6937 15.85 31.9674 15.85 42.1372ZM17.6138 42.1372C17.6138 32.9403 25.0964 25.4575 34.2935 25.4575C43.4904 25.4575 50.9732 32.9402 50.9732 42.1372C50.9732 51.3341 43.4904 58.8169 34.2935 58.8169C25.0966 58.8169 17.6138 51.3343 17.6138 42.1372Z"
      style={{ fill: "var(--dark-blue)", stroke: "var(--dark-blue)" }}
      strokeWidth="0.3"
    />
    <path
      d="M34.628 50.0391C34.3322 50.0391 34.0923 49.7991 34.0923 49.5034V35.5748C34.0923 35.279 34.3322 35.0391 34.628 35.0391C34.9238 35.0391 35.1637 35.279 35.1637 35.5748V49.5034C35.1637 49.7995 34.9238 50.0391 34.628 50.0391Z"
      style={{ fill: "var(--dark-blue)" }}
    />
    <path
      d="M38.9138 40.3962C38.7768 40.3962 38.6394 40.3438 38.5349 40.2393L34.6281 36.3325L30.7213 40.2393C30.5123 40.4486 30.1729 40.4486 29.9636 40.2393C29.7543 40.03 29.7543 39.691 29.9636 39.4817L34.2493 35.196C34.4582 34.9867 34.7976 34.9867 35.0069 35.196L39.2925 39.4817C39.5019 39.691 39.5019 40.03 39.2925 40.2393C39.1881 40.3442 39.0507 40.3962 38.9137 40.3962H38.9138Z"
      style={{ fill: "var(--dark-blue)" }}
    />
  </svg>
);

export const IconCheckCircleIconSvg = () => (
  <svg
    width="24"
    height="23"
    viewBox="0 0 24 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.7726 0.0859375C11.8646 0.0859375 11.9559 0.0859375 12.0479 0.0859375C12.1742 0.105529 12.2998 0.131057 12.4268 0.143524C12.8964 0.191018 13.3715 0.204079 13.835 0.283631C17.1849 0.855339 19.8088 2.52475 21.6971 5.25625C22.9006 6.9969 23.5333 8.92871 23.6019 11.0279C23.605 11.1289 23.6387 11.2286 23.6584 11.3289V11.5955C23.6375 11.7326 23.6136 11.8698 23.5964 12.0075C23.5278 12.5715 23.5039 13.1444 23.3868 13.6989C22.8816 16.0914 21.7088 18.1354 19.845 19.7823C17.1934 22.1255 14.0557 23.092 10.4968 22.7168C8.51654 22.5078 6.70183 21.8192 5.09617 20.6692C1.90265 18.3824 0.284119 15.2917 0.194609 11.4584C0.173765 10.5732 0.300672 9.68981 0.517089 8.82304C1.75551 3.85696 6.30394 0.295504 11.4391 0.141149C11.5507 0.137587 11.6616 0.104935 11.7726 0.0859375ZM10.2142 13.7642C9.79849 13.3617 9.42696 13.0007 9.05421 12.6404C8.46994 12.0752 7.88568 11.51 7.30142 10.9442C7.07948 10.7293 6.8085 10.6789 6.51852 10.7566C6.21872 10.8368 6.04338 11.0469 5.9882 11.3402C5.93364 11.6282 6.05932 11.8543 6.26715 12.0544C7.35046 13.0993 8.4442 14.1358 9.50605 15.2009C9.92294 15.6188 10.4496 15.6153 10.8744 15.1991C13.0674 13.05 15.2806 10.9205 17.4865 8.78386C17.5515 8.72093 17.6208 8.66097 17.6753 8.59032C17.8654 8.34395 17.9052 8.07501 17.7593 7.79836C17.6201 7.53358 17.3958 7.39822 17.0776 7.39288C16.806 7.38813 16.6178 7.5128 16.4387 7.68615C14.418 9.64587 12.3949 11.6032 10.3742 13.5629C10.3208 13.6146 10.281 13.6799 10.2148 13.7642H10.2142Z"
      fill="#B3B3B3"
    />
  </svg>
);
export const IconCheckCircleIconGreenSvg = ({
  width = 24,
  height = 23,
}: any) => (
  <svg
    width={width}
    height={height}
    viewBox={`0 0 24 23`}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.7726 0.0859375C11.8646 0.0859375 11.9559 0.0859375 12.0479 0.0859375C12.1742 0.105529 12.2998 0.131057 12.4268 0.143524C12.8964 0.191018 13.3715 0.204079 13.835 0.283631C17.1849 0.855339 19.8088 2.52475 21.6971 5.25625C22.9006 6.9969 23.5333 8.92871 23.6019 11.0279C23.605 11.1289 23.6387 11.2286 23.6584 11.3289V11.5955C23.6375 11.7326 23.6136 11.8698 23.5964 12.0075C23.5278 12.5715 23.5039 13.1444 23.3868 13.6989C22.8816 16.0914 21.7088 18.1354 19.845 19.7823C17.1934 22.1255 14.0557 23.092 10.4968 22.7168C8.51654 22.5078 6.70183 21.8192 5.09617 20.6692C1.90265 18.3824 0.284119 15.2917 0.194609 11.4584C0.173765 10.5732 0.300672 9.68981 0.517089 8.82304C1.75551 3.85696 6.30394 0.295504 11.4391 0.141149C11.5507 0.137587 11.6616 0.104935 11.7726 0.0859375ZM10.2142 13.7642C9.79849 13.3617 9.42696 13.0007 9.05421 12.6404C8.46994 12.0752 7.88568 11.51 7.30142 10.9442C7.07948 10.7293 6.8085 10.6789 6.51852 10.7566C6.21872 10.8368 6.04338 11.0469 5.9882 11.3402C5.93364 11.6282 6.05932 11.8543 6.26715 12.0544C7.35046 13.0993 8.4442 14.1358 9.50605 15.2009C9.92294 15.6188 10.4496 15.6153 10.8744 15.1991C13.0674 13.05 15.2806 10.9205 17.4865 8.78386C17.5515 8.72093 17.6208 8.66097 17.6753 8.59032C17.8654 8.34395 17.9052 8.07501 17.7593 7.79836C17.6201 7.53358 17.3958 7.39822 17.0776 7.39288C16.806 7.38813 16.6178 7.5128 16.4387 7.68615C14.418 9.64587 12.3949 11.6032 10.3742 13.5629C10.3208 13.6146 10.281 13.6799 10.2148 13.7642H10.2142Z"
      fill="#74b856"
    />
  </svg>
);
export const IconCrossCircleIconSvg = ({ width = 24, height = 24 }: any) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 24C8.8175 24 5.76497 22.7356 3.51473 20.4853C1.26435 18.2347 0 15.1825 0 12C0 8.8175 1.26435 5.76497 3.51473 3.51473C5.7653 1.26435 8.8175 0 12 0C15.1825 0 18.235 1.26435 20.4853 3.51473C22.7356 5.7653 24 8.8175 24 12C23.9962 15.1814 22.7307 18.2318 20.4811 20.4811C18.2314 22.7307 15.1811 23.9962 12 24ZM16.6276 8.91362C16.838 8.71035 16.958 8.43095 16.9606 8.13836C16.9631 7.84579 16.8481 7.56437 16.6412 7.35744C16.4342 7.15051 16.1528 7.03547 15.8602 7.03802C15.5677 7.04058 15.2884 7.16057 15.0852 7.37096L12 10.4572L8.91484 7.37096C8.63779 7.1034 8.24019 7.00168 7.86855 7.10359C7.49708 7.20549 7.2069 7.49569 7.10497 7.86716C7.00307 8.23881 7.10479 8.6364 7.37235 8.91346L10.4575 11.9997L7.37235 15.086C7.16196 15.2893 7.04197 15.5685 7.03941 15.8611C7.03685 16.1537 7.15191 16.4351 7.35883 16.642C7.56575 16.849 7.84717 16.964 8.13975 16.9614C8.43233 16.9589 8.71155 16.8389 8.91483 16.6285L12 13.5422L15.0852 16.6285C15.2884 16.8389 15.5676 16.9589 15.8602 16.9614C16.1528 16.964 16.4342 16.8489 16.6411 16.642C16.8481 16.4351 16.9631 16.1537 16.9606 15.8611C16.958 15.5685 16.838 15.2893 16.6276 15.086L13.5425 11.9997L16.6276 8.91362Z"
      fill="#E84336"
    />
  </svg>
);

export const IconInProgressSvg = ({ width = 24, height = 24 }: any) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 24C8.8175 24 5.76497 22.7356 3.51473 20.4853C1.26435 18.2347 0 15.1825 0 12C0 8.8175 1.26435 5.76497 3.51473 3.51473C5.7653 1.26435 8.8175 0 12 0C15.1825 0 18.235 1.26435 20.4853 3.51473C22.7356 5.7653 24 8.8175 24 12C23.9962 15.1814 22.7307 18.2318 20.4811 20.4811C18.2314 22.7307 15.1811 23.9962 12 24Z"
      fill="#F9A825"
    />
    <path
      d="M12 6C11.4477 6 11 6.44772 11 7V12C11 12.5523 11.4477 13 12 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H13V7C13 6.44772 12.5523 6 12 6Z"
      fill="white"
    />
  </svg>
);

export const IconInQueueSvg = ({ width = 24, height = 24 }: any) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 24C8.8175 24 5.76497 22.7356 3.51473 20.4853C1.26435 18.2347 0 15.1825 0 12C0 8.8175 1.26435 5.76497 3.51473 3.51473C5.7653 1.26435 8.8175 0 12 0C15.1825 0 18.235 1.26435 20.4853 3.51473C22.7356 5.7653 24 8.8175 24 12C23.9962 15.1814 22.7307 18.2318 20.4811 20.4811C18.2314 22.7307 15.1811 23.9962 12 24Z"
      fill="#9E9E9E"
    />
    <path
      d="M7 12C7 11.4477 7.44772 11 8 11H16C16.5523 11 17 11.4477 17 12C17 12.5523 16.5523 13 16 13H8C7.44772 13 7 12.5523 7 12Z"
      fill="white"
    />
    <path
      d="M7 8C7 7.44772 7.44772 7 8 7H16C16.5523 7 17 7.44772 17 8C17 8.55228 16.5523 9 16 9H8C7.44772 9 7 8.55228 7 8Z"
      fill="white"
    />
    <path
      d="M7 16C7 15.4477 7.44772 15 8 15H16C16.5523 15 17 15.4477 17 16C17 16.5523 16.5523 17 16 17H8C7.44772 17 7 16.5523 7 16Z"
      fill="white"
    />
  </svg>
);

export const IconRefreshAPISvg = () => (
  <svg
    width="23"
    height="24"
    viewBox="0 0 23 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="11.5303" cy="11.9141" r="11.1602" fill="#F48400" />
    <g clipPath="url(#clip0_2800_7214)">
      <path
        d="M15.5926 8.16356C15.5926 7.73008 15.5867 7.29632 15.5946 6.86284C15.6011 6.50886 15.9836 6.30434 16.2843 6.48667C16.4181 6.56786 16.4993 6.6895 16.5002 6.84598C16.5038 7.72053 16.5089 8.59536 16.4988 9.46991C16.4957 9.73483 16.302 9.89862 16.0331 9.89946C15.184 9.90255 14.3346 9.90255 13.4856 9.89946C13.2298 9.89862 13.0508 9.73315 13.0204 9.48789C12.9948 9.27972 13.1359 9.06677 13.3456 9.00974C13.413 8.99148 13.4861 8.98839 13.5564 8.98811C14.0052 8.98614 14.4541 8.98727 14.9032 8.98727C14.9431 8.98727 14.9833 8.98727 15.0468 8.98727C15.0114 8.94091 14.9917 8.91141 14.9684 8.88529C14.2163 8.04529 13.2843 7.53624 12.1654 7.38116C11.0131 7.22159 9.94312 7.46376 8.98443 8.11496C7.86077 8.87826 7.18342 9.94329 6.99539 11.294C6.81214 12.6113 7.14688 13.8061 7.99961 14.8189C9.07887 16.1008 10.4799 16.6655 12.1508 16.4455C13.5718 16.2584 14.6637 15.5198 15.4291 14.311C15.8788 13.6005 16.1025 12.8184 16.1129 11.9761C16.1162 11.6915 16.2677 11.4957 16.5021 11.4657C16.8009 11.4272 17.0322 11.6348 17.0305 11.9452C17.0263 12.6751 16.8937 13.3819 16.5977 14.0489C15.8714 15.6856 14.6474 16.7683 12.9133 17.2307C9.97853 18.0134 6.95071 16.216 6.20085 13.2687C5.4538 10.3355 7.22979 7.31795 10.175 6.59679C12.2031 6.1001 13.9822 6.62236 15.4774 8.08575C15.5083 8.11609 15.5272 8.15851 15.5516 8.19531C15.5654 8.18464 15.5789 8.17424 15.5926 8.16356Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_2800_7214">
        <rect
          width="11"
          height="11"
          fill="white"
          transform="translate(6.03027 6.41406)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const IconReplaceOrange = () => (
  <svg
    width="36"
    height="36"
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.77148 0.710449H31.7715C33.4283 0.710449 34.7715 2.0536 34.7715 3.71045V31.7104C34.7715 33.3673 33.4283 34.7104 31.7715 34.7104H3.77148C2.11463 34.7104 0.771484 33.3673 0.771484 31.7104V3.71045C0.771484 2.05359 2.11463 0.710449 3.77148 0.710449Z"
      style={{ stroke: "var(--orange)" }}
    />
    <mask maskUnits="userSpaceOnUse" x="7" y="7" width="22" height="22">
      <path d="M29 29H7V7H29V29Z" fill="white" />
    </mask>
    <g mask="url(#mask0_307_31)">
      <path
        d="M17.0833 11.1342C17.0833 10.6758 16.9092 10.2175 16.5517 9.8692L14.1958 7.55917C14.1042 7.47667 13.9942 7.43083 13.875 7.43083C13.7558 7.43083 13.6367 7.47667 13.545 7.56833C13.3708 7.75167 13.3708 8.03583 13.545 8.21917L15.9008 10.52C15.9558 10.575 15.9925 10.63 16.0292 10.685H11.125C9.3558 10.685 7.91667 12.1242 7.91667 13.8933V16.6433C7.91667 16.9 8.11833 17.1017 8.375 17.1017C8.63167 17.1017 8.83333 16.9 8.83333 16.6433V13.8933C8.83333 12.6283 9.86 11.6017 11.125 11.6017H16.0383C16.0017 11.6658 15.9558 11.7208 15.9008 11.7758L13.545 14.0767C13.3617 14.2508 13.3617 14.5442 13.545 14.7275C13.7283 14.9108 14.0125 14.9108 14.1958 14.7275L16.5517 12.4267C16.9 12.0783 17.0833 11.6108 17.0833 11.1525V11.1342ZM19.4483 23.5917L21.8042 21.2908C21.8958 21.2083 22.0058 21.1625 22.125 21.1625C22.2442 21.1625 22.3633 21.2083 22.455 21.3C22.6292 21.4833 22.6292 21.7675 22.455 21.9508L20.0992 24.2517C20.0442 24.3067 19.9983 24.3617 19.9617 24.4258H24.875C26.14 24.4258 27.1667 23.3992 27.1667 22.1342V19.3842C27.1667 19.1275 27.3683 18.9258 27.625 18.9258C27.8817 18.9258 28.0833 19.1275 28.0833 19.3842V22.1342C28.0833 23.9033 26.6442 25.3425 24.875 25.3425H19.9617C19.9983 25.4067 20.0442 25.4617 20.09 25.5075L22.4458 27.8083C22.6292 27.9825 22.6292 28.2758 22.4458 28.4592C22.2717 28.6425 21.9783 28.6425 21.795 28.4592L19.4392 26.1492C19.0908 25.8008 18.9167 25.3425 18.9075 24.8842C18.9075 24.4258 19.0908 23.9675 19.4392 23.61L19.4483 23.5917ZM29 13.875V10.2083C29 8.43917 27.5608 7 25.7917 7H22.125C20.3558 7 18.9167 8.43917 18.9167 10.2083V13.875C18.9167 15.6442 20.3558 17.0833 22.125 17.0833H25.7917C27.5608 17.0833 29 15.6442 29 13.875ZM28.0833 13.875C28.0833 15.14 27.0567 16.1667 25.7917 16.1667H22.125C20.86 16.1667 19.8333 15.14 19.8333 13.875V10.2083C19.8333 8.94333 20.86 7.91667 22.125 7.91667H25.7917C27.0567 7.91667 28.0833 8.94333 28.0833 10.2083V13.875ZM17.0833 25.7917V22.125C17.0833 20.3558 15.6442 18.9167 13.875 18.9167H10.2083C8.43917 18.9167 7 20.3558 7 22.125V25.7917C7 27.5608 8.43917 29 10.2083 29H13.875C15.6442 29 17.0833 27.5608 17.0833 25.7917ZM16.1667 25.7917C16.1667 27.0567 15.14 28.0833 13.875 28.0833H10.2083C8.94333 28.0833 7.91667 27.0567 7.91667 25.7917V22.125C7.91667 20.86 8.94333 19.8333 10.2083 19.8333H13.875C15.14 19.8333 16.1667 20.86 16.1667 22.125V25.7917Z"
        style={{ fill: "var(--orange)" }}
      />
    </g>
  </svg>
);

export const IconDiscard = () => (
  <svg
    width="36"
    height="36"
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.25098 0.710449H32.251C33.9078 0.710449 35.251 2.0536 35.251 3.71045V31.7104C35.251 33.3673 33.9078 34.7104 32.251 34.7104H4.25098C2.59412 34.7104 1.25098 33.3673 1.25098 31.7104V3.71045C1.25098 2.05359 2.59412 0.710449 4.25098 0.710449Z"
      stroke="#142A41"
    />
    <path
      d="M10.4727 9.93213L26.029 25.4885"
      stroke="#142A41"
      strokeWidth="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M26.0291 9.93213L10.4727 25.4885"
      stroke="#142A41"
      strokeWidth="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const IconImportGray = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask id="mask0_295_287" x="0" y="0" width="24" height="24">
      <rect width="24" height="24" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_295_287)">
      <path
        d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
        stroke="#142A41"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M17 10L12 15L7 10"
        stroke="#142A41"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12 15V3"
        stroke="#142A41"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
  </svg>
);

export const IconAddOrange = () => (
  <svg
    width="19"
    height="19"
    viewBox="0 0 19 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_2912_2931)">
      <path
        d="M15.6512 4.52407L15.6516 4.52454C16.1492 5.01908 16.5437 5.60744 16.8123 6.25553C17.0809 6.90362 17.2182 7.59856 17.2163 8.3001V8.30064V15.0414C17.2151 16.0379 16.8188 16.9932 16.1142 17.6978C15.4096 18.4025 14.4542 18.7988 13.4578 18.8H5.54158C4.54511 18.7988 3.5898 18.4025 2.88519 17.6978L2.74377 17.8393L2.88519 17.6978C2.1806 16.9933 1.78423 16.038 1.78301 15.0416V3.95847C1.78423 2.96204 2.1806 2.00678 2.88519 1.3022C3.58977 0.597615 4.54503 0.201244 5.54146 0.200018L9.11572 0.200019L9.11623 0.200018C9.81783 0.198212 10.5128 0.335503 11.161 0.603954C11.8092 0.872405 12.3978 1.26669 12.8927 1.76401L12.893 1.76432L15.6512 4.52407ZM14.8166 5.3627L14.8166 5.36269L14.8147 5.36072L12.0565 2.60097L12.0565 2.60095L12.0542 2.59869C11.7925 2.34519 11.4986 2.12712 11.1802 1.95006L10.883 1.78481V2.12485V5.54168C10.883 5.80469 10.9875 6.05693 11.1735 6.2429C11.3594 6.42887 11.6117 6.53335 11.8747 6.53335H15.2915H15.6317L15.4663 6.23609C15.2891 5.91767 15.0706 5.62401 14.8166 5.3627ZM9.69968 1.62056V1.43822L9.51811 1.42141C9.47728 1.41763 9.43885 1.41263 9.39656 1.40713C9.37636 1.40451 9.35528 1.40177 9.33264 1.39899C9.26654 1.39088 9.193 1.38335 9.11572 1.38335H5.54134C4.85841 1.38335 4.20345 1.65465 3.72054 2.13755C3.23764 2.62046 2.96634 3.27542 2.96634 3.95835V15.0417C2.96634 15.7246 3.23764 16.3796 3.72054 16.8625C4.20345 17.3454 4.85841 17.6167 5.54134 17.6167H13.458C14.1409 17.6167 14.7959 17.3454 15.2788 16.8625C15.7617 16.3796 16.033 15.7246 16.033 15.0417V8.30064C16.033 8.22299 16.0255 8.14947 16.0174 8.08338C16.0146 8.06073 16.0118 8.03966 16.0092 8.01949C16.0037 7.97732 15.9987 7.93904 15.9949 7.89824L15.9781 7.71668H15.7958H11.8747C11.2978 7.71668 10.7446 7.48753 10.3367 7.07964C9.92883 6.67175 9.69968 6.11853 9.69968 5.54168V1.62056Z"
        style={{ fill: "var(--orange)" }}
        stroke="white"
        strokeWidth="0.4"
      />
      <path
        d="M8.68848 9V14.376"
        style={{ stroke: "var(--orange)" }}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6 11.6875H11.376"
        style={{ stroke: "var(--orange)" }}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_2912_2931">
        <rect width="19" height="19" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconImportFileWhite = () => (
  <svg
    width="16"
    height="24"
    viewBox="0 0 16 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g mask="url(#mask0_296_14)">
      <path
        d="M14.0139 18.5139H14.5139V18.0139V12.3828H15.2656V18.0139C15.2656 18.7012 14.7012 19.2656 14.0139 19.2656H1.75174C1.06442 19.2656 0.5 18.7012 0.5 18.0139V12.3828H1.25174V18.0139V18.5139H1.75174H14.0139ZM8.25868 12.4696V13.6735L9.11155 12.8239L11.0265 10.9163L11.555 11.4449L7.88281 15.1171L4.21058 11.4449L4.73913 10.9163L6.65408 12.8239L7.50694 13.6735V12.4696V4.5H8.25868V12.4696Z"
        fill="#F48400"
        stroke="white"
      />
    </g>
  </svg>
);

export const IconResearchQuerySvg = () => (
  <svg
    width="16"
    height="20"
    viewBox="0 0 16 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.4533 16.4228L11.2395 14.4395C11.8423 13.7779 12.1386 12.9336 12.0673 12.0814C11.9959 11.2292 11.5622 10.4342 10.856 9.86085C10.1498 9.2875 9.22499 8.97967 8.27294 9.00104C7.3209 9.02241 6.41442 9.37134 5.74101 9.97565C5.0676 10.58 4.67878 11.3934 4.65497 12.2478C4.63116 13.1021 4.97418 13.932 5.61308 14.5658C6.25199 15.1996 7.13789 15.5887 8.08756 15.6528C9.03722 15.7168 9.978 15.4509 10.7153 14.9099L12.9278 16.8943C12.9618 16.9269 13.0027 16.9531 13.0482 16.9713C13.0937 16.9894 13.1428 16.9992 13.1925 17C13.2423 17.0007 13.2918 16.9925 13.3379 16.9757C13.384 16.959 13.426 16.9341 13.4612 16.9025C13.4964 16.8709 13.5241 16.8333 13.5428 16.7919C13.5614 16.7505 13.5706 16.7061 13.5697 16.6615C13.5689 16.6168 13.558 16.5727 13.5378 16.5319C13.5176 16.4911 13.4884 16.4544 13.452 16.4239L13.4533 16.4228ZM8.37361 14.9956C7.78729 14.9956 7.21414 14.8395 6.72664 14.5472C6.23914 14.2549 5.85917 13.8394 5.6348 13.3533C5.41043 12.8672 5.35172 12.3323 5.4661 11.8163C5.58049 11.3002 5.86283 10.8262 6.27741 10.4542C6.692 10.0821 7.22022 9.82874 7.79527 9.72609C8.37032 9.62345 8.96637 9.67613 9.50806 9.87748C10.0497 10.0788 10.5127 10.4198 10.8385 10.8573C11.1642 11.2948 11.3381 11.8091 11.3381 12.3353C11.3381 13.0408 11.0257 13.7175 10.4698 14.2164C9.91386 14.7153 9.15983 14.9956 8.37361 14.9956Z"
      style={{ fill: "var(--dark-blue)" }}
    />
    <path
      d="M15.3855 4.00041V16.0021C15.3855 17.6598 12.0518 19.0025 7.94275 19.0025C3.83373 19.0025 0.5 17.6598 0.5 16.0021V4.00041C0.5 2.34268 3.83373 1 7.94275 1C12.0518 1 15.3855 2.34268 15.3855 4.00041Z"
      style={{ stroke: "var(--dark-blue)" }}
      strokeWidth="0.75"
    />
    <path
      d="M15.3855 4.00195C15.3855 5.65968 12.0518 7.00237 7.94275 7.00237C3.83373 7.00237 0.5 5.65968 0.5 4.00195"
      style={{ stroke: "var(--dark-blue)" }}
      strokeWidth="0.75"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.2712 13.1941C11.3074 12.9543 11.3263 12.7072 11.3263 12.4548C11.3263 12.357 11.3235 12.26 11.3179 12.164C11.951 12.0288 12.5237 11.8574 13.0181 11.6581C13.6565 11.4008 14.1398 11.107 14.4555 10.8062C14.7699 10.5066 14.8855 10.2341 14.8855 10H15.8855C15.8855 10.5948 15.5844 11.1118 15.1453 11.5301C14.7075 11.9474 14.1005 12.3 13.3919 12.5856C12.7724 12.8353 12.0563 13.0412 11.2712 13.1941ZM4.61532 13.1943C3.82986 13.0414 3.11341 12.8354 2.49359 12.5856C1.78496 12.3 1.17803 11.9474 0.740174 11.5301C0.301105 11.1118 0 10.5948 0 10H1C1 10.2341 1.11561 10.5066 1.43002 10.8062C1.74565 11.107 2.22897 11.4008 2.86739 11.6581C3.36206 11.8575 3.93513 12.029 4.56859 12.1642C4.56302 12.2602 4.56019 12.3571 4.56019 12.4548C4.56019 12.7073 4.57911 12.9544 4.61532 13.1943Z"
      style={{ fill: "var(--dark-blue)" }}
    />
  </svg>
);

export const IconFilterSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="mask0_316_20"
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="24"
      height="24"
    >
      <rect width="24" height="24" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_316_20)">
      <path
        d="M21 4L3 4"
        style={{ stroke: "var(--dark-blue)" }}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M19 7L3 7"
        style={{ stroke: "var(--dark-blue)" }}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M17 10H3"
        style={{ stroke: "var(--dark-blue)" }}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13 13H3"
        style={{ stroke: "var(--dark-blue)" }}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11 16H3"
        style={{ stroke: "var(--dark-blue)" }}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M9 19H3"
        style={{ stroke: "var(--dark-blue)" }}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
  </svg>
);

export const IconCompareReportSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.9468 16.6485H24.0016C24.0016 14.5154 23.9649 12.4848 23.9282 10.4543C23.8915 8.42372 23.8547 6.39316 23.8547 4.26004C23.8161 4.23106 23.5847 3.99024 23.4487 3.84865C23.401 3.79902 23.365 3.76159 23.3532 3.74973C22.6006 2.99712 22.1272 2.5251 21.6542 2.05339L21.6541 2.05328L21.6539 2.05312L21.6526 2.05184L21.6514 2.05057C21.1782 1.57876 20.7049 1.10671 19.9521 0.353949L19.8838 0.28646L19.8836 0.28633L19.8836 0.286295C19.7191 0.124029 19.3247 -0.264942 19.2925 -0.307855L12.9346 -0.311035C12.9346 2.61059 12.936 5.37585 12.9374 8.14495C12.9389 10.9218 12.9403 13.7024 12.9403 16.6485H12.9468ZM21.7134 3.74973H19.9521V1.98845L21.7134 3.74973ZM14.0942 0.846877C14.824 0.846877 15.6303 0.847926 16.4374 0.848975H16.4375H16.4375H16.4376C17.2465 0.850027 18.0563 0.85108 18.791 0.85108V4.91682C19.5355 4.91682 20.1909 4.91575 20.8428 4.9147C21.4874 4.91365 22.1284 4.91261 22.8484 4.91261V15.4721H14.0877V0.846877H14.0942ZM4.5569 -0.31047H10.9199V16.6483H-0.142578L-0.142498 4.39406C-0.109572 4.36937 -0.0763742 4.33084 -0.0448856 4.2943L-0.0448836 4.2943C-0.0264644 4.27292 -0.00863013 4.25222 0.0082223 4.23537C0.761656 3.48194 1.44867 2.79676 2.13569 2.11158L2.13569 2.11158L2.13569 2.11158L2.13569 2.11158L2.13569 2.11158L2.1357 2.11157L2.1357 2.11157C2.82272 1.42639 3.50974 0.741206 4.26318 -0.0122343C4.27296 -0.0220102 4.29022 -0.0388776 4.31169 -0.0598641C4.39052 -0.136905 4.52614 -0.269457 4.5569 -0.31047ZM3.91128 3.74977V1.98849L2.14999 3.74977H3.91128ZM1.00842 15.4721H9.76265V0.846914H5.06812V4.8951H1.00842V15.4721ZM17.0296 21.2253C16.6382 20.8404 16.4796 20.6819 16.16 20.3622L16.173 20.3687C16.8637 19.6781 17.4723 19.0151 18.1186 18.311C18.2842 18.1306 18.4523 17.9475 18.6249 17.7605C19.0229 18.1796 19.3584 18.544 19.7088 18.9246L19.7091 18.9249L19.7096 18.9254L19.7096 18.9255L19.71 18.9259L19.7102 18.9261C20.1126 19.3632 20.5348 19.8217 21.0941 20.4094C20.9407 20.5628 20.8292 20.6728 20.7143 20.7861L20.7143 20.7862L20.7143 20.7862L20.7142 20.7862L20.7133 20.7872L20.7122 20.7882L20.712 20.7884L20.7119 20.7886L20.7115 20.7889L20.7114 20.789L20.7114 20.789C20.5879 20.9108 20.4601 21.0369 20.2717 21.2253C20.1412 21.0855 20.0092 20.9413 19.8758 20.7954L19.8754 20.795L19.8752 20.7948C19.7287 20.6346 19.5803 20.4724 19.4302 20.312C19.4204 20.3186 19.4122 20.3235 19.4041 20.3284L19.3948 20.334C19.3896 20.3372 19.3841 20.3406 19.378 20.3447V24.035H4.72672V20.2794C4.39403 20.6382 4.11353 20.9448 3.8526 21.2318L3.04426 20.4233C3.89286 19.5136 4.60611 18.746 5.47958 17.7879C5.87288 18.1936 6.22476 18.5861 6.5746 18.9763C6.9889 19.4384 7.40032 19.8973 7.87424 20.3712C7.72441 20.521 7.61863 20.6319 7.50831 20.7476L7.50825 20.7476C7.38805 20.8737 7.26244 21.0053 7.06857 21.1992C6.92065 21.0439 6.76644 20.876 6.60832 20.7038C6.48752 20.5723 6.36443 20.4383 6.24012 20.3055C6.22639 20.3101 6.21587 20.3179 6.20405 20.3266C6.19903 20.3304 6.19377 20.3342 6.18793 20.3381V22.8887H17.8711V20.2925C17.7715 20.4029 17.6741 20.5112 17.5788 20.6171L17.5777 20.6182L17.5772 20.6188C17.386 20.8313 17.2035 21.0341 17.0296 21.2253ZM2.46925 7.57245H8.30106V6.70486H2.46925V7.57245ZM2.46925 9.63356H8.30105V10.5012H2.46925V9.63356ZM15.5487 7.57245H21.3805V6.70486H15.5487V7.57245ZM15.5555 9.63356H21.3873V10.5012H15.5555V9.63356ZM15.5555 13.4366H18.4779V12.569H15.5555V13.4366Z"
      style={{ fill: "var(--dark-blue)" }}
    />
  </svg>
);

export const IconIncidentResolveSvg = () => (
  <svg
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.6659 11.0375C18.8959 7.97752 17.1359 4.91752 15.3759 1.85752C14.7159 0.707516 13.6259 0.717516 12.9659 1.86752C11.6659 4.11752 10.3659 6.37752 9.05586 8.63752C7.80586 10.8175 6.54586 12.9975 5.28586 15.1775C3.95586 17.4675 2.61586 19.7575 1.28586 22.0475C1.08586 22.3975 0.925864 22.7675 1.03586 23.1975C1.20586 23.8575 1.65586 24.1575 2.46586 24.1575H17.1959"
      stroke="#74B856"
      strokeWidth="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M9.15576 14.7476C9.15576 14.7476 11.1958 16.2076 11.9958 17.9176C11.9958 17.9176 12.8558 15.7276 17.3558 11.4976"
      stroke="#74B856"
      strokeWidth="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M20.5958 22.2375C22.7497 22.2375 24.4958 20.4914 24.4958 18.3375C24.4958 16.1836 22.7497 14.4375 20.5958 14.4375C18.4419 14.4375 16.6958 16.1836 16.6958 18.3375C16.6958 20.4914 18.4419 22.2375 20.5958 22.2375Z"
      fill="#74B856"
    />
    <path
      d="M21.2456 15.7576C21.2456 15.7576 21.2456 15.8176 21.2456 15.8476C21.1656 17.0176 21.0956 18.1776 21.0156 19.3376C20.9956 19.5976 20.8656 19.6976 20.5756 19.6976C20.3056 19.6976 20.1956 19.5976 20.1756 19.3376C20.1156 18.5376 20.0956 17.7476 19.9956 16.9476C19.9556 16.5476 19.9956 16.1576 19.9356 15.7576C19.8756 15.3176 20.2856 15.1276 20.5956 15.1376C20.9556 15.1376 21.3056 15.3676 21.2356 15.7576H21.2456Z"
      fill="white"
    />
    <path
      d="M20.6054 21.5375C20.2954 21.5375 20.0654 21.2975 20.0654 20.9875C20.0654 20.6775 20.2954 20.4575 20.5854 20.4375C20.8854 20.4375 21.1354 20.6675 21.1454 20.9775C21.1454 21.2875 20.9154 21.5275 20.6054 21.5375Z"
      fill="white"
    />
  </svg>
);

export const IconIncidentTriggeredSvg = () => (
  <svg
    width="27"
    height="25"
    viewBox="0 0 27 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.6659 11.0375C18.8959 7.97752 17.1359 4.91752 15.3759 1.85752C14.7159 0.707516 13.6259 0.717516 12.9659 1.86752C11.6659 4.11752 10.3659 6.37752 9.05586 8.63752C7.80586 10.8175 6.54586 12.9975 5.28586 15.1775C3.95586 17.4675 2.61586 19.7575 1.28586 22.0475C1.08586 22.3975 0.925864 22.7675 1.03586 23.1975C1.20586 23.8575 1.65586 24.1575 2.46586 24.1575H17.1959"
      stroke="#E84336"
      strokeWidth="1.5"
      stroke-linecap="round"
      stroke-linejoin="bevel"
    />
    <path
      d="M22.276 22.2375C24.4299 22.2375 26.176 20.4914 26.176 18.3375C26.176 16.1836 24.4299 14.4375 22.276 14.4375C20.1221 14.4375 18.376 16.1836 18.376 18.3375C18.376 20.4914 20.1221 22.2375 22.276 22.2375Z"
      fill="#E84336"
    />
    <path
      d="M22.9258 15.7576C22.9258 15.7576 22.9258 15.8176 22.9258 15.8476C22.8458 17.0176 22.7758 18.1776 22.6958 19.3376C22.6758 19.5976 22.5458 19.6976 22.2558 19.6976C21.9858 19.6976 21.8758 19.5976 21.8558 19.3376C21.7958 18.5376 21.7758 17.7476 21.6758 16.9476C21.6358 16.5476 21.6758 16.1576 21.6158 15.7576C21.5558 15.3176 21.9658 15.1276 22.2758 15.1376C22.6358 15.1376 22.9858 15.3676 22.9158 15.7576H22.9258Z"
      fill="white"
    />
    <path
      d="M22.2856 21.5375C21.9756 21.5375 21.7456 21.2975 21.7456 20.9875C21.7456 20.6775 21.9756 20.4575 22.2656 20.4375C22.5656 20.4375 22.8156 20.6675 22.8256 20.9775C22.8256 21.2875 22.5956 21.5275 22.2856 21.5375Z"
      fill="white"
    />
    <path
      d="M13.4157 15.3575C12.9457 16.8275 12.5057 18.1975 12.0657 19.5775C12.1057 19.5975 12.1557 19.6175 12.1957 19.6375C13.5557 17.8675 14.9157 16.0975 16.3457 14.2375H14.0457C14.5157 12.7275 14.9657 11.2975 15.4357 9.79754C14.8157 9.79754 14.2457 9.80754 13.6857 9.79754C13.3357 9.78754 13.1757 9.85754 13.0657 10.1375C12.6657 11.2575 12.2157 12.3575 11.7857 13.4675C11.5457 14.0775 11.3157 14.6875 11.0557 15.3575H13.4157Z"
      fill="#E84336"
    />
  </svg>
);
export const IconIncidentAcknowledgeSvg = () => (
  <svg
    width="27"
    height="25"
    viewBox="0 0 27 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.6659 11.0375C18.8959 7.97752 17.1359 4.91752 15.3759 1.85752C14.7159 0.707516 13.6259 0.717516 12.9659 1.86752C11.6659 4.11752 10.3659 6.37752 9.05586 8.63752C7.80586 10.8175 6.54586 12.9975 5.28586 15.1775C3.95586 17.4675 2.61586 19.7575 1.28586 22.0475C1.08586 22.3975 0.925864 22.7675 1.03586 23.1975C1.20586 23.8575 1.65586 24.1575 2.46586 24.1575H17.1959"
      stroke="#FFBF00"
      strokeWidth="1.5"
      stroke-linecap="round"
      stroke-linejoin="bevel"
    />
    <path
      d="M11.5263 13.3475C11.1763 13.5275 10.9463 13.8675 10.9463 14.2575V18.0075C10.9463 19.1675 11.4163 19.6275 12.6063 19.6375C13.7463 19.6375 14.8963 19.6375 16.0363 19.6375C16.3463 19.6375 16.6463 19.6375 16.9563 19.6375C17.2763 19.6375 17.5463 19.5275 17.5363 19.1675C17.5363 18.8375 17.2663 18.7475 16.9763 18.7475C16.7563 18.7475 16.5263 18.7475 16.3063 18.7475C16.0063 18.7475 15.8363 18.5975 15.8563 18.2975C15.8663 18.0275 16.0363 17.9175 16.3063 17.9175C16.7163 17.9175 17.1263 17.9175 17.5263 17.9175C17.8463 17.9175 18.1263 17.8175 18.1163 17.4575C18.1163 17.1375 17.8563 17.0275 17.5563 17.0275C17.1663 17.0275 16.7763 17.0075 16.3963 17.0275C16.0963 17.0275 15.8463 16.9575 15.8663 16.6075C15.8863 16.2775 16.1163 16.1875 16.4263 16.1975C16.8163 16.1975 17.1963 16.1975 17.5863 16.1975C17.8863 16.1975 18.1363 16.0875 18.1263 15.7575C18.1263 15.4375 17.8763 15.3175 17.5763 15.3075C17.2063 15.3075 16.8463 15.3075 16.4763 15.3075C16.1763 15.3075 15.8663 15.3175 15.8563 14.9175C15.8563 14.4875 16.1763 14.4675 16.5063 14.4675C16.8163 14.4675 17.1163 14.4675 17.4263 14.4675C17.7363 14.4675 18.1063 14.4875 18.1263 14.0675C18.1463 13.6175 17.7763 13.5875 17.4363 13.5875C16.5463 13.5875 15.6463 13.5875 14.7563 13.5875C14.3563 13.5875 14.0963 13.2575 14.1863 12.8675C14.3463 12.1775 14.5263 11.4875 14.6763 10.7975C14.7863 10.3275 14.6063 9.66748 14.0863 9.84748C13.2463 10.1175 13.2463 11.2375 12.8563 11.9975C12.5363 12.6175 12.1163 13.0875 11.5363 13.3875L11.5263 13.3475Z"
      fill="#FFBF00"
    />
    <path
      d="M8.71606 16.3875C8.71606 17.0375 8.71606 17.6975 8.71606 18.3475C8.71606 19.0075 8.85606 19.1675 9.40606 19.1675C9.52606 19.1675 9.63605 19.1675 9.75605 19.1675C10.2061 19.1575 10.3561 18.9675 10.3561 18.4275C10.3561 17.2775 10.3361 16.1175 10.3561 14.9675C10.3861 13.5575 10.2561 13.6675 9.24606 13.6775C8.86606 13.6775 8.70605 13.8775 8.70605 14.3275C8.70605 15.0175 8.70605 15.6975 8.70605 16.3875H8.71606Z"
      fill="#FFBF00"
    />
    <path
      d="M22.7159 22.2375C24.8698 22.2375 26.6159 20.4914 26.6159 18.3375C26.6159 16.1836 24.8698 14.4375 22.7159 14.4375C20.562 14.4375 18.8159 16.1836 18.8159 18.3375C18.8159 20.4914 20.562 22.2375 22.7159 22.2375Z"
      fill="#FFBF00"
    />
    <path
      d="M23.376 15.7576C23.376 15.7576 23.376 15.8176 23.376 15.8476C23.296 17.0176 23.226 18.1776 23.146 19.3376C23.126 19.5976 22.996 19.6976 22.706 19.6976C22.436 19.6976 22.326 19.5976 22.306 19.3376C22.246 18.5376 22.226 17.7476 22.126 16.9476C22.086 16.5476 22.126 16.1576 22.066 15.7576C22.006 15.3176 22.416 15.1276 22.726 15.1376C23.086 15.1376 23.436 15.3676 23.366 15.7576H23.376Z"
      fill="white"
    />
    <path
      d="M22.726 21.5375C22.416 21.5375 22.186 21.2975 22.186 20.9875C22.186 20.6775 22.416 20.4575 22.706 20.4375C23.006 20.4375 23.256 20.6675 23.266 20.9775C23.266 21.2875 23.036 21.5275 22.726 21.5375Z"
      fill="white"
    />
  </svg>
);

export const RefreshIncidentIconSvg = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.299 3.25L27.6913 21.25C28.2687 22.25 27.547 23.5 26.3923 23.5H5.60769C4.45299 23.5 3.73131 22.25 4.30866 21.25L14.701 3.25C15.2783 2.25 16.7217 2.25 17.299 3.25Z"
      stroke="#196BB4"
    />
    <path
      d="M15.36 16.5609C14.89 18.0309 14.45 19.4009 14.01 20.7809C14.05 20.8009 14.1 20.8209 14.14 20.8409C15.5 19.0709 16.86 17.3009 18.29 15.4409H15.99C16.46 13.9309 16.91 12.5009 17.38 11.0009C16.76 11.0009 16.19 11.0109 15.63 11.0009C15.28 10.9909 15.12 11.0609 15.01 11.3409C14.61 12.4609 14.16 13.5609 13.73 14.6709C13.49 15.2809 13.26 15.8909 13 16.5609H15.36Z"
      fill="#196BB4"
    />
  </svg>
);

export const RefreshIncidentResolvedIconSvg = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.701 3.25C15.2783 2.25 16.7217 2.25 17.299 3.25L27.6913 21.25C28.2687 22.25 27.547 23.5 26.3923 23.5H5.60769C4.45299 23.5 3.73131 22.25 4.30866 21.25L14.701 3.25Z"
      stroke="#74B856"
    />
    <path
      d="M11 16.1789C11 16.1789 12.9954 17.607 13.7779 19.2796C13.7779 19.2796 14.6191 17.1375 19.0207 13"
      stroke="#74B856"
      strokeWidth="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const AcknowledgeIconSvg = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.701 3.25C15.2783 2.25 16.7217 2.25 17.299 3.25L27.6913 21.25C28.2687 22.25 27.547 23.5 26.3923 23.5H5.60769C4.45299 23.5 3.73131 22.25 4.30866 21.25L14.701 3.25Z"
      stroke="#FFBF00"
    />
    <path
      d="M13.7595 13.4548C13.417 13.6309 13.1919 13.9636 13.1919 14.3453V18.015C13.1919 19.1502 13.6518 19.6003 14.8164 19.6101C15.932 19.6101 17.0573 19.6101 18.1729 19.6101C18.4763 19.6101 18.7699 19.6101 19.0732 19.6101C19.3864 19.6101 19.6506 19.5025 19.6408 19.1502C19.6408 18.8272 19.3766 18.7392 19.0928 18.7392C18.8775 18.7392 18.6524 18.7392 18.4372 18.7392C18.1436 18.7392 17.9772 18.5924 17.9968 18.2988C18.0066 18.0346 18.1729 17.9269 18.4372 17.9269C18.8384 17.9269 19.2396 17.9269 19.631 17.9269C19.9442 17.9269 20.2182 17.8291 20.2084 17.4768C20.2084 17.1636 19.954 17.056 19.6604 17.056C19.2787 17.056 18.8971 17.0364 18.5252 17.056C18.2316 17.056 17.987 16.9875 18.0066 16.645C18.0261 16.322 18.2512 16.234 18.5546 16.2438C18.9362 16.2438 19.3081 16.2438 19.6898 16.2438C19.9833 16.2438 20.228 16.1361 20.2182 15.8132C20.2182 15.5 19.9735 15.3826 19.68 15.3728C19.3179 15.3728 18.9656 15.3728 18.6035 15.3728C18.3099 15.3728 18.0066 15.3826 17.9968 14.9912C17.9968 14.5704 18.3099 14.5508 18.6329 14.5508C18.9362 14.5508 19.2298 14.5508 19.5332 14.5508C19.8365 14.5508 20.1986 14.5704 20.2182 14.1594C20.2378 13.719 19.8757 13.6896 19.543 13.6896C18.672 13.6896 17.7913 13.6896 16.9203 13.6896C16.5289 13.6896 16.2745 13.3667 16.3625 12.985C16.5191 12.3098 16.6953 11.6346 16.842 10.9594C16.9497 10.4994 16.7735 9.85354 16.2647 10.0297C15.4427 10.2939 15.4427 11.3899 15.061 12.1337C14.7479 12.7404 14.3368 13.2003 13.7693 13.4939L13.7595 13.4548Z"
      fill="#FFBF00"
    />
    <path
      d="M11.0098 16.4297C11.0098 17.0658 11.0098 17.7116 11.0098 18.3477C11.0098 18.9936 11.1468 19.1502 11.685 19.1502C11.8024 19.1502 11.9101 19.1502 12.0275 19.1502C12.4679 19.1404 12.6147 18.9545 12.6147 18.426C12.6147 17.3006 12.5951 16.1655 12.6147 15.0401C12.644 13.6603 12.5168 13.7679 11.5284 13.7777C11.1566 13.7777 11 13.9734 11 14.4138C11 15.089 11 15.7545 11 16.4297H11.0098Z"
      fill="#FFBF00"
    />
  </svg>
);

export const IconIncidentBlankSvg = () => (
  <svg
    width="24"
    height="26"
    viewBox="0 0 24 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="25.2" />
  </svg>
);

export const IconFilterSvg1 = () => (
  <svg
    width="20"
    height="17"
    viewBox="0 0 20 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19 1L1 0.999999"
      stroke="#142A41"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M17 4L1 4"
      stroke="#142A41"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M15 7L1 7"
      stroke="#142A41"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M11 10H1"
      stroke="#142A41"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M9 13H1"
      stroke="#142A41"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M7 16H1"
      stroke="#142A41"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const IconInvisibleLinks = () => (
  <svg
    width="84"
    height="114"
    viewBox="0 0 84 114"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0 0H0.5H59.5H59.6629L59.7946 0.0959962L83.7946 17.596L84 17.7458V18V113V113.5H83.5H80.5H0.5H0V113V0.5V0ZM1 1V112.5H80.5H83V19.0104L61.1002 23.4899L60.5392 23.6046L60.5011 23.0333L59.0322 1H1ZM83 18.2542V18.5L82.9409 18.2111L83 18.2542ZM60.0701 1.53449L82.7166 18.0476L61.4608 22.3954L60.0701 1.53449ZM5.09358 78.7836C5.19821 78.9649 5.35563 79.2323 5.56499 79.5735C5.98374 80.2559 6.61005 81.2333 7.43714 82.4073C9.027 84.6641 11.3558 87.6431 14.3749 90.6492L14.3749 66.8522C11.3562 69.8578 9.02755 72.8363 7.43765 75.0927C6.61041 76.2667 5.98396 77.2441 5.56511 77.9266C5.35571 78.2678 5.19825 78.5352 5.0936 78.7164L5.07423 78.75L5.09358 78.7836ZM23.3749 97.8445C20.2882 95.9329 17.5598 93.7106 15.2091 91.4632C15.3109 91.3717 15.3749 91.239 15.3749 91.0913L15.3749 66.0913C15.3749 66.028 15.3632 65.9675 15.3418 65.9118C17.6623 63.7075 20.3462 61.5325 23.3749 59.6566L23.3749 97.8445ZM24.3747 59.0752C24.3749 59.0805 24.3749 59.0859 24.3749 59.0913L24.3749 98.0913C24.3749 98.2025 24.3386 98.3052 24.2773 98.3882C26.5798 99.7374 29.0721 100.907 31.7465 101.785V85.9735C30.5587 84.0117 29.875 81.7107 29.875 79.25C29.875 76.7893 30.5587 74.4883 31.7465 72.5265V55.7154C29.1094 56.5816 26.6494 57.7308 24.3737 59.0564C24.3742 59.0626 24.3745 59.0689 24.3747 59.0752ZM42.2919 103.5C38.9007 103.492 35.7156 102.976 32.7465 102.097V87.4001C35.0194 90.2212 38.4467 92.073 42.3109 92.238L42.2919 103.5ZM42.3547 66.2602C38.4726 66.413 35.0279 68.2682 32.7465 71.0999V55.445C32.7465 55.4359 32.7462 55.4269 32.7457 55.418C32.7455 55.4132 32.7452 55.4084 32.7448 55.4036C35.7387 54.5172 38.9521 54 42.375 54L42.4418 54.0001C42.3995 54.0734 42.3751 54.1584 42.375 54.2492L42.3547 66.2602ZM43.3154 54.013C43.3535 54.0838 43.3751 54.1648 43.375 54.2508L43.3547 66.2587C50.3122 66.5111 55.875 72.2309 55.875 79.25C55.875 86.2838 50.2888 92.0129 43.3109 92.2428L43.2919 103.488C54.7259 103.178 63.7595 97.1213 70.0018 91.0175C73.2028 87.8876 75.6581 84.7562 77.3129 82.4073C78.1399 81.2333 78.7663 80.2559 79.185 79.5735C79.3944 79.2323 79.5518 78.9649 79.6564 78.7836L79.6758 78.75L79.6564 78.7164C79.5517 78.5352 79.3943 78.2678 79.1849 77.9266C78.766 77.2441 78.1396 76.2667 77.3123 75.0927C75.6573 72.7438 73.2016 69.6125 70.0004 66.4825C63.7619 60.3829 54.7364 54.3303 43.3154 54.013ZM43.3182 87.9055C47.8987 87.6748 51.5417 83.8878 51.5417 79.25C51.5417 74.622 47.9142 70.8413 43.3474 70.596L43.3182 87.9055ZM42.3474 70.5991C37.8066 70.8718 34.2083 74.6407 34.2083 79.25C34.2083 83.8494 37.7912 87.612 42.3182 87.8991L42.3474 70.5991ZM80.25 78.75C80.689 78.9894 80.6888 78.9897 80.6886 78.99L80.688 78.9912L80.6857 78.9953L80.6775 79.0103L80.6459 79.067C80.6181 79.1165 80.5769 79.1892 80.5225 79.2835C80.4136 79.4721 80.2516 79.7472 80.0374 80.0965C79.6088 80.7948 78.9709 81.7901 78.1304 82.9833C76.4498 85.3688 73.9557 88.5499 70.7009 91.7325C64.1976 98.0915 54.61 104.5 42.375 104.5C30.14 104.5 20.5524 98.0915 14.0491 91.7325C10.7943 88.5499 8.30024 85.3688 6.61964 82.9833C5.77905 81.7901 5.14117 80.7948 4.71264 80.0965C4.49836 79.7472 4.33637 79.4721 4.22751 79.2835C4.17307 79.1892 4.13191 79.1165 4.10414 79.067L4.07252 79.0103L4.06426 78.9953L4.06202 78.9912L4.06137 78.99C4.06118 78.9897 4.06104 78.9894 4.5 78.75C4.06107 78.5105 4.06121 78.5103 4.0614 78.5099L4.06205 78.5088L4.06428 78.5047L4.07255 78.4897L4.10417 78.433C4.13196 78.3834 4.17313 78.3107 4.22757 78.2164C4.33647 78.0278 4.4985 77.7527 4.71283 77.4035C5.14145 76.7051 5.77946 75.7099 6.6202 74.5167C8.30109 72.1312 10.7955 68.95 14.0505 65.7675C20.5543 59.4085 30.1418 53 42.375 53C54.6082 53 64.1957 59.4085 70.6995 65.7675C73.9545 68.95 76.4489 72.1312 78.1298 74.5167C78.9705 75.7099 79.6086 76.7051 80.0372 77.4035C80.2515 77.7527 80.4135 78.0278 80.5224 78.2164C80.5486 78.2617 80.5716 78.302 80.5917 78.3371C80.6133 78.3752 80.6314 78.4072 80.6458 78.433L80.6775 78.4897L80.6857 78.5047L80.688 78.5088L80.6886 78.5099C80.6888 78.5103 80.6889 78.5105 80.25 78.75ZM80.25 78.75L80.6889 78.5105C80.7703 78.6598 80.7704 78.8402 80.689 78.9894L80.25 78.75ZM4.5 78.75L4.06104 78.9894C3.97964 78.8402 3.97965 78.6598 4.06107 78.5105L4.5 78.75Z"
      fill="black"
    />
  </svg>
);

export const IconNewRunInstanceSvg = () => (
  <svg
    width="30"
    height="31"
    viewBox="0 0 30 31"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.86849 13.0499L7.40137 15.517L9.86849 17.9841"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M19.737 13.0499L22.2041 15.517L19.737 17.9841"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M14.8027 27.8526C21.6155 27.8526 27.1383 22.3298 27.1383 15.517C27.1383 8.70423 21.6155 3.1814 14.8027 3.1814C7.98997 3.1814 2.46713 8.70423 2.46713 15.517C2.46713 22.3298 7.98997 27.8526 14.8027 27.8526Z"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M16.0363 12.6428L13.5692 18.3912"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const IconNewRulesImport = () => (
  <svg
    width="23"
    height="23"
    viewBox="0 0 23 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.7845 12.8231L7.37877 16.7833"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M6.25169 13.2988L6.50823 17.76L10.9694 17.5035"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10.8776 2.64624H9.02722C4.40137 2.64624 2.55103 4.49658 2.55103 9.12243V14.6735C2.55103 19.2993 4.40137 21.1496 9.02722 21.1496H14.5782C19.2041 21.1496 21.0544 19.2993 21.0544 14.6735V12.8231"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <rect
      x="13.2279"
      y="2.22107"
      width="8.2517"
      height="8.2517"
      rx="0.5"
      stroke="#232323"
    />
    <path
      d="M14.5782 5.41705L15.6279 6.34692L17.3537 4.034"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M17.3537 7.72998L18.4035 8.65985L20.1293 6.34692"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const IconNewLinkedServices = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.8027 19.7369V23.4376"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M14.8027 28.3719C16.1653 28.3719 17.2698 27.2673 17.2698 25.9047C17.2698 24.5422 16.1653 23.4376 14.8027 23.4376C13.4402 23.4376 12.3356 24.5422 12.3356 25.9047C12.3356 27.2673 13.4402 28.3719 14.8027 28.3719Z"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M22.2041 25.9048H17.2699"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M12.3356 25.9048H7.40137"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M27.1383 9.56012V15.1111C27.1383 18.8118 25.9048 19.7369 20.9705 19.7369H8.63493C3.70069 19.7369 2.46713 18.8118 2.46713 15.1111V5.85937C2.46713 2.15869 3.70069 1.23352 8.63493 1.23352H10.4853C12.3356 1.23352 12.7409 1.64319 13.4458 2.34368L15.2962 4.19405C15.772 4.65664 16.0363 4.93427 17.2699 4.93427H20.9705C25.9048 4.93427 27.1383 5.85944 27.1383 9.56012Z"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-miterlimit="10"
    />
    <path
      d="M14.8027 13.5692C15.484 13.5692 16.0363 13.0169 16.0363 12.3356C16.0363 11.6543 15.484 11.1021 14.8027 11.1021C14.1215 11.1021 13.5692 11.6543 13.5692 12.3356C13.5692 13.0169 14.1215 13.5692 14.8027 13.5692Z"
      stroke="#3A3A3A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10.6908 12.6973V11.9737C10.6908 11.546 11.0403 11.1924 11.4721 11.1924C12.2163 11.1924 12.5206 10.6661 12.1464 10.0205C11.9326 9.65045 12.06 9.16936 12.4342 8.95554L13.1456 8.54847C13.4704 8.35521 13.8898 8.47034 14.0831 8.79518L14.1283 8.8733C14.4984 9.51887 15.1069 9.51887 15.4811 8.8733L15.5264 8.79518C15.7196 8.47034 16.139 8.35521 16.4639 8.54847L17.1752 8.95554C17.5494 9.16936 17.6769 9.65045 17.463 10.0205C17.0889 10.6661 17.3931 11.1924 18.1374 11.1924C18.565 11.1924 18.9186 11.5419 18.9186 11.9737V12.6973C18.9186 13.125 18.5691 13.4786 18.1374 13.4786C17.3931 13.4786 17.0889 14.0049 17.463 14.6505C17.6769 15.0247 17.5494 15.5016 17.1752 15.7155L16.4639 16.1225C16.139 16.3158 15.7196 16.2007 15.5264 15.8758L15.4811 15.7977C15.1111 15.1521 14.5025 15.1521 14.1283 15.7977L14.0831 15.8758C13.8898 16.2007 13.4704 16.3158 13.1456 16.1225L12.4342 15.7155C12.06 15.5016 11.9326 15.0205 12.1464 14.6505C12.5206 14.0049 12.2163 13.4786 11.4721 13.4786C11.0403 13.4786 10.6908 13.125 10.6908 12.6973Z"
      stroke="#3A3A3A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const IconNewConnectionKeys = () => (
  <svg
    width="30"
    height="31"
    viewBox="0 0 30 31"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.46713 24.2947C4.6382 24.3441 6.11847 25.8244 6.16781 27.9954"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M9.86849 27.9955C9.85616 27.058 9.6958 26.1821 9.41208 25.3803C8.68428 23.3203 7.12998 21.7783 5.08227 21.0505C4.28046 20.7668 3.40464 20.6065 2.46713 20.5941"
      stroke="#3A3A3A"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M2.46157 27.9955H2.47267"
      stroke="#3A3A3A"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.4151 14.0359L12.9599 13.8289L12.7529 14.284L11.9452 16.0593L11.7382 16.5144L12.1933 16.7215L13.9686 17.5292L14.4238 17.7362L14.6308 17.2811L15.2367 15.9493L21.1336 18.4765C21.0282 18.7509 20.9705 19.049 20.9705 19.3605C20.9705 20.7231 22.075 21.8276 23.4376 21.8276C24.8002 21.8276 25.9047 20.7231 25.9047 19.3605C25.9047 17.998 24.8002 16.8934 23.4376 16.8934C22.7519 16.8934 22.1316 17.1731 21.6845 17.6246L15.1097 14.8068L13.4151 14.0359ZM13.4435 15.1808L13.0625 16.0183L13.9276 16.4119L14.3173 15.5552L13.4435 15.1808ZM23.4376 20.8276C24.2479 20.8276 24.9047 20.1708 24.9047 19.3605C24.9047 18.5502 24.2479 17.8934 23.4376 17.8934C22.6273 17.8934 21.9705 18.5502 21.9705 19.3605C21.9705 20.1708 22.6273 20.8276 23.4376 20.8276Z"
      fill="black"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M17.6525 3.76352L17.6515 3.76275C16.0587 2.48339 13.5352 2.48134 11.9522 3.75036L11.9519 3.75064L3.40583 10.595L3.40465 10.5959C2.79402 11.0817 2.33096 11.8006 2.04769 12.5431C1.7646 13.2851 1.63118 14.1291 1.76384 14.8974L2.10885 16.9674C2.17695 17.376 2.56337 17.652 2.97195 17.5839C3.38052 17.5158 3.65654 17.1293 3.58844 16.7208L3.24304 14.6485L3.24306 14.6485L3.24221 14.6436C3.1659 14.2037 3.23606 13.6363 3.44916 13.0777C3.66223 12.5193 3.98824 12.0482 4.3392 11.7692L4.34135 11.7675L12.8899 4.92116L12.8903 4.92087C13.9222 4.09338 15.6658 4.0912 16.7128 4.93269L16.7143 4.93392L25.2608 11.7662L25.2618 11.767C25.6077 12.046 25.9316 12.5176 26.1453 13.0777C26.3589 13.6376 26.4323 14.2072 26.3605 14.6496L26.3602 14.6509L25.9856 16.8934H27.5063L27.8402 14.8954L27.8406 14.8926C27.9658 14.1263 27.8294 13.284 27.5467 12.5431C27.2641 11.8023 26.8051 11.0839 26.2015 10.5979L26.1995 10.5963L17.6525 3.76352ZM26.4758 23.0612H24.955L24.7205 24.465L24.7203 24.4662C24.4848 25.8458 23.1065 27.0112 21.7098 27.0112H14.0617C13.6475 27.0112 13.3117 27.347 13.3117 27.7612C13.3117 28.1754 13.6475 28.5112 14.0617 28.5112H21.7098C23.8407 28.5112 25.842 26.8153 26.1992 24.7167L26.1996 24.7145L26.4758 23.0612Z"
      fill="#3A3A3A"
    />
  </svg>
);
export const IconNewFileProcessingAttributes = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.91281 6.44603C6.4212 7.12881 6.25 7.97068 6.25 8.5208V8.54441V8.56813V8.59195V8.61587V8.6399V8.66403V8.68826V8.7126V8.73703V8.76157V8.7862V8.81094V8.83578V8.86071V8.88575V8.91088V8.9361V8.96143V8.98685V9.01237V9.03798V9.06369V9.08949V9.11539V9.14138V9.16746V9.19364V9.2199V9.24626V9.27271V9.29925V9.32588V9.3526V9.37941V9.40631V9.43329V9.46037V9.48753V9.51477V9.54211V9.56953V9.59703V9.62462V9.65229V9.68005V9.70789V9.73581V9.76382V9.7919V9.82007V9.84832V9.87665V9.90506V9.93354V9.96211V9.99076V10.0195V10.0483V10.0772V10.1061V10.1351V10.1642V10.1934V10.2227V10.252V10.2814V10.3109V10.3404V10.3701V10.3997V10.4295V10.4593V10.4892V10.5192V10.5493V10.5794V10.6095V10.6398V10.6701V10.7005V10.7309V10.7614V10.792V10.8226V10.8534V10.8841V10.915V10.9458V10.9768V11.0078V11.0389V11.07V11.1012V11.1325V11.1638V11.1952V11.2266V11.2581V11.2897V11.3213V11.353V11.3847V11.4165V11.4483V11.4802V11.5121V11.5441V11.5762V11.6083V11.6404V11.6726V11.7049V11.7372V11.7695V11.8019V11.8344V11.8669V11.8994V11.932V11.9647V11.9974V12.0301V12.0629V12.0957V12.1286V12.1615V12.1945V12.2275V12.2605V12.2936V12.3267V12.3599V12.3931V12.4264V12.4597V12.493V12.5264V12.5598V12.5932V12.6267V12.6602V12.6938V12.7274V12.761V12.7947V12.8284V12.8621V12.8959V12.9297V12.9635V12.9974V13.0313V13.0652V13.0991V13.1331V13.1671V13.2012V13.2353V13.2694V13.3035V13.3376V13.3718V13.406V13.4403V13.4745V13.5088V13.5431V13.5775V13.6118V13.6462V13.6806V13.715V13.7495V13.7839V13.8184V13.8529V13.8874V13.922V13.9565V13.9911V14.0257V14.0603V14.0949V14.1296V14.1643V14.1989V14.2336V14.2683V14.303V14.3378V14.3725V14.4073V14.442V14.4768V14.5116V14.5464V14.5812V14.616V14.6508V14.6857V14.7205V14.7554V14.7902V14.8251V14.86V14.8948V14.9297V14.9646V14.9995V15.0343V15.0692V15.1041V15.139V15.1739V15.2088V15.2437V15.2786V15.3135V15.3484V15.3833V15.4182V15.4531V15.488V15.5228V15.5577V15.5926V15.6274V15.6623V15.6972V15.732V15.7669V15.8017V15.8365V15.8713V15.9061V15.9409V15.9757V16.0105V16.0453V16.08V16.1148V16.1495V16.1842V16.2189V16.2536V16.2883V16.323V16.3576V16.3923V16.4269V16.4615V16.496V16.5306V16.5652V16.5997V16.6342V16.6687V16.7031V16.7376V16.772V16.8064V16.8408V16.8752V16.9095V16.9438V16.9781V17.0124V17.0466V17.0808V17.115V17.1492V17.1833V17.2174V17.2515V17.2855V17.3195V17.3535V17.3875V17.4214V17.4553V17.4892V17.523V17.5568V17.5906V17.6243V17.658V17.6917V17.7254V17.759V17.7925V17.826V17.8595V17.893V17.9264V17.9598V17.9931V18.0264V18.0597V18.0929V18.1261V18.1592V18.1923V18.2254V18.2584V18.2913V18.3243V18.3571V18.39V18.4228V18.4555V18.4882V18.5209V18.5535V18.586V18.6185V18.651V18.6834V18.7158V18.7481V18.7803V18.8125V18.8447V18.8768V18.9089V18.9409V18.9728V19.0047V19.0366V19.0683V19.1001V19.1317V19.1634V19.1949V19.2264V19.2579V19.2893V19.3206V19.3519V19.3831V19.4142V19.4453V19.4763V19.5073V19.5382V19.5691V19.5998V19.6305V19.6612V19.6918V19.7223V19.7528V19.7832V19.8135V19.8437V19.8739V19.904V19.9341V19.9641V19.994V20.0238V20.0536V20.0833V20.1129V20.1425V20.172V20.2014V20.2307V20.26V20.2892V20.3183V20.3473V20.3763V20.4052V20.434V20.4628V20.4914V20.52V20.5485V20.5769V20.6053V20.6335V20.6617V20.6898V20.7178V20.7457V20.7736V20.8014V20.8291V20.8567V20.8842V20.9116V20.939V20.9662V20.9934V21.0205V21.0475V21.0744V21.1012V21.1279V21.1546V21.1811V21.2076V21.234V21.2603V21.2865V21.3125V21.3386V21.3645V21.3903V21.416V21.4416V21.4672V21.4926V21.5179V21.5432V21.5683V21.5934V21.6183V21.6432V21.6679V21.6926V21.7171V21.7416V21.7659V21.7902V21.8143V21.8384V21.8623V21.8862V21.9099V21.9335V21.957V21.9805V22.0038V22.027V22.0501V22.0731V22.0959V22.1187V22.1414V22.1639V22.1864V22.2087V22.2309V22.2531V22.2751V22.297V22.3187V22.3404V22.3619V22.3834V22.4047V22.4259V22.447V22.468V22.4888V22.5095V22.5302V22.5507V22.571V22.5913V22.6115V22.6315V22.6514V22.6712V22.6908V22.7104V22.7298V22.7491V22.7682V22.7873V22.8062V22.825V22.8437V22.8622V22.8806V22.8989V22.9171V22.9351V22.953V22.9708V22.9884V23.0059V23.0233V23.0406V23.0577V23.0747V23.0916V23.1083V23.1249V23.1413V23.1577V23.1739V23.1899V23.2058V23.2216V23.2373V23.2528V23.2681V23.2834V23.2985V23.3134V23.3282V23.3429V23.3574V23.3718V23.3861V23.4002V23.4141V23.428V23.4417V23.4552C6.25 24.1033 6.34787 24.6155 6.49344 24.9829C6.64114 25.3556 6.81658 25.5305 6.94123 25.5998C7.04043 25.6549 7.17311 25.6826 7.38919 25.5914C7.62888 25.4901 7.96256 25.2401 8.3464 24.7284C9.11012 23.7101 9.93693 23.2125 10.7774 23.2368C11.5796 23.2599 12.1456 23.7535 12.4294 24.1686L14.1416 26.4516L12.9416 27.3516L11.2184 25.054L11.2059 25.0373L11.1944 25.02C11.0907 24.8645 10.915 24.7414 10.7341 24.7362C10.579 24.7317 10.1612 24.8086 9.5464 25.6284C9.06863 26.2654 8.54072 26.7334 7.97266 26.9732C7.38099 27.223 6.75976 27.2149 6.21277 26.911C5.69122 26.6212 5.32816 26.114 5.09891 25.5354C4.86753 24.9515 4.75 24.2431 4.75 23.4552V23.4417V23.428V23.4141V23.4002V23.3861V23.3718V23.3574V23.3429V23.3282V23.3134V23.2985V23.2834V23.2681V23.2528V23.2373V23.2216V23.2058V23.1899V23.1739V23.1577V23.1413V23.1249V23.1083V23.0916V23.0747V23.0577V23.0406V23.0233V23.0059V22.9884V22.9708V22.953V22.9351V22.9171V22.8989V22.8806V22.8622V22.8437V22.825V22.8062V22.7873V22.7682V22.7491V22.7298V22.7104V22.6908V22.6712V22.6514V22.6315V22.6115V22.5913V22.571V22.5507V22.5302V22.5095V22.4888V22.468V22.447V22.4259V22.4047V22.3834V22.3619V22.3404V22.3187V22.297V22.2751V22.2531V22.2309V22.2087V22.1864V22.1639V22.1414V22.1187V22.0959V22.0731V22.0501V22.027V22.0038V21.9805V21.957V21.9335V21.9099V21.8862V21.8623V21.8384V21.8143V21.7902V21.7659V21.7416V21.7171V21.6926V21.6679V21.6432V21.6183V21.5934V21.5683V21.5432V21.5179V21.4926V21.4672V21.4416V21.416V21.3903V21.3645V21.3386V21.3125V21.2865V21.2603V21.234V21.2076V21.1811V21.1546V21.1279V21.1012V21.0744V21.0475V21.0205V20.9934V20.9662V20.939V20.9116V20.8842V20.8567V20.8291V20.8014V20.7736V20.7457V20.7178V20.6898V20.6617V20.6335V20.6053V20.5769V20.5485V20.52V20.4914V20.4628V20.434V20.4052V20.3763V20.3473V20.3183V20.2892V20.26V20.2307V20.2014V20.172V20.1425V20.1129V20.0833V20.0536V20.0238V19.994V19.9641V19.9341V19.904V19.8739V19.8437V19.8135V19.7832V19.7528V19.7223V19.6918V19.6612V19.6305V19.5998V19.5691V19.5382V19.5073V19.4763V19.4453V19.4142V19.3831V19.3519V19.3206V19.2893V19.2579V19.2264V19.1949V19.1634V19.1317V19.1001V19.0683V19.0366V19.0047V18.9728V18.9409V18.9089V18.8768V18.8447V18.8125V18.7803V18.7481V18.7158V18.6834V18.651V18.6185V18.586V18.5535V18.5209V18.4882V18.4555V18.4228V18.39V18.3571V18.3243V18.2913V18.2584V18.2254V18.1923V18.1592V18.1261V18.0929V18.0597V18.0264V17.9931V17.9598V17.9264V17.893V17.8595V17.826V17.7925V17.759V17.7254V17.6917V17.658V17.6243V17.5906V17.5568V17.523V17.4892V17.4553V17.4214V17.3875V17.3535V17.3195V17.2855V17.2515V17.2174V17.1833V17.1492V17.115V17.0808V17.0466V17.0124V16.9781V16.9438V16.9095V16.8752V16.8408V16.8064V16.772V16.7376V16.7031V16.6687V16.6342V16.5997V16.5652V16.5306V16.496V16.4615V16.4269V16.3923V16.3576V16.323V16.2883V16.2536V16.2189V16.1842V16.1495V16.1148V16.08V16.0453V16.0105V15.9757V15.9409V15.9061V15.8713V15.8365V15.8017V15.7669V15.732V15.6972V15.6623V15.6274V15.5926V15.5577V15.5228V15.488V15.4531V15.4182V15.3833V15.3484V15.3135V15.2786V15.2437V15.2088V15.1739V15.139V15.1041V15.0692V15.0343V14.9995V14.9646V14.9297V14.8948V14.86V14.8251V14.7902V14.7554V14.7205V14.6857V14.6508V14.616V14.5812V14.5464V14.5116V14.4768V14.442V14.4073V14.3725V14.3378V14.303V14.2683V14.2336V14.1989V14.1643V14.1296V14.0949V14.0603V14.0257V13.9911V13.9565V13.922V13.8874V13.8529V13.8184V13.7839V13.7495V13.715V13.6806V13.6462V13.6118V13.5775V13.5431V13.5088V13.4745V13.4403V13.406V13.3718V13.3376V13.3035V13.2694V13.2353V13.2012V13.1671V13.1331V13.0991V13.0652V13.0313V12.9974V12.9635V12.9297V12.8959V12.8621V12.8284V12.7947V12.761V12.7274V12.6938V12.6602V12.6267V12.5932V12.5598V12.5264V12.493V12.4597V12.4264V12.3931V12.3599V12.3267V12.2936V12.2605V12.2275V12.1945V12.1615V12.1286V12.0957V12.0629V12.0301V11.9974V11.9647V11.932V11.8994V11.8669V11.8344V11.8019V11.7695V11.7372V11.7049V11.6726V11.6404V11.6083V11.5762V11.5441V11.5121V11.4802V11.4483V11.4165V11.3847V11.353V11.3213V11.2897V11.2581V11.2266V11.1952V11.1638V11.1325V11.1012V11.07V11.0389V11.0078V10.9768V10.9458V10.915V10.8841V10.8534V10.8226V10.792V10.7614V10.7309V10.7005V10.6701V10.6398V10.6095V10.5794V10.5493V10.5192V10.4892V10.4593V10.4295V10.3997V10.3701V10.3404V10.3109V10.2814V10.252V10.2227V10.1934V10.1642V10.1351V10.1061V10.0772V10.0483V10.0195V9.99076V9.96211V9.93354V9.90506V9.87665V9.84832V9.82007V9.7919V9.76382V9.73581V9.70789V9.68005V9.65229V9.62462V9.59703V9.56953V9.54211V9.51477V9.48753V9.46037V9.43329V9.40631V9.37941V9.3526V9.32588V9.29925V9.27271V9.24626V9.2199V9.19364V9.16746V9.14138V9.11539V9.08949V9.06369V9.03798V9.01237V8.98685V8.96143V8.9361V8.91088V8.88575V8.86071V8.83578V8.81094V8.7862V8.76157V8.73703V8.7126V8.68826V8.66403V8.6399V8.61587V8.59195V8.56813V8.54441V8.5208C4.75 7.73065 4.98088 6.56212 5.69551 5.56957C6.43768 4.53878 7.66804 3.75 9.5208 3.75H9.53967H9.55861H9.5776H9.59665H9.61576H9.63493H9.65415H9.67343H9.69277H9.71216H9.73161H9.75112H9.77068H9.7903H9.80997H9.82969H9.84947H9.86931H9.8892H9.90914H9.92914H9.94919H9.96929H9.98944H10.0097H10.0299H10.0502H10.0706H10.091H10.1115H10.132H10.1526H10.1732H10.1938H10.2146H10.2353H10.2561H10.277H10.2979H10.3189H10.3399H10.361H10.3821H10.4032H10.4244H10.4457H10.467H10.4883H10.5097H10.5311H10.5526H10.5741H10.5957H10.6173H10.639H10.6607H10.6824H10.7042H10.726H10.7479H10.7698H10.7918H10.8138H10.8359H10.8579H10.8801H10.9023H10.9245H10.9467H10.969H10.9914H11.0137H11.0361H11.0586H11.0811H11.1036H11.1262H11.1488H11.1715H11.1942H11.2169H11.2396H11.2624H11.2853H11.3082H11.3311H11.354H11.377H11.4H11.4231H11.4462H11.4693H11.4924H11.5156H11.5389H11.5621H11.5854H11.6088H11.6321H11.6555H11.6789H11.7024H11.7259H11.7494H11.773H11.7965H11.8202H11.8438H11.8675H11.8912H11.9149H11.9387H11.9625H11.9863H12.0102H12.0341H12.058H12.0819H12.1059H12.1299H12.1539H12.1779H12.202H12.2261H12.2502H12.2744H12.2986H12.3228H12.347H12.3713H12.3956H12.4199H12.4442H12.4685H12.4929H12.5173H12.5417H12.5662H12.5907H12.6151H12.6397H12.6642H12.6888H12.7133H12.7379H12.7625H12.7872H12.8118H12.8365H12.8612H12.8859H12.9107H12.9354H12.9602H12.985H13.0098H13.0347H13.0595H13.0844H13.1093H13.1341H13.1591H13.184H13.2089H13.2339H13.2589H13.2839H13.3089H13.3339H13.359H13.384H13.4091H13.4342H13.4592H13.4844H13.5095H13.5346H13.5598H13.5849H13.6101H13.6353H13.6605H13.6857H13.7109H13.7361H13.7613H13.7866H13.8118H13.8371H13.8624H13.8876H13.9129H13.9382H13.9635H13.9889H14.0142H14.0395H14.0648H14.0902H14.1155H14.1409H14.1663H14.1916H14.217H14.2424H14.2678H14.2932H14.3186H14.3439H14.3694H14.3948H14.4202H14.4456H14.471H14.4964H14.5218H14.5473H14.5727H14.5981H14.6235H14.649H14.6744H14.6998H14.7253H14.7507H14.7761H14.8015H14.827H14.8524H14.8778H14.9033H14.9287H14.9541H14.9795H15.0049H15.0303H15.0558H15.0812H15.1066H15.132H15.1574H15.1828H15.2081H15.2335H15.2589H15.2843H15.3096H15.335H15.3604H15.3857H15.411H15.4364H15.4617H15.487H15.5123H15.5376H15.5629H15.5882H15.6135H15.6388H15.664H15.6893H15.7145H15.7397H15.7649H15.7902H15.8153H15.8405H15.8657H15.8909H15.916H15.9412H15.9663H15.9914H16.0165H16.0416H16.0667H16.0917H16.1168H16.1418H16.1668H16.1918H16.2168H16.2418H16.2667H16.2917H16.3166H16.3415H16.3664H16.3912H16.4161H16.4409H16.4658H16.4906H16.5153H16.5401H16.5649H16.5896H16.6143H16.639H16.6636H16.6883H16.7129H16.7375H16.7621H16.7867H16.8112H16.8357H16.8602H16.8847H16.9092H16.9336H16.958H16.9824H17.0068H17.0311H17.0554H17.0797H17.104H17.1282H17.1524H17.1766H17.2008H17.2249H17.249H17.2731H17.2972H17.3212H17.3452H17.3692H17.3932H17.4171H17.441H17.4649H17.4887H17.5125H17.5363H17.56H17.5837H17.6074H17.6311H17.6547H17.6783H17.7019H17.7254H17.7489H17.7724H17.7958H17.8192H17.8426H17.866H17.8893H17.9125H17.9358H17.959H17.9822H18.0053H18.0284H18.0515H18.0745H18.0975H18.1205H18.1434H18.1663H18.1891H18.212H18.2347H18.2575H18.2802H18.3029H18.3255H18.3481H18.3706H18.3931H18.4156H18.438H18.4604H18.4828H18.5051H18.5274H18.5496H18.5718H18.5939H18.6161H18.6381H18.6601H18.6821H18.7041H18.726H18.7478H18.7696H18.7914H18.8131H18.8348H18.8564H18.878H18.8996H18.921H18.9425H18.9639H18.9853H19.0066H19.0279H19.0491H19.0702H19.0914H19.1125H19.1335H19.1545H19.1754H19.1963H19.2171H19.2379H19.2586H19.2793H19.3H19.3206H19.3411H19.3616H19.382H19.4024H19.4227H19.443H19.4633H19.4834H19.5036H19.5236H19.5436H19.5636H19.5835H19.6034H19.6232H19.6429H19.6626H19.6823H19.7019H19.7214H19.7409H19.7603H19.7796H19.7989H19.8182H19.8374H19.8565H19.8756H19.8946H19.9136H19.9325H19.9513H19.9701H19.9888H20.0075H20.0261H20.0446H20.0631H20.0815H20.0999H20.1182H20.1364H20.1546H20.1727H20.1908H20.2088H20.2267H20.2446H20.2624H20.2801H20.2978H20.3154H20.333H20.3505H20.3679H20.3853H20.4026H20.4198H20.4369H20.454H20.4711H20.488H20.5049H20.5218H20.5385H20.5552H20.5718H20.5884H20.6049H20.6213H20.6377H20.6539H20.6702H20.6863H20.7024H20.7184H20.7343H20.7502H20.766H20.7817H20.7973H20.8129H20.8284H20.8439H20.8592H20.8745H20.8897H20.9049H20.9199H20.9349H20.9498H20.9647H20.9795H20.9942H21.0088C23.2834 3.75 24.5365 4.75108 25.1748 5.8453C25.4797 6.3679 25.6289 6.88388 25.703 7.26475C25.7403 7.45662 25.7593 7.61856 25.7691 7.73603C25.774 7.79489 25.7767 7.84299 25.778 7.87855C25.7787 7.89634 25.7791 7.91103 25.7793 7.9224L25.7795 7.93696L25.7796 7.94232L25.7796 7.94452L25.7796 7.9455C25.7796 7.94596 25.7796 7.9464 25.0296 7.9464H25.7796V15.4136H24.2796V7.95031L24.2792 7.93684C24.2786 7.92191 24.2773 7.89596 24.2743 7.86059C24.2684 7.78959 24.2561 7.68228 24.2305 7.55105C24.179 7.28572 24.0769 6.94009 23.8791 6.6011C23.5122 5.97212 22.755 5.25 21.0088 5.25H20.9942H20.9795H20.9647H20.9498H20.9349H20.9199H20.9049H20.8897H20.8745H20.8592H20.8439H20.8284H20.8129H20.7973H20.7817H20.766H20.7502H20.7343H20.7184H20.7024H20.6863H20.6702H20.6539H20.6377H20.6213H20.6049H20.5884H20.5718H20.5552H20.5385H20.5218H20.5049H20.488H20.4711H20.454H20.4369H20.4198H20.4026H20.3853H20.3679H20.3505H20.333H20.3154H20.2978H20.2801H20.2624H20.2446H20.2267H20.2088H20.1908H20.1727H20.1546H20.1364H20.1182H20.0999H20.0815H20.0631H20.0446H20.0261H20.0075H19.9888H19.9701H19.9513H19.9325H19.9136H19.8946H19.8756H19.8565H19.8374H19.8182H19.7989H19.7796H19.7603H19.7409H19.7214H19.7019H19.6823H19.6626H19.6429H19.6232H19.6034H19.5835H19.5636H19.5436H19.5236H19.5036H19.4834H19.4633H19.443H19.4227H19.4024H19.382H19.3616H19.3411H19.3206H19.3H19.2793H19.2586H19.2379H19.2171H19.1963H19.1754H19.1545H19.1335H19.1125H19.0914H19.0702H19.0491H19.0279H19.0066H18.9853H18.9639H18.9425H18.921H18.8996H18.878H18.8564H18.8348H18.8131H18.7914H18.7696H18.7478H18.726H18.7041H18.6821H18.6601H18.6381H18.6161H18.5939H18.5718H18.5496H18.5274H18.5051H18.4828H18.4604H18.438H18.4156H18.3931H18.3706H18.3481H18.3255H18.3029H18.2802H18.2575H18.2347H18.212H18.1891H18.1663H18.1434H18.1205H18.0975H18.0745H18.0515H18.0284H18.0053H17.9822H17.959H17.9358H17.9125H17.8893H17.866H17.8426H17.8192H17.7958H17.7724H17.7489H17.7254H17.7019H17.6783H17.6547H17.6311H17.6074H17.5837H17.56H17.5363H17.5125H17.4887H17.4649H17.441H17.4171H17.3932H17.3692H17.3452H17.3212H17.2972H17.2731H17.249H17.2249H17.2008H17.1766H17.1524H17.1282H17.104H17.0797H17.0554H17.0311H17.0068H16.9824H16.958H16.9336H16.9092H16.8847H16.8602H16.8357H16.8112H16.7867H16.7621H16.7375H16.7129H16.6883H16.6636H16.639H16.6143H16.5896H16.5649H16.5401H16.5153H16.4906H16.4658H16.4409H16.4161H16.3912H16.3664H16.3415H16.3166H16.2917H16.2667H16.2418H16.2168H16.1918H16.1668H16.1418H16.1168H16.0917H16.0667H16.0416H16.0165H15.9914H15.9663H15.9412H15.916H15.8909H15.8657H15.8405H15.8153H15.7902H15.7649H15.7397H15.7145H15.6893H15.664H15.6388H15.6135H15.5882H15.5629H15.5376H15.5123H15.487H15.4617H15.4364H15.411H15.3857H15.3604H15.335H15.3096H15.2843H15.2589H15.2335H15.2081H15.1828H15.1574H15.132H15.1066H15.0812H15.0558H15.0303H15.0049H14.9795H14.9541H14.9287H14.9033H14.8778H14.8524H14.827H14.8015H14.7761H14.7507H14.7253H14.6998H14.6744H14.649H14.6235H14.5981H14.5727H14.5473H14.5218H14.4964H14.471H14.4456H14.4202H14.3948H14.3694H14.3439H14.3186H14.2932H14.2678H14.2424H14.217H14.1916H14.1663H14.1409H14.1155H14.0902H14.0648H14.0395H14.0142H13.9889H13.9635H13.9382H13.9129H13.8876H13.8624H13.8371H13.8118H13.7866H13.7613H13.7361H13.7109H13.6857H13.6605H13.6353H13.6101H13.5849H13.5598H13.5346H13.5095H13.4844H13.4592H13.4342H13.4091H13.384H13.359H13.3339H13.3089H13.2839H13.2589H13.2339H13.2089H13.184H13.1591H13.1341H13.1093H13.0844H13.0595H13.0347H13.0098H12.985H12.9602H12.9354H12.9107H12.8859H12.8612H12.8365H12.8118H12.7872H12.7625H12.7379H12.7133H12.6888H12.6642H12.6397H12.6151H12.5907H12.5662H12.5417H12.5173H12.4929H12.4685H12.4442H12.4199H12.3956H12.3713H12.347H12.3228H12.2986H12.2744H12.2502H12.2261H12.202H12.1779H12.1539H12.1299H12.1059H12.0819H12.058H12.0341H12.0102H11.9863H11.9625H11.9387H11.9149H11.8912H11.8675H11.8438H11.8202H11.7965H11.773H11.7494H11.7259H11.7024H11.6789H11.6555H11.6321H11.6088H11.5854H11.5621H11.5389H11.5156H11.4924H11.4693H11.4462H11.4231H11.4H11.377H11.354H11.3311H11.3082H11.2853H11.2624H11.2396H11.2169H11.1942H11.1715H11.1488H11.1262H11.1036H11.0811H11.0586H11.0361H11.0137H10.9914H10.969H10.9467H10.9245H10.9023H10.8801H10.8579H10.8359H10.8138H10.7918H10.7698H10.7479H10.726H10.7042H10.6824H10.6607H10.639H10.6173H10.5957H10.5741H10.5526H10.5311H10.5097H10.4883H10.467H10.4457H10.4244H10.4032H10.3821H10.361H10.3399H10.3189H10.2979H10.277H10.2561H10.2353H10.2146H10.1938H10.1732H10.1526H10.132H10.1115H10.091H10.0706H10.0502H10.0299H10.0097H9.98944H9.96929H9.94919H9.92914H9.90914H9.8892H9.86931H9.84947H9.82969H9.80997H9.7903H9.77068H9.75112H9.73161H9.71216H9.69277H9.67343H9.65415H9.63493H9.61576H9.59665H9.5776H9.55861H9.53967H9.5208C8.15692 5.25 7.37688 5.80148 6.91281 6.44603Z"
      fill="black"
    />
    <path
      d="M20.4445 23.5285C21.1809 23.5285 21.7779 22.9315 21.7779 22.1951C21.7779 21.4588 21.1809 20.8618 20.4445 20.8618C19.7082 20.8618 19.1112 21.4588 19.1112 22.1951C19.1112 22.9315 19.7082 23.5285 20.4445 23.5285Z"
      stroke="#3A3A3A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M16 22.5862V21.804C16 21.3417 16.3778 20.9595 16.8444 20.9595C17.6489 20.9595 17.9778 20.3906 17.5733 19.6928C17.3422 19.2928 17.48 18.7728 17.8844 18.5417L18.6533 18.1017C19.0044 17.8928 19.4578 18.0173 19.6667 18.3684L19.7156 18.4528C20.1156 19.1506 20.7733 19.1506 21.1778 18.4528L21.2267 18.3684C21.4356 18.0173 21.8889 17.8928 22.24 18.1017L23.0089 18.5417C23.4133 18.7728 23.5511 19.2928 23.32 19.6928C22.9156 20.3906 23.2444 20.9595 24.0489 20.9595C24.5111 20.9595 24.8933 21.3373 24.8933 21.804V22.5862C24.8933 23.0484 24.5156 23.4306 24.0489 23.4306C23.2444 23.4306 22.9156 23.9995 23.32 24.6973C23.5511 25.1017 23.4133 25.6173 23.0089 25.8484L22.24 26.2884C21.8889 26.4973 21.4356 26.3728 21.2267 26.0217L21.1778 25.9373C20.7778 25.2395 20.12 25.2395 19.7156 25.9373L19.6667 26.0217C19.4578 26.3728 19.0044 26.4973 18.6533 26.2884L17.8844 25.8484C17.48 25.6173 17.3422 25.0973 17.5733 24.6973C17.9778 23.9995 17.6489 23.4306 16.8444 23.4306C16.3778 23.4306 16 23.0484 16 22.5862Z"
      stroke="#3A3A3A"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const IconBlobList = () => (
  <svg
    width="20"
    height="26"
    viewBox="0 0 20 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.3308 1H2.99738C2.37855 1 1.78506 1.25286 1.34748 1.70295C0.909903 2.15303 0.664062 2.76348 0.664062 3.4V22.6C0.664062 23.2365 0.909903 23.8469 1.34748 24.297C1.78506 24.7472 2.37855 25 2.99738 25H16.9974C17.6162 25 18.2097 24.7472 18.6473 24.297C19.0848 23.8469 19.3308 23.2365 19.3308 22.6V8.2L12.3308 1Z"
      stroke="black"
    />
    <path
      d="M8.63446 16.5696C8.47576 16.1711 8.24566 15.8643 7.94306 15.6523C7.64016 15.4401 7.27936 15.335 6.86286 15.335C6.44636 15.335 6.08556 15.4401 5.78266 15.6523C5.48176 15.8642 5.25166 16.1711 5.09136 16.5694C4.93266 16.9663 4.85326 17.4453 4.85156 18.0049C4.85156 18.5645 4.93006 19.046 5.08876 19.4477C5.24906 19.8477 5.47906 20.1571 5.77976 20.3724C6.08266 20.5863 6.44466 20.6924 6.86286 20.6924C7.28096 20.6924 7.64196 20.5865 7.94326 20.3725C8.24576 20.1588 8.47576 19.8504 8.63446 19.4503C8.79486 19.0485 8.87416 18.5663 8.87416 18.005C8.87416 17.447 8.79496 16.9682 8.63446 16.5696ZM8.58796 16.5881C8.74546 16.9792 8.82416 17.4515 8.82416 18.005C8.82416 18.5618 8.74546 19.0374 8.58796 19.4319L5.13776 16.5881C5.29516 16.197 5.51966 15.8987 5.81136 15.6932C6.10466 15.4877 6.45516 15.385 6.86286 15.385C7.27056 15.385 7.62106 15.4877 7.91436 15.6932C8.20766 15.8987 8.43226 16.197 8.58796 16.5881ZM6.11216 16.6254C6.29606 16.3105 6.54636 16.1531 6.86286 16.1531C7.07336 16.1531 7.25566 16.2235 7.40976 16.3644C7.56386 16.5036 7.68316 16.7107 7.76766 16.9858C7.85216 17.2609 7.89446 17.6007 7.89446 18.005C7.89446 18.6165 7.80166 19.0797 7.61606 19.3946C7.43206 19.7078 7.18106 19.8644 6.86286 19.8644C6.65076 19.8644 6.46766 19.7948 6.31346 19.6556L6.11216 16.6254ZM6.11216 16.6254C5.92816 16.9386 5.83536 17.3985 5.83376 18.005L6.11216 16.6254ZM7.10176 7.45456V7.40456H7.05176H6.17426H6.15966L6.14726 7.41246L4.88196 8.22536L4.85906 8.24006V8.26736V9.11256V9.20386L4.93596 9.15476L6.07956 8.42416V12.5455V12.5955H6.12956H7.05176H7.10176V12.5455V7.45456ZM7.57296 19.3692C7.39646 19.6697 7.16046 19.8144 6.86286 19.8144C6.66286 19.8144 6.49176 19.7492 6.34706 19.6186C6.20176 19.4857 6.08626 19.2859 6.00336 19.0147C5.92246 18.7442 5.88206 18.4081 5.88376 18.0052C5.88536 17.4022 5.97786 16.9528 6.15526 16.6507C6.33186 16.3484 6.56706 16.2031 6.86286 16.2031C7.06096 16.2031 7.23116 16.2689 7.37596 16.4013C7.52126 16.5326 7.63706 16.7309 7.71986 17.0005C7.80246 17.2694 7.84446 17.6038 7.84446 18.005C7.84446 18.6129 7.75196 19.0656 7.57296 19.3692ZM11.7185 12.3724C12.0214 12.5863 12.3835 12.6924 12.8016 12.6924C13.2198 12.6924 13.5808 12.5865 13.8821 12.3725C14.1845 12.1588 14.4145 11.8505 14.5732 11.4504C14.7336 11.0486 14.8129 10.5663 14.8129 10.005C14.8129 9.44706 14.7336 8.96806 14.5732 8.56946C14.4144 8.17096 14.1844 7.86426 13.8818 7.65226C13.5789 7.44006 13.2181 7.33496 12.8016 7.33496C12.3852 7.33496 12.0243 7.44006 11.7214 7.65226C11.4205 7.86416 11.1905 8.17106 11.0301 8.56936C10.8713 8.96626 10.792 9.44526 10.7903 10.0049C10.7903 10.5645 10.8688 11.046 11.0275 11.4477C11.1878 11.8477 11.4178 12.1571 11.7185 12.3724ZM13.5117 11.3692C13.3352 11.6697 13.0993 11.8144 12.8016 11.8144C12.6016 11.8144 12.4306 11.7492 12.2859 11.6186C12.1405 11.4857 12.025 11.2859 11.9422 11.0147C11.8612 10.7442 11.8209 10.4081 11.8225 10.0052C11.8241 9.40216 11.9166 8.95276 12.094 8.65066C12.2706 8.34836 12.5059 8.20306 12.8016 8.20306C12.9997 8.20306 13.1699 8.26886 13.3148 8.40126C13.4601 8.53256 13.5758 8.73086 13.6587 9.00046C13.7412 9.26936 13.7832 9.60376 13.7832 10.005C13.7832 10.6129 13.6907 11.0656 13.5117 11.3692ZM13.5405 15.4546V15.4046H13.4905H12.6131H12.5984L12.586 15.4125L11.3208 16.2254L11.2978 16.2401V16.2674V17.1126V17.2039L11.3747 17.1548L12.5183 16.4242V20.5455V20.5955H12.5683H13.4905H13.5405V20.5455V15.4546Z"
      fill="black"
    />
    <path
      d="M8.58796 16.5881L8.63446 16.5696M8.58796 16.5881C8.74546 16.9792 8.82416 17.4515 8.82416 18.005C8.82416 18.5618 8.74546 19.0374 8.58796 19.4319L5.13776 16.5881C5.29516 16.197 5.51966 15.8987 5.81136 15.6932C6.10466 15.4877 6.45516 15.385 6.86286 15.385C7.27056 15.385 7.62106 15.4877 7.91436 15.6932C8.20766 15.8987 8.43226 16.197 8.58796 16.5881ZM8.63446 16.5696C8.47576 16.1711 8.24566 15.8643 7.94306 15.6523C7.64016 15.4401 7.27936 15.335 6.86286 15.335C6.44636 15.335 6.08556 15.4401 5.78266 15.6523C5.48176 15.8642 5.25166 16.1711 5.09136 16.5694C4.93266 16.9663 4.85326 17.4453 4.85156 18.0049C4.85156 18.5645 4.93006 19.046 5.08876 19.4477C5.24906 19.8477 5.47906 20.1571 5.77976 20.3724C6.08266 20.5863 6.44466 20.6924 6.86286 20.6924C7.28096 20.6924 7.64196 20.5865 7.94326 20.3725C8.24576 20.1588 8.47576 19.8504 8.63446 19.4503C8.79486 19.0485 8.87416 18.5663 8.87416 18.005C8.87416 17.447 8.79496 16.9682 8.63446 16.5696ZM6.11216 16.6254C6.29606 16.3105 6.54636 16.1531 6.86286 16.1531C7.07336 16.1531 7.25566 16.2235 7.40976 16.3644C7.56386 16.5036 7.68316 16.7107 7.76766 16.9858C7.85216 17.2609 7.89446 17.6007 7.89446 18.005C7.89446 18.6165 7.80166 19.0797 7.61606 19.3946C7.43206 19.7078 7.18106 19.8644 6.86286 19.8644C6.65076 19.8644 6.46766 19.7948 6.31346 19.6556L6.11216 16.6254ZM6.11216 16.6254C5.92816 16.9386 5.83536 17.3985 5.83376 18.005L6.11216 16.6254ZM7.10176 7.45456V7.40456H7.05176H6.17426H6.15966L6.14726 7.41246L4.88196 8.22536L4.85906 8.24006V8.26736V9.11256V9.20386L4.93596 9.15476L6.07956 8.42416V12.5455V12.5955H6.12956H7.05176H7.10176V12.5455V7.45456ZM7.57296 19.3692C7.39646 19.6697 7.16046 19.8144 6.86286 19.8144C6.66286 19.8144 6.49176 19.7492 6.34706 19.6186C6.20176 19.4857 6.08626 19.2859 6.00336 19.0147C5.92246 18.7442 5.88206 18.4081 5.88376 18.0052C5.88536 17.4022 5.97786 16.9528 6.15526 16.6507C6.33186 16.3484 6.56706 16.2031 6.86286 16.2031C7.06096 16.2031 7.23116 16.2689 7.37596 16.4013C7.52126 16.5326 7.63706 16.7309 7.71986 17.0005C7.80246 17.2694 7.84446 17.6038 7.84446 18.005C7.84446 18.6129 7.75196 19.0656 7.57296 19.3692ZM11.7185 12.3724C12.0214 12.5863 12.3835 12.6924 12.8016 12.6924C13.2198 12.6924 13.5808 12.5865 13.8821 12.3725C14.1845 12.1588 14.4145 11.8505 14.5732 11.4504C14.7336 11.0486 14.8129 10.5663 14.8129 10.005C14.8129 9.44706 14.7336 8.96806 14.5732 8.56946C14.4144 8.17096 14.1844 7.86426 13.8818 7.65226C13.5789 7.44006 13.2181 7.33496 12.8016 7.33496C12.3852 7.33496 12.0243 7.44006 11.7214 7.65226C11.4205 7.86416 11.1905 8.17106 11.0301 8.56936C10.8713 8.96626 10.792 9.44526 10.7903 10.0049C10.7903 10.5645 10.8688 11.046 11.0275 11.4477C11.1878 11.8477 11.4178 12.1571 11.7185 12.3724ZM13.5117 11.3692C13.3352 11.6697 13.0993 11.8144 12.8016 11.8144C12.6016 11.8144 12.4306 11.7492 12.2859 11.6186C12.1405 11.4857 12.025 11.2859 11.9422 11.0147C11.8612 10.7442 11.8209 10.4081 11.8225 10.0052C11.8241 9.40216 11.9166 8.95276 12.094 8.65066C12.2706 8.34836 12.5059 8.20306 12.8016 8.20306C12.9997 8.20306 13.1699 8.26886 13.3148 8.40126C13.4601 8.53256 13.5758 8.73086 13.6587 9.00046C13.7412 9.26936 13.7832 9.60376 13.7832 10.005C13.7832 10.6129 13.6907 11.0656 13.5117 11.3692ZM13.5405 15.4546V15.4046H13.4905H12.6131H12.5984L12.586 15.4125L11.3208 16.2254L11.2978 16.2401V16.2674V17.1126V17.2039L11.3747 17.1548L12.5183 16.4242V20.5455V20.5955H12.5683H13.4905H13.5405V20.5455V15.4546Z"
      stroke="black"
      stroke-width="0.1"
    />
  </svg>
);
export const SimCardDownloadIconSvg = () => (
  <svg
    className="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-18mw38c-MuiSvgIcon-root"
    focusable="false"
    aria-hidden="true"
    viewBox="0 0 24 24"
    data-testid="SimCardDownloadIcon"
  >
    <path
      d="M18 2h-8L4 8v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-6 15-4-4h3V9.02L13 9v4h3l-4 4z"
      fill="grey"
    ></path>
  </svg>
);
export const IconDirectionArrowSvg = () => (
  <svg
    width="7"
    height="7"
    viewBox="0 0 7 7"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="7" height="1" fill="#979797" />
    <rect
      x="7"
      width="7"
      height="1"
      transform="rotate(90 7 0)"
      fill="#979797"
    />
  </svg>
);
export const IconPlusWhite = () => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="4.16406" width="1.66667" height="10" fill="white" />
    <rect
      x="10"
      y="4.16699"
      width="1.66667"
      height="10"
      transform="rotate(90 10 4.16699)"
      fill="white"
    />
  </svg>
);
export const IconMinusWhite = () => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M10 5.83366V4.16699L0 4.16699V5.83366L10 5.83366Z" fill="white" />
  </svg>
);

export const IconConnector = () => (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="5" cy="5" r="4.5" stroke="#F48400" />
    <circle cx="5" cy="5" r="2.5" fill="#F48400" />
  </svg>
);

export const IconFalsePositive = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.1457 21.6039C8.79529 21.6067 6.51665 20.7816 4.69821 19.2693C2.87977 17.7569 1.63409 15.6508 1.1735 13.3101C0.712918 10.9694 1.06594 8.53893 2.17239 6.43297C3.27884 4.327 5.07023 2.67592 7.2412 1.76116C9.41218 0.846397 11.8283 0.724584 14.0779 1.41648C16.3274 2.10838 18.271 3.57116 19.5774 5.55548C20.8838 7.5398 21.4721 9.92283 21.242 12.2984C21.012 14.6739 19.9778 16.8949 18.3158 18.5827C16.4147 20.5153 13.8356 21.602 11.1457 21.6039ZM14.5835 9.13299C14.6713 9.04654 14.7413 8.9432 14.7895 8.82901C14.8376 8.71481 14.8628 8.59203 14.8638 8.46783C14.8647 8.34362 14.8414 8.22046 14.795 8.10551C14.7486 7.99057 14.6802 7.88614 14.5938 7.7983C14.5073 7.71046 14.4045 7.64097 14.2913 7.59387C14.1781 7.54677 14.0568 7.52301 13.9345 7.52395C13.8122 7.5249 13.6913 7.55055 13.5789 7.5994C13.4664 7.64825 13.3647 7.71933 13.2795 7.80849L11.2813 9.83872L9.14747 7.80849C8.97355 7.63778 8.74055 7.54329 8.49868 7.54538C8.25682 7.54748 8.02544 7.64598 7.85441 7.81968C7.68337 7.99337 7.58638 8.22835 7.58432 8.47398C7.58226 8.71961 7.6753 8.95623 7.8434 9.13286L9.9772 11.1631L7.7079 13.331C7.61986 13.4175 7.54964 13.5208 7.50134 13.635C7.45304 13.7493 7.42763 13.8722 7.42658 13.9965C7.42553 14.1208 7.44887 14.2441 7.49523 14.3592C7.5416 14.4743 7.61006 14.5789 7.69663 14.6668C7.7832 14.7547 7.88614 14.8242 7.99945 14.8713C8.11276 14.9184 8.23417 14.9422 8.3566 14.9411C8.47903 14.9401 8.60003 14.9143 8.71254 14.8652C8.82504 14.8162 8.92681 14.7449 9.0119 14.6555L11.2813 12.4875L13.2795 14.5178C13.3645 14.6074 13.4663 14.6789 13.5789 14.7281C13.6914 14.7773 13.8125 14.8033 13.9351 14.8044C14.0576 14.8056 14.1791 14.7819 14.2926 14.7348C14.406 14.6877 14.5091 14.6181 14.5957 14.5301C14.6824 14.4421 14.7509 14.3375 14.7973 14.2223C14.8436 14.1071 14.8669 13.9836 14.8658 13.8592C14.8646 13.7347 14.8391 13.6118 14.7906 13.4975C14.7422 13.3831 14.6718 13.2798 14.5835 13.1934L12.5853 11.1631L14.5835 9.13299Z"
      fill="#E84336"
    />
    <circle cx="17.9978" cy="18.0017" r="5.9744" fill="white" />
    <path
      d="M18.1262 23.2094C21.104 23.2094 23.518 20.7954 23.518 17.8176C23.518 14.8398 21.104 12.4258 18.1262 12.4258C15.1484 12.4258 12.7344 14.8398 12.7344 17.8176C12.7344 20.7954 15.1484 23.2094 18.1262 23.2094Z"
      fill="white"
    />
    <path
      d="M17.9387 12.3037H18.0684C18.1279 12.3129 18.187 12.3259 18.2469 12.3321C18.4681 12.3555 18.6919 12.362 18.9098 12.4012C20.4881 12.6831 21.7241 13.5066 22.6135 14.8538C23.1742 15.6962 23.4855 16.6841 23.5108 17.7005C23.5122 17.7503 23.5163 18.1156 23.5081 18.1837C23.4758 18.4619 23.4645 18.7445 23.4094 19.0179C23.1881 20.1753 22.6026 21.2283 21.7411 22.0184C20.4921 23.1741 19.0141 23.6509 17.3378 23.4658C16.416 23.3669 15.5373 23.0181 14.7939 22.4559C13.2896 21.3279 12.5272 19.8034 12.4847 17.9126C12.4796 17.4746 12.5308 17.0377 12.6369 16.613C13.2203 14.1636 15.3627 12.4071 17.7816 12.3309C17.8345 12.3256 17.887 12.3165 17.9387 12.3037ZM17.2046 19.0501C17.0088 18.8517 16.8338 18.6736 16.6583 18.4958C16.383 18.217 16.1078 17.9382 15.8326 17.6592C15.7859 17.6102 15.7265 17.5755 15.6613 17.5592C15.5961 17.5429 15.5276 17.5456 15.4638 17.567C15.3986 17.584 15.3398 17.6205 15.2953 17.6719C15.2507 17.7232 15.2224 17.7869 15.214 17.8548C15.204 17.92 15.2109 17.9867 15.234 18.0484C15.257 18.1101 15.2955 18.1647 15.3455 18.2068C15.8558 18.7222 16.3709 19.2334 16.8712 19.7587C17.0676 19.9648 17.3157 19.963 17.516 19.7577C18.5486 18.6978 19.5911 17.6475 20.6302 16.5937C20.662 16.5641 20.6917 16.5322 20.7191 16.4983C20.7611 16.4446 20.7873 16.38 20.7947 16.3119C20.8021 16.2438 20.7904 16.175 20.761 16.1133C20.7316 16.0516 20.6856 15.9996 20.6284 15.9633C20.5712 15.927 20.505 15.9078 20.4376 15.908C20.3801 15.9087 20.3235 15.922 20.2715 15.947C20.2196 15.9719 20.1736 16.008 20.1366 16.0526C19.1848 17.019 18.2325 17.985 17.2799 18.9508C17.253 18.9823 17.2279 19.0155 17.2048 19.05L17.2046 19.0501Z"
      fill="#74B856"
    />
  </svg>
);
export const IconPencilGray = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.1038 4.66847C16.5318 4.24045 17.1124 4 17.7177 4C18.323 4 18.9035 4.24045 19.3316 4.66847C19.7596 5.09649 20 5.67702 20 6.28232C20 6.88763 19.7596 7.46815 19.3316 7.89617L8.43807 18.7896L4 20L5.21038 15.5619L16.1038 4.66847Z"
      stroke="#797979"
    />
  </svg>
);

export const IconPlusGray = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="6" width="2" height="14" fill="black" />
    <rect
      x="14"
      y="6"
      width="2"
      height="14"
      transform="rotate(90 14 6)"
      fill="black"
    />
  </svg>
);

export const IconDeleteGray = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M1 4.60059H19" stroke="black" />
    <path
      d="M17 4.6V17.2C17 17.6774 16.7893 18.1352 16.4142 18.4728C16.0391 18.8104 15.5304 19 15 19H5C4.46957 19 3.96086 18.8104 3.58579 18.4728C3.21071 18.1352 3 17.6774 3 17.2V4.6M6 4.6V2.8C6 2.32261 6.21071 1.86477 6.58579 1.52721C6.96086 1.18964 7.46957 1 8 1H12C12.5304 1 13.0391 1.18964 13.4142 1.52721C13.7893 1.86477 14 2.32261 14 2.8V4.6"
      stroke="black"
    />
    <path d="M8 9.10059V14.5006" stroke="black" />
    <path d="M12 9.10059V14.5006" stroke="black" />
  </svg>
);

export const IconArrowAngleRight = () => (
  <svg
    width="25"
    height="18"
    viewBox="0 0 25 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <line x1="8.64262" y1="0.868126" x2="17.1279" y2="9.35341" stroke="black" />
    <line x1="17.1348" y1="8.64652" x2="8.64952" y2="17.1318" stroke="black" />
    <line x1="15.6426" y1="0.868126" x2="24.1279" y2="9.35341" stroke="black" />
    <line x1="24.1348" y1="8.64652" x2="15.6495" y2="17.1318" stroke="black" />
  </svg>
);

export const IconStatusNew = () => (
  <svg
    width="22"
    height="25"
    viewBox="0 0 22 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.91281 3.44603C2.4212 4.12881 2.25 4.97068 2.25 5.5208V5.54441V5.56813V5.59195V5.61587V5.6399V5.66403V5.68826V5.7126V5.73703V5.76157V5.7862V5.81094V5.83578V5.86071V5.88575V5.91088V5.9361V5.96143V5.98685V6.01237V6.03798V6.06369V6.08949V6.11539V6.14138V6.16746V6.19364V6.2199V6.24626V6.27271V6.29925V6.32588V6.3526V6.37941V6.40631V6.43329V6.46037V6.48753V6.51477V6.54211V6.56953V6.59703V6.62462V6.65229V6.68005V6.70789V6.73581V6.76382V6.7919V6.82007V6.84832V6.87665V6.90506V6.93354V6.96211V6.99076V7.0195V7.0483V7.0772V7.1061V7.1351V7.1642V7.1934V7.2227V7.252V7.2814V7.3109V7.3404V7.3701V7.3997V7.4295V7.4593V7.4892V7.5192V7.5493V7.5794V7.6095V7.6398V7.6701V7.7005V7.7309V7.7614V7.792V7.8226V7.8534V7.8841V7.915V7.9458V7.9768V8.0078V8.0389V8.07V8.1012V8.1325V8.1638V8.1952V8.2266V8.2581V8.2897V8.3213V8.353V8.3847V8.4165V8.4483V8.4802V8.5121V8.5441V8.5762V8.6083V8.6404V8.6726V8.7049V8.7372V8.7695V8.8019V8.8344V8.8669V8.8994V8.932V8.9647V8.9974V9.0301V9.0629V9.0957V9.1286V9.1615V9.1945V9.2275V9.2605V9.2936V9.3267V9.3599V9.3931V9.4264V9.4597V9.493V9.5264V9.5598V9.5932V9.6267V9.6602V9.6938V9.7274V9.761V9.7947V9.8284V9.8621V9.8959V9.9297V9.9635V9.9974V10.0313V10.0652V10.0991V10.1331V10.1671V10.2012V10.2353V10.2694V10.3035V10.3376V10.3718V10.406V10.4403V10.4745V10.5088V10.5431V10.5775V10.6118V10.6462V10.6806V10.715V10.7495V10.7839V10.8184V10.8529V10.8874V10.922V10.9565V10.9911V11.0257V11.0603V11.0949V11.1296V11.1643V11.1989V11.2336V11.2683V11.303V11.3378V11.3725V11.4073V11.442V11.4768V11.5116V11.5464V11.5812V11.616V11.6508V11.6857V11.7205V11.7554V11.7902V11.8251V11.86V11.8948V11.9297V11.9646V11.9995V12.0343V12.0692V12.1041V12.139V12.1739V12.2088V12.2437V12.2786V12.3135V12.3484V12.3833V12.4182V12.4531V12.488V12.5228V12.5577V12.5926V12.6274V12.6623V12.6972V12.732V12.7669V12.8017V12.8365V12.8713V12.9061V12.9409V12.9757V13.0105V13.0453V13.08V13.1148V13.1495V13.1842V13.2189V13.2536V13.2883V13.323V13.3576V13.3923V13.4269V13.4615V13.496V13.5306V13.5652V13.5997V13.6342V13.6687V13.7031V13.7376V13.772V13.8064V13.8408V13.8752V13.9095V13.9438V13.9781V14.0124V14.0466V14.0808V14.115V14.1492V14.1833V14.2174V14.2515V14.2855V14.3195V14.3535V14.3875V14.4214V14.4553V14.4892V14.523V14.5568V14.5906V14.6243V14.658V14.6917V14.7254V14.759V14.7925V14.826V14.8595V14.893V14.9264V14.9598V14.9931V15.0264V15.0597V15.0929V15.1261V15.1592V15.1923V15.2254V15.2584V15.2913V15.3243V15.3571V15.39V15.4228V15.4555V15.4882V15.5209V15.5535V15.586V15.6185V15.651V15.6834V15.7158V15.7481V15.7803V15.8125V15.8447V15.8768V15.9089V15.9409V15.9728V16.0047V16.0366V16.0683V16.1001V16.1317V16.1634V16.1949V16.2264V16.2579V16.2893V16.3206V16.3519V16.3831V16.4142V16.4453V16.4763V16.5073V16.5382V16.5691V16.5998V16.6305V16.6612V16.6918V16.7223V16.7528V16.7831V16.8135V16.8437V16.8739V16.904V16.9341V16.9641V16.994V17.0238V17.0536V17.0833V17.1129V17.1425V17.172V17.2014V17.2307V17.26V17.2892V17.3183V17.3473V17.3763V17.4052V17.434V17.4628V17.4914V17.52V17.5485V17.5769V17.6053V17.6335V17.6617V17.6898V17.7178V17.7457V17.7736V17.8014V17.8291V17.8567V17.8842V17.9116V17.939V17.9662V17.9934V18.0205V18.0475V18.0744V18.1012V18.1279V18.1546V18.1811V18.2076V18.234V18.2603V18.2865V18.3125V18.3386V18.3645V18.3903V18.416V18.4416V18.4672V18.4926V18.5179V18.5432V18.5683V18.5934V18.6183V18.6432V18.6679V18.6926V18.7171V18.7416V18.7659V18.7902V18.8143V18.8384V18.8623V18.8862V18.9099V18.9335V18.957V18.9805V19.0038V19.027V19.0501V19.0731V19.0959V19.1187V19.1414V19.1639V19.1864V19.2087V19.2309V19.2531V19.2751V19.297V19.3187V19.3404V19.3619V19.3834V19.4047V19.4259V19.447V19.468V19.4888V19.5095V19.5302V19.5507V19.571V19.5913V19.6115V19.6315V19.6514V19.6712V19.6908V19.7104V19.7298V19.7491V19.7682V19.7873V19.8062V19.825V19.8437V19.8622V19.8806V19.8989V19.9171V19.9351V19.953V19.9708V19.9884V20.0059V20.0233V20.0406V20.0577V20.0747V20.0916V20.1083V20.1249V20.1413V20.1577V20.1739V20.1899V20.2058V20.2216V20.2373V20.2528V20.2681V20.2834V20.2985V20.3134V20.3282V20.3429V20.3574V20.3718V20.3861V20.4002V20.4141V20.428V20.4417V20.4552C2.25 21.1033 2.34787 21.6155 2.49344 21.9829C2.64114 22.3556 2.81658 22.5305 2.94123 22.5998C3.04043 22.6549 3.17311 22.6826 3.38919 22.5914C3.62888 22.4901 3.96256 22.2401 4.3464 21.7284C5.11012 20.7101 5.93693 20.2125 6.7774 20.2368C7.5796 20.2599 8.1456 20.7535 8.4294 21.1686L10.1416 23.4516L8.9416 24.3516L7.2184 22.054L7.2059 22.0373L7.1944 22.02C7.0907 21.8645 6.915 21.7414 6.7341 21.7362C6.579 21.7317 6.1612 21.8086 5.5464 22.6284C5.06863 23.2654 4.54072 23.7334 3.97266 23.9732C3.38099 24.223 2.75976 24.2149 2.21277 23.911C1.69122 23.6212 1.32816 23.114 1.09891 22.5354C0.86753 21.9515 0.75 21.2431 0.75 20.4552V20.4417V20.428V20.4141V20.4002V20.3861V20.3718V20.3574V20.3429V20.3282V20.3134V20.2985V20.2834V20.2681V20.2528V20.2373V20.2216V20.2058V20.1899V20.1739V20.1577V20.1413V20.1249V20.1083V20.0916V20.0747V20.0577V20.0406V20.0233V20.0059V19.9884V19.9708V19.953V19.9351V19.9171V19.8989V19.8806V19.8622V19.8437V19.825V19.8062V19.7873V19.7682V19.7491V19.7298V19.7104V19.6908V19.6712V19.6514V19.6315V19.6115V19.5913V19.571V19.5507V19.5302V19.5095V19.4888V19.468V19.447V19.4259V19.4047V19.3834V19.3619V19.3404V19.3187V19.297V19.2751V19.2531V19.2309V19.2087V19.1864V19.1639V19.1414V19.1187V19.0959V19.0731V19.0501V19.027V19.0038V18.9805V18.957V18.9335V18.9099V18.8862V18.8623V18.8384V18.8143V18.7902V18.7659V18.7416V18.7171V18.6926V18.6679V18.6432V18.6183V18.5934V18.5683V18.5432V18.5179V18.4926V18.4672V18.4416V18.416V18.3903V18.3645V18.3386V18.3125V18.2865V18.2603V18.234V18.2076V18.1811V18.1546V18.1279V18.1012V18.0744V18.0475V18.0205V17.9934V17.9662V17.939V17.9116V17.8842V17.8567V17.8291V17.8014V17.7736V17.7457V17.7178V17.6898V17.6617V17.6335V17.6053V17.5769V17.5485V17.52V17.4914V17.4628V17.434V17.4052V17.3763V17.3473V17.3183V17.2892V17.26V17.2307V17.2014V17.172V17.1425V17.1129V17.0833V17.0536V17.0238V16.994V16.9641V16.9341V16.904V16.8739V16.8437V16.8135V16.7831V16.7528V16.7223V16.6918V16.6612V16.6305V16.5998V16.5691V16.5382V16.5073V16.4763V16.4453V16.4142V16.3831V16.3519V16.3206V16.2893V16.2579V16.2264V16.1949V16.1634V16.1317V16.1001V16.0683V16.0366V16.0047V15.9728V15.9409V15.9089V15.8768V15.8447V15.8125V15.7803V15.7481V15.7158V15.6834V15.651V15.6185V15.586V15.5535V15.5209V15.4882V15.4555V15.4228V15.39V15.3571V15.3243V15.2913V15.2584V15.2254V15.1923V15.1592V15.1261V15.0929V15.0597V15.0264V14.9931V14.9598V14.9264V14.893V14.8595V14.826V14.7925V14.759V14.7254V14.6917V14.658V14.6243V14.5906V14.5568V14.523V14.4892V14.4553V14.4214V14.3875V14.3535V14.3195V14.2855V14.2515V14.2174V14.1833V14.1492V14.115V14.0808V14.0466V14.0124V13.9781V13.9438V13.9095V13.8752V13.8408V13.8064V13.772V13.7376V13.7031V13.6687V13.6342V13.5997V13.5652V13.5306V13.496V13.4615V13.4269V13.3923V13.3576V13.323V13.2883V13.2536V13.2189V13.1842V13.1495V13.1148V13.08V13.0453V13.0105V12.9757V12.9409V12.9061V12.8713V12.8365V12.8017V12.7669V12.732V12.6972V12.6623V12.6274V12.5926V12.5577V12.5228V12.488V12.4531V12.4182V12.3833V12.3484V12.3135V12.2786V12.2437V12.2088V12.1739V12.139V12.1041V12.0692V12.0343V11.9995V11.9646V11.9297V11.8948V11.86V11.8251V11.7902V11.7554V11.7205V11.6857V11.6508V11.616V11.5812V11.5464V11.5116V11.4768V11.442V11.4073V11.3725V11.3378V11.303V11.2683V11.2336V11.1989V11.1643V11.1296V11.0949V11.0603V11.0257V10.9911V10.9565V10.922V10.8874V10.8529V10.8184V10.7839V10.7495V10.715V10.6806V10.6462V10.6118V10.5775V10.5431V10.5088V10.4745V10.4403V10.406V10.3718V10.3376V10.3035V10.2694V10.2353V10.2012V10.1671V10.1331V10.0991V10.0652V10.0313V9.9974V9.9635V9.9297V9.8959V9.8621V9.8284V9.7947V9.761V9.7274V9.6938V9.6602V9.6267V9.5932V9.5598V9.5264V9.493V9.4597V9.4264V9.3931V9.3599V9.3267V9.2936V9.2605V9.2275V9.1945V9.1615V9.1286V9.0957V9.0629V9.0301V8.9974V8.9647V8.932V8.8994V8.8669V8.8344V8.8019V8.7695V8.7372V8.7049V8.6726V8.6404V8.6083V8.5762V8.5441V8.5121V8.4802V8.4483V8.4165V8.3847V8.353V8.3213V8.2897V8.2581V8.2266V8.1952V8.1638V8.1325V8.1012V8.07V8.0389V8.0078V7.9768V7.9458V7.915V7.8841V7.8534V7.8226V7.792V7.7614V7.7309V7.7005V7.6701V7.6398V7.6095V7.5794V7.5493V7.5192V7.4892V7.4593V7.4295V7.3997V7.3701V7.3404V7.3109V7.2814V7.252V7.2227V7.1934V7.1642V7.1351V7.1061V7.0772V7.0483V7.0195V6.99076V6.96211V6.93354V6.90506V6.87665V6.84832V6.82007V6.7919V6.76382V6.73581V6.70789V6.68005V6.65229V6.62462V6.59703V6.56953V6.54211V6.51477V6.48753V6.46037V6.43329V6.40631V6.37941V6.3526V6.32588V6.29925V6.27271V6.24626V6.2199V6.19364V6.16746V6.14138V6.11539V6.08949V6.06369V6.03798V6.01237V5.98685V5.96143V5.9361V5.91088V5.88575V5.86071V5.83578V5.81094V5.7862V5.76157V5.73703V5.7126V5.68826V5.66403V5.6399V5.61587V5.59195V5.56813V5.54441V5.5208C0.75 4.73065 0.98088 3.56212 1.69551 2.56957C2.43768 1.53878 3.66804 0.75 5.5208 0.75H5.53967H5.55861H5.5776H5.59665H5.61576H5.63493H5.65415H5.67343H5.69277H5.71216H5.73161H5.75112H5.77068H5.7903H5.80997H5.82969H5.84947H5.86931H5.8892H5.90914H5.92914H5.94919H5.96929H5.98944H6.0097H6.0299H6.0502H6.0706H6.091H6.1115H6.132H6.1526H6.1732H6.1938H6.2146H6.2353H6.2561H6.277H6.2979H6.3189H6.3399H6.361H6.3821H6.4032H6.4244H6.4457H6.467H6.4883H6.5097H6.5311H6.5526H6.5741H6.5957H6.6173H6.639H6.6607H6.6824H6.7042H6.726H6.7479H6.7698H6.7918H6.8138H6.8359H6.8579H6.8801H6.9023H6.9245H6.9467H6.969H6.9914H7.0137H7.0361H7.0586H7.0811H7.1036H7.1262H7.1488H7.1715H7.1942H7.2169H7.2396H7.2624H7.2853H7.3082H7.3311H7.354H7.377H7.4H7.4231H7.4462H7.4693H7.4924H7.5156H7.5389H7.5621H7.5854H7.6088H7.6321H7.6555H7.6789H7.7024H7.7259H7.7494H7.773H7.7965H7.8202H7.8438H7.8675H7.8912H7.9149H7.9387H7.9625H7.9863H8.0102H8.0341H8.058H8.0819H8.1059H8.1299H8.1539H8.1779H8.202H8.2261H8.2502H8.2744H8.2986H8.3228H8.347H8.3713H8.3956H8.4199H8.4442H8.4685H8.4929H8.5173H8.5417H8.5662H8.5907H8.6151H8.6397H8.6642H8.6888H8.7133H8.7379H8.7625H8.7872H8.8118H8.8365H8.8612H8.8859H8.9107H8.9354H8.9602H8.985H9.0098H9.0347H9.0595H9.0844H9.1093H9.1341H9.1591H9.184H9.2089H9.2339H9.2589H9.2839H9.3089H9.3339H9.359H9.384H9.4091H9.4342H9.4592H9.4844H9.5095H9.5346H9.5598H9.5849H9.6101H9.6353H9.6605H9.6857H9.7109H9.7361H9.7613H9.7866H9.8118H9.8371H9.8624H9.8876H9.9129H9.9382H9.9635H9.9889H10.0142H10.0395H10.0648H10.0902H10.1155H10.1409H10.1663H10.1916H10.217H10.2424H10.2678H10.2932H10.3186H10.3439H10.3694H10.3948H10.4202H10.4456H10.471H10.4964H10.5218H10.5473H10.5727H10.5981H10.6235H10.649H10.6744H10.6998H10.7253H10.7507H10.7761H10.8015H10.827H10.8524H10.8778H10.9033H10.9287H10.9541H10.9795H11.0049H11.0303H11.0558H11.0812H11.1066H11.132H11.1574H11.1828H11.2081H11.2335H11.2589H11.2843H11.3096H11.335H11.3604H11.3857H11.411H11.4364H11.4617H11.487H11.5123H11.5376H11.5629H11.5882H11.6135H11.6388H11.664H11.6893H11.7145H11.7397H11.7649H11.7902H11.8153H11.8405H11.8657H11.8909H11.916H11.9412H11.9663H11.9914H12.0165H12.0416H12.0667H12.0917H12.1168H12.1418H12.1668H12.1918H12.2168H12.2418H12.2667H12.2917H12.3166H12.3415H12.3664H12.3912H12.4161H12.4409H12.4658H12.4906H12.5153H12.5401H12.5649H12.5896H12.6143H12.639H12.6636H12.6883H12.7129H12.7375H12.7621H12.7867H12.8112H12.8357H12.8602H12.8847H12.9092H12.9336H12.958H12.9824H13.0068H13.0311H13.0554H13.0797H13.104H13.1282H13.1524H13.1766H13.2008H13.2249H13.249H13.2731H13.2972H13.3212H13.3452H13.3692H13.3932H13.4171H13.441H13.4649H13.4887H13.5125H13.5363H13.56H13.5837H13.6074H13.6311H13.6547H13.6783H13.7019H13.7254H13.7489H13.7724H13.7958H13.8192H13.8426H13.866H13.8893H13.9125H13.9358H13.959H13.9822H14.0053H14.0284H14.0515H14.0745H14.0975H14.1205H14.1434H14.1663H14.1891H14.212H14.2347H14.2575H14.2802H14.3029H14.3255H14.3481H14.3706H14.3931H14.4156H14.438H14.4604H14.4828H14.5051H14.5274H14.5496H14.5718H14.5939H14.6161H14.6381H14.6601H14.6821H14.7041H14.726H14.7478H14.7696H14.7914H14.8131H14.8348H14.8564H14.878H14.8996H14.921H14.9425H14.9639H14.9853H15.0066H15.0279H15.0491H15.0702H15.0914H15.1125H15.1335H15.1545H15.1754H15.1963H15.2171H15.2379H15.2586H15.2793H15.3H15.3206H15.3411H15.3616H15.382H15.4024H15.4227H15.443H15.4633H15.4834H15.5036H15.5236H15.5436H15.5636H15.5835H15.6034H15.6232H15.6429H15.6626H15.6823H15.7019H15.7214H15.7409H15.7603H15.7796H15.7989H15.8182H15.8374H15.8565H15.8756H15.8946H15.9136H15.9325H15.9513H15.9701H15.9888H16.0075H16.0261H16.0446H16.0631H16.0815H16.0999H16.1182H16.1364H16.1546H16.1727H16.1908H16.2088H16.2267H16.2446H16.2624H16.2801H16.2978H16.3154H16.333H16.3505H16.3679H16.3853H16.4026H16.4198H16.4369H16.454H16.4711H16.488H16.5049H16.5218H16.5385H16.5552H16.5718H16.5884H16.6049H16.6213H16.6377H16.6539H16.6702H16.6863H16.7024H16.7184H16.7343H16.7502H16.766H16.7817H16.7973H16.8129H16.8284H16.8439H16.8592H16.8745H16.8897H16.9049H16.9199H16.9349H16.9498H16.9647H16.9795H16.9942H17.0088C19.2834 0.75 20.5365 1.75108 21.1748 2.8453C21.4797 3.3679 21.6289 3.88388 21.703 4.26475C21.7403 4.45662 21.7593 4.61856 21.7691 4.73603C21.774 4.79489 21.7767 4.84299 21.778 4.87855C21.7787 4.89634 21.7791 4.91103 21.7793 4.9224L21.7795 4.93696L21.7796 4.94232V4.94452C21.7796 4.94498 21.7796 4.9464 21.0296 4.9464H21.7796V12.4136H20.2796V4.95031L20.2792 4.93684C20.2786 4.92191 20.2773 4.89596 20.2743 4.86059C20.2684 4.78959 20.2561 4.68228 20.2305 4.55105C20.179 4.28572 20.0769 3.94009 19.8791 3.6011C19.5122 2.97212 18.755 2.25 17.0088 2.25H16.9942H16.9795H16.9647H16.9498H16.9349H16.9199H16.9049H16.8897H16.8745H16.8592H16.8439H16.8284H16.8129H16.7973H16.7817H16.766H16.7502H16.7343H16.7184H16.7024H16.6863H16.6702H16.6539H16.6377H16.6213H16.6049H16.5884H16.5718H16.5552H16.5385H16.5218H16.5049H16.488H16.4711H16.454H16.4369H16.4198H16.4026H16.3853H16.3679H16.3505H16.333H16.3154H16.2978H16.2801H16.2624H16.2446H16.2267H16.2088H16.1908H16.1727H16.1546H16.1364H16.1182H16.0999H16.0815H16.0631H16.0446H16.0261H16.0075H15.9888H15.9701H15.9513H15.9325H15.9136H15.8946H15.8756H15.8565H15.8374H15.8182H15.7989H15.7796H15.7603H15.7409H15.7214H15.7019H15.6823H15.6626H15.6429H15.6232H15.6034H15.5835H15.5636H15.5436H15.5236H15.5036H15.4834H15.4633H15.443H15.4227H15.4024H15.382H15.3616H15.3411H15.3206H15.3H15.2793H15.2586H15.2379H15.2171H15.1963H15.1754H15.1545H15.1335H15.1125H15.0914H15.0702H15.0491H15.0279H15.0066H14.9853H14.9639H14.9425H14.921H14.8996H14.878H14.8564H14.8348H14.8131H14.7914H14.7696H14.7478H14.726H14.7041H14.6821H14.6601H14.6381H14.6161H14.5939H14.5718H14.5496H14.5274H14.5051H14.4828H14.4604H14.438H14.4156H14.3931H14.3706H14.3481H14.3255H14.3029H14.2802H14.2575H14.2347H14.212H14.1891H14.1663H14.1434H14.1205H14.0975H14.0745H14.0515H14.0284H14.0053H13.9822H13.959H13.9358H13.9125H13.8893H13.866H13.8426H13.8192H13.7958H13.7724H13.7489H13.7254H13.7019H13.6783H13.6547H13.6311H13.6074H13.5837H13.56H13.5363H13.5125H13.4887H13.4649H13.441H13.4171H13.3932H13.3692H13.3452H13.3212H13.2972H13.2731H13.249H13.2249H13.2008H13.1766H13.1524H13.1282H13.104H13.0797H13.0554H13.0311H13.0068H12.9824H12.958H12.9336H12.9092H12.8847H12.8602H12.8357H12.8112H12.7867H12.7621H12.7375H12.7129H12.6883H12.6636H12.639H12.6143H12.5896H12.5649H12.5401H12.5153H12.4906H12.4658H12.4409H12.4161H12.3912H12.3664H12.3415H12.3166H12.2917H12.2667H12.2418H12.2168H12.1918H12.1668H12.1418H12.1168H12.0917H12.0667H12.0416H12.0165H11.9914H11.9663H11.9412H11.916H11.8909H11.8657H11.8405H11.8153H11.7902H11.7649H11.7397H11.7145H11.6893H11.664H11.6388H11.6135H11.5882H11.5629H11.5376H11.5123H11.487H11.4617H11.4364H11.411H11.3857H11.3604H11.335H11.3096H11.2843H11.2589H11.2335H11.2081H11.1828H11.1574H11.132H11.1066H11.0812H11.0558H11.0303H11.0049H10.9795H10.9541H10.9287H10.9033H10.8778H10.8524H10.827H10.8015H10.7761H10.7507H10.7253H10.6998H10.6744H10.649H10.6235H10.5981H10.5727H10.5473H10.5218H10.4964H10.471H10.4456H10.4202H10.3948H10.3694H10.3439H10.3186H10.2932H10.2678H10.2424H10.217H10.1916H10.1663H10.1409H10.1155H10.0902H10.0648H10.0395H10.0142H9.9889H9.9635H9.9382H9.9129H9.8876H9.8624H9.8371H9.8118H9.7866H9.7613H9.7361H9.7109H9.6857H9.6605H9.6353H9.6101H9.5849H9.5598H9.5346H9.5095H9.4844H9.4592H9.4342H9.4091H9.384H9.359H9.3339H9.3089H9.2839H9.2589H9.2339H9.2089H9.184H9.1591H9.1341H9.1093H9.0844H9.0595H9.0347H9.0098H8.985H8.9602H8.9354H8.9107H8.8859H8.8612H8.8365H8.8118H8.7872H8.7625H8.7379H8.7133H8.6888H8.6642H8.6397H8.6151H8.5907H8.5662H8.5417H8.5173H8.4929H8.4685H8.4442H8.4199H8.3956H8.3713H8.347H8.3228H8.2986H8.2744H8.2502H8.2261H8.202H8.1779H8.1539H8.1299H8.1059H8.0819H8.058H8.0341H8.0102H7.9863H7.9625H7.9387H7.9149H7.8912H7.8675H7.8438H7.8202H7.7965H7.773H7.7494H7.7259H7.7024H7.6789H7.6555H7.6321H7.6088H7.5854H7.5621H7.5389H7.5156H7.4924H7.4693H7.4462H7.4231H7.4H7.377H7.354H7.3311H7.3082H7.2853H7.2624H7.2396H7.2169H7.1942H7.1715H7.1488H7.1262H7.1036H7.0811H7.0586H7.0361H7.0137H6.9914H6.969H6.9467H6.9245H6.9023H6.8801H6.8579H6.8359H6.8138H6.7918H6.7698H6.7479H6.726H6.7042H6.6824H6.6607H6.639H6.6173H6.5957H6.5741H6.5526H6.5311H6.5097H6.4883H6.467H6.4457H6.4244H6.4032H6.3821H6.361H6.3399H6.3189H6.2979H6.277H6.2561H6.2353H6.2146H6.1938H6.1732H6.1526H6.132H6.1115H6.091H6.0706H6.0502H6.0299H6.0097H5.98944H5.96929H5.94919H5.92914H5.90914H5.8892H5.86931H5.84947H5.82969H5.80997H5.7903H5.77068H5.75112H5.73161H5.71216H5.69277H5.67343H5.65415H5.63493H5.61576H5.59665H5.5776H5.55861H5.53967H5.5208C4.15692 2.25 3.37688 2.80148 2.91281 3.44603ZM11.2348 5.28799L10.827 6.64399H12.7828L13.2772 5L14.2348 5.28799L13.827 6.64399H15V7.644H13.5263L13.2256 8.644H15V9.644H12.9249L12.4788 11.1272L11.5212 10.8392L11.8806 9.644H9.9249L9.4788 11.1272L8.5212 10.8392L8.8806 9.644H8V8.644H9.1813L9.4821 7.644H8V6.64399H9.7828L10.2772 5L11.2348 5.28799ZM12.1813 8.644L12.4821 7.644H10.5263L10.2256 8.644H12.1813ZM16 23.3481C18.5034 23.3481 20.5 21.3494 20.5 18.9241C20.5 16.4987 18.5034 14.5 16 14.5C13.4966 14.5 11.5 16.4987 11.5 18.9241C11.5 21.3494 13.4966 23.3481 16 23.3481ZM16 24.8481C19.3137 24.8481 22 22.1958 22 18.9241C22 15.6523 19.3137 13 16 13C12.6863 13 10 15.6523 10 18.9241C10 22.1958 12.6863 24.8481 16 24.8481ZM16 16L16.6735 18.0729H18.8532L17.0898 19.3541L17.7634 21.4271L16 20.1459L14.2366 21.4271L14.9102 19.3541L13.1468 18.0729H15.3265L16 16Z"
      fill="#196bb4"
    />
  </svg>
);
export const IconStatusInprogress = () => (
  <svg
    width="22"
    height="25"
    viewBox="0 0 22 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.91281 3.44603C2.4212 4.12881 2.25 4.97068 2.25 5.5208V5.54441V5.56813V5.59195V5.61587V5.6399V5.66403V5.68826V5.7126V5.73703V5.76157V5.7862V5.81094V5.83578V5.86071V5.88575V5.91088V5.9361V5.96143V5.98685V6.01237V6.03798V6.06369V6.08949V6.11539V6.14138V6.16746V6.19364V6.2199V6.24626V6.27271V6.29925V6.32588V6.3526V6.37941V6.40631V6.43329V6.46037V6.48753V6.51477V6.54211V6.56953V6.59703V6.62462V6.65229V6.68005V6.70789V6.73581V6.76382V6.7919V6.82007V6.84832V6.87665V6.90506V6.93354V6.96211V6.99076V7.0195V7.0483V7.0772V7.1061V7.1351V7.1642V7.1934V7.2227V7.252V7.2814V7.3109V7.3404V7.3701V7.3997V7.4295V7.4593V7.4892V7.5192V7.5493V7.5794V7.6095V7.6398V7.6701V7.7005V7.7309V7.7614V7.792V7.8226V7.8534V7.8841V7.915V7.9458V7.9768V8.0078V8.0389V8.07V8.1012V8.1325V8.1638V8.1952V8.2266V8.2581V8.2897V8.3213V8.353V8.3847V8.4165V8.4483V8.4802V8.5121V8.5441V8.5762V8.6083V8.6404V8.6726V8.7049V8.7372V8.7695V8.8019V8.8344V8.8669V8.8994V8.932V8.9647V8.9974V9.0301V9.0629V9.0957V9.1286V9.1615V9.1945V9.2275V9.2605V9.2936V9.3267V9.3599V9.3931V9.4264V9.4597V9.493V9.5264V9.5598V9.5932V9.6267V9.6602V9.6938V9.7274V9.761V9.7947V9.8284V9.8621V9.8959V9.9297V9.9635V9.9974V10.0313V10.0652V10.0991V10.1331V10.1671V10.2012V10.2353V10.2694V10.3035V10.3376V10.3718V10.406V10.4403V10.4745V10.5088V10.5431V10.5775V10.6118V10.6462V10.6806V10.715V10.7495V10.7839V10.8184V10.8529V10.8874V10.922V10.9565V10.9911V11.0257V11.0603V11.0949V11.1296V11.1643V11.1989V11.2336V11.2683V11.303V11.3378V11.3725V11.4073V11.442V11.4768V11.5116V11.5464V11.5812V11.616V11.6508V11.6857V11.7205V11.7554V11.7902V11.8251V11.86V11.8948V11.9297V11.9646V11.9995V12.0343V12.0692V12.1041V12.139V12.1739V12.2088V12.2437V12.2786V12.3135V12.3484V12.3833V12.4182V12.4531V12.488V12.5228V12.5577V12.5926V12.6274V12.6623V12.6972V12.732V12.7669V12.8017V12.8365V12.8713V12.9061V12.9409V12.9757V13.0105V13.0453V13.08V13.1148V13.1495V13.1842V13.2189V13.2536V13.2883V13.323V13.3576V13.3923V13.4269V13.4615V13.496V13.5306V13.5652V13.5997V13.6342V13.6687V13.7031V13.7376V13.772V13.8064V13.8408V13.8752V13.9095V13.9438V13.9781V14.0124V14.0466V14.0808V14.115V14.1492V14.1833V14.2174V14.2515V14.2855V14.3195V14.3535V14.3875V14.4214V14.4553V14.4892V14.523V14.5568V14.5906V14.6243V14.658V14.6917V14.7254V14.759V14.7925V14.826V14.8595V14.893V14.9264V14.9598V14.9931V15.0264V15.0597V15.0929V15.1261V15.1592V15.1923V15.2254V15.2584V15.2913V15.3243V15.3571V15.39V15.4228V15.4555V15.4882V15.5209V15.5535V15.586V15.6185V15.651V15.6834V15.7158V15.7481V15.7803V15.8125V15.8447V15.8768V15.9089V15.9409V15.9728V16.0047V16.0366V16.0683V16.1001V16.1317V16.1634V16.1949V16.2264V16.2579V16.2893V16.3206V16.3519V16.3831V16.4142V16.4453V16.4763V16.5073V16.5382V16.5691V16.5998V16.6305V16.6612V16.6918V16.7223V16.7528V16.7831V16.8135V16.8437V16.8739V16.904V16.9341V16.9641V16.994V17.0238V17.0536V17.0833V17.1129V17.1425V17.172V17.2014V17.2307V17.26V17.2892V17.3183V17.3473V17.3763V17.4052V17.434V17.4628V17.4914V17.52V17.5485V17.5769V17.6053V17.6335V17.6617V17.6898V17.7178V17.7457V17.7736V17.8014V17.8291V17.8567V17.8842V17.9116V17.939V17.9662V17.9934V18.0205V18.0475V18.0744V18.1012V18.1279V18.1546V18.1811V18.2076V18.234V18.2603V18.2865V18.3125V18.3386V18.3645V18.3903V18.416V18.4416V18.4672V18.4926V18.5179V18.5432V18.5683V18.5934V18.6183V18.6432V18.6679V18.6926V18.7171V18.7416V18.7659V18.7902V18.8143V18.8384V18.8623V18.8862V18.9099V18.9335V18.957V18.9805V19.0038V19.027V19.0501V19.0731V19.0959V19.1187V19.1414V19.1639V19.1864V19.2087V19.2309V19.2531V19.2751V19.297V19.3187V19.3404V19.3619V19.3834V19.4047V19.4259V19.447V19.468V19.4888V19.5095V19.5302V19.5507V19.571V19.5913V19.6115V19.6315V19.6514V19.6712V19.6908V19.7104V19.7298V19.7491V19.7682V19.7873V19.8062V19.825V19.8437V19.8622V19.8806V19.8989V19.9171V19.9351V19.953V19.9708V19.9884V20.0059V20.0233V20.0406V20.0577V20.0747V20.0916V20.1083V20.1249V20.1413V20.1577V20.1739V20.1899V20.2058V20.2216V20.2373V20.2528V20.2681V20.2834V20.2985V20.3134V20.3282V20.3429V20.3574V20.3718V20.3861V20.4002V20.4141V20.428V20.4417V20.4552C2.25 21.1033 2.34787 21.6155 2.49344 21.9829C2.64114 22.3556 2.81658 22.5305 2.94123 22.5998C3.04043 22.6549 3.17311 22.6826 3.38919 22.5914C3.62888 22.4901 3.96256 22.2401 4.3464 21.7284C5.11012 20.7101 5.93693 20.2125 6.7774 20.2368C7.5796 20.2599 8.1456 20.7535 8.4294 21.1686L10.1416 23.4516L8.9416 24.3516L7.2184 22.054L7.2059 22.0373L7.1944 22.02C7.0907 21.8645 6.915 21.7414 6.7341 21.7362C6.579 21.7317 6.1612 21.8086 5.5464 22.6284C5.06863 23.2654 4.54072 23.7334 3.97266 23.9732C3.38099 24.223 2.75976 24.2149 2.21277 23.911C1.69122 23.6212 1.32816 23.114 1.09891 22.5354C0.86753 21.9515 0.75 21.2431 0.75 20.4552V20.4417V20.428V20.4141V20.4002V20.3861V20.3718V20.3574V20.3429V20.3282V20.3134V20.2985V20.2834V20.2681V20.2528V20.2373V20.2216V20.2058V20.1899V20.1739V20.1577V20.1413V20.1249V20.1083V20.0916V20.0747V20.0577V20.0406V20.0233V20.0059V19.9884V19.9708V19.953V19.9351V19.9171V19.8989V19.8806V19.8622V19.8437V19.825V19.8062V19.7873V19.7682V19.7491V19.7298V19.7104V19.6908V19.6712V19.6514V19.6315V19.6115V19.5913V19.571V19.5507V19.5302V19.5095V19.4888V19.468V19.447V19.4259V19.4047V19.3834V19.3619V19.3404V19.3187V19.297V19.2751V19.2531V19.2309V19.2087V19.1864V19.1639V19.1414V19.1187V19.0959V19.0731V19.0501V19.027V19.0038V18.9805V18.957V18.9335V18.9099V18.8862V18.8623V18.8384V18.8143V18.7902V18.7659V18.7416V18.7171V18.6926V18.6679V18.6432V18.6183V18.5934V18.5683V18.5432V18.5179V18.4926V18.4672V18.4416V18.416V18.3903V18.3645V18.3386V18.3125V18.2865V18.2603V18.234V18.2076V18.1811V18.1546V18.1279V18.1012V18.0744V18.0475V18.0205V17.9934V17.9662V17.939V17.9116V17.8842V17.8567V17.8291V17.8014V17.7736V17.7457V17.7178V17.6898V17.6617V17.6335V17.6053V17.5769V17.5485V17.52V17.4914V17.4628V17.434V17.4052V17.3763V17.3473V17.3183V17.2892V17.26V17.2307V17.2014V17.172V17.1425V17.1129V17.0833V17.0536V17.0238V16.994V16.9641V16.9341V16.904V16.8739V16.8437V16.8135V16.7831V16.7528V16.7223V16.6918V16.6612V16.6305V16.5998V16.5691V16.5382V16.5073V16.4763V16.4453V16.4142V16.3831V16.3519V16.3206V16.2893V16.2579V16.2264V16.1949V16.1634V16.1317V16.1001V16.0683V16.0366V16.0047V15.9728V15.9409V15.9089V15.8768V15.8447V15.8125V15.7803V15.7481V15.7158V15.6834V15.651V15.6185V15.586V15.5535V15.5209V15.4882V15.4555V15.4228V15.39V15.3571V15.3243V15.2913V15.2584V15.2254V15.1923V15.1592V15.1261V15.0929V15.0597V15.0264V14.9931V14.9598V14.9264V14.893V14.8595V14.826V14.7925V14.759V14.7254V14.6917V14.658V14.6243V14.5906V14.5568V14.523V14.4892V14.4553V14.4214V14.3875V14.3535V14.3195V14.2855V14.2515V14.2174V14.1833V14.1492V14.115V14.0808V14.0466V14.0124V13.9781V13.9438V13.9095V13.8752V13.8408V13.8064V13.772V13.7376V13.7031V13.6687V13.6342V13.5997V13.5652V13.5306V13.496V13.4615V13.4269V13.3923V13.3576V13.323V13.2883V13.2536V13.2189V13.1842V13.1495V13.1148V13.08V13.0453V13.0105V12.9757V12.9409V12.9061V12.8713V12.8365V12.8017V12.7669V12.732V12.6972V12.6623V12.6274V12.5926V12.5577V12.5228V12.488V12.4531V12.4182V12.3833V12.3484V12.3135V12.2786V12.2437V12.2088V12.1739V12.139V12.1041V12.0692V12.0343V11.9995V11.9646V11.9297V11.8948V11.86V11.8251V11.7902V11.7554V11.7205V11.6857V11.6508V11.616V11.5812V11.5464V11.5116V11.4768V11.442V11.4073V11.3725V11.3378V11.303V11.2683V11.2336V11.1989V11.1643V11.1296V11.0949V11.0603V11.0257V10.9911V10.9565V10.922V10.8874V10.8529V10.8184V10.7839V10.7495V10.715V10.6806V10.6462V10.6118V10.5775V10.5431V10.5088V10.4745V10.4403V10.406V10.3718V10.3376V10.3035V10.2694V10.2353V10.2012V10.1671V10.1331V10.0991V10.0652V10.0313V9.9974V9.9635V9.9297V9.8959V9.8621V9.8284V9.7947V9.761V9.7274V9.6938V9.6602V9.6267V9.5932V9.5598V9.5264V9.493V9.4597V9.4264V9.3931V9.3599V9.3267V9.2936V9.2605V9.2275V9.1945V9.1615V9.1286V9.0957V9.0629V9.0301V8.9974V8.9647V8.932V8.8994V8.8669V8.8344V8.8019V8.7695V8.7372V8.7049V8.6726V8.6404V8.6083V8.5762V8.5441V8.5121V8.4802V8.4483V8.4165V8.3847V8.353V8.3213V8.2897V8.2581V8.2266V8.1952V8.1638V8.1325V8.1012V8.07V8.0389V8.0078V7.9768V7.9458V7.915V7.8841V7.8534V7.8226V7.792V7.7614V7.7309V7.7005V7.6701V7.6398V7.6095V7.5794V7.5493V7.5192V7.4892V7.4593V7.4295V7.3997V7.3701V7.3404V7.3109V7.2814V7.252V7.2227V7.1934V7.1642V7.1351V7.1061V7.0772V7.0483V7.0195V6.99076V6.96211V6.93354V6.90506V6.87665V6.84832V6.82007V6.7919V6.76382V6.73581V6.70789V6.68005V6.65229V6.62462V6.59703V6.56953V6.54211V6.51477V6.48753V6.46037V6.43329V6.40631V6.37941V6.3526V6.32588V6.29925V6.27271V6.24626V6.2199V6.19364V6.16746V6.14138V6.11539V6.08949V6.06369V6.03798V6.01237V5.98685V5.96143V5.9361V5.91088V5.88575V5.86071V5.83578V5.81094V5.7862V5.76157V5.73703V5.7126V5.68826V5.66403V5.6399V5.61587V5.59195V5.56813V5.54441V5.5208C0.75 4.73065 0.98088 3.56212 1.69551 2.56957C2.43768 1.53878 3.66804 0.75 5.5208 0.75H5.53967H5.55861H5.5776H5.59665H5.61576H5.63493H5.65415H5.67343H5.69277H5.71216H5.73161H5.75112H5.77068H5.7903H5.80997H5.82969H5.84947H5.86931H5.8892H5.90914H5.92914H5.94919H5.96929H5.98944H6.0097H6.0299H6.0502H6.0706H6.091H6.1115H6.132H6.1526H6.1732H6.1938H6.2146H6.2353H6.2561H6.277H6.2979H6.3189H6.3399H6.361H6.3821H6.4032H6.4244H6.4457H6.467H6.4883H6.5097H6.5311H6.5526H6.5741H6.5957H6.6173H6.639H6.6607H6.6824H6.7042H6.726H6.7479H6.7698H6.7918H6.8138H6.8359H6.8579H6.8801H6.9023H6.9245H6.9467H6.969H6.9914H7.0137H7.0361H7.0586H7.0811H7.1036H7.1262H7.1488H7.1715H7.1942H7.2169H7.2396H7.2624H7.2853H7.3082H7.3311H7.354H7.377H7.4H7.4231H7.4462H7.4693H7.4924H7.5156H7.5389H7.5621H7.5854H7.6088H7.6321H7.6555H7.6789H7.7024H7.7259H7.7494H7.773H7.7965H7.8202H7.8438H7.8675H7.8912H7.9149H7.9387H7.9625H7.9863H8.0102H8.0341H8.058H8.0819H8.1059H8.1299H8.1539H8.1779H8.202H8.2261H8.2502H8.2744H8.2986H8.3228H8.347H8.3713H8.3956H8.4199H8.4442H8.4685H8.4929H8.5173H8.5417H8.5662H8.5907H8.6151H8.6397H8.6642H8.6888H8.7133H8.7379H8.7625H8.7872H8.8118H8.8365H8.8612H8.8859H8.9107H8.9354H8.9602H8.985H9.0098H9.0347H9.0595H9.0844H9.1093H9.1341H9.1591H9.184H9.2089H9.2339H9.2589H9.2839H9.3089H9.3339H9.359H9.384H9.4091H9.4342H9.4592H9.4844H9.5095H9.5346H9.5598H9.5849H9.6101H9.6353H9.6605H9.6857H9.7109H9.7361H9.7613H9.7866H9.8118H9.8371H9.8624H9.8876H9.9129H9.9382H9.9635H9.9889H10.0142H10.0395H10.0648H10.0902H10.1155H10.1409H10.1663H10.1916H10.217H10.2424H10.2678H10.2932H10.3186H10.3439H10.3694H10.3948H10.4202H10.4456H10.471H10.4964H10.5218H10.5473H10.5727H10.5981H10.6235H10.649H10.6744H10.6998H10.7253H10.7507H10.7761H10.8015H10.827H10.8524H10.8778H10.9033H10.9287H10.9541H10.9795H11.0049H11.0303H11.0558H11.0812H11.1066H11.132H11.1574H11.1828H11.2081H11.2335H11.2589H11.2843H11.3096H11.335H11.3604H11.3857H11.411H11.4364H11.4617H11.487H11.5123H11.5376H11.5629H11.5882H11.6135H11.6388H11.664H11.6893H11.7145H11.7397H11.7649H11.7902H11.8153H11.8405H11.8657H11.8909H11.916H11.9412H11.9663H11.9914H12.0165H12.0416H12.0667H12.0917H12.1168H12.1418H12.1668H12.1918H12.2168H12.2418H12.2667H12.2917H12.3166H12.3415H12.3664H12.3912H12.4161H12.4409H12.4658H12.4906H12.5153H12.5401H12.5649H12.5896H12.6143H12.639H12.6636H12.6883H12.7129H12.7375H12.7621H12.7867H12.8112H12.8357H12.8602H12.8847H12.9092H12.9336H12.958H12.9824H13.0068H13.0311H13.0554H13.0797H13.104H13.1282H13.1524H13.1766H13.2008H13.2249H13.249H13.2731H13.2972H13.3212H13.3452H13.3692H13.3932H13.4171H13.441H13.4649H13.4887H13.5125H13.5363H13.56H13.5837H13.6074H13.6311H13.6547H13.6783H13.7019H13.7254H13.7489H13.7724H13.7958H13.8192H13.8426H13.866H13.8893H13.9125H13.9358H13.959H13.9822H14.0053H14.0284H14.0515H14.0745H14.0975H14.1205H14.1434H14.1663H14.1891H14.212H14.2347H14.2575H14.2802H14.3029H14.3255H14.3481H14.3706H14.3931H14.4156H14.438H14.4604H14.4828H14.5051H14.5274H14.5496H14.5718H14.5939H14.6161H14.6381H14.6601H14.6821H14.7041H14.726H14.7478H14.7696H14.7914H14.8131H14.8348H14.8564H14.878H14.8996H14.921H14.9425H14.9639H14.9853H15.0066H15.0279H15.0491H15.0702H15.0914H15.1125H15.1335H15.1545H15.1754H15.1963H15.2171H15.2379H15.2586H15.2793H15.3H15.3206H15.3411H15.3616H15.382H15.4024H15.4227H15.443H15.4633H15.4834H15.5036H15.5236H15.5436H15.5636H15.5835H15.6034H15.6232H15.6429H15.6626H15.6823H15.7019H15.7214H15.7409H15.7603H15.7796H15.7989H15.8182H15.8374H15.8565H15.8756H15.8946H15.9136H15.9325H15.9513H15.9701H15.9888H16.0075H16.0261H16.0446H16.0631H16.0815H16.0999H16.1182H16.1364H16.1546H16.1727H16.1908H16.2088H16.2267H16.2446H16.2624H16.2801H16.2978H16.3154H16.333H16.3505H16.3679H16.3853H16.4026H16.4198H16.4369H16.454H16.4711H16.488H16.5049H16.5218H16.5385H16.5552H16.5718H16.5884H16.6049H16.6213H16.6377H16.6539H16.6702H16.6863H16.7024H16.7184H16.7343H16.7502H16.766H16.7817H16.7973H16.8129H16.8284H16.8439H16.8592H16.8745H16.8897H16.9049H16.9199H16.9349H16.9498H16.9647H16.9795H16.9942H17.0088C19.2834 0.75 20.5365 1.75108 21.1748 2.8453C21.4797 3.3679 21.6289 3.88388 21.703 4.26475C21.7403 4.45662 21.7593 4.61856 21.7691 4.73603C21.774 4.79489 21.7767 4.84299 21.778 4.87855C21.7787 4.89634 21.7791 4.91103 21.7793 4.9224L21.7795 4.93696L21.7796 4.94232V4.94452C21.7796 4.94498 21.7796 4.9464 21.0296 4.9464H21.7796V12.4136H20.2796V4.95031L20.2792 4.93684C20.2786 4.92191 20.2773 4.89596 20.2743 4.86059C20.2684 4.78959 20.2561 4.68228 20.2305 4.55105C20.179 4.28572 20.0769 3.94009 19.8791 3.6011C19.5122 2.97212 18.755 2.25 17.0088 2.25H16.9942H16.9795H16.9647H16.9498H16.9349H16.9199H16.9049H16.8897H16.8745H16.8592H16.8439H16.8284H16.8129H16.7973H16.7817H16.766H16.7502H16.7343H16.7184H16.7024H16.6863H16.6702H16.6539H16.6377H16.6213H16.6049H16.5884H16.5718H16.5552H16.5385H16.5218H16.5049H16.488H16.4711H16.454H16.4369H16.4198H16.4026H16.3853H16.3679H16.3505H16.333H16.3154H16.2978H16.2801H16.2624H16.2446H16.2267H16.2088H16.1908H16.1727H16.1546H16.1364H16.1182H16.0999H16.0815H16.0631H16.0446H16.0261H16.0075H15.9888H15.9701H15.9513H15.9325H15.9136H15.8946H15.8756H15.8565H15.8374H15.8182H15.7989H15.7796H15.7603H15.7409H15.7214H15.7019H15.6823H15.6626H15.6429H15.6232H15.6034H15.5835H15.5636H15.5436H15.5236H15.5036H15.4834H15.4633H15.443H15.4227H15.4024H15.382H15.3616H15.3411H15.3206H15.3H15.2793H15.2586H15.2379H15.2171H15.1963H15.1754H15.1545H15.1335H15.1125H15.0914H15.0702H15.0491H15.0279H15.0066H14.9853H14.9639H14.9425H14.921H14.8996H14.878H14.8564H14.8348H14.8131H14.7914H14.7696H14.7478H14.726H14.7041H14.6821H14.6601H14.6381H14.6161H14.5939H14.5718H14.5496H14.5274H14.5051H14.4828H14.4604H14.438H14.4156H14.3931H14.3706H14.3481H14.3255H14.3029H14.2802H14.2575H14.2347H14.212H14.1891H14.1663H14.1434H14.1205H14.0975H14.0745H14.0515H14.0284H14.0053H13.9822H13.959H13.9358H13.9125H13.8893H13.866H13.8426H13.8192H13.7958H13.7724H13.7489H13.7254H13.7019H13.6783H13.6547H13.6311H13.6074H13.5837H13.56H13.5363H13.5125H13.4887H13.4649H13.441H13.4171H13.3932H13.3692H13.3452H13.3212H13.2972H13.2731H13.249H13.2249H13.2008H13.1766H13.1524H13.1282H13.104H13.0797H13.0554H13.0311H13.0068H12.9824H12.958H12.9336H12.9092H12.8847H12.8602H12.8357H12.8112H12.7867H12.7621H12.7375H12.7129H12.6883H12.6636H12.639H12.6143H12.5896H12.5649H12.5401H12.5153H12.4906H12.4658H12.4409H12.4161H12.3912H12.3664H12.3415H12.3166H12.2917H12.2667H12.2418H12.2168H12.1918H12.1668H12.1418H12.1168H12.0917H12.0667H12.0416H12.0165H11.9914H11.9663H11.9412H11.916H11.8909H11.8657H11.8405H11.8153H11.7902H11.7649H11.7397H11.7145H11.6893H11.664H11.6388H11.6135H11.5882H11.5629H11.5376H11.5123H11.487H11.4617H11.4364H11.411H11.3857H11.3604H11.335H11.3096H11.2843H11.2589H11.2335H11.2081H11.1828H11.1574H11.132H11.1066H11.0812H11.0558H11.0303H11.0049H10.9795H10.9541H10.9287H10.9033H10.8778H10.8524H10.827H10.8015H10.7761H10.7507H10.7253H10.6998H10.6744H10.649H10.6235H10.5981H10.5727H10.5473H10.5218H10.4964H10.471H10.4456H10.4202H10.3948H10.3694H10.3439H10.3186H10.2932H10.2678H10.2424H10.217H10.1916H10.1663H10.1409H10.1155H10.0902H10.0648H10.0395H10.0142H9.9889H9.9635H9.9382H9.9129H9.8876H9.8624H9.8371H9.8118H9.7866H9.7613H9.7361H9.7109H9.6857H9.6605H9.6353H9.6101H9.5849H9.5598H9.5346H9.5095H9.4844H9.4592H9.4342H9.4091H9.384H9.359H9.3339H9.3089H9.2839H9.2589H9.2339H9.2089H9.184H9.1591H9.1341H9.1093H9.0844H9.0595H9.0347H9.0098H8.985H8.9602H8.9354H8.9107H8.8859H8.8612H8.8365H8.8118H8.7872H8.7625H8.7379H8.7133H8.6888H8.6642H8.6397H8.6151H8.5907H8.5662H8.5417H8.5173H8.4929H8.4685H8.4442H8.4199H8.3956H8.3713H8.347H8.3228H8.2986H8.2744H8.2502H8.2261H8.202H8.1779H8.1539H8.1299H8.1059H8.0819H8.058H8.0341H8.0102H7.9863H7.9625H7.9387H7.9149H7.8912H7.8675H7.8438H7.8202H7.7965H7.773H7.7494H7.7259H7.7024H7.6789H7.6555H7.6321H7.6088H7.5854H7.5621H7.5389H7.5156H7.4924H7.4693H7.4462H7.4231H7.4H7.377H7.354H7.3311H7.3082H7.2853H7.2624H7.2396H7.2169H7.1942H7.1715H7.1488H7.1262H7.1036H7.0811H7.0586H7.0361H7.0137H6.9914H6.969H6.9467H6.9245H6.9023H6.8801H6.8579H6.8359H6.8138H6.7918H6.7698H6.7479H6.726H6.7042H6.6824H6.6607H6.639H6.6173H6.5957H6.5741H6.5526H6.5311H6.5097H6.4883H6.467H6.4457H6.4244H6.4032H6.3821H6.361H6.3399H6.3189H6.2979H6.277H6.2561H6.2353H6.2146H6.1938H6.1732H6.1526H6.132H6.1115H6.091H6.0706H6.0502H6.0299H6.0097H5.98944H5.96929H5.94919H5.92914H5.90914H5.8892H5.86931H5.84947H5.82969H5.80997H5.7903H5.77068H5.75112H5.73161H5.71216H5.69277H5.67343H5.65415H5.63493H5.61576H5.59665H5.5776H5.55861H5.53967H5.5208C4.15692 2.25 3.37688 2.80148 2.91281 3.44603ZM11.2348 5.28799L10.827 6.64399H12.7828L13.2772 5L14.2348 5.28799L13.827 6.64399H15V7.644H13.5263L13.2256 8.644H15V9.644H12.9249L12.4788 11.1272L11.5212 10.8392L11.8806 9.644H9.9249L9.4788 11.1272L8.5212 10.8392L8.8806 9.644H8V8.644H9.1813L9.4821 7.644H8V6.64399H9.7828L10.2772 5L11.2348 5.28799ZM12.1813 8.644L12.4821 7.644H10.5263L10.2256 8.644H12.1813ZM21.1492 19.9391L21.8844 20.0873C21.9602 19.7112 22 19.3222 22 18.9241C22 18.5259 21.9602 18.1369 21.8844 17.7608L21.1492 17.909L20.414 18.0573C20.4703 18.3364 20.5 18.6261 20.5 18.9241C20.5 19.222 20.4703 19.5117 20.414 19.7908L21.1492 19.9391ZM19.3191 13.9882C19.9747 14.4188 20.5396 14.9734 20.9795 15.6181L20.36 16.0408L19.7405 16.4635C19.4116 15.9816 18.9881 15.5654 18.4956 15.2419L18.9073 14.615L19.3191 13.9882ZM17.021 13.8476L17.1639 13.1114C16.7874 13.0383 16.3982 13 16 13C15.6018 13 15.2126 13.0383 14.8361 13.1114L14.979 13.8476L15.1219 14.5839C15.4047 14.529 15.6982 14.5 16 14.5C16.3018 14.5 16.5953 14.529 16.8781 14.5839L17.021 13.8476ZM13.0927 14.615L12.6809 13.9882C12.0253 14.4188 11.4604 14.9734 11.0205 15.6181L11.64 16.0408L12.2595 16.4635C12.5884 15.9816 13.0119 15.5654 13.5044 15.2419L13.0927 14.615ZM10 18.9241C10 18.5259 10.0398 18.1369 10.1156 17.7608L10.8508 17.909L11.586 18.0573C11.5297 18.3364 11.5 18.6261 11.5 18.9241C11.5 19.222 11.5297 19.5117 11.586 19.7908L10.8508 19.9391L10.1156 20.0873C10.0398 19.7112 10 19.3222 10 18.9241ZM11.64 21.8073L11.0205 22.2301C11.4604 22.8747 12.0253 23.4293 12.6809 23.8599L13.0927 23.2331L13.5044 22.6062C13.0119 22.2827 12.5884 21.8665 12.2595 21.3846L11.64 21.8073ZM14.979 24.0005L14.8361 24.7367C15.2126 24.8098 15.6018 24.8481 16 24.8481C16.3982 24.8481 16.7874 24.8098 17.1639 24.7367L17.021 24.0005L16.8781 23.2642C16.5953 23.3191 16.3018 23.3481 16 23.3481C15.6982 23.3481 15.4047 23.3191 15.1219 23.2642L14.979 24.0005ZM20.9795 22.2301C20.5396 22.8747 19.9747 23.4293 19.3191 23.8599L18.9073 23.2331L18.4956 22.6062C18.9881 22.2827 19.4116 21.8665 19.7405 21.3846L20.36 21.8073L20.9795 22.2301Z"
      fill="#F48400"
    />
    <path
      d="M18.7841 18.8229L14.5255 21.3491L14.4375 16.4512L18.7841 18.8229Z"
      fill="#F48400"
    />
  </svg>
);

export const IconStatusPass = () => (
  <svg
    width="22"
    height="25"
    viewBox="0 0 22 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.91281 3.44603C2.4212 4.12881 2.25 4.97068 2.25 5.5208V5.54441V5.56813V5.59195V5.61587V5.6399V5.66403V5.68826V5.7126V5.73703V5.76157V5.7862V5.81094V5.83578V5.86071V5.88575V5.91088V5.9361V5.96143V5.98685V6.01237V6.03798V6.06369V6.08949V6.11539V6.14138V6.16746V6.19364V6.2199V6.24626V6.27271V6.29925V6.32588V6.3526V6.37941V6.40631V6.43329V6.46037V6.48753V6.51477V6.54211V6.56953V6.59703V6.62462V6.65229V6.68005V6.70789V6.73581V6.76382V6.7919V6.82007V6.84832V6.87665V6.90506V6.93354V6.96211V6.99076V7.0195V7.0483V7.0772V7.1061V7.1351V7.1642V7.1934V7.2227V7.252V7.2814V7.3109V7.3404V7.3701V7.3997V7.4295V7.4593V7.4892V7.5192V7.5493V7.5794V7.6095V7.6398V7.6701V7.7005V7.7309V7.7614V7.792V7.8226V7.8534V7.8841V7.915V7.9458V7.9768V8.0078V8.0389V8.07V8.1012V8.1325V8.1638V8.1952V8.2266V8.2581V8.2897V8.3213V8.353V8.3847V8.4165V8.4483V8.4802V8.5121V8.5441V8.5762V8.6083V8.6404V8.6726V8.7049V8.7372V8.7695V8.8019V8.8344V8.8669V8.8994V8.932V8.9647V8.9974V9.0301V9.0629V9.0957V9.1286V9.1615V9.1945V9.2275V9.2605V9.2936V9.3267V9.3599V9.3931V9.4264V9.4597V9.493V9.5264V9.5598V9.5932V9.6267V9.6602V9.6938V9.7274V9.761V9.7947V9.8284V9.8621V9.8959V9.9297V9.9635V9.9974V10.0313V10.0652V10.0991V10.1331V10.1671V10.2012V10.2353V10.2694V10.3035V10.3376V10.3718V10.406V10.4403V10.4745V10.5088V10.5431V10.5775V10.6118V10.6462V10.6806V10.715V10.7495V10.7839V10.8184V10.8529V10.8874V10.922V10.9565V10.9911V11.0257V11.0603V11.0949V11.1296V11.1643V11.1989V11.2336V11.2683V11.303V11.3378V11.3725V11.4073V11.442V11.4768V11.5116V11.5464V11.5812V11.616V11.6508V11.6857V11.7205V11.7554V11.7902V11.8251V11.86V11.8948V11.9297V11.9646V11.9995V12.0343V12.0692V12.1041V12.139V12.1739V12.2088V12.2437V12.2786V12.3135V12.3484V12.3833V12.4182V12.4531V12.488V12.5228V12.5577V12.5926V12.6274V12.6623V12.6972V12.732V12.7669V12.8017V12.8365V12.8713V12.9061V12.9409V12.9757V13.0105V13.0453V13.08V13.1148V13.1495V13.1842V13.2189V13.2536V13.2883V13.323V13.3576V13.3923V13.4269V13.4615V13.496V13.5306V13.5652V13.5997V13.6342V13.6687V13.7031V13.7376V13.772V13.8064V13.8408V13.8752V13.9095V13.9438V13.9781V14.0124V14.0466V14.0808V14.115V14.1492V14.1833V14.2174V14.2515V14.2855V14.3195V14.3535V14.3875V14.4214V14.4553V14.4892V14.523V14.5568V14.5906V14.6243V14.658V14.6917V14.7254V14.759V14.7925V14.826V14.8595V14.893V14.9264V14.9598V14.9931V15.0264V15.0597V15.0929V15.1261V15.1592V15.1923V15.2254V15.2584V15.2913V15.3243V15.3571V15.39V15.4228V15.4555V15.4882V15.5209V15.5535V15.586V15.6185V15.651V15.6834V15.7158V15.7481V15.7803V15.8125V15.8447V15.8768V15.9089V15.9409V15.9728V16.0047V16.0366V16.0683V16.1001V16.1317V16.1634V16.1949V16.2264V16.2579V16.2893V16.3206V16.3519V16.3831V16.4142V16.4453V16.4763V16.5073V16.5382V16.5691V16.5998V16.6305V16.6612V16.6918V16.7223V16.7528V16.7832V16.8135V16.8437V16.8739V16.904V16.9341V16.9641V16.994V17.0238V17.0536V17.0833V17.1129V17.1425V17.172V17.2014V17.2307V17.26V17.2892V17.3183V17.3473V17.3763V17.4052V17.434V17.4628V17.4914V17.52V17.5485V17.5769V17.6053V17.6335V17.6617V17.6898V17.7178V17.7457V17.7736V17.8014V17.8291V17.8567V17.8842V17.9116V17.939V17.9662V17.9934V18.0205V18.0475V18.0744V18.1012V18.1279V18.1546V18.1811V18.2076V18.234V18.2603V18.2865V18.3125V18.3386V18.3645V18.3903V18.416V18.4416V18.4672V18.4926V18.5179V18.5432V18.5683V18.5934V18.6183V18.6432V18.6679V18.6926V18.7171V18.7416V18.7659V18.7902V18.8143V18.8384V18.8623V18.8862V18.9099V18.9335V18.957V18.9805V19.0038V19.027V19.0501V19.0731V19.0959V19.1187V19.1414V19.1639V19.1864V19.2087V19.2309V19.2531V19.2751V19.297V19.3187V19.3404V19.3619V19.3834V19.4047V19.4259V19.447V19.468V19.4888V19.5095V19.5302V19.5507V19.571V19.5913V19.6115V19.6315V19.6514V19.6712V19.6908V19.7104V19.7298V19.7491V19.7682V19.7873V19.8062V19.825V19.8437V19.8622V19.8806V19.8989V19.9171V19.9351V19.953V19.9708V19.9884V20.0059V20.0233V20.0406V20.0577V20.0747V20.0916V20.1083V20.1249V20.1413V20.1577V20.1739V20.1899V20.2058V20.2216V20.2373V20.2528V20.2681V20.2834V20.2985V20.3134V20.3282V20.3429V20.3574V20.3718V20.3861V20.4002V20.4141V20.428V20.4417V20.4552C2.25 21.1033 2.34787 21.6155 2.49344 21.9829C2.64114 22.3556 2.81658 22.5305 2.94123 22.5998C3.04043 22.6549 3.17311 22.6826 3.38919 22.5914C3.62888 22.4901 3.96256 22.2401 4.3464 21.7284C5.11012 20.7101 5.93693 20.2125 6.7774 20.2368C7.5796 20.2599 8.1456 20.7535 8.4294 21.1686L10.1416 23.4516L8.9416 24.3516L7.2184 22.054L7.2059 22.0373L7.1944 22.02C7.0907 21.8645 6.915 21.7414 6.7341 21.7362C6.579 21.7317 6.1612 21.8086 5.5464 22.6284C5.06863 23.2654 4.54072 23.7334 3.97266 23.9732C3.38099 24.223 2.75976 24.2149 2.21277 23.911C1.69122 23.6212 1.32816 23.114 1.09891 22.5354C0.86753 21.9515 0.75 21.2431 0.75 20.4552V20.4417V20.428V20.4141V20.4002V20.3861V20.3718V20.3574V20.3429V20.3282V20.3134V20.2985V20.2834V20.2681V20.2528V20.2373V20.2216V20.2058V20.1899V20.1739V20.1577V20.1413V20.1249V20.1083V20.0916V20.0747V20.0577V20.0406V20.0233V20.0059V19.9884V19.9708V19.953V19.9351V19.9171V19.8989V19.8806V19.8622V19.8437V19.825V19.8062V19.7873V19.7682V19.7491V19.7298V19.7104V19.6908V19.6712V19.6514V19.6315V19.6115V19.5913V19.571V19.5507V19.5302V19.5095V19.4888V19.468V19.447V19.4259V19.4047V19.3834V19.3619V19.3404V19.3187V19.297V19.2751V19.2531V19.2309V19.2087V19.1864V19.1639V19.1414V19.1187V19.0959V19.0731V19.0501V19.027V19.0038V18.9805V18.957V18.9335V18.9099V18.8862V18.8623V18.8384V18.8143V18.7902V18.7659V18.7416V18.7171V18.6926V18.6679V18.6432V18.6183V18.5934V18.5683V18.5432V18.5179V18.4926V18.4672V18.4416V18.416V18.3903V18.3645V18.3386V18.3125V18.2865V18.2603V18.234V18.2076V18.1811V18.1546V18.1279V18.1012V18.0744V18.0475V18.0205V17.9934V17.9662V17.939V17.9116V17.8842V17.8567V17.8291V17.8014V17.7736V17.7457V17.7178V17.6898V17.6617V17.6335V17.6053V17.5769V17.5485V17.52V17.4914V17.4628V17.434V17.4052V17.3763V17.3473V17.3183V17.2892V17.26V17.2307V17.2014V17.172V17.1425V17.1129V17.0833V17.0536V17.0238V16.994V16.9641V16.9341V16.904V16.8739V16.8437V16.8135V16.7832V16.7528V16.7223V16.6918V16.6612V16.6305V16.5998V16.5691V16.5382V16.5073V16.4763V16.4453V16.4142V16.3831V16.3519V16.3206V16.2893V16.2579V16.2264V16.1949V16.1634V16.1317V16.1001V16.0683V16.0366V16.0047V15.9728V15.9409V15.9089V15.8768V15.8447V15.8125V15.7803V15.7481V15.7158V15.6834V15.651V15.6185V15.586V15.5535V15.5209V15.4882V15.4555V15.4228V15.39V15.3571V15.3243V15.2913V15.2584V15.2254V15.1923V15.1592V15.1261V15.0929V15.0597V15.0264V14.9931V14.9598V14.9264V14.893V14.8595V14.826V14.7925V14.759V14.7254V14.6917V14.658V14.6243V14.5906V14.5568V14.523V14.4892V14.4553V14.4214V14.3875V14.3535V14.3195V14.2855V14.2515V14.2174V14.1833V14.1492V14.115V14.0808V14.0466V14.0124V13.9781V13.9438V13.9095V13.8752V13.8408V13.8064V13.772V13.7376V13.7031V13.6687V13.6342V13.5997V13.5652V13.5306V13.496V13.4615V13.4269V13.3923V13.3576V13.323V13.2883V13.2536V13.2189V13.1842V13.1495V13.1148V13.08V13.0453V13.0105V12.9757V12.9409V12.9061V12.8713V12.8365V12.8017V12.7669V12.732V12.6972V12.6623V12.6274V12.5926V12.5577V12.5228V12.488V12.4531V12.4182V12.3833V12.3484V12.3135V12.2786V12.2437V12.2088V12.1739V12.139V12.1041V12.0692V12.0343V11.9995V11.9646V11.9297V11.8948V11.86V11.8251V11.7902V11.7554V11.7205V11.6857V11.6508V11.616V11.5812V11.5464V11.5116V11.4768V11.442V11.4073V11.3725V11.3378V11.303V11.2683V11.2336V11.1989V11.1643V11.1296V11.0949V11.0603V11.0257V10.9911V10.9565V10.922V10.8874V10.8529V10.8184V10.7839V10.7495V10.715V10.6806V10.6462V10.6118V10.5775V10.5431V10.5088V10.4745V10.4403V10.406V10.3718V10.3376V10.3035V10.2694V10.2353V10.2012V10.1671V10.1331V10.0991V10.0652V10.0313V9.9974V9.9635V9.9297V9.8959V9.8621V9.8284V9.7947V9.761V9.7274V9.6938V9.6602V9.6267V9.5932V9.5598V9.5264V9.493V9.4597V9.4264V9.3931V9.3599V9.3267V9.2936V9.2605V9.2275V9.1945V9.1615V9.1286V9.0957V9.0629V9.0301V8.9974V8.9647V8.932V8.8994V8.8669V8.8344V8.8019V8.7695V8.7372V8.7049V8.6726V8.6404V8.6083V8.5762V8.5441V8.5121V8.4802V8.4483V8.4165V8.3847V8.353V8.3213V8.2897V8.2581V8.2266V8.1952V8.1638V8.1325V8.1012V8.07V8.0389V8.0078V7.9768V7.9458V7.915V7.8841V7.8534V7.8226V7.792V7.7614V7.7309V7.7005V7.6701V7.6398V7.6095V7.5794V7.5493V7.5192V7.4892V7.4593V7.4295V7.3997V7.3701V7.3404V7.3109V7.2814V7.252V7.2227V7.1934V7.1642V7.1351V7.1061V7.0772V7.0483V7.0195V6.99076V6.96211V6.93354V6.90506V6.87665V6.84832V6.82007V6.7919V6.76382V6.73581V6.70789V6.68005V6.65229V6.62462V6.59703V6.56953V6.54211V6.51477V6.48753V6.46037V6.43329V6.40631V6.37941V6.3526V6.32588V6.29925V6.27271V6.24626V6.2199V6.19364V6.16746V6.14138V6.11539V6.08949V6.06369V6.03798V6.01237V5.98685V5.96143V5.9361V5.91088V5.88575V5.86071V5.83578V5.81094V5.7862V5.76157V5.73703V5.7126V5.68826V5.66403V5.6399V5.61587V5.59195V5.56813V5.54441V5.5208C0.75 4.73065 0.98088 3.56212 1.69551 2.56957C2.43768 1.53878 3.66804 0.75 5.5208 0.75H5.53967H5.55861H5.5776H5.59665H5.61576H5.63493H5.65415H5.67343H5.69277H5.71216H5.73161H5.75112H5.77068H5.7903H5.80997H5.82969H5.84947H5.86931H5.8892H5.90914H5.92914H5.94919H5.96929H5.98944H6.0097H6.0299H6.0502H6.0706H6.091H6.1115H6.132H6.1526H6.1732H6.1938H6.2146H6.2353H6.2561H6.277H6.2979H6.3189H6.3399H6.361H6.3821H6.4032H6.4244H6.4457H6.467H6.4883H6.5097H6.5311H6.5526H6.5741H6.5957H6.6173H6.639H6.6607H6.6824H6.7042H6.726H6.7479H6.7698H6.7918H6.8138H6.8359H6.8579H6.8801H6.9023H6.9245H6.9467H6.969H6.9914H7.0137H7.0361H7.0586H7.0811H7.1036H7.1262H7.1488H7.1715H7.1942H7.2169H7.2396H7.2624H7.2853H7.3082H7.3311H7.354H7.377H7.4H7.4231H7.4462H7.4693H7.4924H7.5156H7.5389H7.5621H7.5854H7.6088H7.6321H7.6555H7.6789H7.7024H7.7259H7.7494H7.773H7.7965H7.8202H7.8438H7.8675H7.8912H7.9149H7.9387H7.9625H7.9863H8.0102H8.0341H8.058H8.0819H8.1059H8.1299H8.1539H8.1779H8.202H8.2261H8.2502H8.2744H8.2986H8.3228H8.347H8.3713H8.3956H8.4199H8.4442H8.4685H8.4929H8.5173H8.5417H8.5662H8.5907H8.6151H8.6397H8.6642H8.6888H8.7133H8.7379H8.7625H8.7872H8.8118H8.8365H8.8612H8.8859H8.9107H8.9354H8.9602H8.985H9.0098H9.0347H9.0595H9.0844H9.1093H9.1341H9.1591H9.184H9.2089H9.2339H9.2589H9.2839H9.3089H9.3339H9.359H9.384H9.4091H9.4342H9.4592H9.4844H9.5095H9.5346H9.5598H9.5849H9.6101H9.6353H9.6605H9.6857H9.7109H9.7361H9.7613H9.7866H9.8118H9.8371H9.8624H9.8876H9.9129H9.9382H9.9635H9.9889H10.0142H10.0395H10.0648H10.0902H10.1155H10.1409H10.1663H10.1916H10.217H10.2424H10.2678H10.2932H10.3186H10.3439H10.3694H10.3948H10.4202H10.4456H10.471H10.4964H10.5218H10.5473H10.5727H10.5981H10.6235H10.649H10.6744H10.6998H10.7253H10.7507H10.7761H10.8015H10.827H10.8524H10.8778H10.9033H10.9287H10.9541H10.9795H11.0049H11.0303H11.0558H11.0812H11.1066H11.132H11.1574H11.1828H11.2081H11.2335H11.2589H11.2843H11.3096H11.335H11.3604H11.3857H11.411H11.4364H11.4617H11.487H11.5123H11.5376H11.5629H11.5882H11.6135H11.6388H11.664H11.6893H11.7145H11.7397H11.7649H11.7902H11.8153H11.8405H11.8657H11.8909H11.916H11.9412H11.9663H11.9914H12.0165H12.0416H12.0667H12.0917H12.1168H12.1418H12.1668H12.1918H12.2168H12.2418H12.2667H12.2917H12.3166H12.3415H12.3664H12.3912H12.4161H12.4409H12.4658H12.4906H12.5153H12.5401H12.5649H12.5896H12.6143H12.639H12.6636H12.6883H12.7129H12.7375H12.7621H12.7867H12.8112H12.8357H12.8602H12.8847H12.9092H12.9336H12.958H12.9824H13.0068H13.0311H13.0554H13.0797H13.104H13.1282H13.1524H13.1766H13.2008H13.2249H13.249H13.2731H13.2972H13.3212H13.3452H13.3692H13.3932H13.4171H13.441H13.4649H13.4887H13.5125H13.5363H13.56H13.5837H13.6074H13.6311H13.6547H13.6783H13.7019H13.7254H13.7489H13.7724H13.7958H13.8192H13.8426H13.866H13.8893H13.9125H13.9358H13.959H13.9822H14.0053H14.0284H14.0515H14.0745H14.0975H14.1205H14.1434H14.1663H14.1891H14.212H14.2347H14.2575H14.2802H14.3029H14.3255H14.3481H14.3706H14.3931H14.4156H14.438H14.4604H14.4828H14.5051H14.5274H14.5496H14.5718H14.5939H14.6161H14.6381H14.6601H14.6821H14.7041H14.726H14.7478H14.7696H14.7914H14.8131H14.8348H14.8564H14.878H14.8996H14.921H14.9425H14.9639H14.9853H15.0066H15.0279H15.0491H15.0702H15.0914H15.1125H15.1335H15.1545H15.1754H15.1963H15.2171H15.2379H15.2586H15.2793H15.3H15.3206H15.3411H15.3616H15.382H15.4024H15.4227H15.443H15.4633H15.4834H15.5036H15.5236H15.5436H15.5636H15.5835H15.6034H15.6232H15.6429H15.6626H15.6823H15.7019H15.7214H15.7409H15.7603H15.7796H15.7989H15.8182H15.8374H15.8565H15.8756H15.8946H15.9136H15.9325H15.9513H15.9701H15.9888H16.0075H16.0261H16.0446H16.0631H16.0815H16.0999H16.1182H16.1364H16.1546H16.1727H16.1908H16.2088H16.2267H16.2446H16.2624H16.2801H16.2978H16.3154H16.333H16.3505H16.3679H16.3853H16.4026H16.4198H16.4369H16.454H16.4711H16.488H16.5049H16.5218H16.5385H16.5552H16.5718H16.5884H16.6049H16.6213H16.6377H16.6539H16.6702H16.6863H16.7024H16.7184H16.7343H16.7502H16.766H16.7817H16.7973H16.8129H16.8284H16.8439H16.8592H16.8745H16.8897H16.9049H16.9199H16.9349H16.9498H16.9647H16.9795H16.9942H17.0088C19.2834 0.75 20.5365 1.75108 21.1748 2.8453C21.4797 3.3679 21.6289 3.88388 21.703 4.26475C21.7403 4.45662 21.7593 4.61856 21.7691 4.73603C21.774 4.79489 21.7767 4.84299 21.778 4.87855C21.7787 4.89634 21.7791 4.91103 21.7793 4.9224L21.7795 4.93696L21.7796 4.94232V4.94452C21.7796 4.94498 21.7796 4.9464 21.0296 4.9464H21.7796V12.4136H20.2796V4.95031L20.2792 4.93684C20.2786 4.92191 20.2773 4.89596 20.2743 4.86059C20.2684 4.78959 20.2561 4.68228 20.2305 4.55105C20.179 4.28572 20.0769 3.94009 19.8791 3.6011C19.5122 2.97212 18.755 2.25 17.0088 2.25H16.9942H16.9795H16.9647H16.9498H16.9349H16.9199H16.9049H16.8897H16.8745H16.8592H16.8439H16.8284H16.8129H16.7973H16.7817H16.766H16.7502H16.7343H16.7184H16.7024H16.6863H16.6702H16.6539H16.6377H16.6213H16.6049H16.5884H16.5718H16.5552H16.5385H16.5218H16.5049H16.488H16.4711H16.454H16.4369H16.4198H16.4026H16.3853H16.3679H16.3505H16.333H16.3154H16.2978H16.2801H16.2624H16.2446H16.2267H16.2088H16.1908H16.1727H16.1546H16.1364H16.1182H16.0999H16.0815H16.0631H16.0446H16.0261H16.0075H15.9888H15.9701H15.9513H15.9325H15.9136H15.8946H15.8756H15.8565H15.8374H15.8182H15.7989H15.7796H15.7603H15.7409H15.7214H15.7019H15.6823H15.6626H15.6429H15.6232H15.6034H15.5835H15.5636H15.5436H15.5236H15.5036H15.4834H15.4633H15.443H15.4227H15.4024H15.382H15.3616H15.3411H15.3206H15.3H15.2793H15.2586H15.2379H15.2171H15.1963H15.1754H15.1545H15.1335H15.1125H15.0914H15.0702H15.0491H15.0279H15.0066H14.9853H14.9639H14.9425H14.921H14.8996H14.878H14.8564H14.8348H14.8131H14.7914H14.7696H14.7478H14.726H14.7041H14.6821H14.6601H14.6381H14.6161H14.5939H14.5718H14.5496H14.5274H14.5051H14.4828H14.4604H14.438H14.4156H14.3931H14.3706H14.3481H14.3255H14.3029H14.2802H14.2575H14.2347H14.212H14.1891H14.1663H14.1434H14.1205H14.0975H14.0745H14.0515H14.0284H14.0053H13.9822H13.959H13.9358H13.9125H13.8893H13.866H13.8426H13.8192H13.7958H13.7724H13.7489H13.7254H13.7019H13.6783H13.6547H13.6311H13.6074H13.5837H13.56H13.5363H13.5125H13.4887H13.4649H13.441H13.4171H13.3932H13.3692H13.3452H13.3212H13.2972H13.2731H13.249H13.2249H13.2008H13.1766H13.1524H13.1282H13.104H13.0797H13.0554H13.0311H13.0068H12.9824H12.958H12.9336H12.9092H12.8847H12.8602H12.8357H12.8112H12.7867H12.7621H12.7375H12.7129H12.6883H12.6636H12.639H12.6143H12.5896H12.5649H12.5401H12.5153H12.4906H12.4658H12.4409H12.4161H12.3912H12.3664H12.3415H12.3166H12.2917H12.2667H12.2418H12.2168H12.1918H12.1668H12.1418H12.1168H12.0917H12.0667H12.0416H12.0165H11.9914H11.9663H11.9412H11.916H11.8909H11.8657H11.8405H11.8153H11.7902H11.7649H11.7397H11.7145H11.6893H11.664H11.6388H11.6135H11.5882H11.5629H11.5376H11.5123H11.487H11.4617H11.4364H11.411H11.3857H11.3604H11.335H11.3096H11.2843H11.2589H11.2335H11.2081H11.1828H11.1574H11.132H11.1066H11.0812H11.0558H11.0303H11.0049H10.9795H10.9541H10.9287H10.9033H10.8778H10.8524H10.827H10.8015H10.7761H10.7507H10.7253H10.6998H10.6744H10.649H10.6235H10.5981H10.5727H10.5473H10.5218H10.4964H10.471H10.4456H10.4202H10.3948H10.3694H10.3439H10.3186H10.2932H10.2678H10.2424H10.217H10.1916H10.1663H10.1409H10.1155H10.0902H10.0648H10.0395H10.0142H9.9889H9.9635H9.9382H9.9129H9.8876H9.8624H9.8371H9.8118H9.7866H9.7613H9.7361H9.7109H9.6857H9.6605H9.6353H9.6101H9.5849H9.5598H9.5346H9.5095H9.4844H9.4592H9.4342H9.4091H9.384H9.359H9.3339H9.3089H9.2839H9.2589H9.2339H9.2089H9.184H9.1591H9.1341H9.1093H9.0844H9.0595H9.0347H9.0098H8.985H8.9602H8.9354H8.9107H8.8859H8.8612H8.8365H8.8118H8.7872H8.7625H8.7379H8.7133H8.6888H8.6642H8.6397H8.6151H8.5907H8.5662H8.5417H8.5173H8.4929H8.4685H8.4442H8.4199H8.3956H8.3713H8.347H8.3228H8.2986H8.2744H8.2502H8.2261H8.202H8.1779H8.1539H8.1299H8.1059H8.0819H8.058H8.0341H8.0102H7.9863H7.9625H7.9387H7.9149H7.8912H7.8675H7.8438H7.8202H7.7965H7.773H7.7494H7.7259H7.7024H7.6789H7.6555H7.6321H7.6088H7.5854H7.5621H7.5389H7.5156H7.4924H7.4693H7.4462H7.4231H7.4H7.377H7.354H7.3311H7.3082H7.2853H7.2624H7.2396H7.2169H7.1942H7.1715H7.1488H7.1262H7.1036H7.0811H7.0586H7.0361H7.0137H6.9914H6.969H6.9467H6.9245H6.9023H6.8801H6.8579H6.8359H6.8138H6.7918H6.7698H6.7479H6.726H6.7042H6.6824H6.6607H6.639H6.6173H6.5957H6.5741H6.5526H6.5311H6.5097H6.4883H6.467H6.4457H6.4244H6.4032H6.3821H6.361H6.3399H6.3189H6.2979H6.277H6.2561H6.2353H6.2146H6.1938H6.1732H6.1526H6.132H6.1115H6.091H6.0706H6.0502H6.0299H6.0097H5.98944H5.96929H5.94919H5.92914H5.90914H5.8892H5.86931H5.84947H5.82969H5.80997H5.7903H5.77068H5.75112H5.73161H5.71216H5.69277H5.67343H5.65415H5.63493H5.61576H5.59665H5.5776H5.55861H5.53967H5.5208C4.15692 2.25 3.37688 2.80148 2.91281 3.44603ZM11.2348 5.28799L10.827 6.64399H12.7828L13.2772 5L14.2348 5.28799L13.827 6.64399H15V7.644H13.5263L13.2256 8.644H15V9.644H12.9249L12.4788 11.1272L11.5212 10.8392L11.8806 9.644H9.9249L9.4788 11.1272L8.5212 10.8392L8.8806 9.644H8V8.644H9.1813L9.4821 7.644H8V6.64399H9.7828L10.2772 5L11.2348 5.28799ZM12.1813 8.644L12.4821 7.644H10.5263L10.2256 8.644H12.1813ZM16 23.3481C18.5034 23.3481 20.5 21.3494 20.5 18.9241C20.5 16.4987 18.5034 14.5 16 14.5C13.4966 14.5 11.5 16.4987 11.5 18.9241C11.5 21.3494 13.4966 23.3481 16 23.3481ZM16 24.8481C19.3137 24.8481 22 22.1958 22 18.9241C22 15.6523 19.3137 13 16 13C12.6863 13 10 15.6523 10 18.9241C10 22.1958 12.6863 24.8481 16 24.8481ZM18.6031 17.4457L15.6316 21.467L15.1859 22.0702L14.5827 21.6244L12.5721 20.1387L13.4635 18.9323L14.871 19.9723L17.3967 16.5543L18.6031 17.4457Z"
      fill="#74B856"
    />
  </svg>
);

export const IconStatusFail = () => (
  <svg
    width="22"
    height="25"
    viewBox="0 0 22 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.91281 3.44603C2.4212 4.12881 2.25 4.97068 2.25 5.5208V5.54441V5.56813V5.59195V5.61587V5.6399V5.66403V5.68826V5.7126V5.73703V5.76157V5.7862V5.81094V5.83578V5.86071V5.88575V5.91088V5.9361V5.96143V5.98685V6.01237V6.03798V6.06369V6.08949V6.11539V6.14138V6.16746V6.19364V6.2199V6.24626V6.27271V6.29925V6.32588V6.3526V6.37941V6.40631V6.43329V6.46037V6.48753V6.51477V6.54211V6.56953V6.59703V6.62462V6.65229V6.68005V6.70789V6.73581V6.76382V6.7919V6.82007V6.84832V6.87665V6.90506V6.93354V6.96211V6.99076V7.0195V7.0483V7.0772V7.1061V7.1351V7.1642V7.1934V7.2227V7.252V7.2814V7.3109V7.3404V7.3701V7.3997V7.4295V7.4593V7.4892V7.5192V7.5493V7.5794V7.6095V7.6398V7.6701V7.7005V7.7309V7.7614V7.792V7.8226V7.8534V7.8841V7.915V7.9458V7.9768V8.0078V8.0389V8.07V8.1012V8.1325V8.1638V8.1952V8.2266V8.2581V8.2897V8.3213V8.353V8.3847V8.4165V8.4483V8.4802V8.5121V8.5441V8.5762V8.6083V8.6404V8.6726V8.7049V8.7372V8.7695V8.8019V8.8344V8.8669V8.8994V8.932V8.9647V8.9974V9.0301V9.0629V9.0957V9.1286V9.1615V9.1945V9.2275V9.2605V9.2936V9.3267V9.3599V9.3931V9.4264V9.4597V9.493V9.5264V9.5598V9.5932V9.6267V9.6602V9.6938V9.7274V9.761V9.7947V9.8284V9.8621V9.8959V9.9297V9.9635V9.9974V10.0313V10.0652V10.0991V10.1331V10.1671V10.2012V10.2353V10.2694V10.3035V10.3376V10.3718V10.406V10.4403V10.4745V10.5088V10.5431V10.5775V10.6118V10.6462V10.6806V10.715V10.7495V10.7839V10.8184V10.8529V10.8874V10.922V10.9565V10.9911V11.0257V11.0603V11.0949V11.1296V11.1643V11.1989V11.2336V11.2683V11.303V11.3378V11.3725V11.4073V11.442V11.4768V11.5116V11.5464V11.5812V11.616V11.6508V11.6857V11.7205V11.7554V11.7902V11.8251V11.86V11.8948V11.9297V11.9646V11.9995V12.0343V12.0692V12.1041V12.139V12.1739V12.2088V12.2437V12.2786V12.3135V12.3484V12.3833V12.4182V12.4531V12.488V12.5228V12.5577V12.5926V12.6274V12.6623V12.6972V12.732V12.7669V12.8017V12.8365V12.8713V12.9061V12.9409V12.9757V13.0105V13.0453V13.08V13.1148V13.1495V13.1842V13.2189V13.2536V13.2883V13.323V13.3576V13.3923V13.4269V13.4615V13.496V13.5306V13.5652V13.5997V13.6342V13.6687V13.7031V13.7376V13.772V13.8064V13.8408V13.8752V13.9095V13.9438V13.9781V14.0124V14.0466V14.0808V14.115V14.1492V14.1833V14.2174V14.2515V14.2855V14.3195V14.3535V14.3875V14.4214V14.4553V14.4892V14.523V14.5568V14.5906V14.6243V14.658V14.6917V14.7254V14.759V14.7925V14.826V14.8595V14.893V14.9264V14.9598V14.9931V15.0264V15.0597V15.0929V15.1261V15.1592V15.1923V15.2254V15.2584V15.2913V15.3243V15.3571V15.39V15.4228V15.4555V15.4882V15.5209V15.5535V15.586V15.6185V15.651V15.6834V15.7158V15.7481V15.7803V15.8125V15.8447V15.8768V15.9089V15.9409V15.9728V16.0047V16.0366V16.0683V16.1001V16.1317V16.1634V16.1949V16.2264V16.2579V16.2893V16.3206V16.3519V16.3831V16.4142V16.4453V16.4763V16.5073V16.5382V16.5691V16.5998V16.6305V16.6612V16.6918V16.7223V16.7528V16.7832V16.8135V16.8437V16.8739V16.904V16.9341V16.9641V16.994V17.0238V17.0536V17.0833V17.1129V17.1425V17.172V17.2014V17.2307V17.26V17.2892V17.3183V17.3473V17.3763V17.4052V17.434V17.4628V17.4914V17.52V17.5485V17.5769V17.6053V17.6335V17.6617V17.6898V17.7178V17.7457V17.7736V17.8014V17.8291V17.8567V17.8842V17.9116V17.939V17.9662V17.9934V18.0205V18.0475V18.0744V18.1012V18.1279V18.1546V18.1811V18.2076V18.234V18.2603V18.2865V18.3125V18.3386V18.3645V18.3903V18.416V18.4416V18.4672V18.4926V18.5179V18.5432V18.5683V18.5934V18.6183V18.6432V18.6679V18.6926V18.7171V18.7416V18.7659V18.7902V18.8143V18.8384V18.8623V18.8862V18.9099V18.9335V18.957V18.9805V19.0038V19.027V19.0501V19.0731V19.0959V19.1187V19.1414V19.1639V19.1864V19.2087V19.2309V19.2531V19.2751V19.297V19.3187V19.3404V19.3619V19.3834V19.4047V19.4259V19.447V19.468V19.4888V19.5095V19.5302V19.5507V19.571V19.5913V19.6115V19.6315V19.6514V19.6712V19.6908V19.7104V19.7298V19.7491V19.7682V19.7873V19.8062V19.825V19.8437V19.8622V19.8806V19.8989V19.9171V19.9351V19.953V19.9708V19.9884V20.0059V20.0233V20.0406V20.0577V20.0747V20.0916V20.1083V20.1249V20.1413V20.1577V20.1739V20.1899V20.2058V20.2216V20.2373V20.2528V20.2681V20.2834V20.2985V20.3134V20.3282V20.3429V20.3574V20.3718V20.3861V20.4002V20.4141V20.428V20.4417V20.4552C2.25 21.1033 2.34787 21.6155 2.49344 21.9829C2.64114 22.3556 2.81658 22.5305 2.94123 22.5998C3.04043 22.6549 3.17311 22.6826 3.38919 22.5914C3.62888 22.4901 3.96256 22.2401 4.3464 21.7284C5.11012 20.7101 5.93693 20.2125 6.7774 20.2368C7.5796 20.2599 8.1456 20.7535 8.4294 21.1686L10.1416 23.4516L8.9416 24.3516L7.2184 22.054L7.2059 22.0373L7.1944 22.02C7.0907 21.8645 6.915 21.7414 6.7341 21.7362C6.579 21.7317 6.1612 21.8086 5.5464 22.6284C5.06863 23.2654 4.54072 23.7334 3.97266 23.9732C3.38099 24.223 2.75976 24.2149 2.21277 23.911C1.69122 23.6212 1.32816 23.114 1.09891 22.5354C0.86753 21.9515 0.75 21.2431 0.75 20.4552V20.4417V20.428V20.4141V20.4002V20.3861V20.3718V20.3574V20.3429V20.3282V20.3134V20.2985V20.2834V20.2681V20.2528V20.2373V20.2216V20.2058V20.1899V20.1739V20.1577V20.1413V20.1249V20.1083V20.0916V20.0747V20.0577V20.0406V20.0233V20.0059V19.9884V19.9708V19.953V19.9351V19.9171V19.8989V19.8806V19.8622V19.8437V19.825V19.8062V19.7873V19.7682V19.7491V19.7298V19.7104V19.6908V19.6712V19.6514V19.6315V19.6115V19.5913V19.571V19.5507V19.5302V19.5095V19.4888V19.468V19.447V19.4259V19.4047V19.3834V19.3619V19.3404V19.3187V19.297V19.2751V19.2531V19.2309V19.2087V19.1864V19.1639V19.1414V19.1187V19.0959V19.0731V19.0501V19.027V19.0038V18.9805V18.957V18.9335V18.9099V18.8862V18.8623V18.8384V18.8143V18.7902V18.7659V18.7416V18.7171V18.6926V18.6679V18.6432V18.6183V18.5934V18.5683V18.5432V18.5179V18.4926V18.4672V18.4416V18.416V18.3903V18.3645V18.3386V18.3125V18.2865V18.2603V18.234V18.2076V18.1811V18.1546V18.1279V18.1012V18.0744V18.0475V18.0205V17.9934V17.9662V17.939V17.9116V17.8842V17.8567V17.8291V17.8014V17.7736V17.7457V17.7178V17.6898V17.6617V17.6335V17.6053V17.5769V17.5485V17.52V17.4914V17.4628V17.434V17.4052V17.3763V17.3473V17.3183V17.2892V17.26V17.2307V17.2014V17.172V17.1425V17.1129V17.0833V17.0536V17.0238V16.994V16.9641V16.9341V16.904V16.8739V16.8437V16.8135V16.7832V16.7528V16.7223V16.6918V16.6612V16.6305V16.5998V16.5691V16.5382V16.5073V16.4763V16.4453V16.4142V16.3831V16.3519V16.3206V16.2893V16.2579V16.2264V16.1949V16.1634V16.1317V16.1001V16.0683V16.0366V16.0047V15.9728V15.9409V15.9089V15.8768V15.8447V15.8125V15.7803V15.7481V15.7158V15.6834V15.651V15.6185V15.586V15.5535V15.5209V15.4882V15.4555V15.4228V15.39V15.3571V15.3243V15.2913V15.2584V15.2254V15.1923V15.1592V15.1261V15.0929V15.0597V15.0264V14.9931V14.9598V14.9264V14.893V14.8595V14.826V14.7925V14.759V14.7254V14.6917V14.658V14.6243V14.5906V14.5568V14.523V14.4892V14.4553V14.4214V14.3875V14.3535V14.3195V14.2855V14.2515V14.2174V14.1833V14.1492V14.115V14.0808V14.0466V14.0124V13.9781V13.9438V13.9095V13.8752V13.8408V13.8064V13.772V13.7376V13.7031V13.6687V13.6342V13.5997V13.5652V13.5306V13.496V13.4615V13.4269V13.3923V13.3576V13.323V13.2883V13.2536V13.2189V13.1842V13.1495V13.1148V13.08V13.0453V13.0105V12.9757V12.9409V12.9061V12.8713V12.8365V12.8017V12.7669V12.732V12.6972V12.6623V12.6274V12.5926V12.5577V12.5228V12.488V12.4531V12.4182V12.3833V12.3484V12.3135V12.2786V12.2437V12.2088V12.1739V12.139V12.1041V12.0692V12.0343V11.9995V11.9646V11.9297V11.8948V11.86V11.8251V11.7902V11.7554V11.7205V11.6857V11.6508V11.616V11.5812V11.5464V11.5116V11.4768V11.442V11.4073V11.3725V11.3378V11.303V11.2683V11.2336V11.1989V11.1643V11.1296V11.0949V11.0603V11.0257V10.9911V10.9565V10.922V10.8874V10.8529V10.8184V10.7839V10.7495V10.715V10.6806V10.6462V10.6118V10.5775V10.5431V10.5088V10.4745V10.4403V10.406V10.3718V10.3376V10.3035V10.2694V10.2353V10.2012V10.1671V10.1331V10.0991V10.0652V10.0313V9.9974V9.9635V9.9297V9.8959V9.8621V9.8284V9.7947V9.761V9.7274V9.6938V9.6602V9.6267V9.5932V9.5598V9.5264V9.493V9.4597V9.4264V9.3931V9.3599V9.3267V9.2936V9.2605V9.2275V9.1945V9.1615V9.1286V9.0957V9.0629V9.0301V8.9974V8.9647V8.932V8.8994V8.8669V8.8344V8.8019V8.7695V8.7372V8.7049V8.6726V8.6404V8.6083V8.5762V8.5441V8.5121V8.4802V8.4483V8.4165V8.3847V8.353V8.3213V8.2897V8.2581V8.2266V8.1952V8.1638V8.1325V8.1012V8.07V8.0389V8.0078V7.9768V7.9458V7.915V7.8841V7.8534V7.8226V7.792V7.7614V7.7309V7.7005V7.6701V7.6398V7.6095V7.5794V7.5493V7.5192V7.4892V7.4593V7.4295V7.3997V7.3701V7.3404V7.3109V7.2814V7.252V7.2227V7.1934V7.1642V7.1351V7.1061V7.0772V7.0483V7.0195V6.99076V6.96211V6.93354V6.90506V6.87665V6.84832V6.82007V6.7919V6.76382V6.73581V6.70789V6.68005V6.65229V6.62462V6.59703V6.56953V6.54211V6.51477V6.48753V6.46037V6.43329V6.40631V6.37941V6.3526V6.32588V6.29925V6.27271V6.24626V6.2199V6.19364V6.16746V6.14138V6.11539V6.08949V6.06369V6.03798V6.01237V5.98685V5.96143V5.9361V5.91088V5.88575V5.86071V5.83578V5.81094V5.7862V5.76157V5.73703V5.7126V5.68826V5.66403V5.6399V5.61587V5.59195V5.56813V5.54441V5.5208C0.75 4.73065 0.98088 3.56212 1.69551 2.56957C2.43768 1.53878 3.66804 0.75 5.5208 0.75H5.53967H5.55861H5.5776H5.59665H5.61576H5.63493H5.65415H5.67343H5.69277H5.71216H5.73161H5.75112H5.77068H5.7903H5.80997H5.82969H5.84947H5.86931H5.8892H5.90914H5.92914H5.94919H5.96929H5.98944H6.0097H6.0299H6.0502H6.0706H6.091H6.1115H6.132H6.1526H6.1732H6.1938H6.2146H6.2353H6.2561H6.277H6.2979H6.3189H6.3399H6.361H6.3821H6.4032H6.4244H6.4457H6.467H6.4883H6.5097H6.5311H6.5526H6.5741H6.5957H6.6173H6.639H6.6607H6.6824H6.7042H6.726H6.7479H6.7698H6.7918H6.8138H6.8359H6.8579H6.8801H6.9023H6.9245H6.9467H6.969H6.9914H7.0137H7.0361H7.0586H7.0811H7.1036H7.1262H7.1488H7.1715H7.1942H7.2169H7.2396H7.2624H7.2853H7.3082H7.3311H7.354H7.377H7.4H7.4231H7.4462H7.4693H7.4924H7.5156H7.5389H7.5621H7.5854H7.6088H7.6321H7.6555H7.6789H7.7024H7.7259H7.7494H7.773H7.7965H7.8202H7.8438H7.8675H7.8912H7.9149H7.9387H7.9625H7.9863H8.0102H8.0341H8.058H8.0819H8.1059H8.1299H8.1539H8.1779H8.202H8.2261H8.2502H8.2744H8.2986H8.3228H8.347H8.3713H8.3956H8.4199H8.4442H8.4685H8.4929H8.5173H8.5417H8.5662H8.5907H8.6151H8.6397H8.6642H8.6888H8.7133H8.7379H8.7625H8.7872H8.8118H8.8365H8.8612H8.8859H8.9107H8.9354H8.9602H8.985H9.0098H9.0347H9.0595H9.0844H9.1093H9.1341H9.1591H9.184H9.2089H9.2339H9.2589H9.2839H9.3089H9.3339H9.359H9.384H9.4091H9.4342H9.4592H9.4844H9.5095H9.5346H9.5598H9.5849H9.6101H9.6353H9.6605H9.6857H9.7109H9.7361H9.7613H9.7866H9.8118H9.8371H9.8624H9.8876H9.9129H9.9382H9.9635H9.9889H10.0142H10.0395H10.0648H10.0902H10.1155H10.1409H10.1663H10.1916H10.217H10.2424H10.2678H10.2932H10.3186H10.3439H10.3694H10.3948H10.4202H10.4456H10.471H10.4964H10.5218H10.5473H10.5727H10.5981H10.6235H10.649H10.6744H10.6998H10.7253H10.7507H10.7761H10.8015H10.827H10.8524H10.8778H10.9033H10.9287H10.9541H10.9795H11.0049H11.0303H11.0558H11.0812H11.1066H11.132H11.1574H11.1828H11.2081H11.2335H11.2589H11.2843H11.3096H11.335H11.3604H11.3857H11.411H11.4364H11.4617H11.487H11.5123H11.5376H11.5629H11.5882H11.6135H11.6388H11.664H11.6893H11.7145H11.7397H11.7649H11.7902H11.8153H11.8405H11.8657H11.8909H11.916H11.9412H11.9663H11.9914H12.0165H12.0416H12.0667H12.0917H12.1168H12.1418H12.1668H12.1918H12.2168H12.2418H12.2667H12.2917H12.3166H12.3415H12.3664H12.3912H12.4161H12.4409H12.4658H12.4906H12.5153H12.5401H12.5649H12.5896H12.6143H12.639H12.6636H12.6883H12.7129H12.7375H12.7621H12.7867H12.8112H12.8357H12.8602H12.8847H12.9092H12.9336H12.958H12.9824H13.0068H13.0311H13.0554H13.0797H13.104H13.1282H13.1524H13.1766H13.2008H13.2249H13.249H13.2731H13.2972H13.3212H13.3452H13.3692H13.3932H13.4171H13.441H13.4649H13.4887H13.5125H13.5363H13.56H13.5837H13.6074H13.6311H13.6547H13.6783H13.7019H13.7254H13.7489H13.7724H13.7958H13.8192H13.8426H13.866H13.8893H13.9125H13.9358H13.959H13.9822H14.0053H14.0284H14.0515H14.0745H14.0975H14.1205H14.1434H14.1663H14.1891H14.212H14.2347H14.2575H14.2802H14.3029H14.3255H14.3481H14.3706H14.3931H14.4156H14.438H14.4604H14.4828H14.5051H14.5274H14.5496H14.5718H14.5939H14.6161H14.6381H14.6601H14.6821H14.7041H14.726H14.7478H14.7696H14.7914H14.8131H14.8348H14.8564H14.878H14.8996H14.921H14.9425H14.9639H14.9853H15.0066H15.0279H15.0491H15.0702H15.0914H15.1125H15.1335H15.1545H15.1754H15.1963H15.2171H15.2379H15.2586H15.2793H15.3H15.3206H15.3411H15.3616H15.382H15.4024H15.4227H15.443H15.4633H15.4834H15.5036H15.5236H15.5436H15.5636H15.5835H15.6034H15.6232H15.6429H15.6626H15.6823H15.7019H15.7214H15.7409H15.7603H15.7796H15.7989H15.8182H15.8374H15.8565H15.8756H15.8946H15.9136H15.9325H15.9513H15.9701H15.9888H16.0075H16.0261H16.0446H16.0631H16.0815H16.0999H16.1182H16.1364H16.1546H16.1727H16.1908H16.2088H16.2267H16.2446H16.2624H16.2801H16.2978H16.3154H16.333H16.3505H16.3679H16.3853H16.4026H16.4198H16.4369H16.454H16.4711H16.488H16.5049H16.5218H16.5385H16.5552H16.5718H16.5884H16.6049H16.6213H16.6377H16.6539H16.6702H16.6863H16.7024H16.7184H16.7343H16.7502H16.766H16.7817H16.7973H16.8129H16.8284H16.8439H16.8592H16.8745H16.8897H16.9049H16.9199H16.9349H16.9498H16.9647H16.9795H16.9942H17.0088C19.2834 0.75 20.5365 1.75108 21.1748 2.8453C21.4797 3.3679 21.6289 3.88388 21.703 4.26475C21.7403 4.45662 21.7593 4.61856 21.7691 4.73603C21.774 4.79489 21.7767 4.84299 21.778 4.87855C21.7787 4.89634 21.7791 4.91103 21.7793 4.9224L21.7795 4.93696L21.7796 4.94232V4.94452C21.7796 4.94498 21.7796 4.9464 21.0296 4.9464H21.7796V12.4136H20.2796V4.95031L20.2792 4.93684C20.2786 4.92191 20.2773 4.89596 20.2743 4.86059C20.2684 4.78959 20.2561 4.68228 20.2305 4.55105C20.179 4.28572 20.0769 3.94009 19.8791 3.6011C19.5122 2.97212 18.755 2.25 17.0088 2.25H16.9942H16.9795H16.9647H16.9498H16.9349H16.9199H16.9049H16.8897H16.8745H16.8592H16.8439H16.8284H16.8129H16.7973H16.7817H16.766H16.7502H16.7343H16.7184H16.7024H16.6863H16.6702H16.6539H16.6377H16.6213H16.6049H16.5884H16.5718H16.5552H16.5385H16.5218H16.5049H16.488H16.4711H16.454H16.4369H16.4198H16.4026H16.3853H16.3679H16.3505H16.333H16.3154H16.2978H16.2801H16.2624H16.2446H16.2267H16.2088H16.1908H16.1727H16.1546H16.1364H16.1182H16.0999H16.0815H16.0631H16.0446H16.0261H16.0075H15.9888H15.9701H15.9513H15.9325H15.9136H15.8946H15.8756H15.8565H15.8374H15.8182H15.7989H15.7796H15.7603H15.7409H15.7214H15.7019H15.6823H15.6626H15.6429H15.6232H15.6034H15.5835H15.5636H15.5436H15.5236H15.5036H15.4834H15.4633H15.443H15.4227H15.4024H15.382H15.3616H15.3411H15.3206H15.3H15.2793H15.2586H15.2379H15.2171H15.1963H15.1754H15.1545H15.1335H15.1125H15.0914H15.0702H15.0491H15.0279H15.0066H14.9853H14.9639H14.9425H14.921H14.8996H14.878H14.8564H14.8348H14.8131H14.7914H14.7696H14.7478H14.726H14.7041H14.6821H14.6601H14.6381H14.6161H14.5939H14.5718H14.5496H14.5274H14.5051H14.4828H14.4604H14.438H14.4156H14.3931H14.3706H14.3481H14.3255H14.3029H14.2802H14.2575H14.2347H14.212H14.1891H14.1663H14.1434H14.1205H14.0975H14.0745H14.0515H14.0284H14.0053H13.9822H13.959H13.9358H13.9125H13.8893H13.866H13.8426H13.8192H13.7958H13.7724H13.7489H13.7254H13.7019H13.6783H13.6547H13.6311H13.6074H13.5837H13.56H13.5363H13.5125H13.4887H13.4649H13.441H13.4171H13.3932H13.3692H13.3452H13.3212H13.2972H13.2731H13.249H13.2249H13.2008H13.1766H13.1524H13.1282H13.104H13.0797H13.0554H13.0311H13.0068H12.9824H12.958H12.9336H12.9092H12.8847H12.8602H12.8357H12.8112H12.7867H12.7621H12.7375H12.7129H12.6883H12.6636H12.639H12.6143H12.5896H12.5649H12.5401H12.5153H12.4906H12.4658H12.4409H12.4161H12.3912H12.3664H12.3415H12.3166H12.2917H12.2667H12.2418H12.2168H12.1918H12.1668H12.1418H12.1168H12.0917H12.0667H12.0416H12.0165H11.9914H11.9663H11.9412H11.916H11.8909H11.8657H11.8405H11.8153H11.7902H11.7649H11.7397H11.7145H11.6893H11.664H11.6388H11.6135H11.5882H11.5629H11.5376H11.5123H11.487H11.4617H11.4364H11.411H11.3857H11.3604H11.335H11.3096H11.2843H11.2589H11.2335H11.2081H11.1828H11.1574H11.132H11.1066H11.0812H11.0558H11.0303H11.0049H10.9795H10.9541H10.9287H10.9033H10.8778H10.8524H10.827H10.8015H10.7761H10.7507H10.7253H10.6998H10.6744H10.649H10.6235H10.5981H10.5727H10.5473H10.5218H10.4964H10.471H10.4456H10.4202H10.3948H10.3694H10.3439H10.3186H10.2932H10.2678H10.2424H10.217H10.1916H10.1663H10.1409H10.1155H10.0902H10.0648H10.0395H10.0142H9.9889H9.9635H9.9382H9.9129H9.8876H9.8624H9.8371H9.8118H9.7866H9.7613H9.7361H9.7109H9.6857H9.6605H9.6353H9.6101H9.5849H9.5598H9.5346H9.5095H9.4844H9.4592H9.4342H9.4091H9.384H9.359H9.3339H9.3089H9.2839H9.2589H9.2339H9.2089H9.184H9.1591H9.1341H9.1093H9.0844H9.0595H9.0347H9.0098H8.985H8.9602H8.9354H8.9107H8.8859H8.8612H8.8365H8.8118H8.7872H8.7625H8.7379H8.7133H8.6888H8.6642H8.6397H8.6151H8.5907H8.5662H8.5417H8.5173H8.4929H8.4685H8.4442H8.4199H8.3956H8.3713H8.347H8.3228H8.2986H8.2744H8.2502H8.2261H8.202H8.1779H8.1539H8.1299H8.1059H8.0819H8.058H8.0341H8.0102H7.9863H7.9625H7.9387H7.9149H7.8912H7.8675H7.8438H7.8202H7.7965H7.773H7.7494H7.7259H7.7024H7.6789H7.6555H7.6321H7.6088H7.5854H7.5621H7.5389H7.5156H7.4924H7.4693H7.4462H7.4231H7.4H7.377H7.354H7.3311H7.3082H7.2853H7.2624H7.2396H7.2169H7.1942H7.1715H7.1488H7.1262H7.1036H7.0811H7.0586H7.0361H7.0137H6.9914H6.969H6.9467H6.9245H6.9023H6.8801H6.8579H6.8359H6.8138H6.7918H6.7698H6.7479H6.726H6.7042H6.6824H6.6607H6.639H6.6173H6.5957H6.5741H6.5526H6.5311H6.5097H6.4883H6.467H6.4457H6.4244H6.4032H6.3821H6.361H6.3399H6.3189H6.2979H6.277H6.2561H6.2353H6.2146H6.1938H6.1732H6.1526H6.132H6.1115H6.091H6.0706H6.0502H6.0299H6.0097H5.98944H5.96929H5.94919H5.92914H5.90914H5.8892H5.86931H5.84947H5.82969H5.80997H5.7903H5.77068H5.75112H5.73161H5.71216H5.69277H5.67343H5.65415H5.63493H5.61576H5.59665H5.5776H5.55861H5.53967H5.5208C4.15692 2.25 3.37688 2.80148 2.91281 3.44603ZM11.2348 5.28799L10.827 6.64399H12.7828L13.2772 5L14.2348 5.28799L13.827 6.64399H15V7.644H13.5263L13.2256 8.644H15V9.644H12.9249L12.4788 11.1272L11.5212 10.8392L11.8806 9.644H9.9249L9.4788 11.1272L8.5212 10.8392L8.8806 9.644H8V8.644H9.1813L9.4821 7.644H8V6.64399H9.7828L10.2772 5L11.2348 5.28799ZM12.1813 8.644L12.4821 7.644H10.5263L10.2256 8.644H12.1813ZM20.5 18.9241C20.5 21.3494 18.5034 23.3481 16 23.3481C13.4966 23.3481 11.5 21.3494 11.5 18.9241C11.5 16.4987 13.4966 14.5 16 14.5C18.5034 14.5 20.5 16.4987 20.5 18.9241ZM22 18.9241C22 22.1958 19.3137 24.8481 16 24.8481C12.6863 24.8481 10 22.1958 10 18.9241C10 15.6523 12.6863 13 16 13C19.3137 13 22 15.6523 22 18.9241ZM14.5303 21.5303L15.9974 20.0632L17.4574 21.5374L18.5232 20.4819L17.0581 19.0026L18.5303 17.5303L17.4697 16.4697L16.0026 17.9368L14.5426 16.4626L13.4768 17.5181L14.9419 18.9974L13.4697 20.4697L14.5303 21.5303Z"
      fill="#D84A35"
    />
  </svg>
);

export const IconAssignUser = () => (
  <svg
    width="21"
    height="18"
    viewBox="0 0 21 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.364 11.636C17.3837 10.6558 16.217 9.93013 14.9439 9.49085C16.3074 8.55179 17.2031 6.9802 17.2031 5.20312C17.2031 2.33413 14.869 0 12 0C9.131 0 6.79688 2.33413 6.79688 5.20312C6.79688 6.9802 7.69262 8.55179 9.05609 9.49085C7.78308 9.93013 6.61631 10.6558 5.63605 11.636C3.93618 13.3359 3 15.596 3 18H4.40625C4.40625 13.8128 7.81279 10.4062 12 10.4062C16.1872 10.4062 19.5938 13.8128 19.5938 18H21C21 15.596 20.0638 13.3359 18.364 11.636ZM12 9C9.90641 9 8.20312 7.29675 8.20312 5.20312C8.20312 3.1095 9.90641 1.40625 12 1.40625C14.0936 1.40625 15.7969 3.1095 15.7969 5.20312C15.7969 7.29675 14.0936 9 12 9Z"
      fill="white"
    />
    <path d="M0 8.8L6 8.8" stroke="white" stroke-width="1.2" />
    <path
      d="M5.57575 9.13259C5.81006 9.36691 6.18996 9.36691 6.42428 9.13259C6.65859 8.89828 6.65859 8.51838 6.42428 8.28406L5.57575 9.13259ZM2.86742 6.42426L5.57575 9.13259L6.42428 8.28406L3.71595 5.57574L2.86742 6.42426Z"
      fill="white"
    />
    <path
      d="M6.42428 9.13258C6.65859 8.89826 6.65859 8.51837 6.42428 8.28405C6.18996 8.04974 5.81006 8.04974 5.57575 8.28405L6.42428 9.13258ZM3.71595 11.8409L6.42428 9.13258L5.57575 8.28405L2.86742 10.9924L3.71595 11.8409Z"
      fill="white"
    />
  </svg>
);

export const IconIncidentDetails = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.4942 20.9381C13.5169 21.935 15.0623 23 17.0003 23C20.6444 23 22.9025 19.2331 22.9025 19.2331C23.0325 19.0138 23.0325 18.7374 22.9025 18.518C22.9025 18.518 21.4525 16.0987 19.0004 15.1375V1.68752C19.0004 1.30801 18.7017 1 18.3337 1H3.66668C3.29867 1 3 1.30801 3 1.68752V20.2506C3 20.4328 3.07 20.6081 3.19534 20.7367C3.32001 20.8659 3.49001 20.9381 3.66668 20.9381H12.4942ZM21.5291 18.8756C20.9504 19.693 19.3351 21.6263 17.0003 21.6263C14.6656 21.6263 13.0503 19.693 12.4716 18.8756C13.0503 18.0581 14.6656 16.1248 17.0003 16.1248C19.3351 16.1248 20.9504 18.0581 21.5291 18.8756ZM17.0003 16.813C15.8963 16.813 15.0003 17.737 15.0003 18.8756C15.0003 20.0141 15.8963 20.9381 17.0003 20.9381C18.1037 20.9381 19.0004 20.0141 19.0004 18.8756C19.0004 17.737 18.1037 16.813 17.0003 16.813ZM17.0003 18.1873C17.3684 18.1873 17.667 18.496 17.667 18.8756C17.667 19.2551 17.3684 19.5638 17.0003 19.5638C16.6317 19.5638 16.333 19.2551 16.333 18.8756C16.333 18.496 16.6317 18.1873 17.0003 18.1873ZM17.667 14.7931V2.37504H4.33337V19.5631H11.3175C11.1729 19.3582 11.0982 19.2331 11.0982 19.2331C10.9682 19.0138 10.9682 18.7374 11.0982 18.518C11.0982 18.518 13.3563 14.7511 17.0003 14.7511C17.2284 14.7511 17.4504 14.7656 17.667 14.7931ZM7.66678 13.3754H11.0002C11.3682 13.3754 11.6669 13.0674 11.6669 12.6879C11.6669 12.3084 11.3682 12.0003 11.0002 12.0003H7.66678C7.29877 12.0003 7.0001 12.3084 7.0001 12.6879C7.0001 13.0674 7.29877 13.3754 7.66678 13.3754ZM7.66678 10.6253H14.3336C14.7016 10.6253 15.0003 10.3173 15.0003 9.93778C15.0003 9.55827 14.7016 9.25026 14.3336 9.25026H7.66678C7.29877 9.25026 7.0001 9.55827 7.0001 9.93778C7.0001 10.3173 7.29877 10.6253 7.66678 10.6253ZM7.66678 7.87521H14.3336C14.7016 7.87521 15.0003 7.5672 15.0003 7.18769C15.0003 6.80818 14.7016 6.50017 14.3336 6.50017H7.66678C7.29877 6.50017 7.0001 6.80818 7.0001 7.18769C7.0001 7.5672 7.29877 7.87521 7.66678 7.87521Z"
      fill="white"
    />
  </svg>
);

export const IconIncidentResources = () => (
  <svg
    width="22"
    height="25"
    viewBox="0 0 22 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.91281 3.44603C2.4212 4.12881 2.25 4.97068 2.25 5.5208V5.54441V5.56813V5.59195V5.61587V5.6399V5.66403V5.68826V5.7126V5.73703V5.76157V5.7862V5.81094V5.83578V5.86071V5.88575V5.91088V5.9361V5.96143V5.98685V6.01237V6.03798V6.06369V6.08949V6.11539V6.14138V6.16746V6.19364V6.2199V6.24626V6.27271V6.29925V6.32588V6.3526V6.37941V6.40631V6.43329V6.46037V6.48753V6.51477V6.54211V6.56953V6.59703V6.62462V6.65229V6.68005V6.70789V6.73581V6.76382V6.7919V6.82007V6.84832V6.87665V6.90506V6.93354V6.96211V6.99076V7.0195V7.0483V7.0772V7.1061V7.1351V7.1642V7.1934V7.2227V7.252V7.2814V7.3109V7.3404V7.3701V7.3997V7.4295V7.4593V7.4892V7.5192V7.5493V7.5794V7.6095V7.6398V7.6701V7.7005V7.7309V7.7614V7.792V7.8226V7.8534V7.8841V7.915V7.9458V7.9768V8.0078V8.0389V8.07V8.1012V8.1325V8.1638V8.1952V8.2266V8.2581V8.2897V8.3213V8.353V8.3847V8.4165V8.4483V8.4802V8.5121V8.5441V8.5762V8.6083V8.6404V8.6726V8.7049V8.7372V8.7695V8.8019V8.8344V8.8669V8.8994V8.932V8.9647V8.9974V9.0301V9.0629V9.0957V9.1286V9.1615V9.1945V9.2275V9.2605V9.2936V9.3267V9.3599V9.3931V9.4264V9.4597V9.493V9.5264V9.5598V9.5932V9.6267V9.6602V9.6938V9.7274V9.761V9.7947V9.8284V9.8621V9.8959V9.9297V9.9635V9.9974V10.0313V10.0652V10.0991V10.1331V10.1671V10.2012V10.2353V10.2694V10.3035V10.3376V10.3718V10.406V10.4403V10.4745V10.5088V10.5431V10.5775V10.6118V10.6462V10.6806V10.715V10.7495V10.7839V10.8184V10.8529V10.8874V10.922V10.9565V10.9911V11.0257V11.0603V11.095V11.1296V11.1643V11.1989V11.2336V11.2683V11.303V11.3378V11.3725V11.4073V11.442V11.4768V11.5116V11.5464V11.5812V11.616V11.6508V11.6857V11.7205V11.7554V11.7902V11.8251V11.86V11.8948V11.9297V11.9646V11.9995V12.0343V12.0692V12.1041V12.139V12.1739V12.2088V12.2437V12.2786V12.3135V12.3484V12.3833V12.4182V12.4531V12.488V12.5228V12.5577V12.5926V12.6274V12.6623V12.6972V12.732V12.7669V12.8017V12.8365V12.8713V12.9061V12.9409V12.9757V13.0105V13.0453V13.08V13.1148V13.1495V13.1842V13.2189V13.2536V13.2883V13.323V13.3576V13.3923V13.4269V13.4615V13.496V13.5306V13.5652V13.5997V13.6342V13.6687V13.7031V13.7376V13.772V13.8064V13.8408V13.8752V13.9095V13.9438V13.9781V14.0124V14.0466V14.0808V14.115V14.1492V14.1833V14.2174V14.2515V14.2855V14.3195V14.3535V14.3875V14.4214V14.4553V14.4892V14.523V14.5568V14.5906V14.6243V14.658V14.6917V14.7254V14.759V14.7925V14.826V14.8595V14.893V14.9264V14.9598V14.9931V15.0264V15.0597V15.0929V15.1261V15.1592V15.1923V15.2254V15.2584V15.2913V15.3243V15.3571V15.39V15.4228V15.4555V15.4882V15.5209V15.5535V15.586V15.6185V15.651V15.6834V15.7158V15.7481V15.7803V15.8125V15.8447V15.8768V15.9089V15.9409V15.9728V16.0047V16.0366V16.0683V16.1001V16.1317V16.1634V16.1949V16.2264V16.2579V16.2893V16.3206V16.3519V16.3831V16.4142V16.4453V16.4763V16.5073V16.5382V16.5691V16.5998V16.6305V16.6612V16.6918V16.7223V16.7528V16.7832V16.8135V16.8437V16.8739V16.904V16.9341V16.9641V16.994V17.0238V17.0536V17.0833V17.1129V17.1425V17.172V17.2014V17.2307V17.26V17.2892V17.3183V17.3474V17.3763V17.4052V17.434V17.4628V17.4914V17.52V17.5485V17.5769V17.6053V17.6335V17.6617V17.6898V17.7178V17.7457V17.7736V17.8014V17.8291V17.8567V17.8842V17.9116V17.939V17.9662V17.9934V18.0205V18.0475V18.0744V18.1012V18.1279V18.1546V18.1811V18.2076V18.234V18.2603V18.2865V18.3125V18.3386V18.3645V18.3903V18.416V18.4416V18.4672V18.4926V18.5179V18.5432V18.5683V18.5934V18.6183V18.6432V18.6679V18.6926V18.7171V18.7416V18.7659V18.7902V18.8143V18.8384V18.8623V18.8862V18.9099V18.9335V18.957V18.9805V19.0038V19.027V19.0501V19.0731V19.096V19.1187V19.1414V19.1639V19.1864V19.2087V19.2309V19.2531V19.2751V19.297V19.3187V19.3404V19.3619V19.3834V19.4047V19.4259V19.447V19.468V19.4888V19.5095V19.5302V19.5507V19.571V19.5913V19.6115V19.6315V19.6514V19.6712V19.6908V19.7104V19.7298V19.7491V19.7682V19.7873V19.8062V19.825V19.8437V19.8622V19.8806V19.8989V19.9171V19.9351V19.953V19.9708V19.9884V20.0059V20.0233V20.0406V20.0577V20.0747V20.0916V20.1083V20.1249V20.1413V20.1577V20.1739V20.1899V20.2058V20.2216V20.2373V20.2528V20.2681V20.2834V20.2985V20.3134V20.3282V20.3429V20.3574V20.3718V20.3861V20.4002V20.4141V20.428V20.4417V20.4552C2.25 21.1033 2.34787 21.6155 2.49344 21.9829C2.64114 22.3556 2.81658 22.5305 2.94123 22.5997C3.04043 22.6549 3.17311 22.6826 3.38919 22.5914C3.62888 22.4901 3.96256 22.2401 4.3464 21.7284C5.11012 20.7101 5.93693 20.2125 6.7774 20.2368C7.5796 20.2599 8.1456 20.7535 8.4294 21.1686L10.1416 23.4516L8.9416 24.3516L7.2184 22.054L7.2059 22.0373L7.1944 22.02C7.0907 21.8645 6.915 21.7414 6.7341 21.7362C6.579 21.7317 6.1612 21.8086 5.5464 22.6284C5.06863 23.2654 4.54072 23.7334 3.97266 23.9732C3.38099 24.223 2.75976 24.2149 2.21277 23.911C1.69122 23.6212 1.32816 23.114 1.09891 22.5354C0.86753 21.9515 0.75 21.2431 0.75 20.4552V20.4417V20.428V20.4141V20.4002V20.3861V20.3718V20.3574V20.3429V20.3282V20.3134V20.2985V20.2834V20.2681V20.2528V20.2373V20.2216V20.2058V20.1899V20.1739V20.1577V20.1413V20.1249V20.1083V20.0916V20.0747V20.0577V20.0406V20.0233V20.0059V19.9884V19.9708V19.953V19.9351V19.9171V19.8989V19.8806V19.8622V19.8437V19.825V19.8062V19.7873V19.7682V19.7491V19.7298V19.7104V19.6908V19.6712V19.6514V19.6315V19.6115V19.5913V19.571V19.5507V19.5302V19.5095V19.4888V19.468V19.447V19.4259V19.4047V19.3834V19.3619V19.3404V19.3187V19.297V19.2751V19.2531V19.2309V19.2087V19.1864V19.1639V19.1414V19.1187V19.096V19.0731V19.0501V19.027V19.0038V18.9805V18.957V18.9335V18.9099V18.8862V18.8623V18.8384V18.8143V18.7902V18.7659V18.7416V18.7171V18.6926V18.6679V18.6432V18.6183V18.5934V18.5683V18.5432V18.5179V18.4926V18.4672V18.4416V18.416V18.3903V18.3645V18.3386V18.3125V18.2865V18.2603V18.234V18.2076V18.1811V18.1546V18.1279V18.1012V18.0744V18.0475V18.0205V17.9934V17.9662V17.939V17.9116V17.8842V17.8567V17.8291V17.8014V17.7736V17.7457V17.7178V17.6898V17.6617V17.6335V17.6053V17.5769V17.5485V17.52V17.4914V17.4628V17.434V17.4052V17.3763V17.3474V17.3183V17.2892V17.26V17.2307V17.2014V17.172V17.1425V17.1129V17.0833V17.0536V17.0238V16.994V16.9641V16.9341V16.904V16.8739V16.8437V16.8135V16.7832V16.7528V16.7223V16.6918V16.6612V16.6305V16.5998V16.5691V16.5382V16.5073V16.4763V16.4453V16.4142V16.3831V16.3519V16.3206V16.2893V16.2579V16.2264V16.1949V16.1634V16.1317V16.1001V16.0683V16.0366V16.0047V15.9728V15.9409V15.9089V15.8768V15.8447V15.8125V15.7803V15.7481V15.7158V15.6834V15.651V15.6185V15.586V15.5535V15.5209V15.4882V15.4555V15.4228V15.39V15.3571V15.3243V15.2913V15.2584V15.2254V15.1923V15.1592V15.1261V15.0929V15.0597V15.0264V14.9931V14.9598V14.9264V14.893V14.8595V14.826V14.7925V14.759V14.7254V14.6917V14.658V14.6243V14.5906V14.5568V14.523V14.4892V14.4553V14.4214V14.3875V14.3535V14.3195V14.2855V14.2515V14.2174V14.1833V14.1492V14.115V14.0808V14.0466V14.0124V13.9781V13.9438V13.9095V13.8752V13.8408V13.8064V13.772V13.7376V13.7031V13.6687V13.6342V13.5997V13.5652V13.5306V13.496V13.4615V13.4269V13.3923V13.3576V13.323V13.2883V13.2536V13.2189V13.1842V13.1495V13.1148V13.08V13.0453V13.0105V12.9757V12.9409V12.9061V12.8713V12.8365V12.8017V12.7669V12.732V12.6972V12.6623V12.6274V12.5926V12.5577V12.5228V12.488V12.4531V12.4182V12.3833V12.3484V12.3135V12.2786V12.2437V12.2088V12.1739V12.139V12.1041V12.0692V12.0343V11.9995V11.9646V11.9297V11.8948V11.86V11.8251V11.7902V11.7554V11.7205V11.6857V11.6508V11.616V11.5812V11.5464V11.5116V11.4768V11.442V11.4073V11.3725V11.3378V11.303V11.2683V11.2336V11.1989V11.1643V11.1296V11.095V11.0603V11.0257V10.9911V10.9565V10.922V10.8874V10.8529V10.8184V10.7839V10.7495V10.715V10.6806V10.6462V10.6118V10.5775V10.5431V10.5088V10.4745V10.4403V10.406V10.3718V10.3376V10.3035V10.2694V10.2353V10.2012V10.1671V10.1331V10.0991V10.0652V10.0313V9.9974V9.9635V9.9297V9.8959V9.8621V9.8284V9.7947V9.761V9.7274V9.6938V9.6602V9.6267V9.5932V9.5598V9.5264V9.493V9.4597V9.4264V9.3931V9.3599V9.3267V9.2936V9.2605V9.2275V9.1945V9.1615V9.1286V9.0957V9.0629V9.0301V8.9974V8.9647V8.932V8.8994V8.8669V8.8344V8.8019V8.7695V8.7372V8.7049V8.6726V8.6404V8.6083V8.5762V8.5441V8.5121V8.4802V8.4483V8.4165V8.3847V8.353V8.3213V8.2897V8.2581V8.2266V8.1952V8.1638V8.1325V8.1012V8.07V8.0389V8.0078V7.9768V7.9458V7.915V7.8841V7.8534V7.8226V7.792V7.7614V7.7309V7.7005V7.6701V7.6398V7.6095V7.5794V7.5493V7.5192V7.4892V7.4593V7.4295V7.3997V7.3701V7.3404V7.3109V7.2814V7.252V7.2227V7.1934V7.1642V7.1351V7.1061V7.0772V7.0483V7.0195V6.99076V6.96211V6.93354V6.90506V6.87665V6.84832V6.82007V6.7919V6.76382V6.73581V6.70789V6.68005V6.65229V6.62462V6.59703V6.56953V6.54211V6.51477V6.48753V6.46037V6.43329V6.40631V6.37941V6.3526V6.32588V6.29925V6.27271V6.24626V6.2199V6.19364V6.16746V6.14138V6.11539V6.08949V6.06369V6.03798V6.01237V5.98685V5.96143V5.9361V5.91088V5.88575V5.86071V5.83578V5.81094V5.7862V5.76157V5.73703V5.7126V5.68826V5.66403V5.6399V5.61587V5.59195V5.56813V5.54441V5.5208C0.75 4.73065 0.98088 3.56212 1.69551 2.56957C2.43768 1.53878 3.66804 0.75 5.5208 0.75H5.53967H5.55861H5.5776H5.59665H5.61576H5.63493H5.65415H5.67343H5.69277H5.71216H5.73161H5.75112H5.77068H5.7903H5.80997H5.82969H5.84947H5.86931H5.8892H5.90914H5.92914H5.94919H5.96929H5.98944H6.0097H6.0299H6.0502H6.0706H6.091H6.1115H6.132H6.1526H6.1732H6.1938H6.2146H6.2353H6.2561H6.277H6.2979H6.3189H6.3399H6.3609H6.3821H6.4032H6.4244H6.4457H6.467H6.4883H6.5097H6.5311H6.5526H6.5741H6.5957H6.6173H6.639H6.6607H6.6824H6.7042H6.726H6.7479H6.7698H6.7918H6.8138H6.8359H6.8579H6.8801H6.9023H6.9245H6.9467H6.969H6.9914H7.0137H7.0361H7.0586H7.0811H7.1036H7.1262H7.1488H7.1715H7.1942H7.2169H7.2396H7.2624H7.2853H7.3082H7.3311H7.354H7.377H7.4H7.4231H7.4462H7.4693H7.4924H7.5156H7.5389H7.5621H7.5854H7.6088H7.6321H7.6555H7.6789H7.7024H7.7259H7.7494H7.773H7.7965H7.8202H7.8438H7.8675H7.8912H7.9149H7.9387H7.9625H7.9863H8.0102H8.0341H8.058H8.0819H8.1059H8.1299H8.1539H8.1779H8.202H8.2261H8.2502H8.2744H8.2986H8.3228H8.347H8.3713H8.3956H8.4199H8.4442H8.4685H8.4929H8.5173H8.5417H8.5662H8.5907H8.6151H8.6397H8.6642H8.6888H8.7133H8.7379H8.7625H8.7872H8.8118H8.8365H8.8612H8.8859H8.9107H8.9354H8.9602H8.985H9.0098H9.0347H9.0595H9.0844H9.1093H9.1341H9.1591H9.184H9.2089H9.2339H9.2589H9.2839H9.3089H9.3339H9.359H9.384H9.4091H9.4342H9.4592H9.4844H9.5095H9.5346H9.5598H9.5849H9.6101H9.6353H9.6605H9.6857H9.7109H9.7361H9.7613H9.7866H9.8118H9.8371H9.8624H9.8876H9.9129H9.9382H9.9635H9.9889H10.0142H10.0395H10.0648H10.0902H10.1155H10.1409H10.1663H10.1916H10.217H10.2424H10.2678H10.2932H10.3186H10.3439H10.3694H10.3948H10.4202H10.4456H10.471H10.4964H10.5218H10.5473H10.5727H10.5981H10.6235H10.649H10.6744H10.6998H10.7253H10.7507H10.7761H10.8015H10.827H10.8524H10.8778H10.9033H10.9287H10.9541H10.9795H11.0049H11.0303H11.0558H11.0812H11.1066H11.132H11.1574H11.1828H11.2081H11.2335H11.2589H11.2843H11.3096H11.335H11.3604H11.3857H11.411H11.4364H11.4617H11.487H11.5123H11.5376H11.5629H11.5882H11.6135H11.6388H11.664H11.6893H11.7145H11.7397H11.7649H11.7902H11.8153H11.8405H11.8657H11.8909H11.916H11.9412H11.9663H11.9914H12.0165H12.0416H12.0667H12.0917H12.1168H12.1418H12.1668H12.1918H12.2168H12.2418H12.2667H12.2917H12.3166H12.3415H12.3664H12.3912H12.4161H12.4409H12.4658H12.4906H12.5153H12.5401H12.5649H12.5896H12.6143H12.639H12.6636H12.6883H12.7129H12.7375H12.7621H12.7867H12.8112H12.8357H12.8602H12.8847H12.9092H12.9336H12.958H12.9824H13.0068H13.0311H13.0554H13.0797H13.104H13.1282H13.1524H13.1766H13.2008H13.2249H13.249H13.2731H13.2972H13.3212H13.3452H13.3692H13.3932H13.4171H13.441H13.4649H13.4887H13.5125H13.5363H13.56H13.5837H13.6074H13.6311H13.6547H13.6783H13.7019H13.7254H13.7489H13.7724H13.7958H13.8192H13.8426H13.866H13.8893H13.9125H13.9358H13.959H13.9822H14.0053H14.0284H14.0515H14.0745H14.0975H14.1205H14.1434H14.1663H14.1891H14.212H14.2347H14.2575H14.2802H14.3029H14.3255H14.3481H14.3706H14.3931H14.4156H14.438H14.4604H14.4828H14.5051H14.5274H14.5496H14.5718H14.5939H14.6161H14.6381H14.6601H14.6821H14.7041H14.726H14.7478H14.7696H14.7914H14.8131H14.8348H14.8564H14.878H14.8995H14.921H14.9425H14.9639H14.9853H15.0066H15.0279H15.0491H15.0702H15.0914H15.1125H15.1335H15.1545H15.1754H15.1963H15.2171H15.2379H15.2586H15.2793H15.3H15.3206H15.3411H15.3616H15.382H15.4024H15.4227H15.443H15.4633H15.4834H15.5036H15.5236H15.5436H15.5636H15.5835H15.6034H15.6232H15.6429H15.6626H15.6823H15.7018H15.7214H15.7409H15.7603H15.7796H15.7989H15.8182H15.8374H15.8565H15.8756H15.8946H15.9136H15.9325H15.9513H15.9701H15.9888H16.0075H16.0261H16.0446H16.0631H16.0815H16.0999H16.1182H16.1364H16.1546H16.1727H16.1908H16.2088H16.2267H16.2446H16.2624H16.2801H16.2978H16.3154H16.333H16.3505H16.3679H16.3853H16.4026H16.4198H16.4369H16.454H16.4711H16.488H16.5049H16.5218H16.5385H16.5552H16.5718H16.5884H16.6049H16.6213H16.6377H16.6539H16.6702H16.6863H16.7024H16.7184H16.7343H16.7502H16.766H16.7817H16.7973H16.8129H16.8284H16.8439H16.8592H16.8745H16.8897H16.9049H16.9199H16.9349H16.9498H16.9647H16.9795H16.9942H17.0088C19.2834 0.75 20.5365 1.75108 21.1748 2.8453C21.4797 3.3679 21.6289 3.88388 21.703 4.26474C21.7403 4.45662 21.7593 4.61856 21.7691 4.73603C21.774 4.79489 21.7767 4.84299 21.778 4.87855C21.7787 4.89634 21.7791 4.91103 21.7793 4.9224L21.7795 4.93695L21.7796 4.94232V4.94452C21.7796 4.94498 21.7796 4.9464 21.0296 4.9464H21.7796V20.4552H20.2796V4.95031L20.2792 4.93684C20.2786 4.92191 20.2773 4.89596 20.2743 4.86059C20.2684 4.78959 20.2561 4.68228 20.2305 4.55105C20.179 4.28572 20.0769 3.94009 19.8791 3.6011C19.5122 2.97212 18.755 2.25 17.0088 2.25H16.9942H16.9795H16.9647H16.9498H16.9349H16.9199H16.9049H16.8897H16.8745H16.8592H16.8439H16.8284H16.8129H16.7973H16.7817H16.766H16.7502H16.7343H16.7184H16.7024H16.6863H16.6702H16.6539H16.6377H16.6213H16.6049H16.5884H16.5718H16.5552H16.5385H16.5218H16.5049H16.488H16.4711H16.454H16.4369H16.4198H16.4026H16.3853H16.3679H16.3505H16.333H16.3154H16.2978H16.2801H16.2624H16.2446H16.2267H16.2088H16.1908H16.1727H16.1546H16.1364H16.1182H16.0999H16.0815H16.0631H16.0446H16.0261H16.0075H15.9888H15.9701H15.9513H15.9325H15.9136H15.8946H15.8756H15.8565H15.8374H15.8182H15.7989H15.7796H15.7603H15.7409H15.7214H15.7018H15.6823H15.6626H15.6429H15.6232H15.6034H15.5835H15.5636H15.5436H15.5236H15.5036H15.4834H15.4633H15.443H15.4227H15.4024H15.382H15.3616H15.3411H15.3206H15.3H15.2793H15.2586H15.2379H15.2171H15.1963H15.1754H15.1545H15.1335H15.1125H15.0914H15.0702H15.0491H15.0279H15.0066H14.9853H14.9639H14.9425H14.921H14.8995H14.878H14.8564H14.8348H14.8131H14.7914H14.7696H14.7478H14.726H14.7041H14.6821H14.6601H14.6381H14.6161H14.5939H14.5718H14.5496H14.5274H14.5051H14.4828H14.4604H14.438H14.4156H14.3931H14.3706H14.3481H14.3255H14.3029H14.2802H14.2575H14.2347H14.212H14.1891H14.1663H14.1434H14.1205H14.0975H14.0745H14.0515H14.0284H14.0053H13.9822H13.959H13.9358H13.9125H13.8893H13.866H13.8426H13.8192H13.7958H13.7724H13.7489H13.7254H13.7019H13.6783H13.6547H13.6311H13.6074H13.5837H13.56H13.5363H13.5125H13.4887H13.4649H13.441H13.4171H13.3932H13.3692H13.3452H13.3212H13.2972H13.2731H13.249H13.2249H13.2008H13.1766H13.1524H13.1282H13.104H13.0797H13.0554H13.0311H13.0068H12.9824H12.958H12.9336H12.9092H12.8847H12.8602H12.8357H12.8112H12.7867H12.7621H12.7375H12.7129H12.6883H12.6636H12.639H12.6143H12.5896H12.5649H12.5401H12.5153H12.4906H12.4658H12.4409H12.4161H12.3912H12.3664H12.3415H12.3166H12.2917H12.2667H12.2418H12.2168H12.1918H12.1668H12.1418H12.1168H12.0917H12.0667H12.0416H12.0165H11.9914H11.9663H11.9412H11.916H11.8909H11.8657H11.8405H11.8153H11.7902H11.7649H11.7397H11.7145H11.6893H11.664H11.6388H11.6135H11.5882H11.5629H11.5376H11.5123H11.487H11.4617H11.4364H11.411H11.3857H11.3604H11.335H11.3096H11.2843H11.2589H11.2335H11.2081H11.1828H11.1574H11.132H11.1066H11.0812H11.0558H11.0303H11.0049H10.9795H10.9541H10.9287H10.9033H10.8778H10.8524H10.827H10.8015H10.7761H10.7507H10.7253H10.6998H10.6744H10.649H10.6235H10.5981H10.5727H10.5473H10.5218H10.4964H10.471H10.4456H10.4202H10.3948H10.3694H10.3439H10.3186H10.2932H10.2678H10.2424H10.217H10.1916H10.1663H10.1409H10.1155H10.0902H10.0648H10.0395H10.0142H9.9889H9.9635H9.9382H9.9129H9.8876H9.8624H9.8371H9.8118H9.7866H9.7613H9.7361H9.7109H9.6857H9.6605H9.6353H9.6101H9.5849H9.5598H9.5346H9.5095H9.4844H9.4592H9.4342H9.4091H9.384H9.359H9.3339H9.3089H9.2839H9.2589H9.2339H9.2089H9.184H9.1591H9.1341H9.1093H9.0844H9.0595H9.0347H9.0098H8.985H8.9602H8.9354H8.9107H8.8859H8.8612H8.8365H8.8118H8.7872H8.7625H8.7379H8.7133H8.6888H8.6642H8.6397H8.6151H8.5907H8.5662H8.5417H8.5173H8.4929H8.4685H8.4442H8.4199H8.3956H8.3713H8.347H8.3228H8.2986H8.2744H8.2502H8.2261H8.202H8.1779H8.1539H8.1299H8.1059H8.0819H8.058H8.0341H8.0102H7.9863H7.9625H7.9387H7.9149H7.8912H7.8675H7.8438H7.8202H7.7965H7.773H7.7494H7.7259H7.7024H7.6789H7.6555H7.6321H7.6088H7.5854H7.5621H7.5389H7.5156H7.4924H7.4693H7.4462H7.4231H7.4H7.377H7.354H7.3311H7.3082H7.2853H7.2624H7.2396H7.2169H7.1942H7.1715H7.1488H7.1262H7.1036H7.0811H7.0586H7.0361H7.0137H6.9914H6.969H6.9467H6.9245H6.9023H6.8801H6.8579H6.8359H6.8138H6.7918H6.7698H6.7479H6.726H6.7042H6.6824H6.6607H6.639H6.6173H6.5957H6.5741H6.5526H6.5311H6.5097H6.4883H6.467H6.4457H6.4244H6.4032H6.3821H6.3609H6.3399H6.3189H6.2979H6.277H6.2561H6.2353H6.2146H6.1938H6.1732H6.1526H6.132H6.1115H6.091H6.0706H6.0502H6.0299H6.0097H5.98944H5.96929H5.94919H5.92914H5.90914H5.8892H5.86931H5.84947H5.82969H5.80997H5.7903H5.77068H5.75112H5.73161H5.71216H5.69277H5.67343H5.65415H5.63493H5.61576H5.59665H5.5776H5.55861H5.53967H5.5208C4.15692 2.25 3.37688 2.80148 2.91281 3.44603Z"
      fill="white"
    />
    <path
      d="M14.5379 5.23377C16.0474 6.84688 15.9634 9.37798 14.3503 10.8875C14.2739 10.959 14.1889 11.0302 14.1083 11.0972C12.663 9.77788 10.4537 9.70458 8.92418 10.9252C8.84818 10.853 8.76808 10.7764 8.69658 10.7C7.18708 9.08688 7.27108 6.55571 8.88418 5.0462C10.4973 3.5367 13.0284 3.62067 14.5379 5.23377Z"
      stroke="white"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M14.3035 12.3001C15.813 13.9132 15.7291 16.4443 14.116 17.9538C12.5029 19.4633 9.9717 19.3794 8.4622 17.7663C6.9527 16.1532 7.0367 13.622 8.6498 12.1125C8.7262 12.041 8.8113 11.9698 8.8918 11.9029C10.4213 10.6823 12.6307 10.7556 14.0759 12.0749C14.1519 12.147 14.2321 12.2237 14.3035 12.3001Z"
      stroke="white"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const IconIssueClose = () => (
  <svg
    width="18"
    height="19"
    viewBox="0 0 18 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="mask0_2079_2"
      maskUnits="userSpaceOnUse"
      x="0"
      y="0"
      width="18"
      height="19"
    >
      <path d="M18 0.5H0V18.5H18V0.5Z" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_2079_2)">
      <path d="M0 0.5L18 18.5" stroke="#ADADAD" stroke-width="1.5" />
      <path d="M18 0.500084L0 18.5001" stroke="#ADADAD" stroke-width="1.5" />
    </g>
  </svg>
);
export const IconReRunBlue = () => (
  <svg
    width="20"
    height="22"
    viewBox="0 0 20 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.52783 0.469668C9.82073 0.762558 9.82073 1.23744 9.52783 1.53033L8.21842 2.83973C8.77242 2.74813 9.35903 2.69515 9.98293 2.69515C15.3125 2.69515 19.6377 7.02026 19.6377 12.35C19.6377 17.6796 15.3125 22.0048 9.98293 22.0048C4.65323 22.0048 0.328125 17.6796 0.328125 12.35C0.328125 10.3615 0.927395 8.52656 1.94889 6.98466C2.17765 6.63936 2.64303 6.54486 2.98834 6.77366C3.33365 7.00236 3.42813 7.46776 3.19936 7.81306C2.33303 9.12076 1.82812 10.6696 1.82812 12.35C1.82812 16.8512 5.48173 20.5048 9.98293 20.5048C14.4841 20.5048 18.1377 16.8512 18.1377 12.35C18.1377 7.84876 14.4841 4.19515 9.98293 4.19515C9.10173 4.19515 8.30562 4.31754 7.56962 4.51142L9.52783 6.46966C9.82073 6.76256 9.82073 7.23746 9.52783 7.53036C9.23493 7.82326 8.76013 7.82326 8.46723 7.53036L5.46723 4.53033C5.17433 4.23744 5.17433 3.76256 5.46723 3.46967C5.46713 3.46979 5.46733 3.46955 5.46723 3.46967L8.46723 0.469668C8.76013 0.176777 9.23493 0.176777 9.52783 0.469668Z"
      style={{ fill: "var(--dark-blue)" }}
    />
    <path
      d="M14.0537 11.8691L8.76705 15.0493L8.65625 8.88086L14.0537 11.8691Z"
      style={{ fill: "var(--dark-blue)" }}
    />
  </svg>
);

export const IconReRunWhite = () => (
  <svg
    width="20"
    height="22"
    viewBox="0 0 20 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.52783 0.469668C9.82073 0.762558 9.82073 1.23744 9.52783 1.53033L8.21842 2.83973C8.77242 2.74813 9.35903 2.69515 9.98293 2.69515C15.3125 2.69515 19.6377 7.02026 19.6377 12.35C19.6377 17.6796 15.3125 22.0048 9.98293 22.0048C4.65323 22.0048 0.328125 17.6796 0.328125 12.35C0.328125 10.3615 0.927395 8.52656 1.94889 6.98466C2.17765 6.63936 2.64303 6.54486 2.98834 6.77366C3.33365 7.00236 3.42813 7.46776 3.19936 7.81306C2.33303 9.12076 1.82812 10.6696 1.82812 12.35C1.82812 16.8512 5.48173 20.5048 9.98293 20.5048C14.4841 20.5048 18.1377 16.8512 18.1377 12.35C18.1377 7.84876 14.4841 4.19515 9.98293 4.19515C9.10173 4.19515 8.30562 4.31754 7.56962 4.51142L9.52783 6.46966C9.82073 6.76256 9.82073 7.23746 9.52783 7.53036C9.23493 7.82326 8.76013 7.82326 8.46723 7.53036L5.46723 4.53033C5.17433 4.23744 5.17433 3.76256 5.46723 3.46967C5.46713 3.46979 5.46733 3.46955 5.46723 3.46967L8.46723 0.469668C8.76013 0.176777 9.23493 0.176777 9.52783 0.469668Z"
      fill="#ffffff"
    />
    <path
      d="M14.0537 11.8691L8.76705 15.0493L8.65625 8.88086L14.0537 11.8691Z"
      fill="#ffffff"
    />
  </svg>
);
export const IconNewRunName = () => (
  <svg
    width="24"
    height="22"
    viewBox="0 0 24 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.7833 13.3959C21.9208 13.3959 22.8416 12.5001 22.8416 11.4168V9.58345C22.8416 8.48975 21.9099 7.60434 20.7833 7.60434C18.8224 7.60434 18.0208 6.27094 19.0066 4.63554C19.5699 3.69807 19.2341 2.47932 18.2483 1.93765L16.3741 0.906405C15.5183 0.416825 14.4133 0.708495 13.9041 1.5314L13.7849 1.72933C12.7991 3.36475 11.1958 3.36475 10.2208 1.72933L10.1016 1.5314C9.59237 0.708495 8.48737 0.416825 7.63157 0.906405L5.75739 1.93765C4.77156 2.47932 4.43572 3.69807 4.99906 4.63554C5.98489 6.27094 5.18322 7.60434 3.22239 7.60434C2.08489 7.60434 1.16406 8.50015 1.16406 9.58345V11.4168C1.16406 12.5001 2.08489 13.3959 3.22239 13.3959C5.18322 13.3959 5.98489 14.7293 4.99906 16.3647C4.43572 17.3022 4.77156 18.5209 5.75739 19.0626L7.63157 20.0939C8.48737 20.5834 9.59237 20.2918 10.1016 19.4689L10.2208 19.2709C11.2066 17.6355 12.8099 17.6355 13.7849 19.2709L13.9041 19.4689"
      stroke="white"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.7534 6H11.2533V10.2431L7 10.2161V11.716L11.2533 11.7433V16H12.7534V11.7568L17 11.7839V10.284L12.7534 10.2567V6Z"
      fill="white"
    />
    <path
      d="M23.0968 18.2366L16.9688 21.5611L17.1537 14.5918L23.0968 18.2366Z"
      fill="white"
    />
  </svg>
);
export const IconExistingRunName = () => (
  <svg
    width="24"
    height="22"
    viewBox="0 0 24 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.7833 13.3959C21.9208 13.3959 22.8416 12.5001 22.8416 11.4168V9.58345C22.8416 8.48975 21.9099 7.60434 20.7833 7.60434C18.8224 7.60434 18.0208 6.27094 19.0066 4.63554C19.5699 3.69807 19.2341 2.47932 18.2483 1.93765L16.3741 0.906405C15.5183 0.416825 14.4133 0.708495 13.9041 1.5314L13.7849 1.72933C12.7991 3.36475 11.1958 3.36475 10.2208 1.72933L10.1016 1.5314C9.59237 0.708495 8.48737 0.416825 7.63157 0.906405L5.75739 1.93765C4.77156 2.47932 4.43572 3.69807 4.99906 4.63554C5.98489 6.27094 5.18322 7.60434 3.22239 7.60434C2.08489 7.60434 1.16406 8.50015 1.16406 9.58345V11.4168C1.16406 12.5001 2.08489 13.3959 3.22239 13.3959C5.18322 13.3959 5.98489 14.7293 4.99906 16.3647C4.43572 17.3022 4.77156 18.5209 5.75739 19.0626L7.63157 20.0939C8.48737 20.5834 9.59237 20.2918 10.1016 19.4689L10.2208 19.2709C11.2066 17.6355 12.8099 17.6355 13.7849 19.2709L13.9041 19.4689"
      stroke="white"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M23.0968 18.2366L16.9688 21.5611L17.1537 14.5918L23.0968 18.2366Z"
      fill="white"
    />
    <path
      d="M8 10C8.5 4.5 14 6.3333 14 6.3333M14 6.3333L12.6667 7.3333M14 6.3333C14 6.3333 13.3905 5.5207 13 5"
      stroke="white"
    />
    <path
      d="M16.2152 10C16.625 15.9811 10.125 13.9811 10.125 13.9811M10.125 13.9811L11.3827 12.9735M10.125 13.9811C10.125 13.9811 10.681 14.9108 11.1109 15.4469"
      stroke="white"
    />
  </svg>
);

export const IconIssuesList = () => (
  <svg
    width="23"
    height="23"
    viewBox="0 0 23 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.3867 0.617679C10.6906 0.617679 1.19706 0.616729 0.649273 0.61747C0.648401 1.2426 0.648401 1.60492 0.648401 2.19758V20.0744C0.648401 20.6671 0.636787 21.5402 0.641946 22.309C1.08543 22.309 2.57711 22.309 3.27326 22.309H19.0225C19.7186 22.309 20.321 22.309 20.8786 22.309C21.4361 22.309 21.6474 22.309 21.6474 22.309V15.0033"
      stroke="black"
      stroke-width="0.75"
    />
    <path d="M15.9616 15.0547H5.89844" stroke="black" stroke-width="0.75" />
    <path d="M15.9616 18.4551H5.89844" stroke="black" stroke-width="0.75" />
    <path d="M16.7266 7.30746H15.9766V5.03906H16.7266V7.30746Z" fill="black" />
    <path
      d="M16.3479 8.72265C16.1408 8.7212 15.9733 8.54988 15.9766 8.34293C15.9799 8.14109 16.1492 7.97338 16.3507 7.97266C16.5556 7.97194 16.7273 8.14456 16.7266 8.35021C16.7257 8.55796 16.5567 8.72416 16.3479 8.72265Z"
      fill="black"
    />
    <path
      d="M16.8911 1.00391L21.9114 9.69922C22.1519 10.1159 21.8512 10.6367 21.3701 10.6367H11.3296C10.8485 10.6367 10.5478 10.1159 10.7884 9.69922L15.8086 1.00391C16.0492 0.587239 16.6506 0.58724 16.8911 1.00391Z"
      stroke="black"
      stroke-width="0.75"
    />
  </svg>
);
export const IconResearchQuery = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.92 12.44C16.92 12.32 16.88 12.24 16.8 12.16L15.12 10.48C14.96 10.32 14.72 10.32 14.56 10.48L13.8 11.24C13.72 11.2 13.68 11.2 13.64 11.16V10C13.64 9.76 13.48 9.6 13.24 9.6H10.84C10.6 9.6 10.44 9.76 10.44 10V11.08C10.4 11.12 10.32 11.12 10.28 11.16L9.52 10.4C9.44 10.32 9.36 10.28 9.24 10.28C9.12 10.28 9.04 10.32 8.96 10.4L7.28 12.08C7.2 12.16 7.16 12.24 7.16 12.36C7.16 12.48 7.2 12.56 7.28 12.64L8.04 13.4C8 13.48 8 13.52 7.96 13.56H6.8C6.56 13.56 6.4 13.72 6.4 13.96V16.36C6.4 16.6 6.56 16.76 6.8 16.76H7.92C7.96 16.8 7.96 16.88 8 16.92L7.24 17.68C7.16 17.76 7.12 17.84 7.12 17.96C7.12 18.08 7.16 18.16 7.24 18.24L8.92 19.92C9.08 20.08 9.32 20.08 9.48 19.92L10.24 19.16C10.32 19.2 10.36 19.2 10.4 19.24V20.4C10.4 20.64 10.56 20.8 10.8 20.8H13.2C13.44 20.8 13.6 20.64 13.6 20.4V19.28C13.64 19.24 13.72 19.24 13.76 19.2L14.52 19.96C14.68 20.12 14.92 20.12 15.08 19.96L16.76 18.28C16.84 18.2 16.88 18.12 16.88 18C16.88 17.88 16.84 17.8 16.76 17.72L16 16.96C16.04 16.92 16.04 16.84 16.08 16.8H17.2C17.44 16.8 17.6 16.64 17.6 16.4V14C17.6 13.76 17.44 13.6 17.2 13.6H16.08C16.04 13.56 16.04 13.48 16 13.44L16.8 12.72C16.88 12.64 16.92 12.56 16.92 12.44ZM16.8 14.4V16H15.8C15.6 16 15.44 16.16 15.4 16.32C15.4 16.4 15.24 16.72 15.2 16.8C15.16 16.88 15.12 16.96 15.12 17.04C15.12 17.16 15.16 17.24 15.24 17.32L15.92 18L14.8 19.12L14.12 18.44C14.04 18.36 13.96 18.32 13.84 18.32C13.76 18.32 13.68 18.36 13.6 18.4C13.52 18.44 13.2 18.56 13.12 18.6C12.92 18.64 12.8 18.8 12.8 19V20H11.2V19C11.2 18.88 11.16 18.8 11.08 18.72C11 18.6 10.32 18.32 10.16 18.32C10.04 18.32 9.96 18.36 9.88 18.44L9.2 19.12L8.08 18L8.76 17.32C8.84 17.24 8.88 17.16 8.88 17.04C8.88 16.96 8.84 16.88 8.8 16.8C8.76 16.72 8.64 16.4 8.6 16.32C8.56 16.12 8.4 16 8.2 16H7.2V14.4H8.2C8.32 14.4 8.4 14.36 8.48 14.28C8.6 14.2 8.88 13.52 8.88 13.36C8.88 13.24 8.84 13.16 8.76 13.08L8.08 12.4L9.2 11.28L9.88 11.96C9.96 12.04 10.04 12.08 10.16 12.08C10.24 12.08 10.32 12.04 10.4 12C10.48 11.96 10.8 11.84 10.88 11.8C11.08 11.76 11.2 11.6 11.2 11.4V10.4H12.8V11.4C12.8 11.52 12.84 11.6 12.92 11.68C13 11.8 13.68 12.08 13.84 12.08C13.96 12.08 14.04 12.04 14.12 11.96L14.8 11.28L15.92 12.4L15.24 13.08C15.16 13.16 15.12 13.24 15.12 13.36C15.12 13.44 15.16 13.52 15.2 13.6C15.24 13.68 15.36 14 15.4 14.08C15.44 14.28 15.6 14.4 15.8 14.4H16.8Z"
      fill="black"
    />
    <path
      d="M12 13.6C11.12 13.6 10.4 14.32 10.4 15.2C10.4 16.08 11.12 16.8 12 16.8C12.88 16.8 13.6 16.08 13.6 15.2C13.6 14.32 12.88 13.6 12 13.6ZM12 16C11.56 16 11.2 15.64 11.2 15.2C11.2 14.76 11.56 14.4 12 14.4C12.44 14.4 12.8 14.76 12.8 15.2C12.8 15.64 12.44 16 12 16Z"
      fill="black"
    />
    <path
      d="M18.84 11C19.6 12.24 20 13.68 20 15.2C20 19.6 16.4 23.2 12 23.2C10.36 23.2 8.76 22.68 7.44 21.76L6.96 22.4C8.44 23.44 10.2 24 12 24C16.84 24 20.8 20.04 20.8 15.2C20.8 13.56 20.36 11.96 19.52 10.56L18.84 11Z"
      fill="black"
    />
    <path
      d="M4.76 18.64C4.28 17.6 4 16.4 4 15.2C4 11.4 6.72 8.08 10.48 7.36C10.68 7.32 10.8 7.16 10.8 6.96V2.4H13.2V6.96C13.2 7.16 13.32 7.32 13.52 7.36C14.52 7.56 15.4 7.92 16.2 8.4L16.6 7.72C15.84 7.24 14.96 6.88 14 6.64V2.4H15.2C15.44 2.4 15.6 2.24 15.6 2V0.4C15.6 0.16 15.44 0 15.2 0H8.8C8.56 0 8.4 0.16 8.4 0.4V2C8.4 2.24 8.56 2.4 8.8 2.4H10V6.64C6.04 7.56 3.2 11.12 3.2 15.2C3.2 16.52 3.48 17.84 4.04 18.96L4.76 18.64ZM9.2 0.8H14.8V1.6H9.2V0.8Z"
      fill="black"
    />
    <path
      d="M23.88 9.32L22.68 8.12L22.12 8.68L22.64 9.2H16.4V10H22.64L22.12 10.52L22.68 11.08L23.88 9.88C24.04 9.72 24.04 9.48 23.88 9.32Z"
      fill="black"
    />
    <path
      d="M7.6 20.8V20H1.36L1.88 19.48L1.32 18.92L0.12 20.12C-0.04 20.28 -0.04 20.52 0.12 20.68L1.32 21.88L1.88 21.32L1.36 20.8H7.6Z"
      fill="black"
    />
    <path d="M12.4 8H11.6V8.8H12.4V8Z" fill="black" />
    <path d="M12.4 21.6H11.6V22.4H12.4V21.6Z" fill="black" />
    <path d="M19.2 14.8H18.4V15.6H19.2V14.8Z" fill="black" />
    <path d="M5.6 14.8H4.8V15.6H5.6V14.8Z" fill="black" />
  </svg>
);

export const IconExecuteQuerySvg = () => (
  <svg
    width="19"
    height="18"
    viewBox="0 0 19 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1 3.40002C1 2.07453 3.14938 1 5.80078 1C8.45213 1 10.6016 2.07453 10.6016 3.40002V4.60003C10.6016 5.92553 8.45213 7.00006 5.80078 7.00006C3.14938 7.00006 1 5.92553 1 4.60003V3.40002Z"
      stroke="#196BB4"
    />
    <path
      d="M1 8.19983C1 6.87433 3.14938 5.7998 5.80078 5.7998C8.45213 5.7998 10.6016 6.87433 10.6016 8.19983V9.39984C10.6016 10.7254 8.45213 11.7999 5.80078 11.7999C3.14938 11.7999 1 10.7254 1 9.39984V8.19983Z"
      stroke="#196BB4"
    />
    <path
      d="M1 12.9996C1 11.6741 3.14938 10.5996 5.80078 10.5996C8.45213 10.5996 10.6016 11.6741 10.6016 12.9996V14.1996C10.6016 15.5252 8.45213 16.5997 5.80078 16.5997C3.14938 16.5997 1 15.5252 1 14.1996V12.9996Z"
      stroke="#196BB4"
    />
    <path d="M13 4.60059L19.001 9.40063L13 14.2007V4.60059Z" fill="#196BB4" />
  </svg>
);

export const IconResearchQueryHistorySvg = () => (
  <svg
    width="37"
    height="33"
    viewBox="0 0 37 33"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11 26C16.5228 26 21 21.5228 21 16C21 10.4772 16.5228 6 11 6C5.47715 6 1 10.4772 1 16C1 21.5228 5.47715 26 11 26Z"
      stroke="#1E1E1E"
      stroke-width="2"
    />
    <path d="M17.5 22.5L27 32" stroke="#1E1E1E" stroke-width="2" />
    <path
      d="M26 21C31.5228 21 36 16.5228 36 11C36 5.47715 31.5228 1 26 1C20.4772 1 16 5.47715 16 11C16 16.5228 20.4772 21 26 21Z"
      stroke="#1E1E1E"
      stroke-width="2"
    />
    <path d="M26 11V7" stroke="#1E1E1E" stroke-width="2" />
    <path d="M25 11H30" stroke="#1E1E1E" stroke-width="2" />
  </svg>
);

export const IconEditBase = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="mask0_510_21"
      maskUnits="userSpaceOnUse"
      x="2"
      y="2"
      width="20"
      height="20"
    >
      <path d="M22 2H2V22H22V2Z" fill="white" />
    </mask>
    <g mask="url(#mask0_510_21)">
      <path
        d="M16.6168 3.48055C17.0983 2.99903 17.7514 2.72852 18.4324 2.72852C19.1134 2.72852 19.7664 2.99903 20.248 3.48055C20.7295 3.96207 21 4.61516 21 5.29613C21 5.9771 20.7295 6.63018 20.248 7.1117L7.99283 19.3668L3 20.7285L4.36168 15.7357L16.6168 3.48055Z"
        style={{ stroke: "var(--dark-blue)" }}
      />
    </g>
  </svg>
);

export const IconEyeBase = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="mask0_510_35"
      maskUnits="userSpaceOnUse"
      x="0"
      y="3"
      width="24"
      height="18"
    >
      <path d="M24 3H0V20.7778H24V3Z" fill="white" />
    </mask>
    <g mask="url(#mask0_510_35)">
      <path
        d="M0.890625 11.6464C0.890625 11.6464 4.89118 3.64648 11.8903 3.64648C18.8894 3.64648 22.8899 11.6464 22.8899 11.6464C22.8899 11.6464 18.8904 19.6464 11.8903 19.6464C4.89007 19.6464 0.890625 11.6464 0.890625 11.6464Z"
        style={{ stroke: "var(--dark-blue)" }}
      />
      <path
        d="M11.888 14.8481C13.6555 14.8481 15.0884 13.4153 15.0884 11.6477C15.0884 9.88016 13.6555 8.44727 11.888 8.44727C10.1204 8.44727 8.6875 9.88016 8.6875 11.6477C8.6875 13.4153 10.1204 14.8481 11.888 14.8481Z"
        style={{ stroke: "var(--dark-blue)" }}
      />
    </g>
  </svg>
);

export const IconViewResource = () => (
  <svg
    width="21"
    height="18"
    viewBox="0 0 21 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_511_84)">
      <mask
        id="mask0_511_84"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="21"
        height="18"
      >
        <path d="M21 0H0V18H21V0Z" fill="white" />
      </mask>
      <g mask="url(#mask0_511_84)">
        <path
          d="M14.6964 17.6656C10.8744 17.6656 8.72638 13.4596 8.63637 13.2796C8.60038 13.2136 8.60038 13.1356 8.63637 13.0696C8.72638 12.8896 10.8744 8.68359 14.6964 8.68359C18.5184 8.68359 20.6664 12.8896 20.7564 13.0696C20.7924 13.1356 20.7924 13.2136 20.7564 13.2796C20.6664 13.4596 18.5184 17.6656 14.6964 17.6656ZM9.11638 13.1776C9.47038 13.8256 11.4864 17.1976 14.6964 17.1976C17.9064 17.1976 19.9224 13.8256 20.2764 13.1776C19.9224 12.5296 17.9064 9.15759 14.6964 9.15759C11.4864 9.15759 9.47038 12.5296 9.11638 13.1776Z"
          style={{ fill: "var(--dark-blue)", stroke: "var(--dark-blue)" }}
          stroke-miterlimit="10"
        />
        <path
          d="M14.6958 15.1143C13.6278 15.1143 12.7578 14.2443 12.7578 13.1763C12.7578 12.1083 13.6278 11.2383 14.6958 11.2383C15.7638 11.2383 16.6338 12.1083 16.6338 13.1763C16.6338 14.2443 15.7638 15.1143 14.6958 15.1143ZM14.6958 11.7123C13.8858 11.7123 13.2318 12.3723 13.2318 13.1763C13.2318 13.9803 13.8918 14.6403 14.6958 14.6403C15.4998 14.6403 16.1598 13.9803 16.1598 13.1763C16.1598 12.3723 15.4998 11.7123 14.6958 11.7123Z"
          style={{ fill: "var(--dark-blue)", stroke: "var(--dark-blue)" }}
          stroke-miterlimit="10"
        />
        <path
          d="M11.6523 5.328C13.0323 5.328 14.1483 4.134 14.1483 2.664C14.1483 1.194 13.0323 0 11.6523 0C10.2723 0 9.15625 1.194 9.15625 2.664C9.15625 4.134 10.2723 5.328 11.6523 5.328ZM11.6523 0.852C12.5883 0.852 13.3443 1.662 13.3443 2.658C13.3443 3.654 12.5883 4.464 11.6523 4.464C10.7163 4.464 9.96025 3.654 9.96025 2.658C9.96025 1.662 10.7223 0.852 11.6523 0.852Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M4.3085 5.328C5.6885 5.328 6.80448 4.134 6.80448 2.664C6.80448 1.194 5.6885 0 4.3085 0C2.9285 0 1.8125 1.194 1.8125 2.664C1.8125 4.134 2.9285 5.328 4.3085 5.328ZM4.3085 0.852C5.2445 0.852 6.00048 1.662 6.00048 2.658C6.00048 3.654 5.2445 4.464 4.3085 4.464C3.3725 4.464 2.6165 3.654 2.6165 2.658C2.6165 1.662 3.3785 0.852 4.3085 0.852Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M7.95731 11.7659C5.40131 11.7779 3.25931 13.6859 2.71331 16.2419V16.2779C2.71331 16.2779 2.69531 16.3379 2.69531 16.3679C2.69531 16.6019 2.87531 16.7939 3.09731 16.7939C3.28931 16.7939 3.44531 16.6499 3.48731 16.4579C3.95531 14.2439 5.77931 12.6119 7.95731 12.6119C8.01131 12.6119 8.07131 12.6119 8.12531 12.6179C8.16731 12.5339 8.32931 12.2279 8.60531 11.8019C8.39531 11.7719 8.17931 11.7539 7.95731 11.7539V11.7659Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M14.6498 8.20812C13.8158 7.30212 12.6638 6.73812 11.3798 6.73812C11.1638 6.73812 10.9478 6.75612 10.7318 6.78612C10.7078 6.79212 10.6838 6.79812 10.6658 6.81012C10.2638 5.59809 9.20173 4.74609 7.95373 4.74609C6.70573 4.74609 5.64975 5.59209 5.24775 6.77412V6.79812C5.24775 6.79812 5.22376 6.79212 5.21776 6.78612C5.02576 6.75612 4.80375 6.73812 4.58175 6.73812C2.39775 6.75012 0.573751 8.37612 0.105751 10.5541V10.5901C0.105751 10.5901 0.09375 10.6501 0.09375 10.6801C0.09375 10.9141 0.273752 11.1061 0.495752 11.1061C0.687754 11.1061 0.843748 10.9621 0.885748 10.7701C1.28175 8.94012 2.78176 7.59612 4.58776 7.59012C4.76176 7.59012 4.92975 7.60212 5.09175 7.62612H5.10975C5.10975 7.62612 5.09775 7.62612 5.09175 7.62612C5.09175 7.69212 5.07375 7.75812 5.07375 7.82412C5.07375 9.52812 6.36973 10.9021 7.96573 10.9021C9.56173 10.9021 10.8518 9.52212 10.8518 7.82412C10.8518 7.75812 10.8398 7.69212 10.8338 7.62612C10.8398 7.62612 10.8518 7.62612 10.8578 7.62612C11.0138 7.60212 11.1938 7.59012 11.3798 7.59012C12.1358 7.59012 12.8318 7.84212 13.4198 8.25012C13.6778 8.20812 13.9478 8.17812 14.2238 8.17812C14.3678 8.17812 14.5058 8.19612 14.6498 8.20812ZM7.95973 10.0621C6.80773 10.0621 5.87176 9.06612 5.87176 7.83012C5.87176 6.59412 6.80773 5.59809 7.95973 5.59809C9.11173 5.59809 10.0478 6.59412 10.0478 7.83012C10.0478 9.06012 9.11173 10.0561 7.95973 10.0621Z"
          style={{ fill: "var(--dark-blue)" }}
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_511_84">
        <rect width="21" height="18" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconViewRuleBase = () => (
  <svg
    width="21"
    height="14"
    viewBox="0 0 21 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_512_111)">
      <mask
        id="mask0_512_111"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="21"
        height="14"
      >
        <path d="M21 0H0V13.5484H21V0Z" fill="white" />
      </mask>
      <g mask="url(#mask0_512_111)">
        <path
          d="M13.8959 12.8715C9.5807 12.8715 7.15553 8.12276 7.05393 7.91954C7.01328 7.84502 7.01328 7.75695 7.05393 7.68244C7.15553 7.47921 9.5807 2.73047 13.8959 2.73047C18.2111 2.73047 20.6362 7.47921 20.7378 7.68244C20.7785 7.75695 20.7785 7.84502 20.7378 7.91954C20.6362 8.12276 18.2111 12.8715 13.8959 12.8715ZM7.59586 7.80437C7.99554 8.53599 10.2717 12.343 13.8959 12.343C17.5201 12.343 19.7962 8.53599 20.1959 7.80437C19.7962 7.07276 17.5201 3.26563 13.8959 3.26563C10.2717 3.26563 7.99554 7.07276 7.59586 7.80437Z"
          style={{ fill: "var(--dark-blue)", stroke: "var(--dark-blue)" }}
          stroke-miterlimit="10"
        />
        <path
          d="M13.8911 9.99135C12.6853 9.99135 11.7031 9.00909 11.7031 7.80328C11.7031 6.59749 12.6853 5.61523 13.8911 5.61523C15.0969 5.61523 16.0792 6.59749 16.0792 7.80328C16.0792 9.00909 15.0969 9.99135 13.8911 9.99135ZM13.8911 6.15039C12.9766 6.15039 12.2382 6.89554 12.2382 7.80328C12.2382 8.71102 12.9834 9.45619 13.8911 9.45619C14.7989 9.45619 15.544 8.71102 15.544 7.80328C15.544 6.89554 14.7989 6.15039 13.8911 6.15039Z"
          style={{ fill: "var(--dark-blue)", stroke: "var(--dark-blue)" }}
          stroke-miterlimit="10"
        />
        <path
          d="M16.3902 1.94839H18.6595C18.9237 1.94839 19.1337 1.73839 19.1337 1.4742C19.1337 1.21 18.9237 1 18.6595 1H3.09919C2.835 1 2.625 1.21 2.625 1.4742C2.625 1.73839 2.835 1.94839 3.09919 1.94839H11.3908C11.9966 1.94839 13.0889 1.95508 13.9966 1.95508C14.9043 1.95508 15.4966 1.94839 16.3902 1.94839Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M6.05951 7.6704C6.07984 7.62298 6.22209 7.35879 6.45919 6.97266H3.09919C2.835 6.97266 2.625 7.18266 2.625 7.44685C2.625 7.71105 2.835 7.92105 3.09919 7.92105H6.05951C6.02564 7.83976 6.02564 7.74492 6.05951 7.6704Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M3.09919 11.9238C2.835 11.9238 2.625 12.1338 2.625 12.398C2.625 12.6622 2.835 12.8722 3.09919 12.8722H10.8624C10.3137 12.6012 9.80564 12.2829 9.345 11.9238H3.09919Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M0.738387 2.47677C1.14619 2.47677 1.47677 2.14619 1.47677 1.73839C1.47677 1.33059 1.14619 1 0.738387 1C0.330587 1 0 1.33059 0 1.73839C0 2.14619 0.330587 2.47677 0.738387 2.47677Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M0.738387 8.18381C1.14619 8.18381 1.47677 7.85323 1.47677 7.44543C1.47677 7.03762 1.14619 6.70703 0.738387 6.70703C0.330587 6.70703 0 7.03762 0 7.44543C0 7.85323 0.330587 8.18381 0.738387 8.18381Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M0.738387 13.0745C1.14619 13.0745 1.47677 12.7439 1.47677 12.3361C1.47677 11.9283 1.14619 11.5977 0.738387 11.5977C0.330587 11.5977 0 11.9283 0 12.3361C0 12.7439 0.330587 13.0745 0.738387 13.0745Z"
          style={{ fill: "var(--dark-blue)" }}
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_512_111">
        <rect width="21" height="14" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const IconBtnEditBase = ({ width = 36, height = 36 }: any) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_514_130)">
      <path
        d="M4 36H32C34.2091 36 36 34.2091 36 32V4C36 1.79086 34.2091 0 32 0H4C1.79086 0 0 1.79086 0 4V32C0 34.2091 1.79086 36 4 36Z"
        style={{ fill: "var(--dark-blue)" }}
      />
      <path
        d="M21.5909 11.5849C21.9654 11.2104 22.4733 11 23.003 11C23.5326 11 24.0406 11.2104 24.4151 11.5849C24.7896 11.9594 25 12.4674 25 12.997C25 13.5267 24.7896 14.0346 24.4151 14.4091L14.8833 23.9409L11 25L12.0591 21.1167L21.5909 11.5849Z"
        stroke="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_514_130">
        <rect width={"100%"} height={"100%"} fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconAddRowBase = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_517_142)">
      <mask
        id="mask0_517_142"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="18"
        height="18"
      >
        <path d="M18 0H0V18H18V0Z" fill="white" />
      </mask>
      <g mask="url(#mask0_517_142)">
        <path
          d="M1.07768 9.50713H16.944V9.22873C16.944 7.78943 16.9454 6.3509 16.944 4.91167C16.9427 3.82578 16.2841 3.17014 15.1967 3.16683C14.8826 3.16616 14.5679 3.17412 14.2545 3.15887C13.9722 3.14561 13.7807 2.93281 13.7734 2.65836C13.7661 2.38523 13.9497 2.12735 14.22 2.12536C14.7965 2.12204 15.3882 2.08492 15.9468 2.19563C17.1865 2.44158 17.9969 3.52083 17.9983 4.82284C18.0016 7.99033 18.0009 11.1578 17.9976 14.3253C17.9976 14.8119 18.0148 15.3051 17.9479 15.7844C17.7769 17.0175 16.6962 17.9615 15.4478 17.9867C14.5142 18.0059 13.5793 17.9927 12.645 17.9927C9.33784 17.9927 6.03075 17.9947 2.72429 17.992C1.39972 17.9907 0.348807 17.1633 0.0705064 15.9011C0.0194864 15.6691 0.00424656 15.4258 0.00424656 15.1871C-0.00105344 11.7651 -0.000393405 8.34173 0.000936598 4.91764C0.0015966 3.24174 1.1287 2.11475 2.7985 2.11276C3.08806 2.11276 3.37697 2.10878 3.66653 2.11409C4.00314 2.12005 4.23704 2.33882 4.23837 2.63979C4.23969 2.9401 4.00778 3.15887 3.66918 3.1655C3.3551 3.17147 3.04102 3.16418 2.72694 3.16815C1.7403 3.17942 1.07304 3.83772 1.06907 4.81886C1.06311 6.31577 1.06708 7.81263 1.06774 9.30953C1.06774 9.36663 1.07371 9.42293 1.07768 9.50713ZM1.06708 10.5996C1.06708 10.7077 1.06708 10.7899 1.06708 10.8714C1.06708 12.3186 1.06576 13.7651 1.06708 15.2123C1.06841 16.2889 1.71976 16.9393 2.7985 16.9393C6.93125 16.9406 11.0646 16.9406 15.1974 16.9393C16.2827 16.9393 16.942 16.279 16.9427 15.1931C16.944 13.7545 16.9427 12.3153 16.9427 10.8767V10.5996H1.06708Z"
          style={{ fill: "var(--dark-blue)" }}
        />
        <path
          d="M8.47024 2.11347C8.47024 1.56721 8.46564 1.05543 8.47224 0.544308C8.47554 0.258578 8.63724 0.0663273 8.89704 0.0113073C9.11964 -0.0357627 9.36614 0.0663273 9.45624 0.276478C9.50664 0.392488 9.51924 0.531707 9.52124 0.660317C9.52844 1.13697 9.52384 1.61362 9.52384 2.11414C10.0633 2.11414 10.5735 2.11414 11.0837 2.11414C11.3203 2.11414 11.4972 2.21623 11.5966 2.43433C11.684 2.62592 11.6701 2.82878 11.5197 2.96203C11.4011 3.06677 11.2235 3.14831 11.0671 3.15826C10.6477 3.18478 10.225 3.16754 9.80414 3.16754C9.72334 3.16754 9.64244 3.16754 9.52454 3.16754C9.52454 3.41084 9.52454 3.63027 9.52454 3.8497C9.52454 4.13078 9.52784 4.41187 9.52314 4.69229C9.51784 5.05293 9.30984 5.28628 9.00034 5.2876C8.69224 5.28893 8.47364 5.04895 8.47024 4.69693C8.46634 4.2753 8.47024 3.85434 8.46894 3.43271C8.46894 3.3525 8.46234 3.27228 8.45704 3.16555C7.92294 3.16555 7.40484 3.17483 6.88663 3.1629C6.47979 3.15362 6.2366 2.76713 6.40623 2.41643C6.51027 2.20098 6.6958 2.11215 6.93169 2.11281C7.43394 2.1148 7.93624 2.11347 8.47024 2.11347Z"
          style={{ fill: "var(--dark-blue)" }}
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_517_142">
        <rect width="18" height="18" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconValidateResourceBase = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_517_153)">
      <path
        d="M17.193 7.37733C17.3699 7.55792 17.3681 7.8485 17.1875 8.02633L12.3063 12.8333C11.6774 13.4484 10.8689 13.7564 10.0604 13.7564C9.26017 13.7564 8.459 13.4548 7.83292 12.8508L5.72825 10.7754C5.54858 10.5976 5.54675 10.3079 5.72367 10.1273C5.90333 9.94675 6.193 9.94492 6.37267 10.1228L8.47367 12.1944C9.3665 13.057 10.7699 13.0533 11.6637 12.1779L16.544 7.37183C16.7228 7.19492 17.0142 7.19675 17.1921 7.37642L17.193 7.37733ZM22 11C22 17.0656 17.0656 22 11 22C4.93442 22 0 17.0656 0 11C0 4.93442 4.93442 0 11 0C17.0656 0 22 4.93442 22 11ZM21.0833 11C21.0833 5.44042 16.5596 0.916667 11 0.916667C5.44042 0.916667 0.916667 5.44042 0.916667 11C0.916667 16.5596 5.44042 21.0833 11 21.0833C16.5596 21.0833 21.0833 16.5596 21.0833 11Z"
        style={{ fill: "var(--dark-blue)" }}
      />
    </g>
    <defs>
      <clipPath id="clip0_517_153">
        <rect width="22" height="22" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconNotification = () => (
  <svg
    width="17"
    height="18"
    viewBox="0 0 17 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_522_162)">
      <path
        d="M8.44322 0C4.85983 0 1.95488 2.90494 1.95488 6.48837V7.07785C1.95488 7.6613 1.78218 8.23178 1.45853 8.71719L0.496901 10.1596C-0.619174 11.8338 0.232862 14.1093 2.17401 14.6387C2.80661 14.8112 3.44456 14.9572 4.0862 15.0765L4.08779 15.0808C4.7316 16.7987 6.45232 18 8.44322 18C10.4341 18 12.1548 16.7987 12.7986 15.0808L12.8002 15.0765C13.4419 14.9572 14.0799 14.8112 14.7125 14.6387C16.6537 14.1093 17.5057 11.8338 16.3896 10.1596L15.428 8.71719C15.1043 8.23178 14.9316 7.6613 14.9316 7.07785V6.48837C14.9316 2.90494 12.0266 0 8.44322 0ZM11.27 15.31C9.3922 15.5344 7.49416 15.5343 5.61638 15.31C6.21159 16.1653 7.24685 16.7442 8.44322 16.7442C9.63951 16.7442 10.6748 16.1653 11.27 15.31ZM3.2107 6.48837C3.2107 3.59851 5.55339 1.25581 8.44322 1.25581C11.3331 1.25581 13.6758 3.59851 13.6758 6.48837V7.07785C13.6758 7.90928 13.9219 8.72205 14.3831 9.41383L15.3447 10.8563C15.9853 11.8171 15.4963 13.1233 14.3821 13.4272C10.4939 14.4876 6.39265 14.4876 2.50443 13.4272C1.39025 13.1233 0.901198 11.8171 1.54181 10.8563L2.50342 9.41383C2.9646 8.72205 3.2107 7.90928 3.2107 7.07785V6.48837Z"
        style={{ fill: "var(--grey)" }}
      />
    </g>
    <defs>
      <clipPath id="clip0_522_162">
        <rect width="17" height="18" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconLogoutNew = () => (
  <svg
    width="15"
    height="15"
    viewBox="0 0 15 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="0.5625"
      y="0.869141"
      width="2.39238"
      height="13.9598"
      style={{ fill: "var(--grey)" }}
    />
    <path
      d="M10.625 6.6543L10.625 9.04668L6.29528 9.04668L6.29528 6.6543L10.625 6.6543Z"
      style={{ fill: "var(--grey)" }}
    />
    <path
      d="M14.311 7.70639L9.63281 11.8047V3.89258L14.311 7.70639Z"
      style={{ fill: "var(--grey)" }}
    />
  </svg>
);

export const IconPlusBase = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_540_24)">
      <mask
        id="mask0_540_24"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="24"
      >
        <path d="M24 0H0V24H24V0Z" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_540_24)">
        <path d="M12 3V21" style={{ stroke: "var(--dark-blue)" }} />
        <path d="M21 12H3" style={{ stroke: "var(--dark-blue)" }} />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_540_24">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const IconExecuteBase = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="mask0_541_9"
      maskUnits="userSpaceOnUse"
      x="2"
      y="2"
      width="20"
      height="20"
    >
      <path d="M22 2H2V22H22V2Z" fill="white" />
    </mask>
    <g mask="url(#mask0_541_9)">
      <path
        d="M3 18.4834L10.7411 10.7423L3 3"
        style={{ stroke: "var(--dark-blue)" }}
      />
      <path d="M12 21H21" style={{ stroke: "var(--dark-blue)" }} />
    </g>
  </svg>
);
