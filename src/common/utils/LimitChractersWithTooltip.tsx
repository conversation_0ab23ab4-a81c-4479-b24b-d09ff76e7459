import React from "react";
import { Tooltip, Typography, Box } from "@mui/material";

interface LimitChractersWithTooltipProps {
  value: string;
  onClick?: () => void;
}

// Reusable Tooltip Component
const LimitChractersWithTooltip = ({
  value,
  onClick,
}: LimitChractersWithTooltipProps) => {
  return (
    <Tooltip
      title={
        <Typography sx={{ fontSize: "0.875rem" }}>{value || ""}</Typography>
      }
      placement="top"
      arrow
    >
      <Box
        className="limit-chracters"
        onClick={onClick}
        sx={{ cursor: onClick ? "pointer" : "default" }}
      >
        {value}
      </Box>
    </Tooltip>
  );
};

export default LimitChractersWithTooltip;
