import React from "react";
import { Tooltip, IconButton } from "@mui/material";
import {
  IconCheckCircleIconGreenSvg,
  IconCrossCircleIconSvg,
  IconInProgressSvg,
  IconInQueueSvg,
} from "./icons";

interface StatusCellProps {
  status: string;
}

const DashboardJobstatusIcon: React.FC<StatusCellProps> = ({ status = "" }) => {
  if (status === "in_queue") {
    return (
      <Tooltip title="Inqueue" placement="top" arrow>
        <IconButton className="datagrid-action-btn p-0" color="inherit">
          <IconInQueueSvg />
        </IconButton>
      </Tooltip>
    );
  }

  if (status === "in_progress") {
    return (
      <Tooltip title="Inprogress" placement="top" arrow>
        <IconButton className="datagrid-action-btn p-0" color="inherit">
          <IconInProgressSvg />
        </IconButton>
      </Tooltip>
    );
  }

  if (status === "completed") {
    return (
      <Tooltip title="Completed" placement="top" arrow>
        <IconButton className="datagrid-action-btn p-0" color="inherit">
          <IconCheckCircleIconGreenSvg />
        </IconButton>
      </Tooltip>
    );
  }

  if (status === "terminated") {
    return (
      <Tooltip title="Terminated" placement="top" arrow>
        <IconButton className="datagrid-action-btn p-0" color="inherit">
          <IconCrossCircleIconSvg />
        </IconButton>
      </Tooltip>
    );
  }
  return (
    <Tooltip title="Failed" placement="top" arrow>
      <IconButton className="datagrid-action-btn p-0" color="inherit">
        <IconCrossCircleIconSvg />
      </IconButton>
    </Tooltip>
  );
};

export default DashboardJobstatusIcon;
