// StatusCell.tsx (or StatusCell.js)

import React from "react";
import { Tooltip, IconButton } from "@mui/material";
import {
  IconStatusFail,
  IconStatusInprogress,
  IconStatusNew,
  IconStatusPass,
} from "../../common/utils/icons";

interface StatusCellProps {
  status: string;
}

const StatusCell: React.FC<StatusCellProps> = ({ status }) => {
  const statusType = status?.replace(" ", "")?.toLowerCase();

  if (statusType === "new") {
    return (
      <Tooltip title="New" placement="top" arrow>
        <IconButton className="datagrid-action-btn p-0" color="inherit">
          <IconStatusNew />
        </IconButton>
      </Tooltip>
    );
  }

  if (statusType === "inprogress") {
    return (
      <Tooltip title="Inprogress" placement="top" arrow>
        <IconButton className="datagrid-action-btn p-0" color="inherit">
          <IconStatusInprogress />
        </IconButton>
      </Tooltip>
    );
  }

  if (statusType === "acknowledged") {
    return (
      <Tooltip title="Acknowledged" placement="top" arrow>
        <IconButton className="datagrid-action-btn p-0" color="inherit">
          <IconStatusFail />
        </IconButton>
      </Tooltip>
    );
  }

  if (statusType === "closed" || statusType === "auto_closed") {
    return (
      <Tooltip title="Closed" placement="top" arrow>
        <IconButton className="datagrid-action-btn p-0" color="inherit">
          <IconStatusPass />
        </IconButton>
      </Tooltip>
    );
  }
  return (
    <Tooltip title="New" placement="top" arrow>
      <IconButton className="datagrid-action-btn p-0" color="inherit">
        <IconStatusNew />
      </IconButton>
    </Tooltip>
  );
};

export default StatusCell;
