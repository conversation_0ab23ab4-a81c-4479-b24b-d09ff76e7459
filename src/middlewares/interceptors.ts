import axios from "axios";
import { ToastContentProps, toast } from "react-toastify";
import { fadeSlide } from "../services/utils";
import { refreshToken } from "../services/authService";
import { ReactElement, JSXElementConstructor, ReactNode } from "react";

const base: any = axios.create({
  headers: {
    "Content-Type": "application/json",
  },
});

let isRefreshing = false;
let failedQueue: any[] = [];

const processFailedQueue = async (newToken: any) => {
  for (const queuedRequest of failedQueue) {
    queuedRequest.headers["Authorization"] = `Bearer ${newToken}`;
    await axios(queuedRequest);
  }

  failedQueue = [];
};

base.interceptors.request.use(
  async (config: { headers: { [x: string]: string } }) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  }
);

base.interceptors.response.use(
  (response: any) => {
    return response;
  },
  async (error: {
    config: any;
    response: {
      status: number;
      data: {
        message:
          | string
          | number
          | boolean
          | ReactElement<any, string | JSXElementConstructor<any>>
          | Iterable<ReactNode>
          | ((props: ToastContentProps<unknown>) => ReactNode)
          | null
          | undefined;
        detail: string;
      };
    };
  }) => {
    const originalRequest = error.config;
    const path = originalRequest.url.replace(/^https?:\/\/[0-9.]+\//, "/");

    if (error?.response?.status === 500) {
      //unable to import useToast from utils, so dismissed directly
      toast.dismiss();
      toast.error(error?.response?.data?.message, {
        transition: fadeSlide,
      });
      if (path === "/config/basic-auth/refresh-token") {
        localStorage.removeItem("token");
        localStorage.setItem("lastVisitedPath", window.location.pathname);
        window.location.href = "/login";
      }
      return;
    } else if (
      error?.response?.status === 401 &&
      !originalRequest._retry &&
      error?.response?.data?.detail &&
      error?.response?.data?.detail === "Invalid token or token has expired"
    ) {
      if (!isRefreshing) {
        isRefreshing = true;

        try {
          const token = localStorage.getItem("token");
          const result = await refreshToken({
            expired_token: token,
          });

          if (result?.access_token) {
            localStorage.setItem("token", result.access_token);

            // Retry all queued requests with the new token
            await processFailedQueue(result.access_token);

            // // Retry the original request with the new token
            // originalRequest.headers[
            //   "Authorization"
            // ] = `Bearer ${result.access_token}`;
            // return axios(originalRequest);
          }
        } catch (refreshError) {
          console.error("Error refreshing token:", refreshError);
          // toast.error("Token refresh failed");
        } finally {
          isRefreshing = false;
        }
      }

      // If token refresh is in progress, queue the original request
      if (!originalRequest._retry) {
        originalRequest._retry = true;
        failedQueue.push(originalRequest);
      }

      // Return a promise that will be resolved when the token refresh is successful
      return new Promise<void>(async (resolve) => {
        const checkInterval = setInterval(async () => {
          if (!isRefreshing) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
      });
    } else if (error.response.status === 404) {
      if (
        typeof error.response.data.message === "string" &&
        error.response.data.message.includes("Comparison_research_query")
      ) {
        return;
      } else {
        //unable to import useToast from utils, so dismissed directly
        toast.dismiss();
        toast.error(error.response.data.message, {
          transition: fadeSlide,
        });
      }
    } else if (![200, 201, 206, 413].includes(error?.response?.status)) {
      //unable to import useToast from utils, so dismissed directly
      toast.dismiss();
      toast.error(error?.response?.data?.message, {
        transition: fadeSlide,
      });
    }
  }
);

export default base;
