{"domain_id": 1319, "id": 1054820, "name": "JITENDRA_R2_SECONDARY_MERGE_RESOURCES", "execution_report": "", "comparison_keys": {"base_columns": ["Key_AccountCode", "Key_SecurityID"], "column_mappings": {"Key_AccountCode": {"2193": "JITENDRA_R1_AccountCode", "2194": "JITENDRA_R2_AccountCode", "2195": "JITENDRA_R3_AccountCode"}, "Key_SecurityID": {"2193": "JITENDRA_R1_SecurityID", "2194": "JITENDRA_R2_SecurityID", "2195": "JITENDRA_R3_SecurityID"}}}, "comparison_research_query_results": null, "run_name": "Legacy", "prev_execution_id": null, "complete_execution_params": {"rule_execution_request": {"request_body": {"resource_data": [{"resource_id": 2193, "resource_prefix": null, "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "/home/<USER>/Documents/VIS/random-bulk-data", "file_name": "random_data_R1_10000.csv", "column_delimiter": null, "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}, {"resource_id": 2194, "resource_prefix": null, "linked_service_id": null, "linked_service_code": null, "connection_key": null, "sql_query": null, "container_name": null, "resource_path": "/home/<USER>/Documents/VIS/random-bulk-data", "file_name": "random_data_R1_10000.csv", "column_delimiter": null, "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}, {"resource_id": 2195, "resource_prefix": null, "linked_service_id": null, "linked_service_code": null, "connection_key": null, "sql_query": null, "container_name": null, "resource_path": "/home/<USER>/Documents/VIS/random-bulk-data", "file_name": "random_data_R1_10000.csv", "column_delimiter": null, "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}]}, "additional_info_for_all_resources": [{"file_names": ["random_data_R1_10000.csv"], "resource_id": 2193, "resource_column_details_version": 5, "resource_version": 4, "domain_version": 5}, {"file_names": ["random_data_R1_10000.csv"], "resource_id": 2194, "resource_column_details_version": 1, "resource_version": 3, "domain_version": 5}, {"file_names": ["random_data_R1_10000.csv"], "resource_id": 2195, "resource_column_details_version": 3, "resource_version": 9, "domain_version": 5}]}, "rule_id": 1169, "rule_version": "1"}, "is_success": true, "rule_id": 1169, "domain_name": "Performance_test", "execution_time": "2025-02-14 15:59:42.361892", "is_detailed_execution_data_available": false, "additional_properties": {"status": true, "summary_mode": false, "total_time": 101, "current_url": "http://**************/rules/1319/view/11641169", "resource_prefixes": ["JITENDRA_R1", "JITENDRA_R2", "JITENDRA_R3"], "total_records": 10000, "total_common_records": 10000, "total_mismatched_records": 0, "total_mismatched_records_for_all_columns": 0, "total_mismatched_records_stored_in_db": 0, "total_missing_records": 0, "total_unique_missing_records": 0, "total_missing_records_stored_in_db": 0, "max_records_can_be_stored_in_db": 10000000, "total_false_positive_missing_records": 0, "supporting_documents": {"rule_execution_report": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\rule_results\\JITENDRA_R2_SECONDARY_MERGE_RESOURCES_rule_result_02_14_2025_113239\\JITENDRA_R2_SECONDARY_MERGE_RESOURCES_rule_output.csv", "filter_records_report": ["C:\\inetpub\\wwwroot\\VIS-http\\download-files\\rule_results\\JITENDRA_R1_rule_result_02_14_2025_122324\\filtered_records\\JITENDRA_RC_2_filtered_records.csv"], "adhoc_query_result_reports": null, "comparison_research_query_result_reports": ["lorem", "ipsum"]}, "comparison_rule_exec_details": [], "missing_records_details": null, "adhoc_queries_result": [], "validation_exec_details": [{"resource_id": 2193, "resource_name": "JITENDRA_R1", "total_records_count": 10000, "total_common_records": 10000, "total_columns_count": 11, "filtered_records_by_rule_filters": 0, "unique_records": 10000, "missing_records_count": 0, "mismatched_records_count": 0, "false_positive_missing_records_count": 0, "file_names": ["random_data_R1_10000.csv"], "validation_result_id": 59318, "validation_result": {"is_success": false, "resource_id": 2193, "resource_name": "JITENDRA_R1", "resource_type": "JITENDRA_R1", "domain_id": 1319, "domain_name": "Performance_test", "execution_time": "2025-02-14 15:59:42.361892", "run_id": 1, "run_name": "Legacy", "current_url": null, "is_detailed_execution_data_available": false, "comparison_execution_id": 1054820, "additional_properties": {"summary_mode": false, "total_records": 10000, "unique_records": 10000, "filtered_records_by_null_values_in_unique_keys": 0, "filtered_records_by_resource_filters": 0, "total_invalid_records": 10000, "total_duplicate_records": 0, "total_validation_errors": 20231, "total_time": 21.385721, "current_url": "https://**************/resource/1319/view/2193", "supporting_documents": {"validation_report": "\\home\\jitendra\\Documents\\VIS\\Results_New\\validation_result\\JITENDRA_R1_validation_report_02_14_2025_155942.csv", "null_keys_report": null, "filter_records_report": null, "cross_field_validation_report_paths": null, "downloded_file_paths": null}, "validation_errors_summary": [{"column_name": "Column3", "total_validation_errors": 231, "validation_severity": "high_severity", "general_validation_errors": null, "custom_validation_errros": [{"custom_validation_name": "cv1", "total_validation_errors": 231}], "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1218.0, 'Column2'=4275.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1817.0, 'Column2'=4344.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9547.0, 'Column2'=6811.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=8661.0, 'Column2'=3065.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=427.0, 'Column2'=9909.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}]}, {"column_name": "Column6", "total_validation_errors": 10000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 10000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2024-12-16T08:28:30.261393 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2021-11-01T23:10:37.144212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2021-10-04T22:40:03.747067 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2021-04-08T23:46:44.621862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-07-05T05:27:55.278786 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2024-01-07T17:32:15.019202 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2021-02-27T11:54:29.624460 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2022-12-20T07:59:49.291812 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2022-04-20T02:38:37.014267 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2024-10-09T02:25:16.262605 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}]}, {"column_name": "Column10", "total_validation_errors": 10000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 10000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2022-12-15T08:38:49.322982 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2022-12-22T21:16:55.190121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2024-04-08T15:54:18.916347 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2023-05-12T04:59:59.114433 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-01-03T02:11:21.638074 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2021-02-19T13:04:49.371856 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2020-02-12T05:36:05.933684 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2021-12-22T18:20:05.098660 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2020-10-22T14:21:08.786939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2021-11-18T17:08:48.456770 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}]}], "cross_field_validation_results": [], "base_resource_validation_result": null}, "prev_execution_id": null, "is_rerun": null, "complete_validation_params": {"validation_request_body": {"resource_data": {"resource_id": 2193, "resource_prefix": null, "linked_service_id": null, "linked_service_code": null, "connection_key": null, "sql_query": null, "container_name": null, "resource_path": "/home/<USER>/Documents/VIS/random-bulk-data", "file_name": "random_data_R1_10000.csv", "column_delimiter": null, "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}, "sampling_validation": null, "sample_size": null, "output_storage_params": null, "run_instance": null, "severity_level": null, "severity_column_names": null, "validation_execution_report_name": null, "inline_variables": null, "pull_latest_files": null, "query_params": null, "latest_execution_id": null, "is_rerun": false}, "no_of_errors_in_response": 10, "no_of_sample_validation_errors": 100, "no_of_errors_in_output_files": 10000000, "no_of_errors_in_filter_record_output_files": 1000, "summary_mode": false, "generate_files": true, "save_input_data_file": false, "skip_duplicate_records": true, "keep_downloaded_files": false, "file_names": ["random_data_R1_10000.csv"], "resource_id": 2193, "resource_column_details_version": 5, "resource_version": 4, "domain_version": 5, "store_errors_snapshots_and_create_issues": true}}}, {"resource_id": 2194, "resource_name": "JITENDRA_R2", "total_records_count": 10000, "total_common_records": 10000, "total_columns_count": 11, "filtered_records_by_rule_filters": 0, "unique_records": 10000, "missing_records_count": 0, "mismatched_records_count": 0, "false_positive_missing_records_count": 0, "file_names": ["random_data_R1_10000.csv"], "validation_result_id": 59319, "validation_result": {"is_success": false, "resource_id": 2194, "resource_name": "JITENDRA_R2", "resource_type": "JITENDRA_R2", "domain_id": 1319, "domain_name": "Performance_test", "execution_time": "2025-02-14 15:59:42.361892", "run_id": 1, "run_name": "Legacy", "current_url": null, "is_detailed_execution_data_available": false, "comparison_execution_id": 1054820, "additional_properties": {"summary_mode": false, "total_records": 10000, "unique_records": 10000, "filtered_records_by_null_values_in_unique_keys": 0, "filtered_records_by_resource_filters": 0, "total_invalid_records": 10000, "total_duplicate_records": 0, "total_validation_errors": 20231, "total_time": 51.627688, "current_url": "https://**************/resource/1319/view/2194", "supporting_documents": {"validation_report": "\\home\\jitendra\\Documents\\VIS\\Results_New\\validation_result\\JITENDRA_R2_validation_report_02_14_2025_155942.csv", "null_keys_report": null, "filter_records_report": null, "cross_field_validation_report_paths": null, "downloded_file_paths": null}, "validation_errors_summary": [{"column_name": "Column3", "total_validation_errors": 231, "validation_severity": "high_severity", "general_validation_errors": null, "custom_validation_errros": [{"custom_validation_name": "cv1", "total_validation_errors": 231}], "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1218.0, 'Column2'=4275.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1817.0, 'Column2'=4344.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9547.0, 'Column2'=6811.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=8661.0, 'Column2'=3065.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=427.0, 'Column2'=9909.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}]}, {"column_name": "Column6", "total_validation_errors": 10000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 10000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2024-12-16T08:28:30.261393 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2021-11-01T23:10:37.144212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2021-10-04T22:40:03.747067 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2021-04-08T23:46:44.621862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-07-05T05:27:55.278786 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2024-01-07T17:32:15.019202 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2021-02-27T11:54:29.624460 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2022-12-20T07:59:49.291812 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2022-04-20T02:38:37.014267 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2024-10-09T02:25:16.262605 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}]}, {"column_name": "Column10", "total_validation_errors": 10000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 10000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2022-12-15T08:38:49.322982 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2022-12-22T21:16:55.190121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2024-04-08T15:54:18.916347 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2023-05-12T04:59:59.114433 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-01-03T02:11:21.638074 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2021-02-19T13:04:49.371856 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2020-02-12T05:36:05.933684 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2021-12-22T18:20:05.098660 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2020-10-22T14:21:08.786939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2021-11-18T17:08:48.456770 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}]}], "cross_field_validation_results": [], "base_resource_validation_result": null}, "prev_execution_id": null, "is_rerun": null, "complete_validation_params": {"validation_request_body": {"resource_data": {"resource_id": 2194, "resource_prefix": null, "linked_service_id": null, "linked_service_code": null, "connection_key": null, "sql_query": null, "container_name": null, "resource_path": "/home/<USER>/Documents/VIS/random-bulk-data", "file_name": "random_data_R1_10000.csv", "column_delimiter": null, "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}, "sampling_validation": null, "sample_size": null, "output_storage_params": null, "run_instance": null, "severity_level": null, "severity_column_names": null, "validation_execution_report_name": null, "inline_variables": null, "pull_latest_files": null, "query_params": null, "latest_execution_id": null, "is_rerun": false}, "no_of_errors_in_response": 10, "no_of_sample_validation_errors": 100, "no_of_errors_in_output_files": 10000000, "no_of_errors_in_filter_record_output_files": 1000, "summary_mode": false, "generate_files": true, "save_input_data_file": false, "skip_duplicate_records": true, "keep_downloaded_files": false, "file_names": ["random_data_R1_10000.csv"], "resource_id": 2194, "resource_column_details_version": 1, "resource_version": 3, "domain_version": 5, "store_errors_snapshots_and_create_issues": true}}}, {"resource_id": 2195, "resource_name": "JITENDRA_R3", "total_records_count": 10000, "total_common_records": 10000, "total_columns_count": 11, "filtered_records_by_rule_filters": 0, "unique_records": 10000, "missing_records_count": 0, "mismatched_records_count": 0, "false_positive_missing_records_count": 0, "file_names": ["random_data_R1_10000.csv"], "validation_result_id": 59320, "validation_result": {"is_success": false, "resource_id": 2195, "resource_name": "JITENDRA_R3", "resource_type": "JITENDRA_R3", "domain_id": 1319, "domain_name": "Performance_test", "execution_time": "2025-02-14 15:59:42.361892", "run_id": 1, "run_name": "Legacy", "current_url": null, "is_detailed_execution_data_available": false, "comparison_execution_id": 1054820, "additional_properties": {"summary_mode": false, "total_records": 10000, "unique_records": 10000, "filtered_records_by_null_values_in_unique_keys": 0, "filtered_records_by_resource_filters": 0, "total_invalid_records": 10000, "total_duplicate_records": 0, "total_validation_errors": 20231, "total_time": 85.838591, "current_url": "https://**************/resource/1319/view/2195", "supporting_documents": {"validation_report": "\\home\\jitendra\\Documents\\VIS\\Results_New\\validation_result\\JITENDRA_R3_validation_report_02_14_2025_155942.csv", "null_keys_report": null, "filter_records_report": null, "cross_field_validation_report_paths": null, "downloded_file_paths": null}, "validation_errors_summary": [{"column_name": "Column3", "total_validation_errors": 231, "validation_severity": "high_severity", "general_validation_errors": null, "custom_validation_errros": [{"custom_validation_name": "cv1", "total_validation_errors": 231}], "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1218.0, 'Column2'=4275.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1817.0, 'Column2'=4344.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9547.0, 'Column2'=6811.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=8661.0, 'Column2'=3065.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=427.0, 'Column2'=9909.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_10000.csv'"}]}, {"column_name": "Column6", "total_validation_errors": 10000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 10000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2024-12-16T08:28:30.261393 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2021-11-01T23:10:37.144212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2021-10-04T22:40:03.747067 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2021-04-08T23:46:44.621862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-07-05T05:27:55.278786 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2024-01-07T17:32:15.019202 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2021-02-27T11:54:29.624460 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2022-12-20T07:59:49.291812 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2022-04-20T02:38:37.014267 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2024-10-09T02:25:16.262605 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}]}, {"column_name": "Column10", "total_validation_errors": 10000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 10000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2022-12-15T08:38:49.322982 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2022-12-22T21:16:55.190121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2024-04-08T15:54:18.916347 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2023-05-12T04:59:59.114433 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-01-03T02:11:21.638074 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2021-02-19T13:04:49.371856 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2020-02-12T05:36:05.933684 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2021-12-22T18:20:05.098660 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2020-10-22T14:21:08.786939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2021-11-18T17:08:48.456770 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_10000.csv'"}]}], "cross_field_validation_results": [], "base_resource_validation_result": null}, "prev_execution_id": null, "is_rerun": null, "complete_validation_params": {"validation_request_body": {"resource_data": {"resource_id": 2195, "resource_prefix": null, "linked_service_id": null, "linked_service_code": null, "connection_key": null, "sql_query": null, "container_name": null, "resource_path": "/home/<USER>/Documents/VIS/random-bulk-data", "file_name": "random_data_R1_10000.csv", "column_delimiter": null, "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}, "sampling_validation": null, "sample_size": null, "output_storage_params": null, "run_instance": null, "severity_level": null, "severity_column_names": null, "validation_execution_report_name": null, "inline_variables": null, "pull_latest_files": null, "query_params": null, "latest_execution_id": null, "is_rerun": false}, "no_of_errors_in_response": 10, "no_of_sample_validation_errors": 100, "no_of_errors_in_output_files": 10000000, "no_of_errors_in_filter_record_output_files": 1000, "summary_mode": false, "generate_files": true, "save_input_data_file": false, "skip_duplicate_records": true, "keep_downloaded_files": false, "file_names": ["random_data_R1_10000.csv"], "resource_id": 2195, "resource_column_details_version": 3, "resource_version": 9, "domain_version": 5, "store_errors_snapshots_and_create_issues": true}}}], "secondary_merge_resource_validation_exec_details": []}, "run_id": 1, "current_url": "https://**************//rules-execution-history/1169/1054820/dashboard", "is_rerun": null}