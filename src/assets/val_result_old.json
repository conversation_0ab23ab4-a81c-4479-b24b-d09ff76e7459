{"resource_id": 2193, "additional_properties": {"summary_mode": false, "total_records": 3000, "unique_records": 3000, "filtered_records_by_null_values_in_unique_keys": 0, "filtered_records_by_resource_filters": 0, "total_invalid_records": 3000, "total_duplicate_records": 0, "total_validation_errors": 6069, "total_time": 3.310969, "current_url": "https://172.176.208.22/resource/1319/view/2193", "supporting_documents": {"validation_report": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\validation_result\\JITENDRA_R1_validation_report_02_14_2025_110528.csv", "null_keys_report": null, "filter_records_report": null, "cross_field_validation_report_paths": null, "downloded_file_paths": null}, "validation_errors_summary": [{"column_name": "Column3", "total_validation_errors": 69, "validation_severity": "high_severity", "general_validation_errors": null, "custom_validation_errros": [{"custom_validation_name": "cv1", "total_validation_errors": 69}], "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1218.0, 'Column2'=4275.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1817.0, 'Column2'=4344.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9547.0, 'Column2'=6811.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8661.0, 'Column2'=3065.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=427.0, 'Column2'=9909.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}]}, {"column_name": "Column6", "total_validation_errors": 3000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 3000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2024-12-16T08:28:30.261393 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2021-11-01T23:10:37.144212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2021-10-04T22:40:03.747067 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2021-04-08T23:46:44.621862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-07-05T05:27:55.278786 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2024-01-07T17:32:15.019202 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2021-02-27T11:54:29.624460 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2022-12-20T07:59:49.291812 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2022-04-20T02:38:37.014267 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2024-10-09T02:25:16.262605 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}]}, {"column_name": "Column10", "total_validation_errors": 3000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 3000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2022-12-15T08:38:49.322982 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2022-12-22T21:16:55.190121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2024-04-08T15:54:18.916347 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2023-05-12T04:59:59.114433 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-01-03T02:11:21.638074 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2021-02-19T13:04:49.371856 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2020-02-12T05:36:05.933684 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2021-12-22T18:20:05.098660 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2020-10-22T14:21:08.786939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2021-11-18T17:08:48.456770 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}]}], "cross_field_validation_results": [], "base_resource_validation_result": null}, "is_rerun": null, "resource_name": "JITENDRA_R1", "run_id": 1, "complete_validation_params": {"validation_parameters": {"resource_data": {"resource_id": 2193, "resource_prefix": null, "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_name": "random_data_R1_1739530333857.csv", "column_delimiter": ",", "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": [], "api_request": {"url": null, "method": "get", "content_type": "application/json", "body": null, "query_params": null, "url_params": null, "user_name": null, "password": null, "bearer_token": null, "api_key": null, "oauth_client_id": null, "oauth_client_secret": null, "oauth_url": null, "request_timeout": 0}, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}, "sampling_validation": null, "sample_size": null, "output_storage_params": {"output_storage_linked_service_id": 27, "output_storage_linked_service_code": "localFile1", "output_storage_connection_key": 28, "output_storage_base_file_path": "C:\\inetpub\\wwwroot\\VIS-http\\download-files"}, "run_instance": {"run_id": 1, "run_name": "Legacy"}, "severity_level": 3, "severity_column_names": null, "validation_execution_report_name": null, "inline_variables": {"abc": "abc", "cba": "cba"}, "pull_latest_files": false, "query_params": null, "latest_execution_id": null, "is_rerun": false}, "no_of_errors_in_response": 0, "no_of_sample_validation_errors": 0, "no_of_errors_in_output_files": 0, "no_of_errors_in_filter_record_output_files": 0, "summary_mode": false, "generate_files": true, "save_input_data_file": false, "skip_duplicate_records": true, "keep_downloaded_files": false, "file_names": [], "resource_id": 2193, "resource_column_details_version": 5, "resource_version": 5, "domain_version": 5, "store_errors_snapshots_and_create_issues": true, "inline_variables": {}}, "resource_type": "JITENDRA_R1", "run_name": "Legacy", "domain_id": 1319, "validation_parameters": {"resource_id": 2193, "resource_name": "JITENDRA_R1", "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_names": ["random_data_R1_1739530333857.csv"], "file_processing_attributes": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "inline_variables": {"abc": "abc", "cba": "cba"}, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "resource_column_details_version": 5, "resource_version": 5, "domain_version": 5, "filter_rules": []}, "domain_name": "Performance_test", "current_url": "https://dq-dev.vis-testing.nyc/validation-execution-history/validate-result/59554", "file_name": null, "is_detailed_execution_data_available": false, "is_success": false, "execution_time": "2025-02-14 11:05:28.467064", "comparison_execution_id": 0, "id": 59554, "prev_execution_id": null}