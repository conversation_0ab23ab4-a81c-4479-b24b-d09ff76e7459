[{"name": "Resource Research Query", "source": {"type": "Resource", "resource_id": 913, "resource_code": "resource_913", "linked_service_id": null, "linked_service_code": null, "connection_key_id": null, "connection_key_code": null}, "query": "SELECT * FROM <resource_913> WHERE [Portfolio Code] IN [##MISMATCHED## resource_913.Portfolio Code]", "result": [{"GWP@_EffectiveDate": null, "GWP@_BaseUnitCost": 100, "GWP@_AccruedIncomeBase": null, "GWP@_TotalCostBase": 7000, "GWP@_Quantity": 400, "GWP@_SecurityID": "SEC5", "GWP@_MarketValueBase": 7000, "GWP@_AccountCode": 5, "GWP@_UnrealizedTotalGain": "20000", "GWP@_FishIndustry": null, "GWP@_FishValue": null, "GWP@_FishSecID": null, "GWP@_LongShortCode": "S"}, {"GWP@_EffectiveDate": "09/14/2023", "GWP@_BaseUnitCost": 80, "GWP@_AccruedIncomeBase": null, "GWP@_TotalCostBase": 1800, "GWP@_Quantity": 700, "GWP@_SecurityID": "SEC10", "GWP@_MarketValueBase": 1700, "GWP@_AccountCode": 10, "GWP@_UnrealizedTotalGain": "2000", "GWP@_FishIndustry": null, "GWP@_FishValue": null, "GWP@_FishSecID": null, "GWP@_LongShortCode": "S"}, {"GWP@_EffectiveDate": "09/14/2023", "GWP@_BaseUnitCost": 20, "GWP@_AccruedIncomeBase": null, "GWP@_TotalCostBase": 9000, "GWP@_Quantity": 890, "GWP@_SecurityID": "SEC1", "GWP@_MarketValueBase": 2300, "GWP@_AccountCode": 1, "GWP@_UnrealizedTotalGain": "14000", "GWP@_FishIndustry": null, "GWP@_FishValue": null, "GWP@_FishSecID": null, "GWP@_LongShortCode": "S"}, {"GWP@_EffectiveDate": "09/14/2023", "GWP@_BaseUnitCost": 40, "GWP@_AccruedIncomeBase": null, "GWP@_TotalCostBase": 5000, "GWP@_Quantity": null, "GWP@_SecurityID": "SEC2", "GWP@_MarketValueBase": 1000, "GWP@_AccountCode": 3, "GWP@_UnrealizedTotalGain": "4000", "GWP@_FishIndustry": null, "GWP@_FishValue": null, "GWP@_FishSecID": null, "GWP@_LongShortCode": "S"}, {"GWP@_EffectiveDate": null, "GWP@_BaseUnitCost": 200, "GWP@_AccruedIncomeBase": null, "GWP@_TotalCostBase": 2100, "GWP@_Quantity": 1700, "GWP@_SecurityID": "SEC12", "GWP@_MarketValueBase": 3100, "GWP@_AccountCode": 12, "GWP@_UnrealizedTotalGain": "2700", "GWP@_FishIndustry": null, "GWP@_FishValue": null, "GWP@_FishSecID": null, "GWP@_LongShortCode": "L"}, {"GWP@_EffectiveDate": "09-14-2023", "GWP@_BaseUnitCost": 130, "GWP@_AccruedIncomeBase": null, "GWP@_TotalCostBase": 3000, "GWP@_Quantity": 1100, "GWP@_SecurityID": "SEC11", "GWP@_MarketValueBase": 2500, "GWP@_AccountCode": 11, "GWP@_UnrealizedTotalGain": "1200", "GWP@_FishIndustry": null, "GWP@_FishValue": null, "GWP@_FishSecID": null, "GWP@_LongShortCode": "L"}, {"GWP@_EffectiveDate": "09/14/2023", "GWP@_BaseUnitCost": 50, "GWP@_AccruedIncomeBase": null, "GWP@_TotalCostBase": 4000, "GWP@_Quantity": 900, "GWP@_SecurityID": "SEC4", "GWP@_MarketValueBase": 5000, "GWP@_AccountCode": 4, "GWP@_UnrealizedTotalGain": "5000", "GWP@_FishIndustry": null, "GWP@_FishValue": null, "GWP@_FishSecID": null, "GWP@_LongShortCode": "S"}, {"GWP@_EffectiveDate": "09-14-2023", "GWP@_BaseUnitCost": 150, "GWP@_AccruedIncomeBase": null, "GWP@_TotalCostBase": 2000, "GWP@_Quantity": null, "GWP@_SecurityID": "SEC13", "GWP@_MarketValueBase": 3000, "GWP@_AccountCode": 13, "GWP@_UnrealizedTotalGain": "9000", "GWP@_FishIndustry": null, "GWP@_FishValue": null, "GWP@_FishSecID": null, "GWP@_LongShortCode": "S"}]}, {"name": "External Resource Research Query", "source": {"type": "Resource", "resource_id": 2051, "resource_code": "resource_2051", "linked_service_id": null, "linked_service_code": null, "connection_key_id": null, "connection_key_code": null}, "query": "SELECT * FROM <resource_1408_clone> WHERE [FishIndustry] IN [##MISMATCHED## resource_913.Portfolio Code]", "result": [{"FishIndustry": "1", "FishValue": "2", "FishSecID": "1"}]}]