{"execution_time": "2025-02-14 11:32:39.598540", "rule_exec_parameters": {"resource_details": [{"resource_id": 2193, "resource_name": "JITENDRA_R1", "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_names": ["random_data_R1_1739530333857.csv"], "file_processing_attributes": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "inline_variables": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "resource_column_details_version": 5, "resource_version": 5, "domain_version": 5, "filter_rules": []}, {"resource_id": 2194, "resource_name": "JITENDRA_R2", "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_names": ["random_data_R2_1739530358084.csv"], "file_processing_attributes": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "inline_variables": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "resource_column_details_version": 1, "resource_version": 4, "domain_version": 5, "filter_rules": []}, {"resource_id": 2195, "resource_name": "JITENDRA_R3", "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_names": ["random_data_R3_*************.csv"], "file_processing_attributes": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "inline_variables": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "resource_column_details_version": 3, "resource_version": 11, "domain_version": 5, "filter_rules": []}], "rule_id": 1169, "rule_version": 1, "inline_variables": {"rule_variables": {}, "resource_variables": []}}, "execution_report": "", "current_url": "https://dq-dev.vis-testing.nyc/rules-execution-history/1169/1054820/dashboard", "is_success": false, "is_detailed_execution_data_available": false, "prev_execution_id": null, "id": 1054820, "comparison_keys": {"base_columns": ["Key_AccountCode", "Key_SecurityID"], "column_mappings": {"Key_AccountCode": {"2193": "JITENDRA_R1_AccountCode", "2194": "JITENDRA_R2_AccountCode", "2195": "JITENDRA_R3_AccountCode"}, "Key_SecurityID": {"2193": "JITENDRA_R1_SecurityID", "2194": "JITENDRA_R2_SecurityID", "2195": "JITENDRA_R3_SecurityID"}}}, "is_rerun": null, "rule_id": 1169, "additional_properties": {"status": false, "summary_mode": false, "total_time": 13, "current_url": "http://172.176.208.22/rules/1319/view/********", "resource_prefixes": ["JITENDRA_R1", "JITENDRA_R2", "JITENDRA_R3"], "total_records": 10000, "total_common_records": 3000, "total_mismatched_records": 3, "total_mismatched_records_for_all_columns": 3, "total_mismatched_records_stored_in_db": 3, "total_missing_records": 13900, "total_unique_missing_records": 7000, "total_missing_records_stored_in_db": 1000, "max_records_can_be_stored_in_db": 1000, "total_false_positive_missing_records": 0, "supporting_documents": {"rule_execution_report": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\rule_results\\JITENDRA_R2_SECONDARY_MERGE_RESOURCES_rule_result_02_14_2025_113239\\JITENDRA_R2_SECONDARY_MERGE_RESOURCES_rule_output.csv", "filter_records_report": [], "adhoc_query_result_reports": null, "comparison_research_query_result_reports": []}, "comparison_rule_exec_details": [{"check_name": "Quantity", "total_records": 3000, "mismatched_records": 3, "errors": {"error_message": "Mismatched Quantity", "column_name": "Quantity", "row_details": [{"keys": "'Key_AccountCode'=6475.0, 'Key_SecurityID'=9463.0", "column_values": "'Mismatched_JITENDRA_R1_Value'=-9466.0, 'Mismatched_JITENDRA_R2_Value'=9466.0, 'Mismatched_JITENDRA_R3_Value'=-9466.0", "file_names": "'JITENDRA_R1_file_name'='random_data_R1_1739530333857.csv', 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'"}, {"keys": "'Key_AccountCode'=442.0, 'Key_SecurityID'=60.0", "column_values": "'Mismatched_JITENDRA_R1_Value'=-7385.0, 'Mismatched_JITENDRA_R2_Value'=7385.0, 'Mismatched_JITENDRA_R3_Value'=-7385.0", "file_names": "'JITENDRA_R1_file_name'='random_data_R1_1739530333857.csv', 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'"}, {"keys": "'Key_AccountCode'=4674.0, 'Key_SecurityID'=5552.0", "column_values": "'Mismatched_JITENDRA_R1_Value'=1.0, 'Mismatched_JITENDRA_R2_Value'=1332.0, 'Mismatched_JITENDRA_R3_Value'=1.0", "file_names": "'JITENDRA_R1_file_name'='random_data_R1_1739530333857.csv', 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'"}]}}], "missing_records_details": [{"keys": "'Key_AccountCode'='8463.0', 'Key_SecurityID'='5931.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5519.0', 'Key_SecurityID'='5431.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='2691.0', 'Key_SecurityID'='5964.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='2293.0', 'Key_SecurityID'='6436.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5831.0', 'Key_SecurityID'='6022.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6653.0', 'Key_SecurityID'='4196.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1959.0', 'Key_SecurityID'='1941.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='8622.0', 'Key_SecurityID'='4320.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='633.0', 'Key_SecurityID'='529.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1601.0', 'Key_SecurityID'='9728.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7256.0', 'Key_SecurityID'='5787.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='234.0', 'Key_SecurityID'='3169.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7781.0', 'Key_SecurityID'='6791.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6228.0', 'Key_SecurityID'='8350.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5844.0', 'Key_SecurityID'='3628.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9467.0', 'Key_SecurityID'='703.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='3375.0', 'Key_SecurityID'='2837.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6853.0', 'Key_SecurityID'='1149.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5458.0', 'Key_SecurityID'='2036.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='8396.0', 'Key_SecurityID'='9405.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='2505.0', 'Key_SecurityID'='5396.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9558.0', 'Key_SecurityID'='8934.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='915.0', 'Key_SecurityID'='2271.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5854.0', 'Key_SecurityID'='9260.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7965.0', 'Key_SecurityID'='2795.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9698.0', 'Key_SecurityID'='3889.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7214.0', 'Key_SecurityID'='7957.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5708.0', 'Key_SecurityID'='30.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6251.0', 'Key_SecurityID'='2661.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='4976.0', 'Key_SecurityID'='9894.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1488.0', 'Key_SecurityID'='9216.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='3334.0', 'Key_SecurityID'='4650.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='8821.0', 'Key_SecurityID'='7397.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='3133.0', 'Key_SecurityID'='6492.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='98.0', 'Key_SecurityID'='1781.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='4828.0', 'Key_SecurityID'='8585.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='4828.0', 'Key_SecurityID'='3834.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1424.0', 'Key_SecurityID'='7618.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='8685.0', 'Key_SecurityID'='3261.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9711.0', 'Key_SecurityID'='2854.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='4405.0', 'Key_SecurityID'='7589.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9220.0', 'Key_SecurityID'='1842.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1951.0', 'Key_SecurityID'='4702.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6129.0', 'Key_SecurityID'='776.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9232.0', 'Key_SecurityID'='3255.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='4696.0', 'Key_SecurityID'='6286.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9548.0', 'Key_SecurityID'='4052.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='75.0', 'Key_SecurityID'='304.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='185.0', 'Key_SecurityID'='1893.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='8275.0', 'Key_SecurityID'='2542.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='155.0', 'Key_SecurityID'='9395.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6892.0', 'Key_SecurityID'='983.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9122.0', 'Key_SecurityID'='1676.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9640.0', 'Key_SecurityID'='7932.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='4251.0', 'Key_SecurityID'='5761.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='8099.0', 'Key_SecurityID'='1448.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7694.0', 'Key_SecurityID'='6241.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1033.0', 'Key_SecurityID'='4739.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7471.0', 'Key_SecurityID'='823.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5168.0', 'Key_SecurityID'='5978.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1574.0', 'Key_SecurityID'='9021.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7509.0', 'Key_SecurityID'='386.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6676.0', 'Key_SecurityID'='8102.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9117.0', 'Key_SecurityID'='5680.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='3467.0', 'Key_SecurityID'='9164.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='367.0', 'Key_SecurityID'='9657.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='3471.0', 'Key_SecurityID'='959.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='2088.0', 'Key_SecurityID'='7605.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7372.0', 'Key_SecurityID'='779.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9692.0', 'Key_SecurityID'='5358.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1903.0', 'Key_SecurityID'='3437.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='196.0', 'Key_SecurityID'='7247.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='3753.0', 'Key_SecurityID'='496.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='3196.0', 'Key_SecurityID'='9538.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5057.0', 'Key_SecurityID'='1162.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6338.0', 'Key_SecurityID'='1532.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7607.0', 'Key_SecurityID'='5284.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1202.0', 'Key_SecurityID'='4977.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9347.0', 'Key_SecurityID'='5964.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='5427.0', 'Key_SecurityID'='8524.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='8811.0', 'Key_SecurityID'='1415.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='2937.0', 'Key_SecurityID'='4976.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='606.0', 'Key_SecurityID'='6923.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7998.0', 'Key_SecurityID'='4291.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6276.0', 'Key_SecurityID'='4635.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='4758.0', 'Key_SecurityID'='5445.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6783.0', 'Key_SecurityID'='5541.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='7408.0', 'Key_SecurityID'='3159.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='2884.0', 'Key_SecurityID'='4324.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9557.0', 'Key_SecurityID'='7172.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='113.0', 'Key_SecurityID'='5980.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='1312.0', 'Key_SecurityID'='6535.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6139.0', 'Key_SecurityID'='2304.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='3116.0', 'Key_SecurityID'='9938.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='6092.0', 'Key_SecurityID'='3859.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9801.0', 'Key_SecurityID'='400.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='9420.0', 'Key_SecurityID'='5210.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='276.0', 'Key_SecurityID'='6770.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='8631.0', 'Key_SecurityID'='4783.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}, {"keys": "'Key_AccountCode'='195.0', 'Key_SecurityID'='5668.0'", "message": "MISSING IN JITENDRA_R1;", "missing_in": ["JITENDRA_R1"], "file_names": "None, 'JITENDRA_R2_file_name'='random_data_R2_1739530358084.csv', 'JITENDRA_R3_file_name'='random_data_R3_*************.csv'", "false_positive_missings": {"JITENDRA_R1": false, "JITENDRA_R2": null, "JITENDRA_R3": null}}], "adhoc_queries_result": [], "validation_exec_details": [{"resource_id": 2193, "resource_name": "JITENDRA_R1", "total_records_count": 3000, "total_common_records": 3000, "total_columns_count": 11, "filtered_records_by_rule_filters": 0, "unique_records": 3000, "missing_records_count": 7000, "mismatched_records_count": 3, "false_positive_missing_records_count": 0, "file_names": ["random_data_R1_1739530333857.csv"], "validation_result_id": 59824, "validation_result": {"is_success": false, "resource_id": 2193, "resource_name": "JITENDRA_R1", "resource_type": "JITENDRA_R1", "domain_id": 1319, "domain_name": "Performance_test", "execution_time": "2025-02-14 11:32:39.598540", "run_id": 1, "run_name": "Legacy", "current_url": null, "is_detailed_execution_data_available": false, "comparison_execution_id": 1054820, "additional_properties": {"summary_mode": false, "total_records": 3000, "unique_records": 3000, "filtered_records_by_null_values_in_unique_keys": 0, "filtered_records_by_resource_filters": 0, "total_invalid_records": 3000, "total_duplicate_records": 0, "total_validation_errors": 6069, "total_time": 3.262794, "current_url": "https://172.176.208.22/resource/1319/view/2193", "supporting_documents": {"validation_report": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\validation_result\\JITENDRA_R1_validation_report_02_14_2025_113239.csv", "null_keys_report": null, "filter_records_report": null, "cross_field_validation_report_paths": null, "downloded_file_paths": null}, "validation_errors_summary": [{"column_name": "Column3", "total_validation_errors": 69, "validation_severity": "high_severity", "general_validation_errors": null, "custom_validation_errros": [{"custom_validation_name": "cv1", "total_validation_errors": 69}], "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1218.0, 'Column2'=4275.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1817.0, 'Column2'=4344.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9547.0, 'Column2'=6811.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8661.0, 'Column2'=3065.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=427.0, 'Column2'=9909.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4562.0, 'Column2'=7320.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2512.0, 'Column2'=3396.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4457.0, 'Column2'=8483.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5232.0, 'Column2'=4816.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7437.0, 'Column2'=5850.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9686.0, 'Column2'=6803.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7923.0, 'Column2'=5016.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3745.0, 'Column2'=1228.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5205.0, 'Column2'=1587.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3271.0, 'Column2'=9734.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=189.0, 'Column2'=5765.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1193.0, 'Column2'=3469.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3100.0, 'Column2'=8858.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1370.0, 'Column2'=6285.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5501.0, 'Column2'=8363.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1450.0, 'Column2'=4189.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1774.0, 'Column2'=4142.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5314.0, 'Column2'=6470.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2331.0, 'Column2'=3765.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5266.0, 'Column2'=4886.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6561.0, 'Column2'=1288.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2804.0, 'Column2'=3313.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4843.0, 'Column2'=4762.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=531.0, 'Column2'=87.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=278.0, 'Column2'=6279.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4715.0, 'Column2'=5049.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1477.0, 'Column2'=3927.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1831.0, 'Column2'=9824.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6242.0, 'Column2'=5171.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3284.0, 'Column2'=7062.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5298.0, 'Column2'=3197.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=7469.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6272.0, 'Column2'=2524.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7757.0, 'Column2'=7571.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6841.0, 'Column2'=3678.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2952.0, 'Column2'=4482.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1221.0, 'Column2'=8202.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3888.0, 'Column2'=3890.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2168.0, 'Column2'=4565.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2855.0, 'Column2'=2535.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=573.0, 'Column2'=9389.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1563.0, 'Column2'=4827.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2950.0, 'Column2'=2638.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3832.0, 'Column2'=5879.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=588.0, 'Column2'=2211.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3533.0, 'Column2'=9442.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9108.0, 'Column2'=8403.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7427.0, 'Column2'=5717.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5076.0, 'Column2'=1456.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4860.0, 'Column2'=6437.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=5276.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7261.0, 'Column2'=9847.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8815.0, 'Column2'=7017.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7408.0, 'Column2'=3780.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5564.0, 'Column2'=9642.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5440.0, 'Column2'=6725.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4956.0, 'Column2'=760.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1268.0, 'Column2'=3456.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9606.0, 'Column2'=5828.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R1_1739530333857.csv'"}]}, {"column_name": "Column6", "total_validation_errors": 3000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 3000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2024-12-16T08:28:30.261393 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2021-11-01T23:10:37.144212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2021-10-04T22:40:03.747067 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2021-04-08T23:46:44.621862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-07-05T05:27:55.278786 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2024-01-07T17:32:15.019202 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2021-02-27T11:54:29.624460 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2022-12-20T07:59:49.291812 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2022-04-20T02:38:37.014267 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2024-10-09T02:25:16.262605 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6396.0, 'Column2'=2261.0", "custom_validation_name": null, "error_message": "2021-08-27T15:50:58.412309 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4926.0, 'Column2'=7729.0", "custom_validation_name": null, "error_message": "2021-05-05T12:00:44.663010 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1463.0, 'Column2'=7394.0", "custom_validation_name": null, "error_message": "2022-10-22T15:09:57.193432 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2562.0, 'Column2'=9972.0", "custom_validation_name": null, "error_message": "2023-08-04T10:00:33.126206 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4019.0, 'Column2'=2225.0", "custom_validation_name": null, "error_message": "2020-06-06T02:43:23.501212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4497.0, 'Column2'=783.0", "custom_validation_name": null, "error_message": "2023-10-28T18:01:11.028476 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7651.0, 'Column2'=3648.0", "custom_validation_name": null, "error_message": "2022-11-04T07:25:55.817272 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5117.0, 'Column2'=450.0", "custom_validation_name": null, "error_message": "2020-05-17T13:18:39.495529 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6991.0, 'Column2'=5477.0", "custom_validation_name": null, "error_message": "2021-06-13T10:32:59.971181 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2007.0, 'Column2'=9390.0", "custom_validation_name": null, "error_message": "2020-02-26T05:28:15.203769 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=60.0, 'Column2'=5857.0", "custom_validation_name": null, "error_message": "2021-06-04T07:20:40.737620 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1569.0, 'Column2'=5131.0", "custom_validation_name": null, "error_message": "2022-04-30T20:42:37.812099 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7099.0, 'Column2'=3546.0", "custom_validation_name": null, "error_message": "2022-05-25T13:58:23.135527 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6838.0, 'Column2'=6008.0", "custom_validation_name": null, "error_message": "2023-11-26T12:03:54.537112 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6437.0, 'Column2'=826.0", "custom_validation_name": null, "error_message": "2024-04-23T01:11:39.696862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9106.0, 'Column2'=7692.0", "custom_validation_name": null, "error_message": "2021-06-24T12:49:41.964361 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1273.0, 'Column2'=9343.0", "custom_validation_name": null, "error_message": "2022-06-02T05:28:54.968239 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=452.0, 'Column2'=2348.0", "custom_validation_name": null, "error_message": "2021-07-12T04:01:16.423357 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7737.0, 'Column2'=4480.0", "custom_validation_name": null, "error_message": "2024-04-06T14:22:10.082745 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=2727.0", "custom_validation_name": null, "error_message": "2022-12-27T07:40:07.506321 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1566.0, 'Column2'=9368.0", "custom_validation_name": null, "error_message": "2020-10-10T01:30:35.674845 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4980.0, 'Column2'=5741.0", "custom_validation_name": null, "error_message": "2020-06-02T14:04:33.888319 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=269.0, 'Column2'=2496.0", "custom_validation_name": null, "error_message": "2021-06-16T13:42:37.193746 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7021.0, 'Column2'=4108.0", "custom_validation_name": null, "error_message": "2023-10-29T16:36:35.700299 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6616.0, 'Column2'=3294.0", "custom_validation_name": null, "error_message": "2020-11-28T11:40:15.138620 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7082.0, 'Column2'=5094.0", "custom_validation_name": null, "error_message": "2020-11-17T01:27:59.260323 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2025.0, 'Column2'=5518.0", "custom_validation_name": null, "error_message": "2024-01-19T05:14:12.668187 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1868.0, 'Column2'=9757.0", "custom_validation_name": null, "error_message": "2022-10-27T13:10:17.797493 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6100.0, 'Column2'=6568.0", "custom_validation_name": null, "error_message": "2024-07-08T11:39:01.809308 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5876.0, 'Column2'=7471.0", "custom_validation_name": null, "error_message": "2022-06-30T00:10:32.101972 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3080.0, 'Column2'=7952.0", "custom_validation_name": null, "error_message": "2023-01-29T18:25:17.793075 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1134.0, 'Column2'=3074.0", "custom_validation_name": null, "error_message": "2024-12-21T17:23:37.477971 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=9819.0", "custom_validation_name": null, "error_message": "2023-01-29T19:02:40.766303 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=350.0, 'Column2'=9020.0", "custom_validation_name": null, "error_message": "2022-08-13T16:35:53.869119 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2853.0, 'Column2'=1494.0", "custom_validation_name": null, "error_message": "2020-02-24T21:54:26.246800 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9690.0, 'Column2'=3529.0", "custom_validation_name": null, "error_message": "2023-01-18T14:54:23.207845 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=924.0, 'Column2'=8101.0", "custom_validation_name": null, "error_message": "2020-07-01T08:02:10.620425 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8246.0, 'Column2'=3081.0", "custom_validation_name": null, "error_message": "2021-09-02T10:29:11.061902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7238.0, 'Column2'=8031.0", "custom_validation_name": null, "error_message": "2024-11-11T20:39:29.221639 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=2946.0", "custom_validation_name": null, "error_message": "2024-03-24T09:51:52.082899 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6555.0, 'Column2'=3965.0", "custom_validation_name": null, "error_message": "2023-11-18T20:53:05.621935 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7100.0, 'Column2'=4436.0", "custom_validation_name": null, "error_message": "2021-07-29T10:39:53.443304 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1808.0, 'Column2'=5521.0", "custom_validation_name": null, "error_message": "2022-11-27T21:18:53.508144 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5916.0, 'Column2'=6318.0", "custom_validation_name": null, "error_message": "2023-08-27T09:14:24.812235 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4687.0, 'Column2'=3600.0", "custom_validation_name": null, "error_message": "2022-09-30T11:05:06.433463 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=2539.0", "custom_validation_name": null, "error_message": "2021-10-02T06:48:22.495285 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4511.0, 'Column2'=5752.0", "custom_validation_name": null, "error_message": "2024-04-20T16:57:31.943779 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6593.0, 'Column2'=9741.0", "custom_validation_name": null, "error_message": "2024-03-24T11:54:26.531508 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2521.0, 'Column2'=4682.0", "custom_validation_name": null, "error_message": "2020-11-09T19:42:35.577206 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7690.0, 'Column2'=9451.0", "custom_validation_name": null, "error_message": "2024-01-24T17:34:37.801847 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1661.0, 'Column2'=8725.0", "custom_validation_name": null, "error_message": "2022-10-18T05:32:58.684396 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=382.0, 'Column2'=611.0", "custom_validation_name": null, "error_message": "2021-01-29T19:40:33.479117 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7698.0, 'Column2'=126.0", "custom_validation_name": null, "error_message": "2022-08-24T18:40:13.574663 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4134.0, 'Column2'=2905.0", "custom_validation_name": null, "error_message": "2020-04-02T19:55:55.216137 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3625.0, 'Column2'=753.0", "custom_validation_name": null, "error_message": "2021-07-05T03:36:40.976136 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3204.0, 'Column2'=4364.0", "custom_validation_name": null, "error_message": "2022-07-21T05:10:12.503542 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4533.0, 'Column2'=8345.0", "custom_validation_name": null, "error_message": "2024-09-25T22:44:48.796764 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8296.0, 'Column2'=9233.0", "custom_validation_name": null, "error_message": "2020-03-05T16:32:40.852441 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5542.0, 'Column2'=5715.0", "custom_validation_name": null, "error_message": "2023-10-18T09:32:23.489753 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": null, "error_message": "2023-12-10T20:17:30.688296 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2141.0, 'Column2'=3631.0", "custom_validation_name": null, "error_message": "2023-02-01T05:21:25.243086 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8365.0, 'Column2'=5962.0", "custom_validation_name": null, "error_message": "2021-03-11T19:31:33.133644 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=3693.0", "custom_validation_name": null, "error_message": "2023-07-09T05:27:33.886542 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": null, "error_message": "2021-06-23T21:10:33.797920 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2009.0, 'Column2'=3526.0", "custom_validation_name": null, "error_message": "2020-08-25T23:58:14.689434 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1166.0, 'Column2'=7142.0", "custom_validation_name": null, "error_message": "2021-09-22T03:34:41.936657 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=8978.0", "custom_validation_name": null, "error_message": "2023-03-13T16:43:02.141687 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7121.0, 'Column2'=940.0", "custom_validation_name": null, "error_message": "2022-09-17T11:50:05.093931 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7854.0, 'Column2'=5679.0", "custom_validation_name": null, "error_message": "2023-08-03T03:46:17.117520 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5478.0, 'Column2'=3264.0", "custom_validation_name": null, "error_message": "2021-04-27T09:20:50.017659 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8499.0, 'Column2'=1781.0", "custom_validation_name": null, "error_message": "2024-12-19T15:32:16.459902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2219.0, 'Column2'=3425.0", "custom_validation_name": null, "error_message": "2021-12-12T03:05:33.636490 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7968.0, 'Column2'=2581.0", "custom_validation_name": null, "error_message": "2022-03-15T18:00:42.351450 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7409.0, 'Column2'=3591.0", "custom_validation_name": null, "error_message": "2022-06-27T00:17:56.846807 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1663.0, 'Column2'=5373.0", "custom_validation_name": null, "error_message": "2020-09-25T09:53:47.464502 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2291.0, 'Column2'=7646.0", "custom_validation_name": null, "error_message": "2024-07-31T16:47:31.769712 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6045.0, 'Column2'=1691.0", "custom_validation_name": null, "error_message": "2020-07-14T11:29:37.883313 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7054.0, 'Column2'=2804.0", "custom_validation_name": null, "error_message": "2020-04-02T07:53:18.028208 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7724.0, 'Column2'=5781.0", "custom_validation_name": null, "error_message": "2020-01-27T01:08:44.877221 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3581.0, 'Column2'=8894.0", "custom_validation_name": null, "error_message": "2024-05-06T17:06:16.883752 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1230.0, 'Column2'=7916.0", "custom_validation_name": null, "error_message": "2024-06-01T08:55:39.556947 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3844.0, 'Column2'=1013.0", "custom_validation_name": null, "error_message": "2020-02-12T09:39:20.720809 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5475.0, 'Column2'=9383.0", "custom_validation_name": null, "error_message": "2020-07-10T07:42:11.270281 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3435.0, 'Column2'=6495.0", "custom_validation_name": null, "error_message": "2021-01-24T17:03:02.160093 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6448.0, 'Column2'=3283.0", "custom_validation_name": null, "error_message": "2021-12-01T18:16:03.772331 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1594.0, 'Column2'=1216.0", "custom_validation_name": null, "error_message": "2022-10-16T02:01:45.243440 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6650.0, 'Column2'=7901.0", "custom_validation_name": null, "error_message": "2021-05-27T23:57:56.353643 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2782.0, 'Column2'=9796.0", "custom_validation_name": null, "error_message": "2022-09-24T00:04:17.915957 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=878.0, 'Column2'=2405.0", "custom_validation_name": null, "error_message": "2024-02-20T12:08:21.305734 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3764.0, 'Column2'=9917.0", "custom_validation_name": null, "error_message": "2020-06-18T21:25:28.170829 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}]}, {"column_name": "Column10", "total_validation_errors": 3000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 3000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2022-12-15T08:38:49.322982 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2022-12-22T21:16:55.190121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2024-04-08T15:54:18.916347 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2023-05-12T04:59:59.114433 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-01-03T02:11:21.638074 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2021-02-19T13:04:49.371856 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2020-02-12T05:36:05.933684 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2021-12-22T18:20:05.098660 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2020-10-22T14:21:08.786939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2021-11-18T17:08:48.456770 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6396.0, 'Column2'=2261.0", "custom_validation_name": null, "error_message": "2023-06-19T22:15:43.436526 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4926.0, 'Column2'=7729.0", "custom_validation_name": null, "error_message": "2022-11-13T01:09:48.806556 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1463.0, 'Column2'=7394.0", "custom_validation_name": null, "error_message": "2023-10-04T09:42:12.911893 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2562.0, 'Column2'=9972.0", "custom_validation_name": null, "error_message": "2022-08-17T22:57:53.973426 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4019.0, 'Column2'=2225.0", "custom_validation_name": null, "error_message": "2020-05-09T15:23:01.915999 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4497.0, 'Column2'=783.0", "custom_validation_name": null, "error_message": "2023-11-08T13:08:32.310594 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7651.0, 'Column2'=3648.0", "custom_validation_name": null, "error_message": "2020-01-31T01:29:20.875583 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5117.0, 'Column2'=450.0", "custom_validation_name": null, "error_message": "2020-01-06T23:28:43.895426 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6991.0, 'Column2'=5477.0", "custom_validation_name": null, "error_message": "2020-04-09T04:21:17.239096 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2007.0, 'Column2'=9390.0", "custom_validation_name": null, "error_message": "2021-02-24T06:28:19.453979 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=60.0, 'Column2'=5857.0", "custom_validation_name": null, "error_message": "2022-11-02T13:32:45.166606 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1569.0, 'Column2'=5131.0", "custom_validation_name": null, "error_message": "2021-02-10T02:28:39.174902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7099.0, 'Column2'=3546.0", "custom_validation_name": null, "error_message": "2023-08-31T02:12:27.263022 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6838.0, 'Column2'=6008.0", "custom_validation_name": null, "error_message": "2022-07-10T21:54:50.458963 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6437.0, 'Column2'=826.0", "custom_validation_name": null, "error_message": "2022-06-22T21:30:53.878583 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9106.0, 'Column2'=7692.0", "custom_validation_name": null, "error_message": "2020-12-31T13:13:21.983182 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1273.0, 'Column2'=9343.0", "custom_validation_name": null, "error_message": "2024-05-07T07:26:51.719931 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=452.0, 'Column2'=2348.0", "custom_validation_name": null, "error_message": "2022-06-22T10:04:21.986729 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7737.0, 'Column2'=4480.0", "custom_validation_name": null, "error_message": "2021-05-01T05:23:30.510495 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=2727.0", "custom_validation_name": null, "error_message": "2020-08-02T02:01:05.332920 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1566.0, 'Column2'=9368.0", "custom_validation_name": null, "error_message": "2024-10-18T01:30:33.577282 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4980.0, 'Column2'=5741.0", "custom_validation_name": null, "error_message": "2024-07-04T17:13:57.706008 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=269.0, 'Column2'=2496.0", "custom_validation_name": null, "error_message": "2020-04-19T13:21:06.749604 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7021.0, 'Column2'=4108.0", "custom_validation_name": null, "error_message": "2022-09-11T19:06:03.135922 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6616.0, 'Column2'=3294.0", "custom_validation_name": null, "error_message": "2023-10-13T22:46:46.748961 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7082.0, 'Column2'=5094.0", "custom_validation_name": null, "error_message": "2022-08-09T02:50:17.670284 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2025.0, 'Column2'=5518.0", "custom_validation_name": null, "error_message": "2023-10-20T21:44:39.759039 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1868.0, 'Column2'=9757.0", "custom_validation_name": null, "error_message": "2020-05-22T06:28:39.675081 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6100.0, 'Column2'=6568.0", "custom_validation_name": null, "error_message": "2022-05-08T02:06:38.002186 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5876.0, 'Column2'=7471.0", "custom_validation_name": null, "error_message": "2021-09-12T18:05:30.924341 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3080.0, 'Column2'=7952.0", "custom_validation_name": null, "error_message": "2020-06-24T02:51:13.759078 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1134.0, 'Column2'=3074.0", "custom_validation_name": null, "error_message": "2024-10-19T09:43:29.523091 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=9819.0", "custom_validation_name": null, "error_message": "2024-04-19T06:40:44.629241 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=350.0, 'Column2'=9020.0", "custom_validation_name": null, "error_message": "2022-01-31T09:08:08.105423 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2853.0, 'Column2'=1494.0", "custom_validation_name": null, "error_message": "2024-05-30T14:08:32.197022 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9690.0, 'Column2'=3529.0", "custom_validation_name": null, "error_message": "2023-09-03T01:14:48.811678 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=924.0, 'Column2'=8101.0", "custom_validation_name": null, "error_message": "2021-06-02T04:22:14.423733 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8246.0, 'Column2'=3081.0", "custom_validation_name": null, "error_message": "2021-11-27T10:58:51.409108 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7238.0, 'Column2'=8031.0", "custom_validation_name": null, "error_message": "2020-12-02T23:25:23.561857 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=2946.0", "custom_validation_name": null, "error_message": "2021-03-08T10:28:31.278200 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6555.0, 'Column2'=3965.0", "custom_validation_name": null, "error_message": "2022-11-19T00:40:43.390153 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7100.0, 'Column2'=4436.0", "custom_validation_name": null, "error_message": "2023-11-18T14:06:12.709492 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1808.0, 'Column2'=5521.0", "custom_validation_name": null, "error_message": "2024-10-25T22:44:50.860866 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5916.0, 'Column2'=6318.0", "custom_validation_name": null, "error_message": "2024-03-19T09:02:50.554214 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4687.0, 'Column2'=3600.0", "custom_validation_name": null, "error_message": "2020-02-06T03:41:08.280729 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=2539.0", "custom_validation_name": null, "error_message": "2020-05-19T20:16:25.040374 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4511.0, 'Column2'=5752.0", "custom_validation_name": null, "error_message": "2024-06-02T18:01:08.728363 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6593.0, 'Column2'=9741.0", "custom_validation_name": null, "error_message": "2023-10-15T09:49:57.158881 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2521.0, 'Column2'=4682.0", "custom_validation_name": null, "error_message": "2023-04-07T22:13:42.609957 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7690.0, 'Column2'=9451.0", "custom_validation_name": null, "error_message": "2024-06-18T21:03:26.622015 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1661.0, 'Column2'=8725.0", "custom_validation_name": null, "error_message": "2021-07-26T05:50:02.962948 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=382.0, 'Column2'=611.0", "custom_validation_name": null, "error_message": "2021-05-03T13:50:36.608121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7698.0, 'Column2'=126.0", "custom_validation_name": null, "error_message": "2023-02-25T03:31:09.726174 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4134.0, 'Column2'=2905.0", "custom_validation_name": null, "error_message": "2022-10-30T14:30:38.900473 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3625.0, 'Column2'=753.0", "custom_validation_name": null, "error_message": "2023-01-09T10:15:31.390483 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3204.0, 'Column2'=4364.0", "custom_validation_name": null, "error_message": "2023-06-05T08:26:23.860199 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=4533.0, 'Column2'=8345.0", "custom_validation_name": null, "error_message": "2024-08-01T05:36:18.356111 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8296.0, 'Column2'=9233.0", "custom_validation_name": null, "error_message": "2024-12-17T21:05:44.806689 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5542.0, 'Column2'=5715.0", "custom_validation_name": null, "error_message": "2021-05-11T17:50:37.839088 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": null, "error_message": "2024-04-12T00:20:59.981113 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2141.0, 'Column2'=3631.0", "custom_validation_name": null, "error_message": "2022-05-20T11:10:40.204939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8365.0, 'Column2'=5962.0", "custom_validation_name": null, "error_message": "2020-07-22T23:27:25.790968 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=3693.0", "custom_validation_name": null, "error_message": "2022-04-30T20:25:53.462315 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": null, "error_message": "2021-07-02T18:12:11.369046 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2009.0, 'Column2'=3526.0", "custom_validation_name": null, "error_message": "2020-08-02T23:21:13.531829 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1166.0, 'Column2'=7142.0", "custom_validation_name": null, "error_message": "2022-02-04T17:15:14.098010 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=8978.0", "custom_validation_name": null, "error_message": "2022-05-15T04:01:38.133667 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7121.0, 'Column2'=940.0", "custom_validation_name": null, "error_message": "2021-05-12T10:41:05.790807 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7854.0, 'Column2'=5679.0", "custom_validation_name": null, "error_message": "2020-11-09T10:30:12.708211 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5478.0, 'Column2'=3264.0", "custom_validation_name": null, "error_message": "2024-06-08T11:11:57.315521 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=8499.0, 'Column2'=1781.0", "custom_validation_name": null, "error_message": "2020-01-16T06:15:39.093263 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2219.0, 'Column2'=3425.0", "custom_validation_name": null, "error_message": "2023-10-05T05:45:06.951248 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7968.0, 'Column2'=2581.0", "custom_validation_name": null, "error_message": "2024-07-19T07:10:33.375646 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7409.0, 'Column2'=3591.0", "custom_validation_name": null, "error_message": "2021-09-07T02:29:52.788506 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1663.0, 'Column2'=5373.0", "custom_validation_name": null, "error_message": "2021-11-05T14:50:25.342896 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2291.0, 'Column2'=7646.0", "custom_validation_name": null, "error_message": "2023-03-08T05:16:52.746470 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6045.0, 'Column2'=1691.0", "custom_validation_name": null, "error_message": "2021-05-01T21:05:18.971381 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7054.0, 'Column2'=2804.0", "custom_validation_name": null, "error_message": "2023-12-24T08:10:35.593937 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=7724.0, 'Column2'=5781.0", "custom_validation_name": null, "error_message": "2024-03-21T11:34:19.880888 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3581.0, 'Column2'=8894.0", "custom_validation_name": null, "error_message": "2024-02-09T19:18:26.423769 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1230.0, 'Column2'=7916.0", "custom_validation_name": null, "error_message": "2024-06-27T03:25:07.223783 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3844.0, 'Column2'=1013.0", "custom_validation_name": null, "error_message": "2021-01-26T16:36:31.728071 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=5475.0, 'Column2'=9383.0", "custom_validation_name": null, "error_message": "2024-02-16T05:37:31.096911 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3435.0, 'Column2'=6495.0", "custom_validation_name": null, "error_message": "2023-04-08T05:18:51.150297 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6448.0, 'Column2'=3283.0", "custom_validation_name": null, "error_message": "2020-06-24T21:29:16.753700 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=1594.0, 'Column2'=1216.0", "custom_validation_name": null, "error_message": "2020-03-04T18:59:35.441438 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=6650.0, 'Column2'=7901.0", "custom_validation_name": null, "error_message": "2022-10-07T04:27:04.778222 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=2782.0, 'Column2'=9796.0", "custom_validation_name": null, "error_message": "2022-03-23T01:33:19.517870 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=878.0, 'Column2'=2405.0", "custom_validation_name": null, "error_message": "2020-05-10T07:50:49.027321 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}, {"key_columns": "'Column1'=3764.0, 'Column2'=9917.0", "custom_validation_name": null, "error_message": "2023-11-01T14:28:11.049428 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R1_1739530333857.csv'"}]}], "cross_field_validation_results": [], "base_resource_validation_result": null}, "validation_parameters": {"resource_id": 2193, "resource_name": "JITENDRA_R1", "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_names": ["random_data_R1_1739530333857.csv"], "file_processing_attributes": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "inline_variables": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "resource_column_details_version": 5, "resource_version": 5, "domain_version": 5, "filter_rules": []}, "prev_execution_id": null, "is_rerun": null, "complete_validation_params": {"validation_parameters": {"resource_data": {"resource_id": 2193, "resource_prefix": null, "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_name": "random_data_R1_1739530333857.csv", "column_delimiter": ",", "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": [], "api_request": {"url": null, "method": "get", "content_type": "application/json", "body": null, "query_params": null, "url_params": null, "user_name": null, "password": null, "bearer_token": null, "api_key": null, "oauth_client_id": null, "oauth_client_secret": null, "oauth_url": null, "request_timeout": 0}, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}, "sampling_validation": null, "sample_size": null, "output_storage_params": null, "run_instance": null, "severity_level": null, "severity_column_names": null, "validation_execution_report_name": null, "inline_variables": null, "pull_latest_files": null, "query_params": null, "latest_execution_id": null, "is_rerun": false}, "no_of_errors_in_response": 100, "no_of_sample_validation_errors": 1000000, "no_of_errors_in_output_files": 1000, "no_of_errors_in_filter_record_output_files": 1000, "summary_mode": false, "generate_files": true, "save_input_data_file": false, "skip_duplicate_records": true, "keep_downloaded_files": false, "file_names": [], "resource_id": 2193, "resource_column_details_version": 5, "resource_version": 5, "domain_version": 5, "store_errors_snapshots_and_create_issues": true, "inline_variables": {}}}}, {"resource_id": 2194, "resource_name": "JITENDRA_R2", "total_records_count": 3100, "total_common_records": 3000, "total_columns_count": 11, "filtered_records_by_rule_filters": 0, "unique_records": 3100, "missing_records_count": 6900, "mismatched_records_count": 3, "false_positive_missing_records_count": 0, "file_names": ["random_data_R2_1739530358084.csv"], "validation_result_id": 59825, "validation_result": {"is_success": false, "resource_id": 2194, "resource_name": "JITENDRA_R2", "resource_type": "JITENDRA_R2", "domain_id": 1319, "domain_name": "Performance_test", "execution_time": "2025-02-14 11:32:39.598540", "run_id": 1, "run_name": "Legacy", "current_url": null, "is_detailed_execution_data_available": false, "comparison_execution_id": 1054820, "additional_properties": {"summary_mode": false, "total_records": 3100, "unique_records": 3100, "filtered_records_by_null_values_in_unique_keys": 0, "filtered_records_by_resource_filters": 0, "total_invalid_records": 3100, "total_duplicate_records": 0, "total_validation_errors": 6268, "total_time": 7.250081, "current_url": "https://172.176.208.22/resource/1319/view/2194", "supporting_documents": {"validation_report": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\validation_result\\JITENDRA_R2_validation_report_02_14_2025_113239.csv", "null_keys_report": null, "filter_records_report": null, "cross_field_validation_report_paths": null, "downloded_file_paths": null}, "validation_errors_summary": [{"column_name": "Column3", "total_validation_errors": 68, "validation_severity": "high_severity", "general_validation_errors": null, "custom_validation_errros": [{"custom_validation_name": "cv1", "total_validation_errors": 68}], "validation_errors": [{"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1218.0, 'Column2'=4275.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1817.0, 'Column2'=4344.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9547.0, 'Column2'=6811.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8661.0, 'Column2'=3065.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=427.0, 'Column2'=9909.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4562.0, 'Column2'=7320.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2512.0, 'Column2'=3396.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4457.0, 'Column2'=8483.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5232.0, 'Column2'=4816.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7437.0, 'Column2'=5850.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9686.0, 'Column2'=6803.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7923.0, 'Column2'=5016.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3745.0, 'Column2'=1228.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5205.0, 'Column2'=1587.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3271.0, 'Column2'=9734.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=189.0, 'Column2'=5765.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1193.0, 'Column2'=3469.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3100.0, 'Column2'=8858.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1370.0, 'Column2'=6285.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5501.0, 'Column2'=8363.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1450.0, 'Column2'=4189.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1774.0, 'Column2'=4142.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5314.0, 'Column2'=6470.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2331.0, 'Column2'=3765.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5266.0, 'Column2'=4886.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6561.0, 'Column2'=1288.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2804.0, 'Column2'=3313.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4843.0, 'Column2'=4762.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=531.0, 'Column2'=87.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=278.0, 'Column2'=6279.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4715.0, 'Column2'=5049.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1477.0, 'Column2'=3927.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1831.0, 'Column2'=9824.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6242.0, 'Column2'=5171.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3284.0, 'Column2'=7062.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5298.0, 'Column2'=3197.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=7469.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6272.0, 'Column2'=2524.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7757.0, 'Column2'=7571.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6841.0, 'Column2'=3678.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2952.0, 'Column2'=4482.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1221.0, 'Column2'=8202.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3888.0, 'Column2'=3890.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2168.0, 'Column2'=4565.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2855.0, 'Column2'=2535.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=573.0, 'Column2'=9389.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1563.0, 'Column2'=4827.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2950.0, 'Column2'=2638.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3832.0, 'Column2'=5879.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=588.0, 'Column2'=2211.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3533.0, 'Column2'=9442.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9108.0, 'Column2'=8403.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7427.0, 'Column2'=5717.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5076.0, 'Column2'=1456.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4860.0, 'Column2'=6437.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=5276.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7261.0, 'Column2'=9847.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8815.0, 'Column2'=7017.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7408.0, 'Column2'=3780.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5564.0, 'Column2'=9642.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5440.0, 'Column2'=6725.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4956.0, 'Column2'=760.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1268.0, 'Column2'=3456.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9606.0, 'Column2'=5828.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3375.0, 'Column2'=2837.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7998.0, 'Column2'=4291.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R2_1739530358084.csv'"}]}, {"column_name": "Column6", "total_validation_errors": 3100, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 3100}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2024-12-16T08:28:30.261393 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2021-11-01T23:10:37.144212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2021-10-04T22:40:03.747067 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2021-04-08T23:46:44.621862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-07-05T05:27:55.278786 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2024-01-07T17:32:15.019202 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2021-02-27T11:54:29.624460 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2022-12-20T07:59:49.291812 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2022-04-20T02:38:37.014267 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2024-10-09T02:25:16.262605 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6396.0, 'Column2'=2261.0", "custom_validation_name": null, "error_message": "2021-08-27T15:50:58.412309 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4926.0, 'Column2'=7729.0", "custom_validation_name": null, "error_message": "2021-05-05T12:00:44.663010 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1463.0, 'Column2'=7394.0", "custom_validation_name": null, "error_message": "2022-10-22T15:09:57.193432 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2562.0, 'Column2'=9972.0", "custom_validation_name": null, "error_message": "2023-08-04T10:00:33.126206 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4019.0, 'Column2'=2225.0", "custom_validation_name": null, "error_message": "2020-06-06T02:43:23.501212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4497.0, 'Column2'=783.0", "custom_validation_name": null, "error_message": "2023-10-28T18:01:11.028476 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7651.0, 'Column2'=3648.0", "custom_validation_name": null, "error_message": "2022-11-04T07:25:55.817272 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5117.0, 'Column2'=450.0", "custom_validation_name": null, "error_message": "2020-05-17T13:18:39.495529 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6991.0, 'Column2'=5477.0", "custom_validation_name": null, "error_message": "2021-06-13T10:32:59.971181 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2007.0, 'Column2'=9390.0", "custom_validation_name": null, "error_message": "2020-02-26T05:28:15.203769 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=60.0, 'Column2'=5857.0", "custom_validation_name": null, "error_message": "2021-06-04T07:20:40.737620 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1569.0, 'Column2'=5131.0", "custom_validation_name": null, "error_message": "2022-04-30T20:42:37.812099 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7099.0, 'Column2'=3546.0", "custom_validation_name": null, "error_message": "2022-05-25T13:58:23.135527 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6838.0, 'Column2'=6008.0", "custom_validation_name": null, "error_message": "2023-11-26T12:03:54.537112 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6437.0, 'Column2'=826.0", "custom_validation_name": null, "error_message": "2024-04-23T01:11:39.696862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9106.0, 'Column2'=7692.0", "custom_validation_name": null, "error_message": "2021-06-24T12:49:41.964361 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1273.0, 'Column2'=9343.0", "custom_validation_name": null, "error_message": "2022-06-02T05:28:54.968239 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=452.0, 'Column2'=2348.0", "custom_validation_name": null, "error_message": "2021-07-12T04:01:16.423357 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7737.0, 'Column2'=4480.0", "custom_validation_name": null, "error_message": "2024-04-06T14:22:10.082745 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=2727.0", "custom_validation_name": null, "error_message": "2022-12-27T07:40:07.506321 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1566.0, 'Column2'=9368.0", "custom_validation_name": null, "error_message": "2020-10-10T01:30:35.674845 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4980.0, 'Column2'=5741.0", "custom_validation_name": null, "error_message": "2020-06-02T14:04:33.888319 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=269.0, 'Column2'=2496.0", "custom_validation_name": null, "error_message": "2021-06-16T13:42:37.193746 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7021.0, 'Column2'=4108.0", "custom_validation_name": null, "error_message": "2023-10-29T16:36:35.700299 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6616.0, 'Column2'=3294.0", "custom_validation_name": null, "error_message": "2020-11-28T11:40:15.138620 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7082.0, 'Column2'=5094.0", "custom_validation_name": null, "error_message": "2020-11-17T01:27:59.260323 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2025.0, 'Column2'=5518.0", "custom_validation_name": null, "error_message": "2024-01-19T05:14:12.668187 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1868.0, 'Column2'=9757.0", "custom_validation_name": null, "error_message": "2022-10-27T13:10:17.797493 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6100.0, 'Column2'=6568.0", "custom_validation_name": null, "error_message": "2024-07-08T11:39:01.809308 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5876.0, 'Column2'=7471.0", "custom_validation_name": null, "error_message": "2022-06-30T00:10:32.101972 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3080.0, 'Column2'=7952.0", "custom_validation_name": null, "error_message": "2023-01-29T18:25:17.793075 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1134.0, 'Column2'=3074.0", "custom_validation_name": null, "error_message": "2024-12-21T17:23:37.477971 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=9819.0", "custom_validation_name": null, "error_message": "2023-01-29T19:02:40.766303 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=350.0, 'Column2'=9020.0", "custom_validation_name": null, "error_message": "2022-08-13T16:35:53.869119 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2853.0, 'Column2'=1494.0", "custom_validation_name": null, "error_message": "2020-02-24T21:54:26.246800 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9690.0, 'Column2'=3529.0", "custom_validation_name": null, "error_message": "2023-01-18T14:54:23.207845 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=924.0, 'Column2'=8101.0", "custom_validation_name": null, "error_message": "2020-07-01T08:02:10.620425 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8246.0, 'Column2'=3081.0", "custom_validation_name": null, "error_message": "2021-09-02T10:29:11.061902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7238.0, 'Column2'=8031.0", "custom_validation_name": null, "error_message": "2024-11-11T20:39:29.221639 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=2946.0", "custom_validation_name": null, "error_message": "2024-03-24T09:51:52.082899 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6555.0, 'Column2'=3965.0", "custom_validation_name": null, "error_message": "2023-11-18T20:53:05.621935 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7100.0, 'Column2'=4436.0", "custom_validation_name": null, "error_message": "2021-07-29T10:39:53.443304 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1808.0, 'Column2'=5521.0", "custom_validation_name": null, "error_message": "2022-11-27T21:18:53.508144 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5916.0, 'Column2'=6318.0", "custom_validation_name": null, "error_message": "2023-08-27T09:14:24.812235 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4687.0, 'Column2'=3600.0", "custom_validation_name": null, "error_message": "2022-09-30T11:05:06.433463 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=2539.0", "custom_validation_name": null, "error_message": "2021-10-02T06:48:22.495285 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4511.0, 'Column2'=5752.0", "custom_validation_name": null, "error_message": "2024-04-20T16:57:31.943779 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6593.0, 'Column2'=9741.0", "custom_validation_name": null, "error_message": "2024-03-24T11:54:26.531508 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2521.0, 'Column2'=4682.0", "custom_validation_name": null, "error_message": "2020-11-09T19:42:35.577206 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7690.0, 'Column2'=9451.0", "custom_validation_name": null, "error_message": "2024-01-24T17:34:37.801847 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1661.0, 'Column2'=8725.0", "custom_validation_name": null, "error_message": "2022-10-18T05:32:58.684396 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=382.0, 'Column2'=611.0", "custom_validation_name": null, "error_message": "2021-01-29T19:40:33.479117 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7698.0, 'Column2'=126.0", "custom_validation_name": null, "error_message": "2022-08-24T18:40:13.574663 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4134.0, 'Column2'=2905.0", "custom_validation_name": null, "error_message": "2020-04-02T19:55:55.216137 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3625.0, 'Column2'=753.0", "custom_validation_name": null, "error_message": "2021-07-05T03:36:40.976136 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3204.0, 'Column2'=4364.0", "custom_validation_name": null, "error_message": "2022-07-21T05:10:12.503542 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4533.0, 'Column2'=8345.0", "custom_validation_name": null, "error_message": "2024-09-25T22:44:48.796764 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8296.0, 'Column2'=9233.0", "custom_validation_name": null, "error_message": "2020-03-05T16:32:40.852441 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5542.0, 'Column2'=5715.0", "custom_validation_name": null, "error_message": "2023-10-18T09:32:23.489753 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": null, "error_message": "2023-12-10T20:17:30.688296 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2141.0, 'Column2'=3631.0", "custom_validation_name": null, "error_message": "2023-02-01T05:21:25.243086 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8365.0, 'Column2'=5962.0", "custom_validation_name": null, "error_message": "2021-03-11T19:31:33.133644 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=3693.0", "custom_validation_name": null, "error_message": "2023-07-09T05:27:33.886542 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": null, "error_message": "2021-06-23T21:10:33.797920 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2009.0, 'Column2'=3526.0", "custom_validation_name": null, "error_message": "2020-08-25T23:58:14.689434 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1166.0, 'Column2'=7142.0", "custom_validation_name": null, "error_message": "2021-09-22T03:34:41.936657 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=8978.0", "custom_validation_name": null, "error_message": "2023-03-13T16:43:02.141687 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7121.0, 'Column2'=940.0", "custom_validation_name": null, "error_message": "2022-09-17T11:50:05.093931 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7854.0, 'Column2'=5679.0", "custom_validation_name": null, "error_message": "2023-08-03T03:46:17.117520 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5478.0, 'Column2'=3264.0", "custom_validation_name": null, "error_message": "2021-04-27T09:20:50.017659 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8499.0, 'Column2'=1781.0", "custom_validation_name": null, "error_message": "2024-12-19T15:32:16.459902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2219.0, 'Column2'=3425.0", "custom_validation_name": null, "error_message": "2021-12-12T03:05:33.636490 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7968.0, 'Column2'=2581.0", "custom_validation_name": null, "error_message": "2022-03-15T18:00:42.351450 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7409.0, 'Column2'=3591.0", "custom_validation_name": null, "error_message": "2022-06-27T00:17:56.846807 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1663.0, 'Column2'=5373.0", "custom_validation_name": null, "error_message": "2020-09-25T09:53:47.464502 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2291.0, 'Column2'=7646.0", "custom_validation_name": null, "error_message": "2024-07-31T16:47:31.769712 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6045.0, 'Column2'=1691.0", "custom_validation_name": null, "error_message": "2020-07-14T11:29:37.883313 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7054.0, 'Column2'=2804.0", "custom_validation_name": null, "error_message": "2020-04-02T07:53:18.028208 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7724.0, 'Column2'=5781.0", "custom_validation_name": null, "error_message": "2020-01-27T01:08:44.877221 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3581.0, 'Column2'=8894.0", "custom_validation_name": null, "error_message": "2024-05-06T17:06:16.883752 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1230.0, 'Column2'=7916.0", "custom_validation_name": null, "error_message": "2024-06-01T08:55:39.556947 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3844.0, 'Column2'=1013.0", "custom_validation_name": null, "error_message": "2020-02-12T09:39:20.720809 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5475.0, 'Column2'=9383.0", "custom_validation_name": null, "error_message": "2020-07-10T07:42:11.270281 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3435.0, 'Column2'=6495.0", "custom_validation_name": null, "error_message": "2021-01-24T17:03:02.160093 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6448.0, 'Column2'=3283.0", "custom_validation_name": null, "error_message": "2021-12-01T18:16:03.772331 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1594.0, 'Column2'=1216.0", "custom_validation_name": null, "error_message": "2022-10-16T02:01:45.243440 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6650.0, 'Column2'=7901.0", "custom_validation_name": null, "error_message": "2021-05-27T23:57:56.353643 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2782.0, 'Column2'=9796.0", "custom_validation_name": null, "error_message": "2022-09-24T00:04:17.915957 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=878.0, 'Column2'=2405.0", "custom_validation_name": null, "error_message": "2024-02-20T12:08:21.305734 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3764.0, 'Column2'=9917.0", "custom_validation_name": null, "error_message": "2020-06-18T21:25:28.170829 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}]}, {"column_name": "Column10", "total_validation_errors": 3100, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 3100}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2022-12-15T08:38:49.322982 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2022-12-22T21:16:55.190121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2024-04-08T15:54:18.916347 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2023-05-12T04:59:59.114433 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-01-03T02:11:21.638074 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2021-02-19T13:04:49.371856 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2020-02-12T05:36:05.933684 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2021-12-22T18:20:05.098660 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2020-10-22T14:21:08.786939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2021-11-18T17:08:48.456770 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6396.0, 'Column2'=2261.0", "custom_validation_name": null, "error_message": "2023-06-19T22:15:43.436526 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4926.0, 'Column2'=7729.0", "custom_validation_name": null, "error_message": "2022-11-13T01:09:48.806556 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1463.0, 'Column2'=7394.0", "custom_validation_name": null, "error_message": "2023-10-04T09:42:12.911893 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2562.0, 'Column2'=9972.0", "custom_validation_name": null, "error_message": "2022-08-17T22:57:53.973426 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4019.0, 'Column2'=2225.0", "custom_validation_name": null, "error_message": "2020-05-09T15:23:01.915999 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4497.0, 'Column2'=783.0", "custom_validation_name": null, "error_message": "2023-11-08T13:08:32.310594 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7651.0, 'Column2'=3648.0", "custom_validation_name": null, "error_message": "2020-01-31T01:29:20.875583 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5117.0, 'Column2'=450.0", "custom_validation_name": null, "error_message": "2020-01-06T23:28:43.895426 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6991.0, 'Column2'=5477.0", "custom_validation_name": null, "error_message": "2020-04-09T04:21:17.239096 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2007.0, 'Column2'=9390.0", "custom_validation_name": null, "error_message": "2021-02-24T06:28:19.453979 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=60.0, 'Column2'=5857.0", "custom_validation_name": null, "error_message": "2022-11-02T13:32:45.166606 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1569.0, 'Column2'=5131.0", "custom_validation_name": null, "error_message": "2021-02-10T02:28:39.174902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7099.0, 'Column2'=3546.0", "custom_validation_name": null, "error_message": "2023-08-31T02:12:27.263022 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6838.0, 'Column2'=6008.0", "custom_validation_name": null, "error_message": "2022-07-10T21:54:50.458963 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6437.0, 'Column2'=826.0", "custom_validation_name": null, "error_message": "2022-06-22T21:30:53.878583 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9106.0, 'Column2'=7692.0", "custom_validation_name": null, "error_message": "2020-12-31T13:13:21.983182 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1273.0, 'Column2'=9343.0", "custom_validation_name": null, "error_message": "2024-05-07T07:26:51.719931 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=452.0, 'Column2'=2348.0", "custom_validation_name": null, "error_message": "2022-06-22T10:04:21.986729 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7737.0, 'Column2'=4480.0", "custom_validation_name": null, "error_message": "2021-05-01T05:23:30.510495 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=2727.0", "custom_validation_name": null, "error_message": "2020-08-02T02:01:05.332920 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1566.0, 'Column2'=9368.0", "custom_validation_name": null, "error_message": "2024-10-18T01:30:33.577282 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4980.0, 'Column2'=5741.0", "custom_validation_name": null, "error_message": "2024-07-04T17:13:57.706008 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=269.0, 'Column2'=2496.0", "custom_validation_name": null, "error_message": "2020-04-19T13:21:06.749604 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7021.0, 'Column2'=4108.0", "custom_validation_name": null, "error_message": "2022-09-11T19:06:03.135922 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6616.0, 'Column2'=3294.0", "custom_validation_name": null, "error_message": "2023-10-13T22:46:46.748961 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7082.0, 'Column2'=5094.0", "custom_validation_name": null, "error_message": "2022-08-09T02:50:17.670284 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2025.0, 'Column2'=5518.0", "custom_validation_name": null, "error_message": "2023-10-20T21:44:39.759039 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1868.0, 'Column2'=9757.0", "custom_validation_name": null, "error_message": "2020-05-22T06:28:39.675081 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6100.0, 'Column2'=6568.0", "custom_validation_name": null, "error_message": "2022-05-08T02:06:38.002186 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5876.0, 'Column2'=7471.0", "custom_validation_name": null, "error_message": "2021-09-12T18:05:30.924341 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3080.0, 'Column2'=7952.0", "custom_validation_name": null, "error_message": "2020-06-24T02:51:13.759078 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1134.0, 'Column2'=3074.0", "custom_validation_name": null, "error_message": "2024-10-19T09:43:29.523091 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=9819.0", "custom_validation_name": null, "error_message": "2024-04-19T06:40:44.629241 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=350.0, 'Column2'=9020.0", "custom_validation_name": null, "error_message": "2022-01-31T09:08:08.105423 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2853.0, 'Column2'=1494.0", "custom_validation_name": null, "error_message": "2024-05-30T14:08:32.197022 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9690.0, 'Column2'=3529.0", "custom_validation_name": null, "error_message": "2023-09-03T01:14:48.811678 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=924.0, 'Column2'=8101.0", "custom_validation_name": null, "error_message": "2021-06-02T04:22:14.423733 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8246.0, 'Column2'=3081.0", "custom_validation_name": null, "error_message": "2021-11-27T10:58:51.409108 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7238.0, 'Column2'=8031.0", "custom_validation_name": null, "error_message": "2020-12-02T23:25:23.561857 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=2946.0", "custom_validation_name": null, "error_message": "2021-03-08T10:28:31.278200 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6555.0, 'Column2'=3965.0", "custom_validation_name": null, "error_message": "2022-11-19T00:40:43.390153 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7100.0, 'Column2'=4436.0", "custom_validation_name": null, "error_message": "2023-11-18T14:06:12.709492 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1808.0, 'Column2'=5521.0", "custom_validation_name": null, "error_message": "2024-10-25T22:44:50.860866 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5916.0, 'Column2'=6318.0", "custom_validation_name": null, "error_message": "2024-03-19T09:02:50.554214 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4687.0, 'Column2'=3600.0", "custom_validation_name": null, "error_message": "2020-02-06T03:41:08.280729 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=2539.0", "custom_validation_name": null, "error_message": "2020-05-19T20:16:25.040374 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4511.0, 'Column2'=5752.0", "custom_validation_name": null, "error_message": "2024-06-02T18:01:08.728363 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6593.0, 'Column2'=9741.0", "custom_validation_name": null, "error_message": "2023-10-15T09:49:57.158881 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2521.0, 'Column2'=4682.0", "custom_validation_name": null, "error_message": "2023-04-07T22:13:42.609957 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7690.0, 'Column2'=9451.0", "custom_validation_name": null, "error_message": "2024-06-18T21:03:26.622015 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1661.0, 'Column2'=8725.0", "custom_validation_name": null, "error_message": "2021-07-26T05:50:02.962948 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=382.0, 'Column2'=611.0", "custom_validation_name": null, "error_message": "2021-05-03T13:50:36.608121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7698.0, 'Column2'=126.0", "custom_validation_name": null, "error_message": "2023-02-25T03:31:09.726174 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4134.0, 'Column2'=2905.0", "custom_validation_name": null, "error_message": "2022-10-30T14:30:38.900473 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3625.0, 'Column2'=753.0", "custom_validation_name": null, "error_message": "2023-01-09T10:15:31.390483 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3204.0, 'Column2'=4364.0", "custom_validation_name": null, "error_message": "2023-06-05T08:26:23.860199 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=4533.0, 'Column2'=8345.0", "custom_validation_name": null, "error_message": "2024-08-01T05:36:18.356111 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8296.0, 'Column2'=9233.0", "custom_validation_name": null, "error_message": "2024-12-17T21:05:44.806689 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5542.0, 'Column2'=5715.0", "custom_validation_name": null, "error_message": "2021-05-11T17:50:37.839088 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": null, "error_message": "2024-04-12T00:20:59.981113 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2141.0, 'Column2'=3631.0", "custom_validation_name": null, "error_message": "2022-05-20T11:10:40.204939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8365.0, 'Column2'=5962.0", "custom_validation_name": null, "error_message": "2020-07-22T23:27:25.790968 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=3693.0", "custom_validation_name": null, "error_message": "2022-04-30T20:25:53.462315 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": null, "error_message": "2021-07-02T18:12:11.369046 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2009.0, 'Column2'=3526.0", "custom_validation_name": null, "error_message": "2020-08-02T23:21:13.531829 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1166.0, 'Column2'=7142.0", "custom_validation_name": null, "error_message": "2022-02-04T17:15:14.098010 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=8978.0", "custom_validation_name": null, "error_message": "2022-05-15T04:01:38.133667 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7121.0, 'Column2'=940.0", "custom_validation_name": null, "error_message": "2021-05-12T10:41:05.790807 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7854.0, 'Column2'=5679.0", "custom_validation_name": null, "error_message": "2020-11-09T10:30:12.708211 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5478.0, 'Column2'=3264.0", "custom_validation_name": null, "error_message": "2024-06-08T11:11:57.315521 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=8499.0, 'Column2'=1781.0", "custom_validation_name": null, "error_message": "2020-01-16T06:15:39.093263 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2219.0, 'Column2'=3425.0", "custom_validation_name": null, "error_message": "2023-10-05T05:45:06.951248 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7968.0, 'Column2'=2581.0", "custom_validation_name": null, "error_message": "2024-07-19T07:10:33.375646 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7409.0, 'Column2'=3591.0", "custom_validation_name": null, "error_message": "2021-09-07T02:29:52.788506 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1663.0, 'Column2'=5373.0", "custom_validation_name": null, "error_message": "2021-11-05T14:50:25.342896 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2291.0, 'Column2'=7646.0", "custom_validation_name": null, "error_message": "2023-03-08T05:16:52.746470 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6045.0, 'Column2'=1691.0", "custom_validation_name": null, "error_message": "2021-05-01T21:05:18.971381 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7054.0, 'Column2'=2804.0", "custom_validation_name": null, "error_message": "2023-12-24T08:10:35.593937 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=7724.0, 'Column2'=5781.0", "custom_validation_name": null, "error_message": "2024-03-21T11:34:19.880888 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3581.0, 'Column2'=8894.0", "custom_validation_name": null, "error_message": "2024-02-09T19:18:26.423769 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1230.0, 'Column2'=7916.0", "custom_validation_name": null, "error_message": "2024-06-27T03:25:07.223783 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3844.0, 'Column2'=1013.0", "custom_validation_name": null, "error_message": "2021-01-26T16:36:31.728071 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=5475.0, 'Column2'=9383.0", "custom_validation_name": null, "error_message": "2024-02-16T05:37:31.096911 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3435.0, 'Column2'=6495.0", "custom_validation_name": null, "error_message": "2023-04-08T05:18:51.150297 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6448.0, 'Column2'=3283.0", "custom_validation_name": null, "error_message": "2020-06-24T21:29:16.753700 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=1594.0, 'Column2'=1216.0", "custom_validation_name": null, "error_message": "2020-03-04T18:59:35.441438 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=6650.0, 'Column2'=7901.0", "custom_validation_name": null, "error_message": "2022-10-07T04:27:04.778222 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=2782.0, 'Column2'=9796.0", "custom_validation_name": null, "error_message": "2022-03-23T01:33:19.517870 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=878.0, 'Column2'=2405.0", "custom_validation_name": null, "error_message": "2020-05-10T07:50:49.027321 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}, {"key_columns": "'Column1'=3764.0, 'Column2'=9917.0", "custom_validation_name": null, "error_message": "2023-11-01T14:28:11.049428 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R2_1739530358084.csv'"}]}], "cross_field_validation_results": [], "base_resource_validation_result": null}, "validation_parameters": {"resource_id": 2194, "resource_name": "JITENDRA_R2", "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_names": ["random_data_R2_1739530358084.csv"], "file_processing_attributes": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "inline_variables": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "resource_column_details_version": 1, "resource_version": 4, "domain_version": 5, "filter_rules": []}, "prev_execution_id": null, "is_rerun": null, "complete_validation_params": {"validation_parameters": {"resource_data": {"resource_id": 2194, "resource_prefix": null, "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_name": "random_data_R2_1739530358084.csv", "column_delimiter": ",", "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": [], "api_request": {"url": null, "method": "get", "content_type": "application/json", "body": null, "query_params": null, "url_params": null, "user_name": null, "password": null, "bearer_token": null, "api_key": null, "oauth_client_id": null, "oauth_client_secret": null, "oauth_url": null, "request_timeout": 0}, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null}, "sampling_validation": null, "sample_size": null, "output_storage_params": null, "run_instance": null, "severity_level": null, "severity_column_names": null, "validation_execution_report_name": null, "inline_variables": null, "pull_latest_files": null, "query_params": null, "latest_execution_id": null, "is_rerun": false}, "no_of_errors_in_response": 100, "no_of_sample_validation_errors": 1000000, "no_of_errors_in_output_files": 1000, "no_of_errors_in_filter_record_output_files": 1000, "summary_mode": false, "generate_files": true, "save_input_data_file": false, "skip_duplicate_records": true, "keep_downloaded_files": false, "file_names": [], "resource_id": 2194, "resource_column_details_version": 1, "resource_version": 4, "domain_version": 5, "store_errors_snapshots_and_create_issues": true, "inline_variables": {}}}}, {"resource_id": 2195, "resource_name": "JITENDRA_R3", "total_records_count": 10000, "total_common_records": 3000, "total_columns_count": 11, "filtered_records_by_rule_filters": 0, "unique_records": 10000, "missing_records_count": 0, "mismatched_records_count": 3, "false_positive_missing_records_count": 0, "file_names": ["random_data_R3_*************.csv"], "validation_result_id": 59826, "validation_result": {"is_success": false, "resource_id": 2195, "resource_name": "JITENDRA_R3", "resource_type": "JITENDRA_R3", "domain_id": 1319, "domain_name": "Performance_test", "execution_time": "2025-02-14 11:32:39.598540", "run_id": 1, "run_name": "Legacy", "current_url": null, "is_detailed_execution_data_available": false, "comparison_execution_id": 1054820, "additional_properties": {"summary_mode": false, "total_records": 10000, "unique_records": 10000, "filtered_records_by_null_values_in_unique_keys": 0, "filtered_records_by_resource_filters": 0, "total_invalid_records": 10000, "total_duplicate_records": 0, "total_validation_errors": 20231, "total_time": 11.090123, "current_url": "https://172.176.208.22/resource/1319/view/2195", "supporting_documents": {"validation_report": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\validation_result\\JITENDRA_R3_validation_report_02_14_2025_113239.csv", "null_keys_report": null, "filter_records_report": null, "cross_field_validation_report_paths": null, "downloded_file_paths": null}, "validation_errors_summary": [{"column_name": "Column3", "total_validation_errors": 231, "validation_severity": "high_severity", "general_validation_errors": null, "custom_validation_errros": [{"custom_validation_name": "cv1", "total_validation_errors": 231}], "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1218.0, 'Column2'=4275.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1817.0, 'Column2'=4344.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9547.0, 'Column2'=6811.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8661.0, 'Column2'=3065.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=427.0, 'Column2'=9909.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4562.0, 'Column2'=7320.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2512.0, 'Column2'=3396.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4457.0, 'Column2'=8483.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5232.0, 'Column2'=4816.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7437.0, 'Column2'=5850.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9686.0, 'Column2'=6803.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7923.0, 'Column2'=5016.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3745.0, 'Column2'=1228.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5205.0, 'Column2'=1587.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3271.0, 'Column2'=9734.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=189.0, 'Column2'=5765.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1193.0, 'Column2'=3469.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3100.0, 'Column2'=8858.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1370.0, 'Column2'=6285.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5501.0, 'Column2'=8363.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1450.0, 'Column2'=4189.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1774.0, 'Column2'=4142.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5314.0, 'Column2'=6470.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2331.0, 'Column2'=3765.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5266.0, 'Column2'=4886.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6561.0, 'Column2'=1288.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2804.0, 'Column2'=3313.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4843.0, 'Column2'=4762.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=531.0, 'Column2'=87.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=278.0, 'Column2'=6279.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4715.0, 'Column2'=5049.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1477.0, 'Column2'=3927.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1831.0, 'Column2'=9824.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6242.0, 'Column2'=5171.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3284.0, 'Column2'=7062.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5298.0, 'Column2'=3197.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=7469.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6272.0, 'Column2'=2524.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7757.0, 'Column2'=7571.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6841.0, 'Column2'=3678.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2952.0, 'Column2'=4482.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1221.0, 'Column2'=8202.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3888.0, 'Column2'=3890.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2168.0, 'Column2'=4565.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2855.0, 'Column2'=2535.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=573.0, 'Column2'=9389.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1563.0, 'Column2'=4827.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2950.0, 'Column2'=2638.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3832.0, 'Column2'=5879.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=588.0, 'Column2'=2211.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3533.0, 'Column2'=9442.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9108.0, 'Column2'=8403.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7427.0, 'Column2'=5717.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5076.0, 'Column2'=1456.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4860.0, 'Column2'=6437.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=5276.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7261.0, 'Column2'=9847.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8815.0, 'Column2'=7017.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7408.0, 'Column2'=3780.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5564.0, 'Column2'=9642.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5440.0, 'Column2'=6725.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4956.0, 'Column2'=760.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1268.0, 'Column2'=3456.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9606.0, 'Column2'=5828.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3375.0, 'Column2'=2837.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7998.0, 'Column2'=4291.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7120.0, 'Column2'=2942.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9608.0, 'Column2'=8732.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9261.0, 'Column2'=3645.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4099.0, 'Column2'=5763.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7706.0, 'Column2'=6552.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4903.0, 'Column2'=555.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8768.0, 'Column2'=8615.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7611.0, 'Column2'=8271.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4465.0, 'Column2'=1224.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7676.0, 'Column2'=7978.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8415.0, 'Column2'=173.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1226.0, 'Column2'=8309.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8857.0, 'Column2'=2161.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4900.0, 'Column2'=1471.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9524.0, 'Column2'=4507.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9674.0, 'Column2'=3133.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2352.0, 'Column2'=2178.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1695.0, 'Column2'=3590.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5728.0, 'Column2'=8142.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7715.0, 'Column2'=8481.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7898.0, 'Column2'=4928.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=388.0, 'Column2'=9124.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4665.0, 'Column2'=5011.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=906.0, 'Column2'=5159.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=242.0, 'Column2'=3897.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3525.0, 'Column2'=9172.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=569.0, 'Column2'=1061.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=844.0, 'Column2'=6448.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4304.0, 'Column2'=3960.0", "custom_validation_name": "cv1", "error_message": "cv1:Validation failed for \"Column7\" > 200", "file_names": "'random_data_R3_*************.csv'"}]}, {"column_name": "Column6", "total_validation_errors": 10000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 10000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2024-12-16T08:28:30.261393 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2021-11-01T23:10:37.144212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2021-10-04T22:40:03.747067 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2021-04-08T23:46:44.621862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-07-05T05:27:55.278786 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2024-01-07T17:32:15.019202 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2021-02-27T11:54:29.624460 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2022-12-20T07:59:49.291812 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2022-04-20T02:38:37.014267 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2024-10-09T02:25:16.262605 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6396.0, 'Column2'=2261.0", "custom_validation_name": null, "error_message": "2021-08-27T15:50:58.412309 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4926.0, 'Column2'=7729.0", "custom_validation_name": null, "error_message": "2021-05-05T12:00:44.663010 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1463.0, 'Column2'=7394.0", "custom_validation_name": null, "error_message": "2022-10-22T15:09:57.193432 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2562.0, 'Column2'=9972.0", "custom_validation_name": null, "error_message": "2023-08-04T10:00:33.126206 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4019.0, 'Column2'=2225.0", "custom_validation_name": null, "error_message": "2020-06-06T02:43:23.501212 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4497.0, 'Column2'=783.0", "custom_validation_name": null, "error_message": "2023-10-28T18:01:11.028476 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7651.0, 'Column2'=3648.0", "custom_validation_name": null, "error_message": "2022-11-04T07:25:55.817272 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5117.0, 'Column2'=450.0", "custom_validation_name": null, "error_message": "2020-05-17T13:18:39.495529 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6991.0, 'Column2'=5477.0", "custom_validation_name": null, "error_message": "2021-06-13T10:32:59.971181 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2007.0, 'Column2'=9390.0", "custom_validation_name": null, "error_message": "2020-02-26T05:28:15.203769 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=60.0, 'Column2'=5857.0", "custom_validation_name": null, "error_message": "2021-06-04T07:20:40.737620 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1569.0, 'Column2'=5131.0", "custom_validation_name": null, "error_message": "2022-04-30T20:42:37.812099 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7099.0, 'Column2'=3546.0", "custom_validation_name": null, "error_message": "2022-05-25T13:58:23.135527 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6838.0, 'Column2'=6008.0", "custom_validation_name": null, "error_message": "2023-11-26T12:03:54.537112 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6437.0, 'Column2'=826.0", "custom_validation_name": null, "error_message": "2024-04-23T01:11:39.696862 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9106.0, 'Column2'=7692.0", "custom_validation_name": null, "error_message": "2021-06-24T12:49:41.964361 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1273.0, 'Column2'=9343.0", "custom_validation_name": null, "error_message": "2022-06-02T05:28:54.968239 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=452.0, 'Column2'=2348.0", "custom_validation_name": null, "error_message": "2021-07-12T04:01:16.423357 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7737.0, 'Column2'=4480.0", "custom_validation_name": null, "error_message": "2024-04-06T14:22:10.082745 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=2727.0", "custom_validation_name": null, "error_message": "2022-12-27T07:40:07.506321 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1566.0, 'Column2'=9368.0", "custom_validation_name": null, "error_message": "2020-10-10T01:30:35.674845 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4980.0, 'Column2'=5741.0", "custom_validation_name": null, "error_message": "2020-06-02T14:04:33.888319 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=269.0, 'Column2'=2496.0", "custom_validation_name": null, "error_message": "2021-06-16T13:42:37.193746 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7021.0, 'Column2'=4108.0", "custom_validation_name": null, "error_message": "2023-10-29T16:36:35.700299 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6616.0, 'Column2'=3294.0", "custom_validation_name": null, "error_message": "2020-11-28T11:40:15.138620 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7082.0, 'Column2'=5094.0", "custom_validation_name": null, "error_message": "2020-11-17T01:27:59.260323 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2025.0, 'Column2'=5518.0", "custom_validation_name": null, "error_message": "2024-01-19T05:14:12.668187 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1868.0, 'Column2'=9757.0", "custom_validation_name": null, "error_message": "2022-10-27T13:10:17.797493 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6100.0, 'Column2'=6568.0", "custom_validation_name": null, "error_message": "2024-07-08T11:39:01.809308 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5876.0, 'Column2'=7471.0", "custom_validation_name": null, "error_message": "2022-06-30T00:10:32.101972 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3080.0, 'Column2'=7952.0", "custom_validation_name": null, "error_message": "2023-01-29T18:25:17.793075 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1134.0, 'Column2'=3074.0", "custom_validation_name": null, "error_message": "2024-12-21T17:23:37.477971 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=9819.0", "custom_validation_name": null, "error_message": "2023-01-29T19:02:40.766303 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=350.0, 'Column2'=9020.0", "custom_validation_name": null, "error_message": "2022-08-13T16:35:53.869119 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2853.0, 'Column2'=1494.0", "custom_validation_name": null, "error_message": "2020-02-24T21:54:26.246800 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9690.0, 'Column2'=3529.0", "custom_validation_name": null, "error_message": "2023-01-18T14:54:23.207845 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=924.0, 'Column2'=8101.0", "custom_validation_name": null, "error_message": "2020-07-01T08:02:10.620425 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8246.0, 'Column2'=3081.0", "custom_validation_name": null, "error_message": "2021-09-02T10:29:11.061902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7238.0, 'Column2'=8031.0", "custom_validation_name": null, "error_message": "2024-11-11T20:39:29.221639 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=2946.0", "custom_validation_name": null, "error_message": "2024-03-24T09:51:52.082899 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6555.0, 'Column2'=3965.0", "custom_validation_name": null, "error_message": "2023-11-18T20:53:05.621935 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7100.0, 'Column2'=4436.0", "custom_validation_name": null, "error_message": "2021-07-29T10:39:53.443304 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1808.0, 'Column2'=5521.0", "custom_validation_name": null, "error_message": "2022-11-27T21:18:53.508144 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5916.0, 'Column2'=6318.0", "custom_validation_name": null, "error_message": "2023-08-27T09:14:24.812235 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4687.0, 'Column2'=3600.0", "custom_validation_name": null, "error_message": "2022-09-30T11:05:06.433463 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=2539.0", "custom_validation_name": null, "error_message": "2021-10-02T06:48:22.495285 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4511.0, 'Column2'=5752.0", "custom_validation_name": null, "error_message": "2024-04-20T16:57:31.943779 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6593.0, 'Column2'=9741.0", "custom_validation_name": null, "error_message": "2024-03-24T11:54:26.531508 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2521.0, 'Column2'=4682.0", "custom_validation_name": null, "error_message": "2020-11-09T19:42:35.577206 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7690.0, 'Column2'=9451.0", "custom_validation_name": null, "error_message": "2024-01-24T17:34:37.801847 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1661.0, 'Column2'=8725.0", "custom_validation_name": null, "error_message": "2022-10-18T05:32:58.684396 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=382.0, 'Column2'=611.0", "custom_validation_name": null, "error_message": "2021-01-29T19:40:33.479117 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7698.0, 'Column2'=126.0", "custom_validation_name": null, "error_message": "2022-08-24T18:40:13.574663 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4134.0, 'Column2'=2905.0", "custom_validation_name": null, "error_message": "2020-04-02T19:55:55.216137 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3625.0, 'Column2'=753.0", "custom_validation_name": null, "error_message": "2021-07-05T03:36:40.976136 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3204.0, 'Column2'=4364.0", "custom_validation_name": null, "error_message": "2022-07-21T05:10:12.503542 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4533.0, 'Column2'=8345.0", "custom_validation_name": null, "error_message": "2024-09-25T22:44:48.796764 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8296.0, 'Column2'=9233.0", "custom_validation_name": null, "error_message": "2020-03-05T16:32:40.852441 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5542.0, 'Column2'=5715.0", "custom_validation_name": null, "error_message": "2023-10-18T09:32:23.489753 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": null, "error_message": "2023-12-10T20:17:30.688296 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2141.0, 'Column2'=3631.0", "custom_validation_name": null, "error_message": "2023-02-01T05:21:25.243086 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8365.0, 'Column2'=5962.0", "custom_validation_name": null, "error_message": "2021-03-11T19:31:33.133644 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=3693.0", "custom_validation_name": null, "error_message": "2023-07-09T05:27:33.886542 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": null, "error_message": "2021-06-23T21:10:33.797920 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2009.0, 'Column2'=3526.0", "custom_validation_name": null, "error_message": "2020-08-25T23:58:14.689434 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1166.0, 'Column2'=7142.0", "custom_validation_name": null, "error_message": "2021-09-22T03:34:41.936657 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=8978.0", "custom_validation_name": null, "error_message": "2023-03-13T16:43:02.141687 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7121.0, 'Column2'=940.0", "custom_validation_name": null, "error_message": "2022-09-17T11:50:05.093931 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7854.0, 'Column2'=5679.0", "custom_validation_name": null, "error_message": "2023-08-03T03:46:17.117520 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5478.0, 'Column2'=3264.0", "custom_validation_name": null, "error_message": "2021-04-27T09:20:50.017659 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8499.0, 'Column2'=1781.0", "custom_validation_name": null, "error_message": "2024-12-19T15:32:16.459902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2219.0, 'Column2'=3425.0", "custom_validation_name": null, "error_message": "2021-12-12T03:05:33.636490 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7968.0, 'Column2'=2581.0", "custom_validation_name": null, "error_message": "2022-03-15T18:00:42.351450 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7409.0, 'Column2'=3591.0", "custom_validation_name": null, "error_message": "2022-06-27T00:17:56.846807 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1663.0, 'Column2'=5373.0", "custom_validation_name": null, "error_message": "2020-09-25T09:53:47.464502 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2291.0, 'Column2'=7646.0", "custom_validation_name": null, "error_message": "2024-07-31T16:47:31.769712 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6045.0, 'Column2'=1691.0", "custom_validation_name": null, "error_message": "2020-07-14T11:29:37.883313 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7054.0, 'Column2'=2804.0", "custom_validation_name": null, "error_message": "2020-04-02T07:53:18.028208 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7724.0, 'Column2'=5781.0", "custom_validation_name": null, "error_message": "2020-01-27T01:08:44.877221 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3581.0, 'Column2'=8894.0", "custom_validation_name": null, "error_message": "2024-05-06T17:06:16.883752 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1230.0, 'Column2'=7916.0", "custom_validation_name": null, "error_message": "2024-06-01T08:55:39.556947 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3844.0, 'Column2'=1013.0", "custom_validation_name": null, "error_message": "2020-02-12T09:39:20.720809 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5475.0, 'Column2'=9383.0", "custom_validation_name": null, "error_message": "2020-07-10T07:42:11.270281 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3435.0, 'Column2'=6495.0", "custom_validation_name": null, "error_message": "2021-01-24T17:03:02.160093 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6448.0, 'Column2'=3283.0", "custom_validation_name": null, "error_message": "2021-12-01T18:16:03.772331 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1594.0, 'Column2'=1216.0", "custom_validation_name": null, "error_message": "2022-10-16T02:01:45.243440 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6650.0, 'Column2'=7901.0", "custom_validation_name": null, "error_message": "2021-05-27T23:57:56.353643 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2782.0, 'Column2'=9796.0", "custom_validation_name": null, "error_message": "2022-09-24T00:04:17.915957 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=878.0, 'Column2'=2405.0", "custom_validation_name": null, "error_message": "2024-02-20T12:08:21.305734 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3764.0, 'Column2'=9917.0", "custom_validation_name": null, "error_message": "2020-06-18T21:25:28.170829 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}]}, {"column_name": "Column10", "total_validation_errors": 10000, "validation_severity": "high_severity", "general_validation_errors": {"datetime_format_error": 10000}, "custom_validation_errros": null, "validation_errors": [{"key_columns": "'Column1'=6475.0, 'Column2'=9463.0", "custom_validation_name": null, "error_message": "2022-12-15T08:38:49.322982 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=442.0, 'Column2'=60.0", "custom_validation_name": null, "error_message": "2022-12-22T21:16:55.190121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4674.0, 'Column2'=5552.0", "custom_validation_name": null, "error_message": "2024-04-08T15:54:18.916347 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9302.0, 'Column2'=1172.0", "custom_validation_name": null, "error_message": "2023-05-12T04:59:59.114433 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7734.0, 'Column2'=5684.0", "custom_validation_name": null, "error_message": "2023-01-03T02:11:21.638074 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4842.0, 'Column2'=9152.0", "custom_validation_name": null, "error_message": "2021-02-19T13:04:49.371856 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5386.0, 'Column2'=6172.0", "custom_validation_name": null, "error_message": "2020-02-12T05:36:05.933684 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1985.0, 'Column2'=2345.0", "custom_validation_name": null, "error_message": "2021-12-22T18:20:05.098660 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=930.0, 'Column2'=6434.0", "custom_validation_name": null, "error_message": "2020-10-22T14:21:08.786939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1424.0, 'Column2'=6747.0", "custom_validation_name": null, "error_message": "2021-11-18T17:08:48.456770 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6396.0, 'Column2'=2261.0", "custom_validation_name": null, "error_message": "2023-06-19T22:15:43.436526 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4926.0, 'Column2'=7729.0", "custom_validation_name": null, "error_message": "2022-11-13T01:09:48.806556 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1463.0, 'Column2'=7394.0", "custom_validation_name": null, "error_message": "2023-10-04T09:42:12.911893 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2562.0, 'Column2'=9972.0", "custom_validation_name": null, "error_message": "2022-08-17T22:57:53.973426 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4019.0, 'Column2'=2225.0", "custom_validation_name": null, "error_message": "2020-05-09T15:23:01.915999 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4497.0, 'Column2'=783.0", "custom_validation_name": null, "error_message": "2023-11-08T13:08:32.310594 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7651.0, 'Column2'=3648.0", "custom_validation_name": null, "error_message": "2020-01-31T01:29:20.875583 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5117.0, 'Column2'=450.0", "custom_validation_name": null, "error_message": "2020-01-06T23:28:43.895426 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6991.0, 'Column2'=5477.0", "custom_validation_name": null, "error_message": "2020-04-09T04:21:17.239096 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2007.0, 'Column2'=9390.0", "custom_validation_name": null, "error_message": "2021-02-24T06:28:19.453979 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=60.0, 'Column2'=5857.0", "custom_validation_name": null, "error_message": "2022-11-02T13:32:45.166606 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1569.0, 'Column2'=5131.0", "custom_validation_name": null, "error_message": "2021-02-10T02:28:39.174902 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7099.0, 'Column2'=3546.0", "custom_validation_name": null, "error_message": "2023-08-31T02:12:27.263022 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6838.0, 'Column2'=6008.0", "custom_validation_name": null, "error_message": "2022-07-10T21:54:50.458963 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6437.0, 'Column2'=826.0", "custom_validation_name": null, "error_message": "2022-06-22T21:30:53.878583 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9106.0, 'Column2'=7692.0", "custom_validation_name": null, "error_message": "2020-12-31T13:13:21.983182 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1273.0, 'Column2'=9343.0", "custom_validation_name": null, "error_message": "2024-05-07T07:26:51.719931 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=452.0, 'Column2'=2348.0", "custom_validation_name": null, "error_message": "2022-06-22T10:04:21.986729 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7737.0, 'Column2'=4480.0", "custom_validation_name": null, "error_message": "2021-05-01T05:23:30.510495 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1385.0, 'Column2'=2727.0", "custom_validation_name": null, "error_message": "2020-08-02T02:01:05.332920 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1566.0, 'Column2'=9368.0", "custom_validation_name": null, "error_message": "2024-10-18T01:30:33.577282 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4980.0, 'Column2'=5741.0", "custom_validation_name": null, "error_message": "2024-07-04T17:13:57.706008 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=269.0, 'Column2'=2496.0", "custom_validation_name": null, "error_message": "2020-04-19T13:21:06.749604 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7021.0, 'Column2'=4108.0", "custom_validation_name": null, "error_message": "2022-09-11T19:06:03.135922 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6616.0, 'Column2'=3294.0", "custom_validation_name": null, "error_message": "2023-10-13T22:46:46.748961 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7082.0, 'Column2'=5094.0", "custom_validation_name": null, "error_message": "2022-08-09T02:50:17.670284 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2025.0, 'Column2'=5518.0", "custom_validation_name": null, "error_message": "2023-10-20T21:44:39.759039 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1868.0, 'Column2'=9757.0", "custom_validation_name": null, "error_message": "2020-05-22T06:28:39.675081 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6100.0, 'Column2'=6568.0", "custom_validation_name": null, "error_message": "2022-05-08T02:06:38.002186 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5876.0, 'Column2'=7471.0", "custom_validation_name": null, "error_message": "2021-09-12T18:05:30.924341 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3080.0, 'Column2'=7952.0", "custom_validation_name": null, "error_message": "2020-06-24T02:51:13.759078 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1134.0, 'Column2'=3074.0", "custom_validation_name": null, "error_message": "2024-10-19T09:43:29.523091 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=9819.0", "custom_validation_name": null, "error_message": "2024-04-19T06:40:44.629241 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=350.0, 'Column2'=9020.0", "custom_validation_name": null, "error_message": "2022-01-31T09:08:08.105423 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2853.0, 'Column2'=1494.0", "custom_validation_name": null, "error_message": "2024-05-30T14:08:32.197022 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9690.0, 'Column2'=3529.0", "custom_validation_name": null, "error_message": "2023-09-03T01:14:48.811678 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=924.0, 'Column2'=8101.0", "custom_validation_name": null, "error_message": "2021-06-02T04:22:14.423733 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8246.0, 'Column2'=3081.0", "custom_validation_name": null, "error_message": "2021-11-27T10:58:51.409108 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7238.0, 'Column2'=8031.0", "custom_validation_name": null, "error_message": "2020-12-02T23:25:23.561857 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7947.0, 'Column2'=2946.0", "custom_validation_name": null, "error_message": "2021-03-08T10:28:31.278200 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6555.0, 'Column2'=3965.0", "custom_validation_name": null, "error_message": "2022-11-19T00:40:43.390153 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7100.0, 'Column2'=4436.0", "custom_validation_name": null, "error_message": "2023-11-18T14:06:12.709492 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1808.0, 'Column2'=5521.0", "custom_validation_name": null, "error_message": "2024-10-25T22:44:50.860866 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5916.0, 'Column2'=6318.0", "custom_validation_name": null, "error_message": "2024-03-19T09:02:50.554214 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4687.0, 'Column2'=3600.0", "custom_validation_name": null, "error_message": "2020-02-06T03:41:08.280729 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1953.0, 'Column2'=2539.0", "custom_validation_name": null, "error_message": "2020-05-19T20:16:25.040374 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4511.0, 'Column2'=5752.0", "custom_validation_name": null, "error_message": "2024-06-02T18:01:08.728363 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6593.0, 'Column2'=9741.0", "custom_validation_name": null, "error_message": "2023-10-15T09:49:57.158881 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2521.0, 'Column2'=4682.0", "custom_validation_name": null, "error_message": "2023-04-07T22:13:42.609957 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7690.0, 'Column2'=9451.0", "custom_validation_name": null, "error_message": "2024-06-18T21:03:26.622015 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1661.0, 'Column2'=8725.0", "custom_validation_name": null, "error_message": "2021-07-26T05:50:02.962948 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=382.0, 'Column2'=611.0", "custom_validation_name": null, "error_message": "2021-05-03T13:50:36.608121 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7698.0, 'Column2'=126.0", "custom_validation_name": null, "error_message": "2023-02-25T03:31:09.726174 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4134.0, 'Column2'=2905.0", "custom_validation_name": null, "error_message": "2022-10-30T14:30:38.900473 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3625.0, 'Column2'=753.0", "custom_validation_name": null, "error_message": "2023-01-09T10:15:31.390483 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3204.0, 'Column2'=4364.0", "custom_validation_name": null, "error_message": "2023-06-05T08:26:23.860199 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=4533.0, 'Column2'=8345.0", "custom_validation_name": null, "error_message": "2024-08-01T05:36:18.356111 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8296.0, 'Column2'=9233.0", "custom_validation_name": null, "error_message": "2024-12-17T21:05:44.806689 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5542.0, 'Column2'=5715.0", "custom_validation_name": null, "error_message": "2021-05-11T17:50:37.839088 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3653.0, 'Column2'=7700.0", "custom_validation_name": null, "error_message": "2024-04-12T00:20:59.981113 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2141.0, 'Column2'=3631.0", "custom_validation_name": null, "error_message": "2022-05-20T11:10:40.204939 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8365.0, 'Column2'=5962.0", "custom_validation_name": null, "error_message": "2020-07-22T23:27:25.790968 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=3693.0", "custom_validation_name": null, "error_message": "2022-04-30T20:25:53.462315 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3561.0, 'Column2'=9004.0", "custom_validation_name": null, "error_message": "2021-07-02T18:12:11.369046 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2009.0, 'Column2'=3526.0", "custom_validation_name": null, "error_message": "2020-08-02T23:21:13.531829 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1166.0, 'Column2'=7142.0", "custom_validation_name": null, "error_message": "2022-02-04T17:15:14.098010 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=9738.0, 'Column2'=8978.0", "custom_validation_name": null, "error_message": "2022-05-15T04:01:38.133667 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7121.0, 'Column2'=940.0", "custom_validation_name": null, "error_message": "2021-05-12T10:41:05.790807 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7854.0, 'Column2'=5679.0", "custom_validation_name": null, "error_message": "2020-11-09T10:30:12.708211 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5478.0, 'Column2'=3264.0", "custom_validation_name": null, "error_message": "2024-06-08T11:11:57.315521 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=8499.0, 'Column2'=1781.0", "custom_validation_name": null, "error_message": "2020-01-16T06:15:39.093263 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2219.0, 'Column2'=3425.0", "custom_validation_name": null, "error_message": "2023-10-05T05:45:06.951248 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7968.0, 'Column2'=2581.0", "custom_validation_name": null, "error_message": "2024-07-19T07:10:33.375646 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7409.0, 'Column2'=3591.0", "custom_validation_name": null, "error_message": "2021-09-07T02:29:52.788506 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1663.0, 'Column2'=5373.0", "custom_validation_name": null, "error_message": "2021-11-05T14:50:25.342896 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2291.0, 'Column2'=7646.0", "custom_validation_name": null, "error_message": "2023-03-08T05:16:52.746470 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6045.0, 'Column2'=1691.0", "custom_validation_name": null, "error_message": "2021-05-01T21:05:18.971381 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7054.0, 'Column2'=2804.0", "custom_validation_name": null, "error_message": "2023-12-24T08:10:35.593937 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=7724.0, 'Column2'=5781.0", "custom_validation_name": null, "error_message": "2024-03-21T11:34:19.880888 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3581.0, 'Column2'=8894.0", "custom_validation_name": null, "error_message": "2024-02-09T19:18:26.423769 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1230.0, 'Column2'=7916.0", "custom_validation_name": null, "error_message": "2024-06-27T03:25:07.223783 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3844.0, 'Column2'=1013.0", "custom_validation_name": null, "error_message": "2021-01-26T16:36:31.728071 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=5475.0, 'Column2'=9383.0", "custom_validation_name": null, "error_message": "2024-02-16T05:37:31.096911 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3435.0, 'Column2'=6495.0", "custom_validation_name": null, "error_message": "2023-04-08T05:18:51.150297 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6448.0, 'Column2'=3283.0", "custom_validation_name": null, "error_message": "2020-06-24T21:29:16.753700 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=1594.0, 'Column2'=1216.0", "custom_validation_name": null, "error_message": "2020-03-04T18:59:35.441438 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=6650.0, 'Column2'=7901.0", "custom_validation_name": null, "error_message": "2022-10-07T04:27:04.778222 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=2782.0, 'Column2'=9796.0", "custom_validation_name": null, "error_message": "2022-03-23T01:33:19.517870 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=878.0, 'Column2'=2405.0", "custom_validation_name": null, "error_message": "2020-05-10T07:50:49.027321 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}, {"key_columns": "'Column1'=3764.0, 'Column2'=9917.0", "custom_validation_name": null, "error_message": "2023-11-01T14:28:11.049428 Does not match the supplied datetime format yyyy-mm-dd HH:MM:SS", "file_names": "'random_data_R3_*************.csv'"}]}], "cross_field_validation_results": [], "base_resource_validation_result": null}, "validation_parameters": {"resource_id": 2195, "resource_name": "JITENDRA_R3", "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_names": ["random_data_R3_*************.csv"], "file_processing_attributes": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "inline_variables": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "resource_column_details_version": 3, "resource_version": 11, "domain_version": 5, "filter_rules": []}, "prev_execution_id": null, "is_rerun": null, "complete_validation_params": {"validation_parameters": {"resource_data": {"resource_id": 2195, "resource_prefix": null, "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": 28, "sql_query": null, "container_name": null, "resource_path": "D:\\Uploads", "file_name": "random_data_R3_*************.csv", "column_delimiter": ",", "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": [], "api_request": {"url": null, "method": "get", "content_type": "application/json", "body": null, "query_params": null, "url_params": null, "user_name": null, "password": null, "bearer_token": null, "api_key": null, "oauth_client_id": null, "oauth_client_secret": null, "oauth_url": null, "request_timeout": 0}, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": {"resource_id": 2207, "resource_code": "JITENDRA_R4", "merge_on": ["Column1", "Column2"]}, "pre_processing_request": null}, "sampling_validation": null, "sample_size": null, "output_storage_params": null, "run_instance": null, "severity_level": null, "severity_column_names": null, "validation_execution_report_name": null, "inline_variables": null, "pull_latest_files": null, "query_params": null, "latest_execution_id": null, "is_rerun": false}, "no_of_errors_in_response": 100, "no_of_sample_validation_errors": 1000000, "no_of_errors_in_output_files": 1000, "no_of_errors_in_filter_record_output_files": 1000, "summary_mode": false, "generate_files": true, "save_input_data_file": false, "skip_duplicate_records": true, "keep_downloaded_files": false, "file_names": [], "resource_id": 2195, "resource_column_details_version": 3, "resource_version": 11, "domain_version": 5, "store_errors_snapshots_and_create_issues": true, "inline_variables": {}}}}], "secondary_merge_resource_validation_exec_details": []}, "complete_execution_params": null, "domain_id": 1319, "comparison_research_query_results": null, "domain_name": "Performance_test", "run_id": 1, "name": "JITENDRA_R2_SECONDARY_MERGE_RESOURCES", "run_name": "Legacy"}