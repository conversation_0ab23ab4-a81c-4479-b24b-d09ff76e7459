#root {
  height: 100vh;
  overflow: hidden;
}
.container {
  height: 100vh;
  background-color: #282D93;
}

.site-layout {
}

.btn-box {
  box-sizing: inherit;
}

.profile-img {
  height: 40px;
  width: 40px;
  border-radius: 50%;
}

.header-profile-img {
  width: 100%;
  height: 100%;
  text-align: center;
  object-fit: cover;
  color: transparent;
  text-indent: 10000px;
}

.content {
  padding: 10px 16px;
  margin: 0;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 98px);
  overflow: auto;
}
.content > div {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* max-height: 97%; */
  /* overflow: hidden; */
}
.content > div:not(.ant-row) {
  min-height: 0;
}

.div-container {
  /* min-height: 360px; */
  background: rgb(255, 255, 255);
  display: flex;
  flex-direction: column;
}

.header-row {
  padding: 10px 0 0;
  margin: 0 0 10px 0;
  min-height: 75px;
  max-height: 75px !important;
  flex-direction: row !important;
  align-items: center;
  align-items: flex-end;
}
.header-row .ant-select {
  width: 100%;
}

.btn-ico-with-txt{
    margin-right: 5px; position: relative; top: 2px
}
.ant-btn-primary {
  min-height: 40px;
  align-self: flex-start;
}
.ant-btn-primary:not(:last-child) {
  margin-right: 5px;
}
.section-heading {
  background-color: #282D93;
  padding: 10px 15px;
  color: #fff;
  font-weight: 600;
  margin: 0;
  width: 100%;
  max-height: 45px;
}

.ant-table {
  /* max-height: 50vh; */
  /* overflow: auto; */
}

.top-form {
  margin-top: 10px;
}
.top-form .ant-form-item {
  margin-bottom: 5px;
}
.top-form .ant-form-item-row .ant-col,
.top-form .ant-upload {
  min-width: 100%;
  padding-right: 5px;
}
.top-form .upload-section span {
  line-height: 22px;
  padding-bottom: 8px;
}
.top-form .ant-upload button {
  min-width: 100%;
  text-align: left;
}
.app-footer {
  background: #002140;
  text-align: center;
  color: #fff;
  height: 48px;
  min-height: 48px;
}

.ant-table-wrapper {
  flex: 1;
  min-height: unset !important;
  /* overflow: hidden; */
  background: #fff;
}
.ant-table-wrapper .ant-spin-nested-loading,
.ant-table-wrapper .ant-spin-container {
  height: 100%;
}
.ant-table-wrapper .ant-table {
  max-height: calc(100% - 50px);
  overflow: auto;
}
header.app-header {
  height: 50px;
  background: #fff;
  padding: 0 10px;
}
.top-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 50px;
  max-height: 50px;
}
.top-header .btn-box {
  height: 40px;
}
.top-header .btn-box .ant-image {
  height: 40px;
  width: 40px;
  display: flex;
  border-radius: 50%;
  overflow: hidden;
}
.top-header button.profile-btn {
  background: transparent;
  border:none;
  padding: 0;
  margin: 0 10px;
  border-radius: 8px;
}

/* Modal CSS override */
.ant-modal-content {
  padding: 0 !important;
}
.ant-modal-header {
  background-color: #282D93 !important;
  padding: 10px 15px;
  color: #fff !important;
  font-weight: 600;
  margin: 0;
  width: 100%;
  max-height: 45px;
}
.ant-modal-header .ant-modal-title {
  color: #fff !important;
}
.ant-modal-content .ant-modal-close {
  color: #fff;
}
.ant-modal-body {
  padding: 10px;
}
.ant-modal-footer {
  padding: 10px;
}
.left-operand-row, 
.right-operand-row {
  background-color: #f7f7f7;
  padding: 10px;
  margin: 8px 0px;
  border-radius: 8px;
}
.left-operand-row h3, 
.right-operand-row h3 {
  font-size: 0.75rem;
}
.left-operand-row table, 
.right-operand-row table {
  width: 100%;
}
.left-operand-row table th,
.left-operand-row table td, 
.right-operand-row table th,
.right-operand-row table td {
  text-align: left;
  /* width: 33%; */
}

.ant-card-head{
  display: flex;
justify-content: center;
flex-direction: column;
min-height: 56px;
margin-bottom: -1px;
padding: 0 24px;
color: rgb(255, 255, 255, 0.88);
font-weight: 600;
font-size: 16px;
background: transparent;
  background-color: #282D93;
border-bottom: 1px solid #f0f0f0;
border-radius: 8px 8px 0 0;
}