:root {
  --white: #ffffff;
  --black: #1e1e1e;
  --grey: #555b69;
  --light-grey: #979797; // 9E9E9E
  --dark-grey: #142a41;

  --orange: #f48400;
  --sky-blue: #1985e5;
  --dark-blue: #196bb4;
  --yellow: #ffa500;

  --input-box-border: #cfcfcf;
  --table-head: #232323;
  --card-bg: #f8f8f8;

  --chip-bg: rgba(64, 114, 238, 0.1);
  --chip-close-bg: #f93155;
  --failure-bg: #ffdcdc;
  --success-bg: #cdfed2;
  --success-bg-1: rgb(116, 184, 86);
  --badge-danger-bg: #e84336;
  --badge-success-bg: var(--success-bg-1);
  --badge-blue-border: #0967b1;
  --badge-blue-border-20: color-mix(
    in srgb,
    var(--badge-blue-border) 20%,
    transparent
  );
  --blue-1: #224761;
  --gray-1: #b3b3b3;
  --input-focus-shadow: 3px 3px 3px 0px rgba(0, 0, 0, 0.2);

  --table-header-bg: #ededed;

  --table-border: var(--input-box-border);
  --card-border: var(--input-box-border);
  --orange-20: color-mix(in srgb, var(--orange) 20%, transparent);
  --orange-60: color-mix(in srgb, var(--orange) 60%, transparent);
  --input-box-border20: color-mix(in srgb, var(--gray-1) 20%, transparent);
  --input-box-border67: color-mix(in srgb, var(--gray-1) 67%, transparent);
  --dark-blue50: color-mix(in srgb, var(--dark-blue) 50%, transparent);
  --dark-blue10: color-mix(in srgb, var(--dark-blue) 10%, transparent);
  --table-row-alt-bg: color-mix(
    in srgb,
    var(--badge-blue-border) 5%,
    transparent
  );
  --z-index1200: 1200;
}
