%sql-editor {
  .sql-editor {
    margin: 10px auto;
    background-color: var(--white);
    border: solid 1px var(--table-border);
    border-radius: 4px;
    min-height: 30px;
    overflow: hidden;

    &.mb-0 {
      margin-bottom: 0px;
    }

    &.ace_focus {
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    }

    .ace_gutter {
      padding-top: 8px;
      background: var(--card-bg);
      color: var(--light-grey);

      .ace_gutter-active-line {
        background: transparent;
      }
    }

    .ace_scroller {
      margin-top: 8px;
    }

    .ace_content {
      padding-top: 0px;
    }

    .ace_active-line {
      background: transparent;
    }

    .ace_scroller {
      background: var(--white);
    }

    .ace_hidden-cursors {
      .ace_cursor {
        opacity: 0;
      }
    }

    .ace_placeholder {
      opacity: 1;
      color: var(--light-grey);
    }
  }
}

%available-group {
  .avail-columns-group {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 5px;
    flex-wrap: wrap;
    max-height: 100px;
    overflow-y: auto;
    margin-bottom: 10px;

    .avail-columns {
      border: solid 1px var(--dark-blue);
      background: transparent;
      font-size: 14px;
      color: var(--dark-blue);
      font-weight: 500;
      padding: 6px;
      line-height: 1.1;
      border-radius: 4px;
      font-family: "Inter", Arial, Helvetica, sans-serif;
      cursor: pointer;
    }
  }
}
