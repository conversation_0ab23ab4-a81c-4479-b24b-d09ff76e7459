.sidebar-drawer-closed {
  .logout-box {
    .jira-feedback {
      column-gap: 0px;
      font-size: 0px;
      color: transparent;
    }
  }
  .MuiDrawer-paper {
    position: relative;
  }

  .MuiDrawer-docked {
    overflow: visible;
    height: 100%;
  }

  .list-name {
    max-height: inherit;
    overflow: visible;
    padding-bottom: 111px !important;

    > li {
      &:after {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        content: "";
        box-shadow: -3px 0px 5px rgba(0, 0, 0, 0.15);
        width: 0px;
        background: var(--white);
        z-index: 0;
        transition: all 0.3s ease-in;
      }

      &:hover {
        .list-item-name {
          width: 210px;
          left: 81px;
        }

        &:after {
          width: calc(100%);
        }
      }

      &.active {
        .list-item-name {
          background: var(--dark-blue);
          color: var(--white);
        }

        &:after {
          background: var(--dark-blue);
        }
      }

      .list-item-name {
        position: absolute;
        top: 0;
        left: 0;
        align-items: center;
        background: var(--white);
        width: 0px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;

        overflow: hidden;
        font-size: 15px;
        color: var(--dark-blue);
        padding: 0;
        box-shadow: 4px 1px 5px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease-in;
        z-index: -1;
      }

      .MuiListItemIcon-root {
        margin-left: 10px;
      }
    }
  }
}

.list-name {
  max-height: calc(100vh - 240px);
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  list-style: none;
  margin: 0px;
  padding: 8px 0px;
  padding-top: 32px;
  position: relative;
  height: 100%;

  &:before {
    width: 100%;
    content: "";
    height: calc(100% - 33px);
    background: #f8f8f8;
    display: block;
    position: absolute;
  }

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--dark-blue50);
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--dark-blue);
    border-radius: 10px;
  }

  > li {
    position: relative;
    min-height: 46px;
    display: flex;
    align-items: flex-start;
    padding: 0px 0px 0px 0px;
    background: transparent;
    cursor: pointer;
    &.active {
      .without-link {
        color: var(--white);
      }
    }
    .without-link {
      cursor: default;
    }
    a,
    .without-link {
      color: var(--black);
      font-size: 15px;
      font-weight: 400;
      text-decoration: none;
      padding: 16px 10px 17px 55px;
      display: flex;
      align-items: center;
    }

    &:hover {
      // background-color: var(--white);

      &:before {
        background: var(--dark-blue);
      }

      .MuiListItemIcon-root {
        color: var(--dark-blue);
      }

      a {
        color: var(--dark-blue);
      }
    }

    &.active {
      background-color: var(--dark-blue);

      a {
        font-weight: 500;
        color: var(--white);
      }

      .MuiListItemIcon-root {
        color: var(--white);
      }

      &:hover {
        background-color: var(--dark-blue);

        .MuiListItemIcon-root {
          color: var(--white);
        }

        a {
          color: var(--white);
        }
      }
    }

    .MuiSvgIcon-root {
      display: none;
    }

    .MuiListItemText-root {
      margin: 0px;
    }

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 8px;
      height: 100%;
      background: transparent;
      opacity: 1;
      transition: all 0.3s ease-in;
      z-index: 1;
    }

    &:not(.icon-stroke) {
      svg {
        path {
          fill: var(--black);
        }
      }

      &:hover {
        svg {
          path {
            fill: var(--badge-blue-border);
          }
        }
      }

      &.active {
        svg {
          path {
            fill: var(--white);
          }
        }
      }
    }

    &.icon-stroke {
      svg {
        path {
          stroke: var(--black);
        }
      }

      &:hover {
        svg {
          path {
            stroke: var(--badge-blue-border);
          }
        }
      }

      &.active {
        svg {
          path {
            stroke: var(--white);
          }
        }
      }
    }

    svg {
      min-width: 24px;
      max-width: 24px;
    }
    &.menu-1 {
      .MuiListItemIcon-root {
        padding-top: 12px;
      }
    }
    &.menu-2 {
      .MuiListItemIcon-root {
        padding-top: 12px;
      }
    }
    &.menu-3 {
      .MuiListItemIcon-root {
        padding-top: 8px;
      }
    }
    &.menu-4 {
      .MuiListItemIcon-root {
        padding-top: 11px;
      }
    }
    &.menu-5 {
      .MuiListItemIcon-root {
        padding-top: 12px;
      }
    }

    .MuiListItemIcon-root {
      min-width: 27px;
      margin-right: 0px;
      padding-top: 8px;
      padding-left: 18px;
      z-index: 1;
    }
  }
}
.logo-with-searchbar {
  display: flex;
  align-items: center;
  column-gap: 24px;
}

.sidebarLogoBox {
  margin: 1.5rem 0rem 2rem 0rem;

  .sidebar-drawer-closed & {
    margin: 1.5rem 0rem 2rem 0rem;
  }
}

.sidebar-drawer-closed {
  .navbar-icon {
    transform: rotate(-180deg);
  }

  .menu-in-small-screen {
    margin-left: -68px;

    .logout-box {
      display: none;
    }
  }
}

.sidebar-drawer-closed {
  .MuiDrawer-paper {
    position: relative;
  }
}

.sidebar-drawer-closed {
  .logout-box {
    .jira-feedback {
      font-size: 0px;
      color: transparent;
      column-gap: 0px;
    }
  }
}

.logout-box {
  position: absolute;
  bottom: 0;
  right: 0%;
  width: 100%;
  text-align: center;
  .jira-feedback {
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 8px;
    color: var(--black);
    line-height: 17px;
    margin-bottom: 8px;
    font-size: 15px;
    img {
      width: 24px;
      height: 24px;
    }
  }

  .sidebar-drawer-closed & {
    .btn-logout {
      span {
        display: none;
      }
    }
  }

  .version {
    font-size: 14px;
    line-height: 17px;
    color: var(--black);
    font-weight: 500;
    margin-bottom: 18px;
  }

  .btn-logout {
    padding: 5px 0;
    border: none;
    background: var(--dark-blue);
    color: white;
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 18px;
    width: 100%;
    cursor: pointer;
  }
}

.sidebar-drawer {
  width: 245px;
  white-space: nowrap;
  box-sizing: border-box;
  transition: width 225ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
  height: 100%;

  .MuiDrawer-paper {
    border: none !important;
    overflow: visible !important;
    transition: width 225ms cubic-bezier(0.4, 0, 0.6, 1) 0ms;
    background: var(--card-bg);
    box-shadow: none;
    display: flex;
    flex-direction: column;
    height: 100%;
    z-index: 1200;
    // position: fixed;
    top: 0px;
    outline: 0px;
    left: 0px;
  }
}

.navbar-icon {
  background-color: var(--dark-blue) !important;
  width: 32px;
  height: 32px;
  position: absolute !important;
  top: 8px;
  right: -16px;
  z-index: 1201;
  border-radius: 50% !important;
  transform: rotate(0deg);
  transition: all 0.2s ease-in !important;
  position: absolute;

  &:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    background: url(../assets/svgs/arrow-left-white.svg) 50% 50% no-repeat;
    transform: translate(-50%, -50%);
    width: 7px;
    height: 12px;
  }
}

.sidebar-sep-bar {
  height: 2px;
  margin-top: 50px;
  background-color: rgba(184, 197, 211, 0.3);
  width: 85px;
  margin-left: auto;
  margin-right: auto;
}

.menu-in-small-screen-parent {
  background: var(--white);
  position: fixed;
  top: 0;
  left: 0px;
  height: 100%;
  z-index: 1200;
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

.sidebar-drawer-closed {
  .sub-menu {
    padding-bottom: 10px;
  }
}

.sub-menu {
  padding-left: 0;
  list-style: none;
  margin-top: -10px;

  li {
    margin-top: 0px;
    position: relative;
    padding-left: 73px;
    &:before {
      content: "";
      position: absolute;
      top: 50%;
      left: 53px;
      width: 10px;
      height: 2px;
      transform: translateY(-50%);
      background: var(--white);
    }

    a {
      display: block;
      color: #ccc;
      font-size: 14px;
      padding: 8px 0;
      text-decoration: none;

      &:hover,
      &.active {
        color: white;
      }
    }
  }
}

.list-item-name {
  width: 100%;
}

.list-name {
  & > li {
    .sub-menu {
      li {
        &:before {
          background: var(--light-grey);
        }
        &:hover {
          &:before {
            background: var(--dark-blue);
          }
        }
        a {
          color: var(--light-grey);
          &:hover {
            color: var(--dark-blue);
          }
        }
        &.active-child,
        &:hover {
          &:before {
            background-color: var(--auto-black) !important;
          }
        }
      }
    }
    &.active {
      .sub-menu {
        li {
          &:before {
            background: var(--white);
            z-index: 1;
          }
          a {
            color: var(--white);
            z-index: 1;
            position: relative;
          }
          &:hover {
            &:before {
              background: var(--gray-1);
            }
            a {
              color: var(--gray-1);
            }
          }
        }
      }
    }
  }
}

.list-name {
  & > li {
    .MuiListItemIcon-root {
      position: absolute;
    }
  }
}
.sidebar-drawer-closed {
  .list-name {
    & > li {
      .MuiListItemIcon-root {
        position: static;
      }
      a,
      .without-link {
        padding-left: 10px;
      }
      .sub-menu {
        li {
          padding-left: 18px;
          &:before {
            left: 8px;
          }
        }
      }
    }
  }
}
