@import "variables";
@import "commons";

.accordion-panel {
  &.research-accordion {
    .min-header {
      min-height: 67px;
    }

    .text-orange {
      color: var(--orange);
    }

    .expand-icon {
      display: flex;
      align-items: center;
      margin-right: 16px;
      font-weight: 400;

      svg {
        color: var(--light-grey);
        font-size: 22px;
      }
    }
    .rs-query-column-accordion {
      .MuiPaper-elevation {
        border: solid 1px var(--input-box-border) !important;
      }
    }

    .MuiPaper-elevation {
      border: none !important;

      & + .MuiPaper-elevation {
        border-top: solid 1px var(--input-box-border) !important;
        margin-top: 0px !important;
      }
    }

    .d-flex {
      display: flex;
    }

    .accordion-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .header-icons {
        display: flex;
        align-items: center;
      }
    }

    .MuiAccordionSummary-root {
      .expand-icon {
        svg {
          transition: all 0.1s ease-in;
        }
      }

      &.Mui-expanded {
        .expand-icon {
          svg {
            transform: rotate(180deg);
          }
        }
      }
    }

    .columns-datagrid {
      border: solid 1px var(--input-box-border);

      .MuiDataGrid-root {
        border-radius: 0px;
        border: none;
      }

      .MuiDataGrid-row {
        min-height: 40px;
        max-height: 40px;

        &:nth-child(even) {
          background: var(--card-bg);
        }

        .MuiDataGrid-cellContent {
          font-size: 13px;
        }
      }

      .MuiDataGrid-columnHeaders {
        display: none;
      }

      .header {
        background: var(--card-bg);
        padding: 8px 12px;
        font-size: 15px;
        font-weight: 500;
        border-bottom: solid 1px var(--input-box-border);
      }

      .sub-header {
        background: var(--card-bg);
        padding: 18px 24px;
        font-size: 15px;
        font-weight: 500;
        border-bottom: solid 1px var(--input-box-border);
        width: 100%;
      }

      .searchbox {
        padding: 7px 16px 7px 0px;
        border-bottom: solid 1px var(--input-box-border);

        .MuiFormControl-root {
          width: 100%;

          .MuiInputBase-root {
            display: flex;
            padding-right: 0px;

            .MuiInputBase-input {
              flex: 1 1 auto;
              padding: 8.5px 12px 8.5px 12px;
            }

            .MuiButtonBase-root {
              position: relative;

              &:before {
                content: "";
                width: 1px;
                height: 24px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                background: var(--input-box-border);
                left: -4px;
              }
            }
          }
        }

        fieldset {
          display: none;
        }
      }
    }

    .querydata-container {
      padding: 12px 12px;
      flex-direction: row;
      justify-content: space-between;
      height: 100%;

      textarea.form-control {
        border: solid 1px var(--input-box-border);
        min-height: 118px;
        border-radius: 4px;
        padding: 8px;
        font-family: "Inter", Arial, Helvetica, sans-serif;
        font-size: 15px;
        font-weight: 400;
        color: var(--dark-grey);

        &:focus {
          box-shadow: none;
          outline: none;
        }
      }

      @extend %sql-editor;
    }
  }
}

.resource-column-chips {
  padding: 0px 12px 12px;

  .accordion-panel {
    margin-bottom: 0px;

    .MuiPaper-elevation {
      border-radius: 0px !important;
      border: solid 1px var(--table-border) !important;
      border-bottom: none !important;

      &.mt-16 {
        margin-top: 0px !important;
      }
    }

    &:first-child {
      .MuiPaper-elevation {
        border-top-left-radius: 4px !important;
        border-top-right-radius: 4px !important;
      }
    }

    &:last-child {
      .MuiPaper-elevation {
        border-bottom-left-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
      }
    }
  }
}
.query-sub-type {
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
  padding: 15px;
  border-radius: 4px;
  position: relative;
  margin-bottom: 12px;
  & + .query-sub-type {
    margin-top: 12px;
  }
}
.rs-query-tabs {
  .MuiTabScrollButton-horizontal {
    padding-left: 0px;
    padding-right: 0px;
    width: auto;
  }
  .Mui-disabled {
    svg {
      width: 0px;
    }
  }
}
