:root {
  --auto-green: #094109;
  --auto-green-dark: #022802;
  --auto-black: #222222;
  --auto-black1: rgba(240, 240, 240, 0.04);
  // --auto-btn-green: #81c476;
  --auto-btn-green: var(--auto-green);
  // --auto-btn-green-border: #77b56c;
  --auto-btn-green-border: var(--auto-green);
  // --auto-btn-yellow: #f5ba5e;
  --auto-btn-yellow: var(--auto-green);
  // --auto-btn-yellow-border: #e1ac57;
  --auto-btn-yellow-border: var(--auto-green);
  --auto-text-color: #3d3d50;
  --chip-bg: var(--auto-btn-green-60);
  --auto-sky-blue: #88bfe5;

  --auto-btn-green-60: color-mix(
    in srgb,
    var(--auto-btn-green) 60%,
    transparent
  );
  --auto-btn-green-border-60: color-mix(
    in srgb,
    var(--auto-btn-green-border) 30%,
    transparent
  );
  --auto-btn-yellow-50: color-mix(
    in srgb,
    var(--auto-btn-yellow) 50%,
    transparent
  );
}

.sidebar-drawer {
  .MuiDrawer-paper {
    background: var(--auto-green);
  }
}
.sidebar-drawer-closed {
  .sidebarLogoBox {
    margin: 0 0rem 2rem 0rem;
    padding: 7px 0rem 7px 0rem;
    img {
      max-height: 50px;
    }
  }
  .list-name {
    & > li {
      .list-item-name {
        background: var(--auto-green-dark);
      }
      &:after {
        background: var(--auto-green-dark);
      }
      &.active {
        .list-item-name {
          background: var(--auto-green-dark);
        }
        &:after {
          background: var(--auto-green-dark);
        }
      }
    }
  }
}

.sidebarLogoBox {
  background: var(--auto-black);
  margin: 0 0rem 0rem 0rem;
  padding: 7px 0rem 7px 0rem;
}
.logo-text {
  color: var(--white);
}
.main-header {
  background: var(--auto-black) !important;

  .search-panel {
    .search-icon {
      color: var(--white);
    }
    .search-textbox {
      input {
        &::placeholder {
          color: var(--white);
        }
      }
    }
  }
  .user-dropdown {
    border-radius: 24px;
    .dropdown-arrow {
      display: block;
    }
    &:hover {
      background: var(--auto-black1);
    }
  }
  .username {
    color: var(--white);
    text-transform: uppercase;
    height: 35px;
    width: 35px;
    border-radius: 50%;
    padding: 10px 0px;
    line-height: 15px;
    background-color: var(--auto-green);
    text-align: center;
    font-size: 14px;
  }
  .icon-notification {
    border-color: var(--white);
    --grey: #fff;
  }
}

.list-name {
  &::-webkit-scrollbar-thumb {
    background: var(--auto-green-dark);
  }
  &:before {
    background: var(--auto-green);
  }
  & > li {
    &.icon-stroke {
      &:hover {
        svg {
          path {
            stroke: var(--white);
          }
        }
      }
      svg {
        path {
          stroke: var(--white);
        }
      }
    }
    .sub-menu {
      li {
        &:before {
          background: var(--white);
        }
        &:hover {
          &:before {
            background: var(--white);
          }
        }
        a {
          color: var(--white);
          &:hover {
            color: var(--white);
          }
        }
      }
    }
    a,
    .without-link {
      color: var(--white);
    }
    &:hover {
      a {
        color: var(--white);
      }
      &:before {
        background-color: transparent;
      }
    }

    &.active {
      background-color: var(--auto-green-dark);
      .sub-menu {
        li {
          &:hover {
            a {
              color: var(--white);
            }
            &:before {
              background: var(--white);
            }
          }
        }
      }
      &:hover {
        background-color: var(--auto-green-dark);
      }
    }

    &:not(.icon-stroke) {
      svg {
        path {
          fill: var(--white);
        }
      }
      &:hover {
        svg {
          path {
            fill: var(--white);
          }
        }
      }
    }
  }
}

.logout-box {
  .btn-logout {
    background-color: var(--auto-green-dark);
  }
  .version {
    color: var(--white);
  }
  .jira-feedback {
    color: var(--white);
  }
}
.navbar-icon {
  background-color: var(--auto-sky-blue) !important;
}

.large-logo {
  width: 200px;
  padding-top: 10px;
  padding-bottom: 10px;
}
.small-logo {
  padding-top: 8px;
  padding-bottom: 7px;
  width: 40px;
}
.header-dropdown-menu {
  ul {
    min-width: 240px;
    padding-top: 0px;
    padding-bottom: 0px;
    li {
      padding-top: 9px;
      padding-bottom: 9px;
      &:hover {
        background: transparent;
      }
    }
    .user-details {
      pointer-events: none;
      border-bottom: solid 1px var(--input-box-border20);
      padding-bottom: 12px;
      padding-top: 12px;

      &:hover {
        background: transparent;
      }
    }
    .btn-logout {
      border: solid 1px var(--grey);
      width: 80%;
      margin: 0 auto;
      color: var(--auto-black);
      padding-top: 4px;
      padding-bottom: 4px;
      display: flex;
      align-items: center;
      column-gap: 8px;
    }
  }
}
.btn-orange {
  background-color: var(--auto-btn-green) !important;
  border: solid 1px var(--auto-btn-green-border) !important;
  //color: var(--auto-text-color) !important;
  font-size: 13px !important;
  &:disabled {
    background: var(--auto-btn-green-60) !important;
    border-color: var(--auto-btn-green-border-60) !important;
  }
  &.btn-border {
    border-color: var(--auto-btn-yellow-border) !important;
    color: var(--auto-btn-yellow-border) !important;
    svg {
      color: var(--auto-btn-yellow-border);
      --dark-blue: var(--auto-btn-yellow-border);
    }
  }
  &.btn-export {
    font-size: 0px !important;
    .MuiButton-startIcon {
      margin-left: 0px;
    }
  }
  &.btn-blue {
    background-color: var(--auto-btn-green) !important;
    border: solid 1px var(--auto-btn-green-border) !important;
    &:disabled {
      background-color: var(--auto-btn-green-60) !important;
    }
  }
}

.main-dailog {
  .dailog-body {
    .sql-textarea-box {
      .available-box {
        .btn-dark {
          background-color: var(--auto-btn-green) !important;
          border: solid 1px var(--auto-btn-green-border) !important;
        }
      }
    }
  }
}

.fileUploadButton {
  .MuiButtonBase-root {
    background: var(--auto-btn-green-border);
    color: var(--auto-text-color) !important;

    &:focus,
    &:active,
    &:hover,
    &.Mui-focusVisible {
      background: var(--auto-btn-green-border);
    }
  }
}

.dataTable {
  .list-title {
    color: var(--auto-green);
  }

  .MuiDataGrid-row {
    .MuiDataGrid-cell {
      .MuiIconButton-sizeMedium {
        --dark-blue: var(--auto-btn-yellow-border);
      }
      .MuiCheckbox-root {
        &.Mui-checked {
          svg {
            --dark-blue: var(--auto-btn-green-border);
          }
        }
      }
    }
  }
}

.upload-icon-info {
  svg {
    // color: var(--auto-btn-yellow);
  }
}

.icon-btn-edit {
  --dark-blue: var(--auto-btn-yellow);
}

.MuiMenu-list {
  li {
    .MuiIconButton-root {
      --dark-blue: var(--auto-btn-yellow);
    }
  }
}

.breadcrumb {
  ol {
    li {
      a {
        color: var(--auto-green);
      }
    }
  }
}

.main-dailog,
.modal-dialog-1 {
  .close-icon {
    background-color: var(--auto-btn-green) !important;
    border: solid 1px var(--auto-btn-green-border) !important;
  }
}
.datagrid-action-btn,
.table-filters {
  svg {
    --dark-blue: var(--auto-btn-yellow-border);
  }
}

.radio-group-gap {
  .MuiRadio-root {
    .MuiSvgIcon-root {
      --orange: var(--auto-btn-green-border);
    }
  }
}
.base-resource-checkbox {
  .Mui-checked {
    color: var(--auto-btn-green-border) !important;
  }
}

.switch-controller {
  --orange: var(--auto-btn-green-border);
}
.page-view {
  .filter-sidebar {
    .filter-header {
      color: var(--auto-green);
    }
  }
}

.custom-chip {
  border-color: var(--auto-btn-yellow-border);
  --chip-bg: transparent;
  svg {
    --dark-blue: var(--auto-btn-yellow-border);
  }
}
.custom-checkbox {
  color: var(--auto-btn-green-border) !important;
}

.mergetag-popup {
  .table-draggable {
    .MuiCheckbox-root {
      color: var(--auto-btn-green-border) !important;
    }
  }
}
.page-view {
  .switches-container {
    background-color: var(--auto-btn-green);
  }
}

.mui-stepper {
  .MuiStepper-root {
    &.header-stepper {
      .MuiStep-root {
        .MuiStepLabel-iconContainer {
          .MuiSvgIcon-root {
            &.Mui-active {
              color: var(--auto-btn-green);
            }
          }
        }
        &.active-tab {
          & + .MuiStepConnector-root {
            .MuiStepConnector-line {
              --orange: var(--auto-btn-green);
            }
          }
        }
      }
    }
  }

  .stepper-body {
    .file-upload-box {
      h3 {
        color: var(--auto-green);
      }
    }
    .svg-input-opener {
      svg {
        --dark-blue: var(--auto-btn-green);
      }
    }
  }
  .timeline {
    --orange: var(--auto-btn-green);
  }
}
.sub-menu {
  li {
    &.active-child,
    &:hover {
      background-color: var(--white);
      a {
        color: var(--auto-black) !important;
      }
    }
  }
}
.available-box .avail-columns-group .avail-columns,
.main-dailog
  .dailog-body
  .sql-textarea-box
  .avail-columns-group
  .avail-columns {
  color: var(--auto-btn-green);
  border-color: var(--auto-btn-green-border);
}

.rs-query-tabs {
  .MuiTab-root {
    background-color: var(--auto-btn-green);
  }
  .MuiTabs-indicator {
    background-color: var(--auto-sky-blue);
  }
}
