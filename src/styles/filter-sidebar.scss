@import "variables";
@import "commons";

.filter-sidebar {
  width: 358px;
  height: 100%;
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  right: -358px;
  z-index: calc(var(--z-index1200) + 2);
  opacity: 0;
  transition: all 0.3s ease-in;
  box-shadow: -10px 5px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 8px 0px 0px 8px;
  &.medium-panel {
    width: 458px;
    right: -458px;
  }
  &.large-panel {
    width: auto;
    max-width: 70vw;
    min-width: 70vw;
    right: -70vw;
    height: auto;
    &.one {
      max-width: 23vw;
      min-width: 23vw;
      right: -23vw;
    }
    &.two {
      max-width: 50vw;
      min-width: 50vw;
      right: -50vw;
    }
    &.show {
      right: 0;
      opacity: 1;
    }
  }
  &.show {
    right: 0;
    opacity: 1;
  }
  background: var(--white);
  .filter-header {
    background: var(--orange);
    color: var(--white);
    font-size: 20px;
    line-height: 1;
    padding: 11px 16px;
    position: relative;
    .close-filter {
      width: 24px;
      height: 24px;
      position: absolute;
      top: 50%;
      right: 5px;
      cursor: pointer;
      transform: translateY(-50%);
      border-radius: 50%;
      border: solid 2px var(--white);
      display: flex;
      align-items: center;
      justify-content: center;
      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
  .filter-header {
    background: transparent;
    color: var(--orange);
    border-bottom: solid 1px var(--input-box-border);
  }
  .filter-body {
    height: calc(100% - 110px);
    overflow: auto;
    .not-has-accordion {
      padding: 26px;
    }
    .accordion-panel {
      .MuiPaper-elevation {
        border: none;
        border-bottom: solid 1px var(--input-box-border67);
        border-radius: 0px;
      }
      .min-header {
        color: var(--dark-grey);
        font-size: 14px;
        font-weight: 400;
        min-height: 36px !important;
      }
      .MuiCollapse-wrapper {
        background: var(--card-bg);
        padding: 6px 12px;
      }
      &.alternative {
        .MuiCollapse-vertical {
          &:before {
            display: none;
          }
        }
      }
      .MuiAccordionDetails-root {
        padding: 0px;
      }
    }
    .form-group {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      &.non-flex {
        display: block;
        .form-label {
          margin-bottom: 6px;
        }
      }
      .form-label {
        font-size: 15px;
        color: var(--black);
        flex: 115 115 0;
      }
      .form-input {
        flex: 185 185 0;
      }
      & + .form-group {
        margin-top: 12px;
      }
    }
  }
  .filter-footer {
    padding: 15px 15px;
    display: flex;
    column-gap: 16px;
    align-items: center;
    justify-content: flex-end;
  }

  .issue-trails {
    h4 {
      font-size: 14px;
      font-weight: 400;
      color: var(--black);
      margin-bottom: 8px;
    }
    ul {
      margin: 0px;
      padding: 0px;
      li {
        list-style-type: none;
        font-size: 12px;
        margin-bottom: 8px;
        .list-box {
          padding: 8px;
          background: var(--card-bg);
          border-radius: 4px;
        }
        .date-time-col {
          //border: solid 1px red;
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;
          position: relative;
          &:before {
            content: "";
            position: absolute;
            bottom: -8px;
            left: 0px;
            width: 100%;
            background: var(--card-border);
            height: 1px;
          }
        }

        .comment {
          margin-top: 16px;
          position: relative;
          display: block;
          &:before {
            content: "";
            position: absolute;
            top: -8px;
            left: 0px;
            width: 100%;
            background: var(--card-border);
            height: 1px;
          }
        }
        .status {
          &.new {
            color: var(--dark-blue);
          }
          &.progress {
            color: var(--orange);
          }
          &.acknowledged {
            color: #d84a35;
          }

          &.closed {
            color: #74b856;
          }
        }
      }
    }
  }
}
