import { DomainResourceMapping } from "./domain";

export interface IResourceData {
  code: any;
  additional_properties: any;
  id: number;
  created_on: string;
  name: string;
  resource_name: string;
  resource_path: string;
  resource_prefix: string;
  resource_type: string;
  domain_id: number;
  domain_resource_mapping: DomainResourceMapping;
}

export interface IGetDomainProp {
  page?: number;
  pSize?: number;
}
export interface IGetDomainLinkageProp {
  payload?: any;
  page?: number;
  pSize?: number;
}

export interface IGetResourceProp {
  currentDomainId?: string | number;
  page?: number;
  pSize?: number;
}
export interface IGetResourceFilesList {
  currentResourceId: string | number;
}
export interface IGetResourceColumns {
  resourceColumnDetailId: number | null;
}

export interface IResourceDetail {
  resource_name: string;
  resource_type: string;
  domain_id: number;
  id: number;
  resource_prefix: string;
  additional_properties: {
    resource_definition: {
      type: string;
      local_definition: {
        resource_path: string | null;
        column_delimiter: string | null;
      };
      blob_definition: null;
      sql_definition: null;
    };
    resource_column_details_id: number;
    additional_resource_data: string[] | null;
  };
}

export interface IResourceDetails {
  entity_type: string;
  entity_id: number;
  entity_code: string;
  is_already_exist: boolean;
  is_active_if_exist: boolean;
  entity_id_in_target_env: number;
  status: string;
}
export interface IResourceLocalDefinition {
  resource_path: string;
  file_name: string | null;
  column_delimiter: string | null;
  connection_key: number;
  connection_key_code: string;
}

export interface IResourceSftpDefinition {
  connection_key: number;
  connection_key_code: string;
  remote_directory: string;
  file_name: string;
  column_delimiter: string;
}

export interface IResourceBlobDefinition {
  connection_key: number;
  connection_key_code: string;
  container_name: string;
  resource_path: string;
  file_name: string;
  column_delimiter: string;
}

export interface IResourceSqlDefinition {
  connection_key: number;
  connection_key_code: string;
  database_type: string | null;
  connection_string: string | null;
  sql_query: string;
}

export interface IResourceApiDefinition {
  connection_key: number;
  connection_key_code: string;
  method: string;
  content_type: string;
  body: string | null;
  query_params: Record<string, any>;
  url_params: Record<string, any>;
  user_name: string | null;
  password: string | null;
  bearer_token: string | null;
  api_key: string | null;
  oauth_client_id: string;
  oauth_client_secret: string;
  oauth_url: string;
}

export interface IResourceResponse {
  id: number;
  resource_name: string;
  resource_type: string;
  resource_prefix: string;
  code: string;
  aggregation_type: string;
  domain_id: number;
  domain_name: string;
  domain_code: string;
  is_active: boolean;
  linked_service_id: number;
  linked_service_code: string;
  current_url: string;
  file_processing_id: number;
  file_processing_code: string | null;
  additional_properties: {
    resource_definition: {
      type: string;
      local_definition: IResourceLocalDefinition | null;
      blob_definition: IResourceBlobDefinition | null;
      sql_definition: IResourceSqlDefinition | null;
      sftp_definition: IResourceSftpDefinition | null;
      api_definition: IResourceApiDefinition | null;
    };
    aggregation_properties: any;
    resource_column_details_id: number;
    resource_column_details_code: string;
    additional_resource_data: any[];
    filter_rules: any[];
    inline_variables: {
      v1: string;
      v2: string;
    };
  };
  details?: IResourceDetails;
  version: string;
  created_by: string;
  modified_by: string;
  created_on: any;
  updated_on: any;
}
