export type ValidationSeverityLevel = "Low" | "Medium" | "High";

export interface IRunParameters {
  no_of_errors_in_response: number;
  no_of_errors_in_output_files: number;
  skip_duplicate_records: boolean;
  summary_mode: boolean;
  generate_files: boolean;
  skip_validation: boolean;
  validation_severity_level:
    | ValidationSeverityLevel
    | Lowercase<ValidationSeverityLevel>;
  run_instance: {
    run_id: number;
    run_name: string;
  };
  save_input_data_file: boolean;
  store_errors_snapshots_and_create_issues: boolean;
  keep_downloaded_files: boolean;
  pull_new_files_from_server: boolean;
  is_long_running_job: boolean;
  isCustomiseSeverity: boolean;
}

export interface IResultStorageParameters {
  linked_service_id: number | string | null;
  linked_service_code: string | null;
  connection_key_id: number | string | null;
  file_path: string | null;
  validation_execution_report_name?: string | null;
  rule_execution_report_name?: string | null;
  execute_comparison_research_query?: boolean;
  use_secondary_merge_resources?: boolean;
}
export interface IRerunExecution {
  rule_execution_request: {
    request_body: {
      resource_data: Array<{
        resource_id: number;
        resource_prefix: string | null;
        linked_service_id: number | null;
        linked_service_code: string | null;
        connection_key: string | null;
        sql_query: string | null;
        container_name: string | null;
        resource_path: string | null;
        file_name: string;
        column_delimiter: string | null;
        file_pre_processing: {
          file_format: string;
          schema_definition: string;
          field_delimiter: string;
          custom_attributes: {
            type: string;
            store_name: string;
            store_label: string;
          };
          has_footer: boolean;
          footer_lines: number;
          has_header: boolean;
          header_lines: number;
          skip_rows: number;
          has_multiple_sheets: boolean;
          skip_blanks: boolean;
          compression: string | null;
          compression_codec: string | null;
          line_terminator: string;
          has_comments: string;
          comments_marker: string;
          encoding: string;
          bad_lines: string;
        };
        filter_rules: any | null;
        api_request: any | null;
        aggregation_properties: any | null;
        aggregation_base_resource_data: any | null;
        additional_base_resource_data: any | null;
        secondary_merge_resource: any | null;
        pre_processing_request: any | null;
      }>;
      output_storage_params: any | null;
      run_instance: any | null;
      validation_severity_level: ValidationSeverityLevel | null;
      severity_column_names: any | null;
      filter_rules: any | null;
      rule_execution_report_name: string | null;
      validation_execution_report_name: string | null;
      inline_variables: any | null;
      pivot_results: any | null;
      pull_latest_files: boolean;
      query_params: any | null;
      latest_execution_id: any | null;
      is_rerun: boolean;
    };
    no_of_errors_in_response: number;
    no_of_sample_validation_errors: number;
    no_of_errors_in_output_files: number;
    no_of_errors_in_filter_record_output_files: number;
    summary_mode: boolean;
    generate_files: boolean;
    skip_validation: boolean;
    save_input_data_file: boolean;
    execute_comparison_research_query: boolean;
    skip_duplicate_records: boolean;
    keep_downloaded_files: boolean;
    use_secondary_merge_resources: boolean;
    use_merge_on_keys_as_unique_keys: boolean;
    store_errors_snapshots_and_create_issues: boolean;
    additional_info_for_all_resources: Array<{
      file_names: string[];
      resource_id: number;
      resource_column_details_version: number;
      resource_version: number;
      domain_version: number;
    }>;
  };
  rule_id: number;
  rule_version: string;
}
