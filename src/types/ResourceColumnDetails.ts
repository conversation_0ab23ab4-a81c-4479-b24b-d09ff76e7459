export interface IReferenceColumnDefinition {
  source: string;
  source_type: string;
  use_translation: boolean;
  linked_service_id: number | null;
  linked_service_code: string | null;
  connection_key: number | null;
  connection_key_code: string | null;
}

export interface IConstraints {
  datatype: string;
  is_mandatory: boolean;
  data_format: string;
  column_length: number | null;
  is_derived: boolean | null;
  derived_column_definition: any;
  is_reference: boolean | null;
  reference_column_definition: IReferenceColumnDefinition | null;
  custom_validations: any;
}

export interface IResourceColumn {
  is_active: boolean;
  column_name: string;
  domain_column: string;
  constraints: IConstraints;
  severity_level: number;
}

export interface IResourceColumnProperties {
  unique_columns: string[];
  resource_columns: IResourceColumn[];
  cross_field_validations: any[];
  inline_variables: any;
}

export interface IResourceColumnDetailsResponse {
  id: number;
  name: string;
  code: string;
  domain_id: number;
  domain_code: string;
  is_active: boolean;
  resource_column_properties: IResourceColumnProperties;
  version: number;
  created_by: string;
  modified_by: string;
  created_on: any;
  updated_on: any;
  details: {
    entity_type: string;
    entity_id: number;
    entity_code: string;
    is_already_exist: boolean;
    is_active_if_exist: boolean;
    entity_id_in_target_env: number;
    status: string;
  };
}
