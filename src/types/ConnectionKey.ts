import { IUser } from "./User";

export interface IConnectionKey {
  connection_key_code: string;
  connection_key_id: number;
}

export interface IConnectionDetails {
  connection_keys: IConnectionKey[];
  connection_keys_details: IConnectionKey[];
}

export interface IConnectionKeysDetails {
  connection_keys_details: IConnectionKey[];
}

export interface IConnectionKeyResponse {
  id: number;
  name: string;
  type: string;
  code: string;
  ip_address: any;
  user_name: any;
  password: any;
  is_active: boolean;
  container_name: any;
  connection_string: any;
  api_url: any;
  created_by: string;
  modified_by: string;
  created_on: any;
  updated_on: any;
  created_by_user: IUser;
  modified_by_user: IUser;
  azure_client_id: any;
  azure_client_secret: any;
  azure_tenant_id: any;
  details: {
    entity_type: string;
    entity_id: number;
    entity_code: string;
    is_already_exist: boolean;
    is_active_if_exist: boolean;
    entity_id_in_target_env: number;
    status: string;
  };
}
