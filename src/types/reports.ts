export interface IgetReportsByRunInstanceProps {
  runName: string;
  runDate?: string;
  page: number | null;
  pSize: number | null;
  formattedDate?: any;
}

export interface IPieData {
  resource_name: string;
  resource_id: number;
  domain_id: number;
  total_records: number;
  total_common_records?: number;
  total_mismatched_records: number;
  total_missing_records: number;
  total_duplicate_records: number;
  total_unique_records: number;
  filtered_records_by_resource_filters: number;
  total_matched_records: number;
  false_positive_missing_records_count: number;
  filtered_records_by_rule_filters: number;
  key?: any;
  countsData: {
    match: number;
    mismatch: number;
    missing: number;
    duplicate: number;
    total: number;
    [key: string]: number; // Allow indexing with a string
  };
}
