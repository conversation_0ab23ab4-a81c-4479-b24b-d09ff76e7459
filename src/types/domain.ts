export interface IDomainsData {
  id: number;
  domain_name: string;
  domain_desc: string;
  domain_code: string;
  domain_properties: any;
  render: any;
}

export interface DomainColumnMapping {
  domain_column: string;
  resource_column_name: string;
}

export interface DomainResourceMapping {
  columns: DomainColumnMapping[];
}

export interface DataType {
  id: React.Key;
  name: string;
  datatype: string;
  mandatory: string;
  format: string;
  key: string;
  action: string;
}

export interface IDomainProp {
  colorBgContainer?: string;
}

export interface IDomainResponse {
  id: number;
  domain_name: string;
  domain_code: string;
  domain_desc: string;
  domain_properties: {
    columns: {
      name: string;
      datatype: string;
      mandatory: boolean;
      format: string;
      reference_data: any;
    }[];
    unique_columns: string[];
  };
  created_by: string;
  modified_by: string;
  created_on: any;
  updated_on: any;
  is_active: boolean;
  version: number;
  details: {
    entity_type: string;
    entity_id: number;
    entity_code: string;
    is_already_exist: boolean;
    is_active_if_exist: boolean;
    entity_id_in_target_env: number;
    status: string;
  };
}
