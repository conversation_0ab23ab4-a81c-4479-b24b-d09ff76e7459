import { IConnectionDetails, IConnectionKeysDetails } from "./ConnectionKey";
import { IUser } from "./User";

export interface ILinkedServiceResponse {
  id: number;
  name: string;
  type: string;
  code: string;
  sub_type: string;
  is_active: boolean;
  connection_details: IConnectionDetails;
  connection_keys_details: IConnectionKeysDetails;
  created_by: string;
  modified_by: string;
  created_on: any;
  updated_on: any;
  created_by_user: IUser;
  modified_by_user: IUser;
  details: {
    entity_type: string;
    entity_id: number;
    entity_code: string;
    is_already_exist: boolean;
    is_active_if_exist: boolean;
    entity_id_in_target_env: number;
    status: string;
  };
}
