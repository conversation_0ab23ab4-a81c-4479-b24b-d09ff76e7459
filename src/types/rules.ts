export interface IRulesListData {
  id: number;
  rule_name: string;
  domain_name: string;
  resources: string;
  mandatory: string;
}

export interface IGetRulesListData {
  id: number;
  description: string;
  name: string;
  domain_name: string;
  domain_id: number;
  rule_schema: any;
}
export interface IGetIssuesListData {
  id: number;
  missing_records_id: number;
  rule_id: number;
  execution_id: number;
  comparison_keys: string;
  error_message: string;
  additional_info: string;
  issue_type: string;
}
export interface IGetIssueHistoryData {
  id: number;
  missing_records_id: number;
  rule_id: number;
  execution_id: number;
  comparison_keys: string;
  error_message: string;
  additional_info: string;
  issue_type: string;
}

export interface IResourceData {
  resource_id: number;
  file_name: string;
}

export interface IExecuteRuleData {
  resource_data: IResourceData[];
}

export interface IExecuteRuleResponse {
  is_success: boolean;
  name: string;
  domain_id: number;
  domain_name: string;
  execution_time: string;
  execution_report: string;
}

export interface IRuleResponse {
  id: number;
  name: string;
  description: string;
  code: string;
  domain_id: number;
  domain_name: string;
  domain_code: string;
  current_url: string;
  rule_schema: {
    merge_rule: {
      primary_dataset: IRuleDataset;
      secondary_datasets: IRuleDataset[];
    };
    filter_rules: IFilterRule[];
    domain_comparison_rules: IComparisonRule[];
    custom_comparison_rules: ICustomComparisonRule[];
    adhoc_queries: IRuleAdhocQuery[];
    resources: number[];
    resources_detailed_list: IRuleResourceDetail[];
    overriden_prefix: any;
    inline_variables: { [key: string]: string };
  };
}

export interface IRuleDataset {
  resource_id: number;
  resource_code: string;
  merge_on: string[];
  possible_merge_on_columns: { [key: string]: any };
  merge_type?: string;
}

export interface IFilterRule {
  name: string;
  resource_id: number;
  resource_code: string;
  sql_query: string;
}

export interface IComparisonRule {
  name: string;
  column_name: string;
  comparison_type: string;
  tolerance_type: string;
  tolerance_value: number;
}

export interface ICustomComparisonRule {
  name: string;
  left_operand: IRuleOperand;
  right_operand: IRuleOperand;
  comparison_type: string;
  filter_rules: IFilterRule[];
  tolerance_type: string;
  tolerance_value: number;
}

export interface IRuleOperand {
  type: string;
  value: string;
  resource_id: number;
  resource_code: string;
  column_name: string;
}

export interface IRuleAdhocQuery {
  name: string;
  sql_query: string;
}

export interface IRuleResourceDetail {
  resource_id: number;
  resource_code: string;
}
