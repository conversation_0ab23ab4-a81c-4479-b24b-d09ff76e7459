// Define the interface for the "source" property
export interface IResourceSource {
    type: "Resource" | "Merged_Data" | "External";
    resource_id?: string; // Only present if type is "Resource"
    resource_code?: string; // Only present if type is "Resource"
    linked_service_id?: number; // Only present if type is "External"
    linked_service_code?: string; // Only present if type is "External"
    connection_key_id?: number; // Only present if type is "External"
    connection_key_code?: string; // Only present if type is "External"
}

// Define the interface for each research query
export interface IResearchQueryDetail  {
    name: string;
    code: string;
    description: string;
    rule_id: number | null;
    research_queries: IResearchQuery[];
}

export interface IResearchQuery {
    id: number;
    name: string;
    source: IResourceSource;
    query: string;
}
