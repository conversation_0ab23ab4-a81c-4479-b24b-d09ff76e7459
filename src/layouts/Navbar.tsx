import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Menu as MenuIcon,
  Search,
  DoneAll,
  ArrowDropDownOutlined,
} from "@mui/icons-material";
import FlexBetween from "../components/FlexBetween";
import logoLarge from "../assets/svgs/xlr8_logo.svg";
import {
  AppBar,
  Button,
  Box,
  Typography,
  IconButton,
  InputBase,
  Toolbar,
  Menu,
  MenuItem,
  useTheme,
  Badge,
  Popover,
  List,
  ListItem,
  ListItemText,
  Tooltip,
  Divider,
} from "@mui/material";
import { useAuth } from "../contexts/AuthContext";
import { useNotifications } from "../contexts/NotificationContext";
import { IconLogoutNew, IconNotification } from "../common/utils/icons";

interface NavbarProps {
  user: {
    name: string;
    occupation: string;
  };
  isSidebarOpen: boolean;
  setIsSidebarOpen: (isOpen: boolean) => void;
}

const Navbar: React.FC<NavbarProps> = ({
  user,
  isSidebarOpen,
  setIsSidebarOpen,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const isOpen = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) =>
    setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  const { setIsAuthenticated } = useAuth();
  const [notificationAnchorEl, setNotificationAnchorEl] =
    useState<null | HTMLElement>(null);

  const storedUserInfo = localStorage.getItem("userInfo");
  const parsedUserInfo = storedUserInfo ? JSON.parse(storedUserInfo) : {};
  const { firstname, lastname, username } = parsedUserInfo;
  // Use the notification context
  const {
    notifications,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    unreadCount,
  } = useNotifications();

  const handleNotificationClick = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchorEl(event.currentTarget);
  };

  const handleNotificationClose = () => {
    setNotificationAnchorEl(null);
  };

  const handleNotificationItemClick = (notification: any) => {
    // Extract executionId if it exists

    // Mark the notification as read
    markAsRead(notification.id);

    if (
      notification?.execution_status === "completed" &&
      notification?.execution_id
    ) {
      const { execution_id, execution_type } = notification;

      const paths: any = {
        validation: `/validation-execution-history/validate-result/${execution_id}`,
        validation_rerun: `/validation-execution-history/validate-result/${execution_id}`,
        rule: `/rules-execution-history/${execution_id}/${execution_id}/dashboard`,
        rule_rerun: `/rules-execution-history/${execution_id}/${execution_id}/dashboard`,
      };

      if (paths[execution_type]) {
        navigate(paths[execution_type]);
      }
    }

    handleNotificationClose();
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  const handleClearAllNotifications = () => {
    clearAllNotifications();
    handleNotificationClose();
  };

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("notifications");
    // removed below line because its not navigating to the previous path if removed lastVisitedPath
    // localStorage.removeItem("lastVisitedPath");
    navigate("/login");
    setIsAuthenticated(false);
    handleClose();
  };

  return (
    <AppBar
      sx={{
        position: "static",
        background: "none",
        boxShadow: "none",
      }}
      className="main-header"
    >
      <Toolbar sx={{ justifyContent: "space-between" }}>
        {/* LEFT SIDE */}
        <Box className="logo-with-searchbar">
          <Box className="sidebarLogoBox">
            <Box
              display="flex"
              alignItems="center"
              justifyContent="center"
              sx={{ columnGap: "20px" }}
            >
              <img src={logoLarge} className="large-logo" />
            </Box>
          </Box>
          <FlexBetween className="search-panel">
            <IconButton className="search-icon">
              <Search className="svg_icons" />
            </IconButton>
            <InputBase placeholder="Search..." className="search-textbox" />
          </FlexBetween>
        </Box>

        {/* RIGHT SIDE */}
        <FlexBetween gap="1.5rem">
          <IconButton
            className="icon-notification"
            onClick={handleNotificationClick}
          >
            <Badge badgeContent={unreadCount} color="error">
              <IconNotification />
            </Badge>
          </IconButton>

          <Popover
            open={Boolean(notificationAnchorEl)}
            anchorEl={notificationAnchorEl}
            onClose={handleNotificationClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
          >
            <Box sx={{ width: 300, maxHeight: 500 }}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  padding: "8px 16px",
                  borderBottom: "1px solid #eee",
                }}
              >
                <Typography variant="subtitle1" fontWeight="bold">
                  Notifications
                </Typography>
                <Box>
                  <Tooltip title="Mark all as read">
                    <IconButton size="small" onClick={handleMarkAllAsRead}>
                      <DoneAll fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              <List sx={{ maxHeight: 400, overflow: "auto" }}>
                {notifications.length === 0 ? (
                  <ListItem>
                    <ListItemText primary="No notifications" />
                  </ListItem>
                ) : (
                  notifications.map((notification, index) => (
                    <Tooltip
                      key={notification.id || index}
                      title={notification.message}
                      placement="left"
                      arrow
                    >
                      <ListItem
                        sx={{
                          borderBottom: "1px solid #eee",
                          "&:hover": { backgroundColor: "#f5f5f5" },
                          cursor: "pointer",
                          backgroundColor: notification.read
                            ? "transparent"
                            : "rgba(25, 118, 210, 0.08)",
                        }}
                        onClick={() =>
                          handleNotificationItemClick(notification)
                        }
                      >
                        <ListItemText
                          primary={<></>}
                          secondary={
                            <Box>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {notification.message}
                              </Typography>
                            </Box>
                          }
                          secondaryTypographyProps={{
                            sx: {
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              display: "-webkit-box",
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: "vertical",
                            },
                          }}
                        />
                      </ListItem>
                    </Tooltip>
                  ))
                )}
              </List>

              {notifications.length > 0 && (
                <Box
                  sx={{
                    padding: "8px 16px",
                    borderTop: "1px solid #eee",
                    display: "flex",
                    justifyContent: "center",
                  }}
                >
                  <Button
                    size="small"
                    color="error"
                    onClick={handleClearAllNotifications}
                  >
                    Clear All
                  </Button>
                </Box>
              )}
            </Box>
          </Popover>

          <FlexBetween>
            <Button onClick={handleClick} className={"user-dropdown"}>
              <Box textAlign="left">
                <Typography className="username">
                  {firstname.slice(0, 1)}
                  {lastname.slice(0, 1)}
                </Typography>
              </Box>
              <ArrowDropDownOutlined className="dropdown-arrow" />
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={isOpen}
              onClose={handleClose}
              anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
              className="header-dropdown-menu"
            >
              <MenuItem className="user-details">{username}</MenuItem>
              <MenuItem className="user-details">
                <div className="version">V 25.8.0</div>
              </MenuItem>

              <MenuItem>
                <Button className="btn-logout" onClick={handleLogout}>
                  <IconLogoutNew />
                  Log Out
                </Button>
              </MenuItem>
            </Menu>
          </FlexBetween>
        </FlexBetween>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
