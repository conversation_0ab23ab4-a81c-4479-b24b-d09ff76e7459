import { Link, useLocation, useNavigate } from "react-router-dom";
import Box from "@mui/material/Box";
import ListItemIcon from "@mui/material/ListItemIcon";
import IconButton from "@mui/material/IconButton";
import iconLogout from "../assets/svgs/icon-logout-white.svg";
import jira<PERSON>ogo from "../assets/svgs/jira-logo.svg";
import { constants } from "../services/constants/JIRA I_Issue_Collector";
import {
  IconFileProcessingSvg,
  IconReportsWhiteSvg,
  IconResearchQueryHistorySvg,
} from "../common/utils/icons";
import { useAuth } from "../contexts/AuthContext";

interface NavSubItem {
  text: string;
  url: string;
  routes?: string[];
}
interface NavItem {
  text: string;
  url?: string;
  icon?: JSX.Element;
  isStroke?: boolean;
  routes?: string[];
  children?: NavSubItem[]; // 👈 New: for submenus
}

interface SidebarProps {
  user: {
    name: string;
    occupation: string;
  };
  drawerWidth: number;
  closedDrawerWidth: number;
  isSidebarOpen: boolean;
  setIsSidebarOpen: (isOpen: boolean) => void;
  isNonMobile: boolean;
}

const navItems: NavItem[] = [
  {
    text: "Configurations",
    icon: <IconFileProcessingSvg />,
    isStroke: true,
    children: [
      {
        text: "Domain",
        url: "domain",
        routes: [
          "/",
          "/domain",
          "/domain/add",
          "/domain/view/:id",
          "/domain/edit/:id",
          "/domain/auditDomain/:id",
          "/domain/import-entity",
        ],
      },
      {
        text: "Domain Linkage",
        url: "/domain-linkage",
        routes: [
          "/domain-linkage",
          "/domain-linkage/add",
          "/domain-linkage/view/:id",
          "/domain-linkage/edit/:id",
        ],
      },
      {
        text: "Resource",
        url: "resource/all",
        routes: [
          "/resource/:domainId?",
          "/resource/add/:domainId?",
          "/resource/:domainId/view/:id",
          "/resource/:domainId/edit/:id",
          "/resource/:domainId/auditResource/:id",
          "/resource/:domainId/validate-result",
          "/resource/:domainId/validate-resource/:id",
          "/resource/import-entity",
          "/resource/:domainId/researchQuery/:id",
        ],
      },
      {
        text: "Comparison Rules",
        url: "rules-list/all",
        routes: [
          "/rules-list/:domainId?",
          "/rules/:domainId/edit/:id",
          "/rules/:domainId/view/:id",
          "/rules/:domainId/rule-execution/:ruleId",
          "/rules/:domainId/auditRule/:id",
          "/rules/add",
          "/rules/import-entity",
          "/rules/:domainId/researchQuery/:id",
        ],
      },
    ],
  },
  {
    text: "Results",
    icon: <IconReportsWhiteSvg />,
    isStroke: true,
    children: [
      {
        text: "All Issues List",
        url: "issue-list",
        routes: ["/issue-list"],
      },
      {
        text: "Validation History",
        url: "validation-execution-history",
        routes: [
          "/validation-execution-history",
          "/validation-execution-history/validate-result/:resourceResultId",
        ],
      },
      {
        text: "Validation Summary",
        url: "validation-execution-history/validation-execution-results",
        routes: ["/validation-execution-history/validation-execution-results"],
      },
      {
        text: "Comparison History",
        url: "rules-execution-history",
        routes: [
          "/rules-execution-history",
          "/rules-execution-history/:id/:ruleResultId/dashboard",
        ],
      },
      {
        text: "Comparison Summary",
        url: "rules-execution-history/consolidate-results",
        routes: ["/rules-execution-history/consolidate-results"],
      },
    ],
  },
  {
    text: "Research Management",
    icon: <IconResearchQueryHistorySvg />,
    isStroke: true,
    children: [
      {
        text: "Research Queries",
        url: "/research-query",
        routes: ["/research-query", "/research-query/add"],
      },
      {
        text: "Research Query History",
        url: "/research-execution-history",
        routes: [
          "/research-execution-history",
          "/research-query/:execution_id/dashboard",
        ],
      },
    ],
  },
];

const Sidebar = ({
  isSidebarOpen,
  setIsSidebarOpen,
  isNonMobile,
  drawerWidth,
  closedDrawerWidth,
}: SidebarProps) => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { setIsAuthenticated } = useAuth();

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("notifications");
    setIsAuthenticated(false);
    navigate("/login");
  };

  return (
    <Box
      component="nav"
      className={
        isSidebarOpen
          ? "position-relative"
          : "position-relative sidebar-drawer-closed"
      }
    >
      <Box
        sx={{
          width: `${
            isSidebarOpen
              ? drawerWidth + "px"
              : !isNonMobile
              ? "13px"
              : closedDrawerWidth + "px"
          }`,
        }}
        className={
          isSidebarOpen && !isNonMobile
            ? "small-screen-navbar"
            : isSidebarOpen
            ? "sidebar-drawer"
            : "side-bar-open sidebar-drawer"
        }
      >
        <Box
          className={`MuiDrawer-paper ${
            !isNonMobile ? "menu-in-small-screen-parent" : ""
          }`}
          sx={{
            width: `${
              isSidebarOpen
                ? drawerWidth + "px"
                : !isNonMobile
                ? "13px"
                : closedDrawerWidth + "px"
            }`,
          }}
        >
          <IconButton
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="navbar-icon"
          ></IconButton>
          <Box
            width="100%"
            height="100%"
            className={!isNonMobile ? "menu-in-small-screen" : ""}
          >
            <ul className="list-name">
              {navItems.map(
                (
                  { text, url, icon, isStroke, children, routes }: NavItem,
                  index: number
                ) => {
                  const allRoutes = children
                    ? children.flatMap((child) => child.routes || [])
                    : routes || [];
                  const normalizedUrl = url
                    ? url.startsWith("/")
                      ? url
                      : `/${url}`
                    : undefined;
                  const isActive = allRoutes.some((route) => {
                    if (route === "/") {
                      return pathname === "/";
                    }
                    const regex = new RegExp(
                      "^" +
                        route
                          .replace(/:[^/]+/g, "[^/]+") // convert ":id" to match any segment
                          .replace(/\//g, "\\/") +
                        "$"
                    );
                    return regex.test(pathname);
                  });

                  return (
                    <li
                      key={text}
                      className={`${isStroke ? "icon-stroke" : ""} ${
                        children ? "has-children" : ""
                      } menu-${index + 1} ${isActive ? "active" : ""}
                      `}
                    >
                      <ListItemIcon>{icon}</ListItemIcon>
                      <span className="list-item-name">
                        {normalizedUrl ? (
                          <Link to={normalizedUrl}>{text}</Link>
                        ) : (
                          <span className="without-link">{text}</span>
                        )}
                        {children && (
                          <ul className="sub-menu">
                            {children.map((subItem) => {
                              const isChildActive = (subItem.routes || []).some(
                                (route) => {
                                  const regex = new RegExp(
                                    "^" +
                                      route
                                        .replace(/:[^/]+/g, "[^/]+")
                                        .replace(/\//g, "\\/") +
                                      "$"
                                  );
                                  return regex.test(pathname);
                                }
                              );

                              return (
                                <li
                                  key={subItem.text}
                                  className={
                                    isChildActive ? "active-child" : ""
                                  }
                                >
                                  <Link to={subItem.url}>
                                    <span>{subItem.text}</span>
                                  </Link>
                                </li>
                              );
                            })}
                          </ul>
                        )}
                      </span>
                    </li>
                  );
                }
              )}
            </ul>

            <Box className="logout-box">
              <Box
                className="jira-feedback cursor-pointer"
                onClick={() => {
                  const blankWindow = window.open("Feedback");
                  const document = blankWindow?.document;
                  if (document) {
                    document.open();
                    document.write(`
                    <html>
                      <head>
                        <title>Feedback</title>
                      </head>
                      <body>
                        ${constants.JIRA_ISSUE_COLLECTOR_SCRIPT}
                      </body>
                    </html>
                  `);
                    document.close();
                  }
                }}
              >
                <img src={jiraLogo} alt="" />
                Jira Feedback
              </Box>

              <button className="btn-logout" onClick={handleLogout}>
                <img src={iconLogout} alt="" />
                <span>Logout</span>
              </button>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Sidebar;
