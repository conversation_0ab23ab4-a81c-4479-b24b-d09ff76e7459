import { Typography, Box, useTheme } from "@mui/material";
import React from "react";
import { Theme } from "@mui/material/styles";

interface HeaderProps {
  title: string;
  subtitle: string;
}

const Header: React.FC<HeaderProps> = ({ title, subtitle }) => {
  const theme: Theme = useTheme();

  return (
    <Box>
      <Typography
        variant="h2"
        color={theme.palette.secondary[(100 as unknown) as keyof typeof theme.palette.secondary]}
        fontWeight="bold"
        sx={{ mb: "5px" }}
      >
        {title}
      </Typography>
      <Typography
        variant="h5"
        color={theme.palette.secondary[(300 as unknown) as keyof typeof theme.palette.secondary]}
      >
        {subtitle}
      </Typography>
    </Box>
  );
};

export default Header;
