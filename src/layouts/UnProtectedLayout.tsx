import React, { useState } from "react";
import { Box, useMediaQuery } from "@mui/material";
import { Outlet, Navigate } from "react-router-dom";

interface LayoutProps {
  data: {
    name: string;
    occupation: string;
  }; // Update the type of 'data' according to your requirements
}

const UnProtectedLayout: React.FC<LayoutProps> = ({ data }) => {
  const isNonMobile = useMediaQuery("(min-width: 600px)");
  const token = localStorage.getItem("token");
  const lastVisitedPath = localStorage.getItem("lastVisitedPath");

  if (token) {
    const redirectPath = lastVisitedPath || "/domain";
    return <Navigate to={redirectPath} />;
  }

  return (
    <Box className="unprotected-pages">
      <Outlet />
    </Box>
  );
};

export default UnProtectedLayout;
