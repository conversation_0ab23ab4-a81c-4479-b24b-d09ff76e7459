import React from "react";
import { Route, Routes } from "react-router-dom";
import ProtectedRoutes from "./ProtectedRoutes";
import ProtectedLayout from "../layouts/ProtectedLayout";
import UnProtectedLayout from "../layouts/UnProtectedLayout";
import UnProtectedRoutes from "./UnProtectedRoutes";
import { useAuth } from "../contexts/AuthContext";
import { BreadCrumbProvider } from "../contexts/BreadCrumbContext";

const SiteRoutes: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <Routes>
      <Route
        path="/"
        element={<UnProtectedLayout data={{ name: "VIS", occupation: "" }} />}
      >
        {UnProtectedRoutes()}
      </Route>
      <Route
        path="/"
        element={
          <BreadCrumbProvider>
            <ProtectedLayout data={{ name: "VIS", occupation: "" }} />
          </BreadCrumbProvider>
        }
      >
        {ProtectedRoutes()}
      </Route>
      <Route path="*" element={<center>404</center>} />
    </Routes>
  );
};

export default SiteRoutes;
