import SignIn from "../pages/Auth/SignIn";
import { Route } from "react-router";
import Signup from "../pages/Auth/Signup";
import Forgot from "../pages/Auth/Forgot";

const UnProtectedRoutes = () => {

  return (
    <>
      <Route index path="/login" element={<SignIn />} />
      <Route path="/signup" element={<Signup />} />
      <Route path="/forgotpassword" element={<Forgot />} />
    </>
  );
};

export default UnProtectedRoutes;
