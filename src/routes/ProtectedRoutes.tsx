import React from "react";
import ExecutionHistory from "../pages/Rules/ExecutionHistory";
import { Route } from "react-router-dom";
import Resource from "../pages/Resource/Resource";
import AddResource from "../pages/Resource/AddResource";
import Domain from "../pages/Domain/Domain";
import AddDomain from "../pages/Domain/AddDomain";
import FileList from "../pages/FileList";
import AddRule from "../pages/Rules/AddRule";
import EditRule from "../pages/Rules/EditRule";
import ViewRule from "../pages/Rules/ViewRule";
import RulesList from "../pages/Rules/RulesList";
import RuleHistory from "../pages/Rules/RuleHistory";
import Home from "../pages/Home";
import AddResourceColumnDetails from "../pages/ResourceColumns/AddResourceColumnDetails";
import ViewResource from "../pages/Resource/ViewResource";
import EditResource from "../pages/Resource/EditResource";
import ViewDomain from "../pages/Domain/ViewDomain";
import EditDomain from "../pages/Domain/EditDomain";
import RuleExecution from "../pages/Rules/RuleExecution";
import ValidateResource from "../pages/Resource/ValidateResource";
import ValidationResult from "../pages/Resource/ValidationResult";
import ConsolidateResults from "../pages/Results/ConsolidateResults";
import ValidationExecutionResults from "../pages/Results/ValidationExecutionResults";
import ValidationHistory from "../pages/Results/ValidationHistory";
import { ResourceProvider } from "../contexts/ResourceContext";
import ResourceColumns from "../pages/ResourceColumns/ResourceColumns";
import ViewResourceColumn from "../pages/ResourceColumns/ViewResourceColumn";
import EditResourceColumn from "../pages/ResourceColumns/EditResourceColumn";
import AddLinkedServices from "../pages/LinkedServices/AddLinkedServices";
import LinkedServices from "../pages/LinkedServices/LinkedServices";
import ViewLinkedServices from "../pages/LinkedServices/ViewLinkedServices";
import ConnectionKeys from "../pages/ConnectionKeys/ConnectionKeys";
import AddConnectionKey from "../pages/ConnectionKeys/AddConnectionKey";
import ViewConnectionKey from "../pages/ConnectionKeys/ViewConnectionKey";
import RunInstance from "../pages/Run Instance/RunInstance";
import AddEditRunInstance from "../pages/Run Instance/AddEditRunInstance";
import ViewRunInstance from "../pages/Run Instance/ViewRunInstance";
import FileProcessingAttributes from "../components/FileProcessingAttributes/FileProcessingAttributes";
import AddEditFileProcessingAttributes from "../components/FileProcessingAttributes/AddEditFileProcessingAttributes";
import ViewFileProcessingAttributes from "../components/FileProcessingAttributes/ViewFileProcessingAttributes";
import { RuleProvider } from "../contexts/RuleContext";
import { RuleResourceProvider } from "../contexts/RuleResourceContext";
import AuditResource from "../pages/Resource/AuditResource";
import AuditResourceColumn from "../pages/ResourceColumns/AuditResourceColumn";
import AuditDomain from "../pages/Domain/AuditDomain";
import AuditRule from "../pages/Results/AuditRule";
import ImportJson from "../components/ImportJson/ImportJson";
import ResourceResearchQuery from "../pages/Resource/ResourceResearchQuery";
import RuleResearchQuery from "../pages/Rules/RuleResearchQuery";
import AuditConnectionKey from "../pages/ConnectionKeys/AuditConnectionKey";
import AuditLinkedServices from "../pages/LinkedServices/AuditLinkedServices";
import AdminPages from "../pages/AdminPages";
import BlobList from "../pages/BlobLists/BlobList";
import ComparisonResultDashboard from "../pages/Rules/ComparisonResultDashboard";
import DomainLinkage from "../pages/DomainLinkage/DomainLinkage";
import AddEditDomainLinkage from "../pages/DomainLinkage/AddEditDomainLinkage";
import IncidentReportingReRun from "../pages/Rules/IncidentReportingReRun";
import IncidentReportingReRunReport from "../pages/Rules/IncidentReportingReRunReport";
import AllIssueList from "../pages/Mockups/AllIssueList";
import ReRunValidateResource from "../pages/Resource/ReRun/ReRunValidateResource";
import ReRunRuleExecution from "../pages/Rules/Rerun/ReRunRuleExecution";
import ViewDomainLinkage from "../pages/DomainLinkage/ViewDomainLinkage";
import Notifications from "../components/NotificationComponent";
import GenericResearchQuery from "../pages/ResearchQuery/GenericResearchQuery";
import GenericResearchQueryList from "../pages/ResearchQuery/GenericResearchQueryList";
import ResearchQueryExecution from "../pages/ResearchQuery/ResearchQueryExecution";
import GenericResultDashboard from "../pages/ResearchQuery/GenericResultDashboard";
import GenericResearchExecutionHistory from "../pages/ResearchQuery/GenericResearchExecutionHistory";

const ProtectedRoutes = () => {
  return (
    <>
      <Route index path="/" element={<Domain />} />

      {/* domain routes start */}
      <Route path="/domain" element={<Domain />} />
      <Route path="/domain/add" element={<AddDomain />} />
      <Route path="/domain/view/:id" element={<ViewDomain />} />
      <Route path="/domain/edit/:id" element={<EditDomain />} />
      <Route path="/domain/auditDomain/:id" element={<AuditDomain />} />
      <Route path="/domain-linkage" element={<DomainLinkage />} />
      <Route path="/domain-linkage/add" element={<AddEditDomainLinkage />} />
      <Route
        path="/domain-linkage/edit/:id"
        element={<AddEditDomainLinkage />}
      />
      <Route path="/domain-linkage/view/:id" element={<ViewDomainLinkage />} />
      {/* domain routes end */}
      {/* resource routes starts */}
      <Route path="/resource/:domainId?" element={<Resource />} />
      <Route
        path="/resource/add/:domainId?"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <AddResource />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/resource/:domainId/view/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <ViewResource />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/resource/:domainId/researchQuery/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <ResourceResearchQuery />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      {/* // indvisual research query started here  */}
      <Route
        path="/research-query"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <GenericResearchQueryList />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/research-query/view/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <GenericResearchQuery />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/research-query/add"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <GenericResearchQuery />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/research-execution-history"
        element={<GenericResearchExecutionHistory />}
      />
      <Route
        path="/research-query/:researchQueryId/:execution_id/dashboard"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <GenericResultDashboard />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/resource/:domainId/edit/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <EditResource />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/research-query/research-query-execution/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <ResearchQueryExecution />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/resource/:domainId/auditResource/:id"
        element={<AuditResource />}
      />
      <Route
        path="/resource/:domainId/validate-result"
        element={<ValidationResult />}
      />
      <Route
        path="/resource/:domainId/validate-resource/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <ValidateResource />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/resource/:domainId/re-run-validate-resource/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <ReRunValidateResource />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      {/* resource routes end */}

      {/* resource columns routes start */}
      <Route
        path="/resource-columns/:domainId"
        element={
          <ResourceProvider>
            <ResourceColumns />
          </ResourceProvider>
        }
      />

      <Route
        path="/resource-columns/add-columns"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <AddResourceColumnDetails />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/resource-columns/:domainId/view/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <ViewResourceColumn />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/resource-columns/:domainId/edit/:id"
        element={
          <RuleResourceProvider>
            <ResourceProvider>
              <EditResourceColumn />
            </ResourceProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/resource-columns/:domainId/auditResourceColumn/:id"
        element={
          <ResourceProvider>
            <AuditResourceColumn />
          </ResourceProvider>
        }
      />

      {/* resource column routes end */}

      {/* rules routes starts */}
      <Route
        path="/rules-list/:domainId?"
        element={
          <RuleProvider>
            <RulesList />
          </RuleProvider>
        }
      />
      <Route
        path="/rules/add"
        element={
          <RuleResourceProvider>
            <RuleProvider>
              <AddRule />
            </RuleProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/rules/:domainId/edit/:id"
        element={
          <RuleResourceProvider>
            <RuleProvider>
              <EditRule />
            </RuleProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/rules/:domainId/view/:id"
        element={
          <RuleResourceProvider>
            <RuleProvider>
              <ViewRule />
            </RuleProvider>
          </RuleResourceProvider>
        }
      />
      <Route
        path="/rules/:domainId/researchQuery/:id"
        element={
          <RuleResourceProvider>
            <RuleProvider>
              <ResourceProvider>
                <RuleResearchQuery />
              </ResourceProvider>
            </RuleProvider>
          </RuleResourceProvider>
        }
      />

      <Route
        path="/rules/:domainId/rule-execution/:ruleId"
        element={
          <RuleProvider>
            <RuleResourceProvider>
              <ResourceProvider>
                <RuleExecution />
              </ResourceProvider>
            </RuleResourceProvider>
          </RuleProvider>
        }
      />

      <Route
        path="/rules/:domainId/re-run-rule-execution/:ruleId"
        element={
          <RuleProvider>
            <RuleResourceProvider>
              <ResourceProvider>
                <ReRunRuleExecution />
              </ResourceProvider>
            </RuleResourceProvider>
          </RuleProvider>
        }
      />

      <Route
        path="/rules/:domainId/auditRule/:id"
        element={
          <ResourceProvider>
            <AuditRule />
          </ResourceProvider>
        }
      />

      {/* rules routes ends */}

      {/* rule execution history routes start */}
      <Route path="/rules-execution-history" element={<ExecutionHistory />} />
      <Route
        path="/rules-execution-history/:id/:ruleResultId/dashboard"
        element={<ComparisonResultDashboard />}
      />

      <Route
        path="/incident-reporting-rerun"
        element={<IncidentReportingReRun />}
      />
      <Route
        path="/incident-reporting-rerun-report"
        element={<IncidentReportingReRunReport />}
      />

      <Route
        path="/rules-execution-history/consolidate-results"
        element={<ConsolidateResults />}
      />
      {/* rule execution history routes end */}

      {/* validation execution history routes start */}
      <Route
        path="/validation-execution-history/validate-result/:resourceResultId"
        element={<ValidationResult />}
      />
      <Route
        path="/validation-execution-history/validation-execution-results"
        element={<ValidationExecutionResults />}
      />
      <Route
        path="/validation-execution-history"
        element={<ValidationHistory />}
      />
      {/* validation execution history routes end */}

      <Route path="/files-list" element={<FileList />} />

      <Route path="/rule-history" element={<RuleHistory />} />

      <Route path="/dashboard" element={<Home />} />

      <Route path="/linked-services" element={<LinkedServices />} />
      <Route path="/linked-services/add" element={<AddLinkedServices />} />
      <Route
        path="/linked-services/view/:id"
        element={<ViewLinkedServices />}
      />
      <Route path="/linked-services/edit/:id" element={<AddLinkedServices />} />
      <Route path="/connection-keys" element={<ConnectionKeys />} />
      <Route path="/connection-keys/add" element={<AddConnectionKey />} />
      <Route path="/connection-keys/view/:id" element={<ViewConnectionKey />} />
      <Route
        path="/connection-keys/auditConnectionKey/:id"
        element={<AuditConnectionKey />}
      />

      <Route path="/run-instance" element={<RunInstance />} />
      <Route path="/run-instance/add" element={<AddEditRunInstance />} />
      <Route path="/run-instance/view/:id" element={<ViewRunInstance />} />
      <Route path="/run-instance/edit/:id" element={<AddEditRunInstance />} />
      <Route
        path="/file-processing-attributes"
        element={<FileProcessingAttributes />}
      />
      <Route
        path="/file-processing-attributes/add"
        element={<AddEditFileProcessingAttributes />}
      />
      <Route
        path="/file-processing-attributes/view/:id"
        element={<ViewFileProcessingAttributes />}
      />
      <Route
        path="/file-processing-attributes/edit/:id"
        element={<AddEditFileProcessingAttributes />}
      />
      <Route path="/domain/import-entity" element={<ImportJson />} />
      <Route path="/resource/import-entity" element={<ImportJson />} />
      <Route path="/resource-column/import-entity" element={<ImportJson />} />
      <Route path="/rules/import-entity" element={<ImportJson />} />
      <Route
        path="/file-processing-attributes/import-entity"
        element={<ImportJson />}
      />
      <Route path="/connection-keys/import-entity" element={<ImportJson />} />
      <Route path="/linked-services/import-entity" element={<ImportJson />} />
      <Route path="/run-instance/import-entity" element={<ImportJson />} />
      <Route
        path="/linked-services/auditLinkedServices/:id"
        element={<AuditLinkedServices />}
      />
      <Route path="/blob-list" element={<BlobList />} />
      <Route path="/admin" element={<AdminPages />} />

      {/* Mockups */}
      <Route path="/issue-list" element={<AllIssueList />} />
      <Route path="/validations-issue-list" element={<AllIssueList />} />
    </>
  );
};

export default ProtectedRoutes;
