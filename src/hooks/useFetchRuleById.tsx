import { useEffect, useState } from "react";
import { getRuleDetail } from "../services/rulesService";

interface IuseFetchRule {
  setIsLoading: any;
  currentRuleId: any;
}
const useFetchRuleById = ({ setIsLoading, currentRuleId }: IuseFetchRule) => {
  const [ruleData, setRuleData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    if (currentRuleId && currentRuleId > 0) {
      const getRuleData = async () => {
        try {
          const result = await getRuleDetail(currentRuleId);
          setRuleData(result);
        } catch (err) {
          setRuleData([]);
        } finally {
          setIsLoading(false);
        }
      };

      getRuleData();
    }
  }, []);

  return [ruleData];
};

export default useFetchRuleById;
