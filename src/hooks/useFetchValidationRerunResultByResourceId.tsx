import { useEffect, useState, useCallback } from "react";
import { getValidationRerunResultByResourceId } from "../services/resourcesService";
import { validationReRunMockData } from "../services/constants/resource";

interface FetchValidationRerunResultProps {
  resourceId: number | null;
  setIsLoading: (loading: boolean) => void;
  runId?: number | null;
  runName?: string | null;
}

const useFetchValidationRerunResultByResourceId = ({
  resourceId,
  setIsLoading,
  runId,
  runName,
}: FetchValidationRerunResultProps) => {
  const [validationRerunResultData, setValidationRerunResultData] =
    useState<any>(null);

  const fetchRerunResult = useCallback(async () => {
    if (resourceId) {
      setIsLoading(true);
      try {
        //on temp bases mock data is set
        // setValidationRerunResultData(validationReRunMockData);

        const result = await getValidationRerunResultByResourceId(
          resourceId,
          runId,
          runName
        );
        setValidationRerunResultData(result);
      } catch (error) {
        setValidationRerunResultData(null);
      } finally {
        setIsLoading(false);
      }
    }
  }, [resourceId, setIsLoading]);

  useEffect(() => {
    fetchRerunResult();
  }, [fetchRerunResult]);

  return [validationRerunResultData];
};

export default useFetchValidationRerunResultByResourceId;
