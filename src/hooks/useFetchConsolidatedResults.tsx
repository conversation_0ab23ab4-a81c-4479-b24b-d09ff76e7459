import { useEffect, useState } from "react";
import {
  getReportsDetailsByRunInstance,
  getReportsSummaryByRunInstance,
} from "../services/reportsService";
import { IgetReportsByRunInstanceProps } from "../types/reports";
import { formatDate } from "../services/utils";

const useFetchConsolidatedResults = ({
  setIsLoading,
  runName,
  page,
  pSize,
  reportType,
  runDate
}: any) => {
  const [consolidatedReports, setConsolidatedReports] = useState<any>({});
  // const formattedDate =  formatDate(runDate, 'MM-DD-YYYY');
  const formattedDate =  formatDate(runDate, 'YYYY-MM-DD');
  useEffect(() => {
    setIsLoading(true);
    // Summary consolidated reports
    const reportsSummaryByRunInstance = async ({
      runName,
      page,
      pSize,
      formattedDate
    }: IgetReportsByRunInstanceProps) => {
      try {
        const result = await getReportsSummaryByRunInstance({
          runName,
          page,
          pSize,
          formattedDate
        });
        setConsolidatedReports(result);
      } catch (err) {
        setConsolidatedReports([]);
      } finally {
        setIsLoading(false);
      }
    };

    //Detail consolidated reports
    const reportsDetailByRunInstance = async ({
      runName,
      page,
      pSize,
      formattedDate
    }: IgetReportsByRunInstanceProps) => {
      try {
        const result = await getReportsDetailsByRunInstance({
          runName,
          page,
          pSize,
          formattedDate
        });
        setConsolidatedReports(result);
      } catch (err) {
        setConsolidatedReports([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (runName) {
      if (reportType == "detail") {
        reportsDetailByRunInstance({ runName, page, pSize,formattedDate });
      } else {
        reportsSummaryByRunInstance({ runName, page, pSize,formattedDate });
      }
    }else{
      setConsolidatedReports([]);
      setIsLoading(false);
    }
  }, [runName, runDate,reportType, page, pSize]);

  return [consolidatedReports];
};

export default useFetchConsolidatedResults;
