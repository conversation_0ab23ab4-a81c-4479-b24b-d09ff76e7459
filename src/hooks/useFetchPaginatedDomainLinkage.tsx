import { useCallback, useEffect, useState } from "react";
import { getDomainLinkagePaginatedList } from "../services/domainLinkageService";

import DummyData from "../assets/dummy.json";

interface IFetchIssuesProps {
  searchFilterData: any;
  setIsLoading: (loading: boolean) => void;

  page: number;
  pSize: number;
}

const useFetchPaginatedDomainLinkage = ({
  searchFilterData,
  setIsLoading,
  page,
  pSize,
}: IFetchIssuesProps) => {
  const [domainLinkageData, setDomainLinkageData] = useState<any[]>([]);

  const fetchDomainLinkageList = useCallback(async () => {
    if (Object.keys(searchFilterData).length > 0) {
      setIsLoading(true);
      try {
        const { date, ...filteredSearchData } = searchFilterData;
        const result = await getDomainLinkagePaginatedList({
          payload: filteredSearchData,
          page,
          pSize,
        });
        //const result: any = DummyData;
        setDomainLinkageData(result || []);
      } catch (error) {
        setDomainLinkageData([]);
      } finally {
        setIsLoading(false);
      }
    }
  }, [searchFilterData, setIsLoading, page, pSize]);

  useEffect(() => {
    fetchDomainLinkageList();
  }, [fetchDomainLinkageList]);

  return [domainLinkageData];
};

export default useFetchPaginatedDomainLinkage;
