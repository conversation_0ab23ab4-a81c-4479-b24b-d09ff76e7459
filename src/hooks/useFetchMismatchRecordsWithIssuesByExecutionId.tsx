import { useEffect, useState } from "react";
import { getMismatchRecordsWithIssuesByExecutionId } from "../services/rulesService";
interface UseFetchRuleResultProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentRuleResultId: number | null;
  isMismatchFetch: boolean;

  page: number;
  pSize: number;
}
interface RuleDashboardData {
  [key: string]: any;
}
const useFetchMismatchRecordsWithIssuesByExecutionId = ({
  setIsLoading,
  currentRuleResultId,
  isMismatchFetch,
  page,
  pSize,
}: UseFetchRuleResultProps): [RuleDashboardData] => {
  const [mismatchResults, setMismatchResults] = useState<RuleDashboardData>({});
  useEffect(() => {
    const fetchData = async () => {
      if (
        !isMismatchFetch ||
        !currentRuleResultId ||
        currentRuleResultId <= 0
      ) {
        return;
      }
      setIsLoading(true);
      try {
        const result = await getMismatchRecordsWithIssuesByExecutionId({
          currentRuleResultId,
          page,
          pSize,
        });
        setMismatchResults(result);
      } catch (error) {
        console.error("Failed to fetch mismatch rule results:", error);
        setMismatchResults({});
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [currentRuleResultId, isMismatchFetch, page, pSize, setIsLoading]);
  return [mismatchResults];
};
export default useFetchMismatchRecordsWithIssuesByExecutionId;
