import { useEffect, useState } from "react";
import { IResourceData } from "../types/resource";
import { getResourceColumnsListByDomainId } from "../services/resourcesService";

const useFetchResourceColumnsByDomain = ({
  currentDomainId,
  setIsLoading,
}: any) => {
  const [resourcesData, setResourcesData] = useState<IResourceData[]>([]);
  useEffect(() => {
    const getResourceColumnsData = async () => {
      setIsLoading(true);
      try {
        const result: IResourceData[] = await getResourceColumnsListByDomainId({
          currentDomainId,
        });
        const reversedResult = result && result.reverse();
        setResourcesData(reversedResult);
      } catch (err) {
        setResourcesData([]);
      } finally {
        setIsLoading(false);
      }
    };

    currentDomainId && currentDomainId > 0 && getResourceColumnsData();
  }, [currentDomainId]);

  return [resourcesData];
};

export default useFetchResourceColumnsByDomain;
