import { useEffect, useState } from "react";
import { getMissingRecordsWithIssuesByExecutionId } from "../services/rulesService";

interface IuseFetchRuleResult {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentRuleResultId: any;
  isMissingFetch: boolean;
  page: number;
  pSize: number;
}
const useFetchMissingRecordsWithIssuesByExecutionId = ({
  setIsLoading,
  isMissingFetch = false,
  currentRuleResultId,
  page,
  pSize,
}: IuseFetchRuleResult) => {
  const [missingResultsRuleDashboardData, setMissingResultsRuleDashboardData] =
    useState<any>({});
  useEffect(() => {
    const fetchData = async () => {
      if (!isMissingFetch || !currentRuleResultId || currentRuleResultId <= 0) {
        return;
      }
      setIsLoading(true);
      try {
        const result = await getMissingRecordsWithIssuesByExecutionId({
          currentRuleResultId,
          page,
          pSize,
        });
        setMissingResultsRuleDashboardData(result);
      } catch (err) {
        setMissingResultsRuleDashboardData({});
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [currentRuleResultId, isMissingFetch, page, pSize]);
  return [missingResultsRuleDashboardData];
};
export default useFetchMissingRecordsWithIssuesByExecutionId;
