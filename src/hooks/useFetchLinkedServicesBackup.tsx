import { useEffect, useState } from "react";
import { getLinkedServicesBackup } from "../services/linkedService";

const useFetchLinkedServicesBackup = ({
  linkedServicesId,
  setIsLoading,
  page,
  pSize,
}: any) => {
  const [linkedServicesBackupData, setLinkedServicesBackupData] =
    useState<any>();
  useEffect(() => {
    if (linkedServicesId && linkedServicesId > 0) {
      setIsLoading(true);
      const getLinkedServicesBackupData = async () => {
        try {
          const result = await getLinkedServicesBackup({
            linkedServicesId,
            page,
            pSize,
          });
          setLinkedServicesBackupData(result);
        } catch (err) {
          setLinkedServicesBackupData({});
        } finally {
          setIsLoading(false);
        }
      };
      getLinkedServicesBackupData();
    }
  }, [linkedServicesId, page, pSize]);

  return [linkedServicesBackupData];
};

export default useFetchLinkedServicesBackup;
