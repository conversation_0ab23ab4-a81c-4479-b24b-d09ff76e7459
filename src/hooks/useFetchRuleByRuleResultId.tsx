import { useEffect, useState } from "react";
import { getRuleResultByRuleId } from "../services/rulesService";

interface IuseFetchRuleResult {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentRuleResultId: any;
}

const useFetchRuleByRuleResultId = ({
  setIsLoading,
  currentRuleResultId,
}: IuseFetchRuleResult) => {
  const [ruleDashboardData, setRuleDashboardData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    const getRuleResultData = async () => {
      try {
        const result =
          currentRuleResultId > 0 &&
          (await getRuleResultByRuleId({ currentRuleResultId }));
        setRuleDashboardData(result);
      } catch (err) {
        setRuleDashboardData({});
      } finally {
        setIsLoading(false);
      }
    };
    getRuleResultData();
  }, [currentRuleResultId]);

  return [ruleDashboardData];
};

export default useFetchRuleByRuleResultId;
