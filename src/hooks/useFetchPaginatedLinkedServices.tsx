import { useEffect, useState } from "react";
import {
  getLinkedPaginatedServices,
  getLinkedServices,
} from "../services/linkedService";

interface IuseFetchLinkedServices {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: any;
  pSize: any;
}
const useFetchPaginatedLinkedServices = ({
  setIsLoading,
  page,
  pSize,
}: IuseFetchLinkedServices) => {
  const [linkedServicesData, setLinkedServicesData] = useState<any>([]);
  useEffect(() => {
    setIsLoading(true);
    const getLinkedServicesData = async () => {
      try {
        const result = await getLinkedPaginatedServices(page, pSize);
        setLinkedServicesData(result);
      } catch (err) {
        setLinkedServicesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getLinkedServicesData();
  }, [page, pSize]);

  return [linkedServicesData];
};

export default useFetchPaginatedLinkedServices;
