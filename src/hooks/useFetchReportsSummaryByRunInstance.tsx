import { useEffect, useState } from "react";
import { getReportsSummaryByRunInstance } from "../services/reportsService";
import { IgetReportsByRunInstanceProps } from "../types/reports";

const useFetchReportsSummaryByRunInstance = ({
  setIsLoading,
  runName,
  page,
  pSize,
}: any) => {
  const [reportsSummary, setReportsSummary] = useState<any>([]);
  useEffect(() => {
    setIsLoading(true);
    const reportsSummaryByRunInstance = async ({
      runName,
      page,
      pSize,
    }: IgetReportsByRunInstanceProps) => {
      try {
        const result = await getReportsSummaryByRunInstance({
          runName,
          page,
          pSize,
        });
        setReportsSummary(result);
      } catch (err) {
        setReportsSummary([]);
      } finally {
        setIsLoading(false);
      }
    };
    reportsSummaryByRunInstance({ runName, page, pSize });
  }, [runName, page, pSize]);

  return [reportsSummary];
};

export default useFetchReportsSummaryByRunInstance;
