import { useEffect, useState } from "react";
import { getDomainLinkageDetail } from "../services/domainLinkageService";

interface IuseFetchDomainLinkage {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentDomainLinkageId: any;
}

const useFetchDomainLinkageById = ({
  setIsLoading,
  currentDomainLinkageId,
}: IuseFetchDomainLinkage) => {
  const [domainLinkageData, setDomainLinkageData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    const getDomainLinkageData = async () => {
      try {
        const result =
          currentDomainLinkageId > 0 &&
          (await getDomainLinkageDetail({ currentDomainLinkageId }));
        setDomainLinkageData(result);
      } catch (err) {
        setDomainLinkageData({});
      } finally {
        setIsLoading(false);
      }
    };
    getDomainLinkageData();
  }, [currentDomainLinkageId]);

  return [domainLinkageData];
};

export default useFetchDomainLinkageById;
