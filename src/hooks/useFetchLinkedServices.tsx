import { useEffect, useState } from "react";
import { getLinkedServices } from "../services/linkedService";

interface IuseFetchLinkedServices {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  sub_type?: string;
}
const useFetchLinkedServices = ({
  setIsLoading,
  sub_type,
}: IuseFetchLinkedServices) => {
  const [linkedServicesData, setLinkedServicesData] = useState<any[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const getLinkedServicesData = async () => {
      try {
        const result = await getLinkedServices(sub_type);
        const res = result && result.reverse();
        setLinkedServicesData(res);
      } catch (err) {
        setLinkedServicesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getLinkedServicesData();
  }, []);

  return [linkedServicesData];
};

export default useFetchLinkedServices;
