import { useEffect, useState } from "react";
import { IGetRulesListData } from "../types/rules";
import { getPaginatedRulesList } from "../services/rulesService";
import { IPaginatedProps } from "../types";

const useFetchPaginatedRules = ({
  currentDomainId,
  setIsLoading,
  page,
  pSize,
}: IPaginatedProps) => {
  const [rulesList, setRulesList] = useState<IGetRulesListData[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const fetchRulesList = async () => {
      try {
        const result = await getPaginatedRulesList({
          currentDomainId,
          page,
          pSize,
        });

        setRulesList(result);
      } catch (err) {
        setRulesList([]);
      } finally {
        setIsLoading(false);
      }
    };
    fetchRulesList();
  }, [currentDomainId, page, pSize]);

  return [rulesList];
};

export default useFetchPaginatedRules;
