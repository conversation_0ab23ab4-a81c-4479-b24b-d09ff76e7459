import { useEffect, useState } from "react";
import { getFileProcessingDetail } from "../services/fileProcessingService";

interface IuseFetchFileProcessings {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentFileProcessingId: any;
}

const useFetchFileProcessingById = ({
  setIsLoading,
  currentFileProcessingId,
}: IuseFetchFileProcessings) => {
  const [fileProcessingData, setFileProcessingData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    const getFileProcessingsData = async () => {
      try {
        const result =
          currentFileProcessingId > 0 &&
          (await getFileProcessingDetail({ currentFileProcessingId }));
        setFileProcessingData(result);
      } catch (err) {
        setFileProcessingData({});
      } finally {
        setIsLoading(false);
      }
    };
    getFileProcessingsData();
  }, [currentFileProcessingId]);

  return [fileProcessingData];
};

export default useFetchFileProcessingById;
