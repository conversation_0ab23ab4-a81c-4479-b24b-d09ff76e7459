import { useEffect, useState, useCallback } from "react";
import { IGetIssuesListData } from "../types/rules";
import {
  getPaginatedIssuesDetail,
  getPaginatedValidationIssuesDetail,
} from "../services/IssueManagementService";

interface FetchIssuesProps {
  searchFilterData: any;
  setIsLoading: (loading: boolean) => void;
  currentPageType: string;
  page: number;
  pSize: number;
}

const useFetchAllPaginatedIssues = ({
  searchFilterData,
  setIsLoading,
  currentPageType,
  page,
  pSize,
}: FetchIssuesProps) => {
  const [issuesList, setIssuesList] = useState<any[]>([]);

  const fetchIssuesList = useCallback(async () => {
    if (Object.keys(searchFilterData).length > 0) {
      setIsLoading(true);
      try {
        const { date, ...filteredSearchData } = searchFilterData;
        const result = await (currentPageType === "execution"
          ? getPaginatedIssuesDetail({
              payload: filteredSearchData,
              page,
              pSize,
            })
          : getPaginatedValidationIssuesDetail({
              payload: filteredSearchData,
              page,
              pSize,
            }));

        setIssuesList(result || []);
      } catch (error) {
        setIssuesList([]);
      } finally {
        setIsLoading(false);
      }
    }
  }, [searchFilterData, setIsLoading, currentPageType, page, pSize]);

  useEffect(() => {
    fetchIssuesList();
  }, [fetchIssuesList]);

  return issuesList;
};

export default useFetchAllPaginatedIssues;
