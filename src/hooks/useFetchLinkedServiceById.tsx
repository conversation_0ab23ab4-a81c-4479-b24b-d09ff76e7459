import { useEffect, useState } from "react";
import { getLinkedServiceDetail } from "../services/linkedService";

interface IuseFetchLinkedServices {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentLinkedServiceId: any;
}

const useFetchLinkedServiceById = ({
  setIsLoading,
  currentLinkedServiceId,
}: IuseFetchLinkedServices) => {
  const [linkedServiceData, setLinkedServiceData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    const getLinkedServicesData = async () => {
      try {
        
        const result = currentLinkedServiceId > 0 && await getLinkedServiceDetail({ currentLinkedServiceId });
        setLinkedServiceData(result);
      } catch (err) {
        setLinkedServiceData({});
      } finally {
        setIsLoading(false);
      }
    };
    getLinkedServicesData();
  }, [currentLinkedServiceId]);

  return [linkedServiceData];
};

export default useFetchLinkedServiceById;
