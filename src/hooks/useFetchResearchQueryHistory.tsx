import { useEffect, useState } from "react";
import { getExecutionHistory } from "../services/executionHistoryService";
import { getResearchQueryHistory } from "../services/ResearchQueriesService";

interface ExecutionHistoryProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
  domainId?: number | null;
  ruleId?: number | null;
  fromDate?: string | undefined;
  searchFilterData: any;
}
const useFetchResearchQueryHistory = ({
  setIsLoading,
  page,
  pSize,
  domainId,
  ruleId,
  fromDate,
  searchFilterData,
}: ExecutionHistoryProps) => {
  const [researchQueryHistory, setResearchQueryHistoryList] = useState<any>([]);

  useEffect(() => {
    const getExecutionHistoryList = async () => {
      try {
        setIsLoading(true);
        const result = await getResearchQueryHistory({
          page,
          pSize,
          domainId,
          ruleId,
          fromDate,
          searchFilterData,
        });
        setResearchQueryHistoryList(result);
      } catch (error) {
        // Handle any potential errors here
        console.error("Error fetching execution history:", error);
      } finally {
        setIsLoading(false);
      }
    };

    getExecutionHistoryList();
  }, [setIsLoading, page, pSize, domainId, ruleId, fromDate, searchFilterData]);

  return [researchQueryHistory];
};

export default useFetchResearchQueryHistory;
