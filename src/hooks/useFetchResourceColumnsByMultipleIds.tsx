import { useEffect, useState } from "react";
import { getResourceColumnsByMultipleIds } from "../services/resourcesService";

interface IuseFetchResource {
  resourceColIds: any[];
  setIsLoading: any;
}
const useFetchResourceColumnsByMultipleIds = ({
  resourceColIds,
  setIsLoading,
}: IuseFetchResource) => {
  const [resourceColumnsData, setResourceColumnsData] = useState<any>();
  useEffect(() => {
    const getResourceData = async (resourceColIds: any[]) => {
      try {
        setIsLoading(true);
        const result = await getResourceColumnsByMultipleIds(resourceColIds);
        setResourceColumnsData(result);
      } catch (err) {
        setResourceColumnsData([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (resourceColIds && resourceColIds.length > 0) {
      getResourceData(resourceColIds);
    }
  }, [resourceColIds]);

  return [resourceColumnsData];
};

export default useFetchResourceColumnsByMultipleIds;
