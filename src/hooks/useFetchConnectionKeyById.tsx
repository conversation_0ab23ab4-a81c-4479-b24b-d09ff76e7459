import { useEffect, useState } from "react";
import { getConnectionKeyDetail } from "../services/connectionKeysService";

interface IuseFetchLinkedServices {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentConnectionKeyId: any;
}

const useFetchConnectionKeyById = ({
  setIsLoading,
  currentConnectionKeyId,
}: IuseFetchLinkedServices) => {
  const [linkedServiceData, setLinkedServiceData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    const getLinkedServicesData = async () => {
      try {
        
        const result = currentConnectionKeyId > 0 && await getConnectionKeyDetail({ currentConnectionKeyId });
        setLinkedServiceData(result);
      } catch (err) {
        setLinkedServiceData({});
      } finally {
        setIsLoading(false);
      }
    };
    getLinkedServicesData();
  }, [currentConnectionKeyId]);

  return [linkedServiceData];
};

export default useFetchConnectionKeyById;
