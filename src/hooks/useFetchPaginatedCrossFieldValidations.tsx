import { useEffect, useState } from "react";
import { getPaginatedValidationCrossFieldsList } from "../services/resourcesService";
import { crossFieldValidationsMockData } from "../services/constants/resource";

interface IuseFetchPaginatedCrossFieldValidations {
  currentValidationResultId: any;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
  isDetailedExecutionDataAvailable: boolean;
}

const useFetchPaginatedCrossFieldValidations = ({
  currentValidationResultId,
  setIsLoading,
  page,
  pSize,
  isDetailedExecutionDataAvailable,
}: IuseFetchPaginatedCrossFieldValidations) => {
  const [crossFieldValidation, setCrossFieldValidation] = useState<any>([]);
  useEffect(() => {
    if (!isDetailedExecutionDataAvailable) setIsLoading(true);
    const fetchValidationData = async () => {
      try {
        const result = await getPaginatedValidationCrossFieldsList({
          currentValidationResultId,
          page,
          pSize,
        });

        //Set Mock Data
        // const result: any = crossFieldValidationsMockData;
        setCrossFieldValidation(result);
      } catch (err) {
        setCrossFieldValidation([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (!isDetailedExecutionDataAvailable) fetchValidationData();
  }, [
    currentValidationResultId,
    page,
    pSize,
    isDetailedExecutionDataAvailable,
  ]);

  return [crossFieldValidation];
};

export default useFetchPaginatedCrossFieldValidations;
