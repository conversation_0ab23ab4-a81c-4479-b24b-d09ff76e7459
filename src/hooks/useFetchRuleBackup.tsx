import { useEffect, useState } from "react";
import { getRuleBackup } from "../services/rulesService";

const useFetchRuleBackup = ({ ruleId, setIsLoading, page, pSize }: any) => {
  const [ruleBackupData, setRuleBackupData] = useState<any>();
  useEffect(() => {
    if (ruleId && ruleId > 0) {
      setIsLoading(true);
      const getRuleBackupData = async () => {
        try {
          const result = await getRuleBackup({
            ruleId,
            page,
            pSize,
          });
          setRuleBackupData(result);
        } catch (err) {
          setRuleBackupData({});
        } finally {
          setIsLoading(false);
        }
      };
      getRuleBackupData();
    }
  }, [ruleId, page, pSize]);

  return [ruleBackupData];
};

export default useFetchRuleBackup;
