import { useEffect, useState } from "react";

import { getUsersList } from "../services/userService";

interface IuseFetchUsers {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}
const useFetchUsersList = ({ setIsLoading }: IuseFetchUsers) => {
  const [usersData, setUsersData] = useState<any>({});
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const result = await getUsersList();
        setUsersData(result);
      } catch (err) {
        setUsersData({});
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);

  return [usersData];
};
export default useFetchUsersList;
