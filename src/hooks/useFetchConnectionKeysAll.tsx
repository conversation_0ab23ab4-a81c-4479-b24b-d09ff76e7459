import { useEffect, useState } from "react";
import { getConnectionKeys } from "../services/connectionKeysService";

const useFetchConnectionKeysAll = ({ setIsLoading }: any) => {
  const [connectionKeysData, setConnectionKeysData] = useState<any[]>([]);
  useEffect(() => {
    const getConnectionKeysData = async () => {
      setIsLoading(true);
      try {
        const result = await getConnectionKeys();
        const res = result && result.reverse();
        setConnectionKeysData(res);
      } catch (err) {
        setConnectionKeysData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getConnectionKeysData();
  }, []);

  return [connectionKeysData];
};

export default useFetchConnectionKeysAll;
