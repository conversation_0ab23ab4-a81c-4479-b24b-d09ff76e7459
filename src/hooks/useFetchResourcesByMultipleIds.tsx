import { useEffect, useState } from "react";
import { getResourcesByMultipleIds } from "../services/resourcesService";

interface IuseFetchResource {
  resourceIds: any[];
  setIsLoading: any;
}
const useFetchResourcesByIdMultipleIds = ({
  resourceIds,
  setIsLoading,
}: IuseFetchResource) => {
  const [resourceData, setResourceData] = useState<any>();
  useEffect(() => {
    const getResourceData = async (resourceIds: any[]) => {
      try {
        setIsLoading(true);
        const result = await getResourcesByMultipleIds(resourceIds);
        setResourceData(result);
      } catch (err) {
        setResourceData([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (resourceIds && resourceIds.length > 0) {
      getResourceData(resourceIds);
    }
  }, [resourceIds]);

  return [resourceData];
};

export default useFetchResourcesByIdMultipleIds;
