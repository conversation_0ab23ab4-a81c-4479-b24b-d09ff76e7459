import { useEffect, useState, useCallback } from "react";
import { IGetIssuesListData } from "../types/rules";
import {
  getIssuesDetail,
  getValidationIssuesDetail,
} from "../services/IssueManagementService";

interface FetchIssuesProps {
  searchFilterData: any;
  setIsLoading: (loading: boolean) => void;
  currentPageType: string;
}

const useFetchIssuesByExecutionId = ({
  searchFilterData,
  setIsLoading,
  currentPageType,
}: FetchIssuesProps) => {
  const [issuesList, setIssuesList] = useState<any[]>([]);

  const fetchIssuesList = useCallback(async () => {
    if (Object.keys(searchFilterData).length > 0) {
      setIsLoading(true);
      try {
        const result = await (currentPageType === "execution"
          ? getIssuesDetail(searchFilterData)
          : getValidationIssuesDetail(searchFilterData));
        setIssuesList(result || []);
      } catch (error) {
        setIssuesList([]);
      } finally {
        setIsLoading(false);
      }
    }
  }, [searchFilterData, setIsLoading, currentPageType]);

  useEffect(() => {
    fetchIssuesList();
  }, [fetchIssuesList]);

  return issuesList;
};

export default useFetchIssuesByExecutionId;
