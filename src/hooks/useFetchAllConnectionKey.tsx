import { useEffect, useState } from "react";
import {
  getAllConnectionKeys,
  getAllConnectionKeysWithoutId,
} from "../services/connectionKeysService";

const useFetchAllConnectionKey = ({ connectionKeyIds, setIsLoading }: any) => {
  const [connectionKeysData, setConnectionKeysData] = useState<any[]>([]);
  useEffect(() => {
    const getConnectionKeysData = async () => {
      setIsLoading(true);
      try {
        let result;
        if (connectionKeyIds?.length > 0) {
          result = await getAllConnectionKeys(connectionKeyIds);
        } else {
          result = await getAllConnectionKeysWithoutId();
        }
        setConnectionKeysData(result);
      } catch (err) {
        setConnectionKeysData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getConnectionKeysData();
  }, [connectionKeyIds, setIsLoading]);

  return [connectionKeysData];
};

export default useFetchAllConnectionKey;
