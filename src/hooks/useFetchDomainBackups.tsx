import { useEffect, useState } from "react";
import { getDomainBackup } from "../services/domainsService";

const useFetchDomainBackups = ({
  domainId,
  setIsLoading,
  page,
  pSize,
}: any) => {
  const [domainsBackupData, setDomainsBackupData] = useState<any>();
  useEffect(() => {
    if (domainId && domainId > 0) {
      setIsLoading(true);
      const getDomainBackupData = async () => {
        try {
          const result = await getDomainBackup({
            domainId,
            page,
            pSize,
          });
          setDomainsBackupData(result);
        } catch (err) {
          setDomainsBackupData({});
        } finally {
          setIsLoading(false);
        }
      };
      getDomainBackupData();
    }
  }, [domainId]);

  return [domainsBackupData, page, pSize];
};

export default useFetchDomainBackups;
