import { useEffect, useState } from "react";
import { IGetIssueHistoryData } from "../types/rules";
import { getIssueHistory } from "../services/IssueManagementService";

const useFetchIssueHistory = ({
  currentIssueId,
  setIsLoading,
  issueFrom,
}: any) => {
  const [issueHistory, setIssueHistory] = useState<IGetIssueHistoryData[]>([]);
  useEffect(() => {
    if (currentIssueId && currentIssueId > 0) {
      const fetchIssuesList = async () => {
        setIsLoading(true);
        try {
          const result = await getIssueHistory(currentIssueId, issueFrom);
          setIssueHistory(result);
        } catch (err) {
          setIssueHistory([]);
        } finally {
          setIsLoading(false);
        }
      };
      fetchIssuesList();
    }
  }, [currentIssueId, issueFrom]);

  return [issueHistory];
};

export default useFetchIssueHistory;
