import { useEffect, useCallback } from "react";
import { useNotifications } from "../contexts/NotificationContext";

const useSyncNotifications = (setFileData: any, fileData: any) => {
  const { notifications } = useNotifications();
  // Wrap the function in useCallback to prevent unnecessary re-renders
  const handleStorageChange = useCallback(() => {
    if (!fileData.items?.length) return;

    try {
      setFileData((prevFileData: any) => {
        const updatedItems = prevFileData.items.map((item: any) => {
          const matchingNotification = notifications.find((notification: any) =>
            notification?.execution_type === "validation" ||
            notification?.execution_type === "validation_rerun"
              ? notification.execution_id === item?.resourceResultId
              : notification.execution_id === item?.id
          );

          return matchingNotification?.execution_status
            ? { ...item, job_status: matchingNotification.execution_status }
            : item;
        });

        return JSON.stringify(updatedItems) !==
          JSON.stringify(prevFileData.items)
          ? { ...prevFileData, items: updatedItems }
          : prevFileData;
      });
    } catch (error) {
      console.error("Error processing notifications:", error);
    }
  }, [fileData.items, setFileData, notifications]);

  // Run the handler whenever notifications change
  useEffect(() => {
    handleStorageChange();
  }, [notifications, handleStorageChange]);

  return { handleStorageChange }; // Ensure it's returned properly
};

export default useSyncNotifications;
