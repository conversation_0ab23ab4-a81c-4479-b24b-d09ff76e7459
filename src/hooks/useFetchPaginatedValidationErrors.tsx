import { useEffect, useState } from "react";
import { getPaginatedValidationErrorsList } from "../services/resourcesService";
import { validationErrorsMockData } from "../services/constants/resource";

interface IuseFetchPaginatedValidationErrors {
  currentValidationResultId: any;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
  isDetailedExecutionDataAvailable: boolean;
  selectedColumnName: string;
}

const useFetchPaginatedValidationErrors = ({
  currentValidationResultId,
  setIsLoading,
  page,
  pSize,
  isDetailedExecutionDataAvailable,
  selectedColumnName,
}: IuseFetchPaginatedValidationErrors) => {
  const [validationErrors, setValidationErrors] = useState<any>([]);
  useEffect(() => {
    const fetchValidationErrors = async () => {
      setIsLoading(true);

      try {
        const result = await getPaginatedValidationErrorsList({
          currentValidationResultId,
          selectedColumnName,
          page,
          pSize,
        });

        //Set Mock Data
        // const result: any = validationErrorsMockData;

        setValidationErrors(result);
      } catch (err) {
        setValidationErrors([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (!isDetailedExecutionDataAvailable && selectedColumnName !== "")
      fetchValidationErrors();
  }, [
    currentValidationResultId,
    page,
    pSize,
    isDetailedExecutionDataAvailable,
    selectedColumnName,
  ]);

  return [validationErrors];
};

export default useFetchPaginatedValidationErrors;
