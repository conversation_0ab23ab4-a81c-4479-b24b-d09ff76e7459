import { useEffect, useState } from "react";
import { getAllLinkedServices, getLinkedServices } from "../services/linkedService";

interface IuseFetchLinkedServices {
  linkedServiceIds:any;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}
const useFetchAllLinkedServices = ({ linkedServiceIds,setIsLoading }: IuseFetchLinkedServices) => {
  const [linkedServicesData, setLinkedServicesData] = useState<any>([]);
  useEffect(() => {
    setIsLoading(true);
    const getAllLinkedServicesData = async () => {
      try {
        const result = await getAllLinkedServices(linkedServiceIds);
        setLinkedServicesData(result);
      } catch (err) {
        setLinkedServicesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    if(linkedServiceIds.length>0){
        getAllLinkedServicesData();
    }
  }, [linkedServiceIds]);

  return [linkedServicesData];
};

export default useFetchAllLinkedServices;
