import { useEffect, useState } from "react";
import { getDomainDetail } from "../services/domainsService";

interface IuseFetchDomains {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentDomainId: any;
}

const useFetchDomainById = ({
  setIsLoading,
  currentDomainId,
}: IuseFetchDomains) => {
  const [domainData, setDomainData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    const getDomainsData = async () => {
      try {
        const result =
          currentDomainId > 0 && (await getDomainDetail({ currentDomainId }));
        setDomainData(result);
      } catch (err) {
        setDomainData({});
      } finally {
        setIsLoading(false);
      }
    };
    getDomainsData();
  }, [currentDomainId]);

  return [domainData, setDomainData];
};

export default useFetchDomainById;
