import { useEffect, useState } from "react";
import { getGenericResearchQueryDetail } from "../services/ResearchQueriesService";

interface IuseFetchResearchQuery {
  setIsLoading: any;
  currentRQId: any;
}
const useFetchGenericResearchQueryById = ({
  setIsLoading,
  currentRQId,
}: IuseFetchResearchQuery) => {
  const [genericResearchQueryDetail, setGenericResearchQueryDetail] =
    useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    if (currentRQId && currentRQId > 0) {
      const getResearchQueryData = async () => {
        try {
          const result = await getGenericResearchQueryDetail(currentRQId);
          setGenericResearchQueryDetail(result);
        } catch (err) {
          setGenericResearchQueryDetail({});
        } finally {
          setIsLoading(false);
        }
      };

      getResearchQueryData();
    } 
  }, [currentRQId, setIsLoading]);

  return [genericResearchQueryDetail];
};

export default useFetchGenericResearchQueryById;
