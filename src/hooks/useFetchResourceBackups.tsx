import { useEffect, useState } from "react";
import { IResourceDetail } from "../types/resource";
import { getResourceBackup } from "../services/resourcesService";

const useFetchResourceBackups = ({
  resourceId,
  setIsLoading,
  page,
  pSize,
}: any) => {
  const [resourceBackupData, setResourceBackupData] = useState<any>();
  useEffect(() => {
    if (resourceId && resourceId > 0) {
      setIsLoading(true);
      const getResourceBackupData = async () => {
        try {
          const ResourceResult: IResourceDetail = await getResourceBackup({
            resourceId,
            page,
            pSize,
          });
          setResourceBackupData(ResourceResult);
        } catch (err) {
          setResourceBackupData({});
        } finally {
          setIsLoading(false);
        }
      };
      getResourceBackupData();
    }
  }, [resourceId, page, pSize]);

  return [resourceBackupData];
};

export default useFetchResourceBackups;
