import { useEffect, useState } from "react";
import { IResourceDetail } from "../types/resource";
import {
  getResourceColumnsDetail,
  getResourceDetail,
} from "../services/resourcesService";

const useFetchResourceById = ({
  currentResourceId,
  setIsLoading,
  fetchKey,
}: any) => {
  const [resourceData, setResourceData] = useState<any>();
  const [resourceColumnData, setResourceColumnData] = useState<any>();
  useEffect(() => {
    if (currentResourceId && currentResourceId > 0) {
      setIsLoading(true);
      const getResourcesData = async () => {
        const ResourceResult: IResourceDetail = await getResourceDetail({
          currentResourceId,
        });
        setResourceData(ResourceResult);
        return ResourceResult;
      };
      const getResourceColumnsData = async (resourceColumnDetailId: number) => {
        const result: any = await getResourceColumnsDetail({
          resourceColumnDetailId,
        });
        return result;
      };
      getResourcesData()
        .then((res) => {
          getResourceColumnsData(
            res?.additional_properties?.resource_column_details_id
          ).then((columDetail) => {
            setResourceColumnData(columDetail);
          });
        })
        .catch((e) => {
          console.log(e);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [currentResourceId, fetchKey]);

  return [resourceData, resourceColumnData];
};

export default useFetchResourceById;
