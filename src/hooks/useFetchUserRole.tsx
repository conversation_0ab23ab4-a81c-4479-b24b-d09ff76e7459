import { useEffect, useState } from "react";
import { getUserRole } from "../services/userService";

interface IuseFetchUserRole {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}
const useFetchUserRole = ({ setIsLoading }: IuseFetchUserRole) => {
  const [userRoleData, setUserRoleData] = useState<any[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const getUserRoleData = async () => {
      try {
        const res = await getUserRole();
        setUserRoleData(res);
      } catch (err) {
        setUserRoleData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getUserRoleData();
  }, []);

  return [userRoleData];
};

export default useFetchUserRole;
