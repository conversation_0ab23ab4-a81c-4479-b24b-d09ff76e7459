import { useEffect, useState } from "react";
import { IResourceData } from "../types/resource";
import { getResourceColumnsPaginatedList } from "../services/resourcesService";
import { IPaginatedProps } from "../types";

const useFetchPaginatedResourcesColumns = ({
  currentDomainId,
  setIsLoading,
  page,
  pSize,
}: IPaginatedProps) => {
  const [resourcesData, setResourcesData] = useState<IResourceData[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const getResourcesData = async () => {
      try {
        const result: IResourceData[] = await getResourceColumnsPaginatedList({currentDomainId,
          page,
          pSize,
        });
        setResourcesData(result);
      } catch (err) {
        setResourcesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getResourcesData();
  }, [currentDomainId, page, pSize]);

  return [resourcesData];
};

export default useFetchPaginatedResourcesColumns;
