import { useEffect, useState } from "react";
import { getPaginatedValidationNullKeysList } from "../services/resourcesService";
import { nullKeysValidationsMockData } from "../services/constants/resource";

interface IuseFetchPaginatedNullKeysValidations {
  currentValidationResultId: any;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
  isDetailedExecutionDataAvailable: boolean;
}

const useFetchPaginatedNullKeysValidations = ({
  currentValidationResultId,
  setIsLoading,
  page,
  pSize,
  isDetailedExecutionDataAvailable,
}: IuseFetchPaginatedNullKeysValidations) => {
  const [nullKeysValidation, setNullKeysValidation] = useState<any>([]);
  useEffect(() => {
    const fetchValidationData = async () => {
      setIsLoading(true);

      try {
        const result = await getPaginatedValidationNullKeysList({
          currentValidationResultId,
          page,
          pSize,
        });

        //Set Mock Data
        // const result: any = nullKeysValidationsMockData;

        setNullKeysValidation(result);
      } catch (err) {
        setNullKeysValidation([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (!isDetailedExecutionDataAvailable) fetchValidationData();
  }, [
    currentValidationResultId,
    page,
    pSize,
    isDetailedExecutionDataAvailable,
  ]);

  return [nullKeysValidation];
};

export default useFetchPaginatedNullKeysValidations;
