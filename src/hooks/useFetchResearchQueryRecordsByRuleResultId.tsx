import { useEffect, useState } from "react";
import { getResearchQueryRuleResultByRuleResultId } from "../services/rulesService";

interface UseFetchResearchQueryProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentRuleResultId: number | null;
  isResearchQueryFetch: boolean;
  pageN: number;
  pageS: number;
  queryName: string;
}

interface ResearchQueryResults {
  [key: string]: any;
}

const useFetchResearchQueryRecordsByRuleResultId = ({
  setIsLoading,
  currentRuleResultId,
  isResearchQueryFetch,
  pageN,
  pageS,
  queryName,
}: UseFetchResearchQueryProps): [
  ResearchQueryResults,
  React.Dispatch<React.SetStateAction<ResearchQueryResults>>
] => {
  const [researchQueryResults, setResearchQueryResults] =
    useState<ResearchQueryResults>({});

  useEffect(() => {
    const fetchData = async () => {
      if (
        !isResearchQueryFetch ||
        !currentRuleResultId ||
        currentRuleResultId <= 0
      ) {
        return;
      }

      setIsLoading(true);

      try {
        const result = await getResearchQueryRuleResultByRuleResultId({
          currentRuleResultId,
          pageN,
          pageS,
          queryName,
        });
        setResearchQueryResults(result);
      } catch (error) {
        console.error("Error fetching research query records:", error);
        setResearchQueryResults({});
      } finally {
        setIsLoading(false);
      }
    };

    queryName !== "" && fetchData();
  }, [
    currentRuleResultId,
    isResearchQueryFetch,
    setIsLoading,
    queryName,
    pageN,
    pageS,
  ]);

  return [researchQueryResults, setResearchQueryResults];
};

export default useFetchResearchQueryRecordsByRuleResultId;
