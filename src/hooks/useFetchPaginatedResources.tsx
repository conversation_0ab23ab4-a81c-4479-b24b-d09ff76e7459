import { useEffect, useState } from "react";
import { IResourceData } from "../types/resource";
import { getResourcePaginatedList } from "../services/resourcesService";
import { IPaginatedProps } from "../types";

const useFetchPaginatedResources = ({
  currentDomainId,
  setIsLoading,
  page,
  pSize,
}: IPaginatedProps) => {
  const [resourcesData, setResourcesData] = useState<IResourceData[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const getResourcesData = async () => {
      try {
        const result: IResourceData[] = await getResourcePaginatedList({currentDomainId,
          page,
          pSize,
        });
        setResourcesData(result);
      } catch (err) {
        setResourcesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getResourcesData();
  }, [currentDomainId, page, pSize]);

  return [resourcesData];
};

export default useFetchPaginatedResources;
