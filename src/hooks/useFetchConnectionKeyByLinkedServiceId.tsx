import { useEffect, useState } from "react";
import { getConnectionKeysByLinkedService } from "../services/connectionKeysService";

const useFetchConnectionKeyByLinkedServiceId = ({
  selectLinkedServiceId,
  setIsLoading,
}: any) => {
  const [connectionKeysData, setConnectionKeysData] = useState<any[]>([]);
  useEffect(() => {
    const getConnectionKeysData = async () => {
      setIsLoading(true);
      try {
        const result = await getConnectionKeysByLinkedService(
          selectLinkedServiceId
        );
        setConnectionKeysData(result?.connection_key_details);
      } catch (err) {
        setConnectionKeysData([]);
      } finally {
        setIsLoading(false);
      }
    };
    selectLinkedServiceId &&
      selectLinkedServiceId > 0 &&
      getConnectionKeysData();
  }, [selectLinkedServiceId]);

  return [connectionKeysData];
};

export default useFetchConnectionKeyByLinkedServiceId;
