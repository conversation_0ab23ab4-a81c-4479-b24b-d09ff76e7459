import { useEffect, useState } from "react";
import { getAllPaginatedConnectionKeys } from "../services/connectionKeysService";
interface IuseFetchConnectionKeys {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: any;
  pSize: any;
}
const useFetchPaginatedConnectionKeys = ({
  setIsLoading,
  page,
  pSize,
}: IuseFetchConnectionKeys) => {
  const [connectionKeysData, setConnectionKeysData] = useState<any>([]);
  useEffect(() => {
    setIsLoading(true);
    const getConnectionKeysData = async () => {
      try {
        const result = await getAllPaginatedConnectionKeys(page, pSize);
        setConnectionKeysData(result);
      } catch (err) {
        setConnectionKeysData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getConnectionKeysData();
  }, [page, pSize]);

  return [connectionKeysData];
};
export default useFetchPaginatedConnectionKeys;
