import { useEffect, useState } from "react";
import { getConnectionKeys } from "../services/connectionKeysService";

interface FetchConnectionKeyParams {
  filterKeys?: any;
  setIsLoading: (loading: boolean) => void;
  type?: string;
}
const useFetchConnectionKey = ({
  filterKeys,
  setIsLoading,
  type,
}: FetchConnectionKeyParams) => {
  const [connectionKeysData, setConnectionKeysData] = useState<any[]>([]);
  useEffect(() => {
    const getConnectionKeysData = async () => {
      setIsLoading(true);
      try {
        if (type) {
          const result = await getConnectionKeys(type);
          setConnectionKeysData(result);
        } else {
          const result = await getConnectionKeys();
          const filteredResult = result?.filter((item: any) =>
            filterKeys.includes(item.id)
          );
          setConnectionKeysData(filteredResult);
        }
      } catch (err) {
        setConnectionKeysData([]);
      } finally {
        setIsLoading(false);
      }
    };
    (filterKeys?.length > 0 || type) && getConnectionKeysData();
  }, [filterKeys, type]);

  return [connectionKeysData];
};

export default useFetchConnectionKey;
