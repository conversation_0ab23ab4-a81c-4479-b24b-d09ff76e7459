import { useEffect, useState } from "react";
import { getDomains } from "../services/domainsService";

interface IuseFetchDomains {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}
const useFetchDomains = ({ setIsLoading }: IuseFetchDomains) => {
  const [domainsData, setDomainsData] = useState<any[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const getDomainsData = async () => {
      try {
        const result = await getDomains();
        const res = result && result?.reverse();
        setDomainsData(res);
      } catch (err) {
        setDomainsData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getDomainsData();
  }, []);

  return [domainsData];
};

export default useFetchDomains;
