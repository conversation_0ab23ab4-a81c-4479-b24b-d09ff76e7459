import { useEffect, useState } from "react";
import { IResourceData } from "../types/resource";
import {
  getAllResourceList,
  getResourceList,
} from "../services/resourcesService";

const useFetchAllResources = ({ setIsLoading, currentDomainId }: any) => {
  const [resourcesData, setResourcesData] = useState<IResourceData[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const getResourcesData = async () => {
      try {
        let result: IResourceData[] = [];
        if (currentDomainId && currentDomainId > 0) {
          result = await getResourceList({
            currentDomainId,
          });
        } else {
          result = await getAllResourceList();
        }
        const reversedResult = result && result.reverse();
        setResourcesData(reversedResult);
      } catch (err) {
        setResourcesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getResourcesData();
  }, []);

  return [resourcesData];
};

export default useFetchAllResources;
