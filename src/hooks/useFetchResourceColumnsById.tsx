import { useEffect, useState } from "react";
import { getResourceColumnsDetail } from "../services/resourcesService";

const useFetchResourceColumnsById = ({
  resourceColumnId,
  setIsLoading,
}: any) => {
  const [resourceColumnData, setResourceColumnData] = useState<any>();
  useEffect(() => {
    const getResourceColumnsData = async () => {
      try {
        setIsLoading(true);
        const result = await getResourceColumnsDetail({
          resourceColumnDetailId: resourceColumnId,
        });
        setResourceColumnData(result);
      } catch (err) {
        setResourceColumnData([]);
      } finally {
        setIsLoading(false);
      }
    };

    resourceColumnId && resourceColumnId > 0 && getResourceColumnsData();
  }, [resourceColumnId]);

  return [resourceColumnData, setResourceColumnData];
};

export default useFetchResourceColumnsById;
