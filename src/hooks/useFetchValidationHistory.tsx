import React, { useEffect, useState } from "react";
import { getValidationHistory } from "../services/reportsService";
interface ValidationHistoryProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: number | null;
  pSize: number | null;
  domainId?: number | null;
  resourceId?: number | null;
  fromDate?: string | undefined;
  searchFilterData?: any;
}

const useFetchValidationHistory = ({
  setIsLoading,
  page,
  pSize,
  resourceId,
  domainId,
  fromDate,
  searchFilterData,
}: ValidationHistoryProps) => {
  const [validationHistoryReports, setValidationHistoryReports] = useState<any>(
    {}
  );
  useEffect(() => {
    setIsLoading(true);
    // Validation execution reports
    const validationHistory = async ({
      page,
      pSize,
      resourceId,
      domainId,
      fromDate,
      searchFilterData,
    }: any) => {
      try {
        const result = await getValidationHistory({
          page,
          pSize,
          resourceId,
          domainId,
          fromDate,
          searchFilterData,
        });
        setValidationHistoryReports(result);
      } catch (err) {
        setValidationHistoryReports([]);
      } finally {
        setIsLoading(false);
      }
    };
    validationHistory({
      page,
      pSize,
      resourceId,
      domainId,
      fromDate,
      searchFilterData,
    });
  }, [page, pSize, searchFilterData]);

  return [validationHistoryReports];
};

export default useFetchValidationHistory;
