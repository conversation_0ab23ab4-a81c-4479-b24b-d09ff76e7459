import { useEffect, useState } from "react";
import { IPaginatedProps } from "../types";
import { getDomainPaginatedList } from "../services/domainsService";

const useFetchPaginatedDomain = ({
  setIsLoading,
  page,
  pSize,
}: IPaginatedProps) => {
  const [domainData, setDomainData] = useState<any[]>([]);

  useEffect(() => {
    setIsLoading(true);
    const getDomainData = async () => {
      try {
        const result: any[] = await getDomainPaginatedList({
          page,
          pSize,
        });
        setDomainData(result);
      } catch (err) {
        setDomainData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getDomainData();
  }, [page, pSize]);

  return [domainData];
};

export default useFetchPaginatedDomain;
