import { useEffect, useState } from "react";

import { getResourceResultByResourceId } from "../services/resourcesService";

interface IuseFetchResourceResult {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentResourceResultId: any;
}

const useFetchResourceByResourceResultId = ({
  setIsLoading,
  currentResourceResultId,
}: IuseFetchResourceResult) => {
  const [resourceDashboardData, setResourceDashboardData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    const getResourceResultData = async () => {
      try {
        const result =
          currentResourceResultId > 0 &&
          (await getResourceResultByResourceId({ currentResourceResultId }));
        setResourceDashboardData(result);
      } catch (err) {
        setResourceDashboardData({});
      } finally {
        setIsLoading(false);
      }
    };
    getResourceResultData();
  }, [currentResourceResultId]);

  return [resourceDashboardData];
};

export default useFetchResourceByResourceResultId;
