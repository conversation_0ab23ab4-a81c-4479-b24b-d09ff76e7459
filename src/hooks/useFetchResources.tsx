import { useEffect, useState } from "react";
import { IResourceData } from "../types/resource";
import { getResourceList } from "../services/resourcesService";

const useFetchResources = ({ currentDomainId, setIsLoading }: any) => {
  const [resourcesData, setResourcesData] = useState<IResourceData[] | undefined>([]);
  useEffect(() => {
    if (currentDomainId && currentDomainId > 0) {
      setIsLoading(true);
      const getResourcesData = async () => {
        try {
          const result: IResourceData[] = await getResourceList({
            currentDomainId,
          });
          const reversedResult = result && result.reverse();
          setResourcesData(reversedResult);
        } catch (err) {
          setResourcesData(undefined);
        } finally {
          setIsLoading(false);
        }
      };
      getResourcesData();
    }
  }, [currentDomainId]);

  return [resourcesData];
};

export default useFetchResources;
