import { useEffect, useState } from "react";

import { getConnectionKeyBackup } from "../services/connectionKeysService";

const useFetchConnectionKeyBackup = ({
  connectionKeyId,
  setIsLoading,
  page,
  pSize,
}: any) => {
  const [connectionKeyBackupData, setConnectionKeyBackupData] = useState<any>();
  useEffect(() => {
    if (connectionKeyId && connectionKeyId > 0) {
      setIsLoading(true);
      const getConnectionKeyBackupData = async () => {
        try {
          const result = await getConnectionKeyBackup({
            connectionKeyId,
            page,
            pSize,
          });
          setConnectionKeyBackupData(result);
        } catch (err) {
          setConnectionKeyBackupData({});
        } finally {
          setIsLoading(false);
        }
      };
      getConnectionKeyBackupData();
    }
  }, [connectionKeyId, page, pSize]);

  return [connectionKeyBackupData];
};

export default useFetchConnectionKeyBackup;
