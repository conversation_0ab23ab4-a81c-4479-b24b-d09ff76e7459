import { useEffect, useState } from "react";
import { getRulesList } from "../services/rulesService";

interface IuseFetchAllRules {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

const useFetchAllRules = ({ setIsLoading }: IuseFetchAllRules) => {
  const [allRules, setAllRules] = useState<any[]>([]);

  useEffect(() => {
    const getAllRulesListData = async () => {
      setIsLoading(true);
      try {
        const result = await getRulesList();

        // Ensure result is an array before setting state
        setAllRules(Array.isArray(result) ? result : []);
      } catch (err) {
        console.error("Error fetching rules:", err);
        setAllRules([]); // fallback to empty array
      } finally {
        setIsLoading(false);
      }
    };

    getAllRulesListData();
  }, [setIsLoading]);

  return [allRules];
};

export default useFetchAllRules;
