import { useEffect, useState } from "react";
import { getPaginatedRunInstance } from "../services/runInstance";

const useFetchPaginatedRunInstance = ({setIsLoading, page,pSize}:any) => {
  const [runInstance, setRunInstance] = useState<any>([]);
  useEffect(() => {
    setIsLoading(true);
    const getRunInstance = async () => {
      try {
        const result = await getPaginatedRunInstance({page,pSize});
        setRunInstance(result);
      } catch (err) {
        setRunInstance([]);
      } finally {
        setIsLoading(false);
      }
    };
    getRunInstance();
  }, [page, pSize]);

  return [runInstance];
};

export default useFetchPaginatedRunInstance;
