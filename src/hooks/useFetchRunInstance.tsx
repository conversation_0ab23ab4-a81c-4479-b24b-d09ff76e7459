import { useEffect, useState } from "react";
import { getActiveRunInstance } from "../services/runInstance";

const useFetchRunInstance = ({setIsLoading}:any) => {
  const [runInstance, setRunInstance] = useState<any>([]);
  useEffect(() => {
    setIsLoading(true);
    const getRunInstance = async () => {
      try {
        const result = await getActiveRunInstance();
        setRunInstance(result);
      } catch (err) {
        setRunInstance([]);
      } finally {
        setIsLoading(false);
      }
    };
    getRunInstance();
  }, []);

  return [runInstance];
};

export default useFetchRunInstance;
