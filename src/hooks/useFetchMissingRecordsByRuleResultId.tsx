import { useEffect, useState } from "react";
import { getMissingRuleResultByRuleResultId } from "../services/rulesService";

interface IuseFetchRuleResult {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentRuleResultId: any;
  isMissingFetch: boolean;
  // setIsMissingFetch: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
}

const useFetchMissingRecordsByRuleResultId = ({
  setIsLoading,
  isMissingFetch = false,
  // setIsMissingFetch,
  currentRuleResultId,
  page,
  pSize,
}: IuseFetchRuleResult) => {
  const [missingResultsRuleDashboardData, setMissingResultsRuleDashboardData] =
    useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      if (!isMissingFetch || !currentRuleResultId || currentRuleResultId <= 0) {
        return;
      }

      setIsLoading(true);

      try {
        const result = await getMissingRuleResultByRuleResultId({
          currentRuleResultId,
          page,
          pSize,
        });
        //const result: any = MISSMISSINGDummyData;
        setMissingResultsRuleDashboardData(result);
      } catch (err) {
        setMissingResultsRuleDashboardData({});
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [currentRuleResultId, isMissingFetch, page, pSize]);

  return [missingResultsRuleDashboardData];
};

export default useFetchMissingRecordsByRuleResultId;
