import { useEffect, useState } from "react";
import { IResourceDetail } from "../types/resource";
import { getResourceColumnBackup } from "../services/resourcesService";

const useFetchResourceColumnBackups = ({
  resourceColumnId,
  setIsLoading,
  page,
  pSize,
}: any) => {
  const [resourceColumnBackupData, setResourceColumnBackupData] =
    useState<any>();
  useEffect(() => {
    if (resourceColumnId && resourceColumnId > 0) {
      setIsLoading(true);
      const getResourceColumnBackupData = async () => {
        try {
          const ResourceResult: IResourceDetail = await getResourceColumnBackup(
            {
              resourceColumnId,
              page,
              pSize,
            }
          );
          setResourceColumnBackupData(ResourceResult);
        } catch (err) {
          setResourceColumnBackupData({});
        } finally {
          setIsLoading(false);
        }
      };
      getResourceColumnBackupData();
    }
  }, [resourceColumnId, page, pSize]);

  return [resourceColumnBackupData];
};

export default useFetchResourceColumnBackups;
