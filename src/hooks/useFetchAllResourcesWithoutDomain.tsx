import { useEffect, useState } from "react";
import { IResourceData } from "../types/resource";
import { getAllResourceList } from "../services/resourcesService";

const useFetchAllResourcesWithoutDomain = ({
  setIsLoading,
  aggregation_type = "flat",
}: any) => {
  const [resourcesData, setResourcesData] = useState<IResourceData[]>([]);
  useEffect(() => {
    // setIsLoading(true);
    const getResourcesData = async () => {
      try {
        let result: IResourceData[] = [];
        result = await getAllResourceList();
        setResourcesData(result);
      } catch (err) {
        setResourcesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (aggregation_type === "aggregated") getResourcesData();
  }, [aggregation_type]);

  return [resourcesData];
};

export default useFetchAllResourcesWithoutDomain;
