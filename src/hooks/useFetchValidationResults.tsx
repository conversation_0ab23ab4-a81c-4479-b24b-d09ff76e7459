import { useEffect, useState } from "react";
import { getValidationReportsByRunInstanceAndRunDate, getValidationSummaryReportsByRunInstanceAndRunDate } from "../services/reportsService";
import { IgetReportsByRunInstanceProps } from "../types/reports";
import { formatDate } from "../services/utils";

const useFetchValidationResults = ({
  setIsLoading,
  runName,
  runDate,
  reportType,
  page,
  pSize,
}: any) => {
  // const formattedDate = formatDate(runDate, 'MM-DD-YYYY');
  const formattedDate = formatDate(runDate, 'YYYY-MM-DD');
  const [validationReports, setValidationReports] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    // Validation consolidated reports
    const validationReportByRunInstanceAndRunDate = async ({
      runName,
      runDate,
      page,
      pSize,
      formattedDate
    }: IgetReportsByRunInstanceProps) => {
      try {
        const result = await getValidationReportsByRunInstanceAndRunDate({
          runName,
          formattedDate,
          page,
          pSize,
        });
        setValidationReports(result);
      } catch (err) {
        setValidationReports([]);
      } finally {
        setIsLoading(false);
      }
    };
    const validationSummaryReportByRunInstanceAndRunDate = async ({
      runName,
      runDate,
      page,
      pSize,
      formattedDate
    }: IgetReportsByRunInstanceProps) => {
      try {
        const result = await getValidationSummaryReportsByRunInstanceAndRunDate({
          runName,
          formattedDate,
          page,
          pSize,
        });
        setValidationReports(result);
      } catch (err) {
        setValidationReports([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (runName) {
      if (reportType === "detail") {
        validationReportByRunInstanceAndRunDate({ runName, runDate, page, pSize, formattedDate });
      } else {
        validationSummaryReportByRunInstanceAndRunDate({runName, runDate, page, pSize, formattedDate});
      }
    }else{
      setValidationReports([]);
      setIsLoading(false);
    }
  }, [runName, runDate, reportType, page, pSize]);

  return [validationReports];
};

export default useFetchValidationResults;
