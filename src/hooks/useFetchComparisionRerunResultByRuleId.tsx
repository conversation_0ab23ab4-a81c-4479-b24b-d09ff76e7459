import { useEffect, useState, useCallback } from "react";
import { getComparisonRerunResultByRuleId } from "../services/rulesService";
import { comparisonRerunResultMockData } from "../services/constants/Rules";

interface FetchComparisonRerunResultProps {
  ruleId: number | null;
  setIsLoading: (loading: boolean) => void;
  runId?: number | null;
  runName?: string | null;
}

const useFetchComparisonRerunResultByRuleId = ({
  ruleId,
  setIsLoading,
  runId,
  runName,
}: FetchComparisonRerunResultProps) => {
  const [comparisonRerunResultData, setComparisonRerunResultData] =
    useState<any>(null);

  const fetchRerunResult = useCallback(async () => {
    if (ruleId) {
      setIsLoading(true);
      try {
        //on temp bases mock data is set
        // setComparisonRerunResultData(comparisonRerunResultMockData);

        const result = await getComparisonRerunResultByRuleId(
          ruleId,
          runId,
          runName
        );
        setComparisonRerunResultData(result);
      } catch (error) {
        setComparisonRerunResultData(null);
      } finally {
        setIsLoading(false);
      }
    }
  }, [ruleId, setIsLoading]);

  useEffect(() => {
    fetchRerunResult();
  }, [fetchRerunResult]);

  return [comparisonRerunResultData];
};

export default useFetchComparisonRerunResultByRuleId;
