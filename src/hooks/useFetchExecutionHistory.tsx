import { useEffect, useState } from "react";
import { getExecutionHistory } from "../services/executionHistoryService";

interface ExecutionHistoryProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
  domainId?: number | null;
  ruleId?: number | null;
  fromDate?: string | undefined;
  searchFilterData: any;
}
const useFetchExecutionHistory = ({
  setIsLoading,
  page,
  pSize,
  domainId,
  ruleId,
  fromDate,
  searchFilterData,
}: ExecutionHistoryProps) => {
  const [executionList, setExecutionList] = useState<any>([]);

  useEffect(() => {
    const getExecutionHistoryList = async () => {
      try {
        setIsLoading(true);
        const result = await getExecutionHistory({
          page,
          pSize,
          domainId,
          ruleId,
          fromDate,
          searchFilterData,
        });
        setExecutionList(result);
      } catch (error) {
        // Handle any potential errors here
        console.error("Error fetching execution history:", error);
      } finally {
        setIsLoading(false);
      }
    };

    getExecutionHistoryList();
  }, [setIsLoading, page, pSize, domainId, ruleId, fromDate, searchFilterData]);

  return [executionList];
};

export default useFetchExecutionHistory;
