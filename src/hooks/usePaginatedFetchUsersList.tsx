import { useEffect, useState } from "react";

import { getPaginatedUsersList, getUsersList } from "../services/userService";

interface IuseFetchUsers {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
}
const useFetchUsersList = ({ setIsLoading, page, pSize }: IuseFetchUsers) => {
  const [usersData, setUsersData] = useState<any>({});
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const result = await getPaginatedUsersList({
          page,
          pSize,
        });
        setUsersData(result);
      } catch (err) {
        setUsersData({});
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [page, pSize]);

  return [usersData];
};
export default useFetchUsersList;
