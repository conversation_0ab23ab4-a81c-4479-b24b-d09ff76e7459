import { useEffect, useState } from "react";
import { IGetRulesListData } from "../types/rules";
import { getRulesListByDomainId } from "../services/rulesService";

const useFetchRulesByDomainId = ({ currentDomainId, setIsLoading }: any) => {
  const [rulesList, setRulesList] = useState<IGetRulesListData[]>([]);
  useEffect(() => {
    if (currentDomainId && currentDomainId > 0) {
      setIsLoading(true);
      const fetchRulesList = async () => {
        try {
          const result = await getRulesListByDomainId(currentDomainId);
          const res = result && result.reverse();
          setRulesList(res);
        } catch (err) {
          setRulesList([]);
        } finally {
          setIsLoading(false);
        }
      };
      fetchRulesList();
    }
  }, [currentDomainId]);

  return [rulesList];
};

export default useFetchRulesByDomainId;
