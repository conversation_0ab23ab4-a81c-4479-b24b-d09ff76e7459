import { useEffect, useState, useRef, useCallback } from "react";

// Notification interface
interface Notification {
  id: string;
  message: string;
  read: boolean;
  timestamp: number;
  status?: string;
  status_code?: string;
}

const RETRY_DELAY = Number(process.env.REACT_APP_RETRY_DELAY || 4000); // 4 seconds
const PING_TIMEOUT = 30000; // 30 seconds - time to wait for a ping before considering connection dead

const useWebSocket = (handleStorageChange: () => void) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const socketRef = useRef<WebSocket | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isManuallyClosed = useRef<boolean>(false);
  const lastPingTimeRef = useRef<number>(Date.now());
  const pingCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Load initial notifications from localStorage
  useEffect(() => {
    const storedNotifications = localStorage.getItem("notifications");
    if (storedNotifications) {
      try {
        const parsedNotifications = JSON.parse(storedNotifications);
        // Convert any old format notifications to the new format with read status
        const formattedNotifications = Array.isArray(parsedNotifications)
          ? parsedNotifications.map((item) => {
              // Handle the case of stored string messages
              const notificationData =
                typeof item === "string" ? JSON.parse(item) : item;

              return {
                id:
                  notificationData.id ||
                  `notification-${Date.now()}-${Math.random()
                    .toString(36)
                    .substring(2, 11)}`,
                message:
                  typeof item === "string"
                    ? item
                    : JSON.stringify(notificationData),
                read: notificationData.read || false,
                timestamp: notificationData.timestamp || Date.now(),
                ...notificationData,
              };
            })
          : [];
        setNotifications(formattedNotifications);
      } catch (error) {
        console.error("Error loading notifications from localStorage:", error);
      }
    }
  }, []);

  const connectWebSocket = useCallback(() => {
    const userId = Number(localStorage.getItem("userId")) || 0;
    const token = localStorage.getItem("token") || "";
    if (!userId || !token) return;

    if (socketRef.current?.readyState === WebSocket.OPEN) {
      // console.log("WebSocket already connected. Skipping reconnection.");
      return;
    }

    const baseUrl = process.env.REACT_APP_ENGINE_SOCKET_HOST;
    // const url = `${baseUrl}/notifications/register_notifications?token=${token}`;
    const url = `${baseUrl}/notifications/register_notifications?token=${token}`;
    // console.log("Connecting to WebSocket:", url);

    socketRef.current = new WebSocket(url);

    socketRef.current.onopen = () => {
      // console.log("Connected to WebSocket");
    };

    socketRef.current.onmessage = (event) => {
      try {
        // Check if the message is a ping message
        if (event.data === "ping") {
          // Update the last ping time
          lastPingTimeRef.current = Date.now();
          return;
        }
        // For all other messages, try to parse as JSON
        const data = JSON.parse(event.data);

        const newNotification: Notification = {
          id: `notification-${Date.now()}-${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          message: JSON.stringify(data),
          read: false,
          timestamp: Date.now(),
          ...data,
        };

        // Update state with the new notification
        setNotifications((prevNotifications) => {
          const updatedNotifications = [newNotification, ...prevNotifications];
          // Save the updated notifications to localStorage
          localStorage.setItem(
            "notifications",
            JSON.stringify(updatedNotifications)
          );

          // 🔥 Call handleStorageChange to trigger UI update
          handleStorageChange();

          return updatedNotifications;
        });
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    // socketRef.current.onerror = (error) => {
    //   console.error("WebSocket Error:", error);
    // };

    socketRef.current.onclose = () => {
      if (!isManuallyClosed.current) {
        console.warn(
          `WebSocket disconnected. Reconnecting in ${
            RETRY_DELAY / 1000
          } seconds...`
        );
        retryTimeoutRef.current = setTimeout(connectWebSocket, RETRY_DELAY);
      } else {
        // console.log("WebSocket connection closed manually.");
      }
    };
  }, [handleStorageChange]);

  // Setup ping check interval to monitor connection health
  useEffect(() => {
    // Start a ping check interval to monitor the WebSocket connection
    pingCheckIntervalRef.current = setInterval(() => {
      const currentTime = Date.now();
      const timeSinceLastPing = currentTime - lastPingTimeRef.current;

      // If we haven't received a ping in PING_TIMEOUT ms and the socket is open,
      // we'll consider the connection as potentially stale but won't close it
      if (timeSinceLastPing > PING_TIMEOUT) {
        console.warn(
          `No ping received in ${
            PING_TIMEOUT / 1000
          } seconds, but keeping connection open`
        );
      }
    }, 10000); // Check every 10 seconds

    return () => {
      if (pingCheckIntervalRef.current) {
        clearInterval(pingCheckIntervalRef.current);
        pingCheckIntervalRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    isManuallyClosed.current = false;
    connectWebSocket();

    return () => {
      // We only manually close the connection if the app is shutting down
      // or if we haven't received a ping in a long time
      const timeSinceLastPing = Date.now() - lastPingTimeRef.current;

      // Only set manually closed if we're truly shutting down the app
      // or if we haven't received a ping in a very long time (3x the timeout)
      if (timeSinceLastPing > PING_TIMEOUT * 3) {
        isManuallyClosed.current = true;
        if (socketRef.current) {
          socketRef.current.close();
          socketRef.current = null;
        }
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [connectWebSocket]);

  // Function to mark a notification as read
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications((prevNotifications) => {
      const updatedNotifications = prevNotifications.map((notification) =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      );

      // Update localStorage with the new read status
      localStorage.setItem(
        "notifications",
        JSON.stringify(updatedNotifications)
      );
      return updatedNotifications;
    });
  }, []);

  // Function to mark all notifications as read
  const markAllAsRead = useCallback(() => {
    setNotifications((prevNotifications) => {
      const updatedNotifications = prevNotifications.map((notification) => ({
        ...notification,
        read: true,
      }));

      // Update localStorage with all notifications marked as read
      localStorage.setItem(
        "notifications",
        JSON.stringify(updatedNotifications)
      );
      return updatedNotifications;
    });
  }, []);

  // Function to clear all notifications
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
    localStorage.removeItem("notifications");
  }, []);

  return {
    notifications,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    unreadCount: notifications.filter((notification) => !notification.read)
      .length,
  };
};

export default useWebSocket;
