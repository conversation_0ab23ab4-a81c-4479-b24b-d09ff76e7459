import { useEffect, useState } from "react";
import { getIncidentByExecutionIds } from "../services/userService";

interface IuseFetchIncidentData {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  data: any;
  isTriggereBtnPressed?: any;
  isCommentBtnPressed?: any;
}

const useFetchIncidentByExecutionId = ({
  setIsLoading,
  data,
  isTriggereBtnPressed,
  isCommentBtnPressed,
}: IuseFetchIncidentData) => {
  const [incidentData, setIncidentData] = useState<any>([]);
  useEffect(() => {
    // setIsLoading(true);
    const getIncidentData = async () => {
      try {
        const result = await getIncidentByExecutionIds(data);
        setIncidentData(result);
      } catch (err) {
        setIncidentData([]);
      } finally {
        setIsLoading(false);
      }
    };
    data && getIncidentData();
  }, [data, isTriggereBtnPressed, isCommentBtnPressed]);

  return [incidentData];
};

export default useFetchIncidentByExecutionId;
