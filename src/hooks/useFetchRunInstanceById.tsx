import { useEffect, useState } from "react";
import { getRunInstanceDetail } from "../services/runInstance";

interface IuseFetchRunInstance {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentRunInstanceId: any;
}

const useFetchRunInstanceById = ({
  setIsLoading,
  currentRunInstanceId,
}: IuseFetchRunInstance) => {
  const [runInstanceData, setRunInstanceData] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    const getRunInstanceData = async () => {
      try {
        
        const result = currentRunInstanceId > 0 && await getRunInstanceDetail({ currentRunInstanceId });
        setRunInstanceData(result);
      } catch (err) {
        setRunInstanceData({});
      } finally {
        setIsLoading(false);
      }
    };
    getRunInstanceData();
  }, [currentRunInstanceId]);

  return [runInstanceData];
};

export default useFetchRunInstanceById;
