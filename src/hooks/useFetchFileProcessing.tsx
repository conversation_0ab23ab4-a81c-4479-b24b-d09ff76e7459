import { useEffect, useState } from "react";
import { getFileProcessing } from "../services/fileProcessingService";

const useFetchFileProcessing = ({setIsLoading}:any) => {
  const [fileProcessingData, setFileProcessingData] = useState<any[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const getFileProcessingData = async () => {
      try {
        const result = await getFileProcessing();
        const res = result && result.reverse();
        setFileProcessingData(res);
      } catch (err) {
        setFileProcessingData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getFileProcessingData();
  }, []);

  return [fileProcessingData];
};

export default useFetchFileProcessing;
