import { useEffect, useState } from "react";
import { getResourceFilesList } from "../services/resourcesService";

interface IuseFetchFilesByResource {
  currentResourceId: string | number;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

const useFetchFilesByResource = ({
  currentResourceId,
  setIsLoading,
}: IuseFetchFilesByResource) => {
  const [fileData, setFileData] = useState<{ name: string }[]>([]);
  useEffect(() => {
    if (currentResourceId !== 0) {
      setIsLoading(true);
      const getFilesByResource = async () => {
        try {
          const fileListData: { name: string }[] = [];
          const filesName = await getResourceFilesList({ currentResourceId });
          {
            filesName &&
              filesName.map((e: any) => {
                fileListData.push({ name: e });
              });
          }
          setFileData(fileListData);
        } catch (err) {
          setFileData([]);
        } finally {
          setIsLoading(false);
        }
      };
      getFilesByResource();
    }
  }, [currentResourceId]);

  return [fileData];
};

export default useFetchFilesByResource;
