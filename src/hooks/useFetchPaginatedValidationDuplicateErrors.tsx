import { useEffect, useState } from "react";
import { getPaginatedValidationDuplicateErrorsList } from "../services/resourcesService";
import { validationDuplicateErrorsMockData } from "../services/constants/resource";

interface IuseFetchPaginatedValidationDuplicateErrors {
  currentValidationResultId: any;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
  isDetailedExecutionDataAvailable: boolean;
  resourceId: any;
}

const useFetchPaginatedValidationDuplicateErrors = ({
  currentValidationResultId,
  setIsLoading,
  page,
  pSize,
  isDetailedExecutionDataAvailable,
  resourceId,
}: IuseFetchPaginatedValidationDuplicateErrors) => {
  const [duplicateValidationErrors, setduplicateValidationErrors] =
    useState<any>([]);
  useEffect(() => {
    setIsLoading(true);
    const fetchValidationData = async () => {
      try {
        const result = await getPaginatedValidationDuplicateErrorsList({
          currentValidationResultId,
          resourceId,
          page,
          pSize,
        });

        //Set Mock Data
        // const result: any = validationDuplicateErrorsMockData;

        setduplicateValidationErrors(result);
      } catch (err) {
        setduplicateValidationErrors([]);
      } finally {
        setIsLoading(false);
      }
    };
    if (!isDetailedExecutionDataAvailable) fetchValidationData();
  }, [currentValidationResultId, page, pSize, resourceId]);

  return [duplicateValidationErrors];
};

export default useFetchPaginatedValidationDuplicateErrors;
