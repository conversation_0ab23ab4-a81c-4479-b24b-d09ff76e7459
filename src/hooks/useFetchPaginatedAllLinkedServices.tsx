import { useEffect, useState } from "react";
import { getAllLinkedPaginatedServices } from "../services/linkedService";

interface IuseFetchLinkedServices {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  page: any;
  pSize: any;
}
const useFetchPaginatedAllLinkedServices = ({
  setIsLoading,
  page,
  pSize,
}: IuseFetchLinkedServices) => {
  const [linkedServicesData, setLinkedServicesData] = useState<any>([]);
  useEffect(() => {
    setIsLoading(true);
    const getLinkedServicesData = async () => {
      try {
        const result = await getAllLinkedPaginatedServices(page, pSize);
        setLinkedServicesData(result);
      } catch (err) {
        setLinkedServicesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getLinkedServicesData();
  }, [page, pSize]);

  return [linkedServicesData];
};

export default useFetchPaginatedAllLinkedServices;
