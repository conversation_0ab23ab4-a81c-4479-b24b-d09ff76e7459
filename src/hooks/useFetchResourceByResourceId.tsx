import { useEffect, useState } from "react";
import { IResourceDetail } from "../types/resource";
import { getResourceDetail } from "../services/resourcesService";

const useFetchResourceByResourceId = ({ currentResourceId, setIsLoading }: any) => {
    const [resourceData, setResourceData] = useState<any>();
    useEffect(() => {
        if (currentResourceId && currentResourceId > 0) {
            setIsLoading(true);
            const getResourcesData = async () => {
                try {
                    const ResourceResult: IResourceDetail = await getResourceDetail({ currentResourceId });
                    setResourceData(ResourceResult);
                } catch (err) {
                    setResourceData({});
                } finally {
                    setIsLoading(false);
                }
            };
            getResourcesData();
        }
    }, [currentResourceId]);

    return [resourceData];
};

export default useFetchResourceByResourceId;
