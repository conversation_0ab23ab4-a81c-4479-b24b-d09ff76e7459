import { useEffect, useState } from "react";
import { getMismatchRuleResultByRuleResultId } from "../services/rulesService";

interface UseFetchRuleResultProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentRuleResultId: number | null;
  isMismatchFetch: boolean;
  // setIsMismatchFetch: React.Dispatch<React.SetStateAction<boolean>>;
  page: number;
  pSize: number;
}

interface RuleDashboardData {
  [key: string]: any; // Adjust this type based on the actual structure of the fetched data
}

const useFetchMismatchRecordsByRuleResultId = ({
  setIsLoading,
  currentRuleResultId,
  isMismatchFetch,
  // setIsMismatchFetch,
  page,
  pSize,
}: UseFetchRuleResultProps): [RuleDashboardData] => {
  const [mismatchResults, setMismatchResults] = useState<RuleDashboardData>({});
  useEffect(() => {
    const fetchData = async () => {
      if (
        !isMismatchFetch ||
        !currentRuleResultId ||
        currentRuleResultId <= 0
      ) {
        return;
      }

      setIsLoading(true);
      try {
        const result = await getMismatchRuleResultByRuleResultId({
          currentRuleResultId,
          page,
          pSize,
        });
        setMismatchResults(result);
      } catch (error) {
        console.error("Failed to fetch mismatch rule results:", error);
        setMismatchResults({});
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [
    currentRuleResultId,
    isMismatchFetch,
    page,
    pSize,
    setIsLoading,
    // setIsMismatchFetch,
  ]);

  return [mismatchResults];
};

export default useFetchMismatchRecordsByRuleResultId;
