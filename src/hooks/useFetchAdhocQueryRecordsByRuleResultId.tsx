import { useEffect, useState } from "react";
import { getAdhocQueryResultByRuleResultId } from "../services/rulesService";

interface UseFetchAdhocQueryProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentRuleResultId: number | null;
  isAdhocQueryFetch: boolean;
  pageN: number;
  pageS: number;
  queryName: string;
}

interface AdhocQueryResults {
  [key: string]: any;
}

const useFetchAdhocQueryRecordsByRuleResultId = ({
  setIsLoading,
  currentRuleResultId,
  isAdhocQueryFetch,
  pageN,
  pageS,
  queryName,
}: UseFetchAdhocQueryProps): [
  AdhocQueryResults,
  React.Dispatch<React.SetStateAction<AdhocQueryResults>>
] => {
  const [adhocQueryResults, setAdhocQueryResults] = useState<AdhocQueryResults>(
    {}
  );

  useEffect(() => {
    const fetchData = async () => {
      if (
        !isAdhocQueryFetch ||
        !currentRuleResultId ||
        currentRuleResultId <= 0
      ) {
        return;
      }

      setIsLoading(true);
      try {
        const result = await getAdhocQueryResultByRuleResultId({
          currentRuleResultId,
          queryName,
          pageN,
          pageS,
        });
        setAdhocQueryResults(result);
      } catch (error) {
        console.error("Error fetching adhoc query records:", error);
        setAdhocQueryResults({});
      } finally {
        setIsLoading(false);
      }
    };

    queryName !== "" && fetchData();
  }, [currentRuleResultId, isAdhocQueryFetch, queryName, pageN, pageS]);

  return [adhocQueryResults, setAdhocQueryResults];
};

export default useFetchAdhocQueryRecordsByRuleResultId;
