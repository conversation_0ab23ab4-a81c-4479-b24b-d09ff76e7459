import { useEffect, useState } from "react";
import { IResourceData } from "../types/resource";
import { getResourceColumnsList } from "../services/resourcesService";

const useFetchResourceColumns = ({ currentDomainId, setIsLoading }: any) => {
  const [resourcesData, setResourcesData] = useState<IResourceData[]>([]);
  useEffect(() => {
    setIsLoading(true);
    const getResourceColumnsData = async () => {
      try {
        const result: IResourceData[] = await getResourceColumnsList({
          currentDomainId,
        });
        const reversedResult = result && result.reverse();
        setResourcesData(reversedResult);
      } catch (err) {
        setResourcesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getResourceColumnsData();
  }, [currentDomainId]);

  return [resourcesData];
};

export default useFetchResourceColumns;
