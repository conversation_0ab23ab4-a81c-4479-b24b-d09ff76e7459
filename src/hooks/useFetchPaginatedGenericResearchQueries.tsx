import { useEffect, useState } from "react";
import { IGenericResearchQueriesData } from "../types/ResearchQueries";
import { getResourcePaginatedList } from "../services/resourcesService";
import { IPaginatedProps } from "../types";
import { getResearchQueriesList } from "../services/ResearchQueriesService";

const useFetchPaginatedGenericResearchQueries = ({
  currentDomainId,
  setIsLoading,
  page,
  pSize,
}: IPaginatedProps) => {
  const [genericResearchQueriesData, setGenericResearchQueriesData] = useState<
    IGenericResearchQueriesData[]
  >([]);
  useEffect(() => {
    setIsLoading(true);
    const getGenericResearchQueriesData = async () => {
      try {
        const result: IGenericResearchQueriesData[] =
          await getResearchQueriesList({
            page,
            pSize,
          });
        setGenericResearchQueriesData(result);
      } catch (err) {
        setGenericResearchQueriesData([]);
      } finally {
        setIsLoading(false);
      }
    };
    getGenericResearchQueriesData();
  }, [currentDomainId, page, pSize]);

  return [genericResearchQueriesData];
};

export default useFetchPaginatedGenericResearchQueries;
