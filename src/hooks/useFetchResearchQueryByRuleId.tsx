import { useEffect, useState } from "react";
import { getResearchQueryDetail } from "../services/rulesService";

interface IuseFetchResearchQuery {
  setIsLoading: any;
  currentRuleId: any;
}
const useFetchResearchQueryByRuleId = ({
  setIsLoading,
  currentRuleId,
}: IuseFetchResearchQuery) => {
  const [researchQueryDetail, setResearchQueryDetail] = useState<any>({});
  useEffect(() => {
    setIsLoading(true);
    if (currentRuleId && currentRuleId > 0) {
      const getResearchQueryData = async () => {
        try {
          const result = await getResearchQueryDetail(currentRuleId);
          setResearchQueryDetail(result);
        } catch (err) {
          setResearchQueryDetail({});
        } finally {
          setIsLoading(false);
        }
      };

      getResearchQueryData();
    }
  }, []);

  return [researchQueryDetail];
};

export default useFetchResearchQueryByRuleId;
