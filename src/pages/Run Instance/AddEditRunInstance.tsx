import { Box, Button, Grid, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import FlexBetween from "../../components/FlexBetween";
import { addEditRunInstanceSchema } from "../../schemas";
import useFetchRunInstanceById from "../../hooks/useFetchRunInstanceById";
import { addRunInstance, updateRunInstance } from "../../services/runInstance";
import Loader from "../../components/Molecules/Loader/Loader";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { IRunInstance } from "../../types/RunInstance";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { useToast } from "../../services/utils";

const AddEditRunInstance: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { id: runInstanceId } = useParams();
  const [formData, setFormData] = useState<IRunInstance>({
    run_name: "",
    code: "",
    description: "",
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // hooks
  const [runInstanceData] = useFetchRunInstanceById({
    setIsLoading,
    currentRunInstanceId: runInstanceId,
  });
  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: runInstanceData?.run_name,
      id: runInstanceData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [runInstanceData]);

  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    validateField(e.target.name, e.target.value);
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addEditRunInstanceSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  // handel event when save a linked service
  const onSaveRunInstance = async (e: any) => {
    const serviceAction = runInstanceId ? updateRunInstance : addRunInstance;
    e.preventDefault();
    try {
      await addEditRunInstanceSchema.validate(formData, {
        abortEarly: false,
      });
      const reqBody = {
        ...formData,
      };
      serviceAction({
        currentRunInstanceId: runInstanceId,
        payload: reqBody,
      })
        .then((response) => {
          if (response) {
            showToast(
              `Run Instance ${
                runInstanceId ? "updated" : "created"
              } successfully!`,
              "success"
            );
            navigate("/run-instance");
          }
        })
        .catch((error) => {
          console.error(
            `Cannot ${runInstanceId ? "update" : "create"} run instance`
          );
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  useEffect(() => {
    if (runInstanceData?.id) {
      setFormData({
        ...runInstanceData,
      });
      setInitialFormData(JSON.parse(JSON.stringify(runInstanceData)));
    }
  }, [runInstanceData]);

  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);

    setHasChanges(formChanged);
  }, [formData, initialFormData]);

  return (
    <Box>
      <Loader isLoading={isLoading} />
      <form onSubmit={onSaveRunInstance} autoComplete="off">
        <Box className="text-box-card compact-text-box-card">
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                Run Name<span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                // placeholder="Ex: Sample Run Name"
                name="run_name"
                onChange={handleFormData}
                className={`form-control ${
                  errors?.run_name ? "has-error" : ""
                }`}
                onBlur={(e) => validateField(e.target.name, e.target.value)}
                error={!!errors?.run_name}
                helperText={errors?.run_name || ""}
                value={formData?.run_name}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                Code<span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                name="code"
                disabled={!!runInstanceId}
                className={`form-control ${errors?.code ? "has-error" : ""}`}
                onBlur={(e) => validateField(e.target.name, e.target.value)}
                error={!!errors?.code}
                helperText={errors?.code || ""}
                value={formData?.code}
                onChange={(e: any) => {
                  if (!runInstanceData?.code) {
                    handleFormData(e);
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">Description</label>
              <TextField
                type="text"
                name="description"
                onChange={handleFormData}
                className="form-control"
                value={formData?.description}
              />
            </Grid>
          </Grid>
        </Box>
        <Box>
          <FlexBetween
            gap="3rem"
            sx={{ marginTop: 4, display: "flex", justifyContent: "flex-end" }}
          >
            <Button
              type="submit"
              className="btn-orange"
              color="secondary"
              variant="contained"
              disabled={!hasChanges}
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </FlexBetween>
        </Box>
      </form>
    </Box>
  );
};

export default AddEditRunInstance;
