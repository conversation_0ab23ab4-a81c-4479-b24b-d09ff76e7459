import React, { useEffect, useMemo, useState } from "react";
import {
  Box,
  Button,
  Grid,
  IconButton,
  InputBase,
  Tooltip,
} from "@mui/material";
import FlexBetween from "../../components/FlexBetween";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import ButtonComponent from "../../components/Button.Component";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { Search } from "@mui/icons-material";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import { deleteRunInstance } from "../../services/runInstance";
import useFetchPaginatedRunInstance from "../../hooks/useFetchPaginatedRunInstance";
import { getFormattedDateTime } from "../../services/utils";
import {
  IconDeleteBlueSvg,
  IconEditBase,
  IconEyeBase,
  IconImportFileWhite,
} from "../../common/utils/icons";
import { useToast } from "../../services/utils";
// import {
//   downloadRunInstanceExportFile,
//   getFormattedDateTime,
// } from "../../services/utils";
import DropdownMenu from "../../components/Molecules/DropdownMenu/DropdownMenu";
import { IconExportIconBlue } from "../../common/utils/icons";
import Loader from "../../components/Molecules/Loader/Loader";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const RunInstance: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  // states
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>([]);
  // hooks
  const [fetchedRunInstance] = useFetchPaginatedRunInstance({
    setIsLoading,
    page,
    pSize,
  });
  const [searchQuery, setSearchQuery] = useState("");

  const handelClickEvent = () => {
    navigate("/run-instance/add");
  };

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      renderCell: (params: any) => <></>,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
    },
    {
      field: "run_id",
      headerName: "Run Id",
      minWidth: 80,
      flex: 1,
      renderCell: (params: any) => {
        return <span>{params.id}</span>;
      },
    },
    {
      field: "run_name",
      headerName: "Run Name",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return params.value?.username;
      },
    },
    {
      field: "updated_on",
      headerName: "Last Modified",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return getFormattedDateTime(params.value);
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 170,
      renderCell: (params: any) => {
        const handleDelete = async () => {
          setIsLoading(true);
          await deleteRunInstance(params.row.id);
          const updatedRunInstanceData = fileData?.items?.filter(
            (run_instance: { id: any }) => run_instance.id !== params.row.id
          );
          setFileData((prev: any) => ({
            ...prev,
            items: updatedRunInstanceData,
          }));
          setIsLoading(false);
          showToast(`${params.row.run_name} deleted successfully`, "success");
        };
        return (
          <>
            <Tooltip title="View Run Instance" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  navigate(`/run-instance/view/${params.row.id}`);
                }}
              >
                <IconEyeBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit Run Instance" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  navigate(`/run-instance/edit/${params.row.id}`);
                }}
              >
                <IconEditBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete Run Instance" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => handleDelete()}
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </Tooltip>
            {/* <DropdownMenu
              dropdownMenuItems={[
                <IconButton
                  key="export"
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    downloadRunInstanceExportFile(
                      params.row.id,
                      setIsDownloadLoading
                    )
                  }
                >
                  <IconExportIconBlue />
                  Export
                </IconButton>,
              ]}
            /> */}
          </>
        );
      },
    },
  ];

  // for update Run Instance list data
  useEffect(() => {
    if (fetchedRunInstance) {
      setFileData(fetchedRunInstance);
    }
  }, [fetchedRunInstance]);

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  const filteredData = useMemo(() => {
    return fileData?.items?.filter((runInstance: any, index: number) => {
      return runInstance.run_name
        .toLowerCase()
        .includes(searchQuery?.toLowerCase());
    });
  }, [fileData?.items, searchQuery]);

  return (
    <Box>
      {isDownloadLoading && <Loader isLoading={isDownloadLoading} />}
      <Grid
        container
        justifyContent="space-between"
        alignItems="center"
        className="text-box-card compact-text-box-card list-page-card"
      >
        <Grid item>
          <label className="label-text">Search Run Instance </label>
          <form className="common-search-panel">
            <InputBase
              placeholder="Search..."
              className="search-textbox"
              value={searchQuery}
              onChange={handleSearchInputChange}
            />
            <IconButton className="search-icon" type="submit">
              <Search className="svg_icons" />
            </IconButton>
          </form>
        </Grid>
        <Grid item xs="auto">
          <label className="label-text">&nbsp;</label>
          <FlexBetween sx={{ columnGap: 2 }}>
            {/* <Button
              onClick={() =>
                navigate(`/run-instance/import-entity`, {
                  state: {
                    import_defination_name: "Run Instance",
                  },
                })
              }
              className="btn-orange btn-dark"
              sx={{ columnGap: 1 }}
            >
              <IconImportFileWhite />
              Import
            </Button> */}
            <ButtonComponent
              handelClickEvent={handelClickEvent}
              className="btn-orange"
            >
              <AddSharpIcon sx={{ marginRight: "4px" }} /> Run Instance
            </ButtonComponent>
          </FlexBetween>
        </Grid>
      </Grid>

      <FlexBetween>
        <DataTable
          dataColumns={columns}
          dataRows={filteredData || []}
          loading={isLoading}
          dataListTitle={"Run Instance List"}
          className="dataTable no-radius"
          paginationMode="server"
          rowCount={fileData?.total}
          pageSizeOptions={[25]}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={368}
        />
      </FlexBetween>
    </Box>
  );
};

export default RunInstance;
