import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  TextField,
  Backdrop,
  CircularProgress,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import useFetchRunInstanceById from "../../hooks/useFetchRunInstanceById";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { IconBtnEditBase } from "../../common/utils/icons";

const ViewRunInstance: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { id: runInstanceId } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // hooks
  const [runInstanceData] = useFetchRunInstanceById({
    setIsLoading,
    currentRunInstanceId: runInstanceId,
  });
  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: runInstanceData?.run_name,
      id: runInstanceData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [runInstanceData]);

  return (
    <Box>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={isLoading}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
      <Box className="text-box-card compact-text-box-card">
        <Grid container rowSpacing={1.5} columnSpacing={2.5}>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">Run Name</label>
            <div className="form-control">
              {runInstanceData?.run_name || "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text text-bold">Code</label>
            <div className="form-control">{runInstanceData?.code || "N/A"}</div>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            {runInstanceData?.description && (
              <>
                <label className="label-text text-bold">Description</label>
                <div className="form-control break-word">
                  {runInstanceData?.description || "N/A"}
                </div>
              </>
            )}
          </Grid>
          <Grid item xs sx={{ justifyContent: "flex-end", display: "flex" }}>
            <label className="label-text text-bold">&nbsp;</label>
            <button
              className="btn-nostyle icon-btn-edit"
              onClick={() => navigate(`/run-instance/edit/${runInstanceId}`)}
            >
              <IconBtnEditBase />
            </button>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default ViewRunInstance;
