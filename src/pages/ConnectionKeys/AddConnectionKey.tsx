import {
  <PERSON>,
  <PERSON><PERSON>,
  Grid,
  <PERSON>Subheader,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import FlexBetween from "../../components/FlexBetween";
import { useToast } from "../../services/utils";
import {
  addEditConnectionKeyApiSchema,
  addEditConnectionKeyBlobSchema,
  addEditConnectionKeySchema,
  addEditConnectionKeySftpSchema,
  addEditConnectionKeySqlSchema,
} from "../../schemas";
import {
  addConnectionKey,
  testConnectionKey,
  updateConnectionKey,
} from "../../services/connectionKeysService";
import useFetchConnectionKeyById from "../../hooks/useFetchConnectionKeyById";
import Loader from "../../components/Molecules/Loader/Loader";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import {
  CONNECTION_KEY_SERVICES_GROUPED,
  CONNECTION_KEY_SERVICES_TYPE,
  CONNECTION_KEY_SERVICES_TYPES,
} from "../../services/constants/ConnectionKey";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";

const AddConnectionKey: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { id: connectionKeyId } = useParams();
  const [formData, setFormData] = useState<any>({
    name: "",
    type: CONNECTION_KEY_SERVICES_TYPES[0] || "",
    ip_address: "",
    user_name: "",
    password: "",
    container_name: "",
    connection_string: "",
    client_id: "",
    client_secret: "",
    tenant_id: "",
    api_url: "",
    azure_tenant_id: "",
    azure_client_id: "",
    azure_client_secret: "",
    database_uri: "",
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isKeyTested, setIsKeyTested] = useState<boolean>(false);

  // hooks
  const [connectionKeyData] = useFetchConnectionKeyById({
    setIsLoading,
    currentConnectionKeyId: connectionKeyId,
  });

  useEffect(() => {
    if (formData) {
      setIsKeyTested(false);
    }
  }, [formData]);

  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });

    validateField(e.target.name, e.target.value);
  };
  const handelOnChangeType = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
      ip_address: "",
      user_name: "",
      password: "",
      container_name: "",
      connection_string: "",
      client_id: "",
      client_secret: "",
      tenant_id: "",
      api_url: "",
      azure_tenant_id: "",
      azure_client_id: "",
      azure_client_secret: "",
      database_uri: "",
    });
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addEditConnectionKeySchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const authAPINames = [
    "no_auth_api",
    "basic_auth_api",
    "token_auth_api",
    "key_auth_api",
    "oauth_api",
  ];
  const modifiedApiDetails = (formData: any) => {
    if (authAPINames.includes(formData?.type)) {
      const apiDetails = {
        url: formData?.api_url ?? null,
        username: formData?.user_name ?? null,
        password: formData?.password ?? null,
        token: formData?.token ?? null,
        api_key: formData?.api_key ?? null,
      };
      const modifiedFormData = {
        ...formData,
        api_details: apiDetails,
      };
      ["api_url", "user_name", "password", "token", "api_key"].forEach((e) => {
        delete modifiedFormData[e];
      });
      return modifiedFormData;
    }
    return formData;
  };

  // handel event when save a connection key
  const onSaveConnectionKey = async (e: any) => {
    const serviceAction = connectionKeyId
      ? updateConnectionKey
      : addConnectionKey;
    e.preventDefault();
    //let modifiedFormData = { ...formData };
    try {
      if (
        [
          "mariadb",
          "azure_sql",
          "oracle_sql",
          "snowflake_sql",
          "azure_sql_service_principal",
        ].includes(formData?.type)
      ) {
        await addEditConnectionKeySqlSchema.validate(formData, {
          abortEarly: false,
        });
      } else if (formData?.type === "blob") {
        await addEditConnectionKeyBlobSchema.validate(formData, {
          abortEarly: false,
        });
      } else if (formData?.type === "sftp") {
        await addEditConnectionKeySftpSchema.validate(formData, {
          abortEarly: false,
        });
      } else if (authAPINames.includes(formData?.type)) {
        await addEditConnectionKeyApiSchema.validate(formData, {
          abortEarly: false,
        });
        //modifiedFormData = modifiedApiDetails(formData);
      }
      const reqBody = { ...formData, is_active: true };
      const cleanFormData = Object.fromEntries(
        Object.entries(reqBody).filter(([key, value]) => value !== "")
      );
      serviceAction({
        currentConnectionKeyId: connectionKeyId,
        payload: cleanFormData,
      })
        .then((response) => {
          showToast(
            `Connection Key ${
              connectionKeyId ? "updated" : "created"
            } successfully!`,
            "success"
          );
          navigate("/connection-keys");
        })
        .catch((error) => {
          console.error(
            `Cannot ${connectionKeyId ? "update" : "create"} connection key`
          );
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  const handleKeyTesting = async () => {
    let modifiedFormData = { ...formData };
    try {
      if (
        [
          "mariadb",
          "azure_sql",
          "oracle_sql",
          "snowflake_sql",
          "azure_sql_service_principal",
        ].includes(formData?.type)
      ) {
        await addEditConnectionKeySqlSchema.validate(formData, {
          abortEarly: false,
        });
      } else if (formData?.type === "blob") {
        await addEditConnectionKeyBlobSchema.validate(formData, {
          abortEarly: false,
        });
      } else if (formData?.type === "sftp") {
        await addEditConnectionKeySftpSchema.validate(formData, {
          abortEarly: false,
        });
      } else if (authAPINames.includes(formData?.type)) {
        await addEditConnectionKeyApiSchema.validate(formData, {
          abortEarly: false,
        });
        modifiedFormData = modifiedApiDetails(formData);
      }
      const cleanFormData = Object.fromEntries(
        Object.entries(authAPINames ? modifiedFormData : formData).filter(
          ([key, value]) => value !== ""
        )
      );

      testConnectionKey({ payload: cleanFormData })
        .then((response) => {
          if (response?.status) {
            showToast(
              `Connection Key "${formData.type}" tested successfully!`,
              "success"
            );
            setIsKeyTested(true);
          } else {
            showToast(
              `Test for Connection Key "${formData.type}" failed.`,
              "error"
            );
          }
        })
        .catch((error) => {
          console.error(
            `Cannot ${connectionKeyId ? "update" : "create"} connection key`
          );
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  useEffect(() => {
    if (connectionKeyData?.name) {
      setFormData({
        name: connectionKeyData?.name,
        type: connectionKeyData?.type,
        ip_address: connectionKeyData?.ip_address,
        user_name: connectionKeyData?.user_name,
        password: connectionKeyData?.password,
        container_name: connectionKeyData?.container_name,
        connection_string: connectionKeyData?.connection_string,
        client_id: connectionKeyData?.client_id,
        client_secret: connectionKeyData?.client_secret,
        tenant_id: connectionKeyData?.tenant_id,
        api_url: connectionKeyData?.api_url,
        code: connectionKeyData?.code,
      });
    }
    setCurrentBreadcrumbPage({
      name: connectionKeyData?.name,
      id: connectionKeyData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [connectionKeyData]);

  const blobFields = () => {
    return (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Connection String
            <span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Connection String"
            name="connection_string"
            onChange={handleFormData}
            className={`form-control ${
              errors?.connection_string ? "has-error" : ""
            }`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.connection_string}
            helperText={errors?.connection_string || " "}
            value={formData?.connection_string}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">Container Name</label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Container Name"
            name="container_name"
            onChange={handleFormData}
            className={`form-control`}
            value={formData?.container_name}
          />
        </Grid>
      </>
    );
  };
  const sftpFields = () => {
    return (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            IP Address<span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Connection Keys IP Address"
            name="ip_address"
            onChange={handleFormData}
            className={`form-control ${errors?.ip_address ? "has-error" : ""}`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.ip_address}
            helperText={errors?.ip_address || " "}
            value={formData?.ip_address}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Username<span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Connection Keys Username"
            name="user_name"
            onChange={handleFormData}
            className={`form-control ${errors?.user_name ? "has-error" : ""}`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.user_name}
            helperText={errors?.user_name || " "}
            value={formData?.user_name}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Password<span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Connection Keys Password"
            name="password"
            onChange={handleFormData}
            className={`form-control ${errors?.password ? "has-error" : ""}`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.password}
            helperText={errors?.password || " "}
            value={formData?.password}
          />
        </Grid>
      </>
    );
  };
  const basicAuthFields = () => {
    return (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">Username</label>
          <TextField
            type="text"
            name="user_name"
            onChange={handleFormData}
            className={`form-control ${errors?.user_name ? "has-error" : ""}`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.user_name}
            helperText={errors?.user_name || " "}
            value={formData?.user_name}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">Password</label>
          <TextField
            type="text"
            name="password"
            onChange={handleFormData}
            className={`form-control ${errors?.password ? "has-error" : ""}`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.password}
            helperText={errors?.password || " "}
            value={formData?.password}
          />
        </Grid>
      </>
    );
  };

  const tokenAuthFields = () => {
    return (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Token<span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            name="token"
            onChange={handleFormData}
            className={`form-control ${errors?.token ? "has-error" : ""}`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.token}
            helperText={errors?.token || " "}
            value={formData?.token}
          />
        </Grid>
      </>
    );
  };
  const keyAuthFields = () => {
    return (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            API Key<span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            name="api_key"
            onChange={handleFormData}
            className={`form-control ${errors?.api_key ? "has-error" : ""}`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.api_key}
            helperText={errors?.api_key || " "}
            value={formData?.api_key}
          />
        </Grid>
      </>
    );
  };

  const apiFields = () => {
    return (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Api Url
            <span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            placeholder="Ex: Sample Api Url"
            name="api_url"
            onChange={handleFormData}
            className={`form-control ${errors?.api_url ? "has-error" : ""}`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.api_url}
            helperText={errors?.api_url || " "}
            value={formData?.api_url}
          />
        </Grid>
      </>
    );
  };

  const sqlFields = () => {
    return (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Connection String
            <span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Connection String"
            name="connection_string"
            onChange={handleFormData}
            className={`form-control ${
              errors?.connection_string ? "has-error" : ""
            }`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.connection_string}
            helperText={errors?.connection_string || " "}
            value={formData?.connection_string}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">Database Uri</label>
          <TextField
            type="text"
            name="database_uri"
            onChange={handleFormData}
            className={`form-control`}
            value={formData?.database_uri}
          />
        </Grid>
      </>
    );
  };

  const azureSqlServicePrincipalFields = () => {
    return (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Client Id
            <span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Client Id"
            name="azure_client_id"
            onChange={handleFormData}
            className={`form-control ${
              errors?.azure_client_id ? "has-error" : ""
            }`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.azure_client_id}
            helperText={errors?.azure_client_id || " "}
            value={formData?.azure_client_id}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Client Secret
            <span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Client Secret"
            name="azure_client_secret"
            onChange={handleFormData}
            className={`form-control ${
              errors?.azure_client_secret ? "has-error" : ""
            }`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.azure_client_secret}
            helperText={errors?.azure_client_secret || " "}
            value={formData?.azure_client_secret}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <label className="label-text">
            Tenant Id
            <span className="required-asterisk">*</span>
          </label>
          <TextField
            type="text"
            // placeholder="Ex: Sample Tenant Id"
            name="azure_tenant_id"
            onChange={handleFormData}
            className={`form-control ${
              errors?.azure_tenant_id ? "has-error" : ""
            }`}
            onBlur={(e) => validateField(e.target.name, e.target.value)}
            error={!!errors?.azure_tenant_id}
            helperText={errors?.azure_tenant_id || " "}
            value={formData?.azure_tenant_id}
          />
        </Grid>
      </>
    );
  };

  return (
    <>
      {isLoading ? (
        <Loader isLoading={isLoading} />
      ) : (
        <Box>
          <form onSubmit={onSaveConnectionKey} autoComplete="off">
            <Box className="text-box-card-white m-0">
              <Box className="text-box-header">
                <h3>{connectionKeyId ? "Edit " : "Add "} Connection Key</h3>
              </Box>
              <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Name<span className="required-asterisk">*</span>
                  </label>
                  <TextField
                    type="text"
                    // placeholder="Ex: Sample Connection Keys Name"
                    name="name"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.name ? "has-error" : ""
                    }`}
                    onBlur={(e) => validateField(e.target.name, e.target.value)}
                    error={!!errors?.name}
                    helperText={errors?.name || " "}
                    value={formData?.name}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Code <span className="required-asterisk">*</span>
                  </label>
                  <TextField
                    type="text"
                    name="code"
                    onChange={(e) => {
                      if (!connectionKeyData?.code) {
                        handleFormData(e);
                      }
                    }}
                    className={`form-control ${
                      errors?.code ? "has-error" : ""
                    }`}
                    onBlur={(e) => {
                      if (!connectionKeyData?.code) {
                        validateField(e.target.name, e.target.value);
                      }
                    }}
                    error={!!errors?.code}
                    helperText={errors?.code || " "}
                    value={formData?.code}
                    disabled={connectionKeyData?.code ? true : false}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Type<span className="required-asterisk">*</span>
                  </label>
                  <Select
                    fullWidth
                    value={formData?.type} // Use dynamic state
                    onChange={handelOnChangeType} // Ensure correct handler
                    MenuProps={{ disableScrollLock: true }}
                    title="Type"
                    name="type"
                    style={{ width: "100%", height: 35 }}
                    className={`form-control-autocomplete form-control-autocomplete-1`}
                  >
                    {Object.entries(CONNECTION_KEY_SERVICES_GROUPED).reduce(
                      (acc: any, [groupName, services]) => {
                        acc.push(
                          <ListSubheader
                            sx={{
                              fontSize: "15px",
                              fontWeight: "bold",
                              lineHeight: "32px",
                              backgroundColor: "#f3f3f3",
                            }}
                            key={`subheader-${groupName}`}
                          >
                            {groupName}
                          </ListSubheader>
                        );
                        acc.push(
                          ...Object.entries(services).map(([key, value]) => (
                            <MenuItem
                              key={key}
                              value={key}
                              sx={{ paddingLeft: "24px" }}
                            >
                              <span style={{ textTransform: "capitalize" }}>
                                {value}
                              </span>
                            </MenuItem>
                          ))
                        );
                        return acc;
                      },
                      []
                    )}
                  </Select>
                </Grid>
                {formData.type === "sftp" && sftpFields()}
                {[
                  "mariadb",
                  "azure_sql",
                  "oracle_sql",
                  "snowflake_sql",
                ].includes(formData.type) && sqlFields()}
                {formData.type === "blob" && blobFields()}
                {[
                  "no_auth_api",
                  "basic_auth_api",
                  "token_auth_api",
                  "key_auth_api",
                  "oauth_api",
                ].includes(formData.type) && apiFields()}
                {["basic_auth_api"].includes(formData.type) &&
                  basicAuthFields()}

                {["token_auth_api"].includes(formData.type) &&
                  tokenAuthFields()}

                {["key_auth_api"].includes(formData.type) && keyAuthFields()}
                {formData.type === "azure_sql_service_principal" && (
                  <>
                    {sqlFields()}
                    {azureSqlServicePrincipalFields()}
                  </>
                )}
              </Grid>
            </Box>
            <Box>
              <FlexBetween
                gap={2.5}
                sx={{
                  marginTop: "6px",
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                {formData?.type !== "local" && !connectionKeyId && (
                  <Button
                    type="button"
                    className="btn-orange"
                    color="secondary"
                    variant="contained"
                    onClick={handleKeyTesting}
                  >
                    <SaveOutlinedIcon /> &nbsp; Test Key
                  </Button>
                )}
                <Button
                  type="submit"
                  className="btn-orange"
                  color="secondary"
                  variant="contained"
                  disabled={formData?.type !== "local" && !isKeyTested}
                >
                  <SaveOutlinedIcon /> &nbsp; Save
                </Button>
              </FlexBetween>
            </Box>
          </form>
        </Box>
      )}
    </>
  );
};

export default AddConnectionKey;
