import React, { useEffect, useMemo, useState } from "react";
import {
  Box,
  Button,
  Grid,
  IconButton,
  InputBase,
  Tooltip,
} from "@mui/material";
import FlexBetween from "../../components/FlexBetween";
import { useToast } from "../../services/utils";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import { useNavigate, useParams } from "react-router-dom";
import ButtonComponent from "../../components/Button.Component";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { Search } from "@mui/icons-material";
import iconEye from "../../assets/svgs/icon-eye-orange.svg";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import { deleteConnectionKey } from "../../services/connectionKeysService";
import { getFormattedDateTime } from "../../services/utils";
import DropdownMenu from "../../components/Molecules/DropdownMenu/DropdownMenu";
import Loader from "../../components/Molecules/Loader/Loader";
import {
  IconAudit,
  IconDeleteBlueSvg,
  IconCheckCircleIconGreenSvg,
  IconCrossCircleIconSvg,
  IconEyeBase,
} from "../../common/utils/icons";
import { paginatedResponseFormat } from "../../services/constants";
import useFetchPaginatedAllConnectionKeys from "../../hooks/useFetchPaginatedAllConnectionKeys";
import { enableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const ConnectionKeys: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  // states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>(paginatedResponseFormat);
  const [connectionKeysData, setConnectionKeysData] = useState<any>(
    paginatedResponseFormat
  );
  // hooks
  const [fetchedConnectionKeys] = useFetchPaginatedAllConnectionKeys({
    setIsLoading,
    page,
    pSize,
  });
  const [searchQuery, setSearchQuery] = useState("");

  const handelClickEvent = () => {
    navigate("/connection-keys/add");
  };

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
      renderCell: (params: any) => <></>,
    },
    {
      field: "id",
      headerName: "Id",
      minWidth: 80,
      flex: 1,
      renderCell: (params: any) => {
        return <span>{params.id}</span>;
      },
    },
    {
      field: "name",
      headerName: "Connection Key",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "updated_on",
      headerName: "Last Modified",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return getFormattedDateTime(params.value);
      },
    },
    {
      field: "is_active",
      headerName: "isActive",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return params.value ? (
          <Box title="Active">
            <IconCheckCircleIconGreenSvg width={21} height={21} />
          </Box>
        ) : (
          <Box title="InActive">
            <IconCrossCircleIconSvg width={21} height={21} />
          </Box>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "right",
      headerAlign: "right",
      width: 130,
      renderCell: (params: any) => {
        const handleDelete = async () => {
          setIsLoading(true);
          // setFileData((prev: any) => ({
          //   ...prev,
          //   items: prev?.items?.filter(
          //     (item: any) => item.id !== params.row.id
          //   ),
          // }));
          await deleteConnectionKey(params.row.id);
          const updatedConnectionKeysData = fileData?.items?.map((item: any) =>
            item.id === params.row.id ? { ...item, is_active: false } : item
          );
          // setFileData(updatedConnectionKeysData);
          setFileData((prev: any) => ({
            ...prev,
            items: updatedConnectionKeysData,
          }));
          setConnectionKeysData(updatedConnectionKeysData);
          setIsLoading(false);
          showToast(`${params.row.name} deleted successfully`, "success");
        };
        return (
          <>
            {params?.row?.is_active && (
              <>
                <Tooltip title="View Connection Key" placement="top" arrow>
                  <IconButton
                    className="datagrid-action-btn"
                    color="inherit"
                    onClick={() => {
                      navigate(`/connection-keys/view/${params.row.id}`);
                    }}
                  >
                    <IconEyeBase />
                  </IconButton>
                </Tooltip>
              </>
            )}
            <DropdownMenu
              dropdownMenuItems={[
                <IconButton
                  key="audit-connectionKeys"
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    navigate(
                      `/connection-keys/auditConnectionKey/${params.row.id}`,
                      {
                        state: {
                          details: {
                            name: params.row.name,
                            id: params.row.id,
                          },
                        },
                      }
                    )
                  }
                >
                  <IconAudit /> Audit Connection Key
                </IconButton>,
                params?.row?.is_active && (
                  <IconButton
                    className="datagrid-action-btn"
                    color="inherit"
                    onClick={() => handleDelete()}
                  >
                    <IconDeleteBlueSvg />
                    Delete Connection Key
                  </IconButton>
                ),
              ]}
            />
          </>
        );
      },
    },
  ];

  // for update connection keys list data
  useEffect(() => {
    if (fetchedConnectionKeys?.items) {
      setFileData(fetchedConnectionKeys);
      setConnectionKeysData(fetchedConnectionKeys);
    }
  }, [fetchedConnectionKeys]);

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    // Filter connection keys based on the searchQuery
    const filteredConnectionKeys = connectionKeysData?.items?.filter(
      (connectionKey: any) => {
        return connectionKey.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
      }
    );
    //You can set the filtered connection keys as a result in your component state
    setFileData((prev: any) => ({
      ...prev,
      items: filteredConnectionKeys,
    }));
  };
  const filteredData = useMemo(() => {
    return fileData?.items?.filter((item: any, index: number) => {
      return item.name.toLowerCase().includes(searchQuery?.toLowerCase());
    });
  }, [fileData, searchQuery]);

  return (
    <Box>
      {(isLoading || isDownloadLoading) && (
        <Loader isLoading={isLoading || isDownloadLoading} />
      )}
      <Box className="text-box-card compact-text-box-card list-page-card">
        <Grid container rowSpacing={1.5} columnSpacing={2.5}>
          <Grid item xs={12} sm={12} md={6} lg={3} xl={3}>
            <label className="label-text">Search Connection Keys</label>
            <form onSubmit={handleSearchSubmit} className="common-search-panel">
              <InputBase
                placeholder="Search..."
                className="search-textbox"
                value={searchQuery}
                onChange={handleSearchInputChange}
                onKeyDown={(event) => {
                  if (event.key === "Enter") {
                    event.preventDefault();
                  }
                }}
              />
              <IconButton className="search-icon" type="submit">
                <Search className="svg_icons" />
              </IconButton>
            </form>
          </Grid>
          <Grid item xs>
            <label className="label-text">&nbsp;</label>
            <Box
              display="flex"
              justifyContent="flex-end"
              sx={{
                columnGap: { md: "20px", sm: "0", xs: "0px" },
                rowGap: {
                  xl: "0",
                  lg: "0",
                  md: "20",
                  sm: "20px",
                  xs: "20px",
                },
                flexDirection: {
                  xl: "row",
                  lg: "row",
                  md: "row",
                  sm: "column",
                  xs: "column",
                },
              }}
            >
              <ButtonComponent
                handelClickEvent={handelClickEvent}
                className="btn-orange"
              >
                <AddSharpIcon sx={{ marginRight: "4px" }} /> Connection Key
              </ButtonComponent>
            </Box>
          </Grid>
        </Grid>
      </Box>

      <FlexBetween>
        <DataTable
          dataColumns={columns}
          dataRows={filteredData}
          loading={isLoading}
          dataListTitle={"Connection Keys List"}
          className="dataTable no-radius hide-progress-icon"
          paginationMode="server"
          rowCount={fileData?.total || 0}
          pageSizeOptions={[25]}
          rowGroupByColumnProps={enableRowGroupByColumnProps}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={360}
        />
      </FlexBetween>
    </Box>
  );
};

export default ConnectionKeys;
