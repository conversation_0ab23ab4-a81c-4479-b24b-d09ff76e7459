import { useState, useEffect } from "react";
import { Box, Tooltip } from "@mui/material";
import RootCauseAnalysisDataGrid from "../../components/DataGrids/RootCauseAnalysisDataGrid";
interface IGenericRootCauseAnalysisPaginatedDataTableProps {
  rootCauseData: any;
  isLoading: boolean;
  isFilterHide: boolean;
  resourceKeys: any;
  pageN: number;
  setPageN: any;
  pageS: number;
  setPageS: any;
  totalPages: number;
  selectedQueryTab: any;
}

const GenericRootCauseAnalysisPaginatedDataTable = ({
  rootCauseData,
  isLoading,
  isFilterHide,
  resourceKeys,
  pageN,
  setPageN,
  pageS,
  setPageS,
  totalPages,
  selectedQueryTab,
}: IGenericRootCauseAnalysisPaginatedDataTableProps) => {
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 25,
  });
  const [columnsData, setColumnsData] = useState<any>([]);
  const [rowsData, setRowsData] = useState<any>([]);
  const [errorMessage, setErrorMessage] = useState<JSX.Element | string | null>(
    null
  );

  useEffect(() => {
    if (
      rootCauseData?.[selectedQueryTab]?.some((item: any) =>
        item.hasOwnProperty("error")
      )
    ) {
      setErrorMessage(rootCauseData?.[selectedQueryTab][0]?.error);
      setColumnsData([]);
      setRowsData([]);
    } else if (rootCauseData?.[selectedQueryTab]?.length > 0) {
      const rows = rootCauseData?.[selectedQueryTab].map(
        (item: any, index: any) => {
          return {
            id: index,
            ...item,
          };
        }
      );
      const columns =
        rootCauseData &&
        rootCauseData[selectedQueryTab] &&
        Object.keys(rootCauseData[selectedQueryTab][0] || {}).map((key) => ({
          field: key,
          headerName: (
            <Tooltip title={key} placement="top" arrow>
              <span className="text-capitalize">
                {`${key.slice(0, 15)}${key.length >= 15 ? "..." : ""}`}
              </span>
            </Tooltip>
          ),
          minWidth: 160,
        }));

      // Get all unique values from base_columns in selectedColumns
      const baseColumns = resourceKeys?.base_columns;

      // Filter rows if any value from valuesToMatch is present in any column of the row, excluding 'id' column

      setColumnsData(columns);
      setRowsData(rows);
    } else {
      setColumnsData([]);
      setRowsData([]);
    }
  }, [rootCauseData, resourceKeys, selectedQueryTab]);

  return (
    <RootCauseAnalysisDataGrid
      dataRows={rowsData}
      dataColumns={columnsData}
      loading={isLoading}
      className="dataTable filterMenuBox no-radius pt-0 bdr-top-0"
      disableColumnFilter={true}
      isFilterHide={isFilterHide}
      tableHeight={180}
      rowCount={rowsData.length}
      pageSizeOptions={[25, 50, 100]}
      paginationModel={paginationModel}
      onPaginationModelChange={setPaginationModel}
      paginationMode="client"
      errorMsg={errorMessage}
    />
  );
};

export default GenericRootCauseAnalysisPaginatedDataTable;
