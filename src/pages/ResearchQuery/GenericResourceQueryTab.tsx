import {
  Autocomplete,
  Button,
  Grid,
  ListItemText,
  TextField,
} from "@mui/material";
import {
  Dispatch,
  SetStateAction,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import {
  IResearchQueryDetail,
  IResearchQuery,
} from "../../types/researchQuery";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import {
  GenericResourceFormSchema,
  GenericResourceQuerySchema,
  resourceQuerySchema,
} from "../../schemas";
import {
  extractVariables,
  replaceQueryToDomainMapped,
} from "../../services/utils";
import * as Yup from "yup";
import GenericQueryBuilderDialog from "./GenericQueryBuilderDialog";

interface GenericResourceQueryTabProps {
  handleTransition: any;
  researchQueryData: IResearchQueryDetail;
  setResearchQueryData: any;
  resourcesData: any;
  renderFrom: string;
  columns: any;
  setColumns: Dispatch<SetStateAction<any>>;
  isEditQuery: any;
  setIsEditQuery: any;
  editFormData: any;
  setQueryType: any;
  setEditFormData: any;
  handleSaveResearchQuery?: (researchQuery: IResearchQuery[]) => void;
  setEditorValue?: any;
  setIsAddQueryBtnEnabled?: any;
  isFromExecution?: boolean;
  setErrors?: any;
}

const GenericResourceQueryTab = ({
  handleSaveResearchQuery,
  researchQueryData,
  setResearchQueryData,

  handleTransition,
  resourcesData,
  renderFrom,
  columns,
  setColumns,
  isEditQuery,
  setIsEditQuery,
  editFormData,
  setQueryType,
  setEditFormData,
  setEditorValue,
  setIsAddQueryBtnEnabled,
  isFromExecution = false,
  setErrors,
}: GenericResourceQueryTabProps) => {
  const {
    linkedServicesData,
    fetchedConnectionKeys,
    setIsLoading,
    editorRef,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    globalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    setGlobalVariables,
  } = useRuleResourceContext();
  const [formData, setFormData] = useState<any>({
    query: "SELECT * FROM ",
  });
  const [resourceColumnId, setResourceColumnId] = useState<any>(0);
  const [queryError, setQueryError] = useState<{ [key: string]: string }>({});
  const [mergedQueryColumns, setMergedQueryColumns] = useState<any>();
  const [resColumns, setResColumns] = useState<any>([]);
  const [variables, setVariables] = useState<any>([]);
  const [openQueryDialogIndex, setOpenQueryDialogIndex] = useState<
    number | null
  >(null);

  const [fetchedresourceColumnData] = useFetchResourceColumnsById({
    resourceColumnId,
    setIsLoading,
  });
  useEffect(() => {
    if (renderFrom === "resource") {
      setFormData((prev: any) => ({
        ...prev,
        source: {
          ...prev?.source,
          resource_id: resourcesData?.id,
        },
      }));
    }
  }, []);

  useEffect(() => {
    setResColumns([]);
    if (fetchedresourceColumnData) {
      const columns =
        fetchedresourceColumnData?.resource_column_properties?.resource_columns?.map(
          (col: any) => ({
            name: col?.column_name,
            value: col?.column_name,
          })
        ) || [];

      setResColumns(columns);
    }
  }, [fetchedresourceColumnData]);

  useEffect(() => {
    if (resColumns && formData?.source) {
      console.log("formData?.source", formData?.source);
      setColumns((prev: any) => ({
        ...prev,
        resourceColumns: [
          {
            name: formData?.source?.resource_code ?? "",
            columns: resColumns,
          },
        ],
        mergedColumns: [],
      }));
    }
  }, [resColumns]);

  useEffect(() => {
    let resColumnId: string | number = 0;
    let resourceCode: string = "";
    if (renderFrom === "rule") {
      resColumnId = resourcesData?.find(
        (option: { id: any }) => option.id == formData?.source?.resource_id
      )?.additional_properties?.resource_column_details_id;
      resourceCode = resourcesData?.find(
        (option: { id: any }) => option.id == formData?.source?.resource_id
      )?.code;
    } else if (renderFrom === "resource") {
      resColumnId =
        resourcesData?.additional_properties?.resource_column_details_id;
    }
    if (!isEditQuery) {
      setQueryBuilderTempValue(
        `SELECT * FROM ${resourceCode ? `<${resourceCode}>` : ""}`
      );
    }
    setResourceColumnId(resColumnId);
  }, [formData?.source?.resource_id, renderFrom]);
  useEffect(() => {
    const queryVariables: any = extractVariables(queryBuilderTempValue);
    setVariables(queryVariables ?? []);
  }, [queryBuilderTempValue]);

  useEffect(() => {
    if (variables?.length > 0 && globalVariables) {
      const globalVars: any = {};
      variables.forEach((variable: any) => {
        if (globalVariables[variable] !== undefined) {
          globalVars[variable] = globalVariables[variable];
        } else if (tempGlobalVariables[variable] !== undefined) {
          globalVars[variable] = tempGlobalVariables[variable];
        } else {
          globalVars[variable] = "";
        }
      });
      // Compare the current tempLocalVariables with the new ones to avoid unnecessary updates
      if (JSON.stringify(tempGlobalVariables) !== JSON.stringify(globalVars)) {
        // setTempLocalVariables(globalVars);
        setTempGlobalVariables(globalVars);
      }
    } else {
      setTempGlobalVariables({});
    }
  }, [variables]);

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await GenericResourceQuerySchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: undefined,
      }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const validateSourceField = async (name: any, value: any) => {
    try {
      const partialFormData = {
        source: {
          [name]: value,
        },
      };
      const validateFieldName = `source.${name}`;
      await resourceQuerySchema.validateAt(validateFieldName, partialFormData);
      // If validation passes, clear any previous errors for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: undefined,
      }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const handleChangeQuery = (event: any) => {
    const { name, value } = event.target;
    setFormData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
    validateField(name, value);
  };
  const handleSaveQuery = async () => {
    try {
      const researchQuery: any = [...researchQueryData?.research_queries];
      // Validate GenericResourceFormSchema first
      try {
        await GenericResourceFormSchema.validate(researchQueryData, {
          abortEarly: false,
        });
      } catch (formErrors: any) {
        console.log("formErrors", formErrors);
        const newErrors: { [key: string]: string } = {};
        if (formErrors.inner) {
          formErrors.inner.forEach(
            (error: { path: string | number; message: string }) => {
              // const fieldName = String(error.path).replace(/^source?\./, "");
              newErrors[error.path] = error.message;
            }
          );
        }
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          ...newErrors,
        }));
        return;
      }

      const updatedFormData = {
        ...formData,
        query: queryBuilderTempValue,
      };

      // Then validate resourceQuerySchema
      await resourceQuerySchema.validate(updatedFormData, {
        abortEarly: false,
      });

      // Variable validations
      const variableSchema: any = {};
      Object.keys(tempGlobalVariables).map((key: any) => {
        if (tempGlobalVariables[key] === "") {
          variableSchema[key] = Yup.string().required(`Please enter ${key}`);
          setQueryError((prevErrors) => ({
            ...prevErrors,
            [key]: `Please enter ${key}`,
          }));
        } else {
          return {
            name: key,
            value: tempGlobalVariables[key],
          };
        }
      });
      const resourceQueryVariableSchema = Yup.object().shape(variableSchema);
      await resourceQueryVariableSchema.validate(tempGlobalVariables, {
        abortEarly: false,
      });
      // setGlobalVariables((prev: any) => ({
      //   ...Object.keys(tempGlobalVariables).reduce(
      //     (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
      //     prev
      //   ),
      // }));

      if (isEditQuery) {
        const index = researchQuery.findIndex(
          (item: any) => item.id === formData.id
        );
        if (index !== -1) {
          researchQuery[index] = {
            id: formData.id,
            name: formData.name,
            query: queryBuilderTempValue,
            //updatedQuery: updatedQuery,
            source: [
              {
                type: "Resource",
                resource_id: formData?.source?.resource_id,
                resource_code: formData?.source?.resource_code,
              },
            ],
          };
        }
      } else {
        researchQuery.push({
          id: researchQuery.length > 0 ? researchQuery.length + 1 : 1,
          name: formData?.name,
          query: queryBuilderTempValue,
          //updatedQuery: updatedQuery,
          source: [
            {
              type: "Resource",
              resource_id: formData?.source?.resource_id,
              resource_code: formData?.source?.resource_code,
            },
          ],
        });
      }

      setResearchQueryData((prev: any) => ({
        ...prev,
        research_queries: researchQuery,
      }));

      setFormData({ name: "" });
      setTempGlobalVariables({});
      setQueryBuilderTempValue(`SELECT * FROM `);
      handleTransition(false);
      setQueryType("");
      setIsEditQuery(false);
      setIsAddQueryBtnEnabled(true);

      handleSaveResearchQuery && handleSaveResearchQuery(researchQuery);
    } catch (validationErrors: any) {
      console.log(validationErrors);
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(/^source?\./, "");
            newErrors[fieldName] = error.message;
          }
        );
      }
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  const handleColumnSelect = (column: string, columnName: string) => {
    if (!editorRef.current) return;
    const editor = editorRef.current.editor;
    if (!editor) return;

    const currentPosition = editor.getCursorPosition();
    const currentLine = editor.session.getLine(currentPosition.row);
    const currentColumn = currentPosition.column;

    let updatedColumn = column;

    if (columnName === "missing") {
      updatedColumn = `[##MISSING## ${column}]`;
    } else if (columnName === "mismatched") {
      updatedColumn = `[##MISMATCHED## ${column}]`;
    } else if (columnName === "merged") {
      updatedColumn = `[${column}]`;
    }

    const updatedLine =
      currentLine.slice(0, currentColumn) +
      updatedColumn +
      " " +
      currentLine.slice(currentColumn);

    setEditorValue(updatedLine);
    editor.session.insert(currentPosition, updatedColumn);

    const newCursorPosition = {
      row: currentPosition.row,
      column: currentColumn + updatedColumn.length + 1,
    };

    editor.moveCursorToPosition(newCursorPosition);
    editor.clearSelection();
    editor.focus();
  };

  useEffect(() => {
    if (isEditQuery) {
      const resColumnId = resourcesData?.find((option: { id: any }) => {
        return option.id == editFormData?.source?.resource_id;
      })?.additional_properties?.resource_column_details_id;
      setResourceColumnId(resColumnId);
      setFormData(editFormData);
    }
  }, [editFormData, resourcesData]);

  return (
    <>
      <Grid item xl={3} lg={3} md={3} sm>
        <TextField
          label={
            <span>
              Query Name
              <span className="required-asterisk">*</span>
            </span>
          }
          name="name"
          type="text"
          fullWidth
          variant="outlined"
          value={formData?.name}
          onChange={handleChangeQuery}
          error={!!queryError?.name}
          helperText={queryError?.name || ""}
          className={`form-control-autocomplete ${
            queryError?.name ? "has-error" : ""
          }`}
          disabled={isFromExecution}
        />
      </Grid>
      {renderFrom === "rule" && (
        <>
          <Grid item xl={3} lg={3} md={3} sm>
            <Autocomplete
              fullWidth
              options={resourcesData ?? []}
              getOptionLabel={(option: any) => option.resource_name || ""}
              value={
                resourcesData?.find(
                  (option: { id: any }) =>
                    option.id == formData?.source?.resource_id
                ) || null
              }
              renderInput={(params) => (
                <TextField
                  label={
                    <span>
                      Select Resources
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  name="resource"
                  style={{ color: "#000000" }}
                  {...params}
                  placeholder="Select..."
                  InputLabelProps={{
                    shrink: true,
                  }}
                  error={!!queryError?.resource_id}
                  helperText={queryError?.resource_id || ""}
                />
              )}
              renderOption={(params: any, item: any) => (
                <li
                  {...params}
                  key={item.key}
                  style={{
                    paddingTop: "2px",
                    paddingBottom: "2px",
                  }}
                >
                  <ListItemText>{item.resource_name}</ListItemText>
                </li>
              )}
              loadingText="Loading..."
              onChange={(event: any, value: any) => {
                setFormData((prev: any) => ({
                  ...prev,
                  source: {
                    ...prev.source,
                    ["resource_id"]: value?.id,
                    ["resource_code"]: value?.code,
                  },
                }));
                setQueryBuilderTempValue(
                  `SELECT * FROM <${value?.code ? value?.code : ""}>`
                );
                setResColumns([]);
                validateSourceField("resource_id", value?.id);
              }}
              // className={`form-control-autocomplete`}
              className={`form-control-autocomplete
                            ${queryError?.resource_id ? "has-error" : ""}
                          `}
              disabled={isFromExecution}
            />
          </Grid>
          {/* {columns?.resourceColumns && columns?.resourceColumns.length > 0 && (
            <Grid item xl={12} lg={12} md={12} sm>
              <Box className="rs-query-column-accordion">
                <CustomAccordion
                  expandId="panel2d-header-support-documents"
                  title={"Resource Columns"}
                  isEnabled={columns?.missingColumns.length > 0}
                  isDefaultExpaned={true}
                  topMargin={1}
                  accodionTopGap={1}
                >
                  <div
                    style={{
                      maxHeight: "300px",
                      width: "100%",
                      overflowY: "auto",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 1,
                      }}
                    >
                      {columns?.resourceColumns?.map((option: any) => (
                        <Chip
                          key={option.name}
                          label={option.name}
                          onClick={() =>
                            handleColumnSelect(option.name, "resource")
                          }
                        />
                      ))}
                    </Box>
                  </div>
                </CustomAccordion>
              </Box>
            </Grid>
          )} */}
        </>
      )}
      {renderFrom === "resource" && (
        <Grid item xl={3} lg={3} md={3} sm>
          <TextField
            label={
              <span>
                Resource Name
                <span className="required-asterisk">*</span>
              </span>
            }
            type="text"
            fullWidth
            variant="outlined"
            className="form-control-autocomplete"
            value={resourcesData?.resource_name}
            disabled={true}
          />
        </Grid>
      )}

      <GenericQueryBuilderDialog
        columns={columns}
        setColumns={setColumns}
        index={15}
        openQueryDialogIndex={openQueryDialogIndex}
        setOpenQueryDialogIndex={setOpenQueryDialogIndex}
        isTextBoxRequied={true}
        queryType={"resource"}
        queryBuilderType={"resource_query"}
      />
      <Grid
        item
        xs
        sx={{
          display: "flex",
          columnGap: "8px",
          textAlign: "right",
          justifyContent: "flex-end",
          alignItems: "flex-end",
        }}
      >
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange btn-dark"
          onClick={() => {
            setFormData({
              name: "",
            });
            setQueryBuilderTempValue(`SELECT * FROM `);
            setQueryType("");
            setQueryError({});
            setEditFormData({
              name: "",
            });
            handleTransition(false);
            setIsEditQuery(false);
            setIsAddQueryBtnEnabled(true);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange"
          onClick={() => handleSaveQuery()}
        >
          <SaveOutlinedIcon /> Save
        </Button>
      </Grid>
    </>
  );
};

export default GenericResourceQueryTab;
