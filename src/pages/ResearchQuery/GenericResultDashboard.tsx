import React, { useEffect, useState } from "react";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { useLocation, useParams } from "react-router-dom";
import Loader from "../../components/Molecules/Loader/Loader";

import GenericRQDashboardDetails from "./GenericRQDashboardDetails";
import GenericRootCauseAnalysis from "./GenericRootCauseAnalysis";
import { getResearchQueryByExecutionId } from "../../services/ResearchQueriesService";

const GenericResultDashboard = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const location = useLocation();
  const [dashboardData, setDashboardData] = useState<any>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [isTriggereBtnPressed, setIsTriggereBtnPressed] = useState(false);
  const [isCommentBtnPressed, setIsCommentBtnPressed] = useState(false);

  const { execution_id: currentResearchQueryResultId, researchQueryId } =
    useParams();
  const getResearchQueryDashboardData = async (
    currentResearchQueryResultId: any
  ) => {
    setIsLoading(true);
    setIsBackdropLoading(true);
    try {
      const result =
        currentResearchQueryResultId > 0 &&
        (await getResearchQueryByExecutionId({ currentResearchQueryResultId }));
      setDashboardData(result);
    } catch (err) {
      setDashboardData({});
    } finally {
      setIsLoading(false);
      setIsBackdropLoading(false);
    }
  };

  useEffect(() => {
    const isResearchQueryExecutionResponse =
      location?.state?.isResearchQueryExecutionResponse;
    if (isResearchQueryExecutionResponse !== true) {
      getResearchQueryDashboardData(currentResearchQueryResultId);
    } else {
      setDashboardData(location?.state?.rowData);
    }
  }, [location.state?.details]);

  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: dashboardData?.generic_research_query_code,
      id: researchQueryId,
      url: `/research-query/view/${researchQueryId}`,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null, url: "" });
    };
  }, [dashboardData, researchQueryId]);

  return (
    <>
      <Loader isLoading={isLoading || isBackdropLoading} />

      <>
        <GenericRQDashboardDetails
          dashboardData={dashboardData}
          currentResearchQueryResultId={currentResearchQueryResultId}
          setIsLoading={setIsLoading}
          setIsTriggereBtnPressed={setIsTriggereBtnPressed}
          setIsCommentBtnPressed={setIsCommentBtnPressed}
        />
        <GenericRootCauseAnalysis
          isLoading={isLoading}
          heading={"Research Query"}
          dashboardData={dashboardData}
        />
      </>
    </>
  );
};

export default GenericResultDashboard;
