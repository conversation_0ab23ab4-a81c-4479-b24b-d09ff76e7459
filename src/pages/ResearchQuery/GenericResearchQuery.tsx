import { useEffect, useState } from "react";
import "../../styles/research-query.scss";
import {
  Button,
  Box,
  IconButton,
  Grid,
  Autocomplete,
  TextField,
  ListItemText,
  Tooltip,
} from "@mui/material";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import Loader from "../../components/Molecules/Loader/Loader";
import { useToast } from "../../services/utils";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import {
  addResearchQuery,
  updateResearchQuery,
} from "../../services/rulesService";
import { GenericQueriesType } from "../../services/constants";
import { replaceQueryFromDomainMapped } from "../../services/utils";
import ReadMoreLess from "../../components/ReadMoreLess";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import useFetchConnectionKeysAll from "../../hooks/useFetchConnectionKeysAll";
import useFetchResourcesByIdMultipleIds from "../../hooks/useFetchResourcesByMultipleIds";
import useFetchAllResources from "../../hooks/useFetchAllResources";
import useFetchResourceColumnsByMultipleIds from "../../hooks/useFetchResourceColumnsByMultipleIds";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
import GenericResourceQueryTab from "./GenericResourceQueryTab";
import GenericExternalQueryTab from "./GenericExternalQueryTab";
import GenericMixedQueryTab from "./GenericMixedQueryTab";
import {
  IResearchQuery,
  IResearchQueryDetail,
} from "../../types/researchQuery";
import { GridColDef } from "@mui/x-data-grid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { useNavigate, useParams } from "react-router-dom";
import DataTable from "../../components/DataGrids/DataGrid";
import { GenericResourceFormSchema } from "../../schemas";
import useFetchAllRules from "../../hooks/useFetchAllRules";
import {
  addGenericResearchQuery,
  getGenericResearchQueryDetail,
  updateGenericResearchQuery,
} from "../../services/ResearchQueriesService";
import useFetchGenericResearchQueryById from "../../hooks/useFetchGenericResearchQueryById";
import ConfirmationDialog from "../../components/Dialogs/ConfirmationDialog";
import { IconDeleteBlueSvg, IconEditBase } from "../../common/utils/icons";
interface AllRules {
  id: number;
  name: string;
}
interface GenericResearchQueryProps {
  researchQuery?: any;
  setResearchQuery?: any;
  isFromExecution?: boolean;
  isEditResearchQuery?: boolean;
  setIsEditResearchQuery?: any;
}

export default function GenericResearchQuery({
  researchQuery,
  setResearchQuery,
  isFromExecution = false,
  isEditResearchQuery = false,
  setIsEditResearchQuery,
}: GenericResearchQueryProps) {
  const navigate = useNavigate();

  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const [columns, setColumns] = useState<any>({
    resourceColumns: [],
    missingColumns: [],
    mismatchedColumns: [],

    mixedQueryData: [
      {
        name: "",
        columns: [],
      },
    ],
  });

  const {
    isLoading,
    setIsLoading,
    setResourceIds,
    allResourcesData,
    setAllResourcesData,
    resourceData,
    setResourceData,
    resourceColIds,
    setResourceColIds,
    resourceColumnsData,
    setResourceColumnsData,
    globalVariables,
    setGlobalVariables,
    linkedServicesData,
    setLinkedServicesData,
    fetchedConnectionKeys,
    setFetchedConnectionKeys,
    setQueryBuilderTempValue,
    setTemporaryQueryValue,
    resourceIds,
    setShowAggregatedColumns,
  } = useRuleResourceContext();
  const { id: currentRQId } = useParams();
  const [currentResearchQueryId, setCurrentResearchQueryId] = useState<
    number | null
  >(null);
  useEffect(() => {
    setCurrentResearchQueryId(Number(currentRQId));
  }, [currentRQId]);

  const { showToast } = useToast();
  const [allRulesData, setAllRulesData] = useState<AllRules[]>([]);
  const [allRules] = useFetchAllRules({ setIsLoading });
  useEffect(() => {
    setAllRulesData(allRules);
  }, [allRules]);
  // Use props if provided, otherwise use component state
  const [localResearchQueryDetailData, setLocalResearchQueryDetailData] =
    useState<any>({});

  const [localIsEditQuery, setLocalIsEditQuery] = useState<any>(false);
  const [formHasChanges, setFormHasChanges] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Determine which data and setter to use based on props
  const researchQueryDetailData = researchQuery || localResearchQueryDetailData;
  const setResearchQueryDetailData =
    setResearchQuery || setLocalResearchQueryDetailData;

  const isEditQuery = isEditResearchQuery || localIsEditQuery;
  const setIsEditQuery = setIsEditResearchQuery || setLocalIsEditQuery;

  // Function to update the parent component's state
  const handleSetResearchQueryDetailData = (newData: any) => {
    if (setResearchQuery) {
      // If we have a setter from props, use it to update the parent
      setResearchQuery(newData);
    } else {
      // Otherwise, update our local state
      setResearchQueryDetailData(newData);
    }
  };

  // Only fetch data if we're not using props
  const [researchQueryDetail] = useFetchGenericResearchQueryById({
    setIsLoading,
    currentRQId,
  });

  useEffect(() => {
    // Only update local state if we're not using props
    if (researchQueryDetail && !researchQuery) {
      setLocalResearchQueryDetailData(researchQueryDetail);
    }
  }, [researchQueryDetail, researchQuery]);
  const [linkedServices] = useFetchLinkedServices({ setIsLoading });
  const [connectionKeys] = useFetchConnectionKeysAll({ setIsLoading });
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [resourcesData] = useFetchResourcesByIdMultipleIds({
    resourceIds,
    setIsLoading,
  });
  const [allResources] = useFetchAllResources({
    setIsLoading,
  });
  const [resourceColumns] = useFetchResourceColumnsByMultipleIds({
    resourceColIds,
    setIsLoading,
  });
  useEffect(() => {
    if (linkedServices) {
      setLinkedServicesData(linkedServices);
    }
  }, [linkedServices]);

  useEffect(() => {
    if (connectionKeys) {
      setFetchedConnectionKeys(connectionKeys);
    }
  }, [connectionKeys]);

  useEffect(() => {
    if (resourcesData) {
      setResourceData(resourcesData);
    }
  }, [resourcesData]);

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources]);

  useEffect(() => {
    if (resourceColumns) {
      setResourceColumnsData(resourceColumns);
    }
  }, [resourceColumns]);

  // useEffect(() => {
  //   setCurrentBreadcrumbPage({
  //     name: ruleData?.name,
  //     id: ruleData?.id,
  //   });
  //   return () => {
  //     setCurrentBreadcrumbPage({ name: "", id: null });
  //   };
  // }, [ruleData]);

  const [queryType, setQueryType] = useState("");
  const [editFormData, setEditFormData] = useState();

  // const [checked, setChecked] = useState(true);
  const [isAddQueryBtnEnabled, setIsAddQueryBtnEnabled] = useState(true);

  const [researchQueryForm, setResearchQueryForm] =
    useState<IResearchQueryDetail>({
      name: "",
      code: "",
      description: "",
      rule_id: null,
      research_queries: [],
    });

  const handleTransition = (_: boolean, actionType?: string) => {
    if (actionType === "add") {
      setIsEditQuery(false);
      setColumns((prev: any) => ({
        ...prev,
        resourceColumns: [],
      }));
    }
  };

  useEffect(() => {
    if (researchQueryDetailData) {
      const updatedResearchQueryData =
        researchQueryDetailData?.research_queries?.map(
          (researchQuery: any, index: number) => {
            return {
              ...researchQuery,
              source: researchQuery?.source,
              id: index + 1,
              updatedQuery: researchQuery?.query,
              query: replaceQueryFromDomainMapped(
                researchQuery?.query,
                columns?.missingColumns ?? []
              ),
            };
          }
        );
      setResearchQueryForm((prev: any) => ({
        ...prev,
        code: researchQueryDetailData?.code,
        name: researchQueryDetailData?.name,
        description: researchQueryDetailData?.description,
        research_queries: updatedResearchQueryData ?? [],
      }));
      if (!isFromExecution) {
        setGlobalVariables(researchQueryDetailData?.variables);
      }
      // Reset form changes state when loading initial data
      setFormHasChanges(false);
    }
  }, [researchQueryDetailData, columns?.missingColumns]);
  // useEffect(() => {
  //   if (ruleData?.rule_schema?.resources?.length > 0) {
  //     setResourceIds(ruleData.rule_schema.resources);
  //   }
  // }, [ruleData]);

  useEffect(() => {
    let resColIds: any[] = [];
    if (resourceData && resourceData.length > 0) {
      resourceData.map((res: any) => {
        resColIds.push(res.additional_properties?.resource_column_details_id);
      });
      setResourceColIds(resColIds);
    }
  }, [resourceData]);
  useEffect(() => {
    let missingColumns: any[] = [];
    let uniqueColumns: any[] = [];
    // let baseColumns: any[] =
    //   ruleData?.rule_schema?.merge_rule?.primary_dataset?.merge_on.map(
    //     (column: any) => ({ name: `Key_${column}`, value: `Key_${column}` })
    //   );
    if (resourceColumnsData && Array.isArray(resourceColumnsData)) {
      resourceColumnsData.forEach((resCol: any) => {
        const matchedRes: any = resourceData.find(
          (res: any) =>
            res?.additional_properties?.resource_column_details_id == resCol?.id
        );
        // if (
        //   ruleData?.rule_schema?.merge_rule?.primary_dataset?.resource_id ==
        //   matchedRes?.id
        // ) {
        //   uniqueColumns =
        //     ruleData?.rule_schema?.merge_rule?.primary_dataset?.merge_on;
        // } else {
        //   const matchingResource =
        //     ruleData?.rule_schema?.merge_rule?.secondary_datasets.find(
        //       (rule: any) => rule.resource_id === matchedRes?.id
        //     );
        //   if (matchingResource) {
        //     uniqueColumns = matchingResource.merge_on;
        //   }
        // }
        const missing = getMappedColumns(
          uniqueColumns,
          resCol?.resource_column_properties?.resource_columns,
          matchedRes ? matchedRes?.code : ""
        );

        missingColumns = [...missingColumns, ...missing];
      });
      // if (
      //   baseColumns &&
      //   baseColumns.length > 0 &&
      //   missingColumns &&
      //   missingColumns.length > 0
      // ) {
      //   const updatedBaseColumns = updateBaseColumns(
      //     baseColumns,
      //     missingColumns
      //   );
      //   missingColumns = updatedBaseColumns;
      // }
    } else {
      missingColumns = getMappedColumns(
        resourceColumnsData?.resource_column_properties?.unique_columns,
        resourceColumnsData?.resource_column_properties?.resource_columns,
        ""
      );
    }
    setColumns((prev: any) => ({
      ...prev,
      missingColumns: missingColumns,
      mismatchedColumns: missingColumns,
    }));
  }, [resourceColumnsData, resourceData]);

  // Commented out as it's not currently used
  /*
  const updateBaseColumns = (baseColumn: any[], missingColumns: any[]) => {
    // Function to extract the resource from the column name
    const getResource = (columnName: string) => {
      return columnName.split(".")[0];
    };

    // Function to normalize and extract the relevant part of the value
    const extractValue = (value: string) => {
      const parts = value.split(".");
      return parts.length > 1 ? parts[1] : parts[0];
    };

    // Group missingColumns by resource
    const resources = missingColumns.reduce((acc, item) => {
      const resource = getResource(item.name);
      if (!acc[resource]) acc[resource] = [];
      acc[resource].push(item);
      return acc;
    }, {});

    // Ensure each resource has the same number of objects
    const resourceKeys = Object.keys(resources);
    const numColumns = resources[resourceKeys[0]].length;

    const updatedBaseColumn = [...baseColumn];

    // Iterate over each index
    for (let i = 0; i < numColumns; i++) {
      const valuesAtIndex = resourceKeys.map((resource) => {
        return extractValue(resources[resource][i].value); // Extract the relevant part of the value
      });

      // Check if all values at this index match and are not null
      const allMatch = valuesAtIndex.every(
        (value) => value !== null && value === valuesAtIndex[0]
      );

      // Update baseColumn if all values match and none of them are null
      if (allMatch && valuesAtIndex[0] !== "null") {
        updatedBaseColumn[i].value = `Key_${valuesAtIndex[0]}`;
        updatedBaseColumn[i].name = `Key_${valuesAtIndex[0]}`;
      }
    }

    return updatedBaseColumn;
  };
  */

  const getMappedColumns = (
    columns: string[],
    allColumns: any[],
    prefix: string
  ): { name: string; value: string }[] => {
    const result: { name: string; value: string }[] = [];
    if (columns) {
      for (const column of columns) {
        const matchedColumn =
          allColumns && allColumns.find((col) => col.column_name === column);

        if (matchedColumn) {
          result.push({
            name: `${prefix}.${matchedColumn.column_name}`,
            value:
              `${prefix}.${matchedColumn.domain_column}` ||
              `${prefix}.${matchedColumn.column_name}`,
          });
        }
      }
    }
    return result;
  };
  const handleSaveResearchQuery = async (updatedQueries?: IResearchQuery[]) => {
    try {
      if (isFromExecution) {
        // Create a new object with the updated research queries
        const updatedResearchQueryData = {
          ...researchQueryDetailData,
          research_queries: updatedQueries,
        };

        // Use the handler to update the parent's state
        handleSetResearchQueryDetailData(updatedResearchQueryData);
        return;
      }

      const payload = {
        ...researchQueryForm,
        research_queries: updatedQueries
          ? updatedQueries
          : researchQueryForm?.research_queries,
        variables: globalVariables,
      };

      await GenericResourceFormSchema.validate(researchQueryForm, {
        abortEarly: false,
      });
      if (
        researchQueryDetailData &&
        Object.keys(researchQueryDetailData).length > 0
      ) {
        //navigate(`/research-query`);
        updateGenericResearchQuery({
          currentResearchQueryId,
          payload,
        })
          .then((response: any) => {
            if (response) {
              showToast("Research Query updated successfully!", "success");
              setFormHasChanges(false);
            }
          })
          .catch((err: Error) => {
            console.error(err);
            showToast(`Cannot update Research Query`, "error");
          });
      } else {
        addGenericResearchQuery(payload)
          .then(async (response: any) => {
            if (response) {
              const result = await getGenericResearchQueryDetail(response?.id);
              setCurrentResearchQueryId(response?.id);
              setResearchQueryDetailData(result);
              showToast("Research Query created successfully!", "success");
              setFormHasChanges(false);
            }
          })
          .catch((error: any) => {
            showToast(`Cannot create Research Query`, "error");
          });
      }
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            // const fieldName = String(error.path).replace(/^source?\./, "");
            newErrors[error.path] = error.message;
          }
        );
      }
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const getLinkedServiceCodebyId = (id: number | string | null | undefined) => {
    const linkedData = linkedServicesData?.find((item: any) => item.id === id);
    return linkedData ? linkedData.name : null;
  };

  const handleExecuteQuery = () => {
    if (currentResearchQueryId) {
      navigate(
        `/research-query/research-query-execution/${currentResearchQueryId}`
      );
    }
  };
  const getConnectionKeybyId = (id: number | string | null | undefined) => {
    const connectionData = fetchedConnectionKeys?.find(
      (item: any) => item.id === id
    );

    return connectionData ? connectionData?.name : null;
  };

  const getResearchColumns = (): GridColDef[] => {
    // Base columns that are always shown
    const baseColumns: GridColDef[] = [
      {
        ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
        renderCell: (params: any) => (
          <IconButton
            size="small"
            tabIndex={-1}
            disabled={params?.row?.source[0]?.type === "Mixed" ? false : true}
            onClick={() => {
              if (expandedRow.includes(params.row.id)) {
                setExpandedRow((prev: any) =>
                  prev.filter((item: any) => item !== params.row.id)
                );
              } else {
                setExpandedRow((prev: any) => [...prev, params.row.id]);
              }
            }}
          >
            {params?.row?.source[0]?.type === "Mixed" ? (
              <>
                <ExpandMoreIcon
                  sx={{
                    transform: `rotateZ(${
                      expandedRow.includes(params.row.id) ? 180 : 0
                    }deg)`,
                    transition: (theme) =>
                      theme.transitions.create("transform", {
                        duration: theme.transitions.duration.shortest,
                      }),
                  }}
                  fontSize="inherit"
                />
              </>
            ) : null}
          </IconButton>
        ),
      },
      {
        field: "id",
        headerName: "Id",
        minWidth: 50,
        renderCell: (params: any) => <span>{params.value}</span>,
      },
      {
        field: "name",
        headerName: "Query Name",
        minWidth: 250,
        flex: 1,
        renderCell: (params: any) => {
          return <LimitChractersWithTooltip value={params?.value} />;
        },
      },
      {
        field: "query",
        headerName: "SQL Query",
        minWidth: 250,
        groupable: false,
        flex: 1,
        renderCell: (params: any) => {
          return (
            <>
              {params.value && params.value.length > 50 ? (
                <ReadMoreLess
                  data={params?.value?.replace(/"/g, "")}
                  charLimit={50}
                />
              ) : (
                params?.value?.replace(/"/g, "")
              )}
            </>
          );
        },
      },
    ];

    // Add columns based on execution mode
    if (!isFromExecution) {
      // Add Query Type column when not in execution mode
      baseColumns.splice(2, 0, {
        field: "source",
        headerName: "Query Type",
        minWidth: 140,
        renderCell: (params: any) => {
          return (
            <LimitChractersWithTooltip
              value={params?.value[0]?.type?.replace("_", " ")}
            />
          );
        },
      });

      // Add Linked Service column when not in execution mode
      baseColumns.push({
        field: "linked_service_id",
        headerName: "Linked Service",
        minWidth: 150,
        renderCell: (params: any) => {
          const linkedServiceCodebyId =
            getLinkedServiceCodebyId(
              params?.row?.source[0]?.linked_service_id
            ) ?? "";

          return (
            <LimitChractersWithTooltip value={linkedServiceCodebyId ?? ""} />
          );
        },
      });

      // Add Connection Key column when not in execution mode
      baseColumns.push({
        field: "connection_key_id",
        headerName: "Connection Key",
        minWidth: 150,
        renderCell: (params: any) => {
          const connectionKeybyId =
            getConnectionKeybyId(params?.row?.source[0]?.connection_key_id) ??
            "";

          return <LimitChractersWithTooltip value={connectionKeybyId ?? ""} />;
        },
      });
    }

    // Always add Action column with edit button
    // Only show delete button when not in execution mode
    baseColumns.push({
      field: "action",
      headerName: "Action",
      width: 130,
      align: "center",
      headerAlign: "center",
      groupable: false,
      renderCell: (params: any) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const handleEditQuery = () => {
          const formData: any = researchQueryForm?.research_queries.find(
            (query) => query.id === params.row.id
          );

          let linkedService: any;
          let connectionKey: any;
          const isMixed = formData.source[0].type === "Mixed";
          const sources = isMixed ? formData.source : [formData.source[0]];
          linkedService = sources
            .map((src: any) =>
              linkedServicesData?.find(
                (linked: any) => linked?.id === src?.linked_service_id
              )
            )
            .filter(Boolean);

          connectionKey = sources
            .map((src: any) =>
              fetchedConnectionKeys?.find(
                (key: any) => key?.id === src?.connection_key_id
              )
            )
            .filter(Boolean);
          // Optional: if not Mixed, simplify the result to a single item instead of an array
          if (!isMixed) {
            linkedService = linkedService[0];
            connectionKey = connectionKey[0];
          }
          const updatedFormData: any = {
            ...formData,
            source:
              formData.source[0].type == "Mixed"
                ? formData.source
                : formData.source[0],
            linked_service: linkedService,
            connection_key: connectionKey,
          };
          setEditFormData(updatedFormData);
          setQueryBuilderTempValue(formData?.query);
          setTemporaryQueryValue(formData?.query);
          setIsEditQuery(true);
          //handleTransition(true);
          setQueryType(params?.row?.source[0]?.type);
        };

        return (
          <>
            <IconButton onClick={handleEditQuery}>
              <IconEditBase />
            </IconButton>
            {!isFromExecution && (
              <IconButton
                onClick={() => {
                  setSelectedQueryToDelete(params.row);
                  setOpenValidationExecutionConfirmation(true);
                }}
              >
                <IconDeleteBlueSvg />
              </IconButton>
            )}
          </>
        );
      },
    });

    return baseColumns;
  };

  const researchColumns: GridColDef[] = getResearchColumns();
  const handleSelectQueryType = (value: any) => {
    if (value) {
      setQueryType(value);
      if (value === "Resource") {
        setQueryBuilderTempValue(`SELECT * FROM `);
      } else if (value === "External") {
        setQueryBuilderTempValue(`SELECT * FROM `);
      } else if (value === "Mixed") {
        setQueryBuilderTempValue("");
      }
    } else {
      setQueryType("");
    }
  };
  const handleChangeFormData = (event: any) => {
    const { name, value } = event.target;
    setResearchQueryForm((prev) => ({
      ...prev,
      [name]: value,
    }));
    setFormHasChanges(true);
    name !== "description" && validateField(name, value);
  };
  const handleRuleChange = (_: any, selectedOption: any | null) => {
    setResearchQueryForm((prev) => ({
      ...prev,
      rule_id: selectedOption?.id || null,
    }));
    setFormHasChanges(true);
    validateField("rule_id", selectedOption?.id || null);
  };
  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...researchQueryForm, [name]: value };
      await GenericResourceFormSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: undefined,
      }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const [
    openValidationExecutionConfirmation,
    setOpenValidationExecutionConfirmation,
  ] = useState(false);
  const [selectedQueryToDelete, setSelectedQueryToDelete] = useState<any>(null);

  const handleValidationExecutionDelete = () => {
    setOpenValidationExecutionConfirmation(false);
    if (!selectedQueryToDelete) return;

    const updatedQueryData = researchQueryForm?.research_queries.filter(
      (query) => query.id !== selectedQueryToDelete.id
    );
    setResearchQueryForm((prev: any) => ({
      ...prev,
      research_queries: updatedQueryData,
    }));

    if (isFromExecution) {
      // Create a new object with the updated research queries
      const updatedResearchQueryData = {
        ...researchQueryDetailData,
        research_queries: updatedQueryData,
      };

      // Use the handler to update the parent's state
      handleSetResearchQueryDetailData(updatedResearchQueryData);
      showToast(
        `${
          selectedQueryToDelete?.name ?? "Research Query"
        } deleted successfully!`,
        "success"
      );
      setSelectedQueryToDelete(null);
      return;
    }

    const payload = {
      ...researchQueryForm,
      research_queries: updatedQueryData,
      variables: globalVariables,
    };
    updateGenericResearchQuery({
      currentResearchQueryId: researchQueryDetailData.id,
      payload,
    })
      .then((response: any) => {
        if (response) {
          showToast(
            `${
              selectedQueryToDelete?.name ?? "Research Query"
            } deleted successfully!`,
            "success"
          );
        }
      })
      .catch((err: Error) => {
        console.error("Error deleting research query:", err);
        showToast(`Cannot delete Research Query`, "error");
      });

    setSelectedQueryToDelete(null); // clear state
  };

  const handleCancelClick = () => {
    if (researchQueryDetailData) {
      const updatedResearchQueryData =
        researchQueryDetailData?.research_queries?.map(
          (researchQuery: any, index: number) => ({
            ...researchQuery,
            source: researchQuery?.source,
            id: index + 1,
            updatedQuery: researchQuery?.query,
            query: replaceQueryFromDomainMapped(
              researchQuery?.query,
              columns?.missingColumns ?? []
            ),
          })
        );

      setResearchQueryForm((prev: any) => ({
        ...prev,
        code: researchQueryDetailData?.code,
        name: researchQueryDetailData?.name,
        description: researchQueryDetailData?.description || "",
        research_queries: updatedResearchQueryData ?? [],
      }));

      setGlobalVariables(researchQueryDetailData?.variables);
    }

    setFormHasChanges(false);
    setIsEditQuery(false);
  };

  return (
    <>
      <Loader isLoading={isLoading} />
      {!isFromExecution && (
        <Box className="rule-domain-chart-box">
          <h3>Details</h3>
          <Grid container columnSpacing={1.5} rowSpacing={2.5}>
            <Grid item xl={3} lg={3} md={3} sm>
              <TextField
                label={
                  <span>
                    Name
                    <span className="required-asterisk">*</span>
                  </span>
                }
                type="text"
                fullWidth
                variant="outlined"
                className={`form-control-autocomplete ${
                  errors?.name ? "has-error" : ""
                }`}
                name="name"
                value={researchQueryForm?.name}
                onChange={handleChangeFormData}
                error={!!errors?.name}
                helperText={errors?.name}
              />
            </Grid>
            <Grid item xl={3} lg={3} md={3} sm>
              <TextField
                label={
                  <span>
                    Code
                    <span className="required-asterisk">*</span>
                  </span>
                }
                type="text"
                fullWidth
                variant="outlined"
                className={`form-control-autocomplete ${
                  errors?.code ? "has-error" : ""
                }`}
                name="code"
                value={researchQueryForm?.code}
                onChange={handleChangeFormData}
                error={!!errors?.code}
                helperText={errors?.code}
                disabled={currentRQId ? true : false}
              />
            </Grid>
            <Grid item xl={3} lg={3} md={3} sm>
              <TextField
                label={
                  <span>
                    Description
                    {/* <span className="required-asterisk">*</span> */}
                  </span>
                }
                type="text"
                fullWidth
                variant="outlined"
                className={`form-control-autocomplete`}
                name="description"
                value={researchQueryForm?.description}
                onChange={handleChangeFormData}
                error={!!errors?.description}
                helperText={errors?.description}
              />
            </Grid>
            <Grid item xl={3} lg={3} md={3} sm>
              <Autocomplete
                fullWidth
                options={Array.isArray(allRulesData) ? allRulesData : []}
                getOptionLabel={(option: any) => option.name}
                value={
                  allRulesData?.find(
                    (option: any) => option.id === researchQueryForm?.rule_id
                  ) || null
                }
                renderInput={(params) => (
                  <TextField
                    label={<span>Select Rule</span>}
                    name="rule_id"
                    style={{ color: "#000000" }}
                    {...params}
                    placeholder="Select..."
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                )}
                renderOption={(params: any, item: any) => (
                  <li
                    {...params}
                    key={item.key}
                    style={{
                      paddingTop: "2px",
                      paddingBottom: "2px",
                    }}
                  >
                    <ListItemText>{item.name}</ListItemText>
                  </li>
                )}
                loadingText="Loading..."
                onChange={handleRuleChange}
                className={`form-control-autocomplete`}
              />
            </Grid>

            <Grid
              item
              xs
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                columnGap: 1.5,
              }}
            >
              {isAddQueryBtnEnabled && queryType === "" && (
                <>
                  {researchQueryForm?.research_queries?.length > 0 ? (
                    <>
                      {formHasChanges || isEditQuery ? (
                        <>
                          <Button
                            variant="contained"
                            color="secondary"
                            className="btn-orange btn-dark"
                            onClick={handleCancelClick}
                          >
                            Cancel
                          </Button>

                          <Button
                            variant="contained"
                            color="secondary"
                            className="btn-orange"
                            onClick={() => handleSaveResearchQuery()}
                          >
                            <SaveOutlinedIcon /> Save
                          </Button>
                        </>
                      ) : (
                        currentResearchQueryId && (
                          <Tooltip title="Execute Query" placement="top" arrow>
                            <Button
                              variant="contained"
                              color="secondary"
                              className="btn-orange"
                              onClick={handleExecuteQuery}
                            >
                              Execute Query
                            </Button>
                          </Tooltip>
                        )
                      )}
                    </>
                  ) : (
                    <>
                      <Button
                        variant="contained"
                        color="secondary"
                        className="btn-orange"
                        onClick={() => handleSaveResearchQuery()}
                      >
                        <SaveOutlinedIcon /> Save
                      </Button>
                    </>
                  )}

                  <Button
                    variant="contained"
                    color="secondary"
                    className="btn-orange"
                    onClick={() => setIsAddQueryBtnEnabled(false)}
                  >
                    + &nbsp;Add Query
                  </Button>
                </>
              )}
            </Grid>

            {/* )} */}
          </Grid>
        </Box>
      )}

      {(!isAddQueryBtnEnabled || isEditQuery) && (
        <Box className="rule-domain-chart-box" sx={{ marginTop: "6px" }}>
          <h3>Configuration</h3>
          <Grid container columnSpacing={1.5} rowSpacing={2.5}>
            {!isAddQueryBtnEnabled && (
              <Grid item xl={3} lg={3} md={3}>
                <Autocomplete
                  fullWidth
                  options={GenericQueriesType ?? []}
                  getOptionLabel={(option: any) =>
                    option ? `${option.replace("_", " ")} Queries` : ""
                  }
                  value={
                    GenericQueriesType?.find((option) => option == queryType) ||
                    null
                  }
                  renderInput={(params) => (
                    <TextField
                      label={
                        <span>
                          Select Query Type
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      name="queryType"
                      style={{ color: "#000000" }}
                      {...params}
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  )}
                  renderOption={(params: any, item: any) => (
                    <li
                      {...params}
                      key={item.key}
                      style={{
                        paddingTop: "2px",
                        paddingBottom: "2px",
                      }}
                    >
                      <ListItemText>
                        <span
                          style={{ textTransform: "capitalize" }}
                        >{`${item.replace("_", " ")} Queries`}</span>
                      </ListItemText>
                    </li>
                  )}
                  loadingText="Loading..."
                  onChange={(_, value) => {
                    handleSelectQueryType(value);
                    setShowAggregatedColumns(undefined);
                  }}
                  className={`form-control-autocomplete`}
                />
              </Grid>
            )}

            {queryType === "Resource" && (
              <GenericResourceQueryTab
                researchQueryData={researchQueryForm}
                setResearchQueryData={setResearchQueryForm}
                handleTransition={handleTransition}
                resourcesData={allResourcesData}
                renderFrom="rule"
                columns={columns}
                setColumns={setColumns}
                isEditQuery={isEditQuery}
                setIsEditQuery={setIsEditQuery}
                editFormData={editFormData}
                setEditFormData={setEditFormData}
                setQueryType={setQueryType}
                handleSaveResearchQuery={(researchQuery) =>
                  handleSaveResearchQuery(researchQuery)
                }
                setEditorValue={setQueryBuilderTempValue}
                setIsAddQueryBtnEnabled={setIsAddQueryBtnEnabled}
                isFromExecution={isFromExecution}
                setErrors={setErrors}
              />
            )}
            {queryType === "External" && (
              <GenericExternalQueryTab
                researchQueryData={researchQueryForm}
                setResearchQueryData={setResearchQueryForm}
                handleTransition={handleTransition}
                columns={columns}
                setColumns={setColumns}
                isEditQuery={isEditQuery}
                setIsEditQuery={setIsEditQuery}
                editFormData={editFormData}
                setEditFormData={setEditFormData}
                setQueryType={setQueryType}
                handleSaveResearchQuery={(researchQuery) =>
                  handleSaveResearchQuery(researchQuery)
                }
                setIsAddQueryBtnEnabled={setIsAddQueryBtnEnabled}
                isFromExecution={isFromExecution}
                setErrors={setErrors}
              />
            )}

            {queryType === "Mixed" && (
              <GenericMixedQueryTab
                resourcesData={allResourcesData}
                columns={columns}
                setColumns={setColumns}
                isEditQuery={isEditQuery}
                setIsEditQuery={setIsEditQuery}
                researchQueryData={researchQueryForm}
                setResearchQueryData={setResearchQueryForm}
                handleTransition={handleTransition}
                setQueryType={setQueryType}
                editFormData={editFormData}
                setEditFormData={setEditFormData}
                handleSaveResearchQuery={(researchQuery) =>
                  handleSaveResearchQuery(researchQuery)
                }
                setIsAddQueryBtnEnabled={setIsAddQueryBtnEnabled}
                isFromExecution={isFromExecution}
                setErrors={setErrors}
              />
            )}
          </Grid>
          {!isAddQueryBtnEnabled && queryType === "" && (
            <Grid
              item
              xs
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                columnGap: 1.5,
              }}
            >
              <Button
                variant="contained"
                color="secondary"
                className="btn-orange btn-dark"
                onClick={() => {
                  setIsAddQueryBtnEnabled(true);
                  setQueryType("");
                }}
              >
                Cancel
              </Button>
            </Grid>
          )}
        </Box>
      )}

      <Box className="pagination-not-required">
        {researchQueryForm?.research_queries?.length > 0 &&
          (!isFromExecution || (isFromExecution && !isEditQuery)) && (
            <DataTable
              dataRows={researchQueryForm?.research_queries}
              dataColumns={researchColumns}
              dataListTitle=""
              className="dataTable hide-progress-icon no-buttons"
              paginationMode="client"
              pageSizeOptions={[25]}
              singlePageMaxHeightDiff={307}
              isExportButtonDisabled={true}
              isPaginationRequired={false}
              {...(isFromExecution
                ? {
                    minHeight: "inherit",
                    topMargin: 0,
                  }
                : { minHeight: "inherit", topMargin: "6px" })}
            />
          )}
      </Box>
      <ConfirmationDialog
        title={"Confirm removing Query"}
        dialogContent={"Are you sure you want to delete this query?"}
        handleCancel={() => setOpenValidationExecutionConfirmation(false)}
        openConfirmation={openValidationExecutionConfirmation}
        handleConfirm={handleValidationExecutionDelete}
        isShowClassMb0={false}
      />
    </>
  );
}
