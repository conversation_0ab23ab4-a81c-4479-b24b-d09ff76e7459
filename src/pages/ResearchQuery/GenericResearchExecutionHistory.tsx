import React, { useEffect, useState } from "react";
import useFetchExecutionHistory from "../../hooks/useFetchExecutionHistory";
import { GridColDef, GridExpandMoreIcon } from "@mui/x-data-grid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { IconButton, Tooltip } from "@mui/material";
import {
  downloadRulesHistoryFile,
  getFormatedDate,
  getFormattedDateTime,
  getFormattedDateTimeWithAMPM,
  getFormattedTime,
  useToast,
} from "../../services/utils";
import EHistoryDataTable from "../../components/DataGrids/EHistoryDataGrid";
import { useNavigate } from "react-router-dom";
import { Box } from "@mui/material";
import dayjs from "dayjs";
import { enableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";
import Loader from "../../components/Molecules/Loader/Loader";

import {
  IconDeleteBlueSvg,
  IconEyeBase,
  IconFilterSvg,
  IconReRunBlue,
} from "../../common/utils/icons";
// import useFetchIncidentByExecutionId from "../../hooks/useFetchIncidentByExecutionId";
import FilterSearch from "../../components/Molecules/FilterSearch/FilterSearch";
import ConsolidatedReportsFilter from "../../components/Molecules/FilterSearch/ConsolidatedReportsFilter";
import { deleteComparisonExecutionbyID } from "../../services/rulesService";
import ConfirmationDialog from "../../components/Dialogs/ConfirmationDialog";
import DashboardJobstatusIcon from "../../common/utils/DashboardJobstatusIcon";
import useSyncNotifications from "../../hooks/useSyncNotifications";
// import useWebSocket from "../../hooks/useWebSocket"; // No longer needed
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
import useFetchResearchQueryHistory from "../../hooks/useFetchResearchQueryHistory";
import { deleteExecutionResultByExecutionId } from "../../services/ResearchQueriesService";

interface ResearchQueryHistoryData {
  domainId: number | null;
  ruleId: number | null;
  fromDate: string | undefined;
}
interface RuleSearchData {
  search_by: string | null;
  search_query: string | null;
  search_date?: string;
}
const GenericResearchExecutionHistory = () => {
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const queryParams = new URLSearchParams(window.location.search);

  const navigate = useNavigate();
  const { showToast } = useToast();
  const [isloading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>({
    items: [],
    total: 0,
    page: 0,
    size: 0,
    pages: 0,
  });
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [formData, setFormData] = useState({
    run_name: "",
    run_date: dayjs().subtract(1, "day"),
    report_type: "",
  });
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const [searchFilterData, setSearchFilterData] = useState<any[]>([]);
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [isConsolidatedFilterVisible, setIsConsolidatedFilterVisible] =
    useState(false);

  // const [incidentData, setIncidentData] = useState<any>([]);
  // const [incidentResultIds, setIncidentResultIds] = useState();

  const [
    openComparisonExecutionConfirmation,
    setOpenComparisonExecutionConfirmation,
  ] = useState(false);

  const [deleteExecutionId, setDeleteExecutionId] = useState<number | null>(
    null
  );
  const [
    openValidationExecutionConfirmation,
    setOpenValidationExecutionConfirmation,
  ] = useState(false);
  const [selectedQueryToDelete, setSelectedQueryToDelete] = useState<any>(null);

  const [researchQueryHistoryParams, setResearchQueryHistoryParams] =
    useState<ResearchQueryHistoryData>({
      domainId: Number(queryParams.get("domainId")),
      ruleId: Number(queryParams.get("ruleId")),
      fromDate: queryParams.get("fromDate")?.toString(),
    });
  const [researchQueryList] = useFetchResearchQueryHistory({
    setIsLoading: setIsBackdropLoading,
    page,
    pSize,
    domainId: researchQueryHistoryParams.domainId,
    ruleId: researchQueryHistoryParams.ruleId,
    fromDate: researchQueryHistoryParams.fromDate,
    searchFilterData,
  });

  // Get handleStorageChange from useSyncNotifications
  // This will automatically sync with notifications from the context
  useSyncNotifications(setFileData, fileData);

  const handleRuleExecutionList = (data: any) => {
    if (data && data.items) {
      const newData = data.items.map((row: any) => ({
        ...row,
        execution_date: getFormatedDate(row.execution_time),
        execution_name:
          row?.complete_execution_params?.rule_execution_request?.request_body
            ?.execution_name,
      }));
      return { ...data, items: newData };
    }
    return data;
  };
  // const [fetchIncidentData] = useFetchIncidentByExecutionId({
  //   setIsLoading,
  //   data: incidentResultIds,
  // });
  // useEffect(() => {
  //   setIncidentData(fetchIncidentData);
  // }, [fetchIncidentData, page]);

  useEffect(() => {
    if (
      researchQueryList &&
      researchQueryList.items &&
      researchQueryList.items.length >= 0
    ) {
      // const resultIdArr = researchQueryList?.items?.map((row: any) => row.id);
      // setIncidentResultIds(resultIdArr);
      setFileData(researchQueryList);
    }
  }, [researchQueryList]);

  // useEffect(() => {
  //   const updatedFileData = handleRuleExecutionList(executionList);
  //   // Update fileData state
  //   if (updatedFileData?.items?.length > 0) {
  //     const newData = updatedFileData.items.map((file: any) => {
  //       const fileItem: any =
  //         incidentData &&
  //         incidentData.length > 0 &&
  //         incidentData.find((incidentItem: any) => {
  //           return Number(incidentItem?.ExecutionId) === Number(file.id);
  //         });
  //       if (fileItem) {
  //         return {
  //           ...file,
  //           IncidentStatus: fileItem?.IncidentStatus,
  //         };
  //       }
  //       return file;
  //     });
  //     setFileData({ ...updatedFileData, items: newData });
  //   }
  // }, [incidentData, page]);

  useEffect(() => {
    const handleStorageChange = () => {
      const storedNotifications = localStorage.getItem("notifications");

      if (storedNotifications && fileData?.items?.length > 0) {
        try {
          const notifications = JSON.parse(storedNotifications);

          setFileData((prevFileData: any) => {
            const updatedItems = prevFileData.items
              .map((item: any) => {
                const matchingNotification = notifications.find(
                  (notification: any) =>
                    (notification?.execution_type === "rule" ||
                      notification?.execution_type === "rule_rerun") &&
                    notification?.execution_id === item?.id
                );

                if (matchingNotification?.execution_status === "failed") {
                  return null; // Remove this item
                }

                if (matchingNotification?.execution_status) {
                  return {
                    ...item,
                    job_status: matchingNotification.execution_status,
                  };
                }

                return item;
              })
              .filter(Boolean); // Remove nulls (failed items)

            const hasChanges =
              updatedItems.length !== prevFileData.items.length ||
              updatedItems.some(
                (newItem: any, index: number) =>
                  newItem.job_status !== prevFileData.items[index]?.job_status
              );

            return hasChanges
              ? { ...prevFileData, items: updatedItems }
              : prevFileData;
          });
        } catch (error) {
          console.error(
            "Error parsing notifications from localStorage:",
            error
          );
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    const checkInterval = setInterval(handleStorageChange, 1000);
    handleStorageChange();

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      clearInterval(checkInterval);
    };
  }, [researchQueryList]);

  /**
   * Set table coloums for execution history
   */
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => <></>,
    },
    {
      field: "id",
      headerName: "Execution Id",

      minWidth: 90,
      groupable: false,
      renderCell: (params: any) => {
        return (
          <Tooltip
            title={`Run Id: ${params?.row?.run_id}`}
            placement="top"
            arrow
          >
            <span>{params.value}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "run_id",
      headerName: "Run Id",

      minWidth: 90,
      groupable: false,
      renderCell: (params: any) => {
        return <span>{params.value}</span>;
      },
    },
    {
      field: "run_name",
      headerName: "Run Name",
      minWidth: 90,
      groupable: false,
      renderCell: (params: any) => {
        return <span>{params.value}</span>;
      },
    },
    {
      field: "generic_research_query_code",
      headerName: "Research Query Code",
      flex: 1,
      minWidth: 120,
      groupable: false,
      renderCell: (params: any) => {
        return <span>{params.value}</span>;
      },
    },
    {
      field: "execution_name",
      headerName: "Execution Name",
      flex: 1,
      minWidth: 120,
      groupable: false,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },

    {
      field: "execution_time",
      headerName: "Execution Date & Time",
      flex: 1,
      minWidth: 180,
      groupable: false,
      renderCell: (params) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const executionTime = params?.value?.execution_time || params.value;
        const formattedTime = getFormattedDateTimeWithAMPM(executionTime);
        return (
          <div>
            <div>{formattedTime}</div>
          </div>
        );
      },
    },
    {
      field: "execution_status",
      headerName: "Status",
      headerAlign: "center",
      align: "center",
      minWidth: 90,
      groupable: false,
      renderCell: (params: any) => {
        return <DashboardJobstatusIcon status={params?.value} />;
      },
    },

    {
      field: "action",
      headerName: "Action",
      align: "left",
      headerAlign: "left",
      minWidth: 90,
      groupable: false,
      renderCell: (params) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const handleViewClick = (incidentTab?: any) => {
          const basePath = `/research-query/${params?.row?.generic_research_query_id}/${params?.row.id}/dashboard`;

          const navigatePath = incidentTab
            ? `${basePath}#incidentTab`
            : basePath;

          navigate(navigatePath, {
            state: {
              rowData: params?.row,
            },
          });
        };
        return (
          <>
            <Tooltip title="View Dashboard" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => handleViewClick()}
              >
                <IconEyeBase />
              </IconButton>
            </Tooltip>
            <IconButton
              onClick={() => {
                setSelectedQueryToDelete(params.row);
                setOpenValidationExecutionConfirmation(true);
              }}
            >
              <IconDeleteBlueSvg />
            </IconButton>
          </>
        );
      },
    },
  ];

  const handleSubmitReport = async (event: any) => {
    // event.preventDefault();
    navigate(
      `/rules-execution-history/consolidate-results?runName=${formData.run_name}&reportType=${formData.report_type}&runDate=${formData?.run_date}`
    );
  };

  const handleDelete = async () => {
    if (deleteExecutionId) {
      try {
        setIsDeleteLoading(true);
        await deleteComparisonExecutionbyID(deleteExecutionId);
        const deletedRow = fileData.items.find(
          (item: any) => item.id === deleteExecutionId
        );
        setFileData((prev: any) => ({
          ...prev,
          items: prev?.items?.filter(
            (prevItem: any) => prevItem.id !== deleteExecutionId
          ),
        }));
        showToast(`${deleteExecutionId} deleted successfully`, "success");
      } catch (error) {
        console.error("Error deleting execution:", error);
      } finally {
        setIsDeleteLoading(false);
      }
    }
  };

  const handleValidationExecutionDelete = async () => {
    setOpenValidationExecutionConfirmation(false);
    if (!selectedQueryToDelete) return;
    setIsLoading(true);
    try {
      await deleteExecutionResultByExecutionId(selectedQueryToDelete.id);
      const updatedList = fileData?.items?.filter(
        (item: any) => item.id !== selectedQueryToDelete.id
      );
      setFileData((prev: any) => ({
        ...prev,
        items: updatedList,
      }));

      showToast(
        `${selectedQueryToDelete.generic_research_query_code} deleted successfully`,
        "success"
      );
    } catch (error) {
      console.error("Error deleting query:", error);
      showToast("Failed to delete the execution. Please try again.", "error");
    } finally {
      setIsLoading(false);
      setSelectedQueryToDelete(null);
    }
  };

  return (
    <>
      {(isBackdropLoading || isDeleteLoading) && <Loader isLoading={true} />}
      <FilterSearch
        setSearchFilterData={setSearchFilterData}
        isFilterVisible={isFilterVisible}
        setIsFilterVisible={setIsFilterVisible}
        FilterFor="rule"
        searchFilterData={searchFilterData}
        requiredFields={[
          "generic_research_id",
          "generic_research_code",
          "run",
          "execution_status",
          "generic_execution_id",
        ]}
      />
      <ConsolidatedReportsFilter
        setFormData={setFormData}
        isFilterVisible={isConsolidatedFilterVisible}
        setIsFilterVisible={setIsConsolidatedFilterVisible}
        formData={formData}
        handleSubmitReport={handleSubmitReport}
      />
      <div className="execution-history-btn-group">
        <Box className="transaction-btn-group">
          <button
            className="filters-btn btn-orange btn-border"
            onClick={() => setIsFilterVisible(true)}
          >
            <IconFilterSvg />
            Filter
          </button>
          {/* <button
            className="btn-orange btn-border cursor-pointer"
            onClick={() => setIsConsolidatedFilterVisible(true)}
            color="secondary"
          >
            Consolidated Reports
          </button> */}
        </Box>
      </div>
      <EHistoryDataTable
        dataColumns={columns}
        dataRows={fileData?.items}
        loading={isloading}
        setLoading={setIsLoading}
        dataListTitle={"Research Query History"}
        isExportButtonRequired={true}
        className="dataTable no-radius hide-progress-icon"
        rowCount={fileData?.total}
        pageSizeOptions={[25]}
        paginationModel={{
          page: page - 1,
          pageSize: pSize,
        }}
        onPaginationModelChange={(params: any) => {
          if (params.pageSize !== pSize || params.page !== page - 1) {
            setPage(params.page + 1);
            setPSize(params.pageSize);
          }
        }}
        historyType={"Execution"}
        downloadFromAPIFile={() =>
          downloadRulesHistoryFile({
            setLoading: setIsBackdropLoading,
            searchFilterData,
          })
        }
        disableColumnFilter={false}
        rowGroupByColumnProps={enableRowGroupByColumnProps}
        singlePageMaxHeightDiff={285}
        isShowInComparisonHistory={true}
      />
      <ConfirmationDialog
        title={"Confirm removing Execution"}
        dialogContent={"Are you sure you want to delete this execution?"}
        handleCancel={() => setOpenValidationExecutionConfirmation(false)}
        openConfirmation={openValidationExecutionConfirmation}
        handleConfirm={handleValidationExecutionDelete}
        isShowClassMb0={false}
      />
    </>
  );
};

export default GenericResearchExecutionHistory;
