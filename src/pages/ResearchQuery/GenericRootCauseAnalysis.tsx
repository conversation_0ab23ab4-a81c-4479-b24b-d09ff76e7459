import React, { useEffect, useState } from "react";
import { <PERSON>, Icon<PERSON>utton, Tab, Ta<PERSON>, Tooltip } from "@mui/material";
import { IconFilterSvg } from "../../common/utils/icons";
import GenericRootCauseAnalysisDataTable from "./GenericRootCauseAnalysisDataTable";
import GenericRootCauseAnalysisPaginatedDataTable from "./GenericRootCauseAnalysisPaginatedDataTable";

interface IRootCauseAnalysisProps {
  isLoading: boolean;
  heading: string;
  dashboardData: any;
}

interface INewResearchQueryResults {
  rootCauseAnalysisData: any;
  isLoading: boolean;
  isFilterHide: boolean;
  resourceKeys: any;
  dashboardData: any;
  pageN: number;
  setPageN: React.Dispatch<React.SetStateAction<any>>;
  pageS: number;
  setPageS: React.Dispatch<React.SetStateAction<any>>;
  totalPages: number;
}

interface IResearchQueryResults {
  rootCauseAnalysisData: any;
  isLoading: boolean;
  isFilterHide: boolean;
  resourceKeys: any;
}
const GenericRootCauseAnalysis = ({
  isLoading,
  heading,
  dashboardData,
}: IRootCauseAnalysisProps) => {
  const [rootCauseAnalysisData, setRootCauseAnalysisData] = useState<any>([]);
  const [resourceKeys, setResourceKeys] = useState<any>([]);
  const [isFilterHide, setIsFilterHide] = useState(true);
  const [isResearchQueryLoading, setIsResearchQueryLoading] =
    useState<boolean>(false);
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [pageN, setPageN] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pageS, setPageS] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  useEffect(() => {
    if (!dashboardData) return;
    if (dashboardData?.research_query_results) {
      setRootCauseAnalysisData(dashboardData.research_query_results);
    }

    if (dashboardData?.resource_keys) {
      setResourceKeys(dashboardData.resource_keys);
    }
  }, [dashboardData]);
  const handleFilterBox = () => {
    setIsFilterHide(!isFilterHide);
  };

  return (
    <>
      <Box className="mui-tabs-title mt-6">
        <Tooltip
          title={`${isFilterHide ? "Show Filters" : "Hide Filters"} `}
          placement="top"
        >
          <IconButton className="" onClick={() => handleFilterBox()}>
            <IconFilterSvg />
          </IconButton>
        </Tooltip>
        {heading}
      </Box>
      <NewResearchQueryResults
        rootCauseAnalysisData={rootCauseAnalysisData}
        isLoading={isResearchQueryLoading}
        isFilterHide={isFilterHide}
        resourceKeys={resourceKeys}
        dashboardData={dashboardData}
        pageN={pageN}
        setPageN={setPageN}
        pageS={pageS}
        setPageS={setPageS}
        totalPages={0}
      />
    </>
  );
};

const NewResearchQueryResults = ({
  rootCauseAnalysisData,
  isLoading,
  isFilterHide,
  resourceKeys,
  dashboardData,
  pageN,
  setPageN,
  pageS,
  setPageS,
  totalPages,
}: INewResearchQueryResults) => {
  const [selectedQueryTab, setSelectedQueryTab] = useState<any>("");
  useEffect(() => {
    if (
      dashboardData?.research_query_results &&
      Object.keys(dashboardData.research_query_results).length > 0
    ) {
      const firstKey = Object.keys(dashboardData.research_query_results)[0];
      setSelectedQueryTab(firstKey);
    }
  }, [dashboardData]);
  const handleChangeQueryTab = (
    event: React.SyntheticEvent,
    newValue: string
  ) => {
    setSelectedQueryTab(newValue);
  };
  return (
    <>
      <Tabs
        value={selectedQueryTab}
        onChange={handleChangeQueryTab}
        textColor="secondary"
        indicatorColor="secondary"
        className="mui-tabs no-t-lr-radius min-height-0 alternative-1"
        variant="scrollable"
        scrollButtons="auto"
      >
        {dashboardData?.research_query_results &&
          Object.keys(dashboardData.research_query_results).length > 0 &&
          Object.keys(dashboardData.research_query_results).map(
            (key, index) => <Tab key={index} value={key} label={`${key} `} />
          )}
      </Tabs>
      {
        <GenericRootCauseAnalysisPaginatedDataTable
          rootCauseData={rootCauseAnalysisData}
          isLoading={isLoading}
          isFilterHide={isFilterHide}
          resourceKeys={resourceKeys}
          pageN={pageN}
          setPageN={setPageN}
          pageS={pageS}
          setPageS={setPageS}
          totalPages={totalPages}
          selectedQueryTab={selectedQueryTab}
        />
      }
    </>
  );
};

export default GenericRootCauseAnalysis;
