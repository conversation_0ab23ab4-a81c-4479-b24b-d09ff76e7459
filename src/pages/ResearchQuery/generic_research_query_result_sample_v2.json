{"id": 50, "generic_research_query_id": 25, "generic_research_query_code": "RQDemo20250515", "execution_name": "RQDemo20250515 lorem", "run_id": 1, "run_name": "Legacy", "execution_status": "completed", "execution_time": "2025-05-15T13:46:36.581893", "execution_params": {"research_queries": [{"name": "Resource 1", "source": [{"type": "Resource", "sub_type": null, "resource_id": 914, "resource_code": "resource_914", "linked_service_id": null, "linked_service_code": null, "connection_key_id": null, "connection_key_code": null, "external_source_name": null, "data_pull_query": null, "dependency_list": null, "dependency_marker": null}], "query": "SELECT * FROM <resource_914>"}, {"name": "External 1", "source": [{"type": "External", "sub_type": null, "resource_id": null, "resource_code": null, "linked_service_id": 192, "linked_service_code": "MariaDBSQL_with_uri", "connection_key_id": 148, "connection_key_code": "connection_key_with_uri", "external_source_name": null, "data_pull_query": null, "dependency_list": null, "dependency_marker": null}], "query": "SELECT * FROM crd"}, {"name": "Mixed 1", "source": [{"type": "Mixed", "sub_type": "Resource", "resource_id": 914, "resource_code": "resource_914", "linked_service_id": null, "linked_service_code": null, "connection_key_id": null, "connection_key_code": null, "external_source_name": "", "data_pull_query": "Select * from <resource_914>", "dependency_list": [], "dependency_marker": "Sub Query 1"}, {"type": "Mixed", "sub_type": "External", "resource_id": 914, "resource_code": "resource_914", "linked_service_id": 192, "linked_service_code": "MariaDBSQL_with_uri", "connection_key_id": 148, "connection_key_code": "connection_key_with_uri", "external_source_name": "crd", "data_pull_query": "SELECT * FROM crd", "dependency_list": ["Sub Query 1"], "dependency_marker": "Sub Query 2"}], "query": "Select * from <resource_914> r1 inner join <crd> r2 on r1.[ACCT_CD] = r2.[ACCT_CD] and r1.[EXT_SEC_ID] = r2.[ACCT_CD]"}, {"name": "Mixed 2", "source": [{"type": "Mixed", "sub_type": "Resource", "resource_id": 2193, "resource_code": "JITENDRA_R1", "linked_service_id": null, "linked_service_code": null, "connection_key_id": null, "connection_key_code": null, "external_source_name": "", "data_pull_query": "Select * from <JITENDRA_R1>", "dependency_list": [], "dependency_marker": "Sub Query 1"}, {"type": "Mixed", "sub_type": "Resource", "resource_id": 2194, "resource_code": "JITENDRA_R2", "linked_service_id": null, "linked_service_code": null, "connection_key_id": null, "connection_key_code": null, "external_source_name": "", "data_pull_query": "Select * from <JITENDRA_R2>", "dependency_list": ["Sub Query 1"], "dependency_marker": "Sub Query 2"}], "query": "select * from <JITENDRA_R1> r1 inner join <JITENDRA_R2> r2 on r1.[Column1] = r2.[Column1]"}], "resource_data": [{"resource_id": 2193, "resource_prefix": "Position_R1", "resource_type": "Position_R1", "resource_name": "Position_R1", "aggregation_type": "flat", "resource_column_details_id": 1602, "file_processing_id": 102, "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": null, "sql_query": null, "use_multi_thread_reader": null, "column_name_to_partition_on_sql_query": null, "container_name": null, "resource_path": null, "file_name": null, "column_delimiter": null, "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null, "secondary_merge_resource_parent_id": null, "excel_sheet_name": null}, {"resource_id": 914, "resource_prefix": "CRD", "resource_type": "CRD", "resource_name": "DEMO_CRD_May_15", "aggregation_type": "flat", "resource_column_details_id": 603, "file_processing_id": 4, "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": null, "sql_query": null, "use_multi_thread_reader": null, "column_name_to_partition_on_sql_query": null, "container_name": null, "resource_path": null, "file_name": null, "column_delimiter": null, "file_pre_processing": {"file_format": "xlsx", "schema_definition": "string", "field_delimiter": "|", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null, "secondary_merge_resource_parent_id": null, "excel_sheet_name": null}, {"resource_id": 2194, "resource_prefix": "Position_R2", "resource_type": "Position_R2", "resource_name": "Position_R2", "aggregation_type": "flat", "resource_column_details_id": 1603, "file_processing_id": 102, "linked_service_id": 27, "linked_service_code": "localFile1", "connection_key": null, "sql_query": null, "use_multi_thread_reader": null, "column_name_to_partition_on_sql_query": null, "container_name": null, "resource_path": null, "file_name": null, "column_delimiter": null, "file_pre_processing": {"file_format": "csv", "schema_definition": "string", "field_delimiter": ",", "custom_attributes": {"type": "string", "store_name": "string", "store_label": "string"}, "has_footer": false, "footer_lines": 0, "has_header": true, "header_lines": 0, "skip_rows": 0, "has_multiple_sheets": false, "skip_blanks": true, "compression": null, "compression_codec": null, "line_terminator": "\n", "has_comments": "false", "comments_marker": "#", "encoding": "utf-8", "bad_lines": "skip"}, "filter_rules": null, "api_request": null, "aggregation_properties": null, "aggregation_base_resource_data": null, "additional_base_resource_data": null, "secondary_merge_resource": null, "pre_processing_request": null, "secondary_merge_resource_parent_id": null, "excel_sheet_name": null}], "output_storage_params": {"output_storage_linked_service_id": 27, "output_storage_linked_service_code": "localFile1", "output_storage_connection_key": 28, "output_storage_base_file_path": "C:\\inetpub\\wwwroot\\VIS-http\\download-files"}, "run_instance": {"run_id": 1, "run_name": "Legacy"}, "inline_variables": {}, "execution_name": "RQDemo20250515", "query_params": {"no_of_records_in_response": 100, "no_of_records_in_output_files": 100, "no_of_records_in_filter_rule_output_files": 1000, "generate_output_files": true, "pull_new_files_from_server": true, "pull_latest_files": false, "keep_downloaded_files": false, "save_input_data_files": false, "report_name": "generic_research_query_report"}}, "research_query_results": {"Resource 1": [{"ACCT_CD": 1465, "EXT_SEC_ID": "54655", "ASOF_DATE": "2022-08-02 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 1796, "COST": 31953.96, "UNIT_COST": 17.79173719, "MKT_VAL_SOD": 19793.5364, "UNREALIZED_GAIN_LOSS": "-12160.4236", "ACCRUED_INTEREST": "sdfasdfdsf", "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "65898", "ASOF_DATE": "2022-08-02 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 332, "COST": 9249.13, "UNIT_COST": 27.8588253, "MKT_VAL_SOD": 23528.84, "UNREALIZED_GAIN_LOSS": "14279.71/23528.84", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "217927", "ASOF_DATE": "2022-08-03 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 146, "COST": 5924.25, "UNIT_COST": 40.57705479, "MKT_VAL_SOD": 10497.4, "UNREALIZED_GAIN_LOSS": "4573.15", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "72877", "ASOF_DATE": "2022-08-04 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 970, "COST": 52134.48, "UNIT_COST": 53.7468866, "MKT_VAL_SOD": 58035.1, "UNREALIZED_GAIN_LOSS": "5900.62", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "74075", "ASOF_DATE": "2022-08-05 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 442, "COST": 72279.11, "UNIT_COST": 163.52739819, "MKT_VAL_SOD": 74344.4, "UNREALIZED_GAIN_LOSS": "2065.29", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "74791", "ASOF_DATE": "2022-08-06 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 758, "COST": 29415.55, "UNIT_COST": 38.8067942, "MKT_VAL_SOD": 1111.58, "UNREALIZED_GAIN_LOSS": "9250.03", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "75065", "ASOF_DATE": "2022-08-07 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 918, "COST": 53684.16, "UNIT_COST": 58.47947712, "MKT_VAL_SOD": 32726.7, "UNREALIZED_GAIN_LOSS": "-20957.46", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "137217", "ASOF_DATE": "2022-08-08 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 99, "COST": 31819.92, "UNIT_COST": 321.41333333, "MKT_VAL_SOD": 3333333, "UNREALIZED_GAIN_LOSS": "16131.72", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "115926", "ASOF_DATE": "2022-08-09 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 1074, "COST": 41898.41, "UNIT_COST": 39.01155493, "MKT_VAL_SOD": 48506.7804, "UNREALIZED_GAIN_LOSS": "6608.3704", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "79188", "ASOF_DATE": "2022-08-10 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 2222, "COST": 17911.69, "UNIT_COST": 373.16020833, "MKT_VAL_SOD": 26316.96, "UNREALIZED_GAIN_LOSS": "8405.27", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "79524", "ASOF_DATE": "2022-08-11 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 558, "COST": 35129.47, "UNIT_COST": 62.95603943, "MKT_VAL_SOD": 29479.14, "UNREALIZED_GAIN_LOSS": "-5650.33", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "103044", "ASOF_DATE": "2022-08-12 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": -231, "COST": 44461.66, "UNIT_COST": 192.47471861, "MKT_VAL_SOD": 46010.58, "UNREALIZED_GAIN_LOSS": "1548.92", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1465, "EXT_SEC_ID": "205265", "ASOF_DATE": "2022-08-13 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": -456, "COST": 29510.47, "UNIT_COST": 146.09143564, "MKT_VAL_SOD": 22415.94, "UNREALIZED_GAIN_LOSS": "-7094.53", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1466, "EXT_SEC_ID": "54655", "ASOF_DATE": "2022-08-14 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 1796, "COST": 31953.96, "UNIT_COST": 17.79173719, "MKT_VAL_SOD": 19793.5364, "UNREALIZED_GAIN_LOSS": "-12160.4236", "ACCRUED_INTEREST": "sdfasdfdsf", "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1467, "EXT_SEC_ID": "205265", "ASOF_DATE": "2022-08-15 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 332, "COST": 9249.13, "UNIT_COST": 27.8588253, "MKT_VAL_SOD": 23528.84, "UNREALIZED_GAIN_LOSS": "14279.71/23528.84", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1468, "EXT_SEC_ID": "205266", "ASOF_DATE": "2022-08-16 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 146, "COST": 5924.25, "UNIT_COST": 40.57705479, "MKT_VAL_SOD": 10497.4, "UNREALIZED_GAIN_LOSS": "4573.15", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1469, "EXT_SEC_ID": "205267", "ASOF_DATE": "2022-08-17 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 970, "COST": 52134.48, "UNIT_COST": 53.7468866, "MKT_VAL_SOD": 58035.1, "UNREALIZED_GAIN_LOSS": "5900.62", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}, {"ACCT_CD": 1470, "EXT_SEC_ID": "205268", "ASOF_DATE": "2022-08-17 23:59:00", "LONG_SHORT_IND": "L", "QTY_SOD": 442, "COST": 72279.11, "UNIT_COST": 163.52739819, "MKT_VAL_SOD": 74344.4, "UNREALIZED_GAIN_LOSS": "2065.29", "ACCRUED_INTEREST": null, "file_name": "CRD_Position_20241111_1747312374647.xlsx"}], "External 1": [{"ACCT_CD": 1465, "EXT_SEC_ID": 54655, "ASOF_DATE": 1644278400000, "LONG_SHORT_IND": "L", "QTY_SOD": 1796, "COST": 31954, "UNIT_COST": 17.7917, "MKT_VAL_SOD": 19793.5, "UNREALIZED_GAIN_LOSS": -12160.4, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 65898, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 332, "COST": 9249.13, "UNIT_COST": 27.8588, "MKT_VAL_SOD": 23528.8, "UNREALIZED_GAIN_LOSS": 0.606902, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 217927, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 146, "COST": 5924.25, "UNIT_COST": 40.5771, "MKT_VAL_SOD": 10497.4, "UNREALIZED_GAIN_LOSS": 4573.15, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 72877, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 970, "COST": 52134.5, "UNIT_COST": 53.7469, "MKT_VAL_SOD": 58035.1, "UNREALIZED_GAIN_LOSS": 5900.62, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 74075, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 442, "COST": 72279.1, "UNIT_COST": 163.527, "MKT_VAL_SOD": 74344.4, "UNREALIZED_GAIN_LOSS": 2065.29, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 74791, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 758, "COST": 29415.6, "UNIT_COST": 38.8068, "MKT_VAL_SOD": 38665.6, "UNREALIZED_GAIN_LOSS": 9250.03, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 75065, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 918, "COST": 53684.2, "UNIT_COST": 58.4795, "MKT_VAL_SOD": 32726.7, "UNREALIZED_GAIN_LOSS": -20957.5, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 137217, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 99, "COST": 31819.9, "UNIT_COST": 321.413, "MKT_VAL_SOD": 47951.6, "UNREALIZED_GAIN_LOSS": 16131.7, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 115926, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 1074, "COST": 41898.4, "UNIT_COST": 39.0116, "MKT_VAL_SOD": 48506.8, "UNREALIZED_GAIN_LOSS": 6608.37, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 79188, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 48, "COST": 17911.7, "UNIT_COST": 373.16, "MKT_VAL_SOD": 26317, "UNREALIZED_GAIN_LOSS": 8405.27, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 79524, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "L", "QTY_SOD": 558, "COST": 35129.5, "UNIT_COST": 62.956, "MKT_VAL_SOD": 29479.1, "UNREALIZED_GAIN_LOSS": -5650.33, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 103044, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "S", "QTY_SOD": 231, "COST": 44461.7, "UNIT_COST": 192.475, "MKT_VAL_SOD": 46010.6, "UNREALIZED_GAIN_LOSS": 1548.92, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 205265, "ASOF_DATE": 1659398400000, "LONG_SHORT_IND": "S", "QTY_SOD": 202, "COST": 29510.5, "UNIT_COST": 146.091, "MKT_VAL_SOD": 22415.9, "UNREALIZED_GAIN_LOSS": -7094.53, "ACCRUED_INTEREST": null}, {"ACCT_CD": 1465, "EXT_SEC_ID": 54655, "ASOF_DATE": 1644278400000, "LONG_SHORT_IND": "L", "QTY_SOD": 1796, "COST": 31954, "UNIT_COST": 17.7917, "MKT_VAL_SOD": 19793.5, "UNREALIZED_GAIN_LOSS": -12160.4, "ACCRUED_INTEREST": null}], "Mixed 1": [], "Mixed 2": [{"Column1": "6475.0", "Column2": "9463.0", "Column3": "55.06", "Column4": "23.34", "Column5": "L", "Column6": "2024-12-16T08:28:30.261393", "Column7": "-9466.0", "Column8": "qeqdgxscnm", "Column9": "13.23", "Column10": "2022-12-15T08:38:49.322982", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-12-17 08:28:30.261393", "Column1_2": "6475.0", "Column2_2": "9463.0", "Column3_2": "55.06", "Column4_2": "23.34", "Column5_2": "L", "Column6_2": "2024-12-16T08:28:30.261393", "Column7_2": "-9466.0", "Column8_2": "qeqdgxscnm", "Column9_2": "13.23", "Column10_2": "2022-12-15T08:38:49.322982", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "442.0", "Column2": "60.0", "Column3": "57.27", "Column4": "98.49", "Column5": "L", "Column6": "2021-11-01T23:10:37.144212", "Column7": "-7385.0", "Column8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9": "69.3", "Column10": "2022-12-22T21:16:55.190121", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-11-02 23:10:37.144212", "Column1_2": "442.0", "Column2_2": "60.0", "Column3_2": "57.27", "Column4_2": "98.49", "Column5_2": "L", "Column6_2": "2021-11-01T23:10:37.144212", "Column7_2": "-7385.0", "Column8_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9_2": "69.3", "Column10_2": "2022-12-22T21:16:55.190121", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4674.0", "Column2": "5552.0", "Column3": "19.1", "Column4": "48.24", "Column5": "S", "Column6": "2021-10-04T22:40:03.747067", "Column7": "1.0", "Column8": "rpkusaibya", "Column9": "25.43", "Column10": "2024-04-08T15:54:18.916347", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-10-05 22:40:03.747067", "Column1_2": "4674.0", "Column2_2": "5552.0", "Column3_2": "19.1", "Column4_2": "48.24", "Column5_2": "S", "Column6_2": "2021-10-04T22:40:03.747067", "Column7_2": "1.0", "Column8_2": "rpkusaibya", "Column9_2": "25.43", "Column10_2": "2024-04-08T15:54:18.916347", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "9302.0", "Column2": "1172.0", "Column3": "70.6", "Column4": "6.55", "Column5": "L", "Column6": "2021-04-08T23:46:44.621862", "Column7": "9288.0", "Column8": "ozanoiakzn", "Column9": "37.86", "Column10": "2023-05-12T04:59:59.114433", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-04-09 23:46:44.621862", "Column1_2": "9302.0", "Column2_2": "5458.0", "Column3_2": "15.97", "Column4_2": "62.34", "Column5_2": "L", "Column6_2": "2024-06-23T07:36:41.048442", "Column7_2": "7662.0", "Column8_2": "vgmnfusgan", "Column9_2": "38.47", "Column10_2": "2020-03-04T07:43:33.193940", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7734.0", "Column2": "5684.0", "Column3": "3.0", "Column4": "54.68", "Column5": "S", "Column6": "2023-07-05T05:27:55.278786", "Column7": "8528.0", "Column8": "kqryamktyb", "Column9": "18.5", "Column10": "2023-01-03T02:11:21.638074", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-07-06 05:27:55.278786", "Column1_2": "7734.0", "Column2_2": "5684.0", "Column3_2": "3.0", "Column4_2": "54.68", "Column5_2": "S", "Column6_2": "2023-07-05T05:27:55.278786", "Column7_2": "8528.0", "Column8_2": "kqryamktyb", "Column9_2": "18.5", "Column10_2": "2023-01-03T02:11:21.638074", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5386.0", "Column2": "6172.0", "Column3": "90.63", "Column4": "79.26", "Column5": "L", "Column6": "2021-02-27T11:54:29.624460", "Column7": "2210.0", "Column8": "kaewbixjra", "Column9": "88.7", "Column10": "2020-02-12T05:36:05.933684", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-02-28 11:54:29.624460", "Column1_2": "5386.0", "Column2_2": "6172.0", "Column3_2": "90.63", "Column4_2": "79.26", "Column5_2": "L", "Column6_2": "2021-02-27T11:54:29.624460", "Column7_2": "2210.0", "Column8_2": "kaewbixjra", "Column9_2": "88.7", "Column10_2": "2020-02-12T05:36:05.933684", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1985.0", "Column2": "2345.0", "Column3": "34.27", "Column4": "21.01", "Column5": "S", "Column6": "2022-12-20T07:59:49.291812", "Column7": "3933.0", "Column8": "hgybddyffq", "Column9": "97.12", "Column10": "2021-12-22T18:20:05.098660", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-12-21 07:59:49.291812", "Column1_2": "1985.0", "Column2_2": "2345.0", "Column3_2": "34.27", "Column4_2": "21.01", "Column5_2": "S", "Column6_2": "2022-12-20T07:59:49.291812", "Column7_2": "3933.0", "Column8_2": "hgybddyffq", "Column9_2": "97.12", "Column10_2": "2021-12-22T18:20:05.098660", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1424.0", "Column2": "6747.0", "Column3": "52.9", "Column4": "23.85", "Column5": "L", "Column6": "2024-10-09T02:25:16.262605", "Column7": "9409.0", "Column8": "jyjfbimnti", "Column9": "28.12", "Column10": "2021-11-18T17:08:48.456770", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-10-10 02:25:16.262605", "Column1_2": "1424.0", "Column2_2": "6747.0", "Column3_2": "52.9", "Column4_2": "23.85", "Column5_2": "L", "Column6_2": "2024-10-09T02:25:16.262605", "Column7_2": "9409.0", "Column8_2": "jyjfbimnti", "Column9_2": "28.12", "Column10_2": "2021-11-18T17:08:48.456770", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6396.0", "Column2": "2261.0", "Column3": "41.52", "Column4": "65.71", "Column5": "S", "Column6": "2021-08-27T15:50:58.412309", "Column7": "1259.0", "Column8": "vbahpltkxn", "Column9": "70.48", "Column10": "2023-06-19T22:15:43.436526", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-08-28 15:50:58.412309", "Column1_2": "6396.0", "Column2_2": "9255.0", "Column3_2": "79.51", "Column4_2": "83.65", "Column5_2": "L", "Column6_2": "2024-01-03T20:26:34.819502", "Column7_2": "1157.0", "Column8_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9_2": "18.81", "Column10_2": "2022-02-23T17:22:13.447923", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4926.0", "Column2": "7729.0", "Column3": "83.55", "Column4": "67.14", "Column5": "S", "Column6": "2021-05-05T12:00:44.663010", "Column7": "4161.0", "Column8": "snrtjtpmgi", "Column9": "43.51", "Column10": "2022-11-13T01:09:48.806556", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-05-06 12:00:44.663010", "Column1_2": "4926.0", "Column2_2": "7729.0", "Column3_2": "83.55", "Column4_2": "67.14", "Column5_2": "S", "Column6_2": "2021-05-05T12:00:44.663010", "Column7_2": "4161.0", "Column8_2": "snrtjtpmgi", "Column9_2": "43.51", "Column10_2": "2022-11-13T01:09:48.806556", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1463.0", "Column2": "7394.0", "Column3": "26.1", "Column4": "49.6", "Column5": "L", "Column6": "2022-10-22T15:09:57.193432", "Column7": "6886.0", "Column8": "jbvdkknqyy", "Column9": "80.87", "Column10": "2023-10-04T09:42:12.911893", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-10-23 15:09:57.193432", "Column1_2": "1463.0", "Column2_2": "7394.0", "Column3_2": "26.1", "Column4_2": "49.6", "Column5_2": "L", "Column6_2": "2022-10-22T15:09:57.193432", "Column7_2": "6886.0", "Column8_2": "jbvdkknqyy", "Column9_2": "80.87", "Column10_2": "2023-10-04T09:42:12.911893", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2562.0", "Column2": "9972.0", "Column3": "59.88", "Column4": "33.23", "Column5": "L", "Column6": "2023-08-04T10:00:33.126206", "Column7": "1026.0", "Column8": "xjzkeyoyzn", "Column9": "18.32", "Column10": "2022-08-17T22:57:53.973426", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-08-05 10:00:33.126206", "Column1_2": "2562.0", "Column2_2": "9972.0", "Column3_2": "59.88", "Column4_2": "33.23", "Column5_2": "L", "Column6_2": "2023-08-04T10:00:33.126206", "Column7_2": "1026.0", "Column8_2": "xjzkeyoyzn", "Column9_2": "18.32", "Column10_2": "2022-08-17T22:57:53.973426", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4019.0", "Column2": "2225.0", "Column3": "22.46", "Column4": "44.0", "Column5": "S", "Column6": "2020-06-06T02:43:23.501212", "Column7": "5832.0", "Column8": "yd<PERSON>cmuq<PERSON><PERSON>", "Column9": "70.6", "Column10": "2020-05-09T15:23:01.915999", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-06-07 02:43:23.501212", "Column1_2": "4019.0", "Column2_2": "5364.0", "Column3_2": "94.14", "Column4_2": "24.32", "Column5_2": "S", "Column6_2": "2022-10-30T00:10:01.882094", "Column7_2": "7402.0", "Column8_2": "mahriawntp", "Column9_2": "88.5", "Column10_2": "2023-05-04T06:54:02.031298", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4497.0", "Column2": "783.0", "Column3": "44.67", "Column4": "27.45", "Column5": "S", "Column6": "2023-10-28T18:01:11.028476", "Column7": "9915.0", "Column8": "<PERSON>lda<PERSON><PERSON><PERSON><PERSON>", "Column9": "39.2", "Column10": "2023-11-08T13:08:32.310594", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-10-29 18:01:11.028476", "Column1_2": "4497.0", "Column2_2": "783.0", "Column3_2": "44.67", "Column4_2": "27.45", "Column5_2": "S", "Column6_2": "2023-10-28T18:01:11.028476", "Column7_2": "9915.0", "Column8_2": "<PERSON>lda<PERSON><PERSON><PERSON><PERSON>", "Column9_2": "39.2", "Column10_2": "2023-11-08T13:08:32.310594", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5117.0", "Column2": "450.0", "Column3": "26.47", "Column4": "16.83", "Column5": "L", "Column6": "2020-05-17T13:18:39.495529", "Column7": "4126.0", "Column8": "<PERSON><PERSON><PERSON>r<PERSON>j<PERSON>", "Column9": "25.81", "Column10": "2020-01-06T23:28:43.895426", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-05-18 13:18:39.495529", "Column1_2": "5117.0", "Column2_2": "450.0", "Column3_2": "26.47", "Column4_2": "16.83", "Column5_2": "L", "Column6_2": "2020-05-17T13:18:39.495529", "Column7_2": "4126.0", "Column8_2": "<PERSON><PERSON><PERSON>r<PERSON>j<PERSON>", "Column9_2": "25.81", "Column10_2": "2020-01-06T23:28:43.895426", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6991.0", "Column2": "5477.0", "Column3": "79.78", "Column4": "71.76", "Column5": "S", "Column6": "2021-06-13T10:32:59.971181", "Column7": "7464.0", "Column8": "uojqninfzb", "Column9": "73.58", "Column10": "2020-04-09T04:21:17.239096", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-06-14 10:32:59.971181", "Column1_2": "6991.0", "Column2_2": "5477.0", "Column3_2": "79.78", "Column4_2": "71.76", "Column5_2": "S", "Column6_2": "2021-06-13T10:32:59.971181", "Column7_2": "7464.0", "Column8_2": "uojqninfzb", "Column9_2": "73.58", "Column10_2": "2020-04-09T04:21:17.239096", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2007.0", "Column2": "9390.0", "Column3": "1.47", "Column4": "87.26", "Column5": "S", "Column6": "2020-02-26T05:28:15.203769", "Column7": "3738.0", "Column8": "huvugxubgn", "Column9": "11.67", "Column10": "2021-02-24T06:28:19.453979", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-02-27 05:28:15.203769", "Column1_2": "2007.0", "Column2_2": "9390.0", "Column3_2": "1.47", "Column4_2": "87.26", "Column5_2": "S", "Column6_2": "2020-02-26T05:28:15.203769", "Column7_2": "3738.0", "Column8_2": "huvugxubgn", "Column9_2": "11.67", "Column10_2": "2021-02-24T06:28:19.453979", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "60.0", "Column2": "5857.0", "Column3": "84.65", "Column4": "31.35", "Column5": "S", "Column6": "2021-06-04T07:20:40.737620", "Column7": "7725.0", "Column8": "ylymjvpcrq", "Column9": "20.4", "Column10": "2022-11-02T13:32:45.166606", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-06-05 07:20:40.737620", "Column1_2": "60.0", "Column2_2": "5857.0", "Column3_2": "84.65", "Column4_2": "31.35", "Column5_2": "S", "Column6_2": "2021-06-04T07:20:40.737620", "Column7_2": "7725.0", "Column8_2": "ylymjvpcrq", "Column9_2": "20.4", "Column10_2": "2022-11-02T13:32:45.166606", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1569.0", "Column2": "5131.0", "Column3": "68.5", "Column4": "66.09", "Column5": "L", "Column6": "2022-04-30T20:42:37.812099", "Column7": "8919.0", "Column8": "zwnwesimws", "Column9": "66.44", "Column10": "2021-02-10T02:28:39.174902", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-05-01 20:42:37.812099", "Column1_2": "1569.0", "Column2_2": "5131.0", "Column3_2": "68.5", "Column4_2": "66.09", "Column5_2": "L", "Column6_2": "2022-04-30T20:42:37.812099", "Column7_2": "8919.0", "Column8_2": "zwnwesimws", "Column9_2": "66.44", "Column10_2": "2021-02-10T02:28:39.174902", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7099.0", "Column2": "3546.0", "Column3": "18.13", "Column4": "51.14", "Column5": "S", "Column6": "2022-05-25T13:58:23.135527", "Column7": "9313.0", "Column8": "jtiwqkwtdh", "Column9": "6.18", "Column10": "2023-08-31T02:12:27.263022", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-05-26 13:58:23.135527", "Column1_2": "7099.0", "Column2_2": "9432.0", "Column3_2": "28.92", "Column4_2": "26.27", "Column5_2": "L", "Column6_2": "2020-11-24T14:37:26.606010", "Column7_2": "8395.0", "Column8_2": "hwmyienvsa", "Column9_2": "13.06", "Column10_2": "2024-05-22T03:32:45.987922", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6838.0", "Column2": "6008.0", "Column3": "50.12", "Column4": "37.8", "Column5": "L", "Column6": "2023-11-26T12:03:54.537112", "Column7": "949.0", "Column8": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9": "49.27", "Column10": "2022-07-10T21:54:50.458963", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-11-27 12:03:54.537112", "Column1_2": "6838.0", "Column2_2": "1400.0", "Column3_2": "35.77", "Column4_2": "59.29", "Column5_2": "S", "Column6_2": "2024-08-09T02:04:15.192210", "Column7_2": "8415.0", "Column8_2": "ybhydnrnxh", "Column9_2": "21.87", "Column10_2": "2023-06-07T16:39:54.357707", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "9106.0", "Column2": "7692.0", "Column3": "41.9", "Column4": "79.71", "Column5": "L", "Column6": "2021-06-24T12:49:41.964361", "Column7": "9946.0", "Column8": "srsx<PERSON><PERSON><PERSON>", "Column9": "7.44", "Column10": "2020-12-31T13:13:21.983182", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-06-25 12:49:41.964361", "Column1_2": "9106.0", "Column2_2": "7692.0", "Column3_2": "41.9", "Column4_2": "79.71", "Column5_2": "L", "Column6_2": "2021-06-24T12:49:41.964361", "Column7_2": "9946.0", "Column8_2": "srsx<PERSON><PERSON><PERSON>", "Column9_2": "7.44", "Column10_2": "2020-12-31T13:13:21.983182", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1273.0", "Column2": "9343.0", "Column3": "4.85", "Column4": "8.26", "Column5": "L", "Column6": "2022-06-02T05:28:54.968239", "Column7": "4288.0", "Column8": "oebqdmmgxm", "Column9": "24.24", "Column10": "2024-05-07T07:26:51.719931", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-06-03 05:28:54.968239", "Column1_2": "1273.0", "Column2_2": "9343.0", "Column3_2": "4.85", "Column4_2": "8.26", "Column5_2": "L", "Column6_2": "2022-06-02T05:28:54.968239", "Column7_2": "4288.0", "Column8_2": "oebqdmmgxm", "Column9_2": "24.24", "Column10_2": "2024-05-07T07:26:51.719931", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "452.0", "Column2": "2348.0", "Column3": "25.41", "Column4": "62.55", "Column5": "L", "Column6": "2021-07-12T04:01:16.423357", "Column7": "7458.0", "Column8": "vwyerrylws", "Column9": "27.06", "Column10": "2022-06-22T10:04:21.986729", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-07-13 04:01:16.423357", "Column1_2": "452.0", "Column2_2": "5162.0", "Column3_2": "44.15", "Column4_2": "91.54", "Column5_2": "L", "Column6_2": "2022-02-07T17:31:29.730172", "Column7_2": "6919.0", "Column8_2": "yrqpfuyrpp", "Column9_2": "40.44", "Column10_2": "2022-04-30T07:20:56.275297", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1385.0", "Column2": "2727.0", "Column3": "22.61", "Column4": "52.97", "Column5": "S", "Column6": "2022-12-27T07:40:07.506321", "Column7": "356.0", "Column8": "feymxlddop", "Column9": "33.0", "Column10": "2020-08-02T02:01:05.332920", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-12-28 07:40:07.506321", "Column1_2": "1385.0", "Column2_2": "7469.0", "Column3_2": "10.63", "Column4_2": "37.83", "Column5_2": "S", "Column6_2": "2024-08-31T23:42:32.526344", "Column7_2": "92.0", "Column8_2": "waftvapqyu", "Column9_2": "45.29", "Column10_2": "2024-07-21T18:39:47.262800", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1566.0", "Column2": "9368.0", "Column3": "34.95", "Column4": "83.29", "Column5": "S", "Column6": "2020-10-10T01:30:35.674845", "Column7": "4574.0", "Column8": "xwnhpjmdpn", "Column9": "0.92", "Column10": "2024-10-18T01:30:33.577282", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-10-11 01:30:35.674845", "Column1_2": "1566.0", "Column2_2": "9368.0", "Column3_2": "34.95", "Column4_2": "83.29", "Column5_2": "S", "Column6_2": "2020-10-10T01:30:35.674845", "Column7_2": "4574.0", "Column8_2": "xwnhpjmdpn", "Column9_2": "0.92", "Column10_2": "2024-10-18T01:30:33.577282", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7021.0", "Column2": "4108.0", "Column3": "79.67", "Column4": "0.84", "Column5": "S", "Column6": "2023-10-29T16:36:35.700299", "Column7": "1575.0", "Column8": "kbpekqfyrs", "Column9": "68.85", "Column10": "2022-09-11T19:06:03.135922", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-10-30 16:36:35.700299", "Column1_2": "7021.0", "Column2_2": "5646.0", "Column3_2": "16.45", "Column4_2": "85.38", "Column5_2": "S", "Column6_2": "2024-03-17T02:34:35.175782", "Column7_2": "7531.0", "Column8_2": "wqbidtwczb", "Column9_2": "12.44", "Column10_2": "2022-10-23T22:28:43.987172", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6616.0", "Column2": "3294.0", "Column3": "57.72", "Column4": "32.47", "Column5": "L", "Column6": "2020-11-28T11:40:15.138620", "Column7": "8873.0", "Column8": "swrderxdwo", "Column9": "2.67", "Column10": "2023-10-13T22:46:46.748961", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-11-29 11:40:15.138620", "Column1_2": "6616.0", "Column2_2": "3294.0", "Column3_2": "57.72", "Column4_2": "32.47", "Column5_2": "L", "Column6_2": "2020-11-28T11:40:15.138620", "Column7_2": "8873.0", "Column8_2": "swrderxdwo", "Column9_2": "2.67", "Column10_2": "2023-10-13T22:46:46.748961", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7082.0", "Column2": "5094.0", "Column3": "85.6", "Column4": "64.05", "Column5": "S", "Column6": "2020-11-17T01:27:59.260323", "Column7": "9252.0", "Column8": "zjuiwaptfz", "Column9": "32.2", "Column10": "2022-08-09T02:50:17.670284", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-11-18 01:27:59.260323", "Column1_2": "7082.0", "Column2_2": "5094.0", "Column3_2": "85.6", "Column4_2": "64.05", "Column5_2": "S", "Column6_2": "2020-11-17T01:27:59.260323", "Column7_2": "9252.0", "Column8_2": "zjuiwaptfz", "Column9_2": "32.2", "Column10_2": "2022-08-09T02:50:17.670284", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2025.0", "Column2": "5518.0", "Column3": "79.28", "Column4": "22.91", "Column5": "S", "Column6": "2024-01-19T05:14:12.668187", "Column7": "1621.0", "Column8": "rizhzcbdjf", "Column9": "29.62", "Column10": "2023-10-20T21:44:39.759039", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-01-20 05:14:12.668187", "Column1_2": "2025.0", "Column2_2": "5518.0", "Column3_2": "79.28", "Column4_2": "22.91", "Column5_2": "S", "Column6_2": "2024-01-19T05:14:12.668187", "Column7_2": "1621.0", "Column8_2": "rizhzcbdjf", "Column9_2": "29.62", "Column10_2": "2023-10-20T21:44:39.759039", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1868.0", "Column2": "9757.0", "Column3": "0.53", "Column4": "95.64", "Column5": "S", "Column6": "2022-10-27T13:10:17.797493", "Column7": "5036.0", "Column8": "wiesftcngx", "Column9": "72.56", "Column10": "2020-05-22T06:28:39.675081", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-10-28 13:10:17.797493", "Column1_2": "1868.0", "Column2_2": "9757.0", "Column3_2": "0.53", "Column4_2": "95.64", "Column5_2": "S", "Column6_2": "2022-10-27T13:10:17.797493", "Column7_2": "5036.0", "Column8_2": "wiesftcngx", "Column9_2": "72.56", "Column10_2": "2020-05-22T06:28:39.675081", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6100.0", "Column2": "6568.0", "Column3": "94.72", "Column4": "58.2", "Column5": "L", "Column6": "2024-07-08T11:39:01.809308", "Column7": "9557.0", "Column8": "coumnugjxw", "Column9": "74.74", "Column10": "2022-05-08T02:06:38.002186", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-07-09 11:39:01.809308", "Column1_2": "6100.0", "Column2_2": "6568.0", "Column3_2": "94.72", "Column4_2": "58.2", "Column5_2": "L", "Column6_2": "2024-07-08T11:39:01.809308", "Column7_2": "9557.0", "Column8_2": "coumnugjxw", "Column9_2": "74.74", "Column10_2": "2022-05-08T02:06:38.002186", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5876.0", "Column2": "7471.0", "Column3": "68.88", "Column4": "53.01", "Column5": "S", "Column6": "2022-06-30T00:10:32.101972", "Column7": "6689.0", "Column8": "zcyoexzlkl", "Column9": "43.97", "Column10": "2021-09-12T18:05:30.924341", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-07-01 00:10:32.101972", "Column1_2": "5876.0", "Column2_2": "7471.0", "Column3_2": "68.88", "Column4_2": "53.01", "Column5_2": "S", "Column6_2": "2022-06-30T00:10:32.101972", "Column7_2": "6689.0", "Column8_2": "zcyoexzlkl", "Column9_2": "43.97", "Column10_2": "2021-09-12T18:05:30.924341", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3080.0", "Column2": "7952.0", "Column3": "7.56", "Column4": "80.64", "Column5": "S", "Column6": "2023-01-29T18:25:17.793075", "Column7": "8774.0", "Column8": "ezglxtsbgo", "Column9": "86.34", "Column10": "2020-06-24T02:51:13.759078", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-01-30 18:25:17.793075", "Column1_2": "3080.0", "Column2_2": "8314.0", "Column3_2": "36.37", "Column4_2": "5.75", "Column5_2": "L", "Column6_2": "2022-03-05T01:26:09.282180", "Column7_2": "1195.0", "Column8_2": "pltsqsvbim", "Column9_2": "30.07", "Column10_2": "2021-09-06T00:42:03.448305", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1134.0", "Column2": "3074.0", "Column3": "57.5", "Column4": "94.61", "Column5": "L", "Column6": "2024-12-21T17:23:37.477971", "Column7": "6913.0", "Column8": "mjgkbcuwls", "Column9": "62.01", "Column10": "2024-10-19T09:43:29.523091", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-12-22 17:23:37.477971", "Column1_2": "1134.0", "Column2_2": "3074.0", "Column3_2": "57.5", "Column4_2": "94.61", "Column5_2": "L", "Column6_2": "2024-12-21T17:23:37.477971", "Column7_2": "6913.0", "Column8_2": "mjgkbcuwls", "Column9_2": "62.01", "Column10_2": "2024-10-19T09:43:29.523091", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1953.0", "Column2": "9819.0", "Column3": "30.41", "Column4": "99.82", "Column5": "S", "Column6": "2023-01-29T19:02:40.766303", "Column7": "1395.0", "Column8": "ahhkmzlxze", "Column9": "74.99", "Column10": "2024-04-19T06:40:44.629241", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-01-30 19:02:40.766303", "Column1_2": "1953.0", "Column2_2": "1293.0", "Column3_2": "19.69", "Column4_2": "98.33", "Column5_2": "L", "Column6_2": "2024-09-14T23:28:56.236471", "Column7_2": "7849.0", "Column8_2": "nxorkqukuw", "Column9_2": "67.29", "Column10_2": "2022-10-29T06:27:31.457064", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "350.0", "Column2": "9020.0", "Column3": "75.22", "Column4": "24.54", "Column5": "S", "Column6": "2022-08-13T16:35:53.869119", "Column7": "5178.0", "Column8": "gzpkjbsatg", "Column9": "94.13", "Column10": "2022-01-31T09:08:08.105423", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-08-14 16:35:53.869119", "Column1_2": "350.0", "Column2_2": "9020.0", "Column3_2": "75.22", "Column4_2": "24.54", "Column5_2": "S", "Column6_2": "2022-08-13T16:35:53.869119", "Column7_2": "5178.0", "Column8_2": "gzpkjbsatg", "Column9_2": "94.13", "Column10_2": "2022-01-31T09:08:08.105423", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2853.0", "Column2": "1494.0", "Column3": "6.51", "Column4": "12.78", "Column5": "L", "Column6": "2020-02-24T21:54:26.246800", "Column7": "2072.0", "Column8": "vjicacehvi", "Column9": "2.66", "Column10": "2024-05-30T14:08:32.197022", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-02-25 21:54:26.246800", "Column1_2": "2853.0", "Column2_2": "1494.0", "Column3_2": "6.51", "Column4_2": "12.78", "Column5_2": "L", "Column6_2": "2020-02-24T21:54:26.246800", "Column7_2": "2072.0", "Column8_2": "vjicacehvi", "Column9_2": "2.66", "Column10_2": "2024-05-30T14:08:32.197022", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "9690.0", "Column2": "3529.0", "Column3": "76.94", "Column4": "93.81", "Column5": "S", "Column6": "2023-01-18T14:54:23.207845", "Column7": "7266.0", "Column8": "bope<PERSON><PERSON><PERSON>", "Column9": "82.47", "Column10": "2023-09-03T01:14:48.811678", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-01-19 14:54:23.207845", "Column1_2": "9690.0", "Column2_2": "3529.0", "Column3_2": "76.94", "Column4_2": "93.81", "Column5_2": "S", "Column6_2": "2023-01-18T14:54:23.207845", "Column7_2": "7266.0", "Column8_2": "bope<PERSON><PERSON><PERSON>", "Column9_2": "82.47", "Column10_2": "2023-09-03T01:14:48.811678", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "924.0", "Column2": "8101.0", "Column3": "19.59", "Column4": "61.07", "Column5": "L", "Column6": "2020-07-01T08:02:10.620425", "Column7": "2254.0", "Column8": "mcwfwipgga", "Column9": "96.24", "Column10": "2021-06-02T04:22:14.423733", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-07-02 08:02:10.620425", "Column1_2": "924.0", "Column2_2": "8101.0", "Column3_2": "19.59", "Column4_2": "61.07", "Column5_2": "L", "Column6_2": "2020-07-01T08:02:10.620425", "Column7_2": "2254.0", "Column8_2": "mcwfwipgga", "Column9_2": "96.24", "Column10_2": "2021-06-02T04:22:14.423733", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "8246.0", "Column2": "3081.0", "Column3": "33.1", "Column4": "64.59", "Column5": "S", "Column6": "2021-09-02T10:29:11.061902", "Column7": "6710.0", "Column8": "ibdpqaaamt", "Column9": "35.42", "Column10": "2021-11-27T10:58:51.409108", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-09-03 10:29:11.061902", "Column1_2": "8246.0", "Column2_2": "3081.0", "Column3_2": "33.1", "Column4_2": "64.59", "Column5_2": "S", "Column6_2": "2021-09-02T10:29:11.061902", "Column7_2": "6710.0", "Column8_2": "ibdpqaaamt", "Column9_2": "35.42", "Column10_2": "2021-11-27T10:58:51.409108", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7238.0", "Column2": "8031.0", "Column3": "40.28", "Column4": "2.04", "Column5": "S", "Column6": "2024-11-11T20:39:29.221639", "Column7": "7842.0", "Column8": "nen<PERSON><PERSON><PERSON>", "Column9": "33.65", "Column10": "2020-12-02T23:25:23.561857", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-11-12 20:39:29.221639", "Column1_2": "7238.0", "Column2_2": "9275.0", "Column3_2": "79.88", "Column4_2": "59.37", "Column5_2": "L", "Column6_2": "2024-11-17T10:58:00.626745", "Column7_2": "5839.0", "Column8_2": "wstexkgtys", "Column9_2": "47.61", "Column10_2": "2023-03-09T13:38:51.716708", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7947.0", "Column2": "2946.0", "Column3": "80.5", "Column4": "53.42", "Column5": "L", "Column6": "2024-03-24T09:51:52.082899", "Column7": "3698.0", "Column8": "jqfwrttzwa", "Column9": "66.6", "Column10": "2021-03-08T10:28:31.278200", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-03-25 09:51:52.082899", "Column1_2": "7947.0", "Column2_2": "5276.0", "Column3_2": "38.58", "Column4_2": "48.95", "Column5_2": "L", "Column6_2": "2024-03-23T09:02:06.192570", "Column7_2": "168.0", "Column8_2": "rdqhjqnxku", "Column9_2": "60.81", "Column10_2": "2021-06-06T08:48:41.074994", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6555.0", "Column2": "3965.0", "Column3": "17.82", "Column4": "30.63", "Column5": "S", "Column6": "2023-11-18T20:53:05.621935", "Column7": "9332.0", "Column8": "uwdeed<PERSON><PERSON>", "Column9": "20.23", "Column10": "2022-11-19T00:40:43.390153", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-11-19 20:53:05.621935", "Column1_2": "6555.0", "Column2_2": "3965.0", "Column3_2": "17.82", "Column4_2": "30.63", "Column5_2": "S", "Column6_2": "2023-11-18T20:53:05.621935", "Column7_2": "9332.0", "Column8_2": "uwdeed<PERSON><PERSON>", "Column9_2": "20.23", "Column10_2": "2022-11-19T00:40:43.390153", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1808.0", "Column2": "5521.0", "Column3": "58.97", "Column4": "90.32", "Column5": "L", "Column6": "2022-11-27T21:18:53.508144", "Column7": "4026.0", "Column8": "uadkhwkqpq", "Column9": "40.02", "Column10": "2024-10-25T22:44:50.860866", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-11-28 21:18:53.508144", "Column1_2": "1808.0", "Column2_2": "5521.0", "Column3_2": "58.97", "Column4_2": "90.32", "Column5_2": "L", "Column6_2": "2022-11-27T21:18:53.508144", "Column7_2": "4026.0", "Column8_2": "uadkhwkqpq", "Column9_2": "40.02", "Column10_2": "2024-10-25T22:44:50.860866", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5916.0", "Column2": "6318.0", "Column3": "18.63", "Column4": "0.56", "Column5": "S", "Column6": "2023-08-27T09:14:24.812235", "Column7": "8664.0", "Column8": "zpridpmkut", "Column9": "23.86", "Column10": "2024-03-19T09:02:50.554214", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-08-28 09:14:24.812235", "Column1_2": "5916.0", "Column2_2": "6318.0", "Column3_2": "18.63", "Column4_2": "0.56", "Column5_2": "S", "Column6_2": "2023-08-27T09:14:24.812235", "Column7_2": "8664.0", "Column8_2": "zpridpmkut", "Column9_2": "23.86", "Column10_2": "2024-03-19T09:02:50.554214", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4687.0", "Column2": "3600.0", "Column3": "94.25", "Column4": "73.98", "Column5": "L", "Column6": "2022-09-30T11:05:06.433463", "Column7": "9609.0", "Column8": "ehvsatmqbu", "Column9": "83.39", "Column10": "2020-02-06T03:41:08.280729", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-10-01 11:05:06.433463", "Column1_2": "4687.0", "Column2_2": "2757.0", "Column3_2": "21.63", "Column4_2": "45.94", "Column5_2": "S", "Column6_2": "2024-05-17T10:19:37.114636", "Column7_2": "3638.0", "Column8_2": "mmoffqcxto", "Column9_2": "90.67", "Column10_2": "2024-09-28T18:36:34.484932", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1953.0", "Column2": "2539.0", "Column3": "64.87", "Column4": "10.42", "Column5": "L", "Column6": "2021-10-02T06:48:22.495285", "Column7": "9517.0", "Column8": "piyttgwdlj", "Column9": "13.92", "Column10": "2020-05-19T20:16:25.040374", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-10-03 06:48:22.495285", "Column1_2": "1953.0", "Column2_2": "1293.0", "Column3_2": "19.69", "Column4_2": "98.33", "Column5_2": "L", "Column6_2": "2024-09-14T23:28:56.236471", "Column7_2": "7849.0", "Column8_2": "nxorkqukuw", "Column9_2": "67.29", "Column10_2": "2022-10-29T06:27:31.457064", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4511.0", "Column2": "5752.0", "Column3": "64.15", "Column4": "51.43", "Column5": "S", "Column6": "2024-04-20T16:57:31.943779", "Column7": "9655.0", "Column8": "kkoaxzxxkj", "Column9": "14.75", "Column10": "2024-06-02T18:01:08.728363", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-04-21 16:57:31.943779", "Column1_2": "4511.0", "Column2_2": "9671.0", "Column3_2": "87.2", "Column4_2": "25.82", "Column5_2": "L", "Column6_2": "2024-03-16T18:40:50.528274", "Column7_2": "1300.0", "Column8_2": "je<PERSON><PERSON><PERSON><PERSON>", "Column9_2": "97.89", "Column10_2": "2024-09-30T08:08:00.530458", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6593.0", "Column2": "9741.0", "Column3": "17.17", "Column4": "91.57", "Column5": "L", "Column6": "2024-03-24T11:54:26.531508", "Column7": "3752.0", "Column8": "mckzrviltv", "Column9": "23.42", "Column10": "2023-10-15T09:49:57.158881", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-03-25 11:54:26.531508", "Column1_2": "6593.0", "Column2_2": "9741.0", "Column3_2": "17.17", "Column4_2": "91.57", "Column5_2": "L", "Column6_2": "2024-03-24T11:54:26.531508", "Column7_2": "3752.0", "Column8_2": "mckzrviltv", "Column9_2": "23.42", "Column10_2": "2023-10-15T09:49:57.158881", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7690.0", "Column2": "9451.0", "Column3": "87.97", "Column4": "63.7", "Column5": "L", "Column6": "2024-01-24T17:34:37.801847", "Column7": "3448.0", "Column8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9": "59.16", "Column10": "2024-06-18T21:03:26.622015", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-01-25 17:34:37.801847", "Column1_2": "7690.0", "Column2_2": "9451.0", "Column3_2": "87.97", "Column4_2": "63.7", "Column5_2": "L", "Column6_2": "2024-01-24T17:34:37.801847", "Column7_2": "3448.0", "Column8_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9_2": "59.16", "Column10_2": "2024-06-18T21:03:26.622015", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1661.0", "Column2": "8725.0", "Column3": "20.65", "Column4": "33.61", "Column5": "L", "Column6": "2022-10-18T05:32:58.684396", "Column7": "3075.0", "Column8": "qwzymhzqxg", "Column9": "7.97", "Column10": "2021-07-26T05:50:02.962948", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-10-19 05:32:58.684396", "Column1_2": "1661.0", "Column2_2": "4218.0", "Column3_2": "86.44", "Column4_2": "28.88", "Column5_2": "L", "Column6_2": "2021-03-28T11:31:45.609599", "Column7_2": "3300.0", "Column8_2": "lpgbouaroy", "Column9_2": "61.61", "Column10_2": "2024-09-10T09:43:19.087936", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "382.0", "Column2": "611.0", "Column3": "29.13", "Column4": "76.47", "Column5": "L", "Column6": "2021-01-29T19:40:33.479117", "Column7": "5334.0", "Column8": "dazlwdsktf", "Column9": "52.53", "Column10": "2021-05-03T13:50:36.608121", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-01-30 19:40:33.479117", "Column1_2": "382.0", "Column2_2": "611.0", "Column3_2": "29.13", "Column4_2": "76.47", "Column5_2": "L", "Column6_2": "2021-01-29T19:40:33.479117", "Column7_2": "5334.0", "Column8_2": "dazlwdsktf", "Column9_2": "52.53", "Column10_2": "2021-05-03T13:50:36.608121", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7698.0", "Column2": "126.0", "Column3": "40.82", "Column4": "40.72", "Column5": "L", "Column6": "2022-08-24T18:40:13.574663", "Column7": "8806.0", "Column8": "gyushxteen", "Column9": "86.63", "Column10": "2023-02-25T03:31:09.726174", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-08-25 18:40:13.574663", "Column1_2": "7698.0", "Column2_2": "126.0", "Column3_2": "40.82", "Column4_2": "40.72", "Column5_2": "L", "Column6_2": "2022-08-24T18:40:13.574663", "Column7_2": "8806.0", "Column8_2": "gyushxteen", "Column9_2": "86.63", "Column10_2": "2023-02-25T03:31:09.726174", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4134.0", "Column2": "2905.0", "Column3": "82.62", "Column4": "33.41", "Column5": "S", "Column6": "2020-04-02T19:55:55.216137", "Column7": "3148.0", "Column8": "zzgmnpnsyu", "Column9": "51.18", "Column10": "2022-10-30T14:30:38.900473", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-04-03 19:55:55.216137", "Column1_2": "4134.0", "Column2_2": "2717.0", "Column3_2": "0.3", "Column4_2": "19.29", "Column5_2": "L", "Column6_2": "2024-02-17T18:37:58.131289", "Column7_2": "5044.0", "Column8_2": "ivnwrxkrie", "Column9_2": "36.98", "Column10_2": "2021-02-10T18:59:50.838732", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3625.0", "Column2": "753.0", "Column3": "36.9", "Column4": "49.09", "Column5": "L", "Column6": "2021-07-05T03:36:40.976136", "Column7": "3091.0", "Column8": "zdzebygrni", "Column9": "49.78", "Column10": "2023-01-09T10:15:31.390483", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-07-06 03:36:40.976136", "Column1_2": "3625.0", "Column2_2": "753.0", "Column3_2": "36.9", "Column4_2": "49.09", "Column5_2": "L", "Column6_2": "2021-07-05T03:36:40.976136", "Column7_2": "3091.0", "Column8_2": "zdzebygrni", "Column9_2": "49.78", "Column10_2": "2023-01-09T10:15:31.390483", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4533.0", "Column2": "8345.0", "Column3": "20.31", "Column4": "9.84", "Column5": "L", "Column6": "2024-09-25T22:44:48.796764", "Column7": "9927.0", "Column8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9": "3.86", "Column10": "2024-08-01T05:36:18.356111", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-09-26 22:44:48.796764", "Column1_2": "4533.0", "Column2_2": "9196.0", "Column3_2": "28.77", "Column4_2": "79.41", "Column5_2": "L", "Column6_2": "2022-07-11T21:54:29.430592", "Column7_2": "9590.0", "Column8_2": "fxbstxgqdo", "Column9_2": "59.74", "Column10_2": "2023-11-23T01:00:28.577753", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "8296.0", "Column2": "9233.0", "Column3": "49.15", "Column4": "56.49", "Column5": "L", "Column6": "2020-03-05T16:32:40.852441", "Column7": "6459.0", "Column8": "lvledfcknl", "Column9": "66.71", "Column10": "2024-12-17T21:05:44.806689", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-03-06 16:32:40.852441", "Column1_2": "8296.0", "Column2_2": "9233.0", "Column3_2": "49.15", "Column4_2": "56.49", "Column5_2": "L", "Column6_2": "2020-03-05T16:32:40.852441", "Column7_2": "6459.0", "Column8_2": "lvledfcknl", "Column9_2": "66.71", "Column10_2": "2024-12-17T21:05:44.806689", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5542.0", "Column2": "5715.0", "Column3": "86.5", "Column4": "49.99", "Column5": "S", "Column6": "2023-10-18T09:32:23.489753", "Column7": "7943.0", "Column8": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9": "64.92", "Column10": "2021-05-11T17:50:37.839088", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-10-19 09:32:23.489753", "Column1_2": "5542.0", "Column2_2": "5715.0", "Column3_2": "86.5", "Column4_2": "49.99", "Column5_2": "S", "Column6_2": "2023-10-18T09:32:23.489753", "Column7_2": "7943.0", "Column8_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9_2": "64.92", "Column10_2": "2021-05-11T17:50:37.839088", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3653.0", "Column2": "7700.0", "Column3": "76.42", "Column4": "94.42", "Column5": "S", "Column6": "2023-12-10T20:17:30.688296", "Column7": "84.0", "Column8": "lbanxfoyvh", "Column9": "16.23", "Column10": "2024-04-12T00:20:59.981113", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-12-11 20:17:30.688296", "Column1_2": "3653.0", "Column2_2": "1630.0", "Column3_2": "11.4", "Column4_2": "68.16", "Column5_2": "S", "Column6_2": "2020-05-25T11:48:14.400291", "Column7_2": "9782.0", "Column8_2": "faflgbf<PERSON><PERSON>", "Column9_2": "21.6", "Column10_2": "2024-03-01T00:17:35.710033", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2141.0", "Column2": "3631.0", "Column3": "76.29", "Column4": "91.53", "Column5": "L", "Column6": "2023-02-01T05:21:25.243086", "Column7": "9074.0", "Column8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9": "76.56", "Column10": "2022-05-20T11:10:40.204939", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-02-02 05:21:25.243086", "Column1_2": "2141.0", "Column2_2": "3631.0", "Column3_2": "76.29", "Column4_2": "91.53", "Column5_2": "L", "Column6_2": "2023-02-01T05:21:25.243086", "Column7_2": "9074.0", "Column8_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9_2": "76.56", "Column10_2": "2022-05-20T11:10:40.204939", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "8365.0", "Column2": "5962.0", "Column3": "6.42", "Column4": "58.62", "Column5": "S", "Column6": "2021-03-11T19:31:33.133644", "Column7": "1401.0", "Column8": "pimyystaqm", "Column9": "73.11", "Column10": "2020-07-22T23:27:25.790968", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-03-12 19:31:33.133644", "Column1_2": "8365.0", "Column2_2": "5962.0", "Column3_2": "6.42", "Column4_2": "58.62", "Column5_2": "S", "Column6_2": "2021-03-11T19:31:33.133644", "Column7_2": "1401.0", "Column8_2": "pimyystaqm", "Column9_2": "73.11", "Column10_2": "2020-07-22T23:27:25.790968", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "9738.0", "Column2": "3693.0", "Column3": "74.62", "Column4": "1.98", "Column5": "S", "Column6": "2023-07-09T05:27:33.886542", "Column7": "8090.0", "Column8": "oketytxfih", "Column9": "58.46", "Column10": "2022-04-30T20:25:53.462315", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-07-10 05:27:33.886542", "Column1_2": "9738.0", "Column2_2": "4134.0", "Column3_2": "41.93", "Column4_2": "48.11", "Column5_2": "L", "Column6_2": "2021-07-13T09:53:17.776897", "Column7_2": "4921.0", "Column8_2": "srat<PERSON><PERSON><PERSON>b", "Column9_2": "37.51", "Column10_2": "2022-12-26T00:16:06.942147", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3561.0", "Column2": "9004.0", "Column3": "25.38", "Column4": "74.77", "Column5": "L", "Column6": "2021-06-23T21:10:33.797920", "Column7": "18.0", "Column8": "qnvafmkide", "Column9": "0.57", "Column10": "2021-07-02T18:12:11.369046", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-06-24 21:10:33.797920", "Column1_2": "3561.0", "Column2_2": "9004.0", "Column3_2": "25.38", "Column4_2": "74.77", "Column5_2": "L", "Column6_2": "2021-06-23T21:10:33.797920", "Column7_2": "18.0", "Column8_2": "qnvafmkide", "Column9_2": "0.57", "Column10_2": "2021-07-02T18:12:11.369046", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2009.0", "Column2": "3526.0", "Column3": "54.16", "Column4": "39.04", "Column5": "S", "Column6": "2020-08-25T23:58:14.689434", "Column7": "1955.0", "Column8": "qxbxjgnypc", "Column9": "98.97", "Column10": "2020-08-02T23:21:13.531829", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-08-26 23:58:14.689434", "Column1_2": "2009.0", "Column2_2": "3526.0", "Column3_2": "54.16", "Column4_2": "39.04", "Column5_2": "S", "Column6_2": "2020-08-25T23:58:14.689434", "Column7_2": "1955.0", "Column8_2": "qxbxjgnypc", "Column9_2": "98.97", "Column10_2": "2020-08-02T23:21:13.531829", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1166.0", "Column2": "7142.0", "Column3": "36.89", "Column4": "68.01", "Column5": "S", "Column6": "2021-09-22T03:34:41.936657", "Column7": "3362.0", "Column8": "rwfzvmtrtq", "Column9": "28.03", "Column10": "2022-02-04T17:15:14.098010", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-09-23 03:34:41.936657", "Column1_2": "1166.0", "Column2_2": "4287.0", "Column3_2": "5.94", "Column4_2": "24.15", "Column5_2": "L", "Column6_2": "2020-07-28T12:34:14.778072", "Column7_2": "8687.0", "Column8_2": "bnlmqxaqhg", "Column9_2": "72.99", "Column10_2": "2024-03-28T13:14:11.348671", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "9738.0", "Column2": "8978.0", "Column3": "19.95", "Column4": "8.8", "Column5": "L", "Column6": "2023-03-13T16:43:02.141687", "Column7": "2806.0", "Column8": "apltyzyghh", "Column9": "12.63", "Column10": "2022-05-15T04:01:38.133667", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-03-14 16:43:02.141687", "Column1_2": "9738.0", "Column2_2": "4134.0", "Column3_2": "41.93", "Column4_2": "48.11", "Column5_2": "L", "Column6_2": "2021-07-13T09:53:17.776897", "Column7_2": "4921.0", "Column8_2": "srat<PERSON><PERSON><PERSON>b", "Column9_2": "37.51", "Column10_2": "2022-12-26T00:16:06.942147", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7121.0", "Column2": "940.0", "Column3": "45.94", "Column4": "71.9", "Column5": "L", "Column6": "2022-09-17T11:50:05.093931", "Column7": "904.0", "Column8": "hwbvexrhyw", "Column9": "20.64", "Column10": "2021-05-12T10:41:05.790807", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-09-18 11:50:05.093931", "Column1_2": "7121.0", "Column2_2": "940.0", "Column3_2": "45.94", "Column4_2": "71.9", "Column5_2": "L", "Column6_2": "2022-09-17T11:50:05.093931", "Column7_2": "904.0", "Column8_2": "hwbvexrhyw", "Column9_2": "20.64", "Column10_2": "2021-05-12T10:41:05.790807", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7854.0", "Column2": "5679.0", "Column3": "13.63", "Column4": "81.04", "Column5": "S", "Column6": "2023-08-03T03:46:17.117520", "Column7": "466.0", "Column8": "zggnkshjaq", "Column9": "97.86", "Column10": "2020-11-09T10:30:12.708211", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-08-04 03:46:17.117520", "Column1_2": "7854.0", "Column2_2": "5679.0", "Column3_2": "13.63", "Column4_2": "81.04", "Column5_2": "S", "Column6_2": "2023-08-03T03:46:17.117520", "Column7_2": "466.0", "Column8_2": "zggnkshjaq", "Column9_2": "97.86", "Column10_2": "2020-11-09T10:30:12.708211", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5478.0", "Column2": "3264.0", "Column3": "41.59", "Column4": "27.07", "Column5": "S", "Column6": "2021-04-27T09:20:50.017659", "Column7": "9638.0", "Column8": "bclxkltptw", "Column9": "67.17", "Column10": "2024-06-08T11:11:57.315521", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-04-28 09:20:50.017659", "Column1_2": "5478.0", "Column2_2": "3264.0", "Column3_2": "41.59", "Column4_2": "27.07", "Column5_2": "S", "Column6_2": "2021-04-27T09:20:50.017659", "Column7_2": "9638.0", "Column8_2": "bclxkltptw", "Column9_2": "67.17", "Column10_2": "2024-06-08T11:11:57.315521", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "8499.0", "Column2": "1781.0", "Column3": "99.42", "Column4": "99.94", "Column5": "S", "Column6": "2024-12-19T15:32:16.459902", "Column7": "1071.0", "Column8": "sgepkvyfam", "Column9": "2.26", "Column10": "2020-01-16T06:15:39.093263", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-12-20 15:32:16.459902", "Column1_2": "8499.0", "Column2_2": "1781.0", "Column3_2": "99.42", "Column4_2": "99.94", "Column5_2": "S", "Column6_2": "2024-12-19T15:32:16.459902", "Column7_2": "1071.0", "Column8_2": "sgepkvyfam", "Column9_2": "2.26", "Column10_2": "2020-01-16T06:15:39.093263", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2219.0", "Column2": "3425.0", "Column3": "82.59", "Column4": "26.86", "Column5": "S", "Column6": "2021-12-12T03:05:33.636490", "Column7": "6358.0", "Column8": "pydccphkic", "Column9": "46.24", "Column10": "2023-10-05T05:45:06.951248", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-12-13 03:05:33.636490", "Column1_2": "2219.0", "Column2_2": "1971.0", "Column3_2": "83.55", "Column4_2": "50.53", "Column5_2": "L", "Column6_2": "2022-04-26T00:34:53.081733", "Column7_2": "7322.0", "Column8_2": "uzyzntnoxb", "Column9_2": "13.81", "Column10_2": "2023-04-29T06:00:55.797289", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7409.0", "Column2": "3591.0", "Column3": "87.32", "Column4": "31.51", "Column5": "L", "Column6": "2022-06-27T00:17:56.846807", "Column7": "1281.0", "Column8": "xgjolfyzwj", "Column9": "78.28", "Column10": "2021-09-07T02:29:52.788506", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-06-28 00:17:56.846807", "Column1_2": "7409.0", "Column2_2": "5899.0", "Column3_2": "35.57", "Column4_2": "27.81", "Column5_2": "L", "Column6_2": "2023-01-04T00:08:50.314969", "Column7_2": "6229.0", "Column8_2": "bldevutufv", "Column9_2": "43.32", "Column10_2": "2020-02-19T16:10:56.430488", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1663.0", "Column2": "5373.0", "Column3": "40.15", "Column4": "25.6", "Column5": "S", "Column6": "2020-09-25T09:53:47.464502", "Column7": "6339.0", "Column8": "yyjfihzlsv", "Column9": "63.6", "Column10": "2021-11-05T14:50:25.342896", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-09-26 09:53:47.464502", "Column1_2": "1663.0", "Column2_2": "5373.0", "Column3_2": "40.15", "Column4_2": "25.6", "Column5_2": "S", "Column6_2": "2020-09-25T09:53:47.464502", "Column7_2": "6339.0", "Column8_2": "yyjfihzlsv", "Column9_2": "63.6", "Column10_2": "2021-11-05T14:50:25.342896", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2291.0", "Column2": "7646.0", "Column3": "75.23", "Column4": "86.99", "Column5": "L", "Column6": "2024-07-31T16:47:31.769712", "Column7": "4105.0", "Column8": "xum<PERSON><PERSON><PERSON>f", "Column9": "6.55", "Column10": "2023-03-08T05:16:52.746470", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-08-01 16:47:31.769712", "Column1_2": "2291.0", "Column2_2": "5983.0", "Column3_2": "15.4", "Column4_2": "77.26", "Column5_2": "S", "Column6_2": "2020-04-15T02:58:07.506109", "Column7_2": "3736.0", "Column8_2": "iaofmhuzoc", "Column9_2": "2.73", "Column10_2": "2024-03-12T22:02:10.607205", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6045.0", "Column2": "1691.0", "Column3": "92.47", "Column4": "40.24", "Column5": "L", "Column6": "2020-07-14T11:29:37.883313", "Column7": "1825.0", "Column8": "cwxwhvldjv", "Column9": "23.12", "Column10": "2021-05-01T21:05:18.971381", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-07-15 11:29:37.883313", "Column1_2": "6045.0", "Column2_2": "1691.0", "Column3_2": "92.47", "Column4_2": "40.24", "Column5_2": "L", "Column6_2": "2020-07-14T11:29:37.883313", "Column7_2": "1825.0", "Column8_2": "cwxwhvldjv", "Column9_2": "23.12", "Column10_2": "2021-05-01T21:05:18.971381", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7054.0", "Column2": "2804.0", "Column3": "88.33", "Column4": "8.29", "Column5": "S", "Column6": "2020-04-02T07:53:18.028208", "Column7": "3362.0", "Column8": "mzvpqhpulu", "Column9": "34.09", "Column10": "2023-12-24T08:10:35.593937", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-04-03 07:53:18.028208", "Column1_2": "7054.0", "Column2_2": "2804.0", "Column3_2": "88.33", "Column4_2": "8.29", "Column5_2": "S", "Column6_2": "2020-04-02T07:53:18.028208", "Column7_2": "3362.0", "Column8_2": "mzvpqhpulu", "Column9_2": "34.09", "Column10_2": "2023-12-24T08:10:35.593937", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "7724.0", "Column2": "5781.0", "Column3": "65.52", "Column4": "32.93", "Column5": "S", "Column6": "2020-01-27T01:08:44.877221", "Column7": "3615.0", "Column8": "glyiuzrejz", "Column9": "24.29", "Column10": "2024-03-21T11:34:19.880888", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-01-28 01:08:44.877221", "Column1_2": "7724.0", "Column2_2": "5781.0", "Column3_2": "65.52", "Column4_2": "32.93", "Column5_2": "S", "Column6_2": "2020-01-27T01:08:44.877221", "Column7_2": "3615.0", "Column8_2": "glyiuzrejz", "Column9_2": "24.29", "Column10_2": "2024-03-21T11:34:19.880888", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1230.0", "Column2": "7916.0", "Column3": "58.42", "Column4": "14.58", "Column5": "L", "Column6": "2024-06-01T08:55:39.556947", "Column7": "4148.0", "Column8": "fzrevnivkq", "Column9": "0.54", "Column10": "2024-06-27T03:25:07.223783", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-06-02 08:55:39.556947", "Column1_2": "1230.0", "Column2_2": "7916.0", "Column3_2": "58.42", "Column4_2": "14.58", "Column5_2": "L", "Column6_2": "2024-06-01T08:55:39.556947", "Column7_2": "4148.0", "Column8_2": "fzrevnivkq", "Column9_2": "0.54", "Column10_2": "2024-06-27T03:25:07.223783", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3844.0", "Column2": "1013.0", "Column3": "75.36", "Column4": "87.87", "Column5": "S", "Column6": "2020-02-12T09:39:20.720809", "Column7": "7356.0", "Column8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9": "61.76", "Column10": "2021-01-26T16:36:31.728071", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-02-13 09:39:20.720809", "Column1_2": "3844.0", "Column2_2": "1013.0", "Column3_2": "75.36", "Column4_2": "87.87", "Column5_2": "S", "Column6_2": "2020-02-12T09:39:20.720809", "Column7_2": "7356.0", "Column8_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Column9_2": "61.76", "Column10_2": "2021-01-26T16:36:31.728071", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5475.0", "Column2": "9383.0", "Column3": "14.06", "Column4": "68.57", "Column5": "S", "Column6": "2020-07-10T07:42:11.270281", "Column7": "9687.0", "Column8": "fcaxeapalp", "Column9": "49.75", "Column10": "2024-02-16T05:37:31.096911", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-07-11 07:42:11.270281", "Column1_2": "5475.0", "Column2_2": "9383.0", "Column3_2": "14.06", "Column4_2": "68.57", "Column5_2": "S", "Column6_2": "2020-07-10T07:42:11.270281", "Column7_2": "9687.0", "Column8_2": "fcaxeapalp", "Column9_2": "49.75", "Column10_2": "2024-02-16T05:37:31.096911", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3435.0", "Column2": "6495.0", "Column3": "90.78", "Column4": "29.8", "Column5": "S", "Column6": "2021-01-24T17:03:02.160093", "Column7": "4215.0", "Column8": "zntdszrnnz", "Column9": "36.07", "Column10": "2023-04-08T05:18:51.150297", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-01-25 17:03:02.160093", "Column1_2": "3435.0", "Column2_2": "6495.0", "Column3_2": "90.78", "Column4_2": "29.8", "Column5_2": "S", "Column6_2": "2021-01-24T17:03:02.160093", "Column7_2": "4215.0", "Column8_2": "zntdszrnnz", "Column9_2": "36.07", "Column10_2": "2023-04-08T05:18:51.150297", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6448.0", "Column2": "3283.0", "Column3": "65.13", "Column4": "64.04", "Column5": "S", "Column6": "2021-12-01T18:16:03.772331", "Column7": "4217.0", "Column8": "haosnbsmmj", "Column9": "40.76", "Column10": "2020-06-24T21:29:16.753700", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-12-02 18:16:03.772331", "Column1_2": "6448.0", "Column2_2": "3283.0", "Column3_2": "65.13", "Column4_2": "64.04", "Column5_2": "S", "Column6_2": "2021-12-01T18:16:03.772331", "Column7_2": "4217.0", "Column8_2": "haosnbsmmj", "Column9_2": "40.76", "Column10_2": "2020-06-24T21:29:16.753700", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1594.0", "Column2": "1216.0", "Column3": "95.12", "Column4": "85.8", "Column5": "L", "Column6": "2022-10-16T02:01:45.243440", "Column7": "2516.0", "Column8": "ntexhyysfr", "Column9": "78.3", "Column10": "2020-03-04T18:59:35.441438", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-10-17 02:01:45.243440", "Column1_2": "1594.0", "Column2_2": "131.0", "Column3_2": "58.33", "Column4_2": "87.46", "Column5_2": "S", "Column6_2": "2020-01-23T08:55:27.705850", "Column7_2": "5311.0", "Column8_2": "whnoexjlhr", "Column9_2": "24.93", "Column10_2": "2021-04-17T17:18:56.111870", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6650.0", "Column2": "7901.0", "Column3": "52.48", "Column4": "34.5", "Column5": "L", "Column6": "2021-05-27T23:57:56.353643", "Column7": "4554.0", "Column8": "podengdiwj", "Column9": "3.9", "Column10": "2022-10-07T04:27:04.778222", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2021-05-28 23:57:56.353643", "Column1_2": "6650.0", "Column2_2": "7901.0", "Column3_2": "52.48", "Column4_2": "34.5", "Column5_2": "L", "Column6_2": "2021-05-27T23:57:56.353643", "Column7_2": "4554.0", "Column8_2": "podengdiwj", "Column9_2": "3.9", "Column10_2": "2022-10-07T04:27:04.778222", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "878.0", "Column2": "2405.0", "Column3": "89.98", "Column4": "79.64", "Column5": "S", "Column6": "2024-02-20T12:08:21.305734", "Column7": "6500.0", "Column8": "mhpzjloqoe", "Column9": "5.68", "Column10": "2020-05-10T07:50:49.027321", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-02-21 12:08:21.305734", "Column1_2": "878.0", "Column2_2": "3443.0", "Column3_2": "44.23", "Column4_2": "43.53", "Column5_2": "L", "Column6_2": "2023-07-25T02:55:42.177838", "Column7_2": "2011.0", "Column8_2": "bpebkccnqp", "Column9_2": "8.81", "Column10_2": "2020-01-10T12:14:53.835997", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3764.0", "Column2": "9917.0", "Column3": "68.45", "Column4": "95.15", "Column5": "S", "Column6": "2020-06-18T21:25:28.170829", "Column7": "4770.0", "Column8": "pippuxxlwe", "Column9": "46.06", "Column10": "2023-11-01T14:28:11.049428", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-06-19 21:25:28.170829", "Column1_2": "3764.0", "Column2_2": "9917.0", "Column3_2": "68.45", "Column4_2": "95.15", "Column5_2": "S", "Column6_2": "2020-06-18T21:25:28.170829", "Column7_2": "4770.0", "Column8_2": "pippuxxlwe", "Column9_2": "46.06", "Column10_2": "2023-11-01T14:28:11.049428", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "9369.0", "Column2": "217.0", "Column3": "50.69", "Column4": "37.58", "Column5": "S", "Column6": "2022-10-30T14:08:54.080833", "Column7": "593.0", "Column8": "kuelqqpozq", "Column9": "59.86", "Column10": "2022-04-14T06:36:05.582476", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-10-31 14:08:54.080833", "Column1_2": "9369.0", "Column2_2": "217.0", "Column3_2": "50.69", "Column4_2": "37.58", "Column5_2": "S", "Column6_2": "2022-10-30T14:08:54.080833", "Column7_2": "593.0", "Column8_2": "kuelqqpozq", "Column9_2": "59.86", "Column10_2": "2022-04-14T06:36:05.582476", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "8862.0", "Column2": "9209.0", "Column3": "35.19", "Column4": "27.76", "Column5": "L", "Column6": "2020-01-31T03:16:36.037846", "Column7": "1001.0", "Column8": "xuioxooboq", "Column9": "30.14", "Column10": "2020-09-11T07:54:49.271848", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-02-01 03:16:36.037846", "Column1_2": "8862.0", "Column2_2": "9209.0", "Column3_2": "35.19", "Column4_2": "27.76", "Column5_2": "L", "Column6_2": "2020-01-31T03:16:36.037846", "Column7_2": "1001.0", "Column8_2": "xuioxooboq", "Column9_2": "30.14", "Column10_2": "2020-09-11T07:54:49.271848", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "6899.0", "Column2": "5639.0", "Column3": "21.18", "Column4": "31.03", "Column5": "L", "Column6": "2023-09-25T06:18:40.473722", "Column7": "8950.0", "Column8": "tjztioevsc", "Column9": "77.97", "Column10": "2024-04-26T16:20:48.052865", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-09-26 06:18:40.473722", "Column1_2": "6899.0", "Column2_2": "5639.0", "Column3_2": "21.18", "Column4_2": "31.03", "Column5_2": "L", "Column6_2": "2023-09-25T06:18:40.473722", "Column7_2": "8950.0", "Column8_2": "tjztioevsc", "Column9_2": "77.97", "Column10_2": "2024-04-26T16:20:48.052865", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3434.0", "Column2": "6946.0", "Column3": "53.42", "Column4": "45.3", "Column5": "L", "Column6": "2020-12-15T01:54:01.004166", "Column7": "9786.0", "Column8": "yzajcmdnyt", "Column9": "92.92", "Column10": "2022-02-12T00:13:02.764102", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-12-16 01:54:01.004166", "Column1_2": "3434.0", "Column2_2": "6946.0", "Column3_2": "53.42", "Column4_2": "45.3", "Column5_2": "L", "Column6_2": "2020-12-15T01:54:01.004166", "Column7_2": "9786.0", "Column8_2": "yzajcmdnyt", "Column9_2": "92.92", "Column10_2": "2022-02-12T00:13:02.764102", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "903.0", "Column2": "2063.0", "Column3": "80.88", "Column4": "71.13", "Column5": "S", "Column6": "2023-10-17T15:55:18.357652", "Column7": "3493.0", "Column8": "envtsvvcmo", "Column9": "66.15", "Column10": "2020-11-28T15:55:47.941806", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-10-18 15:55:18.357652", "Column1_2": "903.0", "Column2_2": "4075.0", "Column3_2": "98.99", "Column4_2": "6.67", "Column5_2": "S", "Column6_2": "2023-12-09T11:45:33.050533", "Column7_2": "5623.0", "Column8_2": "mxwqldkmwv", "Column9_2": "30.03", "Column10_2": "2024-01-30T05:07:22.899849", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "4763.0", "Column2": "5915.0", "Column3": "51.98", "Column4": "7.84", "Column5": "L", "Column6": "2020-06-15T03:47:51.392547", "Column7": "4662.0", "Column8": "clurpicyxe", "Column9": "77.78", "Column10": "2022-03-05T00:48:08.197705", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-06-16 03:47:51.392547", "Column1_2": "4763.0", "Column2_2": "5915.0", "Column3_2": "51.98", "Column4_2": "7.84", "Column5_2": "L", "Column6_2": "2020-06-15T03:47:51.392547", "Column7_2": "4662.0", "Column8_2": "clurpicyxe", "Column9_2": "77.78", "Column10_2": "2022-03-05T00:48:08.197705", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3383.0", "Column2": "5827.0", "Column3": "12.31", "Column4": "7.46", "Column5": "L", "Column6": "2022-04-10T09:58:21.338614", "Column7": "3725.0", "Column8": "jivdwexdfw", "Column9": "13.09", "Column10": "2021-08-18T08:03:00.685227", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-04-11 09:58:21.338614", "Column1_2": "3383.0", "Column2_2": "8268.0", "Column3_2": "99.9", "Column4_2": "32.75", "Column5_2": "S", "Column6_2": "2021-04-30T21:20:14.964785", "Column7_2": "3120.0", "Column8_2": "wsnkopaotd", "Column9_2": "4.46", "Column10_2": "2023-04-15T05:09:14.454583", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1523.0", "Column2": "5422.0", "Column3": "80.06", "Column4": "67.85", "Column5": "S", "Column6": "2023-03-14T13:18:38.934137", "Column7": "5793.0", "Column8": "miovepuzmz", "Column9": "82.76", "Column10": "2023-10-31T15:29:07.050012", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-03-15 13:18:38.934137", "Column1_2": "1523.0", "Column2_2": "1368.0", "Column3_2": "91.39", "Column4_2": "86.3", "Column5_2": "L", "Column6_2": "2021-09-18T06:12:44.525293", "Column7_2": "9906.0", "Column8_2": "dqdhyhskrj", "Column9_2": "13.37", "Column10_2": "2022-05-16T14:01:20.095998", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "3211.0", "Column2": "4123.0", "Column3": "2.62", "Column4": "69.58", "Column5": "L", "Column6": "2020-07-11T01:55:22.049286", "Column7": "4366.0", "Column8": "eklvdilojs", "Column9": "48.69", "Column10": "2021-10-16T20:58:07.169503", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-07-12 01:55:22.049286", "Column1_2": "3211.0", "Column2_2": "4123.0", "Column3_2": "2.62", "Column4_2": "69.58", "Column5_2": "L", "Column6_2": "2020-07-11T01:55:22.049286", "Column7_2": "4366.0", "Column8_2": "eklvdilojs", "Column9_2": "48.69", "Column10_2": "2021-10-16T20:58:07.169503", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "1218.0", "Column2": "4275.0", "Column3": "73.36", "Column4": "65.85", "Column5": "S", "Column6": "2022-03-13T13:07:30.633346", "Column7": "87.0", "Column8": "zkbdqbrwox", "Column9": "34.65", "Column10": "2023-03-27T13:57:38.835314", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2022-03-14 13:07:30.633346", "Column1_2": "1218.0", "Column2_2": "4275.0", "Column3_2": "73.36", "Column4_2": "65.85", "Column5_2": "S", "Column6_2": "2022-03-13T13:07:30.633346", "Column7_2": "87.0", "Column8_2": "zkbdqbrwox", "Column9_2": "34.65", "Column10_2": "2023-03-27T13:57:38.835314", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5701.0", "Column2": "5089.0", "Column3": "5.1", "Column4": "14.58", "Column5": "S", "Column6": "2024-10-26T04:17:11.046892", "Column7": "1446.0", "Column8": "rccdqgctxp", "Column9": "22.08", "Column10": "2022-09-11T01:03:17.042175", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2024-10-27 04:17:11.046892", "Column1_2": "5701.0", "Column2_2": "5089.0", "Column3_2": "5.1", "Column4_2": "14.58", "Column5_2": "S", "Column6_2": "2024-10-26T04:17:11.046892", "Column7_2": "1446.0", "Column8_2": "rccdqgctxp", "Column9_2": "22.08", "Column10_2": "2022-09-11T01:03:17.042175", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "2641.0", "Column2": "2228.0", "Column3": "91.69", "Column4": "98.35", "Column5": "S", "Column6": "2020-02-07T09:01:47.811133", "Column7": "6042.0", "Column8": "kvzwpcrjcw", "Column9": "12.86", "Column10": "2022-01-02T17:40:04.240947", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2020-02-08 09:01:47.811133", "Column1_2": "2641.0", "Column2_2": "2228.0", "Column3_2": "91.69", "Column4_2": "98.35", "Column5_2": "S", "Column6_2": "2020-02-07T09:01:47.811133", "Column7_2": "6042.0", "Column8_2": "kvzwpcrjcw", "Column9_2": "12.86", "Column10_2": "2022-01-02T17:40:04.240947", "file_name_2": "random_data_R1_1747312845668.csv"}, {"Column1": "5723.0", "Column2": "7799.0", "Column3": "70.89", "Column4": "3.76", "Column5": "S", "Column6": "2023-06-11T17:05:51.567861", "Column7": "7443.0", "Column8": "akqmrapdve", "Column9": "23.16", "Column10": "2022-11-26T01:59:33.873799", "file_name": "random_data_R1_1747312832131.csv", "Column11_Derived": "2023-06-12 17:05:51.567861", "Column1_2": "5723.0", "Column2_2": "7799.0", "Column3_2": "70.89", "Column4_2": "3.76", "Column5_2": "S", "Column6_2": "2023-06-11T17:05:51.567861", "Column7_2": "7443.0", "Column8_2": "akqmrapdve", "Column9_2": "23.16", "Column10_2": "2022-11-26T01:59:33.873799", "file_name_2": "random_data_R1_1747312845668.csv"}]}, "total_time": 2, "current_url": "https://dq-dev.vis-testing.nyc/research-query-results/50/dashboard", "result_files": {"research_query_result_files": [{"research_query_name": "Resource 1", "result_file_path": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\generic_research_query_results\\RQDemo20250515_Resource 1_05_15_2025_134636\\generic_research_query_report.csv", "resource_result_files": [{"resource_id": 914, "resource_name": "DEMO_CRD_May_15", "null_key_result_file": null, "filter_rules_result_file": ""}]}, {"research_query_name": "External 1", "result_file_path": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\generic_research_query_results\\RQDemo20250515_External 1_05_15_2025_134636\\generic_research_query_report.csv", "resource_result_files": [{"resource_id": 914, "resource_name": "DEMO_CRD_May_15", "null_key_result_file": null, "filter_rules_result_file": ""}]}, {"research_query_name": "Mixed 1", "result_file_path": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\generic_research_query_results\\RQDemo20250515_Mixed 1_05_15_2025_134636\\generic_research_query_report.csv", "resource_result_files": [{"resource_id": 914, "resource_name": "DEMO_CRD_May_15", "null_key_result_file": null, "filter_rules_result_file": ""}]}, {"research_query_name": "Mixed 2", "result_file_path": "C:\\inetpub\\wwwroot\\VIS-http\\download-files\\generic_research_query_results\\RQDemo20250515_Mixed 2_05_15_2025_134636\\generic_research_query_report.csv", "resource_result_files": [{"resource_id": 914, "resource_name": "DEMO_CRD_May_15", "null_key_result_file": null, "filter_rules_result_file": ""}, {"resource_id": 2193, "resource_name": "Position_R1", "null_key_result_file": null, "filter_rules_result_file": ""}, {"resource_id": 2194, "resource_name": "Position_R2", "null_key_result_file": null, "filter_rules_result_file": ""}]}]}}