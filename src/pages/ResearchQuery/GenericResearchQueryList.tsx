import React, { useEffect, useState, useMemo } from "react";
import { Box, Grid, IconButton, Tooltip } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { GridColDef } from "@mui/x-data-grid";
import AddSharpIcon from "@mui/icons-material/AddSharp";

import FlexBetween from "../../components/FlexBetween";
import DataTable from "../../components/DataGrids/DataGrid";
import ButtonComponent from "../../components/Button.Component";
import Loader from "../../components/Molecules/Loader/Loader";
import ReadMoreLess from "../../components/ReadMoreLess";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
import { getFormattedDateTime } from "../../services/utils";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { paginatedResponseFormat } from "../../services/constants";
import useFetchPaginatedGenericResearchQueries from "../../hooks/useFetchPaginatedGenericResearchQueries";
import { deleteGenericResearchQueryById } from "../../services/ResearchQueriesService";
import { useToast } from "../../services/utils";
import ConfirmationDialog from "../../components/Dialogs/ConfirmationDialog";
import {
  IconDeleteBlueSvg,
  IconExecuteBase,
  IconEyeBase,
} from "../../common/utils/icons";
const DEFAULT_PAGE = parseInt(process.env.REACT_APP_DEFAULT_PAGE || "1", 10);
const DEFAULT_PAGE_SIZE = parseInt(
  process.env.REACT_APP_DEFAULT_PAGE_SIZE || "25",
  10
);

const GenericResearchQueryList: React.FC = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(DEFAULT_PAGE);
  const [pSize, setPSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { showToast } = useToast();
  const [
    openValidationExecutionConfirmation,
    setOpenValidationExecutionConfirmation,
  ] = useState(false);
  const [selectedExecutionToDelete, setSelectedExecutionToDelete] =
    useState<any>(null);

  const [genericResearchQueryData, setGenericResearchQueryData] = useState<any>(
    paginatedResponseFormat
  );

  const [fetchedResourcesData] = useFetchPaginatedGenericResearchQueries({
    setIsLoading,
    page,
    pSize,
  });

  useEffect(() => {
    if (fetchedResourcesData) {
      setGenericResearchQueryData(fetchedResourcesData);
    }
  }, [fetchedResourcesData]);

  const handleAddNew = () => navigate("/research-query/add");
  const handleView = (id: number) => navigate(`/research-query/view/${id}`);
  const dataTableColumns: GridColDef[] = useMemo(
    () => [
      {
        ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
        width: 1,
        minWidth: 20,
        flex: 0,
        renderCell: () => <></>,
        cellClassName: "no-padding-cell",
        headerClassName: "no-padding-cell",
      },
      {
        field: "id",
        headerName: "Id",
        minWidth: 50,
        renderCell: (params) => <span>{params.value}</span>,
      },
      {
        field: "name",
        headerName: "Query Name",
        minWidth: 150,
        renderCell: (params) => (
          <LimitChractersWithTooltip value={params.value} />
        ),
      },
      {
        field: "code",
        headerName: "Code",
        minWidth: 150,
        renderCell: (params) => (
          <LimitChractersWithTooltip value={params.value} />
        ),
      },
      {
        field: "description",
        headerName: "Description",
        minWidth: 150,
        flex: 1,
        groupable: false,
        renderCell: (params) =>
          params.value && params.value.length > 50 ? (
            <ReadMoreLess
              data={params.value.replace(/"/g, "")}
              charLimit={50}
            />
          ) : (
            params.value?.replace(/"/g, "")
          ),
      },
      {
        field: "modified_by_user",
        headerName: "Modified By",
        minWidth: 150,
        flex: 1,
        renderCell: (params) => (
          <LimitChractersWithTooltip value={params?.value?.username} />
        ),
      },
      {
        field: "updated_on",
        headerName: "Last Modified",
        minWidth: 180,
        flex: 1,
        renderCell: (params) => getFormattedDateTime(params.value),
      },
      {
        field: "action",
        headerName: "Action",
        width: 130,
        align: "center",
        headerAlign: "center",
        groupable: false,
        renderCell: (params: any) => {
          return (
            <>
              <Tooltip title="Execute Query" placement="top" arrow>
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    navigate(
                      `/research-query/research-query-execution/${params.row.id}`
                    )
                  }
                >
                  <IconExecuteBase />
                </IconButton>
              </Tooltip>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => handleView(params.row.id)}
              >
                <IconEyeBase />
              </IconButton>

              <IconButton
                onClick={() => {
                  setSelectedExecutionToDelete(params.row);
                  setOpenValidationExecutionConfirmation(true);
                }}
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </>
          );
        },
      },
    ],
    [handleAddNew]
  );
  const handleValidationExecutionDelete = async () => {
    setOpenValidationExecutionConfirmation(false);
    if (!selectedExecutionToDelete) return;
    setIsLoading(true);
    try {
      await deleteGenericResearchQueryById(selectedExecutionToDelete.id);
      const updatedList = genericResearchQueryData?.items?.filter(
        (item: any) => item.id !== selectedExecutionToDelete.id
      );
      setGenericResearchQueryData((prev: any) => ({
        ...prev,
        items: updatedList,
      }));

      showToast(
        `${selectedExecutionToDelete.name} deleted successfully`,
        "success"
      );
    } catch (error) {
      console.error("Error deleting query:", error);
      showToast("Failed to delete the query. Please try again.", "error");
    } finally {
      setIsLoading(false);
      setSelectedExecutionToDelete(null);
    }
  };

  return (
    <Box>
      {isLoading && <Loader isLoading />}
      <Grid
        container
        justifyContent="space-between"
        alignItems="center"
        className="text-box-card compact-text-box-card"
      >
        <Grid item xs>
          <FlexBetween sx={{ justifyContent: "flex-end", columnGap: 2 }}>
            <ButtonComponent
              handelClickEvent={handleAddNew}
              className="btn-orange"
            >
              <>
                <AddSharpIcon sx={{ marginRight: "4px" }} /> Research Query
              </>
            </ButtonComponent>
          </FlexBetween>
        </Grid>
      </Grid>

      <FlexBetween>
        <DataTable
          dataColumns={dataTableColumns}
          dataRows={genericResearchQueryData?.items || []}
          loading={isLoading}
          dataListTitle="Research Query List"
          className="dataTable no-radius hide-progress-icon"
          paginationMode="server"
          rowCount={genericResearchQueryData?.total || 0}
          pageSizeOptions={[25]}
          paginationModel={{ page: page - 1, pageSize: pSize }}
          onPaginationModelChange={(params) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={358}
          isPaginationRequired={true}
        />
      </FlexBetween>
      <ConfirmationDialog
        title={"Confirm removing Research Query"}
        dialogContent={"Are you sure you want to delete this query?"}
        handleCancel={() => setOpenValidationExecutionConfirmation(false)}
        openConfirmation={openValidationExecutionConfirmation}
        handleConfirm={handleValidationExecutionDelete}
        isShowClassMb0={false}
      />
    </Box>
  );
};

export default GenericResearchQueryList;
