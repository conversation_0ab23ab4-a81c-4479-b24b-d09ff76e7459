import {
  <PERSON>,
  Grid,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import FlexBetween from "../../components/FlexBetween";
import RuleExecutionTab from "../../components/Rules/RuleExecutionTab";
import useFetchFileProcessing from "../../hooks/useFetchFileProcessing";
import useFetchResourcesByIdMultipleIds from "../../hooks/useFetchResourcesByMultipleIds";
import useFetchGenericResearchQueryById from "../../hooks/useFetchGenericResearchQueryById";
import { executeResearchQueryById } from "../../services/researchQueryExecutionService";
import ResultStorageParameters from "../../components/Rules/ResultStorageParameters";
import ResearchQueryRunParameters from "../../components/ResearchQuery/ResearchQueryRunParameters";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useToast } from "../../services/utils";
import Loader from "../../components/Molecules/Loader/Loader";
import useFetchConnectionKeysAll from "../../hooks/useFetchConnectionKeysAll";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import useFetchAllResourcesWithoutDomain from "../../hooks/useFetchAllResourcesWithoutDomain";
import { processResearchQueryExecutionData } from "../../services/utils/processResearchQueryExecutionData";

import { executionNameSchema } from "../../schemas";
import ExecutionName from "../../components/Molecules/Rule/ExecutionName";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import GenericResearchQuery from "./GenericResearchQuery";
import InlineVariables from "../Resource/InlineVariables";
import { getDifferentValues } from "../../services/utils/processRuleExecutionResourcesData";
import { sqlDatabaseType } from "../../services/constants";
import { IconBtnEditBase } from "../../common/utils/icons";

const ResearchQueryExecution = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { id: currentResearchQueryId } = useParams();
  const { showToast } = useToast();
  const {
    isResourceEdit,
    viewInlineVariables,
    setViewInlineVariables,
    setAllResourcesData,
    setLinkedServicesData,
    fileProcessingData,
    setFileProcessingData,
    globalVariables,
    setGlobalVariables,
  } = useRuleResourceContext();
  const [resourceIds, setResourceIds] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [resourcesData, setResourcesData] = useState<any[]>([]);
  const [originalResourcesData, setOriginalResourcesData] = useState<any[]>([]);
  const [isEditResource, setIsEditResource] = useState(false);
  const [resultStorageParameters, setResultStorageParameters] = useState<any>({
    linked_service_id: process.env.REACT_APP_OUTPUT_STORAGE_LINKED_SERVICE_ID,
    linked_service_code:
      process.env.REACT_APP_OUTPUT_STORAGE_LINKED_SERVICE_CODE,
    connection_key_id: process.env.REACT_APP_OUTPUT_STORAGE_CONNECTION_KEY,
    file_path: process.env.REACT_APP_OUTPUT_FILES_BASE_PATH,
  });
  const [runParameters, setRunParameters] = useState<any>({
    no_of_records_in_response: 0,
    no_of_records_in_output_files: 0,
    no_of_records_in_filter_rule_output_files: 0,
    generate_output_files: true,
    pull_new_files_from_server: true,
    pull_latest_files: false,
    keep_downloaded_files: false,
    save_input_data_files: false,
    report_name: "generic_research_query_report",
    run_instance: {
      run_id: 1,
      run_name: "Legacy",
    },
  });
  const [researchQueryDetailData, setResearchQueryDetailData] = useState<any>(
    {}
  );
  const [originalResearchQueryDetailData, setOriginalResearchQueryDetailData] =
    useState<any>({});
  const [isEditResearchQuery, setIsEditResearchQuery] = useState(false);

  const [researchQueryData] = useFetchGenericResearchQueryById({
    setIsLoading,
    currentRQId: currentResearchQueryId,
  });
  const [resourceData] = useFetchResourcesByIdMultipleIds({
    resourceIds,
    setIsLoading,
  });
  const [fileProcessing] = useFetchFileProcessing({ setIsLoading });
  const [fetchedConnectionKeys] = useFetchConnectionKeysAll({ setIsLoading });
  const [allResources] = useFetchAllResourcesWithoutDomain({
    setIsLoading,
    aggregation_type: "aggregated",
  });
  const [linkedServices] = useFetchLinkedServices({ setIsLoading });

  const [expandedAccordion, setExpandedAccordion] = useState<any>("");

  const [inlineVariables, setInlineVariables] = useState<any>({});
  const [isEditVariables, setIsEditVariables] = useState(false);

  const [initialInlineVariables, setInitialInlineVariables] = useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  const [executionName, setExecutionName] = useState("");
  const [executionMode, setExecutionMode] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: any }>({
    execution_name: "",
  });

  const navigate = useNavigate();

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources, setAllResourcesData]);

  useEffect(() => {
    if (linkedServices) {
      setLinkedServicesData(linkedServices);
    }
  }, [linkedServices, setLinkedServicesData]);

  useEffect(() => {
    if (fileProcessing) {
      setFileProcessingData(fileProcessing);
    }
  }, [fileProcessing, setFileProcessingData]);

  useEffect(() => {
    if (researchQueryData) {
      setResearchQueryDetailData(researchQueryData);
      setOriginalResearchQueryDetailData(researchQueryData);
      if (researchQueryData?.variables) {
        setInlineVariables(researchQueryData?.variables);
        setGlobalVariables(researchQueryData?.variables);
        setInitialInlineVariables(
          JSON.parse(
            JSON.stringify({
              ...researchQueryData?.variables,
            })
          )
        );
      }
      setExecutionName(researchQueryData?.name);
      setCurrentBreadcrumbPage({
        name: researchQueryData?.name,
        id: researchQueryData?.id,
      });
      return () => {
        setCurrentBreadcrumbPage({ name: "", id: null });
      };
    }
  }, [researchQueryData]);

  useEffect(() => {
    // Extract resource_ids from research_queries
    if (
      researchQueryDetailData?.research_queries &&
      researchQueryDetailData.research_queries.length > 0
    ) {
      const extractedResourceIds: number[] = [];

      researchQueryDetailData.research_queries.forEach((query: any) => {
        if (query.source && query.source.length > 0) {
          query.source.forEach((source: any) => {
            // Check if type is Resource or if type is Mixed with sub_type Resource
            if (
              (source.type === "Resource" && source.resource_id) ||
              (source.type === "Mixed" &&
                source.sub_type === "Resource" &&
                source.resource_id)
            ) {
              extractedResourceIds.push(source.resource_id);
            }
          });
        }
      });

      // Set the extracted resource_ids
      if (extractedResourceIds.length > 0) {
        setResourceIds(extractedResourceIds);
      }
    }
  }, [researchQueryDetailData]);
  useEffect(() => {
    console.log("resourcesData", resourcesData);
  }, [resourcesData]);

  const getUpdatedResourceDataList = (resourceDataList: any[]) => {
    if (resourceDataList?.length <= 0) return resourceDataList;
    return resourceDataList.map((resourceData) => {
      if (!resourceData) return resourceData;

      const connectionKeyDetail = fetchedConnectionKeys?.find(
        (option: { id: any }) =>
          option.id ===
          resourceData?.additional_properties?.resource_definition
            ?.api_definition?.connection_key
      );

      if (
        resourceData.linked_service_id &&
        resourceData?.additional_properties?.resource_definition?.type
      ) {
        let updatedResourceDefinition =
          resourceData?.additional_properties?.resource_definition?.[
            `${resourceData?.additional_properties?.resource_definition?.type}_definition`
          ];

        if (
          sqlDatabaseType.includes(
            resourceData?.additional_properties?.resource_definition?.type
          )
        ) {
          updatedResourceDefinition =
            resourceData?.additional_properties?.resource_definition
              ?.sql_definition;
        }

        return {
          ...resourceData,
          additional_properties: {
            ...resourceData.additional_properties,
            resource_definition: {
              ...resourceData.additional_properties.resource_definition,
              ...updatedResourceDefinition,
              api_definition: {
                ...resourceData?.additional_properties?.resource_definition
                  ?.api_definition,
                url: connectionKeyDetail?.api_url,
                request_timeout: 0,
              },
            },
          },
        };
      } else {
        return resourceData;
      }
    });
  };

  useEffect(() => {
    if (resourceData) {
      const updatedResourceData = getUpdatedResourceDataList(resourceData);
      setResourcesData(updatedResourceData);
      // Create a deep copy of updatedResourceData to ensure originalResourcesData is independent
      setOriginalResourcesData(JSON.parse(JSON.stringify(updatedResourceData)));
    }
  }, [resourceData]);

  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const inlineVariablesChanged =
      JSON.stringify(inlineVariables) !==
      JSON.stringify(initialInlineVariables);

    setHasChanges(inlineVariablesChanged);
  }, [inlineVariables, initialInlineVariables]);

  const handleChangeAccordion = (panel: any) => (_: any, isExpanded: any) => {
    // Check if any accordion is in edit mode
    if (
      isResourceEdit !== "" ||
      isEditResource ||
      isEditVariables ||
      isEditResearchQuery
    ) {
      showToast("Please save changes first!!", "warning");
      return;
    }
    setExpandedAccordion(isExpanded ? panel : null);
  };

  const handleChangeVariable = (event: any) => {
    const { name, value } = event.target;

    setInlineVariables((prev: any) => ({
      ...prev,
      [name]: value,
    }));
    setGlobalVariables((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleExecuteSubmit = async () => {
    try {
      await executionNameSchema.validate(
        { execution_name: executionName },
        {
          abortEarly: false,
        }
      );
      // const differentResearchQueryData = getDifferentValues(
      //   researchQueryDetailData,
      //   originalResearchQueryDetailData
      // );
      // Create execution payload using the utility function
      const reqBody = processResearchQueryExecutionData(
        researchQueryDetailData,
        resourcesData,
        originalResourcesData,
        resultStorageParameters,
        runParameters,
        inlineVariables,
        executionName
      );

      // Execute the research query
      setIsBackdropLoading(true);

      // Execute the research query
      executeResearchQueryById(Number(currentResearchQueryId), reqBody)
        .then((response: any) => {
          showToast("Research query execution successful!", "success");
          navigate(
            `/research-query/${currentResearchQueryId}/${response?.id}/dashboard`,
            {
              state: {
                isResearchQueryExecutionResponse: true,
                rowData: response,
              },
            }
          );
        })
        .catch((error: any) => {
          console.error(error);
          showToast("Failed to execute research query", "error");
        })
        .finally(() => {
          setIsBackdropLoading(false);
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  return (
    <>
      <Loader isLoading={isBackdropLoading} />
      <Loader isLoading={isLoading} />
      <div
        className="accordion-panel"
        style={{ marginBottom: "0", marginTop: "8px" }}
      >
        <Box>
          <ExecutionName
            handleChangeAccordion={handleChangeAccordion}
            errors={errors}
            setErrors={setErrors}
            executionName={executionName}
            setExecutionName={setExecutionName}
            executionMode={executionMode}
            setExecutionMode={setExecutionMode}
            isShowExecutionMode={false}
          />
          <Accordion
            className="mt-6 heading-bold box-shadow"
            expanded={expandedAccordion === "research-queries"}
            onChange={handleChangeAccordion("research-queries")}
          >
            <AccordionSummary
              aria-controls="research-queries-content"
              id="research-queries-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Research Queries
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <GenericResearchQuery
                researchQuery={researchQueryDetailData}
                setResearchQuery={setResearchQueryDetailData}
                isFromExecution={true}
                isEditResearchQuery={isEditResearchQuery}
                setIsEditResearchQuery={setIsEditResearchQuery}
              />
            </AccordionDetails>
          </Accordion>
          {resourcesData?.length > 0 && (
            <Accordion
              className="mt-6 heading-bold box-shadow"
              expanded={expandedAccordion === "resources-data"}
              onChange={handleChangeAccordion("resources-data")}
            >
              <AccordionSummary
                aria-controls="resources-data-content"
                id="resources-data-header"
                expandIcon={<ExpandMoreIcon />}
              >
                Resources Data
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                {resourcesData.map((resource: any, index: number) => (
                  <>
                    <React.Fragment key={resource?.id}>
                      <RuleExecutionTab
                        resourcesData={resourcesData}
                        resourceData={resource}
                        isLoading={isBackdropLoading}
                        setResourcesData={setResourcesData}
                        setOriginalResourcesData={setOriginalResourcesData}
                        setIsLoading={setIsLoading}
                        fileProcessingData={fileProcessingData}
                        fileStreamIndex={index}
                        setViewInlineVariables={setViewInlineVariables}
                        viewInlineVariables={viewInlineVariables}
                        resource_type={`main-resource-${resource?.id}`}
                        resourcePath={`resources[${index}]`}
                        isFromQueryExecution={true}
                      />
                    </React.Fragment>
                  </>
                ))}
              </AccordionDetails>
            </Accordion>
          )}

          <Accordion
            className="mt-6 heading-bold box-shadow"
            expanded={expandedAccordion === "run-parameters"}
            onChange={handleChangeAccordion("run-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Run Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <ResearchQueryRunParameters
                runParameters={runParameters}
                setRunParameters={setRunParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                isShow={{
                  isShowReportName: true,
                }}
              />
            </AccordionDetails>
          </Accordion>

          <Accordion
            className="mt-6 heading-bold box-shadow"
            expanded={expandedAccordion === "result-storage-parameters"}
            onChange={handleChangeAccordion("result-storage-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Result Storage Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <ResultStorageParameters
                resultStorageParameters={resultStorageParameters}
                setResultStorageParameters={setResultStorageParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                isFromRule={false}
                isReRun={false}
              />
            </AccordionDetails>
          </Accordion>

          {globalVariables && Object.keys(globalVariables).length > 0 && (
            <Accordion
              className="mt-8 heading-bold box-shadow"
              expanded={expandedAccordion === "inline-variables"}
              onChange={handleChangeAccordion("inline-variables")}
            >
              <AccordionSummary
                aria-controls="panel1d-content"
                id="panel1d-header"
                expandIcon={<ExpandMoreIcon />}
              >
                Variables
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                <div className="text-box-card box-card-variables full-radius">
                  {!isEditVariables ? (
                    <div className="inline-variables-parent">
                      <InlineVariables
                        inlineVariables={globalVariables}
                        handleChangeVariable={handleChangeVariable}
                        isReadOnly={true}
                      />
                    </div>
                  ) : (
                    <div className="inline-variables-parent">
                      <InlineVariables
                        inlineVariables={globalVariables}
                        handleChangeVariable={handleChangeVariable}
                        isReadOnly={false}
                      />
                    </div>
                  )}
                  {!isEditVariables ? (
                    <Grid
                      item
                      xs={12}
                      sm={12}
                      md
                      sx={{
                        justifyContent: "flex-end",
                        display: "flex",
                        marginTop: "8px",
                      }}
                    >
                      <button
                        className="btn-nostyle icon-btn-edit"
                        onClick={() => {
                          setIsEditVariables(true);
                        }}
                      >
                        <IconBtnEditBase />
                      </button>
                    </Grid>
                  ) : (
                    <Grid
                      item
                      xs
                      sx={{
                        display: "flex",
                        alignItems: "flex-end",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          columnGap: "8px",
                          flexWrap: "wrap",
                          marginTop: "8px",
                        }}
                      >
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => {
                            setIsEditVariables(false);
                          }}
                          className="btn-orange btn-dark"
                        >
                          Cancel
                        </Button>
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => {
                            // setResourcesData(inlineVariables);
                            setGlobalVariables((prev: any) => ({
                              ...prev,
                              ...inlineVariables,
                            }));
                            setIsEditVariables(false);
                          }}
                          className="btn-orange"
                          disabled={!hasChanges}
                        >
                          <SaveOutlinedIcon /> &nbsp; Save
                        </Button>
                      </Box>
                    </Grid>
                  )}
                </div>
              </AccordionDetails>
            </Accordion>
          )}

          {!isEditResource && isResourceEdit === "" && (
            <FlexBetween
              gap="3rem"
              sx={{ marginTop: 2, display: "flex", justifyContent: "flex-end" }}
            >
              <Button
                type="submit"
                className="btn-orange"
                color="secondary"
                variant="contained"
                onClick={handleExecuteSubmit}
              >
                Execute Query
              </Button>
            </FlexBetween>
          )}
        </Box>
      </div>
    </>
  );
};

export default ResearchQueryExecution;
