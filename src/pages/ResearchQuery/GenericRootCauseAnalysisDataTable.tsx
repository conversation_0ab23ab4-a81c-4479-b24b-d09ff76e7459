import { useState, useEffect } from "react";

import { Box, Tooltip } from "@mui/material";
import RootCauseAnalysisDataGrid from "../../components/DataGrids/RootCauseAnalysisDataGrid";
interface IGenericRootCauseAnalysisDataTableProps {
  rootCauseData: any;
  isLoading: boolean;
  isFilterHide: boolean;
  resourceKeys: any;
  // resourceIds: any[];
}

const GenericRootCauseAnalysisDataTable = ({
  rootCauseData,
  isLoading,
  isFilterHide,
  resourceKeys,
}: // resourceIds,
IGenericRootCauseAnalysisDataTableProps) => {
  const [columnsData, setColumnsData] = useState<any>([]);
  const [rowsData, setRowsData] = useState<any>([]);

  useEffect(() => {
    const rows =
      rootCauseData?.result &&
      rootCauseData?.result.map((item: any, index: any) => ({
        id: index,
        ...item,
      }));
    let filteredRows = [];
    const columns = Object.keys(
      rootCauseData?.result &&
        rootCauseData?.result.length > 0 &&
        rootCauseData?.result[0]
    ).map((key) => ({
      field: key,
      headerName: (
        <Tooltip title={`${key}`} placement={"top"} arrow>
          <span className="text-capitalize">{`${key.slice(0, 15)} ${
            key.length >= 15 ? "..." : ""
          } `}</span>
        </Tooltip>
      ),
      width: 160,
    }));

    setColumnsData(columns);
    setRowsData(rows);
  }, [rootCauseData, resourceKeys]);

  return (
    <RootCauseAnalysisDataGrid
      dataRows={rowsData}
      dataColumns={columnsData}
      loading={isLoading}
      // dataListTitle="Rules Dashboard"
      className="dataTable filterMenuBox no-radius pt-0 bdr-top-0"
      disableColumnFilter={true}
      isFilterHide={isFilterHide}
    />
  );
};

export default GenericRootCauseAnalysisDataTable;
