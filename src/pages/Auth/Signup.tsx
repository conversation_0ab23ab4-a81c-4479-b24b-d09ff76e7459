import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import logo from "../../assets/logo-01.jpg";
import { useFormik } from "formik";
import { signUpSchema } from "../../schemas/Auth.schema"; // Correct path to the schema file
import {
  Container,
  CssBaseline,
  Grid,
  IconButton,
  Input,
  InputAdornment,
  InputLabel,
  TextField,
  Typography,
  Button,
  Box,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { signup } from "../../services/authService";
import { toast } from "react-toastify";
import { useAuth } from "../../contexts/AuthContext";
import useFetchUserRole from "../../hooks/useFetchUserRole";
import Loader from "../../components/Molecules/Loader/Loader";
import { useToast } from "../../services/utils";

const Signup: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  // const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [userRoleData] = useFetchUserRole({ setIsLoading });
  const { register } = useAuth();

  const formik = useFormik({
    initialValues: {
      firstname: "",
      lastname: "",
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      showPassword: false,
      showConfirmPassword: false,
    },
    validationSchema: signUpSchema, // Corrected the property name
    onSubmit: async (values) => {
      const { confirmPassword, showPassword, ...payload } = values;
      try {
        setIsLoading(true);
        const userRoleId = userRoleData.find(
          (user: any) => user?.role_name.toLowerCase() === "qa"
        )?.id;
        const response: any = await register(payload, userRoleId);
        if (response?.id) {
          navigate("/login");
          showToast("Account created successfully", "success");
        }
      } catch (err: any) {
        showToast(err?.response?.data?.message, "error");
      } finally {
        // setIsSubmitting(false);
        setIsLoading(false);
      }
    },
  });

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };
  const handleToggleConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <Container component="main" className="column-right">
      <Loader isLoading={isLoading} />
      <CssBaseline />
      <div>
        <Box className="logo">
          <img src={logo} alt="" />
        </Box>
        <Typography component="h1" variant="h5">
          Create your account
        </Typography>

        <form noValidate onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xl={6} lg={6} md={12} sm={12} xs={12}>
              <InputLabel className="label">
                Firstname<span className="required-asterisk">*</span>
              </InputLabel>
              <TextField
                autoComplete="firstname"
                name="firstname"
                variant="outlined"
                required
                fullWidth
                id="firstname"
                autoFocus
                className="form-control"
                value={formik.values.firstname}
                onChange={formik.handleChange}
                error={
                  formik.touched.firstname && Boolean(formik.errors.firstname)
                }
                helperText={formik.touched.firstname && formik.errors.firstname}
              />
            </Grid>
            <Grid item xl={6} lg={6} md={12} sm={12} xs={12}>
              <InputLabel className="label">
                Lastname<span className="required-asterisk">*</span>
              </InputLabel>
              <TextField
                autoComplete="lastname"
                name="lastname"
                variant="outlined"
                required
                fullWidth
                id="lastname"
                className="form-control"
                value={formik.values.lastname}
                onChange={formik.handleChange}
                error={
                  formik.touched.lastname && Boolean(formik.errors.lastname)
                }
                helperText={formik.touched.lastname && formik.errors.lastname}
              />
            </Grid>
            <Grid item xs={12}>
              <InputLabel className="label">
                Username<span className="required-asterisk">*</span>
              </InputLabel>
              <TextField
                autoComplete="username"
                name="username"
                variant="outlined"
                required
                fullWidth
                id="username"
                className="form-control"
                value={formik.values.username}
                onChange={formik.handleChange}
                error={
                  formik.touched.username && Boolean(formik.errors.username)
                }
                helperText={formik.touched.username && formik.errors.username}
              />
            </Grid>
            <Grid item xs={12}>
              <InputLabel className="label">
                Email<span className="required-asterisk">*</span>
              </InputLabel>
              <TextField
                autoComplete="email"
                name="email"
                variant="outlined"
                required
                fullWidth
                id="email"
                className="form-control"
                value={formik.values.email}
                onChange={formik.handleChange}
                error={formik.touched.email && Boolean(formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
              />
            </Grid>
            <Grid item xs={12}>
              <InputLabel className="label">
                Password<span className="required-asterisk">*</span>
              </InputLabel>
              <TextField
                required
                fullWidth
                name="password"
                type={showPassword ? "text" : "password"}
                id="password"
                autoComplete="new-password"
                className="form-control withAdornment password-box"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={handleTogglePassword} edge="end">
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                value={formik.values.password}
                onChange={formik.handleChange}
                error={
                  formik.touched.password && Boolean(formik.errors.password)
                }
                helperText={formik.touched.password && formik.errors.password}
              />
            </Grid>
            <Grid item xs={12}>
              <InputLabel className="label">
                Re-enter password<span className="required-asterisk">*</span>
              </InputLabel>
              <TextField
                required
                fullWidth
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                id="confirmPassword"
                autoComplete="new-password"
                className="form-control withAdornment  password-box"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleToggleConfirmPassword}
                        edge="end"
                      >
                        {showConfirmPassword ? (
                          <VisibilityOff />
                        ) : (
                          <Visibility />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                value={formik.values.confirmPassword}
                onChange={formik.handleChange}
                error={
                  formik.touched.confirmPassword &&
                  Boolean(formik.errors.confirmPassword)
                }
                helperText={
                  formik.touched.confirmPassword &&
                  formik.errors.confirmPassword
                }
              />
            </Grid>
          </Grid>
          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            className="btn-orange"
          >
            Sign up
          </Button>
          <Grid item xs={12} className="regular-text">
            Already have an account? <Link to="/login">Login</Link>
          </Grid>
        </form>
      </div>
    </Container>
  );
};

export default Signup;
