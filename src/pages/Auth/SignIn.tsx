import React, { useState } from "react";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import {
  Box,
  Button,
  Container,
  CssBaseline,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  TextField,
  Typography,
} from "@mui/material";

import { Link, useNavigate } from "react-router-dom";
import logo from "../../assets/logo-01.jpg";
import { useAuth } from "../../contexts/AuthContext";
import { signInSchema } from "../../schemas/Auth.schema";
import { useFormik } from "formik";
import Loader from "../../components/Molecules/Loader/Loader";
import { useToast } from "../../services/utils";

const SignIn = () => {
  const { showToast } = useToast();
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState<any>({
    username: "",
    password: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { signin } = useAuth();

  const formik = useFormik({
    initialValues: {
      username: "",
      password: "",
      showPassword: false,
    },
    validationSchema: signInSchema, // Corrected the property name
    onSubmit: async (values) => {
      if (isSubmitting) return; // Prevent multiple submissions

      setIsSubmitting(true);

      const { showPassword, ...payload } = values;
      try {
        setIsLoading(true);
        const response: any = await signin(payload);
        if (response?.user_info) {
          localStorage.setItem("userInfo", JSON.stringify(response?.user_info));
          localStorage.setItem("userId", response.user_info.id);
        }
        if (response?.access_token) {
          localStorage.setItem("token", response?.access_token);

          const lastVisitedPath = localStorage.getItem("lastVisitedPath");

          if (lastVisitedPath) {
            navigate(lastVisitedPath);
          } else {
            navigate("/domain");
          }
          showToast("Logged in successfully", "success");
        }
      } catch (err: any) {
        showToast(err?.response?.data?.message, "error");
      } finally {
        setIsSubmitting(false);
        setIsLoading(false);
      }
    },
  });

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  const handleSignIn = async (e: any) => {
    e.preventDefault();
    try {
      const response: any = await signin(formData);
      if (response?.access_token) {
        localStorage.setItem("token", response?.access_token);
        navigate("/domain");
        showToast("Logged in successfully", "success");
      }
    } catch (err: any) {
      showToast(err?.response?.data?.message, "error");
    }
  };

  const handleChange = (e: any) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };
  return (
    <Container component="main" className="column-right">
      <Loader isLoading={isLoading} />
      <CssBaseline />
      <div>
        <Box className="logo">
          <img src={logo} alt="" />
        </Box>
        <Typography component="h1" variant="h5">
          Login to your account
        </Typography>
        {/* <form onSubmit={handleSignIn} noValidate> */}
        <form onSubmit={formik.handleSubmit} noValidate>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <InputLabel className="label">
                Username<span className="required-asterisk">*</span>
              </InputLabel>
              <TextField
                autoComplete="username"
                name="username"
                variant="outlined"
                required
                fullWidth
                id="username"
                autoFocus
                className="form-control"
                onChange={formik.handleChange}
                value={formik.values.username}
                error={
                  formik.touched.username && Boolean(formik.errors.username)
                }
                helperText={formik.touched.username && formik.errors.username}
              />
            </Grid>
            <Grid item xs={12}>
              <InputLabel className="label">
                Password<span className="required-asterisk">*</span>
              </InputLabel>
              <TextField
                required
                fullWidth
                name="password"
                type={showPassword ? "text" : "password"}
                id="password"
                autoComplete="new-password"
                className="form-control withAdornment password-box"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={handleTogglePassword} edge="end">
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                value={formik.values.password}
                onChange={formik.handleChange}
                error={
                  formik.touched.password && Boolean(formik.errors.password)
                }
                helperText={formik.touched.password && formik.errors.password}
              />
            </Grid>
          </Grid>
          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            className="btn-orange"
          >
            Login now
          </Button>

          <Grid item xs={12} className="regular-text">
            Don't have an account?
            <Link to="/signup">Sign Up</Link>
          </Grid>
        </form>
      </div>
    </Container>
  );
};

export default SignIn;
