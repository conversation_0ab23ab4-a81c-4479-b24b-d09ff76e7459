import { useState } from "react";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import {
  Box,
  Button,
  Container,
  CssBaseline,
  Grid,
  IconButton,
  Input,
  InputAdornment,
  InputLabel,
  TextField,
  Typography,
} from "@mui/material";
import { Link } from "react-router-dom";
import logo from "../../assets/logo-01.jpg";

const Forgot = () => {
  const [showPassword, setShowPassword] = useState(false);

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };
  return (
    <Container component="main" className="column-right">
      <CssBaseline />
      <div className="w-100">
        <Box className="logo">
          <img src={logo} alt="" />
        </Box>
        <Box>
          <Typography component="h1" variant="h5">
            Reset your password
          </Typography>
          <form noValidate>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <InputLabel className="label">Email</InputLabel>
                <TextField
                  autoComplete="fname"
                  name="firstName"
                  variant="outlined"
                  required
                  fullWidth
                  id="firstName"
                  autoFocus
                  className="form-control"
                />
              </Grid>
            </Grid>
            <Button
              type="submit"
              fullWidth
              variant="contained"
              color="primary"
              className="btn-orange"
            >
              Reset now
            </Button>

            <Grid item xs={12} className="regular-text">
              Don't have an account?
              <Link to="/signup">Sign up</Link>
            </Grid>
          </form>
        </Box>

        <Box style={{ display: "none" }}>
          <Typography component="h1" variant="h5">
            Check your email
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} className="regular-text">
              We have sent an password recover instruction to your email
            </Grid>
            <Grid item xs={12} className="regular-text">
              <Link to="/signup">Skip, I'll confirm later</Link>
            </Grid>
          </Grid>
        </Box>
        <Box  style={{ display: "none" }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <InputLabel className="label">Enter new password</InputLabel>
              <Input
                required
                fullWidth
                name="password"
                type={showPassword ? "text" : "password"}
                id="password"
                autoComplete="current-password"
                className="form-control withAdornment"
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton onClick={handleTogglePassword} edge="end">
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                }
              />
            </Grid>
            <Grid item xs={12}>
              <InputLabel className="label">Re-enter new password</InputLabel>
              <Input
                required
                fullWidth
                name="password"
                type={showPassword ? "text" : "password"}
                id="password"
                autoComplete="current-password"
                className="form-control withAdornment"
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton onClick={handleTogglePassword} edge="end">
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                }
              />
            </Grid>
          </Grid>
          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            className="btn-orange"
          >
            Reset password
          </Button>
        </Box>
      </div>
    </Container>
  );
};

export default Forgot;
