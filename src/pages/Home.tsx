import * as React from "react";
import {
  DataGridPremium,
  GridColDef,
  GridColumnGroupingModel,
} from "@mui/x-data-grid-premium";

const columns: GridColDef[] = [
  { field: "id", headerName: "ID", width: 90 },
  { field: "firstName", headerName: "First name", width: 150 },
  { field: "lastName", headerName: "Last name", width: 150 },
  { field: "age", headerName: "Age", type: "number", width: 110 },
  { field: "address", headerName: "Address", width: 150 },
  { field: "city", headerName: "City", width: 110 },
  { field: "state", headerName: "State", width: 110 },
  { field: "country", headerName: "Country", width: 110 },
];

const columnGroupingModel: GridColumnGroupingModel = [
  {
    groupId: "personalInfo",
    headerName: "Personal Information",
    children: [{ field: "firstName" }, { field: "lastName" }, { field: "age" }],
  },
  {
    groupId: "location123",
    headerName: "Location123",
    children: [
      { field: "address" },
      { field: "city" },
      { field: "state" },
      { field: "country" },
    ],
  },
];

const rows = [
  {
    id: 1,
    firstName: "John",
    lastName: "Doe",
    age: 25,
    address: "123 Main St",
    city: "New York",
    state: "NY",
    country: "USA",
  },
  {
    id: 2,
    firstName: "Jane",
    lastName: "Smith",
    age: 30,
    address: "456 Market St",
    city: "San Francisco",
    state: "CA",
    country: "USA",
  },
  // More rows...
];

export default function Home() {
  return (
    <div style={{ height: 400, width: "100%" }}>
      <DataGridPremium
        rows={rows}
        columns={columns}
        columnGroupingModel={columnGroupingModel}
        experimentalFeatures={{ columnGrouping: true }} // Add this line
      />
    </div>
  );
}
