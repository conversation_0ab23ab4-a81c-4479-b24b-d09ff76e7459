import React, { useEffect, useState } from "react";
import useFetchExecutionHistory from "../../hooks/useFetchExecutionHistory";
import { GridColDef, GridExpandMoreIcon } from "@mui/x-data-grid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { IconButton, Tooltip } from "@mui/material";
import {
  downloadRulesHistoryFile,
  getFormatedDate,
  getFormattedDateTimeWithAMPM,
  getFormattedTime,
  useToast,
} from "../../services/utils";
import EHistoryDataTable from "../../components/DataGrids/EHistoryDataGrid";
import { useNavigate } from "react-router-dom";
import { Box } from "@mui/material";
import dayjs from "dayjs";
import { enableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";
import Loader from "../../components/Molecules/Loader/Loader";

import {
  IconDeleteBlueSvg,
  IconEyeBase,
  IconFilterSvg,
  IconReRunBlue,
  IconAbortBlue,
} from "../../common/utils/icons";
// import useFetchIncidentByExecutionId from "../../hooks/useFetchIncidentByExecutionId";
import FilterSearch from "../../components/Molecules/FilterSearch/FilterSearch";
import ConsolidatedReportsFilter from "../../components/Molecules/FilterSearch/ConsolidatedReportsFilter";
import {
  abortExecution,
  deleteComparisonExecutionbyID,
} from "../../services/rulesService";
import ConfirmationDialog from "../../components/Dialogs/ConfirmationDialog";
import DashboardJobstatusIcon from "../../common/utils/DashboardJobstatusIcon";
import useSyncNotifications from "../../hooks/useSyncNotifications";
// import useWebSocket from "../../hooks/useWebSocket"; // No longer needed
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

interface RuleHistoryData {
  domainId: number | null;
  ruleId: number | null;
  fromDate: string | undefined;
}
interface RuleSearchData {
  search_by: string | null;
  search_query: string | null;
  search_date?: string;
}
const ExecutionHistory = () => {
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const queryParams = new URLSearchParams(window.location.search);

  const navigate = useNavigate();
  const { showToast } = useToast();
  const [isloading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>({
    items: [],
    total: 0,
    page: 0,
    size: 0,
    pages: 0,
  });
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [formData, setFormData] = useState({
    run_name: "",
    run_date: dayjs().subtract(1, "day"),
    report_type: "",
  });
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const [searchFilterData, setSearchFilterData] = useState<any[]>([]);
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [isConsolidatedFilterVisible, setIsConsolidatedFilterVisible] =
    useState(false);

  // const [incidentData, setIncidentData] = useState<any>([]);
  // const [incidentResultIds, setIncidentResultIds] = useState();

  const [
    openComparisonExecutionConfirmation,
    setOpenComparisonExecutionConfirmation,
  ] = useState(false);

  const [openAbortExecutionConfirmation, setOpenAbortExecutionConfirmation] =
    useState(false);

  const [deleteExecutionId, setDeleteExecutionId] = useState<number | null>(
    null
  );

  const [abortExecutionId, setAbortExecutionId] = useState<number | null>(null);

  const [ruleHistoryParams, setRuleHistoryParams] = useState<RuleHistoryData>({
    domainId: Number(queryParams.get("domainId")),
    ruleId: Number(queryParams.get("ruleId")),
    fromDate: queryParams.get("fromDate")?.toString(),
  });
  const [executionList] = useFetchExecutionHistory({
    setIsLoading: setIsBackdropLoading,
    page,
    pSize,
    domainId: ruleHistoryParams.domainId,
    ruleId: ruleHistoryParams.ruleId,
    fromDate: ruleHistoryParams.fromDate,
    searchFilterData,
  });

  // Get handleStorageChange from useSyncNotifications
  // This will automatically sync with notifications from the context
  useSyncNotifications(setFileData, fileData);

  const handleRuleExecutionList = (data: any) => {
    if (data && data.items) {
      const newData = data.items.map((row: any) => ({
        ...row,
        execution_date: getFormatedDate(row.execution_time),
        execution_name:
          row?.complete_execution_params?.rule_execution_request?.request_body
            ?.execution_name,
      }));
      return { ...data, items: newData };
    }
    return data;
  };
  // const [fetchIncidentData] = useFetchIncidentByExecutionId({
  //   setIsLoading,
  //   data: incidentResultIds,
  // });
  // useEffect(() => {
  //   setIncidentData(fetchIncidentData);
  // }, [fetchIncidentData, page]);

  useEffect(() => {
    if (
      executionList &&
      executionList.items &&
      executionList.items.length > 0
    ) {
      // const resultIdArr = executionList?.items?.map((row: any) => row.id);
      // setIncidentResultIds(resultIdArr);
      setFileData(executionList);
    }
  }, [executionList]);

  // useEffect(() => {
  //   const updatedFileData = handleRuleExecutionList(executionList);
  //   // Update fileData state
  //   if (updatedFileData?.items?.length > 0) {
  //     const newData = updatedFileData.items.map((file: any) => {
  //       const fileItem: any =
  //         incidentData &&
  //         incidentData.length > 0 &&
  //         incidentData.find((incidentItem: any) => {
  //           return Number(incidentItem?.ExecutionId) === Number(file.id);
  //         });
  //       if (fileItem) {
  //         return {
  //           ...file,
  //           IncidentStatus: fileItem?.IncidentStatus,
  //         };
  //       }
  //       return file;
  //     });
  //     setFileData({ ...updatedFileData, items: newData });
  //   }
  // }, [incidentData, page]);

  useEffect(() => {
    const handleStorageChange = () => {
      const storedNotifications = localStorage.getItem("notifications");

      if (storedNotifications && fileData?.items?.length > 0) {
        try {
          const notifications = JSON.parse(storedNotifications);

          setFileData((prevFileData: any) => {
            const updatedItems = prevFileData.items
              .map((item: any) => {
                const matchingNotification = notifications.find(
                  (notification: any) =>
                    (notification?.execution_type === "rule" ||
                      notification?.execution_type === "rule_rerun") &&
                    notification?.execution_id === item?.id
                );

                if (matchingNotification?.execution_status === "failed") {
                  return null; // Remove this item
                }

                if (matchingNotification?.execution_status) {
                  return {
                    ...item,
                    job_status: matchingNotification.execution_status,
                  };
                }

                return item;
              })
              .filter(Boolean); // Remove nulls (failed items)

            const hasChanges =
              updatedItems.length !== prevFileData.items.length ||
              updatedItems.some(
                (newItem: any, index: number) =>
                  newItem.job_status !== prevFileData.items[index]?.job_status
              );

            return hasChanges
              ? { ...prevFileData, items: updatedItems }
              : prevFileData;
          });
        } catch (error) {
          console.error(
            "Error parsing notifications from localStorage:",
            error
          );
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    const checkInterval = setInterval(handleStorageChange, 1000);
    handleStorageChange();

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      clearInterval(checkInterval);
    };
  }, [executionList]);

  /**
   * Set table coloums for execution history
   */
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => (
        <IconButton size="small" tabIndex={-1} className="groupby-expand-icon">
          <GridExpandMoreIcon
            sx={{
              transform: `rotateZ(${
                expandedRow.includes(params.row.id) ? 180 : 0
              }deg)`,
              transition: (theme) =>
                theme.transitions.create("transform", {
                  duration: theme.transitions.duration.shortest,
                }),
            }}
            fontSize="inherit"
          />
        </IconButton>
      ),
    },
    {
      field: "id",
      headerName: "Execution Id",
      flex: 1,
      minWidth: 100,
      groupable: false,
      renderCell: (params: any) => {
        return (
          <Tooltip
            title={`Run Id: ${params?.row?.run_id}`}
            placement="top"
            arrow
          >
            <span style={{ cursor: "pointer" }}>{params.value}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "rule_id",
      headerName: "Rule Id",
      flex: 1,
      minWidth: 100,
      groupable: false,
      renderCell: (params: any) => {
        return <span style={{ cursor: "pointer" }}>{params.value}</span>;
      },
    },
    {
      field: "execution_name",
      headerName: "Execution Name",
      flex: 1,
      minWidth: 160,
      groupable: false,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "name",
      headerName: "Rule Name",
      flex: 1,
      minWidth: 210,
      groupable: false,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "run_name",
      headerName: "Run Name",
      flex: 1,
      minWidth: 100,
      groupable: false,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "domain_name",
      headerName: "Domain Name",
      flex: 1,
      minWidth: 100,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "execution_time",
      headerName: "Execution Date & Time",
      minWidth: 180,
      groupable: false,
      renderCell: (params) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const executionTime = params?.value?.execution_time || params.value;
        const formattedTime = getFormattedDateTimeWithAMPM(executionTime);
        return (
          <div>
            <div>{formattedTime}</div>
          </div>
        );
      },
    },
    {
      field: "job_status",
      headerName: "Execution Status",
      flex: 1,
      minWidth: 130,
      headerAlign: "center",
      align: "center",
      groupable: false,
      renderCell: (params: any) => (
        <DashboardJobstatusIcon status={params?.row?.job_status} />
      ),
    },
    {
      field: "action",
      headerName: "Action",
      align: "left",
      headerAlign: "left",
      width: 140,
      groupable: false,
      renderCell: (params) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const handleViewClick = (incidentTab?: any) => {
          const basePath = `/rules-execution-history/${params?.row.rule_id}/${params?.row.id}/dashboard`;

          const navigatePath = incidentTab
            ? `${basePath}#incidentTab`
            : basePath;

          navigate(navigatePath, {
            state: {
              rowData: params?.row,
            },
          });
        };
        const handleAbortExecution = () => {
          setAbortExecutionId(params?.row?.id);
          setOpenAbortExecutionConfirmation(true);
        };
        return (
          <>
            {params?.row?.job_status === "completed" ? (
              <Tooltip title="View Dashboard" placement="top" arrow>
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleViewClick()}
                >
                  <IconEyeBase />
                </IconButton>
              </Tooltip>
            ) : (
              <Tooltip title="Abort Execution" placement="top" arrow>
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleAbortExecution()}
                >
                  <IconAbortBlue />
                </IconButton>
              </Tooltip>
            )}
            {
              // params?.row?.IncidentStatus && (
              //   <Tooltip title="View Incidents" placement="top" arrow>
              //     <IconButton
              //       className="datagrid-action-btn"
              //       color="inherit"
              //       onClick={() => handleViewClick("incidentTab")}
              //     >
              //       {params?.row?.IncidentStatus === "Triggered" ? (
              //         <IconIncidentTriggeredSvg />
              //       ) : params?.row?.IncidentStatus === "Acknowledged" ? (
              //         <IconIncidentAcknowledgeSvg />
              //       ) : params?.row?.IncidentStatus === "Resolve" ? (
              //         <IconIncidentResolveSvg />
              //       ) : null}
              //     </IconButton>
              //   </Tooltip>
              // )
            }
            <Tooltip title="Re-Run" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  navigate(
                    `/rules/${params?.row.domain_id}/re-run-rule-execution/${params?.row.rule_id}?re-run=true&execution-id=${params?.row.id}&run-id=${params?.row?.run_id}&run-name=${params?.row?.run_name}`
                  )
                }
              >
                <IconReRunBlue />
              </IconButton>
            </Tooltip>

            <Tooltip title="Delete" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  setDeleteExecutionId(params.row.id);
                  setOpenComparisonExecutionConfirmation(true);
                }}
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  const handleSubmitReport = async (event: any) => {
    // event.preventDefault();
    navigate(
      `/rules-execution-history/consolidate-results?runName=${formData.run_name}&reportType=${formData.report_type}&runDate=${formData?.run_date}`
    );
  };

  const handleDelete = async () => {
    if (deleteExecutionId) {
      try {
        setIsDeleteLoading(true);
        await deleteComparisonExecutionbyID(deleteExecutionId);
        const deletedRow = fileData.items.find(
          (item: any) => item.id === deleteExecutionId
        );
        setFileData((prev: any) => ({
          ...prev,
          items: prev?.items?.filter(
            (prevItem: any) => prevItem.id !== deleteExecutionId
          ),
        }));
        showToast(`${deleteExecutionId} deleted successfully`, "success");
      } catch (error) {
        console.error("Error deleting execution:", error);
      } finally {
        setIsDeleteLoading(false);
      }
    }
  };

  const handleComparisonExecutionDelete = () => {
    setOpenComparisonExecutionConfirmation(false);
    handleDelete();
  };

  const handleAbortExecution = async () => {
    if (abortExecutionId) {
      try {
        setIsDeleteLoading(true);
        await abortExecution(abortExecutionId);
        setFileData((prev: any) => ({
          ...prev,
          items: prev?.items?.filter(
            (prevItem: any) => prevItem.id !== abortExecutionId
          ),
        }));
        showToast(`${abortExecutionId} aborted successfully`, "success");
      } catch (error) {
        console.error("Error aborting execution:", error);
      } finally {
        setIsDeleteLoading(false);
      }
    }
  };

  const handleAbortExecutionDelete = () => {
    setOpenAbortExecutionConfirmation(false);
    handleAbortExecution();
  };
  return (
    <>
      {(isBackdropLoading || isDeleteLoading) && <Loader isLoading={true} />}
      <FilterSearch
        setSearchFilterData={setSearchFilterData}
        isFilterVisible={isFilterVisible}
        setIsFilterVisible={setIsFilterVisible}
        FilterFor="rule"
        searchFilterData={searchFilterData}
        requiredFields={["date", "domain_id", "run", "rule"]}
        fieldRequirements={{ dateTitle: "Date", tooltipRequired: true }}
      />
      <ConsolidatedReportsFilter
        setFormData={setFormData}
        isFilterVisible={isConsolidatedFilterVisible}
        setIsFilterVisible={setIsConsolidatedFilterVisible}
        formData={formData}
        handleSubmitReport={handleSubmitReport}
      />
      <div className="execution-history-btn-group">
        <Box className="transaction-btn-group">
          <button
            className="filters-btn btn-orange btn-border"
            onClick={() => setIsFilterVisible(true)}
          >
            <IconFilterSvg />
            Filter
          </button>
          <button
            className="btn-orange btn-border cursor-pointer"
            onClick={() => setIsConsolidatedFilterVisible(true)}
            color="secondary"
          >
            Consolidated Reports
          </button>
        </Box>
      </div>
      <EHistoryDataTable
        dataColumns={columns}
        dataRows={fileData?.items}
        loading={isloading}
        setLoading={setIsLoading}
        dataListTitle={"Comparison History"}
        isExportButtonRequired={true}
        className="dataTable no-radius hide-progress-icon"
        rowCount={fileData?.total}
        pageSizeOptions={[25]}
        paginationModel={{
          page: page - 1,
          pageSize: pSize,
        }}
        onPaginationModelChange={(params: any) => {
          if (params.pageSize !== pSize || params.page !== page - 1) {
            setPage(params.page + 1);
            setPSize(params.pageSize);
          }
        }}
        historyType={"Execution"}
        downloadFromAPIFile={() =>
          downloadRulesHistoryFile({
            setLoading: setIsBackdropLoading,
            searchFilterData,
          })
        }
        disableColumnFilter={false}
        rowGroupByColumnProps={enableRowGroupByColumnProps}
        singlePageMaxHeightDiff={285}
        isShowInComparisonHistory={true}
      />
      <ConfirmationDialog
        title={"Confirm removing Execution"}
        dialogContent={
          "Are you sure you want to delete this comparision execution data?"
        }
        handleCancel={() => setOpenComparisonExecutionConfirmation(false)}
        openConfirmation={openComparisonExecutionConfirmation}
        handleConfirm={handleComparisonExecutionDelete}
        isShowClassMb0={false}
      />
      <ConfirmationDialog
        title={"Confirm aborting Execution"}
        dialogContent={
          "Are you sure you want to abort this comparision execution data?"
        }
        handleCancel={() => setOpenAbortExecutionConfirmation(false)}
        openConfirmation={openAbortExecutionConfirmation}
        handleConfirm={handleAbortExecutionDelete}
        isShowClassMb0={false}
      />
    </>
  );
};

export default ExecutionHistory;
