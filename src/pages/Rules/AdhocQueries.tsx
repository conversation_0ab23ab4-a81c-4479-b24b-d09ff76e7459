import React, { useEffect } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import AddMultipleGridList from "../../components/AddMultipleGridList";
import { useRuleContext } from "../../contexts/RuleContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { Box } from "@mui/material";
const AdhocQueries = ({
  adhocQueryData,
  setAdhocQueryData,
  isViewOnly,
  errors,
  setErrors,
  sampleQuery,
}: any) => {
  const { selectedResourceIds } = useRuleContext();
  const { setAvailColumns, setAvailColumnsWithResourceDetail } =
    useRuleResourceContext();

  useEffect(() => {
    if (selectedResourceIds && selectedResourceIds.length > 0) {
      const activeResourceColumns = selectedResourceIds.flatMap(
        (resource: any) =>
          resource?.resource_column_properties?.resource_columns
            .filter((column: any) => column?.is_active)
            .map((column: any) => column.column_name)
      );

      setAvailColumns(activeResourceColumns);
      const data = selectedResourceIds.map((resource: any) => ({
        id: resource.rId,
        rcId: resource.id,
        resourceName: resource.resource_name,
        resourcePrefix: resource.resource_prefix,
        resourceCode: resource.rCode,
        rcolums: resource.resource_column_properties.resource_columns
          .filter((column: any) => column.is_active)
          .map((column: any) => column.column_name),
      }));
      const rDetailColumns = {
        queryType: "ADHOCQUERY",
        data,
      };
      setAvailColumnsWithResourceDetail(rDetailColumns);
    }
  }, [selectedResourceIds]);

  const [expanded, setExpanded] = React.useState<string | false>();
  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
      setExpanded(newExpanded ? panel : false);
    };
  return (
    <>
      <div
        className={`accordion-panel text-box-card compact-text-box-card no-radius bottom-radius bg-white ${
          isViewOnly ? "" : "table-filter-bg"
        }`}
        style={{ marginBottom: "0" }}
      >
        {isViewOnly ? (
          <Box>
            {adhocQueryData?.length > 0 ? (
              <Box className="MuiCollapse-vertical">
                <table className="custom-table">
                  <thead>
                    <tr>
                      <th style={{ width: "30%" }}>Name:</th>
                      <th>SQL Query(s):</th>
                    </tr>
                  </thead>
                  {adhocQueryData?.map((item: any) => {
                    return (
                      <tr>
                        <td>{item?.name}</td>
                        <td>{item?.sql_query}</td>
                      </tr>
                    );
                  })}
                </table>
              </Box>
            ) : (
              <p>No data found</p>
            )}
          </Box>
        ) : (
          <>
            <AddMultipleGridList
              gridValue={adhocQueryData}
              setGridValue={setAdhocQueryData}
              isViewOnly={isViewOnly}
              buttonName="Adhoc Query"
              errors={errors}
              setErrors={setErrors}
              sampleQuery={sampleQuery}
            />
          </>
        )}
      </div>
    </>
  );
};

export default AdhocQueries;
