import React, { useEffect, useState, useCallback } from "react";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import Loader from "../../components/Molecules/Loader/Loader";
import { GridCellParams } from "@mui/x-data-grid";
import SearchIcon from "@mui/icons-material/Search";
import { IconFilterSvg } from "../../common/utils/icons";
import { debounce } from "lodash";
import { GridFilterModel } from "@mui/x-data-grid-premium";

import { Tooltip, Button, IconButton, Box, TextField } from "@mui/material";
import { useNavigate } from "react-router-dom";

const sampleData: any = [
  {
    Column_1: "Comparison A",
    Column_2: "Run 1",
    Date_Time: "12/26/24 5:00",
    re_run_flag: "",
    Issue_status: "3 issues all marked as closed",
  },
  {
    Column_1: "Comparison A",
    Column_2: "Run 2",
    Date_Time: "12/26/24 9:00",
    re_run_flag: "Y",
    Issue_status: "3 issues all marked as closed",
  },
  {
    Column_1: "Comparison A",
    Column_2: "Run 3",
    Date_Time: "12/26/24 23:00",
    re_run_flag: "Y",
    Issue_status: "3 issues all marked as closed",
  },
  {
    Column_1: "Comparison A",
    Column_2: "Run 4",
    Date_Time: "12/27/24 9:00",
    re_run_flag: "Y",
    Issue_status: "3 issues all marked as closed",
  },
  {
    Column_1: "Comparison A",
    Column_2: "Run 5",
    Date_Time: "12/27/24 23:00",
    re_run_flag: "Y",
    Issue_status: "3 issues all marked as closed",
  },
  {
    Column_1: "Comparison A",
    Column_2: "Run 6",
    Date_Time: "",
    re_run_flag: "",
    Issue_status: "3 issues all marked as open",
  },
  {
    Column_1: "Comparison A",
    Column_2: "Run 7",
    Date_Time: "",
    re_run_flag: "Y",
    Issue_status: "3 issues all marked as closed",
  },
  {
    Column_1: "Comparison A",
    Column_2: "Run 8",
    Date_Time: "",
    re_run_flag: "Y",
    Issue_status: "3 issues all marked as closed",
  },
];

const IncidentReportingReRunReport = () => {
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFilterHide, setIsFilterHide] = useState(true);
  const [filterModel, setFilterModel] = useState<GridFilterModel>({
    items: [],
  });

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 1,
      flex: 0,
      renderCell: (params: any) => null,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
    },
    {
      field: "Column_1",
      headerName: "Column_1",
      width: 100,
      flex: 1,
    },
    {
      field: "Column_2",
      headerName: "Column_2",
      width: 200,
      flex: 1,
    },
    {
      field: "Date_Time",
      headerName: "Date_Time",
      width: 150,
      flex: 1,
      renderCell: (params: any) => {
        return params.row.Date_Time ? params.row.Date_Time : "N/A";
      },
    },
    {
      field: "re_run_flag",
      headerName: "re_run_flag",
      width: 150,
      flex: 1,
      renderCell: (params: any) => {
        return params.row.re_run_flag ? "Y" : "N";
      },
    },

    {
      field: "Issue_status",
      headerName: "Issue_status",
      width: 150,
      flex: 1,
    },
  ];

  // Ensure unique IDs for each row
  const generateUniqueRows = (runData: any, runName: string) => {
    return runData.map((item: any, index: number) => ({
      ...item,
      run: runName,
      id: `${item.key}-${runName}-${index}`, // Create unique ID using key + run name + index
    }));
  };
  const handleFilterBox = () => {
    setIsFilterHide(!isFilterHide);
  };

  const columnWithFilters = columns.map((column) => ({
    ...column,
    sortable: false,
    renderHeader: (params: any) => (
      <CustomHeaderFilter
        column={params.colDef}
        filterValue={
          filterModel.items.find((item) => item.field === column.field)
            ?.value || ""
        }
        onFilterChange={(value: any) => {
          return handleFilterChange(column.field, value);
        }}
        isFilterHide={isFilterHide}
      />
    ),
  }));
  const handleFilterChange = (field: string, value: string) => {
    const newFilterItems = filterModel.items.filter(
      (item) => item.field !== field
    );

    if (value) {
      newFilterItems.push({
        field: field,
        operator: "contains",
        value,
      });
    }

    setFilterModel({ items: newFilterItems });
  };

  return (
    <>
      {isLoading && <Loader isLoading={isLoading} />}

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          position: "relative",
          marginTop: "-41px",
        }}
      >
        <Button
          className="btn-blue btn-orange btn-sm"
          onClick={() => {
            navigate("/incident-reporting-rerun");
          }}
        >
          Detail View
        </Button>
      </Box>

      {/* Render run1 table */}
      <Box className="incident-report-container">
        <IconButton
          className="incident-filter-icon"
          onClick={() => handleFilterBox()}
        >
          <IconFilterSvg />
        </IconButton>
        <DataTable
          dataColumns={columnWithFilters}
          dataRows={generateUniqueRows(sampleData, "run1")}
          loading={isLoading}
          dataListTitle={"Run 1"}
          className="dataTable no-radius hide-progress-icon"
          pageSizeOptions={[25]}
          disableColumnMenu={true}
          filterModel={filterModel}
        />
      </Box>
    </>
  );
};

export default IncidentReportingReRunReport;

const CustomHeaderFilter = ({
  column,
  filterValue,
  onFilterChange,
  isFilterHide,
}: any) => {
  const [inputValue, setInputValue] = useState(filterValue || "");

  const debouncedFilterChange = useCallback(
    debounce((value: any) => onFilterChange(value), 300),
    []
  );

  useEffect(() => {
    debouncedFilterChange(inputValue);
    return () => {
      debouncedFilterChange.cancel();
    };
  }, [inputValue, debouncedFilterChange]);

  return (
    <Box display="flex" flexDirection="column">
      <Box className="header-name">{column?.headerName}</Box>
      <Box className={`textbox-group-wrapper ${isFilterHide ? "hide" : ""}`}>
        <Box className={`textbox-group`}>
          <TextField
            variant="outlined"
            size="small"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={`Filter ${column?.headerName}`}
          />
          <IconButton>
            <SearchIcon />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
};
