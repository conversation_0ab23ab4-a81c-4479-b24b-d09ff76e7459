import {
  Box,
  <PERSON>rid,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import FlexBetween from "../../../components/FlexBetween";
import useFetchFileProcessing from "../../../hooks/useFetchFileProcessing";
import useFetchRuleById from "../../../hooks/useFetchRuleById";
import {
  backgroundExecuteRuleReRun,
  executeRuleReRun,
} from "../../../services/rulesService";
import ResultStorageParameters from "../../../components/Rules/ResultStorageParameters";
import RunParameters from "../../../components/Rules/RunParameters";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  IconBtnEditBase,
  IconPencilGray,
  IconPlusGray,
  IconViewSvgWhite,
} from "../../../common/utils/icons";
import { apiType, sqlDatabaseType } from "../../../services/constants";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import RuleInlineVariables from "../../../components/Rules/RuleInlineVariables";
import { useToast } from "../../../services/utils";

import Loader from "../../../components/Molecules/Loader/Loader";
import useFetchConnectionKeysAll from "../../../hooks/useFetchConnectionKeysAll";
import CustomAccordian from "../../../components/Molecules/Accordian/CustomAccordion";
import FilterRules from "../../../components/Resource/FilterRules";
import AddMultipleGridList from "../../../components/AddMultipleGridList";
import { useBreadCrumbContext } from "../../../contexts/BreadCrumbContext";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import CustomMultipleResourceRuleFilters from "../../../components/Rules/CustomMultipleResourceRuleFilters";
import SecondaryMergeResourceDialog from "../../../components/Dialogs/SecondaryMergeResourceDialog";
import useFetchResources from "../../../hooks/useFetchResources";
import useFetchAllResourcesWithoutDomain from "../../../hooks/useFetchAllResourcesWithoutDomain";
import useFetchLinkedServices from "../../../hooks/useFetchLinkedServices";
import useFetchComparisonRerunResultByRuleId from "../../../hooks/useFetchComparisionRerunResultByRuleId";
import {
  defaultResultStorageParameters,
  defaultRunParameters,
} from "../../../services/models/execution.model";
import { IResultStorageParameters } from "../../../types/Execution";
import ReRunRuleExecutionTab from "../../../components/Rules/ReRun/ReRunRuleExecutionTab";
import { executionNameSchema } from "../../../schemas";
import ExecutionName from "../../../components/Molecules/Rule/ExecutionName";
interface FilterFormData {
  filter_rules: any[];
}

const ReRunRuleExecution = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { ruleId: currentRuleId } = useParams();
  const { showToast } = useToast();
  const {
    isResourceEdit,
    viewInlineVariables,
    setViewInlineVariables,
    setAllResourcesData,
    setLinkedServicesData,
    fileProcessingData,
    setFileProcessingData,
  } = useRuleResourceContext();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [resourceIds, setResourceIds] = useState<any[]>([]);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [resourcesData, setResourcesData] = useState<any[]>([]);
  const [isEditResource, setIsEditResource] = useState(false);
  const [resultStorageParameters, setResultStorageParameters] =
    useState<IResultStorageParameters>(defaultResultStorageParameters);
  const [ruleExecutionPayload, setRuleExecutionPayload] = useState<any>({});
  const [runParameters, setRunParameters] = useState<any>({
    no_of_errors_in_response: 0,
    no_of_errors_in_output_files: 0,
    skip_duplicate_records: true,
    summary_mode: false,
    generate_files: true,
    skip_validation: false,
    validation_severity_level: "Low",
    run_instance: {
      run_id: 1,
      run_name: "Legacy",
    },
    save_input_data_file: false,
    execute_comparison_research_query: true,
    use_secondary_merge_resources: true,
    store_errors_snapshots_and_create_issues: true,
    keep_downloaded_files: false,
    pull_new_files_from_server: false,
    is_long_running_job: false,
    isCustomiseSeverity: false,
  });
  const [severityColumnNames, setSeverityColumnNames] = useState<any>({
    low: null,
    medium: null,
    high: null,
  });
  const [secondaryMergeResourceDialog, setSecondaryMergeResourceDialog] =
    useState(false);
  const [resourceDataWithSecMerge, setResourceDataWithSecMerge] = useState<any>(
    {}
  );
  const [currentDomainId, setCurrentDomainId] = useState(null);
  const [executionName, setExecutionName] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: any }>({
    execution_name: "",
  });
  const [ruleData] = useFetchRuleById({ setIsLoading, currentRuleId });

  const [fileProcessing] = useFetchFileProcessing({ setIsLoading });
  const [fetchedConnectionKeys] = useFetchConnectionKeysAll({ setIsLoading });

  const [fetchedResourcesData] = useFetchResources({
    currentDomainId,
    setIsLoading,
  });
  const [allResources] = useFetchAllResourcesWithoutDomain({
    setIsLoading,
    aggregation_type: "aggregated",
  });

  const [isReRunData, setIsReRunData] = useState<any>({
    executionID: null,
    isReRun: false,
    runId: queryParams.get("run-id"),
    runName: queryParams.get("run-name"),
  });
  const [linkedServices] = useFetchLinkedServices({ setIsLoading });

  const [rerunData] = useFetchComparisonRerunResultByRuleId({
    ruleId: currentRuleId ? Number(currentRuleId) : null,
    setIsLoading,
    runId: isReRunData ? Number(isReRunData?.runId) : null,
    runName: isReRunData ? isReRunData.runName : null,
  });

  const [expandedAccordion, setExpandedAccordion] = useState<any>("");
  const [validationMessage, setValidationMessage] = useState<any[]>([]);
  const [checkValidation, setCheckValidation] = useState(false);

  const [inlineVariables, setInlineVariables] = useState<any>({
    rule_variables: {
      filterRule: null,
      adhoc_queries: null,
    },
    resource_variables: [],
    comparison_research_query_variables: [],
  });
  const [isEditVariables, setIsEditVariables] = useState(false);
  const [isEditFilters, setIsEditFilters] = useState(false);
  const [filterFormData, setFilterFormData] = useState<
    FilterFormData | undefined
  >(undefined);
  const [initialInlineVariables, setInitialInlineVariables] = useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [secondaryResourceData, setSecondaryResourceData] = useState<any[]>([]);

  const [isEditSecondaryResource, setIsEditSecondaryResource] = useState(false);
  const [mergeColumnsLength, setMergeColumnsLength] = useState(0);
  const [executionMode, setExecutionMode] = useState(true);

  const isUpdateRepeatedResourcesCalled = useRef(false);

  const navigate = useNavigate();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const reRun = params.get("re-run");
    const executionId = params.get("execution-id");
    if (reRun && executionId) {
      setIsReRunData({
        executionID: Number(executionId),
        isReRun: true,
      });
    } else {
      setIsReRunData({
        executionID: null,
        isReRun: false,
      });
    }
  }, [location]);

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources, setAllResourcesData]);

  useEffect(() => {
    if (linkedServices) {
      setLinkedServicesData(linkedServices);
    }
  }, [linkedServices, setLinkedServicesData]);

  useEffect(() => {
    if (fileProcessing) {
      setFileProcessingData(fileProcessing);
    }
  }, [fileProcessing, setFileProcessingData]);

  const addSecondaryResourceDetails = (
    resource: any,
    secondaryResourceData: any[]
  ) => {
    const parentMatch = secondaryResourceData?.find(
      (secondaryItem) => secondaryItem?.parentResId === resource?.resource_id
    );
    const secondaryMatch = secondaryResourceData?.find(
      (secondaryItem) =>
        secondaryItem?.secondaryResId === resource?.resource_id &&
        secondaryItem?.parentResId === resource?.parentResId
    );

    let updatedResource = { ...resource };

    if (parentMatch) {
      updatedResource = {
        ...updatedResource,
        secondary_merge_resource: parentMatch,
      };
    }

    if (secondaryMatch) {
      updatedResource = {
        ...updatedResource,
        isSecondaryResource: true,
        parentResId: secondaryMatch?.parentResId,
      };
    }

    return updatedResource;
  };

  /**
   * Set run parametes when re run data update
   */
  useEffect(() => {
    if (rerunData) {
      const severityColumnNames = {
        high:
          rerunData.rule_execution_request?.request_body
            ?.severity_column_names?.["1"] || null,
        medium:
          rerunData.rule_execution_request?.request_body
            ?.severity_column_names?.["2"] || null,
        low:
          rerunData.rule_execution_request?.request_body
            ?.severity_column_names?.["3"] || null,
      };

      setSeverityColumnNames(severityColumnNames);

      //Set values in run parameters
      const requestBody = rerunData?.rule_execution_request?.request_body || {};
      const queryParams = requestBody?.query_params || {};
      const runInstance = requestBody?.run_instance || {};
      const severity = requestBody?.validation_severity_level;

      setRunParameters({
        ...defaultRunParameters,
        no_of_errors_in_response: queryParams.no_of_errors_in_response,
        no_of_errors_in_output_files: queryParams.no_of_errors_in_output_files,
        skip_duplicate_records: queryParams.skip_duplicate_records,
        summary_mode: queryParams.summary_mode,
        generate_files: queryParams.generate_files,
        save_input_data_file: queryParams.save_input_data_file,
        store_errors_snapshots_and_create_issues:
          queryParams.store_errors_snapshots_and_create_issues,
        execute_comparison_research_query:
          queryParams.execute_comparison_research_query,
        skip_validation: queryParams.skip_validation,
        use_secondary_merge_resources:
          queryParams.use_secondary_merge_resources,
        keep_downloaded_files: queryParams.keep_downloaded_files,
        pull_new_files_from_server: queryParams.pull_new_files_from_server,
        is_long_running_job: queryParams.is_long_running_job,

        validation_severity_level:
          severity === 1 ? "high" : severity === 2 ? "medium" : "low",

        run_instance: {
          run_id: runInstance.run_id || 1,
          run_name: runInstance.run_name || "Legacy",
        },
        isCustomiseSeverity:
          severityColumnNames.high ||
          severityColumnNames.medium ||
          severityColumnNames.low
            ? true
            : false,
      });

      // Set values in run storage parameters
      setResultStorageParameters({
        ...defaultResultStorageParameters,
        linked_service_id:
          rerunData.rule_execution_request?.request_body?.output_storage_params
            ?.output_storage_linked_service_id,
        linked_service_code:
          rerunData.rule_execution_request?.request_body?.output_storage_params
            ?.output_storage_linked_service_code,
        connection_key_id:
          rerunData.rule_execution_request?.request_body?.output_storage_params
            ?.output_storage_connection_key,
        file_path:
          rerunData.rule_execution_request?.request_body?.output_storage_params
            ?.output_storage_base_file_path,
        validation_execution_report_name:
          rerunData.rule_execution_request?.request_body
            ?.validation_execution_report_name,
        rule_execution_report_name:
          rerunData.rule_execution_request?.request_body
            ?.rule_execution_report_name,
      });

      setInlineVariables(
        rerunData.rule_execution_request?.request_body?.inline_variables
      );
      const updatedInlineVariables = {
        ...rerunData.rule_execution_request?.request_body?.inline_variables,
        resource_variables:
          rerunData.rule_execution_request?.request_body?.inline_variables?.resource_variables?.map(
            (resource: any) => {
              const resourceData =
                rerunData.rule_execution_request?.request_body?.resource_data?.find(
                  (data: any) => data.resource_id === resource.resource_id
                );

              return {
                ...resource,
                resource_name:
                  resourceData?.resource_name || resource.resource_name,
              };
            }
          ) || [],
        rule_variables: {
          filterRule: {
            ...rerunData.rule_execution_request?.request_body?.inline_variables
              ?.rule_variables,
          },
        },
      };
      setViewInlineVariables(updatedInlineVariables);

      if (rerunData?.rule_execution_request?.request_body?.inline_variables)
        setInitialInlineVariables((prev: any) =>
          JSON.parse(
            JSON.stringify(
              rerunData.rule_execution_request?.request_body?.inline_variables
            )
          )
        );

      const filter_rules =
        rerunData.rule_execution_request?.request_body?.filter_rules;

      setFilterFormData({
        filter_rules: filter_rules,
      });

      const secMergeResource =
        rerunData?.rule_execution_request?.request_body?.resource_data
          ?.filter(
            (resource: { secondary_merge_resource: any }) =>
              resource?.secondary_merge_resource
          )
          ?.map(
            (resource: {
              resource_id: any;
              secondary_merge_resource: { resource_id: any };
            }) => ({
              parentResId: resource?.resource_id || null,
              secondaryResId:
                resource?.secondary_merge_resource?.resource_id || null,
              secondary_merge_resource:
                resource?.secondary_merge_resource || null,
            })
          ) || [];
      const resourceDataWithSecMerge =
        rerunData?.rule_execution_request?.request_body?.resource_data.map(
          (item: any) => {
            const updatedItem = addSecondaryResourceDetails(
              item,
              secMergeResource
            );

            // If type is 'sftp', rename resource_path to remote_directory
            if (item?.resource_type === "sftp" && item?.resource_path) {
              const result = {
                ...updatedItem,
                remote_directory: item?.resource_path,
                isSecondaryResource: item?.is_secondary_resource,
                id: item?.resource_id,
              };
              return result;
            }

            return {
              ...updatedItem,
              isSecondaryResource: item?.is_secondary_resource,
              id: item?.resource_id,
            };
          }
        );
      setResourcesData(resourceDataWithSecMerge);
      setSecondaryResourceData(secMergeResource);
      setExecutionName(
        rerunData.rule_execution_request?.request_body?.execution_name || ""
      );
    }
  }, [rerunData]);

  useEffect(() => {
    setCurrentDomainId(ruleData?.domain_id);
    const adhocQueriesInlineVariables: any = {};

    ruleData?.rule_schema?.adhoc_queries?.forEach(
      (obj: { inline_variables: { name: any; value: any }[] }) => {
        obj.inline_variables?.forEach(({ name, value }) => {
          adhocQueriesInlineVariables[name] = value;
        });
      }
    );
  }, [ruleData]);
  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const inlineVariablesChanged =
      JSON.stringify(inlineVariables) !==
      JSON.stringify(initialInlineVariables);

    setHasChanges(inlineVariablesChanged);
  }, [inlineVariables, initialInlineVariables]);

  useEffect(() => {
    //set primary merge column Length
    const columnLength: number = ruleData?.rule_schema?.merge_rule
      ?.primary_dataset?.merge_on
      ? ruleData?.rule_schema?.merge_rule?.primary_dataset?.merge_on?.length
      : 0;

    setMergeColumnsLength(columnLength);
  }, [ruleData, setViewInlineVariables]);

  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: ruleData?.name,
      id: ruleData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [ruleData]);

  const getComparisonSymbol = (comparisonType: string | number) => {
    const symbolMap: { [key: string]: string } = {
      equals: "=",
      "not equals": "!=",
      "greater than": ">",
      "less than": "<",
    };
    return symbolMap[comparisonType] || comparisonType;
  };
  const getResourceNamebyId = (
    resourceId: number | string | null | undefined
  ) => {
    if (!resourcesData) {
      return undefined;
    }
    const matchingResource = resourcesData?.find(
      (resource: { resource_id: string | number | null | undefined }) =>
        resource.resource_id === resourceId
    );
    return matchingResource ? matchingResource?.resource_name : undefined;
  };
  const getFilePreProcessing = (id: string | number | undefined) => {
    const filePreProcessing = fileProcessingData?.find(
      (file: { id: string | number | null | undefined }) => file.id === id
    );
    return filePreProcessing
      ? filePreProcessing.file_processing_attributes
      : undefined;
  };
  const getApiDefinition = (resource: any) => {
    let user_name = resource?.api_definition?.user_name || null;
    let password = resource?.api_definition?.password || null;
    let bearer_token = resource?.api_definition?.bearer_token || null;
    let api_key = resource?.api_definition?.api_key || null;
    let oauth_client_id = resource?.api_definition?.oauth_client_id || null;
    let oauth_client_secret =
      resource?.api_definition?.oauth_client_secret || null;
    let oauth_url = resource?.api_definition?.oauth_url || null;
    let method = resource?.api_definition?.method || "get";
    let content_type =
      resource?.api_definition?.content_type || "application/json";
    let body = resource?.api_definition?.body || null;
    let query_params = resource?.api_definition?.query_params || null;
    let url_params = resource?.api_definition?.url_params || null;
    let url = resource?.api_definition?.url || null;
    let request_timeout = resource?.api_definition?.request_timeout || 0;
    return {
      user_name,
      password,
      bearer_token,
      api_key,
      oauth_client_id,
      oauth_client_secret,
      oauth_url,
      method,
      content_type,
      body,
      query_params,
      url_params,
      url,
      request_timeout,
    };
  };
  const getResourceDefinition = (resource: any) => {
    let connection_key = resource?.connection_key || null;
    let container_name = resource?.container_name || null;
    let resource_path =
      resource?.resource_type === "sftp"
        ? resource?.remote_directory
        : resource?.resource_path || null;
    let file_name = resource?.file_name || null;
    let excel_sheet_name = resource?.excel_sheet_name || null;
    let column_delimiter = resource?.column_delimiter || null;
    let sql_query = resource?.sql_query || null;
    let use_multi_thread_reader = resource?.use_multi_thread_reader || false;
    let column_name_to_partition_on_sql_query =
      resource?.column_name_to_partition_on_sql_query || null;

    return {
      connection_key,
      container_name,
      resource_path,
      file_name,
      excel_sheet_name,
      column_delimiter,
      sql_query,
      use_multi_thread_reader,
      column_name_to_partition_on_sql_query,
    };
  };

  const processAdditionalResourceData = (additionalResourceData: any) => {
    if (!Array.isArray(additionalResourceData)) {
      return null;
    }
    const res = additionalResourceData
      .map((resource) => getAdditionalResourceData(resource))
      .filter(Boolean);
    return res;
  };

  const getAdditionalResourceData = (resourceData: any) => {
    const resource: any = resourceData;

    const type = resource?.type || resource?.resource_type;

    const {
      connection_key,
      container_name,
      resource_path,
      file_name,
      excel_sheet_name,
      column_delimiter,
      sql_query,
      use_multi_thread_reader,
      column_name_to_partition_on_sql_query,
    } = getResourceDefinition(resource);
    const {
      user_name,
      password,
      bearer_token,
      api_key,
      oauth_client_id,
      oauth_client_secret,
      oauth_url,
      method,
      content_type,
      body,
      query_params,
      url_params,
      url,
      request_timeout,
    } = getApiDefinition(resource);
    const additional_base_resource_data =
      resource?.additional_base_resource_data
        ? processAdditionalResourceData(resource?.additional_base_resource_data)
        : null;

    let resourceObject: any = {
      type: type,
      aggregation_type: resource?.aggregation_type,
      resource_name: resource?.resource_name,
      resource_column_details_id:
        resource?.additional_properties?.resource_column_details_id,
      file_processing_id: resource?.file_processing_id,
      is_secondary_resource: resource?.isSecondaryResource
        ? resource?.isSecondaryResource
        : false,
      resource_id: resource?.resource_id || null,
      linked_service_id: resource?.linked_service_id || null,
      linked_service_code: resource?.linked_service_code || null,
      connection_key,
      sql_query,
      container_name,
      file_name,
      excel_sheet_name,
      column_delimiter,
      use_multi_thread_reader,
      column_name_to_partition_on_sql_query,
      additional_base_resource_data,
      filter_rules: resource?.filter_rules,
      file_pre_processing:
        sqlDatabaseType.includes(type) || apiType.includes(type)
          ? null
          : getFilePreProcessing(resource?.file_processing_id),
      aggregation_properties:
        resource?.aggregation_type === "flat"
          ? null
          : resource?.aggregation_properties || null,
      aggregation_base_resource_data: null,
      api_request: {
        user_name,
        password,
        bearer_token,
        api_key,
        oauth_client_id,
        oauth_client_secret,
        oauth_url,
        method,
        content_type,
        body,
        query_params,
        url_params,
        url,
        request_timeout,
      },
    };

    // If type is 'sftp', rename resource_path to remote_directory
    if (type === "sftp" && resource_path) {
      resourceObject.remote_directory = resource_path;
    } else if (resource_path) {
      resourceObject.resource_path = resource_path;
    }
    //check required validation
    checkRequiredValidation(resource, resourceObject);

    if (
      Object.keys(resourceObject).length === 0 &&
      resourceObject.constructor === Object
    ) {
      return null;
    }
    return Object.keys(resourceObject).length > 1 ? resourceObject : null;
  };
  useEffect(() => {
    const executeRule = () => {
      setValidationMessage([]);
      let output_storage_params = {
        output_storage_linked_service_id:
          resultStorageParameters?.linked_service_id || null,
        output_storage_linked_service_code:
          resultStorageParameters?.linked_service_code || null,
        output_storage_connection_key:
          resultStorageParameters?.connection_key_id || null,
        output_storage_base_file_path:
          resultStorageParameters?.file_path || null,
      };
      const isOutputStorageParamsNull = Object.values(
        output_storage_params
      ).some((value) => value === null);
      let severityColumnNamesPayload = {
        1: severityColumnNames.high,
        2: severityColumnNames.medium,
        3: severityColumnNames.low,
      };
      const severity_column_names = Object.fromEntries(
        Object.entries(severityColumnNamesPayload).filter(
          ([_, v]) => v !== null
        )
      );
      const reqBody = {
        rule_execution_request: {
          request_body: {
            execution_name: executionName,
            resource_data: resourcesData.map((resource: any, index: number) => {
              const type = resource?.type || resource?.resource_type;
              let connection_key = resource?.connection_key || null;
              let container_name = resource?.container_name || null;
              let resource_path =
                resource?.resource_type === "sftp"
                  ? resource?.remote_directory
                  : resource?.resource_path || null;
              let file_name = resource?.file_name || null;
              let excel_sheet_name = resource?.excel_sheet_name || null;
              let column_delimiter = resource?.column_delimiter || null;
              let sql_query = resource?.sql_query || null;
              let use_multi_thread_reader =
                resource?.use_multi_thread_reader || false;
              let column_name_to_partition_on_sql_query =
                resource?.column_name_to_partition_on_sql_query || null;
              let additional_base_resource_data = null;

              additional_base_resource_data =
                resource.additional_base_resource_data !== null
                  ? resource.additional_base_resource_data?.length > 0
                    ? processAdditionalResourceData(
                        resource?.additional_base_resource_data
                      )
                    : null
                  : null;
              const {
                user_name,
                password,
                bearer_token,
                api_key,
                oauth_client_id,
                oauth_client_secret,
                oauth_url,
                method,
                content_type,
                body,
                query_params,
                url_params,
                url,
                request_timeout,
              } = getApiDefinition(resource);

              const secondaryMergeResourceParentId =
                resource?.isSecondaryResource
                  ? resourcesData[index - 1]?.secondary_merge_resource
                      ?.parentResId
                  : null;

              let resourceObject: any = {
                type: type,
                aggregation_type: resource?.aggregation_type,
                resource_name: resource?.resource_name,
                resource_column_details_id:
                  resource?.additional_properties?.resource_column_details_id,
                file_processing_id: resource?.file_processing_id,
                is_secondary_resource: resource?.isSecondaryResource
                  ? resource?.isSecondaryResource
                  : false,
                resource_id: resource?.resource_id || null,
                linked_service_id: resource?.linked_service_id || null,
                linked_service_code: resource?.linked_service_code || null,
                connection_key,
                sql_query,
                container_name,
                file_name,
                excel_sheet_name,
                column_delimiter,
                use_multi_thread_reader,
                column_name_to_partition_on_sql_query,
                additional_base_resource_data,
                filter_rules: resource?.filter_rules,
                file_pre_processing:
                  sqlDatabaseType.includes(type) || apiType.includes(type)
                    ? null
                    : getFilePreProcessing(resource?.file_processing_id),
                aggregation_properties:
                  resource?.aggregation_type === "flat"
                    ? null
                    : resource?.aggregation_properties || null,
                aggregation_base_resource_data:
                  resource?.aggregation_type === "flat"
                    ? null
                    : getAdditionalResourceData(
                        resource?.aggregation_base_resource_data
                      ) || null,
                api_request: {
                  user_name,
                  password,
                  bearer_token,
                  api_key,
                  oauth_client_id,
                  oauth_client_secret,
                  oauth_url,
                  method,
                  content_type,
                  body,
                  query_params,
                  url_params,
                  url,
                  request_timeout,
                },
                secondary_merge_resource:
                  resource?.secondary_merge_resource?.secondary_merge_resource,
                secondary_merge_resource_parent_id:
                  secondaryMergeResourceParentId,
              };

              // If type is 'sftp', rename resource_path to remote_directory
              if (type === "sftp" && resource_path) {
                resourceObject.remote_directory = resource_path;
              } else if (resource_path) {
                resourceObject.resource_path = resource_path;
              }

              checkRequiredValidation(resource, resourceObject);

              resourceObject = Object.fromEntries(
                Object.entries(resourceObject).filter(([_, v]) => v !== null)
              );

              return resourceObject;
            }),
            ...(isOutputStorageParamsNull ? {} : { output_storage_params }),
            run_instance: {
              run_id: runParameters?.run_instance.run_id || 1,
              run_name: runParameters?.run_instance.run_name || "Legacy",
            },
            severity_column_names,
            filter_rules: filterFormData?.filter_rules ?? [],
            validation_severity_level:
              runParameters?.validation_severity_level === "high"
                ? 1
                : runParameters?.validation_severity_level === "medium"
                ? 2
                : 3,
            inline_variables: {
              comparison_research_query_variables:
                viewInlineVariables?.comparison_research_query_variables,
              resource_variables:
                viewInlineVariables?.resource_variables?.length >= 0
                  ? viewInlineVariables?.resource_variables.map((item: any) => {
                      return {
                        resource_id: item?.resource_id,
                        resource_vars: item?.resource_vars,
                      };
                    })
                  : [],
              rule_variables: {
                ...viewInlineVariables?.rule_variables?.filterRule,
                ...viewInlineVariables?.rule_variables?.adhoc_queries,
              },
            },
            rule_execution_report_name:
              resultStorageParameters?.rule_execution_report_name,
            validation_execution_report_name:
              resultStorageParameters?.validation_execution_report_name,
            query_params: {
              ...runParameters,
            },
          },
          ...runParameters,
          no_of_sample_validation_errors:
            rerunData?.rule_execution_request?.no_of_sample_validation_errors,
          no_of_errors_in_filter_record_output_files:
            rerunData?.rule_execution_request
              ?.no_of_errors_in_filter_record_output_files,
          use_merge_on_keys_as_unique_keys:
            rerunData?.rule_execution_request?.use_merge_on_keys_as_unique_keys,
          additional_info_for_all_resources:
            rerunData?.rule_execution_request
              ?.additional_info_for_all_resources,
        },
        rule_id: rerunData?.rule_id,
        rule_version: rerunData?.rule_version,
      };

      if (
        reqBody?.rule_execution_request?.request_body?.inline_variables
          ?.rule_variables &&
        Object.keys(
          reqBody.rule_execution_request?.request_body?.inline_variables
            .rule_variables
        ).length > 0
      ) {
        Object.keys(
          reqBody.rule_execution_request?.request_body?.inline_variables
            .rule_variables
        ).forEach((ruleKey) => {
          const ruleVariables =
            reqBody.rule_execution_request?.request_body?.inline_variables
              .rule_variables[ruleKey];
          if (ruleVariables) {
            Object.keys(ruleVariables).forEach((key) => {
              if (ruleVariables[key] === "") {
                setValidationMessage((prev) => [
                  ...prev,
                  {
                    id: `${ruleKey} Variables`,
                    message: `Please enter value for ${key}`,
                  },
                ]);
              }
            });
          }
        });
      }

      if (
        reqBody.rule_execution_request?.request_body?.inline_variables
          .resource_variables &&
        reqBody.rule_execution_request?.request_body?.inline_variables
          .resource_variables.length > 0
      ) {
        reqBody.rule_execution_request?.request_body?.inline_variables.resource_variables.forEach(
          (resource: { resource_vars: any; resource_name: any }) => {
            const resourceVars = resource.resource_vars;
            if (resourceVars) {
              Object.keys(resourceVars).forEach((key) => {
                if (resourceVars[key] === "") {
                  setValidationMessage((prev) => [
                    ...prev,
                    {
                      id: `Resource ${resource?.resource_name} Variables`,
                      message: `Please enter value for ${key}`,
                    },
                  ]);
                }
              });
            }
          }
        );
      }

      if (
        reqBody?.rule_execution_request?.request_body?.inline_variables
          ?.comparison_research_query_variables &&
        Object.keys(
          reqBody.rule_execution_request?.request_body?.inline_variables
            .comparison_research_query_variables
        ).length > 0
      ) {
        const researchQueryVariables =
          reqBody.rule_execution_request?.request_body?.inline_variables
            .comparison_research_query_variables;
        if (researchQueryVariables) {
          Object.keys(researchQueryVariables).forEach((key) => {
            if (researchQueryVariables[key] === "") {
              setValidationMessage((prev) => [
                ...prev,
                {
                  id: `Research Query Variables`,
                  message: `Please enter value for ${key}`,
                },
              ]);
            }
          });
        }
      }
      setRuleExecutionPayload(reqBody);
    };
    if (checkValidation) executeRule();
  }, [checkValidation]);

  useEffect(() => {
    if (
      validationMessage.length === 0 &&
      ruleExecutionPayload &&
      Object.keys(ruleExecutionPayload).length > 0
    ) {
      setIsBackdropLoading(true);
      if (executionMode) {
        backgroundExecuteRuleReRun(ruleExecutionPayload)
          .then(() => {
            showToast(
              "Your re-run rule execution is in the queue. We will notify you once it's done!",
              "warn"
            );
            navigate(`/rules-execution-history`);
          })
          .catch((error: any) => {
            console.error(error);
          })
          .finally(() => {
            setCheckValidation(false);
            setRuleExecutionPayload({});
            setIsBackdropLoading(false);
          });
      } else {
        executeRuleReRun(ruleExecutionPayload)
          .then((response: any) => {
            showToast("Rule Re-Run successfully!", "success");
            navigate(
              `/rules-execution-history/${response?.rule_id}/${response?.id}/dashboard`,
              {
                state: {
                  rowData: response,
                  isRuleExecutionResponse: true,
                },
              }
            );
          })
          .catch((error: any) => {
            console.error(error);
          })
          .finally(() => {
            setCheckValidation(false);
            setRuleExecutionPayload({});
            setIsBackdropLoading(false);
          });
      }
    } else if (validationMessage.length > 0) {
      const temp = (
        <div>
          Not all fields are configured. Please check the following fields:
          {validationMessage.map((msg) => (
            <div>
              <span style={{ fontWeight: 900 }}>{msg.id}</span>-{msg.message}
            </div>
          ))}
        </div>
      );
      showToast(temp, "warning");
      setCheckValidation(false);
      return;
    }
  }, [validationMessage, ruleExecutionPayload]);

  const checkRequiredValidation = (resource: any, resourceObject: any) => {
    const type = resource?.type || resource?.resource_type;
    const fieldNamesMap: { [key: string]: string } = {
      connection_key: "Connection Key",
      container_name: "Container Name",
      resource_path: "Resource Path",
      file_name: "File Name",
      column_delimiter: "Column Delimiter",
      base_resource_id: "Base resource",
      base_resource_columns: "Base Resource Columns",
      aggregation_query: "Aggregation Query",
      sql_query: "Sql Query",
      user_name: "Username",
      password: "Password",
      url: "Url",
      request_timeout: "Request Timeout",
      bearer_token: "Bearer Token",
      api_key: "Api Key",
      oauth_client_id: "Oauth Client Id",
      oauth_client_secret: "Oauth Client Secret",
      oauth_url: "Oauth Url",
    };
    if (resource?.aggregation_type === "flat") {
      let requiredFields: any = [];
      if (sqlDatabaseType.includes(type)) {
        requiredFields = ["connection_key", "sql_query"];
      } else if (type === "sftp" || type === "local") {
        requiredFields = [
          "connection_key",
          "resource_path",
          "file_name",
          "column_delimiter",
        ];
      } else if (type === "blob") {
        requiredFields = [
          "connection_key",
          "resource_path",
          "file_name",
          "column_delimiter",
          "container_name",
        ];
      } else if (type === "basic_auth_api") {
        requiredFields = [
          "connection_key",
          "user_name",
          "password",
          "url",
          "request_timeout",
        ];
      } else if (type === "token_auth_api") {
        requiredFields = [
          "connection_key",
          "bearer_token",
          "url",
          "request_timeout",
        ];
      } else if (type === "key_auth_api") {
        requiredFields = [
          "connection_key",
          "api_key",
          "url",
          "request_timeout",
        ];
      } else if (type === "oauth_api") {
        requiredFields = [
          "connection_key",
          "oauth_client_id",
          "oauth_client_secret",
          "oauth_url",
          "url",
          "request_timeout",
        ];
      }
      let missingFields: any;
      if (
        type === "basic_auth_api" ||
        type === "token_auth_api" ||
        type === "key_auth_api" ||
        type === "oauth_api"
      ) {
        missingFields = requiredFields?.filter(
          (field: any) => resourceObject?.api_request[field] === null
        );
      } else {
        missingFields =
          requiredFields &&
          requiredFields.filter((field: any) => resourceObject[field] === null);
      }

      if (missingFields && missingFields.length > 0) {
        const missingFieldNames = missingFields.map(
          (field: any) => `${fieldNamesMap[field]}`
        );
        setValidationMessage((prev) => [
          ...prev,
          {
            id: resource?.resource_name,
            message: missingFieldNames.join(", "),
          },
        ]);
        // return false;
      }
    } else if (
      resource?.aggregation_type === "aggregated" &&
      resourceObject.aggregation_properties !== null
    ) {
      const requiredFields: string[] = [
        "base_resource_id",
        "base_resource_columns",
        "aggregation_query",
      ];
      const missingFields = requiredFields.filter(
        (field) => resourceObject.aggregation_properties[field] === null
      );

      if (missingFields.length > 0) {
        const missingFieldNames = missingFields.map(
          (field) => `${fieldNamesMap[field]}`
        );
        setValidationMessage((prev) => [
          ...prev,
          {
            id: resource?.resource_name,
            message: missingFieldNames.join(", "),
          },
        ]);
      }
    }

    // Add for variables start
    if (
      resourceObject.inline_variables &&
      Object.keys(resourceObject.inline_variables).length > 0
    ) {
      Object.keys(resourceObject.inline_variables).forEach((key) => {
        if (resourceObject.inline_variables[key] === "") {
          setValidationMessage((prev) => [
            ...prev,
            {
              id: `${resource?.resource_name}-Variables`,
              message: `Please enter value for ${key}`,
            },
          ]);
        }
      });
    }
    // Add for variables end
  };
  const Summary = () => {
    return (
      <Box className="rule-execution-page compact-text-box-card">
        <Box className="text-box-card  pt-0" sx={{ marginBottom: "12px" }}>
          <Box>
            <h2 className="page-title">
              Domain <span>{ruleData?.domain_name}</span>
            </h2>
          </Box>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6} lg={6}>
              <h4>Primary Resource</h4>
              {ruleData?.rule_schema?.merge_rule?.primary_dataset ? (
                <table className="table">
                  <thead>
                    <tr>
                      <th className="pe-40">Resource</th>
                      <th className="pe-40">Keys</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="pe-40">
                        {getResourceNamebyId(
                          ruleData?.rule_schema?.merge_rule?.primary_dataset
                            ?.resource_id
                        )}
                      </td>
                      <td className="pe-40">
                        {ruleData?.rule_schema?.merge_rule?.primary_dataset?.merge_on?.join(
                          ", "
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              ) : (
                <p>No primary resource available</p>
              )}
            </Grid>
            <Grid item xs={12} md={6} lg={6}>
              <h4>Secondary Resource</h4>
              {ruleData?.rule_schema?.merge_rule?.secondary_datasets ? (
                <table className="table">
                  <thead>
                    <tr>
                      <th className="pe-40">Resource</th>
                      <th className="pe-40">Keys</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ruleData?.rule_schema?.merge_rule?.secondary_datasets?.map(
                      (dataset: {
                        resource_id: string | number | null | undefined;
                        merge_on: any[];
                      }) => (
                        <tr key={dataset.resource_id}>
                          <td className="pe-40">
                            {getResourceNamebyId(dataset.resource_id)}
                          </td>
                          <td className="pe-40">
                            {dataset.merge_on.join(", ")}
                          </td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              ) : (
                <p>No secondary resource available</p>
              )}
            </Grid>
          </Grid>
        </Box>
        <Box className="text-box-card pt-0" sx={{ marginBottom: "12px" }}>
          <Box>
            <h2 className="page-title">
              {" "}
              Rule <span>{ruleData?.name}</span>
            </h2>
          </Box>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6} lg={6}>
              <Grid item xs={12} sx={{ marginBottom: "12px" }}>
                <h4>Domain Comparison Rules</h4>
                {ruleData?.rule_schema?.domain_comparison_rules &&
                ruleData?.rule_schema?.domain_comparison_rules.length > 0 ? (
                  <table className="table">
                    <thead>
                      <tr>
                        <th className="pe-20">Name</th>
                        <th className="pe-20">Column Name</th>
                        <th className="pe-20">Operator</th>
                      </tr>
                    </thead>
                    <tbody>
                      {ruleData?.rule_schema?.domain_comparison_rules?.map(
                        (rule: {
                          name: string | number | null | undefined;
                          column_name: string;
                          comparison_type: string;
                        }) => (
                          <tr key={rule.name}>
                            <td className="pe-20">{rule.name}</td>
                            <td className="pe-20">{rule.column_name}</td>
                            <td className="pe-20">
                              {getComparisonSymbol(rule.comparison_type)}
                            </td>
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                ) : (
                  <p>No domain comparison rules available</p>
                )}
              </Grid>
              <Grid item xs={12}>
                <h4 className="heading-hr">Resource Filter</h4>
                {ruleData?.rule_schema?.filter_rules &&
                ruleData?.rule_schema?.filter_rules.length > 0 ? (
                  <table className="table table-resource-filter mb-3">
                    <thead>
                      <tr>
                        <th className="pe-20 pb-10 width-180">Resource</th>
                        <th className="pe-20 pb-10">SQL Expression</th>
                      </tr>
                    </thead>
                    <tbody>
                      {ruleData?.rule_schema?.filter_rules?.map((rule: any) => (
                        <tr key={rule.name}>
                          <td className="pe-20">{rule.name}</td>
                          <td className="pe-20 ">
                            <Box className="word-break-all">
                              {rule.sql_query}
                            </Box>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <p>No Resource Filter available</p>
                )}
              </Grid>
              <Grid item xs={12}>
                <h4 className="heading-hr">Adhoc Queries</h4>
                {ruleData?.rule_schema?.adhoc_queries &&
                ruleData?.rule_schema?.adhoc_queries.length > 0 ? (
                  <table className="table table-resource-filter">
                    <thead>
                      <tr>
                        <th className="pe-20 pb-10 width-180">Name</th>
                        <th className="pe-20 pb-10">SQL Expression</th>
                      </tr>
                    </thead>
                    <tbody>
                      {ruleData?.rule_schema?.adhoc_queries?.map(
                        (query: any, index: number) => (
                          <tr key={index}>
                            <td className="pe-20">{query.name}</td>
                            <td className="pe-20 ">
                              <Box className="word-break-all">
                                {query.sql_query}
                              </Box>
                            </td>
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                ) : (
                  <p>No Adhoc Query available</p>
                )}
              </Grid>
            </Grid>
            <Grid item xs={12} md={6} lg={6}>
              <h4>Custom Comparison Rules</h4>
              {ruleData?.rule_schema?.custom_comparison_rules &&
              ruleData?.rule_schema?.custom_comparison_rules.length > 0 ? (
                <table className="table w-100">
                  <thead>
                    <tr>
                      <th className="pe-20">Name</th>
                      <th className="pe-20">Column Name</th>
                      <th className="pe-20">Operator</th>
                      <th className="pe-20">Column Name</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ruleData?.rule_schema?.custom_comparison_rules?.map(
                      (rule: any) => (
                        <tr key={rule.name}>
                          <td className="pe-20">{rule.name}</td>
                          <td className="pe-20">
                            <Box
                              sx={{ maxWidth: "135px", wordWrap: "break-word" }}
                            >
                              {rule?.left_operand?.column_name}
                            </Box>
                          </td>
                          <td className="pe-20">
                            {getComparisonSymbol(rule.comparison_type)}
                          </td>
                          <td className="pe-20">
                            <Box
                              sx={{ maxWidth: "135px", wordWrap: "break-word" }}
                            >
                              {rule?.right_operand?.column_name}
                            </Box>
                          </td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              ) : (
                <p>No custom comparison rules available</p>
              )}
            </Grid>
          </Grid>
        </Box>
        <Box className="text-box-card pt-0 mb-0">
          <Box>
            <h2 className="page-title">
              <span>Description</span>
            </h2>
            {ruleData?.description ? (
              <span className="break-word">{ruleData?.description}</span>
            ) : (
              <span>No description available</span>
            )}
          </Box>
        </Box>
      </Box>
    );
  };
  const handleChangeAccordion = (panel: any) => (_: any, isExpanded: any) => {
    if (isResourceEdit !== "") {
      showToast("Please save changes first!!", "warning");
      return;
    }
    setExpandedAccordion(isExpanded ? panel : null);
  };
  const handleViewResource = (domainId: any, resource: any, e: any) => {
    e.preventDefault();
    if (isResourceEdit !== "") {
      return;
    }
    navigate(`/resource/${domainId}/view/${resource}`);
  };

  const handleChangeVariable = (event: any, inline_type: string, id?: any) => {
    const { name, value } = event.target;
    if (id && inline_type === "resource_variables") {
      setInlineVariables((prev: any) => ({
        ...prev,
        resource_variables: prev.resource_variables.map((resource: any) => {
          if (resource.resource_id === id) {
            return {
              ...resource,
              resource_vars: {
                ...resource.resource_vars,
                [name]: value,
              },
            };
          }
          return resource;
        }),
      }));
    } else if (inline_type === "filterRule") {
      setInlineVariables((prev: any) => ({
        ...prev,
        rule_variables: {
          ...prev.rule_variables,
          filterRule: {
            ...prev.rule_variables.filterRule,
            [name]: value,
          },
        },
      }));
    } else if (inline_type === "adhoc_queries") {
      setInlineVariables((prev: any) => ({
        ...prev,
        rule_variables: {
          ...prev.rule_variables,
          adhoc_queries: {
            ...prev.rule_variables.adhoc_queries,
            [name]: value,
          },
        },
      }));
    } else if (inline_type === "comparison_research_query_variables") {
      setInlineVariables((prev: any) => ({
        ...prev,
        comparison_research_query_variables: {
          ...prev.comparison_research_query_variables,
          [name]: value,
        },
      }));
    }
  };
  const handleSecondaryMergeResourceDialog = () => {
    setSecondaryMergeResourceDialog(false);
  };

  const onSaveSecondaryMergeResource = () => {
    setSecondaryMergeResourceDialog(false);
  };
  const handleExecuteSubmit = async () => {
    try {
      await executionNameSchema.validate(
        { execution_name: executionName },
        {
          abortEarly: false,
        }
      );
      setCheckValidation(true);
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  return (
    <>
      <Loader isLoading={isBackdropLoading} />
      <Loader isLoading={isLoading} />

      <div
        className="accordion-panel "
        style={{ marginBottom: "0", marginTop: "8px" }}
      >
        <Box>
          <ExecutionName
            handleChangeAccordion={handleChangeAccordion}
            errors={errors}
            setErrors={setErrors}
            executionName={executionName}
            setExecutionName={setExecutionName}
            executionMode={executionMode}
            setExecutionMode={setExecutionMode}
          />
          <Accordion
            className="heading-bold box-shadow"
            expanded={expandedAccordion === "summary"}
            onChange={handleChangeAccordion("summary")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Summary
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <Summary />
            </AccordionDetails>
          </Accordion>
          {resourcesData?.length > 0 &&
            resourcesData.map((resource: any, index: number) => (
              <>
                <Accordion
                  className={`mt-8 heading-bold box-shadow ${
                    resource?.isSecondaryResource
                      ? "execution-secondary-resource"
                      : ""
                  }`}
                  expanded={
                    expandedAccordion === `${resource?.resource_id}-${index}`
                  }
                  onChange={handleChangeAccordion(
                    `${resource?.resource_id}-${index}`
                  )}
                  key={index}
                >
                  <AccordionSummary
                    aria-controls={`panel-${resource?.resource_id}-content`}
                    id={`panel-${resource?.resource_id}-header`}
                    expandIcon={<ExpandMoreIcon />}
                  >
                    <div className="custom-actions">
                      <div className="heading">
                        {
                          resourcesData?.find(
                            (resource1: any) =>
                              resource1?.resource_id == resource?.resource_id
                          )?.resource_name
                        }
                      </div>
                      <div className="action-btns">
                        <Tooltip
                          title="Navigate to view resource"
                          placement="top"
                        >
                          <button
                            className="view-icon"
                            onClick={(e) =>
                              handleViewResource(
                                ruleData.domain_id,
                                resource?.id,
                                e
                              )
                            }
                          >
                            <IconViewSvgWhite />
                          </button>
                        </Tooltip>
                      </div>
                    </div>
                  </AccordionSummary>
                  <AccordionDetails sx={{ paddingTop: "16px" }}>
                    <React.Fragment key={resource?.resource_id}>
                      <ReRunRuleExecutionTab
                        resourcesData={resourcesData}
                        resourceData={resourcesData?.find((res: any) =>
                          res?.isSecondaryResource
                            ? res?.id === resource?.id &&
                              res?.parentResId === resource?.parentResId
                            : res?.resource_id === resource?.resource_id
                        )}
                        isLoading={isBackdropLoading}
                        setResourcesData={setResourcesData}
                        setIsLoading={setIsLoading}
                        fileProcessingData={fileProcessingData}
                        fileStreamIndex={index}
                        setViewInlineVariables={setViewInlineVariables}
                        viewInlineVariables={viewInlineVariables}
                        isReRun={isReRunData?.isReRun}
                      />
                    </React.Fragment>
                  </AccordionDetails>
                </Accordion>
              </>
            ))}

          <Accordion
            className="mt-8 heading-bold box-shadow"
            expanded={expandedAccordion === "run-parameters"}
            onChange={handleChangeAccordion("run-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Run Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <RunParameters
                runParameters={runParameters}
                setRunParameters={setRunParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                severityColumnNames={severityColumnNames}
                setSeverityColumnNames={setSeverityColumnNames}
                isShow={{
                  isShowSkipValidation: true,
                  isShowExecuteResearchQueries: true,
                  use_secondary_merge_resources: true,
                }}
                isReRun={isReRunData?.isReRun}
              />
            </AccordionDetails>
          </Accordion>

          <Accordion
            className="mt-8 heading-bold box-shadow"
            expanded={expandedAccordion === "result-storage-parameters"}
            onChange={handleChangeAccordion("result-storage-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Result Storage Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <ResultStorageParameters
                resultStorageParameters={resultStorageParameters}
                setResultStorageParameters={setResultStorageParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                isFromRule={true}
                isReRun={isReRunData?.isReRun}
              />
            </AccordionDetails>
          </Accordion>

          {((inlineVariables?.rule_variables?.adhoc_queries &&
            Object.keys(inlineVariables?.rule_variables?.adhoc_queries).length >
              0) ||
            (inlineVariables?.rule_variables?.filterRule &&
              Object.keys(inlineVariables?.rule_variables?.filterRule).length >
                0) ||
            (inlineVariables?.resource_variables &&
              inlineVariables?.resource_variables.length > 0)) && (
            <Accordion
              className="mt-8 heading-bold box-shadow"
              expanded={expandedAccordion === "inline-variables"}
              onChange={handleChangeAccordion("inline-variables")}
            >
              <AccordionSummary
                aria-controls="panel1d-content"
                id="panel1d-header"
                expandIcon={<ExpandMoreIcon />}
              >
                Variables
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                {!isEditVariables ? (
                  <RuleInlineVariables
                    handleChangeVariable={handleChangeVariable}
                    inlineVariables={viewInlineVariables}
                    isReadOnly={true}
                  />
                ) : (
                  <RuleInlineVariables
                    handleChangeVariable={handleChangeVariable}
                    inlineVariables={inlineVariables}
                    isReadOnly={false}
                  />
                )}
                {!isEditVariables ? (
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    md
                    sx={{ justifyContent: "flex-end", display: "flex" }}
                  >
                    <button
                      className="btn-nostyle icon-btn-edit"
                      onClick={() => {
                        setIsEditVariables(true);
                        setInlineVariables(viewInlineVariables);
                      }}
                    >
                      <IconBtnEditBase />
                    </button>
                  </Grid>
                ) : (
                  <Grid
                    item
                    xs
                    sx={{
                      display: "flex",
                      alignItems: "flex-end",
                      justifyContent: "flex-end",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        columnGap: "8px",
                        flexWrap: "wrap",
                      }}
                    >
                      <Button
                        color="secondary"
                        variant="contained"
                        onClick={() => {
                          setIsEditVariables(false);
                          setInlineVariables(viewInlineVariables);
                        }}
                        className="btn-orange btn-dark"
                      >
                        Cancel
                      </Button>
                      <Button
                        color="secondary"
                        variant="contained"
                        onClick={() => {
                          setViewInlineVariables(inlineVariables);
                          setIsEditVariables(false);
                        }}
                        className="btn-orange"
                        disabled={!hasChanges}
                      >
                        <SaveOutlinedIcon /> &nbsp; Save
                      </Button>
                    </Box>
                  </Grid>
                )}
              </AccordionDetails>
            </Accordion>
          )}
          {ruleData?.rule_schema?.filter_rules &&
            ruleData?.rule_schema?.filter_rules.length > 0 && (
              <CustomAccordian
                expandId="filter-rules"
                title="Filter Rules"
                handleChangeAccordion={handleChangeAccordion}
                isEnabled={true}
                topMargin={8}
              >
                {!isEditFilters ? (
                  <FilterRules formData={filterFormData} isViewOnly={true} />
                ) : (
                  <CustomMultipleResourceRuleFilters
                    formData={filterFormData}
                    resourcesData={resourcesData}
                    setFormData={setFilterFormData}
                    setViewInlineVariables={setViewInlineVariables}
                    isEditFilters={isEditFilters}
                    setIsEditFilters={setIsEditFilters}
                  />
                )}
                <Grid
                  item
                  xs={12}
                  sm={12}
                  md
                  sx={{
                    justifyContent: "flex-end",
                    display: "flex",
                    marginTop: "8px",
                  }}
                >
                  {!isEditFilters && (
                    <button
                      className="btn-nostyle icon-btn-edit"
                      onClick={() => {
                        setIsEditFilters((prev) => !prev);
                      }}
                    >
                      <IconBtnEditBase />
                    </button>
                  )}
                </Grid>
              </CustomAccordian>
            )}
          {ruleData?.rule_schema?.adhoc_queries &&
            ruleData?.rule_schema?.adhoc_queries.length > 0 && (
              <CustomAccordian
                expandId="adhoc-query"
                title="Adhoc Query"
                handleChangeAccordion={handleChangeAccordion}
                isEnabled={true}
                topMargin={8}
              >
                <AddMultipleGridList
                  gridValue={ruleData?.rule_schema?.adhoc_queries}
                  setGridValue={() => {}}
                  isViewOnly={true}
                  buttonName=" "
                  errors={{}}
                  setErrors={() => {}}
                />
              </CustomAccordian>
            )}
          {!isEditResource && isResourceEdit === "" && (
            <FlexBetween
              gap="3rem"
              sx={{ marginTop: 2, display: "flex", justifyContent: "flex-end" }}
            >
              <Button
                type="submit"
                className="btn-orange"
                color="secondary"
                variant="contained"
                onClick={handleExecuteSubmit}
              >
                {isReRunData?.isReRun ? "Re-Execute" : "Execute"}
              </Button>
            </FlexBetween>
          )}
        </Box>
      </div>
      <SecondaryMergeResourceDialog
        secondaryMergeResourceDialog={secondaryMergeResourceDialog}
        handleSecondaryMergeResourceDialog={handleSecondaryMergeResourceDialog}
        resourceDataWithSecMerge={resourceDataWithSecMerge}
        setResourceDataWithSecMerge={setResourceDataWithSecMerge}
        onSaveSecondaryMergeResource={onSaveSecondaryMergeResource}
        fetchedResourcesData={fetchedResourcesData}
        setIsLoading={setIsLoading}
        isEditSecondaryResource={isEditSecondaryResource}
        mergeColumnsLength={mergeColumnsLength}
        fetchedConnectionKeys={fetchedConnectionKeys}
        secondaryResourceData={secondaryResourceData}
        setSecondaryResourceData={setSecondaryResourceData}
        setResourcesData={setResourcesData}
        resourcesData={resourcesData}
      />
    </>
  );
};

export default ReRunRuleExecution;
