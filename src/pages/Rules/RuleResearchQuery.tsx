import { useEffect, useState } from "react";
import "../../styles/research-query.scss";
import {
  Button,
  Box,
  IconButton,
  Grid,
  Autocomplete,
  TextField,
  ListItemText,
} from "@mui/material";
import ResourceQueryTab from "../../components/ResearchQuery/ResourceQueryTab";
import ExternalQueryTab from "../../components/ResearchQuery/ExternalQueryTab";
import MergeQueryTab from "../../components/ResearchQuery/MergeQueryTab";
import { IResearchQuery } from "../../types/researchQuery";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import Loader from "../../components/Molecules/Loader/Loader";
import { useParams } from "react-router-dom";
import useFetchRuleById from "../../hooks/useFetchRuleById";
import useFetchResearchQueryByRuleId from "../../hooks/useFetchResearchQueryByRuleId";
import { GridColDef } from "@mui/x-data-grid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { useToast } from "../../services/utils";
import {
  addResearchQuery,
  getResearchQueryDetail,
  updateResearchQuery,
} from "../../services/rulesService";
import DataTable from "../../components/DataGrids/DataGrid";
import { ruleQueriesType } from "../../services/constants";
import { replaceQueryFromDomainMapped } from "../../services/utils";
import ResourceColumnsChips from "../../components/Organisms/ResourceColumnsChips";
import QueryCollapsible from "../../components/Molecules/CommonCollapsible/QueryCollapsible";
import ReadMoreLess from "../../components/ReadMoreLess";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import useFetchConnectionKeysAll from "../../hooks/useFetchConnectionKeysAll";
import useFetchResourcesByIdMultipleIds from "../../hooks/useFetchResourcesByMultipleIds";
import useFetchAllResources from "../../hooks/useFetchAllResources";
import useFetchResourceColumnsByMultipleIds from "../../hooks/useFetchResourceColumnsByMultipleIds";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
import MixedQueryTab from "../../components/ResearchQuery/MixedQueryTab";
import { IconDeleteBlueSvg, IconEditBase } from "../../common/utils/icons";

export default function RuleResearchQuery() {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const [columns, setColumns] = useState<any>({
    resourceColumns: [],
    missingColumns: [],
    mismatchedColumns: [],
    mergedColumns: [],
    mixedQueryData: [
      {
        name: "",
        columns: [],
      },
    ],
  });

  const {
    isLoading,
    setIsLoading,
    setResourceIds,
    allResourcesData,
    setAllResourcesData,
    resourceData,
    setResourceData,
    resourceColIds,
    setResourceColIds,
    resourceColumnsData,
    setResourceColumnsData,
    globalVariables,
    setGlobalVariables,
    linkedServicesData,
    setLinkedServicesData,
    fetchedConnectionKeys,
    setFetchedConnectionKeys,
    setQueryBuilderTempValue,
    editorRef,
    resourceIds,
    setShowAggregatedColumns,
  } = useRuleResourceContext();
  const { id: currentRuleId } = useParams();
  const { showToast } = useToast();
  const [ruleData] = useFetchRuleById({ setIsLoading, currentRuleId });
  const [researchQueryDetail] = useFetchResearchQueryByRuleId({
    setIsLoading,
    currentRuleId,
  });
  const [linkedServices] = useFetchLinkedServices({ setIsLoading });
  const [connectionKeys] = useFetchConnectionKeysAll({ setIsLoading });
  const [resourcesData] = useFetchResourcesByIdMultipleIds({
    resourceIds,
    setIsLoading,
  });
  const [allResources] = useFetchAllResources({
    setIsLoading,
  });
  const [resourceColumns] = useFetchResourceColumnsByMultipleIds({
    resourceColIds,
    setIsLoading,
  });
  useEffect(() => {
    if (linkedServices) {
      setLinkedServicesData(linkedServices);
    }
  }, [linkedServices]);

  useEffect(() => {
    if (connectionKeys) {
      setFetchedConnectionKeys(connectionKeys);
    }
  }, [connectionKeys]);

  useEffect(() => {
    if (resourcesData) {
      setResourceData(resourcesData);
    }
  }, [resourcesData]);

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources]);

  useEffect(() => {
    if (resourceColumns) {
      setResourceColumnsData(resourceColumns);
    }
  }, [resourceColumns]);

  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: ruleData?.name,
      id: ruleData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [ruleData]);

  const [isEditQuery, setIsEditQuery] = useState<any>(false);
  const [queryType, setQueryType] = useState("");
  const [editFormData, setEditFormData] = useState();

  const [checked, setChecked] = useState(false);
  const [researchQueryData, setResearchQueryData] = useState<IResearchQuery[]>(
    []
  );
  const [researchQueryDetailData, setResearchQueryDetailData] = useState<any>(
    {}
  );

  useEffect(() => {
    if (queryType === "Merged_Data") {
      if (columns) {
        setColumns((prev: any) => ({
          ...prev,
          resourceColumns: [],
          mergedColumns: columns?.missingColumns,
        }));
      }
    }
  }, [queryType]);

  const handleTransition = (isChecked: boolean, actionType?: string) => {
    setChecked(isChecked);
    if (actionType === "add") {
      setIsEditQuery(false);
      setColumns((prev: any) => ({
        ...prev,
        mergedColumns: [],
        resourceColumns: [],
      }));
    }
  };

  useEffect(() => {
    if (researchQueryDetail) {
      setResearchQueryDetailData(researchQueryDetail);
    }
  }, [researchQueryDetail]);
  useEffect(() => {
    if (researchQueryDetailData) {
      const updatedResearchQueryData =
        researchQueryDetailData?.research_queries?.map(
          (researchQuery: any, index: number) => {
            return {
              ...researchQuery,
              source:
                researchQuery?.source?.length > 0 &&
                researchQuery?.source[0]?.type === "Mixed"
                  ? researchQuery?.source
                  : researchQuery?.source[0],

              id: index + 1,
              updatedQuery: researchQuery?.query,
              query: replaceQueryFromDomainMapped(
                researchQuery?.query,
                columns?.missingColumns ?? []
              ),
            };
          }
        );
      setResearchQueryData(updatedResearchQueryData ?? []);
      setGlobalVariables(researchQueryDetailData?.variables);
    }
  }, [researchQueryDetailData, columns?.missingColumns]);
  useEffect(() => {
    if (ruleData?.rule_schema?.resources?.length > 0) {
      setResourceIds(ruleData.rule_schema.resources);
    }
  }, [ruleData]);

  useEffect(() => {
    let resColIds: any[] = [];
    if (resourceData && resourceData.length > 0) {
      resourceData.map((res: any) => {
        resColIds.push(res.additional_properties?.resource_column_details_id);
      });
      setResourceColIds(resColIds);
    }
  }, [resourceData]);
  useEffect(() => {
    let missingColumns: any[] = [];
    let uniqueColumns: any[] = [];
    let baseColumns: any[] =
      ruleData?.rule_schema?.merge_rule?.primary_dataset?.merge_on.map(
        (column: any) => ({ name: `Key_${column}`, value: `Key_${column}` })
      );
    if (resourceColumnsData && Array.isArray(resourceColumnsData)) {
      resourceColumnsData.forEach((resCol: any) => {
        const matchedRes: any = resourceData.find(
          (res: any) =>
            res?.additional_properties?.resource_column_details_id == resCol?.id
        );
        if (
          ruleData?.rule_schema?.merge_rule?.primary_dataset?.resource_id ==
          matchedRes?.id
        ) {
          uniqueColumns =
            ruleData?.rule_schema?.merge_rule?.primary_dataset?.merge_on;
        } else {
          const matchingResource =
            ruleData?.rule_schema?.merge_rule?.secondary_datasets.find(
              (rule: any) => rule.resource_id === matchedRes?.id
            );
          if (matchingResource) {
            uniqueColumns = matchingResource.merge_on;
          }
        }
        const missing = getMappedColumns(
          uniqueColumns,
          resCol?.resource_column_properties?.resource_columns,
          matchedRes ? matchedRes?.code : ""
        );

        missingColumns = [...missingColumns, ...missing];
      });
      if (
        baseColumns &&
        baseColumns.length > 0 &&
        missingColumns &&
        missingColumns.length > 0
      ) {
        const updatedBaseColumns = updateBaseColumns(
          baseColumns,
          missingColumns
        );
        // missingColumns = [...updatedBaseColumns, ...missingColumns];
        missingColumns = updatedBaseColumns;
      }
    } else {
      missingColumns = getMappedColumns(
        resourceColumnsData?.resource_column_properties?.unique_columns,
        resourceColumnsData?.resource_column_properties?.resource_columns,
        ""
      );
    }
    setColumns((prev: any) => ({
      ...prev,
      missingColumns: missingColumns,
      mismatchedColumns: missingColumns,
    }));
  }, [resourceColumnsData, resourceData]);

  const updateBaseColumns = (baseColumn: any[], missingColumns: any[]) => {
    // Function to extract the resource from the column name
    const getResource = (columnName: string) => {
      return columnName.split(".")[0];
    };

    // Function to normalize and extract the relevant part of the value
    const extractValue = (value: string) => {
      const parts = value.split(".");
      return parts.length > 1 ? parts[1] : parts[0];
    };

    // Group missingColumns by resource
    const resources = missingColumns.reduce((acc, item) => {
      const resource = getResource(item.name);
      if (!acc[resource]) acc[resource] = [];
      acc[resource].push(item);
      return acc;
    }, {});

    // Ensure each resource has the same number of objects
    const resourceKeys = Object.keys(resources);
    const numColumns = resources[resourceKeys[0]].length;

    const updatedBaseColumn = [...baseColumn];

    // Iterate over each index
    for (let i = 0; i < numColumns; i++) {
      const valuesAtIndex = resourceKeys.map((resource) => {
        return extractValue(resources[resource][i].value); // Extract the relevant part of the value
      });

      // Check if all values at this index match and are not null
      const allMatch = valuesAtIndex.every(
        (value) => value !== null && value === valuesAtIndex[0]
      );

      // Update baseColumn if all values match and none of them are null
      if (allMatch && valuesAtIndex[0] !== "null") {
        updatedBaseColumn[i].value = `Key_${valuesAtIndex[0]}`;
        updatedBaseColumn[i].name = `Key_${valuesAtIndex[0]}`;
      }
    }

    return updatedBaseColumn;
  };

  const getMappedColumns = (
    columns: string[],
    allColumns: any[],
    prefix: string
  ): { name: string; value: string }[] => {
    const result: { name: string; value: string }[] = [];
    if (columns) {
      for (const column of columns) {
        const matchedColumn =
          allColumns && allColumns.find((col) => col.column_name === column);

        if (matchedColumn) {
          result.push({
            name: `${prefix}.${matchedColumn.column_name}`,
            value:
              `${prefix}.${matchedColumn.domain_column}` ||
              `${prefix}.${matchedColumn.column_name}`,
          });
        }
      }
    }
    return result;
  };
  const handleSaveResearchQuery = (researchQueryPayload: any) => {
    const updatedResearchQueryData = researchQueryPayload.map(
      (researchQuery: any) => ({
        ...researchQuery,
        source: Array.isArray(researchQuery?.source)
          ? researchQuery.source
          : [researchQuery?.source],
        query: researchQuery?.updatedQuery ?? researchQuery.query,
      })
    );
    const payload = {
      code: `${ruleData?.id}_RQ`,
      rule_id: ruleData?.id,
      rule_code: ruleData?.code,
      research_queries: updatedResearchQueryData,
      variables: globalVariables,
    };
    if (
      researchQueryDetailData &&
      Object.keys(researchQueryDetailData).length > 0
    ) {
      updateResearchQuery({
        currentResearchQueryId: researchQueryDetailData.id,
        payload,
      })
        .then((response: any) => {
          if (response)
            showToast("Research Query updated successfully!", "success");
        })
        .catch((err: Error) => {
          console.error(err);
          showToast(`Cannot update Research Query`, "error");
        });
    } else {
      addResearchQuery(payload)
        .then(async (response: any) => {
          if (response) {
            const ruleId: any = currentRuleId;
            const result = await getResearchQueryDetail(ruleId);
            setResearchQueryDetailData(result);
            showToast("Research Query created successfully!", "success");
          }
        })
        .catch((error: any) => {
          showToast(`Cannot create Research Query`, "error");
        });
    }
  };
  const getLinkedServiceCodebyId = (id: number | string | null | undefined) => {
    const linkedData = linkedServicesData?.find((item: any) => item.id === id);
    return linkedData ? linkedData.name : null;
  };
  const getConnectionKeybyId = (id: number | string | null | undefined) => {
    const connectionData = fetchedConnectionKeys?.find(
      (item: any) => item.id === id
    );

    return connectionData ? connectionData?.name : null;
  };

  const researchColumns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      renderCell: (params: any) => <></>,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
    },
    {
      field: "id",
      headerName: "Id",
      minWidth: 50,
      // flex: 1,
      renderCell: (params: any) => <span>{params.value}</span>,
    },

    {
      field: "source",
      headerName: "Query Type",
      minWidth: 140,
      renderCell: (params: any) => {
        return (
          <LimitChractersWithTooltip
            value={
              params?.value[0]?.type === "Mixed"
                ? params?.value[0]?.type?.replace("_", " ")
                : params?.value?.type?.replace("_", " ")
            }
          />
        );
      },
    },
    {
      field: "name",
      headerName: "Query Name",
      minWidth: 250,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "query",
      headerName: "Query",
      minWidth: 250,
      // flex: 1,
      groupable: false,
      renderCell: (params: any) => {
        return (
          <>
            {params.value && params.value.length > 50 ? (
              <ReadMoreLess
                data={params?.value?.replace(/"/g, "")}
                charLimit={50}
              />
            ) : (
              params?.value?.replace(/"/g, "")
            )}
          </>
        );
      },
    },
    {
      field: "linked_service_id",
      headerName: "Linked Service",
      minWidth: 150,
      renderCell: (params: any) => {
        const linkedServiceCodebyId =
          getLinkedServiceCodebyId(params?.row?.source?.linked_service_id) ??
          "";

        return <LimitChractersWithTooltip value={linkedServiceCodebyId} />;
      },
    },
    {
      field: "connection_key_id",
      headerName: "Connection Key",
      minWidth: 150,
      renderCell: (params: any) => {
        const connectionKeybyId =
          getConnectionKeybyId(params?.row?.source?.connection_key_id) ?? "";

        return <LimitChractersWithTooltip value={connectionKeybyId} />;
      },
    },

    {
      field: "action",
      headerName: "Action",
      width: 130,
      align: "center",
      headerAlign: "center",
      groupable: false,
      renderCell: (params: any) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const handleEditQuery = () => {
          const formData: any = researchQueryData.find(
            (query) => query.id === params.row.id
          );
          const linkedService: any =
            linkedServicesData &&
            linkedServicesData?.find(
              (linked: any) => linked?.id == formData?.source?.linked_service_id
            );
          const connectionKey: any =
            fetchedConnectionKeys &&
            fetchedConnectionKeys?.find(
              (key: any) => key?.id == formData?.source?.connection_key_id
            );
          const updatedFormData: any = {
            ...formData,
            linked_service: linkedService,
            connection_key: connectionKey,
          };
          setEditFormData(updatedFormData);
          setQueryBuilderTempValue(formData?.query);
          setIsEditQuery(true);
          handleTransition(true);
          setQueryType(
            params?.row?.source[0]?.type === "Mixed"
              ? params?.row?.source[0]?.type
              : params?.row?.source?.type
          );
        };
        const handleDelete = async () => {
          const updatedQueryData = researchQueryData.filter(
            (query) => query.id !== params?.row?.id
          );
          const normalizedQueryData = updatedQueryData.map((query) => ({
            ...query,
            source: Array.isArray(query.source) ? query.source : [query.source],
          }));
          const payload = {
            code: `${ruleData?.id}_RQ`,
            rule_id: ruleData?.id,
            rule_code: ruleData?.code,
            research_queries: normalizedQueryData,
            variables: globalVariables,
          };
          updateResearchQuery({
            currentResearchQueryId: researchQueryDetailData.id,
            payload,
          })
            .then((response: any) => {
              if (response) {
                setResearchQueryData(updatedQueryData);
                showToast(
                  `${
                    params?.row?.name ?? "Research Query"
                  } deleted successfully!`,
                  "success"
                );
              }
            })
            .catch((err: Error) => {
              console.error("Error deleting research query:", err);
              showToast(`Cannot delete Research Query`, "error");
            });
        };

        return (
          <>
            <IconButton onClick={handleEditQuery}>
              <IconEditBase />
            </IconButton>
            <IconButton onClick={handleDelete}>
              <IconDeleteBlueSvg />
            </IconButton>
          </>
        );
      },
    },
  ];
  const handleSelectQueryType = (value: any) => {
    if (value) {
      setQueryType(value);
      if (value === "Resource" || value === "External" || value === "Mixed") {
        setQueryBuilderTempValue(`SELECT * FROM <> WHERE [] in `);
      } else if (value === "Merged_Data") {
        setQueryBuilderTempValue("SELECT * FROM <MERGED_DATA> WHERE [] in");
      }
    } else {
      setQueryType("");
    }
  };
  return (
    <>
      <Loader isLoading={isLoading} />
      {/* <Box className="dashboard-title-group mb-0">
        <Grid container rowSpacing={2.5} columnSpacing={4}>
          <Grid item xs sx={{ justifyContent: "flex-end", display: "flex" }}>
            <Button
              variant="contained"
              color="secondary"
              className="btn-orange"
              onClick={handleSaveResearchQuery}
            >
              <SaveOutlinedIcon /> &nbsp; Save Research Query
            </Button>
          </Grid>
        </Grid>
      </Box> */}
      <QueryCollapsible
        className="collapse-no-bdr"
        children={
          <Box
            className="text-box-card bottom-radius table-filter-bg p-0 mb-0"
            sx={{ marginTop: "18px" }}
          >
            <Box className="accordion-panel research-accordion">
              <Grid container>
                <Grid xl={12} lg={12} md={12} sm={12} xs={12}>
                  <Box className="querydata-container">
                    <Grid container columnSpacing={1.5} rowSpacing={2.5}>
                      <Grid item xl={6} lg={6} md={6}>
                        <Autocomplete
                          fullWidth
                          options={ruleQueriesType ?? []}
                          getOptionLabel={(option: any) =>
                            option ? `${option.replace("_", " ")} Queries` : ""
                          }
                          value={
                            ruleQueriesType?.find(
                              (option) => option == queryType
                            ) || null
                          }
                          renderInput={(params) => (
                            <TextField
                              label={
                                <span>
                                  Select Query Type
                                  <span className="required-asterisk">*</span>
                                </span>
                              }
                              name="queryType"
                              style={{ color: "#000000" }}
                              {...params}
                              placeholder="Select..."
                              InputLabelProps={{
                                shrink: true,
                              }}
                            />
                          )}
                          renderOption={(params: any, item: any) => (
                            <li
                              {...params}
                              key={item.key}
                              style={{
                                paddingTop: "2px",
                                paddingBottom: "2px",
                              }}
                            >
                              <ListItemText>
                                <span
                                  style={{ textTransform: "capitalize" }}
                                >{`${item.replace("_", " ")} Queries`}</span>
                              </ListItemText>
                            </li>
                          )}
                          loadingText="Loading..."
                          onChange={(e, value) => {
                            handleSelectQueryType(value);
                            setShowAggregatedColumns(undefined);
                          }}
                          className={`form-control-autocomplete`}
                        />
                      </Grid>
                      {queryType === "Resource" && (
                        <ResourceQueryTab
                          researchQueryData={researchQueryData}
                          setResearchQueryData={setResearchQueryData}
                          handleTransition={handleTransition}
                          checked={checked}
                          resourcesData={allResourcesData}
                          renderFrom="rule"
                          ruleData={ruleData}
                          columns={columns}
                          setColumns={setColumns}
                          isEditQuery={isEditQuery}
                          setIsEditQuery={setIsEditQuery}
                          editFormData={editFormData}
                          setEditFormData={setEditFormData}
                          setQueryType={setQueryType}
                          handleSaveResearchQuery={(researchQuery) =>
                            handleSaveResearchQuery(researchQuery)
                          }
                          setEditorValue={setQueryBuilderTempValue}
                        />
                      )}
                      {queryType === "External" && (
                        <ExternalQueryTab
                          researchQueryData={researchQueryData}
                          setResearchQueryData={setResearchQueryData}
                          handleTransition={handleTransition}
                          checked={checked}
                          columns={columns}
                          setColumns={setColumns}
                          isEditQuery={isEditQuery}
                          setIsEditQuery={setIsEditQuery}
                          editFormData={editFormData}
                          setEditFormData={setEditFormData}
                          setQueryType={setQueryType}
                          handleSaveResearchQuery={(researchQuery) =>
                            handleSaveResearchQuery(researchQuery)
                          }
                        />
                      )}
                      {queryType === "Merged_Data" && (
                        <MergeQueryTab
                          researchQueryData={researchQueryData}
                          setResearchQueryData={setResearchQueryData}
                          handleTransition={handleTransition}
                          checked={checked}
                          columns={columns}
                          setColumns={setColumns}
                          isEditQuery={isEditQuery}
                          setIsEditQuery={setIsEditQuery}
                          editFormData={editFormData}
                          setEditFormData={setEditFormData}
                          setQueryType={setQueryType}
                          handleSaveResearchQuery={(researchQuery) =>
                            handleSaveResearchQuery(researchQuery)
                          }
                        />
                      )}
                      {queryType === "Mixed" && (
                        <MixedQueryTab
                          resourcesData={allResourcesData}
                          columns={columns}
                          setColumns={setColumns}
                          isEditQuery={isEditQuery}
                          setIsEditQuery={setIsEditQuery}
                          researchQueryData={researchQueryData}
                          setResearchQueryData={setResearchQueryData}
                          handleTransition={handleTransition}
                          setQueryType={setQueryType}
                          editFormData={editFormData}
                          setEditFormData={setEditFormData}
                          handleSaveResearchQuery={(researchQuery) =>
                            handleSaveResearchQuery(researchQuery)
                          }
                        />
                      )}
                      {queryType === "" && (
                        <Grid
                          item
                          xs={12}
                          sm={12}
                          md={12}
                          lg={12}
                          xl={12}
                          sx={{
                            display: "flex",
                            columnGap: "8px",
                            textAlign: "right",
                            justifyContent: "flex-end",
                            alignSelf: "flex-end",
                          }}
                        >
                          <Button
                            variant="contained"
                            color="secondary"
                            className="btn-orange btn-dark"
                            onClick={() => {
                              setQueryType("");
                              handleTransition(false);
                            }}
                          >
                            Cancel
                          </Button>
                        </Grid>
                      )}
                    </Grid>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Box>
        }
        checked={checked}
      />
      <Box>
        <DataTable
          dataRows={researchQueryData}
          dataColumns={researchColumns}
          dataListTitle="Query List"
          className="dataTable hide-progress-icon no-buttons1"
          paginationMode="client"
          pageSizeOptions={[25]}
          handleTransition={handleTransition}
          showAddTxnButton={checked}
          singlePageMaxHeightDiff={307}
        />
      </Box>
    </>
  );
}
