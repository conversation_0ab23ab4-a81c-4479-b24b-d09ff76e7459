import { useEffect, useState } from "react";
import {
  Box,
  Button,
  Checkbox,
  Grid,
  IconButton,
  Stack,
  Tooltip,
} from "@mui/material";
import { IGetRulesListData } from "../../types/rules";
import { useToast } from "../../services/utils";
import useFetchDomains from "../../hooks/useFetchDomains";
import { useNavigate, useParams } from "react-router-dom";
import FlexBetween from "../../components/FlexBetween";
import RulesListDataTable from "../../components/DataGrids/RulesListDataGrid";
import { GridColDef } from "@mui/x-data-grid";
import AutoCompleteDomainList from "../../components/Molecules/Domain/AutoCompleteDomainList";
import ButtonComponent from "../../components/Button.Component";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import { deleteRule } from "../../services/rulesService";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import useFetchPaginatedRules from "../../hooks/useFetchPaginatedRules";
import { paginatedResponseFormat } from "../../services/constants";
import useFetchAllResources from "../../hooks/useFetchAllResources";
import {
  IconAudit,
  IconImportFileWhite,
  IconReportsSvg,
  IconExportIconBlue,
  IconResearchQuerySvg,
  IconEyeBase,
  IconEditBase,
  IconDeleteBlueSvg,
  IconExecuteBase,
} from "../../common/utils/icons";
import {
  downloadRuleExportFile,
  getCurrentDateAndLast7Days,
  getFormattedDateTime,
} from "../../services/utils";
import Loader from "../../components/Molecules/Loader/Loader";
import DropdownMenu from "../../components/Molecules/DropdownMenu/DropdownMenu";
import AssociateEntitiesDialog from "../../components/Dialogs/AssociateEntitiesDialog";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const RulesList = () => {
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  const navigate = useNavigate();
  const { showToast } = useToast();
  const { domainId } = useParams();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isResourceDataLoading, setIsResourceDataLoading] =
    useState<boolean>(false);
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [openAssociateEntitiesDialog, setOpenAssociateEntitiesDialog] =
    useState<boolean>(false);
  const [associateEntityId, setAssociateEntityId] = useState<number | null>(
    null
  );
  const handleAssociateEntitiesDialog = () => {
    setOpenAssociateEntitiesDialog(true);
  };

  const [domainsData] = useFetchDomains({ setIsLoading });
  const [
    domainAutocompleteInitialOptions,
    setDomainAutocompleteInitialOptions,
  ] = useState<{ label: string; value: string }[]>([]);
  const [domainAutocompleteOptions, setDomainAutocompleteOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [domainselected, setDomainselected] = useState<any>();
  const [currentDomainId, setCurrentDomainId] = useState<any>(
    Number(domainId) || ""
  );

  const [ruleslistData] = useFetchPaginatedRules({
    currentDomainId,
    setIsLoading,
    page,
    pSize,
  });

  const [resourcesData] = useFetchAllResources({
    currentDomainId,
    setIsLoading: setIsResourceDataLoading,
  });
  const [fileData, setFileData] = useState<any>(paginatedResponseFormat);
  const [isFileListModalOpen, setIsFileListModalOpen] = useState(false);
  const [fileListModalData, setFileListModalData] =
    useState<IGetRulesListData | null>(null);
  const [expandedRow, setExpandedRow] = useState<any>([]);

  useEffect(() => {
    const new_options = domainsData.map((domainItem: any) => {
      return {
        label: domainItem.domain_name,
        value: `${domainItem.id}`,
      };
    });
    setDomainAutocompleteOptions(new_options);
    setDomainAutocompleteInitialOptions(new_options);
  }, [domainsData]);

  useEffect(() => {
    setDomainselected(
      domainAutocompleteInitialOptions.find((dacio) => dacio.value === domainId)
    );
  }, [domainAutocompleteInitialOptions]);

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
      renderCell: (params: any) => (
        <IconButton
          size="small"
          tabIndex={-1}
          onClick={() => {
            if (expandedRow.includes(params.row.id)) {
              setExpandedRow((prev: any) =>
                prev.filter((item: any) => item !== params.row.id)
              );
            } else {
              setExpandedRow((prev: any) => [...prev, params.row.id]);
            }
          }}
        >
          <ExpandMoreIcon
            sx={{
              transform: `rotateZ(${
                expandedRow.includes(params.row.id) ? 180 : 0
              }deg)`,
              transition: (theme) =>
                theme.transitions.create("transform", {
                  duration: theme.transitions.duration.shortest,
                }),
            }}
            fontSize="inherit"
          />
        </IconButton>
      ),
    },
    {
      field: "id",
      headerName: "Id",
      minWidth: 70,
      renderCell: (params: any) => {
        return <span style={{ cursor: "pointer" }}>{params.id}</span>;
      },
    },
    {
      field: "name",
      headerName: "Rules",
      minWidth: 150,
      flex: 1,
      renderCell: (params) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "domain_name",
      headerName: "Domain",
      minWidth: 140,
      flex: 1,
      renderCell: (params) => (
        <LimitChractersWithTooltip value={params?.value} />
      ),
    },
    {
      field: "resources",
      headerName: "Resources",
      minWidth: 180,
      flex: 1,
      renderCell: (params) => {
        const resourceNames =
          params.row.rule_schema?.resources?.map((rItem: number) => {
            const resourceMatched = resourcesData.find(
              (resourceItem: any) => resourceItem.id === rItem
            );
            return resourceMatched?.resource_name;
          }) || [];
        const resourceString = resourceNames.join(", ");
        return (
          <>
            {resourceNames.length > 0 && (
              <LimitChractersWithTooltip value={resourceString} />
            )}
          </>
        );
      },
    },
    {
      field: "description",
      headerName: "Description",
      flex: 1,
      minWidth: 180,
      renderCell: (params) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "updated_on",
      headerName: "Last Modified",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return getFormattedDateTime(params.value);
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 160,
      renderCell: (params: any) => {
        const handleDelete = async () => {
          setFileData((prev: any) => ({
            ...prev,
            items: prev?.items?.filter(
              (prevItem: any) => prevItem.id !== params.row.id
            ),
          }));

          await deleteRule(params.row.id);
          showToast(`${params.row.name} deleted successfully`, "success");
        };
        const { last7DaysDate } = getCurrentDateAndLast7Days();
        return (
          <>
            <Tooltip title="Rule Execution" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  navigate(
                    `/rules/${domainId}/rule-execution/${params.row.id}`
                  );
                }}
              >
                <IconExecuteBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="View Rule" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  navigate(`/rules/${domainId}/view/${params.row.id}`);
                }}
              >
                <IconEyeBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit Rule" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  navigate(`/rules/${domainId}/edit/${params.row.id}`);
                }}
              >
                <IconEditBase />
              </IconButton>
            </Tooltip>
            <DropdownMenu
              dropdownMenuItems={[
                <IconButton
                  onClick={() => {
                    navigate(
                      `/rules-execution-history?ruleId=${params.row.id}&fromDate=${last7DaysDate}`
                    );
                  }}
                  size="small"
                  className="datagrid-action-btn"
                >
                  <IconReportsSvg />
                  Recent comparisons
                </IconButton>,

                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    navigate(`/rules/${domainId}/auditRule/${params.row.id}`)
                  }
                >
                  <IconAudit />
                  Audit Rule
                </IconButton>,
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    navigate(
                      `/rules/${domainId}/researchQuery/${params.row.id}`
                    )
                  }
                >
                  <IconResearchQuerySvg />
                  Research Query
                </IconButton>,
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => {
                    setAssociateEntityId(params.row.id);
                    handleAssociateEntitiesDialog();
                  }}
                >
                  <IconExportIconBlue />
                  Export
                </IconButton>,
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleDelete()}
                >
                  <IconDeleteBlueSvg />
                  Delete Rule
                </IconButton>,
              ]}
            />
          </>
        );
      },
    },
  ];

  const showFileListModal = (row: IGetRulesListData) => {
    setFileListModalData(row);
    setIsFileListModalOpen(true);
  };

  const handelOnChangeDomain = (event: any, value: any) => {
    if (value?.id !== undefined) {
      setCurrentDomainId(value?.id);
      setDomainselected(value?.id);
      navigate(`/rules-list/${value?.id}`);
      setPage(defaultPage ? parseInt(defaultPage) : 1);
      setPSize(defaultPageSize ? parseInt(defaultPageSize) : 25);
    } else {
      setFileData(paginatedResponseFormat);
      setCurrentDomainId(null);
      navigate(`/rules-list/all`);
    }
  };
  const handelClickEvent = () => {
    navigate("/rules/add");
  };

  // for update rule list data
  useEffect(() => {
    if (ruleslistData) {
      // setIsLoading(true);
      setFileData(ruleslistData);
      // setIsLoading(false);
    }
  }, [ruleslistData]);

  return (
    <Box>
      {(isLoading || isDownloadLoading || isResourceDataLoading) && (
        <Loader
          isLoading={isLoading || isDownloadLoading || isResourceDataLoading}
        />
      )}
      {/* import  button component */}

      {/* import autocomplete list of domain */}
      <Box className="text-box-card list-page-card compact-text-box-card">
        <Grid container rowSpacing={2.5} columnSpacing={4}>
          <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
            <AutoCompleteDomainList
              currentDomainId={currentDomainId}
              setIsLoading={setIsLoading}
              handelOnChangeDomain={handelOnChangeDomain}
              className="form-control-autocomplete"
            />
          </Grid>
          <Grid item xs display={"flex"} justifyContent={"flex-end"}>
            <Stack>
              <label className="label-text">&nbsp;</label>
              <Box sx={{ display: "flex", columnGap: 2 }}>
                {/* <Button
                  onClick={() =>
                    navigate(`/rules/import-entity?type=rule`, {
                      state: {
                        import_defination_name: "rule",
                      },
                    })
                  }
                  className="btn-orange btn-dark"
                  sx={{ columnGap: 1 }}
                >
                  <IconImportFileWhite />
                  Import
                </Button> */}
                <ButtonComponent
                  handelClickEvent={handelClickEvent}
                  className="btn-orange"
                >
                  <AddSharpIcon sx={{ marginRight: "4px" }} /> Rule
                </ButtonComponent>
              </Box>
            </Stack>
          </Grid>
        </Grid>
      </Box>
      {/* import data table for resource list  */}
      <FlexBetween style={{ paddingTop: "20px" }}>
        <RulesListDataTable
          dataRows={fileData?.items || []}
          dataColumns={columns}
          loading={isLoading}
          dataListTitle="Rules List"
          className="dataTable no-radius pt-0 bdr-top-0 hide-progress-icon"
          disableColumnFilter={true}
          paginationMode="server"
          rowCount={fileData?.total || 0}
          pageSizeOptions={[25]}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={293}
        />
      </FlexBetween>
      <AssociateEntitiesDialog
        openAssociateEntitiesDialog={openAssociateEntitiesDialog}
        setOpenAssociateEntitiesDialog={setOpenAssociateEntitiesDialog}
        associateEntityId={associateEntityId}
        downloadExportFile={downloadRuleExportFile}
        setIsDownloadLoading={setIsDownloadLoading}
        header={"Rule"}
      />
    </Box>
  );
};

export default RulesList;
