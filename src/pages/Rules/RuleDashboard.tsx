import React, { useEffect, useState } from "react";
import { LABELS } from "../../services/constants/Rules";

import {
  GridColumnGroupingModel,
  GridAlignment,
} from "@mui/x-data-grid-premium";

import PieChartBox, { ColorBox } from "../../components/Rules/PieChartBox";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
  Tab,
  Tabs,
  Tooltip,
  Button,
  Typography,
} from "@mui/material";
import RulesListDataTable from "../../components/DataGrids/RulesListDataGrid";
import { pieData } from "../../services/constants/results";
import {
  RemoveTextAndSanatizedCode,
  formattedJson,
  getDateFromTimestamp,
  getFormattedTime,
  getSanatizedCode,
  getTimeInHHMMSS,
} from "../../services/utils";
import {
  IconWatchSvg,
  IconCheckCircleIconGreenSvg,
  IconCrossCircleIconSvg,
  IconFalsePositive,
  IconReRunWhite,
} from "../../common/utils/icons";
import { GridExpandMoreIcon } from "@mui/x-data-grid";
import InfoIcon from "@mui/icons-material/Info";
import CustomAccordion from "../../components/Molecules/Accordian/CustomAccordion";
import ResourceSupportDocuments from "../../components/Organisms/ResourceSupportDocuments";
import { Link } from "@mui/material";
import AdhocQueryDashboardTab from "../../components/Rules/AdhocQueryDashboardTab";
import RootCauseAnalysis from "../../components/Rules/RootCauseAnalysis";
import Incidents from "../../components/Rules/Incidents";
import Loader from "../../components/Molecules/Loader/Loader";
import { useNavigate } from "react-router-dom";

const RuleDashboard = ({
  dashboardData,
  isLoading,
  setIsLoading,
  isBackdropLoading,
  currentRuleResultId,
  currentRuleIncidentData,
  setIsTriggereBtnPressed,
  setIsCommentBtnPressed,
}: any) => {
  const navigate = useNavigate();
  const [selectedTab, setSelectedTab] = useState<any>("missingRecords");
  const [columnsData, setColumnsData] = useState<any>({});
  const [rowsData, setRowsData] = useState<any>({});
  const [missingColumns, setMissingColumns] = useState<any>([]);
  const [mismatchedColumns, setMismatchedColumns] = useState<any>([]);
  const [missingCounts, setMissingCounts] = useState({
    keyCount: 0,
    resourceCount: 0,
  });

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
    setMismatchedColumns([]);
    setMissingColumns([]);
  };

  const getFormattedKeysPairs = (
    inputString: string | undefined,
    type?: "missing"
  ) => {
    const pairs: any = {};

    // Check if inputString is defined and is a string
    if (inputString && typeof inputString === "string") {
      inputString.split(",").forEach((pair) => {
        if (pair.trim() !== "None") {
          const [key, value] = pair.trim().split("=");
          pairs[key] = getSanatizedCode(value);
        }
      });
    }

    if (type === "missing") {
      const count = Object.keys(pairs).length;
      setMissingCounts((prev) => ({
        ...prev,
        keyCount: count,
      }));
    }

    return pairs;
  };

  const getFormattedKeysPairs2 = (inputString: string, type?: "missing") => {
    const pairs: any = {};

    if (inputString && typeof inputString === "string") {
      inputString.split(",").forEach((pair) => {
        const [key, value] = pair.trim().split("=");

        if (key && value !== undefined) {
          pairs[key] = getSanatizedCode(value, ";");
        }
      });
    }

    if (type === "missing") {
      const count = Object.keys(pairs).length;
      setMissingCounts((prev) => ({
        ...prev,
        keyCount: count,
      }));
    }

    return pairs;
  };

  const getFormattedKeysPairs1 = (
    missing_in: any,
    false_positive_missings: any,
    resource_prefixes: any,
    type?: "missing"
  ) => {
    const pairs: any = {};
    if (!missing_in || !resource_prefixes) return {};

    resource_prefixes.forEach((prefix: string) => {
      pairs[prefix] = missing_in.includes(prefix)
        ? false_positive_missings[prefix]
          ? "falsepositive"
          : "falsenegative"
        : "truepositive";
    });
    if (type === "missing") {
      setMissingCounts((prev) => ({
        ...prev,
        resourceCount: resource_prefixes.length,
      }));
    }
    return pairs;
  };
  const reverseFormattedKeysPairs = (inputObject: any) => {
    const desiredOutput = Object.entries(inputObject)
      .filter(([key]) => key !== "id")
      .map(([key, value]) => `${key}=${value}`)
      .join(", ");
    return desiredOutput;
  };

  const handelRowData = (comparison_rule_exec_details: any) => {
    const rows: any = [];
    const checkNames: any = [];

    comparison_rule_exec_details?.forEach((item: any) => {
      checkNames.push(item?.check_name);
      item?.errors?.row_details?.forEach((rowItem: any, idx: any) => {
        const row: any = {
          id: idx,
          ...getFormattedKeysPairs(rowItem?.keys),
          ...getFormattedKeysPairs2(rowItem?.file_names),
        };
        rows.push(row);
      });
    });
    let updatedRows: any = [];
    let newRow: any = {};

    comparison_rule_exec_details?.forEach((item: any) => {
      item?.errors?.row_details?.forEach((rowItem: any) => {
        const keysTrimmed = rowItem.keys.replace(/\s+/g, "");
        const fileNameTrimmed = rowItem.file_names;

        const mergedKeyData = keysTrimmed + "," + fileNameTrimmed;
        const row = rows.find((row: any) => {
          const rowKeysTrimmed = reverseFormattedKeysPairs(row).replace(
            /\s+/g,
            ""
          );
          const sanitizedMergedKeyData = mergedKeyData
            .replace(/['"]+/g, "")
            .replace(/\s+/g, "")
            .replace(/,None/g, "")
            .replace(/,undefined/g, "")
            .replace(/;/g, "");
          const sanitizedRowKeysTrimmed = rowKeysTrimmed
            .replace(/['"]+/g, "")
            .replace(/\s+/g, "")
            .replace(/<ul.*?>|<\/ul>|<li.*?>|<\/li>|<div.*?>|<\/div>/g, "");

          return (
            getSanatizedCode(sanitizedMergedKeyData) ===
            getSanatizedCode(sanitizedRowKeysTrimmed)
          );
        });
        newRow = {
          ...row,
          [item?.check_name]: rowItem?.column_values || "",
        };
        updatedRows.push(newRow);
      });
    });
    const commonKeys =
      updatedRows[0] &&
      Object.keys(updatedRows[0]).filter(
        (key) => !checkNames.includes(key) && key !== "id"
      );

    function mergeObjectsByKey(arr: any, keys: any) {
      const mergedMap = new Map();

      arr.forEach((obj: any) => {
        const key = keys.map((k: any) => obj[k]).join("-");
        if (mergedMap.has(key)) {
          Object.assign(mergedMap.get(key), obj);
        } else {
          mergedMap.set(key, { ...obj });
        }
      });

      return Array.from(mergedMap.values());
    }
    const mergedData = mergeObjectsByKey(updatedRows, commonKeys);
    const processedRows: any = processRows(mergedData);
    setRowsData((prev: any) => ({
      ...prev,
      mismatched:
        processedRows &&
        processedRows.map((item: any, idx: any) => ({
          ...item,
          id: idx,
        })),
    }));
  };

  const getColumns = (comparison_rule_exec_details: any) => {
    const firstColumns: any = Object.keys(
      getFormattedKeysPairs(
        comparison_rule_exec_details[0]?.errors?.row_details[0]?.keys
      )
    );
    const secondColumns: any = comparison_rule_exec_details?.map(
      (item: any) => item?.check_name
    );
    const fileColumns: any = Object.keys(
      getFormattedKeysPairs(
        comparison_rule_exec_details[0]?.errors?.row_details[0]?.file_names
      )
    );
    const columnFirst: any = [...firstColumns].map((item: any) => {
      return {
        field: item,
        headerName: getSanatizedCode(item),
        flex: 1,
        minWidth: 110,
        renderCell: (params: any) => {
          return (
            <div className="long-string-width">
              {RemoveTextAndSanatizedCode(params.value, "Mismatched_", "")}
            </div>
          );
        },
      };
    });
    const columnSecond: any = [...secondColumns].map((item: any) => {
      return {
        field: item,
        headerName: getSanatizedCode(item),
        flex: 1,
        minWidth: 320,
        renderCell: (params: any) => {
          return (
            <div className="long-string-width">
              {RemoveTextAndSanatizedCode(params.value, "Mismatched_", "")}
            </div>
          );
        },
      };
    });
    const columnFile: any = [...fileColumns].map((item: any) => {
      return {
        field: item,
        headerName: getSanatizedCode(item),
        flex: 1,
        minWidth: 240,
        // renderCell: (params: any) => {
        //   return (
        //     <div className="long-string-width">
        //       {RemoveTextAndSanatizedCode(params.value, "Mismatched_", "")}
        //     </div>
        //   );
        // },
        renderCell: (params: any) => (
          <div
            className="data-grid-cell"
            dangerouslySetInnerHTML={{ __html: params.value }}
          />
        ),
      };
    });

    setColumnsData((prev: any) => ({
      ...prev,
      mismatched: [...columnFirst, ...columnSecond, ...columnFile],
    }));
  };
  const columnGroupingModel: GridColumnGroupingModel = [
    {
      groupId: "Keys",
      headerName: "Keys",
      headerAlign: "center",
      children: [
        { field: "id" },
        ...(dashboardData?.additional_properties?.missing_records_details
          ?.length > 0
          ? Object.keys(
              getFormattedKeysPairs(
                dashboardData.additional_properties.missing_records_details[0]
                  ?.keys
              )
            ).map((key) => ({
              field: key,
            }))
          : []),
      ],
    },
    {
      groupId: "Resources",
      headerName: "Resources",
      headerAlign: "center",
      children: dashboardData?.additional_properties?.resource_prefixes.map(
        (key: any) => ({
          field: key,
        })
      ),
    },
    {
      groupId: "Messages",
      headerName: "Messages",
      headerAlign: "center",
      children: [{ field: "message" }],
    },
    ...(dashboardData?.additional_properties?.missing_records_details?.length >
      0 &&
    dashboardData.additional_properties.missing_records_details[0]?.file_names
      ? [
          {
            groupId: "file_name",
            headerName: "File Names",
            headerAlign: "center" as GridAlignment,
            children: Object.keys(
              getFormattedKeysPairs(
                dashboardData.additional_properties.missing_records_details[0]
                  ?.file_names
              )
            ).map((key) => ({
              field: key,
            })),
          },
        ]
      : []),
  ];

  const getMissingColumns = (
    missing_records_details: any,
    resource_prefixes?: any
  ) => {
    const resourceKeys = resource_prefixes ? resource_prefixes : [];

    const columns: any = Object.keys(
      getFormattedKeysPairs(missing_records_details[0]?.keys)
    ).map((item: any) => {
      return {
        field: item,
        headerName: getSanatizedCode(item),
        flex: 1,
        minWidth: 100,
        headerAlign: "center",
        align: "center",
        renderCell: (params: any) => {
          const regex = /;(?![\w\s])/g;
          const fileNames = Object.keys(params.row)
            .filter((key) => key.includes("_file_name"))
            .map((key) => params.row[key])
            .filter((fileName) => fileName);

          const fileNamesDisplay =
            fileNames.length > 0 ? (
              <div>
                <strong>File name(s):</strong>
                <br />
                {fileNames.map((fileName, index) => {
                  const tempElement = document.createElement("div");
                  tempElement.innerHTML = fileName;
                  const fileNamesText =
                    tempElement.textContent || tempElement.innerText;
                  const individualFileNames =
                    fileNamesText.split(/\.xlsx|\.xls/);
                  return individualFileNames.map((fileName, subIndex) => {
                    const trimmedFileName = fileName.trim();
                    if (trimmedFileName) {
                      return (
                        <li key={`${index}-${subIndex}`}>
                          {trimmedFileName}.xlsx
                        </li>
                      );
                    }
                    return null;
                  });
                })}
              </div>
            ) : null;
          return (
            <span className="text-ellipsis">
              <Tooltip
                title={
                  <Typography sx={{ fontSize: "0.875rem" }}>
                    <ul className="pl-20">{fileNamesDisplay}</ul>
                  </Typography>
                }
                placement="top"
                className="tooltip-ellipsis"
              >
                {params.value.replace(regex, "")}
              </Tooltip>
            </span>
          );
        },
      };
    });

    const resource_prifix_columns: any = resourceKeys.map((item: any) => {
      return {
        field: item,
        headerName: item,
        headerAlign: "center",
        align: "center",
        flex: 1,
        renderCell: (params: any) => {
          // return params.row[item] ? (
          //   <IconCheckCircleIconGreenSvg width={21} height={21} />
          // ) : (
          //   <IconCrossCircleIconSvg width={21} height={21} />
          // );

          if (params.row[item] == "truepositive") {
            return <IconCheckCircleIconGreenSvg width={21} height={21} />;
          }
          if (params.row[item] == "falsepositive") {
            return (
              <>
                <IconFalsePositive />
              </>
            );
          }
          if (params.row[item] == "falsenegative") {
            return <IconCrossCircleIconSvg width={21} height={21} />;
          }
        },
      };
    });
    setColumnsData((prev: any) => ({
      ...prev,
      missingRecords: [
        ...columns,
        ...resource_prifix_columns,
        {
          field: "message",
          headerName: "Message",
          flex: 1,
          minWidth: 350,
          headerAlign: "center",
          renderCell: (params: any) => {
            const regex = /;(?![\w\s])/g;
            return (
              <span className="text-ellipsis">
                <Tooltip
                  title={
                    <Typography sx={{ fontSize: "0.875rem" }}>
                      {params.value.replace(regex, "")}
                    </Typography>
                  }
                  placement="top"
                  className="tooltip-ellipsis"
                >
                  {params.value.replace(regex, "")}
                </Tooltip>
              </span>
            );
          },
        },
      ],
    }));
  };

  const processRows = (rows: any[]) => {
    if (!rows) return;
    return rows.map((row: any) => {
      const updatedRow = { ...row };

      Object.keys(updatedRow).forEach((key) => {
        if (typeof updatedRow[key] === "string") {
          if (
            /\.0$/.test(updatedRow[key]) &&
            !/\.0[1-9]/.test(updatedRow[key])
          ) {
            updatedRow[key] = updatedRow[key].slice(0, -2);
          }
        }
      });

      return updatedRow;
    });
  };

  const getMissingRows = (
    missing_records_details: any,
    resource_prefixes?: any
  ) => {
    const rows: any = missing_records_details?.map((item: any, idx: any) => {
      return {
        id: idx,
        ...getFormattedKeysPairs(item?.keys, "missing"),
        ...getFormattedKeysPairs1(
          item?.missing_in,
          item?.false_positive_missings,
          resource_prefixes,
          "missing"
        ),
        message: item?.message,
        ...getFormattedKeysPairs2(item?.file_names, "missing"),
      };
    });
    const processedRows = processRows(rows);
    setRowsData((prev: any) => ({
      ...prev,
      missingRecords: processedRows,
    }));
  };

  useEffect(() => {
    const {
      comparison_rule_exec_details,
      missing_records_details,
      resource_prefixes,
    } = dashboardData?.additional_properties || {};
    // mismatched records
    if (comparison_rule_exec_details?.length) {
      getColumns(comparison_rule_exec_details);
      handelRowData(comparison_rule_exec_details);
    }
    // missing records
    if (missing_records_details && missing_records_details?.length > 0) {
      if (typeof missing_records_details[0] === "object") {
        if (resource_prefixes) {
          getMissingColumns(missing_records_details, resource_prefixes);
          getMissingRows(missing_records_details, resource_prefixes);
        } else {
          getMissingColumns(missing_records_details);
          getMissingRows(missing_records_details);
        }
      }
    }
  }, [dashboardData, selectedTab]);

  useEffect(() => {
    const handleHashNavigation = () => {
      const hash = window.location.hash;
      if (hash) {
        const element = document.querySelector(hash);
        if (element) {
          const addBoxShadow = setTimeout(() => {
            element.classList.add("box-shadow10");
          }, 1000);

          element.scrollIntoView({ behavior: "smooth" });

          const removeBoxShadow = setTimeout(() => {
            element.classList.remove("box-shadow10");
          }, 3000);

          return () => {
            clearTimeout(addBoxShadow);
            clearTimeout(removeBoxShadow);
          };
        }
      }
    };
    handleHashNavigation();
  }, [dashboardData]);

  const tabContent: any = {
    mismatched: (
      <>
        <MismatchedRecords
          rows={rowsData?.mismatched || []}
          columns={columnsData?.mismatched || []}
          isLoading={isLoading}
          setMismatchedColumns={setMismatchedColumns}
        />
        {dashboardData?.comparison_research_query_results &&
          dashboardData?.comparison_research_query_results != null && (
            <RootCauseAnalysis
              selectedColumns={mismatchedColumns}
              isLoading={isLoading}
              heading={"Research"}
              dashboardData={dashboardData}
            />
          )}
      </>
    ),
    missingRecords: (
      <>
        <div className="datatable-parent-container">
          <MissingRecords
            rows={rowsData?.missingRecords || []}
            columns={columnsData?.missingRecords || []}
            isLoading={isLoading}
            columnGroupingModel={columnGroupingModel}
            setMissingColumns={setMissingColumns}
          />
        </div>
        {dashboardData?.comparison_research_query_results &&
          dashboardData?.comparison_research_query_results != null && (
            <RootCauseAnalysis
              selectedColumns={missingColumns}
              isLoading={isLoading}
              heading={"Research"}
              dashboardData={dashboardData}
            />
          )}
      </>
    ),

    adhocQuery: (
      <Box className="adhock-query-accordion-wrapper">
        {<AdhocQueryDashboardTab dashboardData={dashboardData || []} />}
      </Box>
    ),
  };
  const isValidationExecDetails =
    dashboardData?.additional_properties?.validation_exec_details !== null &&
    dashboardData?.additional_properties?.validation_exec_details.length > 0
      ? true
      : false;
  const supportDocumentsPath =
    dashboardData?.additional_properties?.supporting_documents;

  const reqPath =
    dashboardData?.complete_execution_params?.rule_execution_request;
  const additionalInfoForAllResources =
    reqPath?.additional_info_for_all_resources;
  const resourceNames =
    dashboardData?.additional_properties?.validation_exec_details;

  const formattedData = reqPath?.request_body?.resource_data.map(
    (item: any, index: any) => {
      const {
        file_name,
        linked_service_code,
        connection_key,
        resource_path,
        resource_id,
        file_names = [],
        ...otherProps
      } = item || {};
      const fileNames = additionalInfoForAllResources.find(
        (additionalItem: any) => additionalItem?.resource_id === resource_id
      )?.file_names;
      const resource_name = resourceNames.find(
        (rsItem: any) => rsItem?.resource_id === resource_id
      )?.resource_name;
      return (
        <tr key={index}>
          <td>
            {resource_name} ({item?.resource_id})
          </td>
          <td>
            <div className="file-scroll">
              <div className="break-word w-240">
                {Array.isArray(fileNames) && fileNames.length > 0
                  ? fileNames.map((fileName: any, index: number) => (
                      <Box className="file-name-box" key={index}>
                        {fileName}
                      </Box>
                    ))
                  : "N/A"}
              </div>
            </div>
          </td>
          <td>
            {item?.linked_service_code ? item?.linked_service_code : "N/A"}
          </td>
          <td>{item?.connection_key ? item?.connection_key : "N/A"}</td>
          <td>
            <div className="break-word w-240">
              {item.resource_path
                ? item.resource_path.replace(/"/g, "")
                : "N/A"}
            </div>
          </td>
          <td>
            <span className="position-relative">
              <Tooltip
                componentsProps={{
                  tooltip: { className: "wide-tooltip w-380" },
                }}
                title={
                  <pre
                    style={{
                      whiteSpace: "pre-wrap",
                      margin: 0,
                      maxHeight: "200px",
                      overflowY: "auto",
                    }}
                  >
                    <React.Fragment>
                      <Typography color="inherit">
                        Additional Resource Info
                      </Typography>
                      <Typography>
                        {formattedJson(JSON.stringify(otherProps))}
                      </Typography>
                    </React.Fragment>
                  </pre>
                }
              >
                <InfoIcon
                  sx={{
                    position: "absolute",
                    top: "50%",
                    transform: "translateY(-50%)",
                    right: "-24px",
                    width: "16px",
                    cursor: "pointer",
                  }}
                />
              </Tooltip>
            </span>
          </td>
        </tr>
      );
    }
  );

  const getReportPath = (path: string) => {
    if (path) {
      if (path.startsWith("\\")) {
        path = path.substring(1);
      }

      const startIndex = path.indexOf("download-files");

      let relativePath = path.substring(
        startIndex + "download-files".length + 1
      );

      relativePath = relativePath.replace(/\\/g, "/");
      const parts = relativePath.split("/");
      const fileName = parts[parts.length - 1];
      const downloadLink = `${process.env.REACT_APP_REPORT_DOWNLOAD_PATH}${relativePath}`;
      return { downloadLink, fileName };
    }
  };

  const ruleVariables =
    reqPath?.request_body?.inline_variables?.rule_variables || {};
  const resourceVariables =
    reqPath?.request_body?.inline_variables?.resource_variables || [];
  const researchQueryVariables =
    reqPath?.request_body?.inline_variables
      ?.comparison_research_query_variables || [];
  return (
    <>
      <Loader isLoading={isBackdropLoading} />
      <Box className="dashboard-title-group">
        <div className="heading">
          <strong> Execution name:</strong>
          {reqPath?.request_body?.execution_name}
        </div>
        <div className="right-column">
          <div className="rerun-btn">
            <Button
              className="btn-orange btn-dark btn-sm plus-btn-sm"
              onClick={() =>
                navigate(
                  `/rules/${dashboardData?.domain_id}/re-run-rule-execution/${dashboardData?.rule_id}?re-run=true&execution-id=${dashboardData?.id}&run-id=${dashboardData?.run_id}&run-name=${dashboardData?.run_name}`
                )
              }
            >
              <IconReRunWhite /> &nbsp; ReRun
            </Button>
          </div>
          <div className="watch-info">
            {dashboardData?.additional_properties?.total_time && (
              <IconWatchSvg />
            )}

            {dashboardData?.additional_properties?.total_time &&
              getTimeInHHMMSS(dashboardData?.additional_properties?.total_time)}
          </div>
          <Box className="timestamp-info">
            {dashboardData?.execution_time &&
              `${getDateFromTimestamp(
                dashboardData?.execution_time
              )} ${getFormattedTime(dashboardData?.execution_time)}`}
          </Box>

          {dashboardData && (
            <div
              className={`badge ${
                dashboardData?.is_success ? "success" : "failed"
              }`}
            >
              {dashboardData?.is_success ? "Success" : "Failed"}
            </div>
          )}
        </div>
      </Box>
      <Grid container rowSpacing={2} columnSpacing={2} marginBottom="20px">
        {isValidationExecDetails ? (
          dashboardData?.additional_properties?.validation_exec_details?.map(
            (item: any, index: any) => {
              let duplicateRecords =
                item?.validation_result?.additional_properties
                  .total_duplicate_records || 0;
              const localPieData = { ...pieData }; // Create a new object

              localPieData.resource_name = item.resource_name;
              localPieData.resource_id = item.resource_id;
              localPieData.domain_id = item?.validation_result?.domain_id;
              localPieData.total_records = item.total_records_count;
              localPieData.total_common_records = item.total_common_records;
              localPieData.total_mismatched_records =
                item.total_mismatched_records;
              localPieData.total_missing_records = item.missing_records_count;
              localPieData.total_duplicate_records = duplicateRecords;
              localPieData.total_unique_records = item.unique_records;
              localPieData.filtered_records_by_resource_filters =
                item?.validation_result?.additional_properties
                  ?.filtered_records_by_resource_filters || 0;
              localPieData.total_matched_records =
                dashboardData?.additional_properties?.total_common_records -
                dashboardData?.additional_properties?.total_mismatched_records;
              localPieData.false_positive_missing_records_count =
                item?.false_positive_missing_records_count || 0;
              localPieData.filtered_records_by_rule_filters =
                item.filtered_records_by_rule_filters || 0;
              localPieData.countsData = {
                match:
                  dashboardData?.additional_properties?.total_common_records -
                  dashboardData?.additional_properties
                    ?.total_mismatched_records,
                mismatch:
                  dashboardData?.additional_properties
                    ?.total_mismatched_records,
                missing: item.missing_records_count,
                duplicate: duplicateRecords,
                total: item.total_records_count || 0,
              };
              localPieData.key = { index };

              return (
                <Grid item xs={12} sm={12} md={6} lg={6} xl={4} key={index}>
                  {/* Add a unique key for each component */}
                  <PieChartBox
                    key={index}
                    className="rule-domain-chart-box"
                    dataType="SQL"
                    pieData={localPieData}
                    domainId={dashboardData?.domain_id}
                  />
                </Grid>
              );
            }
          )
        ) : (
          <>
            <Grid item xs={12} sm={12} md={6} lg={4}>
              <PieChartBox
                className="rule-domain-chart-box"
                dataType="SQL"
                pieData={pieData}
              />
            </Grid>
          </>
        )}
      </Grid>
      <div className="rule-domain-chart-legends">
        {LABELS.map((label, index) => {
          return (
            <span key={index}>
              <ColorBox color={label.color} />
              {label.label}
            </span>
          );
        })}
      </div>
      <Incidents
        currentResultId={currentRuleResultId || ""}
        currentIncidentData={currentRuleIncidentData ?? []}
        setIsLoading={setIsLoading}
        setIsTriggereBtnPressed={setIsTriggereBtnPressed}
        setIsCommentBtnPressed={setIsCommentBtnPressed}
      />
      {reqPath?.request_body && (
        <Box className="accordion-panel" sx={{ marginBottom: "8px" }}>
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
              expandIcon={<GridExpandMoreIcon />}
            >
              <h4>Supporting Documents </h4>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <CustomAccordion
                expandId="panel2d-header-support-documents"
                title="Rule Execution Report"
                isEnabled={true}
                topMargin={1}
              >
                <table className="custom-table">
                  <tbody>
                    <tr>
                      <th className="w-180">Report Type </th>
                      <th>Report Location </th>
                    </tr>

                    <tr>
                      <td width="180">Rule Execution Report</td>
                      <td>
                        {dashboardData?.additional_properties
                          ?.supporting_documents?.rule_execution_report ? (
                          <Box className="word-break-all row-hr">
                            <Link
                              sx={{ color: "black" }}
                              href={
                                getReportPath(
                                  supportDocumentsPath?.rule_execution_report
                                )?.downloadLink
                              }
                            >
                              {
                                getReportPath(
                                  supportDocumentsPath?.rule_execution_report
                                )?.fileName
                              }
                            </Link>
                          </Box>
                        ) : (
                          "N/A"
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td width="180">
                        Comparison Research Query Result Reports
                      </td>
                      <td>
                        {supportDocumentsPath
                          ?.comparison_research_query_result_reports?.length > 0
                          ? supportDocumentsPath.comparison_research_query_result_reports.map(
                              (researchItem: any, index: any) => {
                                const reportPath = getReportPath(researchItem);
                                return (
                                  <Box
                                    key={researchItem}
                                    className="word-break-all row-hr"
                                  >
                                    <Link
                                      sx={{ color: "black" }}
                                      href={reportPath?.downloadLink}
                                    >
                                      {reportPath?.fileName}
                                    </Link>
                                  </Box>
                                );
                              }
                            )
                          : "N/A"}
                      </td>
                    </tr>
                    <tr>
                      <td>Filter Records Report</td>
                      <td>
                        {supportDocumentsPath?.filter_records_report != null &&
                        supportDocumentsPath?.filter_records_report.length > 0
                          ? supportDocumentsPath?.filter_records_report.map(
                              (filterItem: string, index: number) => {
                                return (
                                  <Box className="word-break-all  row-hr">
                                    <Link
                                      key={index} // Provide a unique key for each mapped element
                                      sx={{ color: "black" }}
                                      href={
                                        getReportPath(filterItem)?.downloadLink
                                      }
                                    >
                                      {getReportPath(filterItem)?.fileName}
                                    </Link>
                                  </Box>
                                );
                              }
                            )
                          : "N/A"}
                      </td>
                    </tr>
                    <tr>
                      <td>Adhoc Query Reports</td>
                      <td>
                        {supportDocumentsPath?.adhoc_query_result_reports !=
                        null
                          ? supportDocumentsPath.adhoc_query_result_reports.filter(
                              (filterItem: any) => filterItem != null
                            ).length > 0
                            ? supportDocumentsPath.adhoc_query_result_reports
                                .filter((filterItem: any) => filterItem != null) // Filter again for rendering
                                .map((filterItem: string, index: number) => {
                                  const reportPath = getReportPath(filterItem);
                                  return (
                                    <Box
                                      key={index}
                                      className="word-break-all row-hr"
                                    >
                                      <Link
                                        sx={{ color: "black" }}
                                        href={reportPath?.downloadLink}
                                      >
                                        {reportPath?.fileName}
                                      </Link>
                                    </Box>
                                  );
                                })
                            : "N/A"
                          : "N/A"}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </CustomAccordion>

              {isValidationExecDetails &&
                dashboardData?.additional_properties?.validation_exec_details?.map(
                  (item: any, index: any) => {
                    const supportingDocuments =
                      item?.validation_result?.additional_properties
                        ?.supporting_documents;
                    return (
                      supportingDocuments && (
                        <ResourceSupportDocuments
                          key={index}
                          title={`${item?.resource_name}(${item?.resource_id})`}
                          documents={supportingDocuments}
                        />
                      )
                    );
                  }
                )}
            </AccordionDetails>
          </Accordion>
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
              expandIcon={<GridExpandMoreIcon />}
            >
              <h4>Comparison Parameters </h4>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <table className="custom-table">
                <tbody>
                  <tr>
                    <th>Resource Name (Resource ID)</th>
                    <th>File Name</th>
                    <th>Linked Service Code</th>
                    <th>Connection Key</th>
                    <th>Resource Location</th>
                    <th>Additional Resource Info</th>
                  </tr>
                  {formattedData}
                </tbody>
              </table>
            </AccordionDetails>
          </Accordion>
        </Box>
      )}
      {((dashboardData &&
        reqPath?.request_body &&
        reqPath?.request_body?.inline_variables &&
        Object.keys(ruleVariables).length > 0) ||
        resourceVariables.length > 0 ||
        Object.keys(researchQueryVariables).length > 0) && (
        <Box
          className="accordion-panel"
          sx={{ marginBottom: "8px", marginTop: "8px" }}
        >
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<GridExpandMoreIcon />}
            >
              Variables
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <Grid container spacing={2.5}>
                {resourceVariables.length > 0 && (
                  <Grid item xs>
                    <h3 className="m-0 mb-2">{"Resource Variables"}</h3>
                    {resourceVariables.map((item: any) => {
                      const resourceName =
                        dashboardData?.rule_exec_parameters?.resource_details.find(
                          (resItem: any) =>
                            resItem.resource_id === item?.resource_id
                        )?.resource_name;
                      return (
                        <div
                          key={item?.resource_id}
                          className="text-box-card box-card-variables full-radius"
                        >
                          <h3>{resourceName}</h3>

                          <table className="inline-variables-table">
                            <tbody>
                              {item?.resource_vars &&
                                Object.entries(item?.resource_vars).length >
                                  0 &&
                                Object.entries(item?.resource_vars).map(
                                  ([key, value]: any) => (
                                    <tr key={`${item.resource_id}_${key}`}>
                                      <td>
                                        <div className="inner-column">
                                          <div className="label">
                                            <strong>{key}</strong>
                                            <span className="required-asterisk">
                                              *
                                            </span>
                                            :
                                          </div>
                                          <input
                                            className={`form-control-1 read-only`}
                                            value={value}
                                            name={`${key}_${item.resource_id}`}
                                            title={`inlineVariables[${key}_${item.resource_id}]`}
                                            readOnly
                                          />
                                        </div>
                                      </td>
                                    </tr>
                                  )
                                )}
                            </tbody>
                          </table>
                        </div>
                      );
                    })}
                  </Grid>
                )}
                {Object.keys(ruleVariables).length > 0 && (
                  <Grid item xs>
                    <h3 className="m-0 mb-2">{"Rule Variables"}</h3>

                    <table className="inline-variables-table">
                      <tbody>
                        {Object.keys(ruleVariables).map(
                          (key: string, index: number) => {
                            return (
                              <tr key={index}>
                                <td>
                                  <div className="inner-column">
                                    <div className="label">
                                      <strong>{key}</strong>
                                      <span className="required-asterisk">
                                        *
                                      </span>
                                      :
                                    </div>
                                    <input
                                      className="form-control-1 read-only"
                                      value={ruleVariables[key]}
                                      name={key}
                                      title={ruleVariables[key]}
                                      readOnly
                                    />
                                  </div>
                                </td>
                              </tr>
                            );
                          }
                        )}
                      </tbody>
                    </table>
                  </Grid>
                )}

                {Object.keys(researchQueryVariables).length > 0 && (
                  <Grid item xs>
                    <h3 className="m-0 mb-2">{"Research Query Variables"}</h3>

                    <table className="inline-variables-table">
                      <tbody>
                        {Object.keys(researchQueryVariables).map(
                          (key: string, index: number) => {
                            return (
                              <tr key={index}>
                                <td>
                                  <div className="inner-column">
                                    <div className="label">
                                      <strong>{key}</strong>
                                      <span className="required-asterisk">
                                        *
                                      </span>
                                      :
                                    </div>
                                    <input
                                      className="form-control-1 read-only"
                                      value={researchQueryVariables[key]}
                                      name={key}
                                      title={researchQueryVariables[key]}
                                      readOnly
                                    />
                                  </div>
                                </td>
                              </tr>
                            );
                          }
                        )}
                      </tbody>
                    </table>
                  </Grid>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Box>
      )}

      {
        <>
          <Box className="mui-tabs-title mt-8">Results</Box>
          <Tabs
            value={selectedTab}
            onChange={handleChange}
            textColor="secondary"
            indicatorColor="secondary"
            className="mui-tabs no-t-lr-radius min-height-0 alternative-1"
          >
            <Tab value="missingRecords" label="Missing Records" />
            <Tab value="mismatched" label="Mismatched Rows" />
            <Tab value="adhocQuery" label="Adhoc Query" />
            {/* <Tab value="rootCauseAnalysis" label="Root Cause Analysis" /> */}
          </Tabs>

          <Box>{tabContent[selectedTab]}</Box>
        </>
      }
    </>
  );
};

export default RuleDashboard;

const MismatchedRecords = ({
  rows,
  columns,
  isLoading,
  setMismatchedColumns,
}: any) => {
  return (
    <RulesListDataTable
      dataRows={rows}
      dataColumns={columns}
      loading={isLoading}
      dataListTitle="Rules Dashboard"
      className="dataTable no-radius pt-0 bdr-top-0"
      disableColumnFilter={false}
      tableHeight={180}
      setResultColumns={setMismatchedColumns}
      checkboxSelection={rows.length > 0 ? true : false}
      singlePageMaxHeightDiff={362}
    />
  );
};

const MissingRecords = ({
  rows,
  columns,
  isLoading,
  columnGroupingModel,
  setMissingColumns,
}: any) => {
  return (
    <RulesListDataTable
      dataRows={rows}
      dataColumns={columns}
      loading={isLoading}
      dataListTitle="Rules Dashboard"
      className="dataTable no-radius pt-0 bdr-top-0"
      disableColumnFilter={true}
      tableHeight={180}
      disableColumnReorder={true}
      columnGroupingModel={columnGroupingModel}
      setResultColumns={setMissingColumns}
      checkboxSelection={rows.length > 0 ? true : false}
      singlePageMaxHeightDiff={362}
    />
  );
};
