import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  MenuItem,
  Button,
  Grid,
  TextField,
  Autocomplete,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Chip,
  Select,
} from "@mui/material";
import Backdrop from "@mui/material/Backdrop";
import { useToast } from "../../services/utils";
import CircularProgress from "@mui/material/CircularProgress";
import { useNavigate, useParams } from "react-router-dom";
import useFetchResources from "../../hooks/useFetchResources";
import { updateRule } from "../../services/rulesService";
import { CheckCircleRounded } from "@mui/icons-material";
import AutoCompleteDomainList from "../../components/Molecules/Domain/AutoCompleteDomainList";
import RulesDataTable from "../../components/DataGrids/RulesDataGrid";
import useFetchDomainById from "../../hooks/useFetchDomainById";
import useFetchRuleById from "../../hooks/useFetchRuleById";
import useFetchResourceColumnsByDomain from "../../hooks/useFetchResourceColumnsByDomain";
import AdhocQueries from "./AdhocQueries";
import ResourceFilter from "../../components/Rules/ResourceFilter";
import CustomComparisionTab from "../../components/Rules/CustomComparisionTab";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import CancelIcon from "@mui/icons-material/Cancel";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import CreateIcon from "@mui/icons-material/Create";
import { mergeType } from "../../services/constants";
import {
  IconAddTolranceDataSvg,
  IconCloseSvgBlue,
} from "../../common/utils/icons";
import ToleranceDialog from "../../components/Dialogs/ToleranceDialog";
import { addEditRuleSchema } from "../../schemas";
import { Popper } from "@mui/base";
import ConfirmationDialog from "../../components/Dialogs/ConfirmationDialog";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useRuleContext } from "../../contexts/RuleContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import TagsDialog from "../../components/Dialogs/TagsDialog";
import { combineArrays } from "../../services/utils";
import CommentBeforeUpdateDialog from "../../components/Dialogs/CommentBeforeUpdateDialog";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import ViewVariables from "../../components/Molecules/Resource/ViewVariables";
import SecondaryMergeResource from "../../components/Molecules/Rule/SecondaryMergeResource";
import Loader from "../../components/Molecules/Loader/Loader";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

interface PossibleMergeOnColumns {
  [key: string]: string[] | null;
}

interface Dataset {
  resource_id: number;
  merge_on: string[];
  possible_merge_on_columns: PossibleMergeOnColumns;
  merge_type?: string;
}

interface InputData {
  primary_dataset: Dataset;
  secondary_datasets: Dataset[];
}

interface OutputData {
  key: string;
  values: string[];
  id?: number;
}

const EditRule: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  //ruleContext imports
  const {
    customFilters,
    setCustomFilters,
    selectedResourceIds,
    setSelectedResourceIds,
    customComparisonRules,
    setCustomComparisonRules,
    selectedResourceColumns,
    setSelectedResourceColumns,
    toleranceData,
    setToleranceData,
  } = useRuleContext();
  const {
    availColumns,
    setAvailColumns,
    availColumnsWithResourceDetail,
    setAvailColumnsWithResourceDetail,
    globalVariables,
    setGlobalVariables,
  } = useRuleResourceContext();
  const navigate = useNavigate();
  const { showToast } = useToast();
  //states
  const [activeTab, setActiveTab] = useState<string>("domainComparisonRule");
  const [currentDomainId, setCurrentDomainId] = useState<string | number>(0);
  const [domainId, setDomainId] = useState<string | number>(0);
  const [resultData, setResultData] = useState<any>({
    name: "",
    description: "",
    domain_id: null,
    domain_name: "",
    rule_schema: {},
  });
  const [initialFormData, setInitialFormData] = useState<any>({
    basicForm: {
      name: "",
      description: "",
      domain_id: null,
      domain_name: "",
    },
    rule_schema: {},
  });
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [selectedMergeType, setSelectedMergeType] = useState<any>([]);
  const [resIds, setResIds] = useState<any>([]);
  const [columns, setColumns] = useState<any[]>([]);
  const [tableRows, setTableRows] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [domainComparisonRules, setDomainComparisonRules] = useState<any[]>([]);
  const [resourceIds, setResourceIds] = useState<any[]>([]);
  const [finalRule, setFinalRule] = useState<any[]>([]);
  const [isLoaded, setIsLoaded] = useState<boolean>(true);
  const [isSelecting, setIsSelecting] = useState<boolean>(false);
  const [selectedDomainData, setSelectedDomainData] = useState<any>(null);
  const [domainRuleArr, setDomainRuleArr] = useState<any>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [openResourceConfirmation, setOpenResourceConfirmation] =
    useState(false);
  const [resourceDataObj, setResourceDataObj] = useState<any>([]);
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [adhocQueryData, setAdhocQueryData] = useState<any>([]);
  const [sampleAdhocQuery, setSampleAdhocQuery] = useState<any>("");
  const [selectResourceValue, setSelectResourceValue] = useState<any>([]);
  const [secondaryMergeResource, setSecondaryMergeResource] = useState<any>([]);

  //hooks
  const [resourcesData] = useFetchResources({ currentDomainId, setIsLoading });
  const [resourcesColumnData] = useFetchResourceColumnsByDomain({
    currentDomainId: domainId,
    setIsLoading,
  });
  const { id: currentRuleId } = useParams();
  const [ruleData] = useFetchRuleById({ setIsLoading, currentRuleId });
  const [domainData] = useFetchDomainById({
    setIsLoading,
    currentDomainId: domainId,
  });
  const cdmData: string[] = [];
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [selectedRowId, setSelectedRowId] = useState<any>(null);
  const [openToleranceDialog, setOpenToleranceDialog] =
    useState<boolean>(false);
  const [showTagsDialog, setShowTagsDialog] = useState<any>({});
  const [selectedTags, setSelectedTags] = useState<any>({
    key: "",
    values: [],
  });
  const [tagColResult, setTagColResult] = useState<any>([
    {
      key: "",
      values: [],
    },
  ]);
  const [possibleMergeColumns, setPossibleMergeColumns] = useState<any>([]);
  const [tagId, setTagId] = useState("");
  const [openCommentConfirmation, setOpenCommentConfirmation] =
    useState<boolean>(false);

  useEffect(() => {
    const resData: any[] = [];

    // Check if resourceIds and resourcesData are available
    if (resourceIds && resourceIds.length > 0 && resourcesData) {
      resourceIds.forEach((resId: any) => {
        const resourceData = resourcesData.find(
          (resource: any) => resource.id === resId
        );
        if (resourceData) {
          resData.push(resourceData);
        }
      });
    }

    // Construct the query string
    const query = `SELECT * FROM <TABLE_NAME> WHERE [${
      resData.length > 0 ? resData[0].code : ""
    }${
      availColumns && availColumns.length > 0 && resData.length > 0
        ? `.${availColumns[0]}`
        : ""
    }] <> [${resData.length > 1 ? resData[1].code : ""}${
      resData.length > 1 && availColumns && availColumns.length > 1
        ? `.${availColumns[1]}`
        : resData.length > 1 && availColumns && availColumns.length > 0
        ? `.${availColumns[0]}`
        : ""
    }]`;

    setSampleAdhocQuery(query);
  }, [resourceIds, resourcesData, availColumns]);

  const handleDefaultData = async () => {
    if (ruleData && Object.keys(ruleData)?.length > 0) {
      let adhocQueriesWithId;
      let ruleDataIds;
      let secondaryMergeResource: any = [];
      let resourceColumnFilteredResources: any = [];
      let transformedData: any = [];
      setGlobalVariables(ruleData?.rule_schema?.inline_variables || {});
      setAllVariablesList((prev: any) => ({
        ...prev,
        rule_Variables: ruleData?.rule_schema?.inline_variables ?? {},
      }));
      secondaryMergeResource = [
        {
          parent_id:
            ruleData?.rule_schema?.merge_rule?.primary_dataset?.resource_id,
          ...ruleData?.rule_schema?.merge_rule?.primary_dataset
            ?.secondary_merge_resource,
        },
        ...(ruleData?.rule_schema?.merge_rule?.secondary_datasets || []).map(
          (item: any) => ({
            parent_id: item?.resource_id,
            ...item?.secondary_merge_resource,
          })
        ),
      ].filter(Boolean);

      setSecondaryMergeResource(secondaryMergeResource);
      setResultData((prev: any) => ruleData);
      setCurrentDomainId((prev: any) => ruleData?.domain_id);
      const customComparisonRules = ruleData?.rule_schema
        ?.custom_comparison_rules
        ? ruleData?.rule_schema?.custom_comparison_rules?.map(
            (item: any, idx: any) => {
              if (!item) {
                return null;
              }

              const {
                left_operand,
                right_operand,
                comparison_type,
                filter_rules,
                ...rest
              } = item;

              // Add id to each object in the filter_rules array
              const updatedFilterRules =
                filter_rules?.map((filterItem: any, filterIdx: any) => ({
                  ...filterItem,
                  id: Math.random(),
                })) || [];

              return {
                ...rest,
                id: idx,
                resource: {
                  left_operand,
                  right_operand,
                  comparison_type,
                },
                filter_rules: updatedFilterRules,
              };
            }
          )
        : [];
      setCustomComparisonRules(customComparisonRules);

      const newCustomFilters =
        ruleData?.rule_schema?.filter_rules?.length > 0
          ? ruleData?.rule_schema?.filter_rules.map((item: any, index: any) => {
              return {
                key: index,
                ...item,
                id: index,
              };
            })
          : [];
      setCustomFilters(newCustomFilters);
      const uniqueRIds = new Set<number>();
      const mergeRuleData = [
        ruleData?.rule_schema?.merge_rule?.primary_dataset,
        ...ruleData?.rule_schema?.merge_rule?.secondary_datasets,
      ];

      mergeRuleData.forEach((item: any) => {
        uniqueRIds.add(item.resource_id);
      });

      const uniqueObjects: any = Array.from(uniqueRIds).map((rId) => {
        const item = mergeRuleData.find(
          (item: any) => item.resource_id === rId
        );

        return {
          rId: rId,
          columns: item?.merge_on || [],
          merge_type: item?.merge_type,
          resource_code: item?.resource_code,
          possible_merge_on_columns: item?.possible_merge_on_columns,
        };
      });
      setSelectedResourceColumns(uniqueObjects);

      //Default data for selectedMergeType

      const intial_selected_merge_type = uniqueObjects.map((item: any) => {
        const { columns, ...rest } = item;
        return rest;
      });

      const primaryResourceId =
        ruleData?.rule_schema?.merge_rule?.primary_dataset?.resource_id;
      const secondaryResourceIds =
        ruleData?.rule_schema?.merge_rule?.secondary_datasets?.map(
          (dataset: any) => dataset.resource_id
        ) || [];

      if (
        resourcesData &&
        resourcesData?.length > 0 &&
        domainData &&
        Object.keys(domainData)?.length > 0
      ) {
        const resourcePromises = resourcesData.reduce(
          (acc: any, item: any) => {
            if (item.id === primaryResourceId) {
              acc.primary = {
                ...item,
                rId: item.id,
                rCode: item.code,
                ...(resourcesColumnData.find(
                  (resItem: any) =>
                    resItem.id ===
                    item?.additional_properties?.resource_column_details_id
                ) || {}),
              };
            }
            return acc;
          },
          { primary: {}, secondary: [] }
        );
        secondaryResourceIds.forEach((secondaryId: any) => {
          const item = resourcesData.find(
            (resItem: any) => resItem.id === secondaryId
          );
          if (item) {
            resourcePromises.secondary.push({
              ...item,
              rId: item.id,
              rCode: item.code,
              ...(resourcesColumnData.find(
                (resItem: any) =>
                  resItem.id ===
                  item?.additional_properties?.resource_column_details_id
              ) || {}),
            });
          }
        });

        const filteredResources = [
          resourcePromises.primary,
          ...resourcePromises.secondary,
        ].filter((item) => item !== null);

        const resourceColumnFilteredResources = filteredResources.filter(
          (item) => item?.resource_column_properties != null
        );
        ruleDataIds = resourceColumnFilteredResources.map(
          (item: any) => item.rId
        );
        setResourceIds(ruleDataIds);
        if (resourceColumnFilteredResources?.length > 0) {
          setSelectedResourceIds(
            (prev: any) => resourceColumnFilteredResources
          );
          setSelectResourceValue(
            (prev: any) => resourceColumnFilteredResources
          );
          setResIds((prev: any) => resourceColumnFilteredResources);
          setSelectedDomainData((prev: any) => domainData);
          setIsLoaded(false);
        }

        if (
          secondaryMergeResource.length > 0 &&
          resourceColumnFilteredResources.length > 0
        ) {
          secondaryMergeResource = secondaryMergeResource.map(
            (secItem: any) => ({
              ...secItem,
              resourceColumnDetails: resourceColumnFilteredResources.find(
                (item: any) => item.rId === secItem.parent_id
              ),
            })
          );
        }
      }
      if (ruleData?.rule_schema?.adhoc_queries) {
        adhocQueriesWithId = ruleData?.rule_schema?.adhoc_queries.map(
          (adhocQuerie: any) => ({
            ...adhocQuerie,
            id: Math.random(),
          })
        );
        setAdhocQueryData(adhocQueriesWithId || []);
      }
      if (ruleData?.rule_schema?.merge_rule) {
        transformedData = transformData(ruleData?.rule_schema?.merge_rule);
        setTagColResult(transformedData);
      }

      //domainComparison Start
      let domainComparisonData: any = [];

      if (domainData?.domain_properties?.columns?.length > 0) {
        const tableRowsData: any = [];
        let resourcesNames: any = [];

        const uniqueColumnsSet = new Set();

        uniqueObjects.forEach((res: any) => {
          res.columns.forEach((col: any) => {
            uniqueColumnsSet.add(col);
          });
        });

        // Convert the set back to an array
        const uniqueColumns = Array.from(uniqueColumnsSet);

        domainData?.domain_properties?.columns.forEach(
          (item: any, idx: any) => {
            const tableRow: any = {
              id: idx + 1,
              domain_name: item?.name,
            };
            resourceColumnFilteredResources.forEach((resourceItem: any) => {
              if (!resourceItem) return;
              resourcesNames.push(resourceItem.resource_name);
              const resourceColumnItem =
                resourceItem.resource_column_properties?.resource_columns.find(
                  (resourceColumnItem: any) =>
                    resourceColumnItem.domain_column === item?.name &&
                    resourceColumnItem?.is_active
                );
              tableRow[resourceItem.resource_name] =
                resourceColumnItem?.column_name;
              tableRow["column_name"] = resourceColumnItem?.column_name;
            });
            tableRowsData.push(tableRow);
          }
        );

        const uniqueResourcesNames: any[] = [];
        for (const name of resourcesNames) {
          if (!uniqueResourcesNames.includes(name)) {
            uniqueResourcesNames.push(name);
          }
        }
        resourcesNames = uniqueResourcesNames;

        const result = tableRowsData.filter((tableRowItem: any) => {
          const objectValues = Object.values(tableRowItem);
          return resourcesNames.every(
            (resourceName: any) =>
              tableRowItem[resourceName] !== undefined &&
              !objectValues.some((value) => uniqueColumns.includes(value))
          );
        });

        const domainRules = result.map((item: any, idx: any) => {
          return {
            id: item?.id,
            name: item?.domain_name,
            column_name: item?.domain_name,
          };
        });
        const filteredDomainRules = domainRules.filter(
          (rule: { name: any }) => {
            const matchingRule =
              ruleData?.rule_schema?.domain_comparison_rules &&
              ruleData?.rule_schema?.domain_comparison_rules.find(
                (comparisonRule: { name: any }) =>
                  comparisonRule.name === rule.name
              );
            return matchingRule !== undefined;
          }
        );
        const filteredDomainRulesWithRowId = filteredDomainRules.map(
          (rule: any, index: number) => ({
            ...rule,
            rowId: index + 1,
          })
        );
        const hasDomainComparisonRules =
          ruleData?.rule_schema?.domain_comparison_rules &&
          ruleData?.rule_schema?.domain_comparison_rules?.length > 0;
        hasDomainComparisonRules &&
          (domainComparisonData = filteredDomainRulesWithRowId);
        domainComparisonData = domainComparisonData.map(
          (item: { name: any }) => {
            const matchingRule =
              hasDomainComparisonRules &&
              ruleData?.rule_schema?.domain_comparison_rules?.find(
                (ruleItem: any) => ruleItem.name === item?.name
              );

            if (matchingRule) {
              return {
                ...item,
                tolerance_type: matchingRule.tolerance_type,
                tolerance_value: matchingRule.tolerance_value,
                fallback_value: matchingRule.fallback_value,
                comparison_type: matchingRule.comparison_type,
              };
            }
            return item;
          }
        );
      }
      ////domainComparison End

      // Set initial form data after all manipulations are done
      setInitialFormData({
        basicForm: {
          name: ruleData.name,
          description: ruleData.description,
          domain_id: ruleData.domain_id,
          domain_name: ruleData.domain_name,
        },
        rule_schema: {
          ...ruleData.rule_schema,
          filter_rules:
            ruleData?.rule_schema?.filter_rules?.length > 0
              ? ruleData?.rule_schema?.filter_rules.map(
                  (item: any, index: any) => {
                    return {
                      key: index,
                      ...item,
                      id: index,
                    };
                  }
                )
              : [],
          custom_comparison_rules: customComparisonRules,
          merge_rule: {
            ...ruleData.rule_schema?.merge_rule,
            primary_dataset: {
              ...ruleData.rule_schema?.merge_rule?.primary_dataset,
              merge_on: uniqueObjects,
            },
          },
          inline_variables: ruleData?.rule_schema?.inline_variables || {},
          adhoc_queries: adhocQueriesWithId,
          resourceIds: ruleDataIds,
          select_resource_column: uniqueObjects,
        },
        secondaryMergeResource: secondaryMergeResource,
        domainComparisonRules: domainComparisonData,
        tag_col_result: transformedData,
        intial_selected_merge_type: intial_selected_merge_type,
      });
    }
  };

  // Remove the old useEffect for initialFormData since we're setting it in handleDefaultData
  useEffect(() => {
    handleDefaultData();
  }, [ruleData, resourcesData, domainData, resourcesColumnData]);

  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: ruleData?.name,
      id: ruleData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [ruleData]);

  useEffect(() => {
    const basicDetails = {
      name: resultData?.name,
      description: resultData?.description,
      domain_id: resultData?.domain_id,
      domain_name: resultData?.domain_name,
    };
    const hasFormChanges =
      // Basic form fields
      JSON.stringify(basicDetails) !==
        JSON.stringify(initialFormData?.basicForm) ||
      // Custom filters
      JSON.stringify(customFilters) !==
        JSON.stringify(initialFormData.rule_schema?.filter_rules) ||
      // Custom comparison rules
      JSON.stringify(customComparisonRules) !==
        JSON.stringify(initialFormData.rule_schema?.custom_comparison_rules) ||
      // Adhoc queries
      JSON.stringify(adhocQueryData) !==
        JSON.stringify(initialFormData.rule_schema?.adhoc_queries) ||
      // Selected resource columns
      JSON.stringify(selectedResourceColumns) !==
        JSON.stringify(initialFormData.rule_schema?.select_resource_column) ||
      //Secondary merge resource
      JSON.stringify(secondaryMergeResource) !==
        JSON.stringify(initialFormData?.secondaryMergeResource) ||
      // Domain comparison rules
      JSON.stringify(domainComparisonRules) !==
        JSON.stringify(initialFormData?.domainComparisonRules) ||
      // Resource IDs
      JSON.stringify(resourceIds) !==
        JSON.stringify(initialFormData?.rule_schema?.resourceIds) ||
      // selectedMergeType
      JSON.stringify(selectedMergeType) !==
        JSON.stringify(initialFormData?.intial_selected_merge_type) ||
      // tagColResult
      JSON.stringify(tagColResult) !==
        JSON.stringify(initialFormData?.tag_col_result);

    setHasChanges(hasFormChanges);
  }, [
    resultData,
    customFilters,
    customComparisonRules,
    selectedResourceColumns,
    adhocQueryData,
    domainComparisonRules,
    selectedMergeType,
    secondaryMergeResource,
    resourceIds,
    initialFormData,
    tagColResult,
  ]);

  const transformData = (inputData: InputData): OutputData[] => {
    const output: OutputData[] = [{ key: "", values: [] }];

    const processDataset = (dataset: Dataset) => {
      if (dataset && dataset.possible_merge_on_columns) {
        for (const [key, values] of Object.entries(
          dataset.possible_merge_on_columns
        )) {
          const uniqueValues = values
            ? Array.from(
                new Set([
                  ...values,
                  // ...dataset.merge_on.filter((col) => col !== key),
                ])
              )
            : [];
          output.push({
            key,
            values: uniqueValues,
            id: dataset.resource_id,
          });
        }
      }
    };

    processDataset(inputData.primary_dataset);
    inputData.secondary_datasets.forEach((dataset) => processDataset(dataset));

    return output;
  };

  useEffect(() => {
    if (
      selectedResourceIds &&
      selectedResourceIds.length > 0 &&
      selectedResourceColumns &&
      selectedResourceColumns.length > 0
    ) {
      const matchingIds = selectedResourceIds
        .filter((resource: any) => {
          const selectedResource = selectedResourceColumns.find(
            (selected: any) => selected.rId === resource.rId
          );
          if (
            selectedResource &&
            resource?.resource_column_properties?.resource_columns
          ) {
            return (
              selectedResource.columns.length ===
              resource.resource_column_properties.resource_columns.length
            );
          }
          return false;
        })
        .map((resource: any) => resource.rId);

      if (
        matchingIds &&
        matchingIds.length > 0 &&
        customComparisonRules &&
        customComparisonRules.length > 0
      ) {
        const updatedComparisonRules = customComparisonRules.filter(
          (rule: any) => {
            // Check if rule.resource exists before accessing its properties
            if (
              rule.resource &&
              rule.resource.left_operand &&
              rule.resource.right_operand
            ) {
              // Check if updatedColumnsData contains any empty columns and if so, remove matching rules
              return matchingIds.every((id: any) => {
                // Filter out the rules where resource_id matches the id in updatedColumnsData
                return (
                  rule.resource.left_operand.resource_id !== id &&
                  rule.resource.right_operand.resource_id !== id
                );
              });
            } else {
              // If rule.resource is undefined or doesn't have required properties, keep the rule
              return true;
            }
          }
        );
        // showToast(`"Since all columns are unique, the custom comparison for this resource has been removed."`, "warning");
        setCustomComparisonRules(updatedComparisonRules);
      }
    }
    setSecondaryMergeResource((prev: any) => {
      if (
        secondaryMergeResource.length === 0 ||
        selectedResourceIds.length === 0
      ) {
        return prev;
      }

      const updated = secondaryMergeResource.map((secItem: any) => {
        const columnData = selectedResourceIds.find((item: any) => {
          return item.rId === secItem.parent_id;
        });

        return { ...secItem, resourceColumnDetails: columnData };
      });
      return updated;
    });
  }, [selectedResourceColumns, selectedResourceIds]);

  useEffect(() => {
    if (resourcesData === undefined) {
      navigate("/rules-list/all");
    } else {
      setDomainId(currentDomainId);
    }
  }, [resourcesData]);
  /**
   * Set current domain id & Update result data state
   * @param data
   */
  const handelChangeDomain = (dataObj: any, data: any) => {
    setSelectedDomainData(data);
    setCurrentDomainId(data?.id);
    setResultData({
      ...resultData,
      domain_id: data?.id,
      domain_name: data?.domain_name,
    });
  };

  useEffect(() => {
    selectedDomainData?.domain_properties?.columns.map((domData: any) => {
      return cdmData.push(domData?.name.trim());
    });
    setAvailColumns(cdmData);
    setAvailColumnsWithResourceDetail(null);
  }, [selectedDomainData]);

  /**
   * On change rule name update result data with new name
   * @param e
   */
  const handleChangeRule = (e: any) => {
    setResultData({ ...resultData, [e.target.name]: e.target.value });
    validateField(e.target.name, e.target.value);
  };
  const handleChangeDescription = (e: any) => {
    setResultData({ ...resultData, description: e.target.value });
  };

  /**
   * When we select resource
   * Filter resource using id
   * set selected resources in a state
   * set all resource ids in a state
   * @param value
   */
  const onSelectResources = async (dataObj: any) => {
    const validIds = dataObj.map((item: any) => item.id);
    const filteredMergeResource = secondaryMergeResource.filter((item: any) =>
      validIds.includes(item.parent_id)
    );
    if (dataObj.length < selectResourceValue.length) {
      setSecondaryMergeResource(filteredMergeResource);
    } else {
      const existingParentIds = new Set(
        filteredMergeResource.map((item: any) => item.parent_id)
      );
      const missingParentIds = validIds.filter(
        (id: any) => !existingParentIds.has(id)
      );
      if (missingParentIds.length > 0) {
        setSecondaryMergeResource((prevState: any) => [
          ...prevState,
          ...missingParentIds.map((id: any) => ({ parent_id: id })),
        ]);
      }
    }

    if (dataObj?.length === 0) {
      setOpenResourceConfirmation(true);
      setResourceDataObj([]);
      setGlobalVariables(allVariablesList?.rule_Variables || {});
      return;
    }
    setSelectResourceValue((prevValues: any[]) => {
      const validPrevValues = prevValues.filter((item: any) =>
        dataObj.some((dataItem: any) => dataItem === item)
      );
      const newlySelected = dataObj.filter(
        (item: any) => !validPrevValues.includes(item)
      );
      const newState = [...validPrevValues, ...newlySelected];
      processResources(newState);
      return newState;
    });

    //why I am changing this because I am not getting plus button while I am adding new resource
  };

  const processResources = async (updatedSelectResourceValue: any[]) => {
    try {
      setIsLoaded(true);
      const resourcePromises = updatedSelectResourceValue.map(
        async (item: any) => {
          const rId = item.id;
          const rCode = item.code;
          const resourceColumnDetailId =
            item?.additional_properties?.resource_column_details_id;
          const result: any = resourcesColumnData.find(
            (item: any) => item.id === resourceColumnDetailId
          );
          return {
            ...item,
            ...result,
            ...{ rId: rId },
            ...{ rCode: rCode },
          };
        }
      );
      const resources = await Promise.all(resourcePromises);
      if (resIds && resIds.length > updatedSelectResourceValue.length) {
        setOpenResourceConfirmation(true);
        setResIds(resources);
        setResourceDataObj(updatedSelectResourceValue);
        return;
      }
      setSelectedResourceIds([]);
      setResIds([]);
      setResourceIds([]);
      setDomainComparisonRules([]);
      setTableRows([]);
      setColumns([]);
      setDomainRuleArr([]);
      const updatedFilters =
        customFilters?.length > 0 &&
        customFilters?.filter((customFilter: any) =>
          updatedSelectResourceValue.some(
            (item: any) => item?.id === customFilter?.resource_id
          )
        );
      const updatedFiltersWithId =
        updatedFilters &&
        updatedFilters.map((filter: any, index: any) => {
          return {
            ...filter,
            id: index,
            key: index,
          };
        });
      const updatedCustomRules =
        customComparisonRules && customComparisonRules?.length > 0
          ? customComparisonRules?.filter((rule: any) => {
              // Check if left or right operand ID is present in dataObj array
              const leftOperandIdPresent = updatedSelectResourceValue.some(
                (item: { id: any }) =>
                  item?.id === rule?.resource?.left_operand?.resource_id
              );
              const rightOperandIdPresent = updatedSelectResourceValue.some(
                (item: { id: any }) =>
                  item?.id === rule?.resource?.right_operand?.resource_id
              );

              // Filter the rule if neither left nor right operand ID is present in dataObj
              return leftOperandIdPresent && rightOperandIdPresent;
            })
          : [];

      setCustomFilters(updatedFiltersWithId);
      setCustomComparisonRules(updatedCustomRules);

      setIsSelecting(false);
      setSelectedResourceIds(resources);
      setResIds(resources);
      setResourceIds(resources.map(({ rId }: any) => rId));
      //Changes for update unique keys from rule start
      if (resources?.length > 0) {
        const uniqueRIds = new Set<number>();
        resources.forEach((item: any) => {
          uniqueRIds.add(item.rId);
        });
        const uniqueObjects: any = Array.from(uniqueRIds).map((rId) => {
          const item = resources.find((item: any) => item.rId === rId);
          const mergeType = selectedMergeType.find(
            (item: any) => item.rId === rId
          );
          return {
            rId: rId,
            resource_code: item?.rCode,
            columns: item?.resource_column_properties?.unique_columns || [],
            merge_type: mergeType?.merge_type,
          };
        });
        setSelectedResourceColumns(uniqueObjects);

        let resourceInlineVar: any = {};
        let allVariableList: any = {
          ...(allVariablesList?.rule_Variables || {}),
        };
        resources.map((res: any) => {
          resourceInlineVar = {
            ...resourceInlineVar,
            [res.rId]: res?.additional_properties?.inline_variables,
          };
          allVariableList = {
            ...allVariableList,
            ...res?.additional_properties?.inline_variables,
          };
        });
        setAllVariablesList((prev: any) => ({
          ...prev,
          resourceVar: resourceInlineVar,
        }));
        setGlobalVariables(allVariableList);
      }
      if (
        updatedSelectResourceValue &&
        updatedSelectResourceValue?.length !== 0
      ) {
        setErrors((prevError) => ({
          ...prevError,
          resources: "",
        }));
      }
    } catch (error) {
      console.error("Error selecting resources:", error);
      setIsSelecting(false);
    } finally {
      setIsLoaded(false);
    }
  };

  const handleValidations = async () => {
    try {
      const resultDataWithResources = {
        ...resultData,
        secondaryResource: secondaryMergeResource
          .map((item: any, idx: any) => {
            if (item.hasOwnProperty("resource_id")) {
              return {
                resource_id: item?.resource_id ?? null,
                resource_columns: item?.merge_on || [],
              };
            } else {
              return {
                resource_id: "blank",
                resource_columns: ["blank"],
              };
            }
          })
          .filter((item: any) => item !== null),
      };

      if (selectedResourceIds.length === 0) {
        // showToast(`"Please select resource"`, "error");
        setErrors((prevError) => ({
          ...prevError,
          resources: "Please select resource",
        }));
        await addEditRuleSchema.validate(resultDataWithResources, {
          abortEarly: false,
        });
        return;
      } else {
        await addEditRuleSchema.validate(resultDataWithResources, {
          abortEarly: false,
        });
      }
      if (customFilters.length > 0) {
        const isValidCustomFilters = customFilters.every(
          (filter: { resource_id: undefined; sql_query: undefined }) => {
            return filter.resource_id !== "" && filter.sql_query !== "";
          }
        );

        if (!isValidCustomFilters) {
          showToast(
            `Please select "Resource" and enter "Query" in Resource Filter`,
            "warning"
          );
          return;
        }
      }
      if (selectedResourceIds.length === 0) {
        showToast(`"Please select resource"`, "error");
        return;
      }

      if (selectedResourceColumns.length === 0) {
        showToast(`"Please select resource columns"`, "error");
        return;
      }
      const isColumnLengthNull = selectedResourceColumns.every(
        (item: any) => item.columns.length === 0
      );
      if (isColumnLengthNull) {
        showToast(`"Please select column/columns for all resources"`, "error");
        return;
      }
      const isSecondaryMergeResourceRequired = secondaryMergeResource.some(
        (item: any) => {
          return (
            item?.isBtnShow &&
            (item.resource_id === undefined || item.resource_id === null)
          );
        }
      );
      if (isSecondaryMergeResourceRequired) {
        showToast(
          "Please select secondary merge resource or remove the field",
          "error"
        );
        return;
      }
      for (const secondary of secondaryMergeResource) {
        const selectedResource = selectedResourceColumns.find(
          (resource: any) => resource.rId === secondary.parent_id
        );
        if (selectedResource && secondary?.resource_id) {
          const columnsLength = selectedResource.columns.length;
          const mergeOnLength = secondary?.merge_on
            ? secondary.merge_on.length
            : 0;
          const resourceName = resourcesData?.find((item) => {
            return item.id === selectedResource.rId;
          })?.resource_name;

          const secondaryResourceName = resourcesData?.find((item) => {
            return item.id === secondary.resource_id;
          })?.resource_name;

          if (mergeOnLength === 0) {
            showToast(
              "Please select resource column for secondary merge resource",
              "error"
            );
            return;
          }
          if (columnsLength !== mergeOnLength) {
            showToast(
              `The number of merge columns should be similar for the Resource ${resourceName} and the respective  Secondary merge Resource ${secondaryResourceName}`,
              "error"
            );
            return;
          }
        }
      }

      const isSameLength = selectedResourceColumns.every(
        (item: any) =>
          item.columns.length === selectedResourceColumns[0].columns.length
      );
      if (!isSameLength) {
        showToast(
          `"Please select same number of columns for all resources"`,
          "error"
        );
        return;
      }
      let isFilterRulesValid = true;
      const updatedCustomRules =
        customComparisonRules?.length > 0
          ? customComparisonRules.map((ruleItem: any) => {
              const { id, resource, filter_rules, ...rest } = ruleItem;
              // filter_rules?.length > 0 &&
              //   filter_rules.map((item: any) => delete item.id);
              if (filter_rules?.length > 0) {
                filter_rules.forEach((item: any) => {
                  if (!item.name || !item.sql_query) {
                    isFilterRulesValid = false;
                    return;
                  }

                  delete item.id;
                });
              }
              return {
                ...rest,
                ...resource,
                filter_rules,
              };
            })
          : [];
      if (!isFilterRulesValid) {
        showToast(
          "Resource and SQL Query cannot be empty in custom comparison's filter rules!",
          "warning"
        );
        return;
      }

      setOpenCommentConfirmation(true);
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  /**
   * When we click save rule
   * set unique doamin
   * create primary & secondary dataset
   * create merge rule
   * set final result state update
   */
  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      const uniqueDomains =
        selectedDomainData?.domain_properties?.unique_columns;

      const transformPossibleMergeColumns = possibleMergeColumns.map(
        (item: any) => {
          const { rId, columns } = item;
          let possible_merge_on_columns: any = {};
          columns.forEach((column: any) => {
            const { key, [key]: values } = column;
            possible_merge_on_columns[key] = values.length > 0 ? values : null;
          });
          // Check if all child keys are null
          const allKeysNull = Object.values(possible_merge_on_columns).every(
            (value) => value === null
          );
          return {
            rId,
            possible_merge_on_columns: allKeysNull
              ? null
              : possible_merge_on_columns,
          };
        }
      );

      const primaryDataset = {
        resource_id: selectedResourceColumns[0].rId,
        resource_code: selectedResourceColumns[0].resource_code,
        merge_on: selectedResourceColumns[0]?.columns,
        merge_type: selectedMergeType[0]?.merge_type ?? "outer",
        possible_merge_on_columns:
          transformPossibleMergeColumns[0].possible_merge_on_columns,
        secondary_merge_resource: (() => {
          const primaryResource = secondaryMergeResource.find(
            (item: any) => item.parent_id === selectedResourceColumns[0].rId
          );
          if (primaryResource && primaryResource?.resource_id) {
            const {
              resource_column_details_id,
              isBtnShow,
              parent_id,
              resourceColumnDetails,
              ...rest
            } = primaryResource;
            return rest;
          }
          return null;
        })(),
      };

      const secondaryPossibleMergeColumns =
        transformPossibleMergeColumns.slice(1);

      const secondaryDatasets = selectedResourceColumns
        .slice(1)
        .map((item: any) => {
          if (item == undefined) {
            return;
          }
          return {
            resource_id: item.rId,
            resource_code: item.resource_code,
            merge_on: item?.columns,
            secondary_merge_resource: (() => {
              const secondaryResource = secondaryMergeResource.find(
                (resItem: any) => resItem.parent_id === item.rId
              );
              if (secondaryResource && secondaryResource?.resource_id) {
                const {
                  resource_column_details_id,
                  isBtnShow,
                  parent_id,
                  resourceColumnDetails,
                  ...rest
                } = secondaryResource;
                return rest;
              }
              return null;
            })(),
          };
        });

      const mergedSecondaryDataset = secondaryDatasets.map((dataset: any) => {
        const correspondingMergeColumns = secondaryPossibleMergeColumns.find(
          (mergeColumns: any) => mergeColumns.rId === dataset.resource_id
        );
        const correspondingMergeType = selectedMergeType.find(
          (mergeType: any) => mergeType.rId === dataset.resource_id
        );
        if (correspondingMergeColumns) {
          return {
            ...dataset,
            possible_merge_on_columns:
              correspondingMergeColumns.possible_merge_on_columns,
            merge_type: correspondingMergeType?.merge_type ?? "outer",
          };
        }
        return dataset;
      });

      const mergeRule = {
        primary_dataset: primaryDataset,
        secondary_datasets: mergedSecondaryDataset,
      };
      const resourcesDetailedList = selectedResourceColumns.map(
        (item: any) => ({
          resource_id: item.rId,
          resource_code: item.resource_code,
        })
      );

      // customFilters?.length > 0 &&
      //   customFilters.map((filterItem: any) => delete filterItem.id);

      let isFilterRulesValid = true;
      const updatedCustomRules =
        customComparisonRules?.length > 0
          ? customComparisonRules.map((ruleItem: any) => {
              const { id, resource, filter_rules, ...rest } = ruleItem;
              // filter_rules?.length > 0 &&
              //   filter_rules.map((item: any) => delete item.id);
              if (filter_rules?.length > 0) {
                filter_rules.forEach((item: any) => {
                  if (!item.name || !item.sql_query) {
                    isFilterRulesValid = false;
                    return;
                  }

                  delete item.id;
                });
              }
              return {
                ...rest,
                ...resource,
                filter_rules,
              };
            })
          : [];
      if (adhocQueryData.length > 0) {
        const isValidFilterRules = adhocQueryData.every(
          (adhoc: { name: undefined; sql_query: undefined }) => {
            return adhoc.name !== "" && adhoc.sql_query !== "";
          }
        );
        if (!isValidFilterRules) {
          showToast(
            `"Name and SQL Query cannot be empty in Adhoc Queries!"`,
            "warning"
          );
          return;
        }
      }
      setResultData(() => ({
        ...resultData,
        rule_schema: {
          merge_rule: mergeRule,
          filter_rules: customFilters || [],
          domain_comparison_rules: domainComparisonRules,
          custom_comparison_rules: updatedCustomRules,
          resources: resourceIds,
          adhoc_queries: adhocQueryData,
          inline_variables: globalVariables,
          resources_detailed_list: resourcesDetailedList,
        },
      }));
      const filteredDomainComparisonRules = domainComparisonRules.map(
        ({ id, rowId, ...rest }) => rest
      );
      setFinalRule({
        ...resultData,
        rule_schema: {
          merge_rule: mergeRule,
          filter_rules: customFilters || [],
          domain_comparison_rules: filteredDomainComparisonRules,
          custom_comparison_rules: updatedCustomRules,
          resources: resourceIds,
          adhoc_queries: adhocQueryData,
          inline_variables: globalVariables,
          resources_detailed_list: resourcesDetailedList,
        },
      });
      setIsLoading(false);
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const validateSecondaryResourceField = async (
    name: any,
    index: number,
    element: string,
    value: any
  ) => {
    try {
      await addEditRuleSchema.validateAt(name, value);
      setErrors((prevErrors: any) => {
        return {
          ...prevErrors,
          [`secondaryResource[${index}].${element}`]: undefined,
        };
      });
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...resultData, [name]: value };
      await addEditRuleSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  /**
   * On select or update resource from dropdown
   * set coloums of resources & genrate dynamic rows data
   */

  useEffect(() => {
    const fetchData = async () => {
      if (
        selectedResourceIds.length > 0 &&
        selectedDomainData?.domain_properties?.columns?.length > 0
      ) {
        const allColumnNamesSet = new Set();

        // Assuming getColumns and tableRowsData are asynchronous functions
        await getColumns();
        await tableRowsData();

        for (const resource of selectedResourceIds) {
          if (resource.resource_column_properties) {
            for (const column of resource.resource_column_properties
              .resource_columns) {
              if (column.column_name) {
                allColumnNamesSet.add(column.column_name);
              }
            }
          }
        }
        const uniqueColumnNames: any = Array.from(allColumnNamesSet); // Convert Set back to an array
        setAvailColumns(uniqueColumnNames);
        setAvailColumnsWithResourceDetail(null);
      }
    };

    fetchData();
  }, [selectedResourceIds, selectedDomainData, selectedResourceColumns]);

  /**
   * Finally submit a rule & hit api for rule creation
   */
  useEffect(() => {
    const handleUpdateRule = () => {
      updateRule({ currentRuleId, payload: finalRule })
        .then((res) => {
          setIsLoading(false);
          setResultData((prev: any) => ({
            ...prev,
            comment: "",
          }));
          showToast("Rule Updated Successfully", "success");
          navigate(`/rules-list/${Number(currentDomainId)}`);
        })
        .catch((err) => {
          setIsLoading(false);
          console.log("err", err);
        });
    };

    if (Object.keys(finalRule).length > 0) {
      handleUpdateRule();
    }
  }, [finalRule]);

  /**
   * Set table coloums for comparision rules with unique keys
   */
  const getColumns = () => {
    const columns: any = [
      {
        ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
        renderCell: (params: any) => (
          <IconButton
            size="small"
            className="rotate-arrow"
            tabIndex={-1}
            disabled={
              !["percentage", "absolute"].includes(params?.row?.tolerance_type)
            }
            onClick={() => {
              if (expandedRow.includes(params.row.id)) {
                setExpandedRow((prev: any) =>
                  prev.filter((item: any) => item !== params.row.id)
                );
              } else {
                setExpandedRow((prev: any) => [...prev, params.row.id]);
              }
            }}
          >
            {params?.row?.tolerance_value != null ? (
              <ExpandMoreIcon fontSize="inherit" />
            ) : null}
          </IconButton>
        ),
      },
      {
        field: "id",
        headerName: "Sr No",
        width: 100,
        maxWidth: 100,
        renderCell: (params: any, a: any, b: any) => params.row.id + 1,
      },
      {
        field: "domain_name",
        headerName: "Domain Name",
        width: 120,
        flex: 1,
        renderCell: (params: any) => {
          return <LimitChractersWithTooltip value={params?.value} />;
        },
      },
    ];

    selectedResourceIds?.length > 0 &&
      selectedResourceIds.forEach((resourceItem: any) => {
        if (!resourceItem) return;
        columns.push({
          headerName: resourceItem.resource_name,
          field: resourceItem.resource_name,
          width: 120,
          flex: 1,
          renderCell: (params: any) => {
            return <LimitChractersWithTooltip value={params?.value} />;
          },
        });
      });

    columns.push(
      {
        headerName: "Comparison Type",
        field: "comparison_type",
        maxWidth: 120,
        flex: 1,
        renderCell: (params: any) => {
          const handleOperatorChange = (value: any, id: any) => {
            setTableRows((prev) => {
              return prev.map((item) => {
                if (item.id === id) {
                  item.comparison_type = value;
                }
                return item;
              });
            });
            setDomainComparisonRules((prev) => {
              return prev.map((item) => {
                if (item.rowId === id + 1) {
                  item.comparison_type = value;
                }
                return item;
              });
            });
          };
          return (
            <>
              <select
                value={params.row.comparison_type}
                onChange={(e: any) => {
                  handleOperatorChange(e.target.value, params.row.id);
                }}
                className="form-control-1 input-sm"
              >
                <option value="equals">==</option>
                <option value="not equals">!=</option>
              </select>
            </>
          );
        },
      },
      {
        headerName: "Action",
        field: "action",
        maxWidth: 100,
        align: "center",
        headerAlign: "center",
        renderCell: (params: any) => {
          const handleToleranceValue = () => {
            setOpenToleranceDialog(true);
            setSelectedRowId(params.row.id);
            tableRows.map((row: any) => {
              if (row.rowId === params.row.id) {
                setToleranceData({
                  tolerance_type: row?.tolerance_type || "percentage",
                  tolerance_value: row?.tolerance_value || null,
                  fallback_value: row?.fallback_value || null,
                });
              }
            });
            setToleranceData({
              tolerance_type: "percentage",
              tolerance_value: null,
              fallback_value: null,
            });
          };
          return (
            <>
              <Tooltip title="Add Tolerance" placement="top" arrow>
                <Button
                  color="secondary"
                  onClick={handleToleranceValue}
                  className="datagrid-action-btn min-width-40"
                >
                  <IconAddTolranceDataSvg />
                </Button>
              </Tooltip>
              <Tooltip title="Remove" placement="top" arrow key={params.row.id}>
                <IconButton
                  className="datagrid-action-btn"
                  sx={{ color: "grey" }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setTableRows((prev: any) =>
                      prev
                        .filter((prevItem: any) => {
                          return prevItem.id !== params.row.id;
                        })
                        .map((prevItem: any, index: number) => {
                          return {
                            ...prevItem,
                            id: index,
                          };
                        })
                    );
                    setDomainComparisonRules((prevRules) => {
                      const updatedRules = prevRules.filter(
                        (rule) => rule.rowId !== params.row.id + 1
                      );
                      return updatedRules.map((rule, index) => ({
                        ...rule,
                        rowId: index + 1,
                      }));
                    });
                  }}
                >
                  <IconCloseSvgBlue />
                </IconButton>
              </Tooltip>
            </>
          );
        },
      }
    );

    setColumns(columns);
    setIsLoaded(false);
  };

  /**
   * Set table rows data for unique keys
   * Get domain maping key from selected resource ids
   */
  const tableRowsData = () => {
    if (selectedDomainData?.domain_properties?.columns?.length > 0) {
      const tableRowsData: any = [];
      let resourcesNames: any = [];

      const uniqueColumnsSet = new Set();

      selectedResourceColumns.forEach((res: any) => {
        res.columns.forEach((col: any) => {
          uniqueColumnsSet.add(col);
        });
      });

      // Convert the set back to an array
      const uniqueColumns = Array.from(uniqueColumnsSet);

      selectedDomainData?.domain_properties?.columns.forEach(
        (item: any, idx: any) => {
          const tableRow: any = {
            id: idx + 1,
            domain_name: item?.name,
          };
          selectedResourceIds.forEach((resourceItem: any) => {
            if (!resourceItem) return;
            resourcesNames.push(resourceItem.resource_name);
            const resourceColumnItem =
              resourceItem.resource_column_properties?.resource_columns.find(
                (resourceColumnItem: any) =>
                  resourceColumnItem.domain_column === item?.name &&
                  resourceColumnItem?.is_active
              );
            tableRow[resourceItem.resource_name] =
              resourceColumnItem?.column_name;
            tableRow["column_name"] = resourceColumnItem?.column_name;
          });
          tableRowsData.push(tableRow);
        }
      );

      const uniqueResourcesNames: any[] = [];
      for (const name of resourcesNames) {
        if (!uniqueResourcesNames.includes(name)) {
          uniqueResourcesNames.push(name);
        }
      }
      resourcesNames = uniqueResourcesNames;

      const result = tableRowsData.filter((tableRowItem: any) => {
        const objectValues = Object.values(tableRowItem);
        return resourcesNames.every(
          (resourceName: any) =>
            tableRowItem[resourceName] !== undefined &&
            !objectValues.some((value) => uniqueColumns.includes(value))
        );
      });

      const domainRules = result.map((item: any, idx: any) => {
        return {
          id: item?.id,
          name: item?.domain_name,
          column_name: item?.domain_name,
        };
      });
      const filteredDomainRules = domainRules.filter((rule: { name: any }) => {
        const matchingRule =
          ruleData?.rule_schema?.domain_comparison_rules &&
          ruleData?.rule_schema?.domain_comparison_rules.find(
            (comparisonRule: { name: any }) => comparisonRule.name === rule.name
          );
        return matchingRule !== undefined;
      });
      const filteredDomainRulesWithRowId = filteredDomainRules.map(
        (rule: any, index: number) => ({
          ...rule,
          rowId: index + 1,
        })
      );
      // if (ruleData?.rule_schema?.domain_comparison_rules?.length > 0) {
      const hasDomainComparisonRules =
        ruleData?.rule_schema?.domain_comparison_rules &&
        ruleData?.rule_schema?.domain_comparison_rules?.length > 0;
      hasDomainComparisonRules &&
        setDomainComparisonRules(filteredDomainRulesWithRowId);
      setDomainComparisonRules((prev) => {
        const updatedRows = prev.map((item) => {
          const matchingRule =
            hasDomainComparisonRules &&
            ruleData?.rule_schema?.domain_comparison_rules?.find(
              (ruleItem: any) => ruleItem.name === item?.name
            );

          if (matchingRule) {
            return {
              ...item,
              tolerance_type: matchingRule.tolerance_type,
              tolerance_value: matchingRule.tolerance_value,
              fallback_value: matchingRule.fallback_value,
              comparison_type: matchingRule.comparison_type,
            };
          }
          return item;
        });

        return updatedRows;
      });
      const rows = result.map((item: any, idx: any) => {
        const domainRule =
          hasDomainComparisonRules &&
          ruleData?.rule_schema?.domain_comparison_rules?.find(
            (ruleItem: any) => ruleItem?.name === item?.domain_name
          );
        if (domainRule) {
          return {
            ...item,
            id: idx,
          };
        }
      });
      const filteredRows = rows.filter((row: any) => row?.domain_name);
      setTableRows(
        filteredRows.map((item: any, idx: any) => {
          return {
            ...item,
            id: idx,
          };
        })
      );

      setTableRows((prev) => {
        const updatedRows = prev.map((item) => {
          const matchingRule =
            hasDomainComparisonRules &&
            ruleData?.rule_schema?.domain_comparison_rules?.find(
              (ruleItem: any) => ruleItem.name === item?.domain_name
            );

          if (matchingRule) {
            return {
              ...item,
              tolerance_type: matchingRule.tolerance_type,
              tolerance_value: matchingRule.tolerance_value,
              fallback_value: matchingRule.fallback_value,
              comparison_type: matchingRule.comparison_type,
            };
          }
          return item;
        });

        return updatedRows;
      });
      // }
      setDomainRuleArr(
        result.map((item: any, idx: any) => {
          return {
            ...item,
            id: idx,
          };
        })
      );
      setIsLoaded(false);
    }
  };

  const handleChangeTab = (event: any, newValue: any) => {
    setIsLoading(true);
    setActiveTab(newValue);
    setIsLoading(false);
    setCustomFilters(customFilters);
  };
  const renderAvailableColumnsPopper = useCallback((props: any) => {
    return (
      <Popper
        {...props}
        anchorEl={document.getElementById("auto-complete-available-columns")}
      />
    );
  }, []);
  const renderSelectedResourcesPopper = useCallback(
    (props: any, ids: string[]) => {
      return (
        <Popper
          {...props}
          anchorEl={document.getElementById(
            `autocomplete-selected-resources-${ids}`
          )}
        />
      );
    },
    []
  );
  const renderMultipleResourcesPopper = useCallback((props: any) => {
    return (
      <Popper
        {...props}
        anchorEl={document.getElementById("auto-complete-multiple-resources")}
      />
    );
  }, []);
  const handleEditToleranceValue = (rowData: any) => {
    setOpenToleranceDialog(true);
    setSelectedRowId(rowData?.id);
    setToleranceData({
      tolerance_type: rowData?.tolerance_type,
      tolerance_value: rowData?.tolerance_value,
      fallback_value: rowData?.fallback_value,
    });
  };
  const handleDeleteToleranceValue = (rowData: any) => {
    const updatedDomainComparision = domainComparisonRules.map((rule) => {
      if (rule.rowId === rowData?.id + 1) {
        // Remove tolerance_type and tolerance_value for the matching rowId
        return {
          ...rule,
          tolerance_type: null,
          tolerance_value: null,
          fallback_value: null,
        };
      }
      return rule;
    });

    setDomainComparisonRules(updatedDomainComparision);
    const updatedTableRows = tableRows.map((row) => {
      if (row.id === rowData?.id) {
        // Remove tolerance_type and tolerance_value for the matching rowId
        return {
          ...row,
          tolerance_type: null,
          tolerance_value: null,
          fallback_value: null,
        };
      }
      return row;
    });
    setTableRows(updatedTableRows);
  };
  const tabContent: any = {
    domainComparisonRule: (
      <Box gap="3rem">
        <Box className="text-box-card compact-text-box-card no-radius mb-0 bdr-bottom-0">
          <Grid>
            <Autocomplete
              id={"auto-complete-available-columns"}
              PopperComponent={renderAvailableColumnsPopper}
              fullWidth={true}
              className="form-control-autocomplete"
              disabled={Boolean(isSelecting)}
              options={domainRuleArr}
              getOptionLabel={(option: any) => option.domain_name}
              disableCloseOnSelect
              multiple
              onChange={(key: any, value: any) =>
                handleChangeDomainRule(key, value)
              }
              renderInput={(params) => (
                <>
                  <div className="autocomplete-chips-direction">
                    <TextField
                      {...params}
                      variant="outlined"
                      label="Available Columns"
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </div>
                </>
              )}
              renderOption={(props, option, { selected }) => (
                <MenuItem
                  {...props}
                  key={option.id}
                  value={option.domain_name}
                  sx={{ justifyContent: "space-between" }}
                >
                  {option.domain_name}
                  {selected ? <CheckCircleRounded color="info" /> : null}
                </MenuItem>
              )}
              value={
                domainRuleArr.filter((item: any) =>
                  tableRows.find(
                    (row: any) => row?.domain_name === item?.domain_name
                  )
                ) || null
              }
            />
          </Grid>
        </Box>
        <RulesDataTable
          dataColumns={columns}
          dataRows={tableRows}
          checkboxSelection={true}
          dataListTitle="Comparison Rule List"
          className="dataTable no-radius"
          loading={false}
          handleEditToleranceValue={handleEditToleranceValue}
          handleDeleteToleranceValue={handleDeleteToleranceValue}
        />
      </Box>
    ),
    customComparisonRule: (
      <CustomComparisionTab
        selectedResourceIds={selectedResourceIds}
        resourcesData={resourcesData}
      />
    ),
    resourceFilter: <ResourceFilter isLoading={false} />,
    adhocQueries: (
      <AdhocQueries
        adhocQueryData={adhocQueryData}
        setAdhocQueryData={setAdhocQueryData}
        isViewOnly={false}
        errors={errors}
        setErrors={setErrors}
        sampleQuery={sampleAdhocQuery}
      />
    ),
    renderVariables: (
      <>
        <div
          className={`accordion-panel text-box-card compact-text-box-card no-radius bottom-radius bg-white`}
          style={{ marginBottom: "0", border: "0", padding: "0" }}
        >
          {Object.keys(globalVariables).length > 0 ? (
            <ViewVariables
              isViewOnly={true}
              error={errors}
              setErrors={setErrors}
            />
          ) : (
            <span>No Variables found</span>
          )}
        </div>
      </>
    ),
  };

  const handleChangeDomainRule = (value: any, data: any) => {
    if (data?.length > 0) {
      const sortedData = data
        .slice()
        .sort((a: { id: number }, b: { id: number }) => a.id - b.id);
      const domainRules = sortedData.map((item: any, idx: any) => {
        const comparisonData = domainComparisonRules.find(
          (comparisonItem) => comparisonItem.name === item.domain_name
        );
        if (comparisonData) {
          return {
            rowId: idx + 1,
            name: item.domain_name,
            column_name: item.domain_name,
            comparison_type: comparisonData.comparison_type || "equals",
            tolerance_type: comparisonData.tolerance_type,
            tolerance_value: comparisonData.tolerance_value,
            fallback_value: comparisonData.fallback_value,
          };
        } else {
          return {
            rowId: idx + 1,
            name: item.domain_name,
            column_name: item.domain_name,
            comparison_type: "equals",
            tolerance_type: null,
            tolerance_value: null,
            fallback_value: null,
          };
        }
      });
      setTableRows(
        sortedData.map((item: any, idx: any) => {
          const comparisonData = domainComparisonRules.find(
            (comparisonItem) => comparisonItem.name === item.domain_name
          );
          if (comparisonData) {
            return {
              ...item,
              id: idx,
              tolerance_type: comparisonData.tolerance_type,
              tolerance_value: comparisonData.tolerance_value,
              fallback_value: comparisonData.fallback_value,
              comparison_type: comparisonData.comparison_type || "equals",
            };
          } else {
            return {
              ...item,
              id: idx,
              tolerance_type: null,
              tolerance_value: null,
              fallback_value: null,
              comparison_type: "equals",
            };
          }
        })
      );
      setDomainComparisonRules(domainRules);
    } else {
      setDomainComparisonRules([]);
      setTableRows([]);
    }
  };

  const onSelectResouceColumns = (rId: any, dataObj: any) => {
    const isExist = selectedResourceColumns.find(
      (item: any) => item.rId === rId
    );
    const updatedColumns = isExist
      ? selectedResourceColumns.map((item: any) => {
          if (item.rId === rId) {
            return {
              ...item,
              columns: dataObj,
            };
          }
          return item;
        })
      : [
          ...selectedResourceColumns,
          {
            rId,
            columns: dataObj,
          },
        ];
    // Find the selected resource from selectedResourceIds
    const selectedResource = selectedResourceIds.find(
      (resource: any) => resource.rId === rId
    );

    // If the selected resource is found, get the length of its resource columns
    let resourceColumnLength = 0;
    if (selectedResource) {
      resourceColumnLength =
        selectedResource.resource_column_properties.resource_columns.length;
    }

    if (dataObj && resourceColumnLength === dataObj?.length) {
      showToast(
        "Since all columns are unique, the custom comparison for this resource has been removed.",
        "warning"
      );
    }
    setSelectedResourceColumns(updatedColumns);
  };

  useEffect(() => {
    const transformedData = selectedResourceColumns.map((item: any) => {
      const transformedColumns = item.columns.map((columnName: any) => {
        const mergeValues =
          (item?.possible_merge_on_columns &&
            item?.possible_merge_on_columns[columnName]) ||
          [];
        return {
          key: columnName,
          [columnName]: mergeValues,
        };
      });

      return {
        rId: item.rId,
        columns: transformedColumns,
      };
    });
    setPossibleMergeColumns(transformedData);
    const filteredSelectedResourceColumn = selectedResourceColumns.map(
      (item: any) => {
        const { columns, ...rest } = item;
        return rest;
      }
    );
    setSelectedMergeType(filteredSelectedResourceColumn);
  }, [selectedResourceColumns]);

  const onClickMergeTag = (
    option: any,
    columnsArr: any,
    resourceName: string,
    resourceId: number
  ) => {
    let modifiedColumnsArr: string[] = [];

    const matchedTag = tagColResult.find(
      (tag: { id: any; key: any }) =>
        tag.id === resourceId && tag.key === option
    );

    // Find the columns to exclude based on resourceId
    const excludeKeys = possibleMergeColumns
      .filter((item: any) => item.rId === resourceId)
      .flatMap((item: any) => item.columns.map((col: any) => col.key));

    // Set the selectedRows with the values from the matched tag
    if (matchedTag) {
      modifiedColumnsArr = combineArrays(matchedTag?.values, columnsArr);
    } else {
      modifiedColumnsArr = columnsArr;
    }
    modifiedColumnsArr = modifiedColumnsArr.filter(
      (col) => !excludeKeys.includes(col)
    );
    setShowTagsDialog((prevState: any) => ({
      ...prevState,
      [`${resourceName}_${option.replace(" ", "")}`]:
        !prevState[`${resourceName}_${option.replace(" ", "")}`],
    }));
    setSelectedTags({
      id: resourceId,
      key: option,
      values: modifiedColumnsArr,
    });
  };
  const handleChangeMergeType = (event: any, rId: number) => {
    const newMergeType = event.target.value;
    setSelectedMergeType((prevColumns: any) =>
      prevColumns.map((column: any) => {
        if (column.rId === rId) {
          return { ...column, merge_type: newMergeType };
        }
        return column;
      })
    );
  };

  const SelectedResourceList = () => {
    return (
      <div className="merge-definition-wrapper">
        {selectedResourceIds.length > 0 && (
          <div className="selected-resource-list">
            <h4>Select Resource column for Merge definition</h4>
            <div className="merge-def-inner">
              <Grid container>
                {selectedResourceIds.map((resourceItem: any, idx: any) => {
                  const columnsArr =
                    resourceItem?.resource_column_properties?.resource_columns
                      ?.map((item: any) => {
                        if (item?.is_active) {
                          return item.column_name === "file_name"
                            ? null
                            : item.column_name;
                        }
                      })
                      .filter((filterItem: any) => filterItem);
                  return (
                    <Grid
                      item
                      xs
                      md={12}
                      lg={12}
                      key={resourceItem?.id}
                      className={`data-grid`}
                    >
                      <Grid container rowSpacing={2.5} columnSpacing={4}>
                        <Grid
                          item
                          xs
                          md={12}
                          lg={8}
                          className="position-relative"
                        >
                          <Box
                            className="label-text"
                            sx={{ paddingTop: "10px", paddingRight: "16px" }}
                          >
                            {`${resourceItem?.resource_name} ${
                              idx === 0
                                ? "(Primary Resource)"
                                : "(Secondary Resource)"
                            }  `}
                          </Box>
                          <Autocomplete
                            id={`autocomplete-selected-resources-${idx}`}
                            PopperComponent={(props) =>
                              renderSelectedResourcesPopper(props, idx)
                            }
                            fullWidth={true}
                            className="form-control-autocomplete  alternative-2"
                            disabled={Boolean(isSelecting)}
                            multiple
                            options={columnsArr?.length > 0 ? columnsArr : []}
                            getOptionLabel={(option) => option}
                            disableCloseOnSelect
                            onChange={(key: any, value: any) =>
                              onSelectResouceColumns(resourceItem?.rId, value)
                            }
                            value={
                              selectedResourceColumns?.length > 0
                                ? selectedResourceColumns.find(
                                    (item: any) =>
                                      item.rId === resourceItem?.rId
                                  )?.columns
                                : []
                            }
                            renderInput={(params) => (
                              <>
                                <div className="autocomplete-chips-direction">
                                  <TextField
                                    {...params}
                                    variant="outlined"
                                    placeholder="Select..."
                                    InputLabelProps={{
                                      shrink: true,
                                    }}
                                  />
                                </div>
                              </>
                            )}
                            renderOption={(props, option, { selected }) => (
                              <MenuItem
                                {...props}
                                key={option}
                                value={option}
                                sx={{ justifyContent: "space-between" }}
                              >
                                {option}
                                {selected ? (
                                  <CheckCircleRounded color="info" />
                                ) : null}
                              </MenuItem>
                            )}
                            renderTags={(value, getTagProps) => {
                              return (
                                <React.Fragment>
                                  <div
                                    className={`align-from-left flex-reverse`}
                                  >
                                    {value.map((option, index) => {
                                      const resultValues =
                                        tagColResult &&
                                        tagColResult.find(
                                          (obj: any) =>
                                            obj.key === option &&
                                            obj.id === resourceItem?.rId
                                        )?.values;

                                      const resultValuesLength =
                                        resultValues && resultValues.length;

                                      return (
                                        <React.Fragment key={index}>
                                          <Chip
                                            {...getTagProps({ index })}
                                            label={option}
                                            className={`${
                                              resultValuesLength
                                                ? "sep-position"
                                                : ""
                                            }`}
                                            avatar={
                                              <Box>
                                                <IconButton
                                                  size="small"
                                                  aria-label="info"
                                                  onClick={() => {
                                                    setTagId(
                                                      `${option.replace(
                                                        " ",
                                                        ""
                                                      )}`
                                                    );
                                                    onClickMergeTag(
                                                      option,
                                                      columnsArr,
                                                      resourceItem?.resource_name,
                                                      resourceItem?.rId
                                                    );
                                                  }}
                                                >
                                                  {resultValuesLength >= 1 ? (
                                                    <CreateIcon fontSize="small" />
                                                  ) : (
                                                    <AddCircleIcon fontSize="small" />
                                                  )}
                                                </IconButton>

                                                <Tooltip
                                                  placement="bottom"
                                                  arrow
                                                  componentsProps={{
                                                    tooltip: {
                                                      sx: {
                                                        bgcolor: "#F8F8F8",
                                                        padding: "13px 15px",
                                                        borderRadius: "8px",
                                                        boxShadow:
                                                          "0 8px 4px 4px rgba(0,0,0,.22)",
                                                        "& .MuiTooltip-arrow": {
                                                          color: "#F8F8F8",
                                                        },
                                                      },
                                                    },
                                                  }}
                                                  title={
                                                    <Box className="tags-tooltip-parent">
                                                      {resultValues &&
                                                        resultValues.length >=
                                                          1 &&
                                                        resultValues?.map(
                                                          (item: any) => {
                                                            return (
                                                              <div className="chip">
                                                                {item}
                                                              </div>
                                                            );
                                                          }
                                                        )}
                                                    </Box>
                                                  }
                                                >
                                                  <span
                                                    className={`${
                                                      resultValuesLength >= 1
                                                        ? "counter"
                                                        : ""
                                                    }`}
                                                  >
                                                    {resultValuesLength > 0
                                                      ? resultValuesLength < 10
                                                        ? `0${resultValuesLength}`
                                                        : resultValuesLength
                                                      : ""}
                                                  </span>
                                                </Tooltip>
                                              </Box>
                                            }
                                            deleteIcon={
                                              <span>
                                                <CancelIcon />
                                              </span>
                                            }
                                          />
                                        </React.Fragment>
                                      );
                                    })}
                                  </div>
                                </React.Fragment>
                              );
                            }}
                          />
                          <div
                            className={`mergetag-popup ${tagId}`}
                            style={{
                              display: showTagsDialog[
                                `${resourceItem?.resource_name}_${tagId}`
                              ]
                                ? "block"
                                : "none",
                            }}
                          >
                            <TagsDialog
                              setShowDialog={setShowTagsDialog}
                              selectedTags={selectedTags}
                              setTagColResult={setTagColResult}
                              tagColResult={tagColResult}
                              setPossibleMergeColumns={setPossibleMergeColumns}
                              possibleMergeColumns={possibleMergeColumns}
                            />
                          </div>
                        </Grid>
                        <Grid
                          item
                          xs
                          md={12}
                          lg={3}
                          className="position-relative"
                        >
                          {idx === 0 ? null : (
                            <>
                              <Box
                                className="label-text"
                                sx={{
                                  paddingTop: "10px",
                                  paddingRight: "16px",
                                }}
                              >
                                Merge Type
                              </Box>
                              <Select
                                MenuProps={{
                                  disableScrollLock: true,
                                }}
                                title="Merge type"
                                defaultValue="outer"
                                value={
                                  selectedMergeType &&
                                  selectedMergeType.length > 0
                                    ? selectedMergeType.find(
                                        (item: any) =>
                                          item?.rId === resourceItem?.rId
                                      )?.merge_type ?? "outer"
                                    : "outer"
                                }
                                name="merge_type"
                                style={{ width: "100%", height: 35 }}
                                onChange={(e) =>
                                  handleChangeMergeType(e, resourceItem?.rId)
                                }
                                className={`form-control-autocomplete form-control-autocomplete-1`}
                              >
                                {mergeType.map((type: string) => (
                                  <MenuItem key={type} value={type}>
                                    <span
                                      style={{ textTransform: "capitalize" }}
                                    >
                                      {type}
                                    </span>
                                  </MenuItem>
                                ))}
                              </Select>
                            </>
                          )}
                        </Grid>
                        <SecondaryMergeResource
                          resourcesData={resourcesData}
                          resultData={resultData}
                          setResultData={setResultData}
                          dataSetTypeIdx={idx}
                          parentId={resourceItem?.rId}
                          setSecondaryMergeResource={setSecondaryMergeResource}
                          secondaryMergeResource={secondaryMergeResource}
                          setIsLoading={setIsLoaded}
                          errors={errors}
                          validateSecondaryResourceField={
                            validateSecondaryResourceField
                          }
                        />
                      </Grid>
                    </Grid>
                  );
                })}
              </Grid>
            </div>
          </div>
        )}
      </div>
    );
  };

  const handleCloseToleranceDialog = () => {
    setOpenToleranceDialog(false);
    setToleranceData({});
    setSelectedRowId(null);
  };
  const onSaveToleranceDialog = () => {
    setTableRows((prev) => {
      return prev.map((item) => {
        if (item.id === selectedRowId) {
          item.tolerance_type = toleranceData.tolerance_type;
          item.tolerance_value = toleranceData.tolerance_value;
          item.fallback_value = toleranceData.fallback_value;
        }
        return item;
      });
    });
    setDomainComparisonRules((prev) => {
      return prev.map((item) => {
        if (item.rowId === selectedRowId + 1) {
          item.tolerance_type = toleranceData.tolerance_type;
          item.tolerance_value = toleranceData.tolerance_value;
          item.fallback_value =
            toleranceData.tolerance_type === "percentage"
              ? toleranceData?.fallback_value
              : null;
        }
        return item;
      });
    });
    setOpenToleranceDialog(false);
  };
  const handleChipClick = (option: any) => {
    const url = `/resource/${option?.domain_id}/view/${option?.rId}`;
    window.open(url, "_blank");
  };
  const handleCancel = () => {
    setOpenResourceConfirmation(false);
  };
  const handleConfirm = () => {
    if (resourceDataObj.length === 0) {
      setSelectedResourceIds([]);
      setResIds([]);
      setResourceIds([]);
      setCustomComparisonRules([]);
      setCustomFilters([]);
      setSelectedResourceColumns([]);
      setTableRows([]);
      setDomainComparisonRules([]);
      setDomainRuleArr([]);
      setColumns([]);
      setErrors((prevError) => ({
        ...prevError,
        resources: "Please select resource",
      }));
    }
    onSelectResources(resourceDataObj);
    setOpenResourceConfirmation(false);
  };
  const handleCommentChange = (e: any) => {
    setResultData((prev: any) => ({
      ...prev,
      comment: e.target.value,
    }));
  };
  const handleSaveComment = async (e: any) => {
    setOpenCommentConfirmation(false);
    handleSubmit();
  };
  const handleCancelComment = () => {
    setOpenCommentConfirmation(false);
    setResultData((prev: any) => ({
      ...prev,
      comment: "",
    }));
  };
  return (
    <Box>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={isLoaded}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
      <Box>
        <Box className="text-box-card compact-text-box-card no-radius mb-0 top-radius merge-zindex">
          <Grid container rowSpacing={2.5} columnSpacing={4}>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                {" "}
                Rule Name<span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                name="name"
                // placeholder="Ex: Sample Rule Name"
                onChange={handleChangeRule}
                className={`form-control ${errors?.name ? "has-error" : ""}`}
                value={resultData?.name || ""}
                error={!!errors?.name}
                helperText={errors?.name || ""}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <AutoCompleteDomainList
                setIsLoading={setIsLoading}
                handelOnChangeDomain={handelChangeDomain}
                currentDomainId={currentDomainId}
                className={`form-control-autocomplete disabled disable-arrow ${
                  errors?.domain_id ? "has-error" : ""
                }`}
                isDisabled={Boolean(true)}
                required
                error={errors?.domain_id}
                helperText={errors?.domain_id}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                Select Resources<span className="required-asterisk">*</span>
              </label>
              <Autocomplete
                id={"auto-complete-multiple-resources"}
                PopperComponent={renderMultipleResourcesPopper}
                fullWidth={true}
                className={`form-control-autocomplete ${
                  errors?.resources ? "has-error" : ""
                }`}
                disabled={Boolean(isSelecting)}
                multiple
                options={resourcesData || []}
                getOptionLabel={(option) => option?.resource_name || ""}
                disableCloseOnSelect
                onChange={(key: any, value: any) => onSelectResources(value)}
                renderInput={(params) => (
                  <>
                    <div className="autocomplete-chips-direction">
                      <TextField
                        {...params}
                        variant="outlined"
                        // label="Multiple Resources"
                        placeholder="Select..."
                        InputLabelProps={{
                          shrink: true,
                        }}
                        error={!!errors?.resources}
                        helperText={errors?.resources || ""}
                      />
                    </div>
                  </>
                )}
                renderTags={(value, getTagProps) => {
                  return (
                    <>
                      <div className="align-from-left">
                        {selectedResourceIds.map(
                          (option: any, index: number) => {
                            return (
                              <Chip
                                {...getTagProps({ index })}
                                key={index}
                                label={option?.resource_name || ""}
                                onClick={() => handleChipClick(option)}
                              />
                            );
                          }
                        )}
                      </div>
                    </>
                  );
                }}
                renderOption={(props, option, { selected, index }) => {
                  return (
                    <MenuItem
                      {...props}
                      key={`${option?.id}-${option?.created_on}`}
                      value={option?.resource_name || ""}
                      sx={{ justifyContent: "space-between" }}
                    >
                      {option?.resource_name}
                      {selected ? <CheckCircleRounded color="info" /> : null}
                    </MenuItem>
                  );
                }}
                value={
                  resourceIds
                    .map((id) =>
                      (resourcesData || []).find((option) => option.id === id)
                    )
                    .filter((option) => option !== undefined) || null
                }
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="label-text">
                {" "}
                Code<span className="required-asterisk">*</span>
              </label>
              <TextField
                type="text"
                name="code"
                // onChange={handleChangeRule}
                className={`form-control ${errors?.code ? "has-error" : ""}`}
                value={resultData?.code}
                error={!!errors?.code}
                helperText={errors?.code || ""}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6} md={8} lg={8} xl={8}>
              <label className="label-text"> Description</label>
              <TextField
                type="text"
                name="description"
                // placeholder="Ex: Sample Description"
                onChange={handleChangeDescription}
                className={`form-control`}
                value={resultData?.description}
              />
            </Grid>
            <Grid
              item
              xs
              display={{ md: "flex", sm: "block" }}
              justifyContent={"space-between"}
            >
              <Grid container item>
                <Grid item xs={12}>
                  <SelectedResourceList />
                </Grid>
                <Grid
                  item
                  xs={12}
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    columnGap: 1.5,
                  }}
                >
                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={() => {
                      navigate(
                        `/rules/${domainId}/rule-execution/${currentRuleId}`
                      );
                    }}
                    className="btn-orange btn-nostyle btn-blue"
                  >
                    Execute Rule
                  </Button>

                  <Button
                    color="secondary"
                    variant="contained"
                    onClick={() => handleValidations()}
                    className="btn-orange"
                    disabled={!hasChanges}
                  >
                    <SaveOutlinedIcon /> &nbsp; Save
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>

        <Tabs
          value={activeTab}
          onChange={handleChangeTab}
          className="mui-tabs"
          variant="scrollable"
        >
          <Tab label="Domain Comparison Rule" value="domainComparisonRule" />
          <Tab label="Custom Comparison Rule" value="customComparisonRule" />
          <Tab label="Resource Filter" value="resourceFilter" />
          <Tab label="Adhoc Queries" value="adhocQueries" />
          <Tab label="Variables" value="renderVariables" />
        </Tabs>
        <Box>{tabContent[activeTab]}</Box>
      </Box>
      <ToleranceDialog
        openToleranceDialog={openToleranceDialog}
        handleCloseToleranceDialog={handleCloseToleranceDialog}
        //toleranceData={toleranceData}
        //setToleranceData={setToleranceData}
        onSaveToleranceDialog={onSaveToleranceDialog}
      />
      <ConfirmationDialog
        title={"Confirm removing resource"}
        dialogContent={
          "Removing resource will delete existing data, still want to continue?"
        }
        handleCancel={handleCancel}
        openConfirmation={openResourceConfirmation}
        handleConfirm={handleConfirm}
      />
      <CommentBeforeUpdateDialog
        title={"Confirm updating Rule"}
        dialogContent={
          <div>
            <p className="m-0 mb-2">Add a note before updating the Rule</p>
            <textarea
              value={resultData?.comment}
              className={`form-control-1 max-60`}
              onChange={(e) => handleCommentChange(e)}
            />
          </div>
        }
        openConfirmation={openCommentConfirmation}
        handleSaveComment={(e) => handleSaveComment(e)}
        handleCancel={handleCancelComment}
      />
    </Box>
  );
};

export default EditRule;
