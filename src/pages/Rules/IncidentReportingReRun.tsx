import React, { useState, useCallback, useEffect } from "react";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import Loader from "../../components/Molecules/Loader/Loader";
import { GridCellParams } from "@mui/x-data-grid";
import { IconFilterSvg } from "../../common/utils/icons";
import SearchIcon from "@mui/icons-material/Search";
import { GridFilterModel } from "@mui/x-data-grid-premium";

import { Tooltip, Button, IconButton, Box, TextField } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { debounce } from "lodash";

const sampleData: any = [
  {
    run1: [
      {
        rule: "acct1",
        runId: 1,
        key: "acct1",
        Issue: "missing in resource 2",
        status: "closed",
        User: "User A",
      },
      {
        rule: "acct2",
        runId: 2,
        key: "acct2",
        Issue: "missing in resource 1",
        status: "closed",
        User: "User B",
      },
      {
        rule: "acct3",
        runId: 3,
        key: "acct3",
        Issue: "missing in resource 2",
        status: "open",
        User: "User B",
      },
      {
        rule: "acct4",
        runId: 4,
        key: "acct4",
        Issue: "missing in resource 2",
        status: "open",
        User: "User A",
      },
      {
        rule: "acct5",
        runId: 5,
        key: "acct5",
        Issue: "missing in resource 2",
        status: "open",
        User: "User B",
      },
    ],
  },
];

const IncidentReportingReRun = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const navigate = useNavigate();

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 1,
      flex: 0,
      renderCell: (params: any) => null,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
    },
    {
      field: "rule",
      headerName: "Rule",
      width: 100,
      flex: 1,
      renderCell: (params: any) => <span>{params.row.rule}</span>,
    },
    {
      field: "runId",
      headerName: "Run Id",
      width: 100,
      flex: 1,
      renderCell: (params: any) => <span>{params.row.runId}</span>,
    },
    {
      field: "key",
      headerName: "Key",
      width: 100,
      flex: 1,
      renderCell: (params: any) => <span>{params.row.key}</span>,
    },
    {
      field: "Issue",
      headerName: "Issue",
      width: 200,
      flex: 1,
    },
    {
      field: "status",
      headerName: "Status",
      width: 150,
      flex: 1,
    },
    {
      field: "User",
      headerName: "User",
      width: 150,
      flex: 1,
    },
  ];

  // Ensure unique IDs for each row
  const generateUniqueRows = (runData: any, runName: string) => {
    return runData.map((item: any, index: number) => ({
      ...item,
      run: runName,
      id: `${item.key}-${runName}-${index}`, // Create unique ID using key + run name + index
    }));
  };

  const [isFilterHide, setIsFilterHide] = useState(true);

  // Separate filter model states for each table
  const [filterModelRun1, setFilterModelRun1] = useState<GridFilterModel>({
    items: [],
  });
  const [filterModelRun2, setFilterModelRun2] = useState<GridFilterModel>({
    items: [],
  });
  const [filterModelRun3, setFilterModelRun3] = useState<GridFilterModel>({
    items: [],
  });

  const handleFilterBox = () => {
    setIsFilterHide(!isFilterHide);
  };
  const columnWithFilters = (runName: string, filterModel: GridFilterModel) => {
    return columns.map((column) => ({
      ...column,
      sortable: false,
      renderHeader: (params: any) => (
        <CustomHeaderFilter
          column={params.colDef}
          filterValue={
            filterModel.items.find((item) => item.field === column.field)
              ?.value || ""
          }
          onFilterChange={(value: any) =>
            handleFilterChange(runName, column.field, value)
          }
        />
      ),
    }));
  };
  const handleFilterChange = (
    runName: string,
    field: string,
    value: string
  ) => {
    let newFilterItems;

    if (runName === "run1") {
      newFilterItems = filterModelRun1.items.filter(
        (item) => item.field !== field
      );
      if (value) {
        newFilterItems.push({
          field: field,
          operator: "contains",
          value,
        });
      }
      setFilterModelRun1({ items: newFilterItems });
    } else if (runName === "run2") {
      newFilterItems = filterModelRun2.items.filter(
        (item) => item.field !== field
      );
      if (value) {
        newFilterItems.push({
          field: field,
          operator: "contains",
          value,
        });
      }
      setFilterModelRun2({ items: newFilterItems });
    } else if (runName === "run3") {
      newFilterItems = filterModelRun3.items.filter(
        (item) => item.field !== field
      );
      if (value) {
        newFilterItems.push({
          field: field,
          operator: "contains",
          value,
        });
      }
      setFilterModelRun3({ items: newFilterItems });
    }
  };

  return (
    <>
      {isLoading && <Loader isLoading={isLoading} />}

      {/* Render run1 table */}
      <Box className="incident-report-container">
        <IconButton
          className="incident-filter-icon"
          onClick={() => handleFilterBox()}
        >
          <IconFilterSvg />
        </IconButton>
        <DataTable
          dataColumns={columnWithFilters("run1", filterModelRun1)}
          dataRows={generateUniqueRows(sampleData[0].run1, "run1")}
          loading={isLoading}
          dataListTitle={"Run 1"}
          className="dataTable no-radius hide-progress-icon"
          pageSizeOptions={[25]}
          disableColumnMenu={true}
          filterModel={filterModelRun1}
        />
      </Box>
    </>
  );
};

export default IncidentReportingReRun;

const CustomHeaderFilter = ({
  column,
  filterValue,
  onFilterChange,
  isFilterHide,
}: any) => {
  const [inputValue, setInputValue] = useState(filterValue || "");

  const debouncedFilterChange = useCallback(
    debounce((value: any) => onFilterChange(value), 300),
    []
  );

  useEffect(() => {
    debouncedFilterChange(inputValue);
    return () => {
      debouncedFilterChange.cancel();
    };
  }, [inputValue, debouncedFilterChange]);

  return (
    <Box display="flex" flexDirection="column">
      <Box className="header-name">{column?.headerName}</Box>
      <Box className={`textbox-group-wrapper ${isFilterHide ? "hide" : ""}`}>
        <Box className={`textbox-group`}>
          <TextField
            variant="outlined"
            size="small"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={`Filter ${column?.headerName}`}
          />
          <IconButton>
            <SearchIcon />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
};
