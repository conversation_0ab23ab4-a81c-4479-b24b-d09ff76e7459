import React, { useEffect, useState } from "react";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { useLocation, useParams } from "react-router-dom";
import { getRuleResultByRuleId } from "../../services/rulesService";
import useFetchIncidentByExecutionId from "../../hooks/useFetchIncidentByExecutionId";
import RuleDashboard from "./RuleDashboard";
import ComparisonDashboardPieChart from "../../components/Rules/ComparisonDashboardPieChart";
import ComparisonDashboardResults from "../../components/Rules/ComparisonDashboardResults";
import Loader from "../../components/Molecules/Loader/Loader";

const ComparisonResultDashboard = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const location = useLocation();
  const [dashboardData, setDashboardData] = useState<any>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [isTriggereBtnPressed, setIsTriggereBtnPressed] = useState(false);
  const [isCommentBtnPressed, setIsCommentBtnPressed] = useState(false);
  const [currentRuleIncidentData, setCurrentRuleIncidentData] = useState<any>(
    []
  );

  const { ruleResultId: currentRuleResultId } = useParams();
  const getDashboardResultData = async (currentRuleResultId: any) => {
    setIsLoading(true);
    setIsBackdropLoading(true);
    try {
      const result =
        currentRuleResultId > 0 &&
        (await getRuleResultByRuleId({ currentRuleResultId }));
      setDashboardData(result);
    } catch (err) {
      setDashboardData({});
    } finally {
      setIsLoading(false);
      setIsBackdropLoading(false);
    }
  };

  useEffect(() => {
    const isRuleExecutionResponse = location?.state?.isRuleExecutionResponse;
    if (isRuleExecutionResponse !== true) {
      getDashboardResultData(currentRuleResultId);
    } else {
      setDashboardData(location?.state?.rowData);
    }
  }, [location.state?.details]);

  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: dashboardData?.name,
      id: dashboardData?.rule_id,
      url: `/rules/${dashboardData?.domain_id}/view/${dashboardData?.rule_id}`,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null, url: "" });
    };
  }, [dashboardData]);

  const [fetchIncidentData] = useFetchIncidentByExecutionId({
    setIsLoading,
    data: currentRuleResultId,
    isTriggereBtnPressed,
    isCommentBtnPressed,
  });

  useEffect(() => {
    setCurrentRuleIncidentData(fetchIncidentData);
  }, [fetchIncidentData]);
  return (
    <>
      <Loader isLoading={isLoading} />
      {dashboardData?.is_detailed_execution_data_available ? (
        <RuleDashboard
          dashboardData={dashboardData}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          isBackdropLoading={isBackdropLoading}
          currentRuleResultId={currentRuleResultId}
          currentRuleIncidentData={currentRuleIncidentData}
          setIsTriggereBtnPressed={setIsTriggereBtnPressed}
          setIsCommentBtnPressed={setIsCommentBtnPressed}
        />
      ) : (
        <>
          <ComparisonDashboardPieChart
            dashboardData={dashboardData}
            currentRuleResultId={currentRuleResultId}
            currentRuleIncidentData={currentRuleIncidentData}
            setIsLoading={setIsLoading}
            setIsTriggereBtnPressed={setIsTriggereBtnPressed}
            setIsCommentBtnPressed={setIsCommentBtnPressed}
          />
          <ComparisonDashboardResults
            dashboardData={dashboardData}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
          />
        </>
      )}
    </>
  );
};

export default ComparisonResultDashboard;
