import React, { useEffect, useState } from "react";
import FlexBetween from "../../components/FlexBetween";
import {
  Box,
  MenuItem,
  Input,
  Grid,
  TextField,
  Autocomplete,
  Tabs,
  Tab,
  // Stack,
  IconButton,
  Tooltip,
  Button,
} from "@mui/material";
import Backdrop from "@mui/material/Backdrop";
import CircularProgress from "@mui/material/CircularProgress";
import { styled } from "@mui/material/styles";
import ArrowForwardIosSharpIcon from "@mui/icons-material/ArrowForwardIosSharp";
import MuiAccordion, { AccordionProps } from "@mui/material/Accordion";
import MuiAccordionSummary, {
  AccordionSummaryProps,
} from "@mui/material/AccordionSummary";
import MuiAccordionDetails from "@mui/material/AccordionDetails";
import { useNavigate, useParams } from "react-router-dom";
import useFetchResources from "../../hooks/useFetchResources";
import { updateRule } from "../../services/rulesService";
import { toast } from "react-toastify";
import { CheckCircleRounded } from "@mui/icons-material";
import { getResourceColumnsDetail } from "../../services/resourcesService";
import AutoCompleteDomainList from "../../components/Molecules/Domain/AutoCompleteDomainList";
import RulesDataTable from "../../components/DataGrids/RulesDataGrid";
import useFetchDomainById from "../../hooks/useFetchDomainById";
import useFetchRuleById from "../../hooks/useFetchRuleById";
import AdhocQueries from "./AdhocQueries";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { GridColDef } from "@mui/x-data-grid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { useRuleContext } from "../../contexts/RuleContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import iconExecuteWhite from "../../assets/svgs/icon-execute-white.svg";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import ViewVariables from "../../components/Molecules/Resource/ViewVariables";
import { IconBtnEditBase, IconConnector } from "../../common/utils/icons";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
// import { IconExecuteWhiteSvg } from "../../common/utils/icons";

interface PossibleMergeOnColumns {
  [key: string]: string[] | null;
}

interface Dataset {
  resource_id: number;
  merge_on: string[];
  possible_merge_on_columns: PossibleMergeOnColumns;
  merge_type?: string;
}

interface InputData {
  primary_dataset: Dataset;
  secondary_datasets: Dataset[];
}

interface OutputData {
  key: string;
  values: string[];
  id?: number;
}
const ViewRule: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  //ruleContext imports
  const {
    customFilters,
    setCustomFilters,
    selectedResourceIds,
    setSelectedResourceIds,
    customComparisonRules,
    setCustomComparisonRules,
    selectedResourceColumns,
    setSelectedResourceColumns,
  } = useRuleContext();
  const {
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    globalVariables,
    setGlobalVariables,
  } = useRuleResourceContext();
  const navigate = useNavigate();
  const { domainId, id } = useParams();
  //states

  const [activeTab, setActiveTab] = useState<string>("domainComparisonRule");
  const [currentDomainId, setCurrentDomainId] = useState<string | number>(0);
  const [resultData, setResultData] = useState<any>({
    name: "",
    description: "",
    domain_id: null,
    domain_name: "",
    rule_schema: {},
  });
  const [columns, setColumns] = useState<any[]>([]);
  const [tableRows, setTableRows] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedDomainData, setSelectedDomainData] = useState<any>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(true);
  const [domainid, setDomainId] = useState<string | number>(0);
  const [adhocQueryData, setAdhocQueryData] = useState<any>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  //hooks
  const [resourcesData] = useFetchResources({ currentDomainId, setIsLoading });
  const { id: currentRuleId } = useParams();
  const [ruleData] = useFetchRuleById({ setIsLoading, currentRuleId });
  const [domainData] = useFetchDomainById({
    setIsLoading,
    currentDomainId: domainid,
  });
  const cdmData: string[] = [];
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [tagColResult, setTagColResult] = useState<any>([
    {
      key: "",
      values: [],
    },
  ]);
  const [secondaryMergeResource, setSecondaryMergeResource] = useState<any>([]);

  const handleDefaultData = async () => {
    if (ruleData && Object.keys(ruleData)?.length > 0) {
      setResultData(ruleData);
      setCurrentDomainId(ruleData?.domain_id);
      setGlobalVariables(ruleData?.rule_schema?.inline_variables || {});
      // setCustomComparisonRules(ruleData?.rule_schema?.custom_comparison_rules);
      const defaultRules =
        ruleData?.rule_schema?.custom_comparison_rules?.length > 0 &&
        resourcesData &&
        resourcesData?.length > 0
          ? ruleData?.rule_schema?.custom_comparison_rules?.map((item: any) => {
              const { left_operand, right_operand, comparison_type, ...rest } =
                item;
              return {
                ...rest,
                resource: {
                  left_operand,
                  right_operand,
                  comparison_type,
                },
              };
            })
          : [];
      const primaryMergeSecondaryResource = {
        parent_id:
          ruleData?.rule_schema?.merge_rule?.primary_dataset?.resource_id,
        ...ruleData?.rule_schema?.merge_rule?.primary_dataset
          ?.secondary_merge_resource,
      };

      const secondaryMergeSecondaryResource =
        ruleData?.rule_schema?.merge_rule?.secondary_datasets?.map(
          (item: any) => ({
            parent_id: item?.resource_id,
            ...item.secondary_merge_resource,
          })
        ) || [];
      setSecondaryMergeResource([
        primaryMergeSecondaryResource,
        ...secondaryMergeSecondaryResource,
      ]);
      setCustomComparisonRules(defaultRules);
      const newCustomFilters =
        ruleData?.rule_schema?.filter_rules?.length > 0
          ? ruleData?.rule_schema?.filter_rules.map((item: any, index: any) => {
              return {
                key: index,
                ...item,
                id: index,
              };
            })
          : [];
      setCustomFilters(newCustomFilters);
      // handle and updated selected resource columns
      const uniqueRIds = new Set<number>();
      const mergeRuleData = [
        ruleData?.rule_schema?.merge_rule?.primary_dataset,
        ...ruleData?.rule_schema?.merge_rule?.secondary_datasets,
      ];

      mergeRuleData.forEach((item: any) => {
        uniqueRIds.add(item.resource_id);
      });

      const uniqueObjects: any = Array.from(uniqueRIds).map((rId) => {
        const item = mergeRuleData.find(
          (item: any) => item.resource_id === rId
        );

        return {
          rId: rId,
          columns: item?.merge_on || [],
        };
      });
      setSelectedResourceColumns(uniqueObjects);
      if (
        resourcesData &&
        resourcesData?.length > 0 &&
        domainData &&
        Object.keys(domainData)?.length > 0
      ) {
        const orderedResources = ruleData?.rule_schema?.resources || [];
        const resourcePromises = orderedResources.map(
          async (resourceId: any) => {
            const item = resourcesData.find((item) => item.id === resourceId);
            if (item) {
              const resourceColumnDetailId =
                item?.additional_properties?.resource_column_details_id;
              const result: any = await getResourceColumnsDetail({
                resourceColumnDetailId,
              });
              return {
                ...item,
                ...result,
                rId: resourceId,
              };
            }
            return null;
          }
        );

        const Resources = await Promise.all(resourcePromises);
        const filteredResources = Resources.filter(
          (item) => item != null || item != undefined
        );
        const resourceColumnFilteredResources = filteredResources.filter(
          (item) => item?.resource_column_properties != null
        );
        // setResourceIds(ruleData?.rule_schema?.resources);
        if (resourceColumnFilteredResources?.length > 0) {
          setSelectedResourceIds(resourceColumnFilteredResources);
          setSelectedDomainData(domainData);
        }
      }
      setAdhocQueryData(ruleData?.rule_schema?.adhoc_queries || []);
      if (ruleData?.rule_schema?.merge_rule) {
        const transformedData = transformData(
          ruleData?.rule_schema?.merge_rule
        );
        setTagColResult(transformedData);
      }
    }
  };
  const transformData = (inputData: InputData): OutputData[] => {
    const output: OutputData[] = [{ key: "", values: [] }];

    const processDataset = (dataset: Dataset) => {
      if (dataset && dataset.possible_merge_on_columns) {
        for (const [key, values] of Object.entries(
          dataset.possible_merge_on_columns
        )) {
          const uniqueValues = values ? Array.from(new Set([...values])) : [];
          output.push({
            key,
            values: uniqueValues,
            id: dataset.resource_id,
          });
        }
      }
    };

    processDataset(inputData.primary_dataset);
    inputData.secondary_datasets.forEach((dataset) => processDataset(dataset));

    return output;
  };
  useEffect(() => {
    handleDefaultData();
  }, [ruleData, resourcesData, domainData]);
  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: ruleData?.name,
      id: ruleData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [ruleData]);

  useEffect(() => {
    if (resourcesData === undefined) {
      navigate("/rules-list/all");
    } else {
      setDomainId(currentDomainId);
    }
  }, [resourcesData]);

  /**
   * Set current domain id & Update result data state
   * @param data
   */
  const handelChangeDomain = (dataObj: any, data: any) => {
    setSelectedDomainData(data);
    setCurrentDomainId(data?.id);
    setResultData({
      ...resultData,
      domain_id: data?.id,
      domain_name: data?.domain_name,
    });
  };

  useEffect(() => {
    selectedDomainData?.domain_properties?.columns.map((domData: any) => {
      return cdmData.push(domData?.name.trim());
    });
    setAvailColumns(cdmData);
    setAvailColumnsWithResourceDetail(null);
  }, [selectedDomainData]);

  /**
   * On change rule name update result data with new name
   * @param e
   */
  // const handleChangeRuleName = (e: any) => {
  //   setResultData({ ...resultData, name: e.target.value });
  // };

  /**
   * When we select resource
   * Filter resource using id
   * set selected resources in a state
   * set all resource ids in a state
   * @param value
   */

  /**
   * On select or update resource from dropdown
   * set coloums of resources & genrate dynamic rows data
   */
  useEffect(() => {
    if (
      selectedResourceIds.length > 0 &&
      selectedDomainData?.domain_properties?.columns?.length > 0
    ) {
      getColumns();
      tableRowsData();
    }
  }, [selectedResourceIds, selectedDomainData]);

  /**
   * Finally submit a rule & hit api for rule creation
   */

  /**
   * Set table coloums for comparision rules with unique keys
   */
  const getColumns = () => {
    const columns: any = [
      {
        ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
        renderCell: (params: any) => (
          <IconButton
            size="small"
            tabIndex={-1}
            disabled={params?.row?.tolerance_value ? false : true}
            onClick={() => {
              if (expandedRow.includes(params.row.id)) {
                setExpandedRow((prev: any) =>
                  prev.filter((item: any) => item !== params.row.id)
                );
              } else {
                setExpandedRow((prev: any) => [...prev, params.row.id]);
              }
            }}
          >
            {params?.row?.tolerance_value != null ? (
              <ExpandMoreIcon
                sx={{
                  transform: `rotateZ(${
                    expandedRow.includes(params.row.id) ? 180 : 0
                  }deg)`,
                  transition: (theme) =>
                    theme.transitions.create("transform", {
                      duration: theme.transitions.duration.shortest,
                    }),
                }}
                fontSize="inherit"
              />
            ) : null}
          </IconButton>
        ),
      },
      {
        field: "id",
        headerName: "Sr No",
        width: 100,
        maxWidth: 100,
        renderCell: (params: any) => params.row.id + 1,
      },
      {
        field: "domain_name",
        headerName: "Domain Name",
        width: 120,
        flex: 1,
        renderCell: (params: any) => {
          return <LimitChractersWithTooltip value={params?.value} />;
        },
      },
    ];

    selectedResourceIds?.length > 0 &&
      selectedResourceIds.forEach((resourceItem: any) => {
        if (!resourceItem) return;

        columns.push({
          headerName: resourceItem.resource_name,
          field: resourceItem.resource_name,
          width: 120,
          flex: 1,
          renderCell: (params: any) => {
            return <LimitChractersWithTooltip value={params?.value} />;
          },
        });
      });
    columns.push({
      headerName: "Comparison Type",
      field: "comparison_type",
      maxWidth: 120,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <>
            <select
              value={params.row?.comparison_type}
              disabled
              className="form-control-1 input-sm"
            >
              <option value="equals">==</option>
              <option value="not equals">!=</option>
            </select>
          </>
        );
      },
    });
    setColumns(columns);
    setIsLoaded(false);
  };

  /**
   * Set table rows data for unique keys
   * Get domain maping key from selected resource ids
   */
  const tableRowsData = () => {
    if (selectedDomainData?.domain_properties?.columns?.length > 0) {
      const tableRowsData: any = [];
      let resourcesNames: any = [];

      const uniqueColumnsSet = new Set();

      selectedResourceColumns.forEach((res: any) => {
        res.columns.forEach((col: any) => {
          uniqueColumnsSet.add(col);
        });
      });

      // Convert the set back to an array
      const uniqueColumns = Array.from(uniqueColumnsSet);

      selectedDomainData?.domain_properties?.columns.forEach(
        (item: any, idx: any) => {
          const tableRow: any = {
            id: idx + 1,
            domain_name: item?.name,
          };
          selectedResourceIds.forEach((resourceItem: any) => {
            if (!resourceItem) return;
            resourcesNames.push(resourceItem.resource_name);
            const resourceColumnItem =
              resourceItem.resource_column_properties?.resource_columns.find(
                (resourceColumnItem: any) =>
                  resourceColumnItem.domain_column === item?.name
              );
            tableRow[resourceItem.resource_name] =
              resourceColumnItem?.column_name;
            tableRow["column_name"] = resourceColumnItem?.column_name;
          });
          tableRowsData.push(tableRow);
        }
      );

      const uniqueResourcesNames: any[] = [];
      for (const name of resourcesNames) {
        if (!uniqueResourcesNames.includes(name)) {
          uniqueResourcesNames.push(name);
        }
      }
      resourcesNames = uniqueResourcesNames;

      const result = tableRowsData.filter((tableRowItem: any) => {
        const objectValues = Object.values(tableRowItem);
        return resourcesNames.every(
          (resourceName: any) =>
            tableRowItem[resourceName] !== undefined &&
            !objectValues.some((value) => uniqueColumns.includes(value))
        );
      });

      const hasDomainComparisonRules =
        ruleData?.rule_schema?.domain_comparison_rules &&
        ruleData?.rule_schema?.domain_comparison_rules?.length > 0;
      const rows = result.map((item: any, idx: any) => {
        const domainRule =
          hasDomainComparisonRules &&
          ruleData?.rule_schema?.domain_comparison_rules?.find(
            (ruleItem: any) => ruleItem?.name === item?.domain_name
          );
        if (domainRule) {
          return {
            ...item,
            id: idx,
          };
        }
      });
      const filteredRows = rows.filter((row: any) => row?.domain_name);
      setTableRows(
        filteredRows.map((item: any, idx: any) => {
          return {
            ...item,
            id: idx,
          };
        })
      );

      setTableRows((prev) => {
        const updatedRows = prev.map((item) => {
          const matchingRule =
            hasDomainComparisonRules &&
            ruleData?.rule_schema?.domain_comparison_rules?.find(
              (ruleItem: any) => ruleItem.name === item?.domain_name
            );

          if (matchingRule) {
            return {
              ...item,
              tolerance_type: matchingRule.tolerance_type,
              tolerance_value: matchingRule.tolerance_value,
              fallback_value: matchingRule.fallback_value,
              comparison_type: matchingRule.comparison_type,
            };
          }
          return item;
        });

        return updatedRows;
      });
      // }
      setIsLoaded(false);
    }
  };
  const handleChangeTab = (event: any, newValue: any) => {
    setIsLoading(true);
    setActiveTab(newValue);
    setIsLoading(false);
  };

  const tabContent: any = {
    domainComparisonRule: (
      <FlexBetween gap="3rem">
        <RulesDataTable
          dataColumns={columns}
          dataRows={tableRows}
          checkboxSelection={true}
          dataListTitle="Comparison Rule List"
          className="dataTable no-radius"
          loading={false}
        />
      </FlexBetween>
    ),
    customComparisonRule: (
      <ThirdTable
        selectedResourceIds={selectedResourceIds}
        resourcesData={resourcesData}
        customComparisonRules={customComparisonRules}
        setCustomComparisonRules={setCustomComparisonRules}
        setIsLoading={setIsLoading}
        isLoading={false}
      />
    ),
    resourceFilter: (
      <ResourceFilter setIsLoading={setIsLoading} isLoading={false} />
    ),
    adhocQueries: (
      <AdhocQueries
        adhocQueryData={adhocQueryData}
        setAdhocQueryData={setAdhocQueryData}
        isViewOnly={true}
        errors={errors}
        setErrors={setErrors}
      />
    ),
    renderVariables:
      Object.keys(globalVariables).length > 0 ? (
        <div
          className={`accordion-panel text-box-card compact-text-box-card no-radius bottom-radius bg-white`}
        >
          <ViewVariables
            isViewOnly={true}
            error={errors}
            setErrors={setErrors}
          />
        </div>
      ) : (
        <div
          className={`accordion-panel text-box-card compact-text-box-card no-radius bottom-radius bg-white`}
        >
          <p>No Variable Found</p>
        </div>
      ),
  };
  const SelectedResourceList = () => {
    return (
      <div>
        {selectedResourceIds.length > 0 && (
          <div className="selected-resource-list">
            <h4 className="m-0 mb-2">
              Selected Resource column for Merge definition
            </h4>
            <table style={{ width: "100%" }}>
              <tbody>
                {selectedResourceIds.map((resourceItem: any) => {
                  const columnsArr =
                    resourceItem?.resource_column_properties?.resource_columns?.map(
                      (item: any) => item.column_name
                    );
                  return (
                    <>
                      <tr key={resourceItem?.id}>
                        <td
                          style={{
                            paddingTop: "10px",
                            paddingRight: "15px",
                            verticalAlign: "top",
                            width: "260px",
                          }}
                        >
                          <strong>{resourceItem?.resource_name}</strong>
                        </td>
                        <td>
                          {selectedResourceColumns?.length > 0 ? (
                            <Box className="tags-tooltip-parent">
                              {selectedResourceColumns
                                .find(
                                  (item: any) => item.rId === resourceItem?.rId
                                )
                                ?.columns.map((item: any) => {
                                  const resultValues =
                                    tagColResult &&
                                    tagColResult.find(
                                      (obj: any) =>
                                        obj.key === item &&
                                        obj.id === resourceItem?.rId
                                    )?.values;
                                  const resultValuesLength =
                                    resultValues && resultValues.length;
                                  return (
                                    <Box className="chip" key={item.rId}>
                                      {item}
                                      {resultValuesLength >= 1 && (
                                        <Tooltip
                                          placement="bottom"
                                          arrow
                                          componentsProps={{
                                            tooltip: {
                                              sx: {
                                                bgcolor: "#F8F8F8",
                                                padding: "13px 15px",
                                                borderRadius: "8px",
                                                boxShadow:
                                                  "0 8px 4px 4px rgba(0,0,0,.22)",
                                                "& .MuiTooltip-arrow": {
                                                  color: "#F8F8F8",
                                                },
                                              },
                                            },
                                          }}
                                          title={
                                            <Box className="tags-tooltip-parent">
                                              {resultValues &&
                                                resultValues.length >= 1 &&
                                                resultValues?.map(
                                                  (item: any) => {
                                                    return (
                                                      <div className="chip">
                                                        {item}
                                                      </div>
                                                    );
                                                  }
                                                )}
                                            </Box>
                                          }
                                        >
                                          <span
                                            className={`${
                                              resultValuesLength >= 1
                                                ? "counter"
                                                : ""
                                            }`}
                                          >
                                            (
                                            {resultValuesLength > 0
                                              ? resultValuesLength < 10
                                                ? `0${resultValuesLength}`
                                                : resultValuesLength
                                              : ""}
                                            )
                                          </span>
                                        </Tooltip>
                                      )}
                                    </Box>
                                  );
                                })}
                            </Box>
                          ) : (
                            []
                          )}
                        </td>
                      </tr>
                      {secondaryMergeResource.map((item: any) => {
                        if (item.parent_id === resourceItem?.rId) {
                          if (item.resource_id !== undefined) {
                            return (
                              <React.Fragment key={item.parent_id}>
                                <tr>
                                  <td className="position-relative">
                                    <>
                                      <span className="secondary-resource-gap">
                                        {resourcesData &&
                                          resourcesData?.length > 0 &&
                                          resourcesData?.find((rItem: any) => {
                                            return (
                                              rItem.id === item.resource_id
                                            );
                                          })?.resource_name}
                                      </span>

                                      <div className="secondary-resource-sep">
                                        <em className="connector-top">
                                          <IconConnector />
                                        </em>
                                        <span></span>
                                      </div>
                                    </>
                                  </td>
                                  <td>
                                    <div className="tags-tooltip-parent MuiBox-root">
                                      {item?.merge_on &&
                                        item?.merge_on.map((mergeOn: any) => {
                                          return (
                                            <div className="chip MuiBox-root">
                                              {mergeOn || ""}
                                            </div>
                                          );
                                        })}
                                    </div>
                                  </td>
                                </tr>
                              </React.Fragment>
                            );
                          }
                        } else {
                          return null;
                        }
                      })}
                    </>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    );
  };
  return (
    <Box>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={isLoaded}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
      <Box>
        <Box className="text-box-card compact-text-box-card no-radius mb-0 top-radius">
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs>
              <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                <Grid item xs={12} sm={6} md>
                  <label className="label-text text-bold"> Rule Name</label>
                  <div className="form-control word-break-all">
                    {resultData?.name}
                  </div>
                </Grid>
                <Grid item xs={12} sm={6} md>
                  <AutoCompleteDomainList
                    setIsLoading={setIsLoading}
                    handelOnChangeDomain={handelChangeDomain}
                    currentDomainId={currentDomainId}
                    className="form-control-autocomplete read-only"
                    isDisabled={Boolean(true)}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md>
                  <label className="label-text text-bold">
                    Selected Resources
                  </label>
                  <div className="form-control word-break-all">
                    {selectedResourceIds.map(
                      (resourceItem: any, index: number) => {
                        return (
                          <span key={resourceItem?.resource_name + index}>
                            {resourceItem?.resource_name}
                            {index < selectedResourceIds.length - 1 && ", "}
                          </span>
                        );
                      }
                    )}
                  </div>
                </Grid>
                <Grid item xs={12} className="break-columns-grid"></Grid>
                <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
                  <label className="label-text text-bold">Code</label>
                  <div className="form-control word-break-all">
                    {resultData?.code}
                  </div>
                </Grid>
                {resultData?.description && (
                  <Grid item xs={12} sm={12} md={8} lg={8} xl={8}>
                    <label className="label-text text-bold"> Description</label>
                    <div className="form-control word-break-all">
                      {resultData?.description}
                    </div>
                  </Grid>
                )}
                <Grid item xs={12} className="break-columns-grid"></Grid>
              </Grid>
            </Grid>
            <Grid item>
              <Box sx={{ display: "flex", columnGap: 1 }}>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={() => {
                    navigate(`/rules/${domainId}/rule-execution/${id}`);
                  }}
                  className="btn-orange btn-nostyle btn-blue"
                >
                  Execute Rule
                </Button>

                <Tooltip title="Edit Rule" placement="top" arrow>
                  <button
                    className="btn-nostyle icon-btn-edit"
                    onClick={() => navigate(`/rules/${domainId}/edit/${id}`)}
                  >
                    <IconBtnEditBase />
                  </button>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
          <Grid container sx={{ marginTop: 2.5 }}>
            <Grid
              item
              xs={12}
              display={{ sm: "block" }}
              justifyContent={"space-between"}
            >
              <SelectedResourceList />
            </Grid>
          </Grid>
        </Box>

        <Tabs
          value={activeTab}
          onChange={handleChangeTab}
          className="mui-tabs"
          variant="scrollable"
        >
          <Tab label="Domain Comparison Rule" value="domainComparisonRule" />
          <Tab label="Custom Comparison Rule" value="customComparisonRule" />
          <Tab label="Resource Filter" value="resourceFilter" />
          <Tab label="Adhoc Queries" value="adhocQueries" />
          <Tab label="Variables" value="renderVariables" />
        </Tabs>
        <Box>{tabContent[activeTab]}</Box>
      </Box>
    </Box>
  );
};

/**
 * Create third table for custom comparision rules list with resource coloums & constants
 * @param selectedResourceIds, customComparisonRules, setCustomComparisonRules
 * @returns domain comparision rules list
 */
const ThirdTable = ({
  selectedResourceIds: resourcesData = [],
  customComparisonRules,
  setCustomComparisonRules,
  //cdmData,
  setIsLoading,
}: any) => {
  //States
  const [expanded, setExpanded] = useState(null);

  const Accordion = styled((props: AccordionProps) => (
    <MuiAccordion disableGutters elevation={0} square {...props} />
  ))(({ theme }) => ({
    border: `1px solid ${theme.palette.divider}`,
    "&:not(:last-child)": {
      borderBottom: 0,
    },
    "&:before": {
      display: "none",
    },
  }));

  const AccordionSummary = styled((props: AccordionSummaryProps) => (
    <MuiAccordionSummary
      expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: "0.9rem" }} />}
      {...props}
    />
  ))(({ theme }) => ({
    backgroundColor:
      theme.palette.mode === "dark"
        ? "rgba(255, 255, 255, .05)"
        : "rgba(0, 0, 0, .03)",
    flexDirection: "row-reverse",
    "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
      transform: "rotate(90deg)",
    },
    "& .MuiAccordionSummary-content": {
      marginLeft: theme.spacing(1),
    },
  }));

  const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
    padding: theme.spacing(2),
    borderTop: "1px solid rgba(0, 0, 0, .125)",
  }));

  /**
   * Update rule name state when we write rule name on custom rule modal
   * @param e
   */

  const handleAccordionChange = (panel: any) => (_: any, isExpanded: any) => {
    setExpanded(isExpanded ? panel : null);
  };

  useEffect(() => {
    setIsLoading(true);
    if (resourcesData?.length) {
      const columns =
        resourcesData[0]?.resource_column_properties.resource_columns.map(
          (columnItem: any) => columnItem.column_name
        );
    }
    setIsLoading(false);
  }, [resourcesData]);

  return (
    <>
      <FlexBetween
        sx={{
          display: "flex",
          justifyContent: "flex-start",
          alignItem: "center",
          gap: "3rem",
          margin: "10px auto",
        }}
      ></FlexBetween>

      {customComparisonRules?.length > 0 ? (
        <Box sx={{ marginTop: "-20px" }}>
          {customComparisonRules.map((item: any, index: any) => (
            <Accordion
              key={item?.name + index}
              expanded={expanded === `panel${index}`}
              onChange={handleAccordionChange(`panel${index}`)}
              className="add-rule-accordion"
            >
              <AccordionSummary>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    width: "100%",
                    alignItems: "center",
                  }}
                >
                  <span>Rule Name: {item?.name}</span>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                {item?.resource ? (
                  <>
                    <h3 className="rule-h3 mt-0">Resource:</h3>
                    <table className="table-filters mb-3">
                      <tbody>
                        <tr>
                          <td>
                            <Tooltip
                              title={
                                resourcesData.find((rData: any) => {
                                  return (
                                    rData.rId ===
                                    item?.resource?.left_operand?.resource_id
                                  );
                                })?.resource_name
                              }
                              placement="top"
                              arrow
                            >
                              <select
                                value={
                                  item?.resource?.left_operand?.resource_id
                                }
                                className="form-control-1 input-sm disabled-dropdown"
                                disabled
                                style={{
                                  backgroundColor: "#f5f5f5",
                                  color: "grey",
                                }}
                              >
                                {resourcesData?.length > 0 && (
                                  <>
                                    {resourcesData.map((resourceItem: any) => {
                                      if (!resourceItem) return;
                                      return (
                                        <option
                                          key={resourceItem.rId}
                                          value={resourceItem.rId}
                                        >
                                          {resourceItem.resource_name}
                                        </option>
                                      );
                                    })}
                                  </>
                                )}
                              </select>
                            </Tooltip>
                          </td>
                          <td>
                            <input
                              value={item?.resource?.left_operand?.column_name}
                              className="form-control-1 input-sm disabled-dropdown"
                              disabled
                              style={{
                                backgroundColor: "#f5f5f5",
                                color: "grey",
                              }}
                            />
                          </td>
                          <td>
                            <select
                              value={item?.resource?.comparison_type}
                              // onChange={(e: any) => {
                              //   handleOperatorChange(e.target.value, item?.id);
                              // }}
                              className="form-control-1 input-sm disabled-dropdown"
                              disabled
                              style={{
                                backgroundColor: "#f5f5f5",
                                color: "grey",
                              }}
                            >
                              <option value="equals">==</option>
                              <option value="not equals">!=</option>
                              <option value="less than">{"<"}</option>
                              <option value="greater than">{"> "}</option>
                              <option value="less than equals">{"<="}</option>
                              <option value="greater than equals">
                                {">="}
                              </option>
                            </select>
                          </td>
                          <td>
                            <Tooltip
                              title={
                                resourcesData.find((rData: any) => {
                                  return (
                                    rData.rId ===
                                    item?.resource?.right_operand?.resource_id
                                  );
                                })?.resource_name
                              }
                              placement="top"
                              arrow
                            >
                              <select
                                value={
                                  item?.resource?.right_operand?.resource_id
                                }
                                className="form-control-1 input-sm disabled-dropdown"
                                disabled
                                style={{
                                  backgroundColor: "#f5f5f5",
                                  color: "grey",
                                }}
                              >
                                {resourcesData?.length > 0 && (
                                  <>
                                    {resourcesData.map((resourceItem: any) => {
                                      if (!resourceItem) return;
                                      return (
                                        <option
                                          key={resourceItem.rId}
                                          value={resourceItem.rId}
                                        >
                                          {resourceItem.resource_name}
                                        </option>
                                      );
                                    })}
                                  </>
                                )}
                              </select>
                            </Tooltip>
                          </td>
                          <td>
                            <input
                              value={item?.resource?.right_operand?.column_name}
                              className="form-control-1 input-sm"
                              disabled
                              style={{
                                backgroundColor: "#f5f5f5",
                                color: "grey",
                              }}
                            />
                          </td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                  </>
                ) : null}
                {item?.tolerance_value && (
                  <>
                    <h3 className="rule-h3">Tolerance:</h3>
                    <table className="table-filters  mb-3">
                      <tbody>
                        <tr>
                          <td width={"300"}>
                            Tolerance Type : {item?.tolerance_type}
                          </td>
                          <td width={"300"}>
                            Tolerance Value : {item?.tolerance_value}
                          </td>
                          {item?.tolerance_type === "percentage" &&
                            typeof item?.fallback_value === "number" && (
                              <td width={"300"}>
                                Fallback Value : {item.fallback_value}
                              </td>
                            )}
                          <td>
                            <IconButton
                              sx={{ color: "grey" }}
                              onClick={() => {
                                const updatedRules = customComparisonRules.map(
                                  (ruleItem: any) => {
                                    if (ruleItem.id === item?.id) {
                                      return {
                                        ...ruleItem,
                                        tolerance_value: null,
                                        fallback_value: null,
                                        tolerance_type: "",
                                      };
                                    }
                                    return ruleItem;
                                  }
                                );
                              }}
                            ></IconButton>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </>
                )}
                {item?.filter_rules?.length > 0 && (
                  <>
                    <h3 className="rule-h3">Filters:</h3>
                    <table className="table-filters  mb-3">
                      <thead>
                        <tr>
                          <th style={{ width: "30%" }}>Name:</th>
                          <th>SQL Query(s):</th>
                        </tr>
                      </thead>
                      <tbody>
                        {item?.filter_rules?.map(
                          (filterItem: any, idx: any) => {
                            return (
                              <tr key={idx}>
                                <td className={"bg-white"}>
                                  {filterItem?.name ?? ""}
                                </td>
                                <td className={`bg-white valign-center`}>
                                  <div>{filterItem?.sql_query}</div>
                                </td>
                              </tr>
                            );
                          }
                        )}
                      </tbody>
                    </table>
                  </>
                )}
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      ) : (
        <Box
          className="text-box-card compact-text-box-card no-radius bottom-radius"
          sx={{ marginTop: "-20px" }}
        >
          No Custom Comparison Rules available
        </Box>
      )}
    </>
  );
};

const ResourceFilter = ({ setIsLoading, isLoading }: any) => {
  const {
    selectedResourceIds,
    customFilters,
    setCustomFilters,
    showQryModal,
    setShowQryModal,
  } = useRuleContext();

  return (
    <>
      <div
        className={`accordion-panel text-box-card compact-text-box-card no-radius bottom-radius bg-white`}
        style={{ marginBottom: "0" }}
      >
        {customFilters?.length > 0 ? (
          <Box className="MuiCollapse-vertical ">
            <table className="custom-table">
              <thead>
                <tr>
                  <th style={{ width: "30%" }}>Name:</th>
                  <th>SQL Query(s):</th>
                </tr>
              </thead>
              {customFilters?.map((item: any, index: number) => {
                return (
                  <tr key={item?.name + index}>
                    <td>{item?.name}</td>
                    <td>{item?.sql_query}</td>
                  </tr>
                );
              })}
            </table>
          </Box>
        ) : (
          <p>No data found</p>
        )}
      </div>
    </>
  );
};

export default ViewRule;
