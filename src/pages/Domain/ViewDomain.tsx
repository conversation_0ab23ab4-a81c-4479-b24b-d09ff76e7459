import React, { useEffect, useState } from "react";
import FlexBetween from "../../components/FlexBetween";
import {
  Box,
  Grid,
  Select,
  MenuItem,
  Checkbox,
  Input,
  Tooltip,
  Button,
} from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import { useToast } from "../../services/utils";
import DataTable from "../../components/DataGrids/DataGrid";
import { datatypes, dateTimeFormat } from "../../services/constants";
import { useNavigate, useParams } from "react-router-dom";
import useFetchDomainById from "../../hooks/useFetchDomainById";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import btnEdit from "../../assets/svgs/btn-edit-orange.svg";
import CloneDomainDialog from "../../components/Dialogs/CloneDomainDialog";
import { cloneDomain } from "../../services/domainsService";
import { toast } from "react-toastify";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
import { IconBtnEditBase } from "../../common/utils/icons";

const ViewDomain: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { id: currentDomainId } = useParams();
  const [fileData, setFileData] = useState<any[]>([]);
  const [formData, setFormData] = useState<any>({
    domain_name: "",
    domain_code: "",
    domain_desc: "",
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [cloneDomainDialog, setCloneDomainDialog] = useState<boolean>(false);
  const [cloneDomainData, setCloneDomainData] = useState<any>({
    domain_name: "",
    domain_code: "",
    domain_desc: "",
  });

  const [domainData] = useFetchDomainById({ setIsLoading, currentDomainId });

  useEffect(() => {
    if (domainData) {
      setFormData({
        domain_name: domainData?.domain_name,
        domain_code: domainData?.domain_code,
        domain_desc: domainData?.domain_desc,
      });
      setCurrentBreadcrumbPage({
        name: domainData?.domain_name,
        id: domainData?.id,
      });
    }
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [domainData]);

  useEffect(() => {
    if (domainData?.domain_properties?.columns?.length > 0) {
      const newFileData = domainData?.domain_properties?.columns?.map(
        (fileItem: any, index: number) => {
          return {
            ...fileItem,
            id: index,
            key: domainData?.domain_properties?.unique_columns?.includes(
              fileItem.name
            )
              ? true
              : false,
          };
        }
      );

      setFileData(newFileData);
    }
  }, [domainData]);

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      renderCell: (params: any) => <></>,
    },
    {
      field: "name",
      headerName: "Column Name",
      width: 170,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 150,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={
              params.value &&
              (params.value === "Y" ||
                params.value === "y" ||
                params.value === "Yes" ||
                params.value === "yes" ||
                params.value === true)
            }
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={
              params.value &&
              (params.value === "Y" ||
                params.value === "y" ||
                params.value === "Yes" ||
                params.value === "yes" ||
                params.value === true)
            }
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "format",
      headerName: "Format",
      width: 150,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <Tooltip
            title={dateTimeFormat.includes(params.value) ? params.value : null}
            placement="top"
            arrow
          >
            <span>{params.value}</span>
          </Tooltip>
        );
      },
    },
  ];

  let rows: any = [];
  if (fileData) {
    rows = fileData.map((row, key) => ({
      id: row.id,
      name: row.name,
      datatype: row.datatype,
      mandatory: row.mandatory,
      key: row.key,
      format: row.format,
    }));
  }

  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleCloseCloneDomain = () => {
    setCloneDomainDialog(false);
    setCloneDomainData({});
  };
  const onSaveCloneDomain = () => {
    const reqBody = {
      domain_id: Number(currentDomainId),
      domain_name: cloneDomainData.domain_name,
      domain_code: cloneDomainData.domain_code,
      domain_desc: cloneDomainData.domain_desc || "",
    };
    cloneDomain(reqBody)
      .then((response) => {
        showToast("Domain clone successfully!", "success");
        navigate(`/domain/view/${response?.id}`);
      })
      .catch((error) => {
        showToast(`Cannot create clone Domain`, "error");
      });
    handleCloseCloneDomain();
  };
  return (
    <Box>
      <Box className="text-box-card compact-text-box-card">
        <Grid container rowSpacing={1.5} columnSpacing={2.5}>
          <Grid item xs={12} sm={6} md={6} lg={3} xl={3}>
            <label className="label-text text-bold"> Domain Name</label>
            <div className="form-control word-break-all">
              {formData.domain_name || "N/A"}
            </div>
          </Grid>
          <Grid item xs={12} sm={6} md={6} lg={3} xl={3}>
            <label className="label-text text-bold"> Domain Code</label>
            <div className="form-control word-break-all">
              {formData.domain_code || "N/A"}
            </div>
          </Grid>
          {formData.domain_desc && (
            <Grid item xs={12} sm md lg xl>
              <label className="label-text text-bold"> Description</label>
              <div className="form-control word-break-all">
                {formData.domain_desc || "N/A"}
              </div>
            </Grid>
          )}
          <Grid
            item
            xs
            sx={{
              justifyContent: "flex-end",
              display: "flex",
              alignItems: "center",
              columnGap: "16px",
            }}
          >
            <Button
              variant="contained"
              color="secondary"
              onClick={() => setCloneDomainDialog(true)}
              className="btn-orange btn-nostyle"
            >
              clone Domain
            </Button>
            <button
              className="btn-nostyle icon-btn-edit"
              onClick={() => navigate(`/domain/edit/${currentDomainId}`)}
            >
              <IconBtnEditBase />
            </button>
          </Grid>
        </Grid>
      </Box>
      <Box>
        <FlexBetween gap="3rem">
          <DataTable
            dataColumns={columns}
            dataRows={fileData}
            checkboxSelection={false}
            loading={isLoading}
            dataListTitle={"Columns List"}
            className="dataTable no-radius"
          />
        </FlexBetween>
      </Box>
      <CloneDomainDialog
        cloneDomainDialog={cloneDomainDialog}
        handleCloseCloneDomain={handleCloseCloneDomain}
        cloneDomainData={cloneDomainData}
        setCloneDomainData={setCloneDomainData}
        onSaveCloneDomain={onSaveCloneDomain}
      />
    </Box>
  );
};

export default ViewDomain;
