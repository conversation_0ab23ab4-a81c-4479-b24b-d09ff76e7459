import React, { useEffect, useMemo, useState } from "react";
import { Box, Grid, IconButton, InputBase, Tooltip } from "@mui/material";
import { useToast } from "../../services/utils";
import FlexBetween from "../../components/FlexBetween";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import ButtonComponent from "../../components/Button.Component";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { deleteDomain } from "../../services/domainsService";
import { Search } from "@mui/icons-material";
import iconEdit from "../../assets/svgs/icon-edit-orange.svg";
import iconEye from "../../assets/svgs/icon-eye-orange.svg";
import iconViewRule from "../../assets/svgs/iconViewRule.svg";
import iconViewResource from "../../assets/svgs/iconViewResource.svg";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import {
  IconAudit,
  IconDeleteBlueSvg,
  IconEditBase,
  IconExportIconBlue,
  IconEyeBase,
  IconReportsSvg,
  IconViewResource,
  IconViewRuleBase,
} from "../../common/utils/icons";
import {
  downloadDomainExportFile,
  getCurrentDateAndLast7Days,
  getFormattedDateTime,
} from "../../services/utils";
import useFetchPaginatedDomain from "../../hooks/useFetchPaginatedDomain";
import { paginatedResponseFormat } from "../../services/constants";
import Loader from "../../components/Molecules/Loader/Loader";
import DropdownMenu from "../../components/Molecules/DropdownMenu/DropdownMenu";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
const DomainTest: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  // states
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [domainsData, setDomainData] = useState<any>(paginatedResponseFormat);
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  // hooks
  const [fetchedDomainsData] = useFetchPaginatedDomain({
    setIsLoading,
    page,
    pSize,
  });

  const [searchQuery, setSearchQuery] = useState("");
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const handelClickEvent = () => {
    navigate("/domain/add");
  };
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      renderCell: (params: any) => <></>,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
    },
    {
      field: "id",
      headerName: "Id",
      width: 100,
      renderCell: (params: any) => {
        const handleClickDomain = () => {
          navigate(`/resource/${params.id}`);
        };
        return (
          <span style={{ cursor: "pointer" }} onClick={handleClickDomain}>
            {params.id}
          </span>
        );
      },
    },
    {
      field: "domain_name",
      headerName: "Domains",
      width: 100,
      flex: 1,
      renderCell: (params: any) => {
        const handleClickDomain = () => {
          navigate(`/resource/${params.id}`);
        };
        return (
          <LimitChractersWithTooltip
            value={params?.value}
            onClick={handleClickDomain}
          />
        );
      },
    },
    {
      field: "domain_desc",
      headerName: "Description",
      flex: 1,
      renderCell: (params: any) => {
        const handleClickDomain = () => {
          navigate(`/resource/${params.id}`);
        };
        return (
          <LimitChractersWithTooltip
            value={params?.value}
            onClick={handleClickDomain}
          />
        );
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      flex: 1,
      renderCell: (params: any) => {
        const handleClickDomain = () => {
          navigate(`/resource/${params.id}`);
        };
        return (
          <LimitChractersWithTooltip
            value={params?.value?.username}
            onClick={handleClickDomain}
          />
        );
      },
    },
    {
      field: "updated_on",
      headerName: "Last Modified",
      flex: 1,
      renderCell: (params: any) => {
        const handleClickDomain = () => {
          navigate(`/resource/${params.id}`);
        };

        return (
          <span style={{ cursor: "pointer" }} onClick={handleClickDomain}>
            {getFormattedDateTime(params.value)}
            {/* {params.value} */}
          </span>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 130,
      renderCell: (params: any) => {
        const handleDelete = async () => {
          setIsLoading(true);
          await deleteDomain(params.row.id);
          setDomainData((prev: any) => ({
            ...prev,
            items: prev?.items?.filter(
              (prevItem: any) => prevItem.id !== params.row.id
            ),
          }));
          // const updatedDomainData = domainsData?.items?.filter(
          //   (domain: any) => domain.id !== params.row.id
          // );
          // setFileData(updatedDomainData);
          // setDomainData(updatedDomainData);
          setIsLoading(false);
          showToast(
            `${params.row.domain_name} deleted successfully`,
            "success"
          );
        };
        const { last7DaysDate } = getCurrentDateAndLast7Days();
        return (
          <>
            <Tooltip title="View Domain" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => navigate(`/domain/view/${params.row.id}`)}
              >
                <IconEyeBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit Domain" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => navigate(`/domain/edit/${params.row.id}`)}
              >
                <IconEditBase />
              </IconButton>
            </Tooltip>
            <DropdownMenu
              dropdownMenuItems={[
                <IconButton
                  key="audit-domain"
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    navigate(`/domain/auditDomain/${params.row.id}`)
                  }
                >
                  <IconAudit /> Audit Domain
                </IconButton>,
                <IconButton
                  key="view-linked-resources"
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => {
                    navigate(`/resource/${params.id}`);
                  }}
                >
                  <IconViewResource />
                  View Linked Resources
                </IconButton>,
                <IconButton
                  key="view-linked-rules"
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => {
                    navigate(`/rules-list/${params.id}`);
                  }}
                >
                  <IconViewRuleBase />
                  View Linked Rules
                </IconButton>,
                <IconButton
                  key="recent-comparisons"
                  onClick={() => {
                    navigate(
                      `/rules-execution-history?domainId=${params.row.id}&fromDate=${last7DaysDate}`
                    );
                  }}
                  size="small"
                  className="datagrid-action-btn"
                >
                  <IconReportsSvg />
                  Recent Comparisons
                </IconButton>,
                <IconButton
                  key="recent-comparisons"
                  onClick={() => {
                    navigate(
                      `/validation-execution-history?domainId=${params.row.id}&fromDate=${last7DaysDate}`
                    );
                  }}
                  size="small"
                  className="datagrid-action-btn"
                >
                  <IconReportsSvg />
                  Recent Validations
                </IconButton>,
                <IconButton
                  key="export"
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    downloadDomainExportFile(
                      params.row.id,
                      setIsDownloadLoading
                    )
                  }
                >
                  <IconExportIconBlue />
                  Export
                </IconButton>,
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleDelete()}
                >
                  <IconDeleteBlueSvg />
                  Delete Domain
                </IconButton>,
              ]}
            />
          </>
        );
      },
    },
  ];
  // for update domains list data
  useEffect(() => {
    if (fetchedDomainsData) {
      // setFileData(fetchedDomainsData);
      setDomainData(fetchedDomainsData);
    }
  }, [fetchedDomainsData]);

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  const filteredData = useMemo(() => {
    return domainsData?.items?.filter((domain: any, index: number) => {
      return domain.domain_name
        .toLowerCase()
        .includes(searchQuery?.toLowerCase());
    });
  }, [domainsData?.items, searchQuery]);

  return (
    <Box>
      {(isLoading || isDownloadLoading) && (
        <Loader isLoading={isLoading || isDownloadLoading} />
      )}
      <Grid
        container
        justifyContent="space-between"
        alignItems="center"
        className="text-box-card compact-text-box-card"
      >
        <Grid item>
          <label className="label-text">Search Domain Name</label>
          <form className="common-search-panel">
            <InputBase
              placeholder="Search..."
              className="search-textbox"
              value={searchQuery}
              onChange={handleSearchInputChange}
              onKeyDown={(event) => {
                if (event.key === "Enter") {
                  event.preventDefault();
                }
              }}
            />
            <IconButton className="search-icon">
              <Search className="svg_icons" />
            </IconButton>
          </form>
        </Grid>
        <Grid item xs="auto">
          <label className="label-text">&nbsp;</label>
          <FlexBetween sx={{ columnGap: 2 }}>
            {/* <Button
              onClick={() =>
                navigate(`/domain/import-entity?type=domain`, {
                  state: {
                    import_defination_name: "domain",
                  },
                })
              }
              className="btn-orange btn-dark"
              sx={{ columnGap: 1 }}
            >
              <IconImportFileWhite />
              Import
            </Button> */}
            <ButtonComponent
              handelClickEvent={handelClickEvent}
              className="btn-orange"
            >
              <>
                <AddSharpIcon sx={{ marginRight: "4px" }} /> Domain
              </>
            </ButtonComponent>
          </FlexBetween>
        </Grid>
      </Grid>
      {/* import  button component */}

      {/* import data table for resource list  */}
      <FlexBetween>
        <DataTable
          dataColumns={columns}
          // dataRows={fileData}
          dataRows={filteredData || []}
          loading={isLoading}
          dataListTitle={"Domain List"}
          className="dataTable no-radius hide-progress-icon"
          paginationMode="server"
          rowCount={domainsData?.total || 0}
          pageSizeOptions={[25]}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={358}
        />
      </FlexBetween>
    </Box>
  );
};

export default DomainTest;
