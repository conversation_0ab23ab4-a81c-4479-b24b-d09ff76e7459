import React, { useEffect, useMemo, useState } from "react";
import { GridColDef } from "@mui/x-data-grid";
import { useLocation } from "react-router-dom";
import ConsolidateDataTable from "../../components/DataGrids/ConsolidateDataGrid";
import {
  Box,
  Grid,
  IconButton,
  InputBase,
  MenuItem,
  Select,
} from "@mui/material";
import { Search } from "@mui/icons-material";
import useFetchValidationResults from "../../hooks/useFetchValidationResults";
import dayjs, { Dayjs } from "dayjs";
import {
  getSanatizedCode,
  downloadValidationSummaryOutputFile,
  downloadValidationDetailedOutputFile,
} from "../../services/utils";
import RunNameDropdown from "../../components/Molecules/RunName/RunNameDropDown";
import CustomDatePicker from "../../components/Molecules/Datepicker/CustomDatePicker";
import { showConsolidateReportSchema } from "../../schemas";
import ReadMoreLess from "../../components/ReadMoreLess";
import Loader from "../../components/Molecules/Loader/Loader";
import {
  IconCheckCircleIconGreenSvg,
  IconCrossCircleIconSvg,
} from "../../common/utils/icons";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const ValidationExecutionResults = () => {
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);

  const [isloading, setIsLoading] = useState<boolean>(false);
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>({
    reports: { items: [], total: 0, page: 0, size: 0, pages: 0 },
    filteredDomains: "",
    runInstance: {},
  });
  const [runName, setRunName] = useState<string>(
    searchParams.get("runName") || "Legacy"
  );
  const [reportType, setReportType] = useState<string>(
    searchParams.get("reportType") || "summary"
  );

  const initialRunDate = searchParams.get("runDate")
    ? dayjs(searchParams.get("runDate"))
    : dayjs().subtract(1, "day");
  const [runDate, setRunDate] = React.useState<Dayjs | null>(initialRunDate);

  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const [validationReports] = useFetchValidationResults({
    setIsLoading,
    runName,
    runDate,
    reportType,
    page,
    pSize,
  });
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    setFileData((prevFormData: any) => ({
      ...prevFormData,
      reports: validationReports,
    }));
  }, [validationReports, runDate, searchQuery]);

  /**
   * Set table coloums for execution history
   */

  const detailColumns: GridColDef[] = [
    {
      field: "resource_name",
      headerName: "Resource Name",
      flex: 1,
      renderCell: (params: any) => {
        return <span style={{ cursor: "pointer" }}>{params.value}</span>;
      },
    },
    {
      field: "resource_type",
      headerName: "System",
      width: 120,
      flex: 1,
    },
    {
      field: "key_columns",
      headerName: "Key Columns",
      flex: 1,
      renderCell: (params: any) => {
        return getSanatizedCode(params.value);
      },
    },
    {
      field: "column_name",
      headerName: "Column Name",
      flex: 1,
      renderCell: (params: any) => {
        return getSanatizedCode(params.value);
      },
    },
    {
      field: "validation_severity",
      headerName: "Validation Severity",
      flex: 1,
      renderCell: (params: any) => {
        return getSanatizedCode(params.value);
      },
    },
    {
      field: "error_message",
      headerName: "Error Message",
      flex: 1,
      renderCell: (params: any) => {
        return getSanatizedCode(params.value);
      },
    },
  ];
  const summaryColumns: GridColDef[] = [
    {
      field: "resource_name",
      headerName: "Resource Name",
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "resource_type",
      headerName: "System",
      width: 120,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "duplicate",
      headerName: "Duplicate",
      flex: 1,
    },
    {
      field: "records",
      headerName: "Records",
      flex: 1,
    },
    {
      field: "errors",
      headerName: "Errors",
      flex: 1,
    },
    {
      field: "invalid",
      headerName: "Invalid",
      flex: 1,
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => {
        return (
          <>
            {params?.value ? (
              <Box title="Success">
                <IconCheckCircleIconGreenSvg width={21} height={21} />
              </Box>
            ) : (
              <Box title="Fail">
                <IconCrossCircleIconSvg width={21} height={21} />
              </Box>
            )}
          </>
        );
      },
    },
  ];

  const validateField = async (name: any, value: any) => {
    try {
      const partialReferenceData = { ...fileData, [name]: value };
      await showConsolidateReportSchema.validateAt(name, partialReferenceData);
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };
  const handleSearchSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    // Filter domains based on the searchQuery
    const filteredReports = fileData?.items?.filter((report: any) => {
      return report.name.toLowerCase().includes(searchQuery.toLowerCase());
    });
    //You can set the filtered domains as a result in your component state
    setFileData((prevFormData: any) => ({
      ...prevFormData,
      reports: filteredReports,
    }));
  };
  const handleChangeRunName = (e: any, value: any) => {
    setFileData((prevFormData: any) => ({
      ...prevFormData,
      runInstance: value,
    }));
    setRunName(value?.run_name);
    validateField("run_name", value?.run_name);
  };

  const handleChangeRunDate = (value: any) => {
    setRunDate(value);
    setFileData((prevFormData: any) => ({
      ...prevFormData,
      runInstance: value,
    }));
  };

  const filteredData = useMemo(() => {
    return fileData?.reports?.items?.filter((report: any, index: number) => {
      report.id = index;
      return report.resource_name
        .toLowerCase()
        .includes(searchQuery?.toLowerCase());
    });
  }, [fileData?.reports?.items, searchQuery]);
  const runDateToString = (runDate ?? dayjs().subtract(1, "day")).format(
    "YYYY-MM-DD"
  );
  return (
    <Box>
      {(isloading || isDownloadLoading) && (
        <Loader isLoading={isloading || isDownloadLoading} />
      )}
      <Box className="text-box-card list-page-card">
        <Grid container rowSpacing={3} columnSpacing={3}>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <RunNameDropdown
              setIsLoading={setIsLoading}
              handleChangeRunName={handleChangeRunName}
              currentRunName={runName}
              className="form-control-autocomplete"
              name="run_name"
              required
              error={!!errors.run_name}
              helperText={errors.run_name || ""}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text">
              Run Date<span className="required-asterisk">*</span>
            </label>

            <CustomDatePicker
              value={runDate}
              onChange={(newValue) => handleChangeRunDate(newValue)}
            />
          </Grid>
          <Grid item xs={9} md={3}>
            <label className="label-text">
              Report Type<span className="required-asterisk">*</span>
            </label>
            <Select
              required
              labelId="report-type-label"
              id="report-type"
              value={reportType}
              displayEmpty
              className="form-control-1 alternative-1"
              onChange={(e) => {
                setReportType(e.target.value);
              }}
              name="reportType"
              error={!!errors.report_type}
            >
              <MenuItem value="" disabled>
                Select...
              </MenuItem>
              <MenuItem value="detail">Detail</MenuItem>
              <MenuItem value="summary">Summary</MenuItem>
            </Select>
            <span className="validation-error">
              {errors.report_type && <span>{errors.report_type}</span>}
            </span>
          </Grid>
          <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
            <label className="label-text">Search Resource </label>
            <form className="common-search-panel">
              <InputBase
                placeholder="Search..."
                className="search-textbox"
                value={searchQuery}
                onChange={handleSearchInputChange}
                onKeyDown={(event) => {
                  if (event.key === "Enter") {
                    event.preventDefault();
                  }
                }}
              />
              <IconButton className="search-icon">
                <Search className="svg_icons" />
              </IconButton>
            </form>
          </Grid>
        </Grid>
      </Box>

      {/* {fileData && fileData?.reports?.length > 0 && ( */}
      <ConsolidateDataTable
        dataColumns={reportType === "summary" ? summaryColumns : detailColumns}
        dataRows={filteredData}
        loading={isloading}
        dataListTitle={"Validation Summary List"}
        isExportButtonRequired={true}
        className="dataTable no-radius hide-progress-icon"
        checkboxSelection={false}
        rowCount={fileData?.reports?.total || 0}
        pageSizeOptions={[25]}
        paginationModel={{
          page: page - 1,
          pageSize: pSize,
        }}
        onPaginationModelChange={(params: any) => {
          if (params.pageSize !== pSize || params.page !== page - 1) {
            setPage(params.page + 1);
            setPSize(params.pageSize);
          }
        }}
        downloadFromAPIFile={() =>
          reportType === "summary"
            ? downloadValidationSummaryOutputFile(
                runName,
                runDateToString,
                setIsDownloadLoading
              )
            : downloadValidationDetailedOutputFile(
                runName,
                runDateToString,
                setIsDownloadLoading
              )
        }
        singlePageMaxHeightDiff={368}
      />
    </Box>
  );
};

export default ValidationExecutionResults;
