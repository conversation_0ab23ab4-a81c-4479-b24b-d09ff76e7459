import React, { useEffect, useState } from "react";
import { GridColDef, GridExpandMoreIcon } from "@mui/x-data-grid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import "../../styles/filter-sidebar.scss";
import { IconButton, Tooltip } from "@mui/material";
import {
  downloadExecutionHistoryFile,
  getFormatedDate,
  getFormattedDateTimeWithAMPM,
  getFormattedTime,
  useToast,
} from "../../services/utils";
import EHistoryDataTable from "../../components/DataGrids/EHistoryDataGrid";
import { useNavigate } from "react-router-dom";
import { Box } from "@mui/material";
import useFetchValidationHistory from "../../hooks/useFetchValidationHistory";
import dayjs, { Dayjs } from "dayjs";
import { enableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";
import Loader from "../../components/Molecules/Loader/Loader";
import {
  IconIncidentAcknowledgeSvg,
  IconIncidentTriggeredSvg,
  IconIncidentResolveSvg,
  IconFilterSvg,
  IconReRunBlue,
  IconEyeBase,
  IconDeleteBlueSvg,
  IconAbortBlue,
} from "../../common/utils/icons";

import useFetchIncidentByExecutionId from "../../hooks/useFetchIncidentByExecutionId";
import FilterSearch from "../../components/Molecules/FilterSearch/FilterSearch";
import ConsolidatedReportsFilter from "../../components/Molecules/FilterSearch/ConsolidatedReportsFilter";
import {
  abortValidationExecutionbyID,
  deleteValidationExecutionbyID,
} from "../../services/resourcesService";
import ConfirmationDialog from "../../components/Dialogs/ConfirmationDialog";
import DashboardJobstatusIcon from "../../common/utils/DashboardJobstatusIcon";
import useSyncNotifications from "../../hooks/useSyncNotifications";
// import useWebSocket from "../../hooks/useWebSocket"; // No longer needed
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

interface ValidationHistoryData {
  domainId?: number | null;
  resourceId: number | null;
  fromDate: string | undefined;
}
const ValidationHistory = () => {
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const queryParams = new URLSearchParams(window.location.search);

  const navigate = useNavigate();
  const { showToast } = useToast();
  const [isloading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>({
    items: [],
    total: 0,
    page: 0,
    size: 0,
    pages: 0,
  });
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [isConsolidatedFilterVisible, setIsConsolidatedFilterVisible] =
    useState(false);

  //incidents
  // const [incidentData, setIncidentData] = useState<any>([]);
  // const [incidentResultIds, setIncidentResultIds] = useState();

  // Get handleStorageChange from useSyncNotifications
  // This will automatically sync with notifications from the context
  useSyncNotifications(setFileData, fileData);

  const [searchFilterData, setSearchFilterData] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    run_name: "",
    run_date: dayjs().subtract(1, "day"),
    report_type: "",
  });
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  const [
    openValidationExecutionConfirmation,
    setOpenValidationExecutionConfirmation,
  ] = useState(false);

  const [
    openAbortValidationExecutionConfirmation,
    setOpenAbortValidationExecutionConfirmation,
  ] = useState(false);
  const [abortExecutionId, setAbortExecutionId] = useState<number | null>(null);

  const [deleteExecutionId, setDeleteExecutionId] = useState<number | null>(
    null
  );

  const [validationHistoryParams] = useState<ValidationHistoryData>({
    domainId: Number(queryParams.get("domainId")),
    resourceId: Number(queryParams.get("resourceId")),
    fromDate: queryParams.get("fromDate")?.toString(),
  });

  // const [fetchIncidentData] = useFetchIncidentByExecutionId({
  //   setIsLoading,
  //   data: incidentResultIds,
  // });

  // useEffect(() => {
  //   setIncidentData(fetchIncidentData);
  // }, [fetchIncidentData, page]);

  const [validationHistoryReports] = useFetchValidationHistory({
    setIsLoading: setIsBackdropLoading,
    page,
    pSize,
    resourceId: validationHistoryParams.resourceId,
    domainId: validationHistoryParams.domainId,
    fromDate: validationHistoryParams.fromDate,
    searchFilterData,
  });

  const handleValidationExecutionList = (data: any) => {
    if (data && data.items) {
      const itemsWithIds = data.items.map((row: any, index: number) => ({
        ...row,
        id: index + 1,
        resourceResultId: row.id,
        execution_date: getFormatedDate(row.execution_time),
        execution_name:
          row?.complete_validation_params?.validation_request_body
            ?.execution_name,
      }));
      return { ...data, items: itemsWithIds };
    }
    return data;
  };
  useEffect(() => {
    if (
      validationHistoryReports &&
      validationHistoryReports.items &&
      validationHistoryReports.items.length > 0
    ) {
      // const newData = validationHistoryReports.items.map((row: any) => ({
      //   ...row,
      //   execution_date: getFormatedDate(row.execution_time),
      //   execution_name:
      //     row?.complete_validation_params?.validation_request_body
      //       ?.execution_name,
      // }));

      // const resultIdArr = newData.map((row: any) => row.id);
      // setIncidentResultIds(resultIdArr);
      setFileData(validationHistoryReports);
    }
  }, [validationHistoryReports]);

  // useEffect(() => {
  //   const updatedFileData = handleValidationExecutionList(
  //     validationHistoryReports
  //   );

  //   if (updatedFileData?.items?.length > 0) {
  //     const newData = updatedFileData.items.map((file: any) => {
  //       const fileItem: any =
  //         incidentData &&
  //         incidentData.length > 0 &&
  //         incidentData.find((incidentItem: any) => {
  //           return (
  //             Number(incidentItem?.ExecutionId) ===
  //             Number(file?.resourceResultId)
  //           );
  //         });
  //       if (fileItem) {
  //         return {
  //           ...file,
  //           IncidentStatus: fileItem?.IncidentStatus,
  //         };
  //       }
  //       return file;
  //     });
  //     setFileData({ ...updatedFileData, items: newData });
  //   }
  // }, [incidentData, page]);

  // useEffect(() => {
  //   const handleStorageChange = () => {
  //     const storedNotifications = localStorage.getItem("notifications");
  //     if (storedNotifications && fileData.items?.length > 0) {
  //       try {
  //         const notifications = JSON.parse(storedNotifications);
  //         setFileData((prevFileData: any) => {
  //           const updatedItems = prevFileData.items.map((item: any) => {
  //             const matchingNotification = notifications.find(
  //               (notification: any) =>
  //                 notification.execution_id === item.resourceResultId
  //             );

  //             if (matchingNotification?.execution_status) {
  //               return {
  //                 ...item,
  //                 job_status: matchingNotification.execution_status,
  //               };
  //             }
  //             return item;
  //           });
  //           // Only update state if there are actual changes
  //           const hasChanges = updatedItems.some(
  //             (newItem: any, index: number) =>
  //               newItem.job_status !== prevFileData.items[index].job_status
  //           );
  //           return hasChanges
  //             ? { ...prevFileData, items: updatedItems }
  //             : prevFileData;
  //         });
  //       } catch (error) {
  //         console.error(
  //           "Error parsing notifications from localStorage:",
  //           error
  //         );
  //       }
  //     }
  //   };

  //   // Add event listener for storage changes from other tabs
  //   window.addEventListener("storage", handleStorageChange);

  //   // Create an interval to check for changes in the current tab
  //   const checkInterval = setInterval(handleStorageChange, 1000);

  //   // Initial check
  //   handleStorageChange();

  //   // Cleanup
  //   return () => {
  //     window.removeEventListener("storage", handleStorageChange);
  //     clearInterval(checkInterval);
  //   };
  // }, [validationHistoryReports?.items]);

  /**
   * Set table coloums for execution history
   */
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => (
        <IconButton size="small" tabIndex={-1} className="groupby-expand-icon">
          <GridExpandMoreIcon
            sx={{
              transform: `rotateZ(${
                expandedRow.includes(params.row.id) ? 180 : 0
              }deg)`,
              transition: (theme) =>
                theme.transitions.create("transform", {
                  duration: theme.transitions.duration.shortest,
                }),
            }}
            fontSize="inherit"
          />
        </IconButton>
      ),
    },
    {
      field: "domain_name",
      headerName: "Domain Name",
      flex: 1,
      minWidth: 120,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "resource_type",
      headerName: "System",
      flex: 1,
      minWidth: 140,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },

    {
      field: "id",
      headerName: "Execution Id",
      flex: 1,
      minWidth: 80,
      groupable: false,
      renderCell: (params: any) => {
        return (
          <Tooltip
            title={`Run Id: ${params?.row?.run_id}`}
            placement="top"
            arrow
          >
            <span style={{ cursor: "pointer" }}>{params.value}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "execution_name",
      headerName: "Execution Name",
      flex: 1,
      minWidth: 160,
      groupable: false,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "resource_id",
      headerName: "Resource Id",
      flex: 1,
      minWidth: 80,
      groupable: false,
      renderCell: (params: any) => {
        return <span style={{ cursor: "pointer" }}>{params.value}</span>;
      },
    },
    {
      field: "resource_name",
      headerName: "Resource Name",
      flex: 1,
      minWidth: 200,
      groupable: false,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "run_name",
      headerName: "Run Name",
      flex: 1,
      minWidth: 100,
      groupable: false,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "execution_time",
      headerName: "Execution Date & Time",
      minWidth: 180,
      groupable: false,
      renderCell: (params) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const executionTime = params?.value?.execution_time || params.value;
        const formattedTime = getFormattedDateTimeWithAMPM(executionTime);
        return (
          <div>
            <div>{formattedTime}</div>
          </div>
        );
      },
    },
    {
      field: "job_status",
      headerName: "Execution Status",
      flex: 1,
      minWidth: 100,
      headerAlign: "center",
      align: "center",
      groupable: false,
      renderCell: (params: any) => (
        <DashboardJobstatusIcon status={params?.row?.job_status} />
      ),
    },
    {
      field: "action",
      headerName: "Action",
      align: "left",
      headerAlign: "left",
      minWidth: 110,
      groupable: false,
      renderCell: (params) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const handleViewClick = (incidentTab?: any) => {
          const basePath = `/validation-execution-history/validate-result/${params?.row?.id}`;
          const navigatePath = incidentTab
            ? `${basePath}#incidentTab`
            : basePath;

          navigate(navigatePath, {
            state: {
              response: params?.row,
            },
          });
        };
        const handleAbortValidation = () => {
          setOpenAbortValidationExecutionConfirmation(true);
          setAbortExecutionId(params?.row?.id);
        };

        return (
          <>
            {params?.row?.job_status === "completed" ? (
              <Tooltip title="View Dashboard" placement="top" arrow>
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleViewClick()}
                >
                  <IconEyeBase />
                </IconButton>
              </Tooltip>
            ) : (
              <Tooltip title="Abort Validation" placement="top" arrow>
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleAbortValidation()}
                >
                  <IconAbortBlue />
                </IconButton>
              </Tooltip>
            )}
            {params?.row?.IncidentStatus && (
              <Tooltip title="View Incidents" placement="top" arrow>
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleViewClick("incidentTab")}
                >
                  {params?.row?.IncidentStatus === "Triggered" ? (
                    <IconIncidentTriggeredSvg />
                  ) : params?.row?.IncidentStatus === "Acknowledged" ? (
                    <IconIncidentAcknowledgeSvg />
                  ) : params?.row?.IncidentStatus === "Resolve" ? (
                    <IconIncidentResolveSvg />
                  ) : null}
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Re-Run" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  navigate(
                    `/resource/${params?.row.domain_id}/re-run-validate-resource/${params?.row.resource_id}?re-run=true&validation-id=${params?.row?.id}&run-id=${params?.row?.run_id}&run-name=${params?.row?.run_name}`
                  )
                }
              >
                <IconReRunBlue />
              </IconButton>
            </Tooltip>

            <Tooltip title="Delete" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() => {
                  setOpenValidationExecutionConfirmation(true);
                  setDeleteExecutionId(params.row.id);
                }}
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  const handleSubmitReport = async (event: any) => {
    navigate(
      `/validation-execution-history/validation-execution-results?runName=${formData.run_name}&reportType=${formData.report_type}&runDate=${formData?.run_date}`
    );
  };

  const handleDelete = async () => {
    try {
      setIsDeleteLoading(true);
      await deleteValidationExecutionbyID(deleteExecutionId);
      setFileData((prev: any) => ({
        ...prev,
        items: prev.items.filter((item: any) => item.id !== deleteExecutionId),
        total: prev.total - 1,
      }));
      showToast(`${deleteExecutionId} deleted successfully`, "success");
    } catch (error) {
      console.error("Error deleting execution:", error);
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const handleValidationExecutionDelete = () => {
    setOpenValidationExecutionConfirmation(false);
    handleDelete();
  };

  const handleAbortValidation = async () => {
    try {
      setIsDeleteLoading(true);
      await abortValidationExecutionbyID(abortExecutionId);
      setFileData((prev: any) => ({
        ...prev,
        items: prev.items.filter((item: any) => item.id !== abortExecutionId),
        total: prev.total - 1,
      }));
      showToast(`${abortExecutionId} aborted successfully`, "success");
    } catch (error) {
      console.error("Error aborting execution:", error);
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const handleAbortValidationExecution = () => {
    setOpenAbortValidationExecutionConfirmation(false);
    handleAbortValidation();
  };

  return (
    <>
      {(isBackdropLoading || isDeleteLoading) && <Loader isLoading={true} />}

      <FilterSearch
        setSearchFilterData={setSearchFilterData}
        isFilterVisible={isFilterVisible}
        setIsFilterVisible={setIsFilterVisible}
        FilterFor="resource"
        searchFilterData={searchFilterData}
        requiredFields={["date", "domain_id", "run", "resource"]}
        fieldRequirements={{ dateTitle: "Date", tooltipRequired: true }}
      />
      <ConsolidatedReportsFilter
        setFormData={setFormData}
        isFilterVisible={isConsolidatedFilterVisible}
        setIsFilterVisible={setIsConsolidatedFilterVisible}
        formData={formData}
        handleSubmitReport={handleSubmitReport}
      />

      <div className="execution-history-btn-group">
        <Box className="transaction-btn-group">
          <button
            className="filters-btn btn-orange btn-border"
            onClick={() => setIsFilterVisible(true)}
          >
            <IconFilterSvg />
            Filter
          </button>
          <button
            className="btn-orange btn-border cursor-pointer "
            onClick={() => setIsConsolidatedFilterVisible(true)}
            color="secondary"
          >
            Consolidated Reports
          </button>
        </Box>
      </div>
      <EHistoryDataTable
        dataColumns={columns}
        dataRows={fileData?.items}
        loading={isloading}
        dataListTitle={"Validation History"}
        setLoading={setIsLoading}
        isExportButtonRequired={true}
        className="dataTable no-radius hide-progress-icon"
        rowCount={fileData?.total}
        pageSizeOptions={[25]}
        rowGroupByColumnProps={enableRowGroupByColumnProps}
        paginationModel={{
          page: page - 1,
          pageSize: pSize,
        }}
        onPaginationModelChange={(params: any) => {
          if (params.pageSize !== pSize || params.page !== page - 1) {
            setPage(params.page + 1);
            setPSize(params.pageSize);
          }
        }}
        historyType={"Validation"}
        downloadFromAPIFile={() =>
          downloadExecutionHistoryFile({
            setLoading: setIsBackdropLoading,
            searchFilterData,
          })
        }
        disableColumnFilter={false}
        singlePageMaxHeightDiff={285}
        isShowInComparisonHistory={false}
      />
      <ConfirmationDialog
        title={"Confirm removing Execution"}
        dialogContent={
          "Are you sure you want to delete this validation execution data?"
        }
        handleCancel={() => setOpenValidationExecutionConfirmation(false)}
        openConfirmation={openValidationExecutionConfirmation}
        handleConfirm={handleValidationExecutionDelete}
        isShowClassMb0={false}
      />
      <ConfirmationDialog
        title={"Confirm abort Validation"}
        dialogContent={
          "Are you sure you want to abort this validation execution data?"
        }
        handleCancel={() => setOpenAbortValidationExecutionConfirmation(false)}
        openConfirmation={openAbortValidationExecutionConfirmation}
        handleConfirm={handleAbortValidationExecution}
        isShowClassMb0={false}
      />
    </>
  );
};

export default ValidationHistory;
