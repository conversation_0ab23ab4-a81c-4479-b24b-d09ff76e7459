import { <PERSON>, But<PERSON>, List, ListItem } from "@mui/material";
import { Link } from "react-router-dom";
import { useState } from "react";
import CircleIcon from "@mui/icons-material/Circle";
import { useToast } from "../services/utils";
import {
  IconBlobList,
  IconDirectionArrowSvg,
  IconNewConnectionKeys,
  IconNewFileProcessingAttributes,
  IconNewLinkedServices,
  IconNewRulesImport,
  IconNewRunInstanceSvg,
  IconResourceColumnSvg,
  SimCardDownloadIconSvg,
} from "../common/utils/icons";
import { toast } from "react-toastify";

export default function AdminPages() {
  const { showToast } = useToast();
  const [showLogs, setShowLogs] = useState(false);

  // Define multiple log files from environment variables
  const logFiles = [
    { name: "log1", url: process.env.REACT_APP_DOWNLOAD_LOG_URL_1 },
    { name: "log2", url: process.env.REACT_APP_DOWNLOAD_LOG_URL_2 },
    { name: "log3", url: process.env.REACT_APP_DOWNLOAD_LOG_URL_3 },
    { name: "log4", url: process.env.REACT_APP_DOWNLOAD_LOG_URL_4 },
    { name: "log5", url: process.env.REACT_APP_DOWNLOAD_LOG_URL_5 },
  ];

  const downloadLogFile = async (
    fileUrl: string | undefined,
    fileName: string
  ) => {
    if (!fileUrl) {
      showToast(`Log file URL is missing`, "error");
      return;
    }

    try {
      const response = await fetch(
        `${process.env.REACT_APP_REPORT_DOWNLOAD_PATH}${fileUrl}`
      );
      if (!response.ok) {
        showToast(`Log file does not exist`, "error");
        return;
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName); // Set the file name dynamically
      document.body.appendChild(link);
      link.click();
      link.remove(); // Clean up after download
    } catch (error: any) {
      showToast("File download failed", "error");
      console.error("File download failed:", error);
    }
  };

  return (
    <>
      <Box className="text-box-card mb-0">
        <ul className="invisible-list-url">
          <li className="icon-stroke">
            <IconResourceColumnSvg />
            <Link to={"/resource-columns/all"}>Resource Columns</Link>
          </li>
          <li>
            <IconNewLinkedServices />
            <Link to={"/linked-services"}>Linked Services</Link>
          </li>
          <li>
            <IconNewConnectionKeys />
            <Link to={"/connection-keys"}>Connection Keys</Link>
          </li>
          <li>
            <IconNewRunInstanceSvg />
            <Link to={"/run-instance"}>Run Instance</Link>
          </li>
          <li>
            <IconNewFileProcessingAttributes />
            <Link to={"/file-processing-attributes"}>
              File Processing Attributes
            </Link>
          </li>
          <li className="import">
            <IconNewRulesImport />
            <Link to={"/domain/import-entity?type=domain"}>Domain Import</Link>
          </li>

          <li className="import">
            <IconNewRulesImport />
            <Link to={"/resource/import-entity?type=resource"}>
              Resource Import
            </Link>
          </li>
          <li className="import">
            <IconNewRulesImport />
            <Link to={"/resource-column/import-entity?type=resource-column"}>
              Resource Column Import
            </Link>
          </li>
          <li className="import">
            <IconNewRulesImport />
            <Link to={"/rules/import-entity?type=rule"}>Rules Import</Link>
          </li>
          <li className="import">
            <IconBlobList />
            <Link to={"/blob-list"}>Get Blob List</Link>
          </li>

          {/* Add the + icon to show log files */}
          <li
            className={`import has-child   ${showLogs ? "child-open" : ""}`}
            onClick={() => setShowLogs(!showLogs)}
          >
            <em>
              <SimCardDownloadIconSvg />
              Download Logs
              <span className="arrow-icon">
                <IconDirectionArrowSvg />
              </span>
            </em>

            {showLogs && (
              <ul className={`${showLogs ? "opened" : ""}`}>
                {logFiles.map((log, index) => (
                  <ListItem
                    key={index}
                    className="import"
                    onClick={() => downloadLogFile(log.url, log.name)}
                    style={{ cursor: "pointer" }}
                  >
                    <CircleIcon />
                    <span>{log.name}</span>
                  </ListItem>
                ))}
              </ul>
            )}
          </li>

          {/* Display the list of log files if showLogs is true */}
        </ul>
      </Box>
    </>
  );
}
