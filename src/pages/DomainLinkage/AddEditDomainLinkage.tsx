import { Box, Button, Checkbox, Grid, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import AutoCompleteDomainList from "../../components/Molecules/Domain/AutoCompleteDomainList";
import {
  IconBtnEditBase,
  IconCrossCircleIconSvg,
  IconLinkedServicesSvg,
  IconMinusWhite,
  IconPlusWhite,
} from "../../common/utils/icons";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import DomainKeyAutocomplete from "../../components/Molecules/AutoComplete/DomainKeyAutocomplete";
import {
  addDomainLinkage,
  updateDomainLinkage,
} from "../../services/domainLinkageService";
import { useToast } from "../../services/utils";
import { Formik, Form, FormikProps } from "formik";
import * as Yup from "yup";
import { useNavigate, useParams } from "react-router-dom";
import useFetchDomainLinkageById from "../../hooks/useFetchDomainLinkageById";
import { getDomainDetail } from "../../services/domainsService";
import Loader from "../../components/Molecules/Loader/Loader";
import btnEdit from "../../assets/svgs/btn-edit-orange.svg";

const initialRowGroup = {
  source_domain_key: "",
  target_domain_key: "",
  source_domain_key_is_key: false,
  target_domain_key_is_key: false,
};

const initialFormValue = [
  {
    domainLinkagRowGroup: [initialRowGroup],
    source_domain_id: null,
    target_domain_id: null,
    fromDomainColumns: [],
    toDomainColumns: [],
  },
];

const validationSchema = Yup.object({
  formData: Yup.array().of(
    Yup.object({
      domainLinkagRowGroup: Yup.array()
        .of(
          Yup.object({
            source_domain_key: Yup.string().required(
              "Source domain key is required"
            ),
            target_domain_key: Yup.string().required(
              "Target domain key is required"
            ),
            source_domain_key_is_key: Yup.boolean(),
            target_domain_key_is_key: Yup.boolean(),
          })
        )
        .required("At least one domain row is required"),
      source_domain_id: Yup.string().required("Source domain is required"),
      target_domain_id: Yup.string().required("Target domain is required"),
    })
  ),
});

interface IAddEditDomainLinkageProps {
  isViewMode?: any;
}
const AddEditDomainLinkage = ({ isViewMode }: IAddEditDomainLinkageProps) => {
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [sourceDomainLoading, setSourceDomainLoading] =
    useState<boolean>(false);
  const [targetDomainLoading, setTargetDomainLoading] =
    useState<boolean>(false);
  const navigate = useNavigate();
  const { id: sourceLinkageId } = useParams();
  const [initialFormData, setInitialFormData] = useState<any>(initialFormValue);
  const [isEditMode, setIsEditMode] = useState(false);

  const [domainLinkageData] = useFetchDomainLinkageById({
    setIsLoading,
    currentDomainLinkageId: sourceLinkageId,
  });

  useEffect(() => {
    const fetchData = async () => {
      if (!domainLinkageData?.length) return;
      const [fromDomainColumns, toDomainColumns] = await Promise.all([
        getDomainDetail({
          currentDomainId: domainLinkageData[0]?.source_domain_id,
        }),
        getDomainDetail({
          currentDomainId: domainLinkageData[0]?.target_domain_id,
        }),
      ]);
      setInitialFormData([
        {
          source_domain_id: domainLinkageData[0]?.source_domain_id,
          target_domain_id: domainLinkageData[0]?.target_domain_id,
          fromDomainColumns:
            fromDomainColumns?.domain_properties?.columns ?? {},
          toDomainColumns: toDomainColumns?.domain_properties?.columns ?? {},
          domainLinkagRowGroup: domainLinkageData.map((data: any) => ({
            source_domain_key: data?.source_domain_key,
            target_domain_key: data?.target_domain_key,
            source_domain_key_is_key: data?.source_domain_key_is_key,
            target_domain_key_is_key: data?.target_domain_key_is_key,
          })),
        },
      ]);
      setIsEditMode(true);
    };

    fetchData();
  }, [domainLinkageData]);

  const handleRemoveColumn = (
    formikProps: FormikProps<{
      formData: {
        domainLinkagRowGroup: {
          source_domain_key: string;
          target_domain_key: string;
          source_domain_key_is_key: boolean;
          target_domain_key_is_key: boolean;
        }[];
        source_domain_id: null;
        target_domain_id: null;
        fromDomainColumns: never[];
        toDomainColumns: never[];
      }[];
    }>,
    index: any,
    colIndex: number
  ) => {
    const { values, setFieldValue } = formikProps;
    const updatedRows = [...values.formData[index].domainLinkagRowGroup];
    updatedRows.splice(colIndex, 1);
    setFieldValue(`formData[${index}].domainLinkagRowGroup`, updatedRows);
  };

  const handleRemoveDomain = (
    formikProps: FormikProps<{
      formData: {
        domainLinkagRowGroup: {
          source_domain_key: string;
          target_domain_key: string;
          source_domain_key_is_key: boolean;
          target_domain_key_is_key: boolean;
        }[];
        source_domain_id: null;
        target_domain_id: null;
        fromDomainColumns: never[];
        toDomainColumns: never[];
      }[];
    }>,
    index: number
  ) => {
    const { values, setFieldValue } = formikProps;
    const updatedFormData = [...values.formData];
    updatedFormData.splice(index, 1);
    setFieldValue("formData", updatedFormData);
  };

  const handleAddMoreColumns = (
    formikProps: FormikProps<{
      formData: {
        domainLinkagRowGroup: {
          source_domain_key: string;
          target_domain_key: string;
          source_domain_key_is_key: boolean;
          target_domain_key_is_key: boolean;
        }[];
        source_domain_id: null;
        target_domain_id: null;
        fromDomainColumns: never[];
        toDomainColumns: never[];
      }[];
    }>,
    index: any
  ) => {
    const { values, setFieldValue } = formikProps;
    const updatedRows = [
      ...values.formData[index].domainLinkagRowGroup,
      initialRowGroup,
    ];
    setFieldValue(`formData[${index}].domainLinkagRowGroup`, updatedRows);
  };

  const handleAddMoreRows = (
    formikProps: FormikProps<{
      formData: {
        domainLinkagRowGroup: {
          source_domain_key: string;
          target_domain_key: string;
          source_domain_key_is_key: boolean;
          target_domain_key_is_key: boolean;
        }[];
        source_domain_id: null;
        target_domain_id: null;
        fromDomainColumns: never[];
        toDomainColumns: never[];
      }[];
    }>
  ) => {
    const { values, setFieldValue } = formikProps;
    setFieldValue("formData", [...values.formData, initialFormValue[0]]);
  };

  const transformDomainLinkageData = (
    formData: any[],
    isUpdate: boolean = false
  ) => {
    const result = formData.flatMap(
      (item: {
        domainLinkagRowGroup: any[];
        source_domain_id?: any;
        target_domain_id?: any;
      }) =>
        item.domainLinkagRowGroup.map(
          (domainLink: {
            source_domain_key: any;
            target_domain_key: any;
            source_domain_key_is_key: any;
            target_domain_key_is_key: any;
          }) => ({
            source_domain_id: item.source_domain_id,
            target_domain_id: item.target_domain_id,
            source_domain_key: domainLink.source_domain_key,
            target_domain_key: domainLink.target_domain_key,
            source_domain_key_is_key: domainLink.source_domain_key_is_key,
            target_domain_key_is_key: domainLink.target_domain_key_is_key,
          })
        )
    );
    if (isUpdate) {
      return result[0] || {};
    }
    return { domain_linkage_details: result };
  };

  const onSaveDomainLinkage = async (
    values: { formData: any },
    { setSubmitting, setErrors }: any
  ) => {
    try {
      setSubmitting(true);
      if (sourceLinkageId) {
        const transformedData = transformDomainLinkageData(
          values.formData,
          true
        );
        updateDomainLinkage(transformedData, sourceLinkageId)
          .then((response) => {
            showToast(
              `Domain linkage id: ${sourceLinkageId}, updated successfully!`,
              "success"
            );
            navigate("/domain-linkage");
          })
          .catch((error) => {});
      } else {
        const transformedData = transformDomainLinkageData(
          values.formData,
          false
        );
        addDomainLinkage(transformedData)
          .then((response) => {
            showToast("Domain linkage created successfully!", "success");
            navigate("/domain-linkage");
          })
          .catch((error) => {});
      }

      setSubmitting(false);
    } catch (error) {
      console.error("Error creating domain linkage:", error);
      showToast("Failed to create domain linkage", "error");
      setSubmitting(false);
    }
  };

  if (initialFormData[0]?.source_domain_id === null && sourceLinkageId)
    return <Loader isLoading={true} />;

  return (
    <>
      <Loader isLoading={sourceDomainLoading || targetDomainLoading} />
      <Formik
        initialValues={{ formData: initialFormData }}
        validationSchema={validationSchema}
        onSubmit={onSaveDomainLinkage}
      >
        {(formikProps) => {
          const {
            values,
            errors,
            touched,
            setFieldValue,
            setFieldTouched,
          }: any = formikProps;
          const isSubmitDisabled = values.formData.some((item: any) => {
            const isRowGroupValid = item?.domainLinkagRowGroup?.every(
              (row: any) => {
                return row?.source_domain_key && row?.target_domain_key;
              }
            );
            const isFormDataValid =
              item?.source_domain_id && item?.target_domain_id;
            return !(isRowGroupValid && isFormDataValid);
          });

          return (
            <Form>
              {values.formData.map((item: any, index: any) => {
                const sourceDomainError =
                  errors?.formData?.[index]?.source_domain_id;
                const targetDomainError =
                  errors?.formData?.[index]?.target_domain_id;
                const sourceDomainTouched =
                  touched?.formData?.[index]?.source_domain_id;
                const targetDomainTouched =
                  touched?.formData?.[index]?.target_domain_id;
                return (
                  <Box
                    className="text-box-card compact-text-box-card full-radius mb-2 position-relative"
                    key={index}
                  >
                    {index !== 0 && (
                      <Box
                        sx={{
                          position: "absolute",
                          top: "-12px",
                          right: "-12px",
                          cursor: "pointer",
                        }}
                        onClick={() => handleRemoveDomain(formikProps, index)}
                      >
                        <IconCrossCircleIconSvg />
                      </Box>
                    )}

                    <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                      <Grid item xs={12} sm={12} md={5} lg={5} xl={5}>
                        <AutoCompleteDomainList
                          currentDomainId={item?.source_domain_id}
                          setIsLoading={setSourceDomainLoading}
                          handelOnChangeDomain={(event, value) => {
                            setFieldValue(
                              `formData[${index}].fromDomainColumns`,
                              value?.domain_properties?.columns || []
                            );
                            setFieldValue(
                              `formData[${index}].source_domain_id`,
                              value?.id ?? null
                            );
                            setFieldTouched(
                              `formData[${index}].source_domain_id`,
                              true,
                              false
                            );
                          }}
                          listLabel="Source Domain"
                          className={`form-control-autocomplete ${
                            sourceDomainError &&
                            sourceDomainTouched &&
                            sourceDomainError
                              ? "has-error"
                              : ""
                          }`}
                          error={
                            !!sourceDomainError &&
                            !!sourceDomainTouched &&
                            !!sourceDomainError
                          }
                          helperText={
                            sourceDomainError &&
                            sourceDomainTouched &&
                            sourceDomainError
                          }
                          isDisabled={isEditMode || isViewMode}
                          required={true}
                        />
                      </Grid>
                      <Grid item xs={1} sm={1} md={1} lg={1} xl={1}></Grid>
                      <Grid item xs={12} sm={12} md={5} lg={5} xl={5}>
                        <AutoCompleteDomainList
                          currentDomainId={item?.target_domain_id}
                          setIsLoading={setTargetDomainLoading}
                          handelOnChangeDomain={(event, value) => {
                            setFieldValue(
                              `formData[${index}].toDomainColumns`,
                              value?.domain_properties?.columns || []
                            );
                            setFieldValue(
                              `formData[${index}].target_domain_id`,
                              value?.id ?? null
                            );
                            setFieldTouched(
                              `formData[${index}].target_domain_id`,
                              true,
                              false
                            );
                          }}
                          listLabel="Target Domain"
                          className={`form-control-autocomplete ${
                            targetDomainError &&
                            targetDomainTouched &&
                            targetDomainError
                              ? "has-error"
                              : ""
                          }`}
                          error={
                            targetDomainError &&
                            targetDomainTouched &&
                            !!targetDomainError
                          }
                          helperText={
                            targetDomainError &&
                            targetDomainTouched &&
                            targetDomainError
                          }
                          isDisabled={isEditMode || isViewMode}
                          required={true}
                        />
                      </Grid>

                      {item?.source_domain_id &&
                        item?.target_domain_id &&
                        item?.domainLinkagRowGroup?.map(
                          (domainCol: any, colIndex: any) => {
                            const sourceDomainKeyError =
                              errors?.formData?.[index]?.domainLinkagRowGroup?.[
                                colIndex
                              ]?.source_domain_key;
                            const sourceDomainKeyTouched =
                              touched?.formData?.[index]
                                ?.domainLinkagRowGroup?.[colIndex]
                                ?.source_domain_key;
                            const targetDomainKeyError =
                              errors?.formData?.[index]?.domainLinkagRowGroup?.[
                                colIndex
                              ]?.target_domain_key;
                            const targetDomainKeyTouched =
                              touched?.formData?.[index]
                                ?.domainLinkagRowGroup?.[colIndex]
                                ?.target_domain_key;
                            return (
                              <React.Fragment key={colIndex}>
                                <Grid item xs={12} sm={12} md={5} lg={5} xl={5}>
                                  <Grid
                                    container
                                    rowSpacing={3}
                                    columnSpacing={3}
                                  >
                                    <Grid
                                      item
                                      xs={12}
                                      sm={12}
                                      md={6}
                                      lg={6}
                                      xl={6}
                                    >
                                      <DomainKeyAutocomplete
                                        {...(isEditMode && {
                                          selectedDomainKey: {
                                            name: domainCol?.source_domain_key,
                                          },
                                        })}
                                        domainKeyOptions={
                                          item?.fromDomainColumns
                                        }
                                        onDomainKeyChange={(e, value) =>
                                          setFieldValue(
                                            `formData[${index}].domainLinkagRowGroup[${colIndex}].source_domain_key`,
                                            value?.name ?? ""
                                          )
                                        }
                                        id={`source-domain-keys-autocomplete-${index}-${colIndex}`}
                                        popperAnchorId={`source-domain-keys-autocomplete-${index}-${colIndex}`}
                                        isMulti={false}
                                        className={`form-control-autocomplete ${
                                          !!sourceDomainKeyError &&
                                          !!sourceDomainKeyTouched &&
                                          sourceDomainKeyError
                                            ? "has-error"
                                            : ""
                                        }`}
                                        error={
                                          !!sourceDomainKeyError &&
                                          !!sourceDomainKeyTouched &&
                                          !!sourceDomainKeyError
                                        }
                                        helperText={
                                          sourceDomainKeyError &&
                                          sourceDomainKeyTouched &&
                                          sourceDomainKeyError
                                        }
                                        isDisabled={isViewMode}
                                        required={true}
                                      />
                                    </Grid>
                                    <Grid
                                      item
                                      xs={12}
                                      sm={12}
                                      md={6}
                                      lg={6}
                                      xl={6}
                                    >
                                      <Box
                                        sx={{
                                          display: { md: "block", sm: "none" },
                                        }}
                                        className="label-text line-height16 "
                                      >
                                        &nbsp;
                                      </Box>
                                      <label className="base-resource-checkbox">
                                        <Checkbox
                                          disabled={isViewMode}
                                          checked={
                                            values.formData[index]
                                              ?.domainLinkagRowGroup[colIndex]
                                              ?.source_domain_key_is_key ||
                                            false
                                          }
                                          onChange={(e) =>
                                            setFieldValue(
                                              `formData[${index}].domainLinkagRowGroup[${colIndex}].source_domain_key_is_key`,
                                              e.target.checked
                                            )
                                          }
                                          sx={{
                                            "&.Mui-checked": {
                                              color: "var(--yellow)",
                                            },
                                          }}
                                        />
                                        <span>Source Domain Key Is Key</span>
                                      </label>
                                    </Grid>
                                  </Grid>
                                </Grid>
                                <Grid item xs={1} sm={1} md={1} lg={1} xl={1}>
                                  <label className="label-text pt-11">
                                    &nbsp;
                                  </label>
                                  <Box className="d-flex justify-content-center">
                                    <IconLinkedServicesSvg />
                                  </Box>
                                </Grid>
                                <Grid item xs={12} sm={12} md={5} lg={5} xl={5}>
                                  <Grid
                                    container
                                    rowSpacing={3}
                                    columnSpacing={3}
                                  >
                                    <Grid
                                      item
                                      xs={12}
                                      sm={12}
                                      md={6}
                                      lg={6}
                                      xl={6}
                                    >
                                      <DomainKeyAutocomplete
                                        {...(isEditMode && {
                                          selectedDomainKey: {
                                            name: domainCol?.target_domain_key,
                                          },
                                        })}
                                        domainKeyOptions={item?.toDomainColumns}
                                        onDomainKeyChange={(e, value) =>
                                          setFieldValue(
                                            `formData[${index}].domainLinkagRowGroup[${colIndex}].target_domain_key`,
                                            value?.name ?? ""
                                          )
                                        }
                                        id={`target-domain-keys-autocomplete-${index}-${colIndex}`}
                                        popperAnchorId={`target-domain-keys-autocomplete-${index}-${colIndex}`}
                                        isMulti={false}
                                        className={`form-control-autocomplete ${
                                          targetDomainKeyError &&
                                          targetDomainKeyTouched &&
                                          targetDomainKeyError
                                            ? "has-error"
                                            : ""
                                        }`}
                                        error={
                                          !!targetDomainKeyError &&
                                          !!targetDomainKeyTouched &&
                                          !!targetDomainKeyError
                                        }
                                        helperText={
                                          targetDomainKeyError &&
                                          targetDomainKeyTouched &&
                                          targetDomainKeyError
                                        }
                                        isDisabled={isViewMode}
                                        required={true}
                                      />
                                    </Grid>
                                    <Grid
                                      item
                                      xs={12}
                                      sm={12}
                                      md={6}
                                      lg={6}
                                      xl={6}
                                    >
                                      <Box
                                        sx={{
                                          display: { md: "block", sm: "none" },
                                        }}
                                        className="label-text line-height16 "
                                      >
                                        &nbsp;
                                      </Box>
                                      <label className="base-resource-checkbox">
                                        <Checkbox
                                          disabled={isViewMode}
                                          checked={
                                            values.formData[index]
                                              ?.domainLinkagRowGroup[colIndex]
                                              ?.target_domain_key_is_key ||
                                            false
                                          }
                                          onChange={(e) =>
                                            setFieldValue(
                                              `formData[${index}].domainLinkagRowGroup[${colIndex}].target_domain_key_is_key`,
                                              e.target.checked
                                            )
                                          }
                                          sx={{
                                            "&.Mui-checked": {
                                              color: "#FFA500",
                                            },
                                          }}
                                        />
                                        <span>Target Domain Key Is Key</span>
                                      </label>
                                    </Grid>
                                  </Grid>
                                </Grid>
                                <Grid item xs={1} sm={1} md={1} lg={1} xl={1}>
                                  <label className="label-text">&nbsp;</label>
                                  <Box className="d-flex justify-content-end">
                                    {colIndex !== 0 && (
                                      <Button
                                        variant="contained"
                                        className="btn-orange btn-nostyle btn-blue"
                                        onClick={() =>
                                          handleRemoveColumn(
                                            formikProps,
                                            index,
                                            colIndex
                                          )
                                        }
                                      >
                                        <IconMinusWhite />
                                      </Button>
                                    )}
                                  </Box>
                                </Grid>
                              </React.Fragment>
                            );
                          }
                        )}

                      {!isEditMode &&
                        item?.source_domain_id &&
                        item?.target_domain_id && (
                          <Grid
                            item
                            xs={12}
                            sm={12}
                            md={12}
                            lg={12}
                            xl={12}
                            className="d-flex justify-content-end"
                          >
                            <Button
                              variant="contained"
                              color="secondary"
                              className="btn-orange btn-nostyle btn-blue"
                              sx={{ gap: 1 }}
                              onClick={() =>
                                handleAddMoreColumns(formikProps, index)
                              }
                              disabled={
                                values.formData[index].domainLinkagRowGroup[
                                  values.formData[index].domainLinkagRowGroup
                                    .length - 1
                                ].source_domain_key === "" ||
                                values.formData[index].domainLinkagRowGroup[
                                  values.formData[index].domainLinkagRowGroup
                                    .length - 1
                                ].target_domain_key === ""
                              }
                            >
                              <IconPlusWhite />
                              Add More Columns
                            </Button>
                          </Grid>
                        )}

                      {isViewMode && (
                        <Grid
                          item
                          xs={12}
                          sx={{ display: "flex", justifyContent: "flex-end" }}
                        >
                          <button
                            className="btn-nostyle icon-btn-edit"
                            type="button"
                            onClick={() => {
                              navigate(
                                `/domain-linkage/edit/${sourceLinkageId}`,
                                {
                                  state: {
                                    details: {
                                      id: sourceLinkageId,
                                    },
                                  },
                                }
                              );
                            }}
                          >
                            <IconBtnEditBase />
                          </button>
                        </Grid>
                      )}
                    </Grid>
                  </Box>
                );
              })}
              <Grid
                container
                rowSpacing={3}
                columnSpacing={3}
                className="d-flex justify-content-end"
              >
                {!isEditMode && (
                  <Grid item>
                    <Button
                      variant="contained"
                      color="secondary"
                      className="btn-orange btn-nostyle btn-blue"
                      sx={{ gap: 1 }}
                      onClick={() => handleAddMoreRows(formikProps)}
                    >
                      <IconPlusWhite />
                      Add More Domains
                    </Button>
                  </Grid>
                )}
                {!isViewMode && (
                  <Grid item>
                    <Button
                      color="secondary"
                      variant="contained"
                      className="btn-orange"
                      title="Save Domain"
                      type="submit"
                      disabled={isSubmitDisabled}
                    >
                      <SaveOutlinedIcon /> &nbsp; Save
                    </Button>
                  </Grid>
                )}
              </Grid>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default AddEditDomainLinkage;
