import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { Box, Button, Grid, IconButton, Tooltip } from "@mui/material";
import FlexBetween from "../../components/FlexBetween";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import { useNavigate } from "react-router-dom";
import ButtonComponent from "../../components/Button.Component";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import iconEdit from "../../assets/svgs/icon-edit-orange.svg";
import iconEye from "../../assets/svgs/icon-eye-orange.svg";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import { paginatedResponseFormat } from "../../services/constants";
import Loader from "../../components/Molecules/Loader/Loader";
import useFetchDomains from "../../hooks/useFetchDomains";
import LinkageFilterSearch from "../../components/Molecules/FilterSearch/LinkageFilterSearch";
import {
  IconEditBase,
  IconEyeBase,
  IconFilterSvg,
} from "../../common/utils/icons";
import useFetchAllDomainLinkage from "../../hooks/useFetchAllDomainLinkage";
const DomainLinkage: React.FC = () => {
  const navigate = useNavigate();
  // States
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLinkageLoading, setIsLinkageLoading] = useState<boolean>(false);
  const [isDomainLoading, setIsDomainLoading] = useState<boolean>(false);
  const [domainsLinkageData, setDomainLinkageData] = useState<any>([]);
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [searchFilterData, setSearchFilterData] = useState<any>({
    source_domain_id_filter: null,
    target_domain_id_filter: null,
    source_domain_key_filter: "",
    target_domain_key_filter: "",
  });

  // Pagination configuration
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE || "1";
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE || "25";
  const [page, setPage] = useState<number>(parseInt(defaultPage, 10));
  const [pSize, setPSize] = useState<number>(parseInt(defaultPageSize, 10));

  // Hooks to fetch data
  const [fetchedDomainsLinkageData]: any = useFetchAllDomainLinkage({
    searchFilterData,
    setIsLoading: setIsLinkageLoading,
    page,
    pSize,
  });
  const [domainsData] = useFetchDomains({ setIsLoading: setIsDomainLoading });

  // Memoize domain data for fast lookup
  const domainsMap =
    domainsData?.reduce((acc: any, domain: any) => {
      acc[domain.id] = {
        name: domain.domain_name,
        isTrue: !!domain.domain_name,
      };
      return acc;
    }, {}) || {};

  // Update domain linkage data when fetched data changes
  useEffect(() => {
    if (!fetchedDomainsLinkageData || domainsData.length === 0) {
      return; // No data to process, exit early
    }
    // Create a map to identify relationships that should be merged//
    const relationshipMap = new Map();
    // First pass: create keys for each source-target domain pair
    fetchedDomainsLinkageData.forEach((item: any) => {
      const key = `${item.source_domain_id}_${item.target_domain_id}`;
      if (!relationshipMap.has(key)) {
        relationshipMap.set(key, []);
      }
      relationshipMap.get(key).push(item);
    });

    // Second pass: process the groups
    const result: any = [];
    let resultId = 1;

    // Process each relationship group
    relationshipMap.forEach((items, key) => {
      // Extract source and target domain IDs
      const [source_domain_id, target_domain_id] = key.split("_").map(Number);
      if (
        !domainsMap[source_domain_id]?.isTrue || // Source domain is invalid
        !domainsMap[target_domain_id]?.isTrue // Target domain is invalid
      ) {
        return; // Skip this entry if either domain name is invalid
      }

      // Check if we should merge these items
      const shouldMerge = items.length > 1;

      if (shouldMerge) {
        // Merge multiple items for the same source-target pair
        const mergedEntry = items.reduce(
          (acc: any, item: any, idx: number) => {
            acc.id = resultId++;
            acc.source_domain_key[idx + 1] = item.source_domain_key;
            acc.target_domain_key[idx + 1] = item.target_domain_key;
            acc.action_ids[idx + 1] = item.id;
            return acc;
          },
          {
            source_domain_id,
            target_domain_id,
            source_domain_key: {},
            target_domain_key: {},
            action_ids: {},
          }
        );
        result.push(mergedEntry);
      } else {
        // Single item, keep it as is
        const item = items[0];
        result.push({
          id: resultId++,
          source_domain_id: item.source_domain_id,
          source_domain_key: { 1: item.source_domain_key },
          target_domain_id: item.target_domain_id,
          target_domain_key: { 1: item.target_domain_key },
          action_ids: { 1: item.id },
        });
      }
    });

    setDomainLinkageData(result);
  }, [fetchedDomainsLinkageData, domainsData]);

  // Handlers
  const handelClickEvent = () => {
    navigate("/domain-linkage/add");
  };
  // Memoize column definitions for the data grid
  const columns: GridColDef[] = useMemo(
    () => [
      {
        ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
        width: 1,
        minWidth: 20,
        flex: 0,
        renderCell: () => <></>,
        cellClassName: "no-padding-cell",
        headerClassName: "no-padding-cell",
      },
      {
        field: "source_domain_id",
        headerName: "Source Domain",
        width: 100,
        flex: 1,
        renderCell: (params: any) => {
          const sourceDomain = domainsMap[params?.row?.source_domain_id];
          return <>{sourceDomain?.isTrue ? sourceDomain.name : "N/A"}</>;
        },
      },
      {
        field: "source_domain_key",
        headerName: "Source Column Key",
        width: 100,
        flex: 1,
        renderCell: (params: any) => {
          return (
            <div className="d-block">
              {Object.entries(params.row.source_domain_key).map(
                ([key, value]: any) => {
                  return (
                    <>
                      <div
                        className=" d-flex align-items-center min-height-32"
                        key={key}
                      >
                        {key}: {value}
                      </div>
                    </>
                  );
                }
              )}
            </div>
          );
        },
      },
      {
        field: "target_domain_id",
        headerName: "Target Domain",
        flex: 1,
        renderCell: (params: any) => {
          const targetDomain = domainsMap[params?.row?.target_domain_id];
          return <>{targetDomain?.isTrue ? targetDomain.name : "N/A"}</>;
        },
      },
      {
        field: "target_domain_key",
        headerName: "Target Column Key",
        flex: 1,
        renderCell: (params: any) => {
          return (
            <div className="d-block">
              {Object.entries(params.row.target_domain_key).map(
                ([key, value]: any) => {
                  return (
                    <>
                      <div
                        className=" d-flex align-items-center min-height-32"
                        key={key}
                      >
                        {key}: {value}
                      </div>
                    </>
                  );
                }
              )}
            </div>
          );
        },
      },
      {
        field: "action",
        headerName: "Action",
        align: "center",
        headerAlign: "center",
        width: 130,
        renderCell: (params: any) => {
          return (
            <>
              <div className="d-block">
                {params &&
                  Object.entries(params?.row?.action_ids).length > 0 &&
                  Object.entries(params?.row?.action_ids).map(
                    ([key, value]: any) => {
                      return (
                        <div key={key}>
                          <Tooltip
                            title="View Domain Linkage"
                            placement="top"
                            arrow
                          >
                            <IconButton
                              className="datagrid-action-btn"
                              color="inherit"
                              onClick={() =>
                                navigate(`/domain-linkage/view/${value}`)
                              }
                            >
                              <IconEyeBase />
                            </IconButton>
                          </Tooltip>
                          <Tooltip
                            title="Edit Domain Linkage"
                            placement="top"
                            arrow
                          >
                            <IconButton
                              className="datagrid-action-btn"
                              color="inherit"
                              onClick={() =>
                                navigate(`/domain-linkage/edit/${value}`)
                              }
                            >
                              <IconEditBase />
                            </IconButton>
                          </Tooltip>
                        </div>
                      );
                    }
                  )}
              </div>
            </>
          );
        },
      },
    ],
    [domainsMap, navigate]
  );

  return (
    <Box>
      {(isLoading || isLinkageLoading || isDomainLoading) && (
        <Loader isLoading={isLoading || isLinkageLoading || isDomainLoading} />
      )}
      <Grid
        container
        justifyContent="space-between"
        alignItems="center"
        className="text-box-card compact-text-box-card"
      >
        <Grid item></Grid>
        <Grid item xs="auto">
          <FlexBetween sx={{ columnGap: 2 }}>
            <ButtonComponent
              handelClickEvent={handelClickEvent}
              className="btn-orange"
            >
              <>
                <AddSharpIcon sx={{ marginRight: "4px" }} /> Add Domain Linkage
              </>
            </ButtonComponent>
            <Button
              className="filters-btn btn-orange btn-border"
              onClick={() => setIsFilterVisible(true)}
            >
              <IconFilterSvg />
              Filter
            </Button>
          </FlexBetween>
        </Grid>
      </Grid>

      <FlexBetween>
        <DataTable
          dataColumns={columns}
          dataRows={domainsLinkageData || []}
          loading={isLoading}
          dataListTitle={"Domain Linkage"}
          className="dataTable no-radius hide-progress-icon"
          paginationMode="server"
          pageSizeOptions={[25]}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={336}
          isPaginationRequired={false}
        />
      </FlexBetween>
      <LinkageFilterSearch
        isFilterVisible={isFilterVisible}
        setIsFilterVisible={setIsFilterVisible}
        searchFilterData={searchFilterData}
        setSearchFilterData={setSearchFilterData}
        domainsData={domainsData}
      />
    </Box>
  );
};

export default DomainLinkage;
