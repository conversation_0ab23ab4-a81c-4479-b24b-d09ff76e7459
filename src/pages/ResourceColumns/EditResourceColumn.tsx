import React, { useState, useEffect, useRef, useMemo, useContext } from "react";
import { debounce } from "lodash";
import { useToast } from "../../services/utils";
import ReactDOM from "react-dom";
import {
  Box,
  Input,
  IconButton,
  Button,
  Grid,
  TextField,
  Tooltip,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  NativeSelect,
} from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import { toast } from "react-toastify";
import DataTable from "../../components/DataGrids/DataGrid";
import {
  datatypes,
  dateTimeFormat,
  severity_level,
} from "../../services/constants";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { IDomainsData } from "../../types/domain";
import { updateResourceColumn } from "../../services/resourcesService";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import {
  defaultEditResourceCDMColumns,
  editResourceCDMColumns,
} from "../../services/constants/resource";
import ValidationDialog from "../../components/Dialogs/ValidationDialog";
import DerivedColumnDialog from "../../components/Dialogs/DerivedColumnDialog";
import ReferenceDialog from "../../components/Dialogs/ReferenceDialog";
import CustomValidationSummary from "../../pages/Resource/CustomValidationSummary";
import { editResourceColumnSchema } from "../../schemas";
import { manageAvailableColumns, setDataType } from "../../services/utils";
import { useResourceContext } from "../../contexts/ResourceContext";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import { useParams } from "react-router-dom";
import ConfirmDailog from "../../components/Dialogs/ConfirmDailog";
import CheckboxWithKeyboardEvent from "../../components/Molecules/Resource/CheckboxWithKeyboardEvent";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import CrossFieldValidations from "./CrossFieldValidations";
import CommentBeforeUpdateDialog from "../../components/Dialogs/CommentBeforeUpdateDialog";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import useFetchDomains from "../../hooks/useFetchDomains";
import {
  IconAddRowBase,
  IconDeleteBlueSvg,
  IconEditBase,
  IconPlusBase,
} from "../../common/utils/icons";

const ACCORDION_HEADER: any = {
  resourceData: "Resource Column Details",
};

const EditResourceColumn: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const {
    formData,
    setFormData,
    errors,
    setErrors,
    isLoading,
    setIsLoading,
    setOpenValidationDialog,
    setSelectedRowId,
    setIsDailogEdit,
    resourceColumnFileData,
    setResourceColumnFileData,
    setPageContext,
    setDailogEditIndex,
    setOpenDerivedColumnDialog,
    setDerivedModalType,
    setOpenReferenceDialog,
    setReferenceData,
    // SetCDMColumns,
    domainsData,
    setDomainsData,
  } = useResourceContext();

  const {
    availColumns,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    globalVariables,
    setGlobalVariables,
    setQueryBuilderTempValue,
  } = useRuleResourceContext();
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(100);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const { id }: any = useParams();
  const { showToast } = useToast();

  const [selectedDomain, setSelectedDomain] = useState<IDomainsData>();
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [activeTab, setActiveTab] = useState<string>("resourceColumns");
  const [expandedAccordion, setExpandedAccordion] =
    useState<any>("resourceData");
  const [crossFieldValidations, setCrossFieldValidations] = useState<any>([]);
  const formEventRef = useRef<any>(null);
  const [isSaveClicked, setIsSaveClicked] = useState(false);

  const [currentResourceColumnId, setCurrentResourceColumnId] =
    React.useState<any>(parseInt(id));
  const [resourceColumnName, setResourceColumnName] = useState("");
  const [resourceCode, setResourceCode] = useState("");
  const [isDailogOpen, setIsDailogOpen] = useState(false);

  // const [domainsData] = useFetchDomains({ setIsLoading });
  const [resourceColumnData, setResourceColumnData] =
    useFetchResourceColumnsById({
      resourceColumnId: currentResourceColumnId,
      setIsLoading,
    });
  const [domains] = useFetchDomains({ setIsLoading });

  const [currentDomainId, setCurrentDomainId] = useState<any>(
    resourceColumnData?.domain_id
  );
  const [allVariablesList, setAllVariablesList] = useState({});
  const [oldColumnName, setOldColumnName] = useState("");
  const [openCommentConfirmation, setOpenCommentConfirmation] =
    useState<boolean>(false);
  const [initialResourceColumnName, setInitialResourceColumnName] =
    useState<any>({});
  const [initialFileData, setInitialFileData] = useState<any[]>([]);
  const [initialCrossFieldValidations, setInitialCrossFieldValidations] =
    useState<any[]>([]);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  useEffect(() => {
    console.log("initialFileData", initialFileData);
  }, [initialFileData]);

  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    console.log(initialFileData);
    const resourceColumnNameChanged =
      JSON.stringify(resourceColumnName) !==
      JSON.stringify(initialResourceColumnName);
    const fileDataChanged =
      JSON.stringify(resourceColumnFileData) !==
      JSON.stringify(initialFileData);
    const crossFieldValidationsChanged =
      JSON.stringify(crossFieldValidations) !==
      JSON.stringify(initialCrossFieldValidations);
    setHasChanges(
      resourceColumnNameChanged ||
        fileDataChanged ||
        crossFieldValidationsChanged
    );
  }, [
    resourceColumnName,
    resourceColumnFileData,
    crossFieldValidations,
    initialResourceColumnName,
    initialFileData,
    initialCrossFieldValidations,
  ]);

  useEffect(() => {
    if (domains) {
      setDomainsData(domains);
    }
  }, [domains]);
  useEffect(() => {
    setResourceColumnName(resourceColumnData?.name);
    setInitialResourceColumnName(resourceColumnData?.name);
    setResourceCode(resourceColumnData?.code);
    setCurrentDomainId(resourceColumnData?.domain_id);
    setGlobalVariables(
      resourceColumnData?.resource_column_properties?.inline_variables ?? []
    );
    setCurrentBreadcrumbPage({
      name: resourceColumnData?.name,
      id: resourceColumnData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [resourceColumnData]);

  // Set forma data in state
  const handleResourceColumnName = (e: any) => {
    setResourceColumnName(e.target.value);
    validateField(e.target.name, e.target.value);
  };
  const handleResourceCode = (e: any) => {
    setResourceCode(e.target.value);
    validateField(e.target.name, e.target.value);
  };

  useEffect(() => {
    if (
      resourceColumnData?.resource_column_properties?.cross_field_validations
    ) {
      const crossFieldWithId =
        resourceColumnData?.resource_column_properties?.cross_field_validations.map(
          (crossField: any) => ({
            ...crossField,
            id: Math.random(),
          })
        );
      setCrossFieldValidations(crossFieldWithId);
      setInitialCrossFieldValidations(crossFieldWithId);
    }
  }, [resourceColumnData]);

  const unique_columns_data =
    resourceColumnData?.resource_column_properties?.unique_columns;
  const handleAddMoreRow = () => {
    let newRow = { ...editResourceCDMColumns };
    const highestId = resourceColumnFileData.reduce(
      (maxId: any, item: any) => Math.max(maxId, item.id),
      -1
    );
    newRow.id = highestId + 1;
    const updatedData = [...resourceColumnFileData, newRow];
    setResourceColumnFileData(updatedData);
    const newTotalRows = updatedData.length;
    const maxRowsPerPage = pSize;
    const newPageIndex = Math.floor((newTotalRows - 1) / maxRowsPerPage);
    setTimeout(() => {
      setPage(newPageIndex + 1);
      setTimeout(() => {
        const lastInput = inputRefs.current[updatedData.length - 1];
        if (lastInput) {
          const domNode = ReactDOM.findDOMNode(lastInput);
          if (domNode instanceof HTMLElement) {
            domNode?.focus();
          }
        }
      }, 700);
    }, 300);
  };
  const handleDeleteAllRows = () => {
    if (resourceColumnFileData && resourceColumnFileData.length > 0) {
      setIsDailogOpen(true);
    }
  };
  const handleCancelDeleteAction = () => {
    setIsDailogOpen(false);
  };
  const handleConfirmDeleteAction = () => {
    setIsDailogOpen(false);
    setResourceColumnFileData([resourceColumnFileData[0]]);
  };
  // Define table column names
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => (
        <IconButton
          size="small"
          tabIndex={-1}
          disabled={
            params?.row?.is_derived ||
            params?.row?.custom_validations?.length > 0 ||
            params?.row?.is_reference ||
            params?.row?.reference_column_definition != null
              ? false
              : true
          }
          // aria-label={isExpanded ? "Close" : "Open"}
          onClick={() => {
            // toggle in array
            if (expandedRow.includes(params.row.id)) {
              setExpandedRow((prev: any) =>
                prev.filter((item: any) => item !== params.row.id)
              );
            } else {
              setExpandedRow((prev: any) => [...prev, params.row.id]);
            }
          }}
        >
          {params?.row?.is_derived ||
          params?.row?.custom_validations?.length > 0 ||
          params?.row?.is_reference ||
          params?.row?.reference_column_definition != null ? (
            <ExpandMoreIcon
              sx={{
                transform: `rotateZ(${
                  expandedRow.includes(params.row.id) ? 180 : 0
                }deg)`,
                transition: (theme) =>
                  theme.transitions.create("transform", {
                    duration: theme.transitions.duration.shortest,
                  }),
              }}
              fontSize="inherit"
            />
          ) : null}
        </IconButton>
      ),
    },
    {
      field: "column_name",
      headerName: "Resource Column",
      width: 120,
      minWidth: 150,
      flex: 1,
      renderCell: (params: any) => {
        const handleFocusIn = (e: React.FocusEvent<HTMLInputElement>) => {
          setOldColumnName(e.target.value);
        };
        const handleOnBlur = (e: React.FocusEvent<HTMLInputElement>) => {
          const updatedColumns = manageAvailableColumns(
            availColumns,
            oldColumnName,
            oldColumnName && oldColumnName.length > 0 ? "update" : "add",
            params.row.column_name
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
        };

        const valueField = resourceColumnFileData?.find(
          (item: any) => item.id === params.row.id
        )?.column_name;
        const isNameFilled = valueField !== "";
        const handleChangeName = (e: any) => {
          const value = e.target.value;
          if (value.toLowerCase() === "file_name") {
            const isFileNameExist = resourceColumnFileData.some(
              (fileItem: any) =>
                fileItem.column_name.toLowerCase() === "file_name"
            );
            if (isFileNameExist) {
              showToast(
                `"file_name" is reserved key column, and already exist, please provide different name.`,
                "warning"
              );
              return;
            }
          }
          setResourceColumnFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.column_name = value;
              }
              return item;
            });
            return newData;
          });
        };

        return (
          <Tooltip title={valueField} placement="top">
            <input
              // placeholder="Ex: Sample Column Name"
              // required
              value={valueField || ""}
              onChange={handleChangeName}
              className={`form-control-1  input-ellipsis ${
                !isNameFilled ? "border-red" : ""
              }`}
              style={{ fontSize: "14px", fontWeight: 500 }}
              onKeyDown={(e: any) => {
                e.stopPropagation();
              }}
              onBlur={handleOnBlur}
              onFocus={handleFocusIn}
              ref={(el) => (inputRefs.current[params?.id] = el)}
            />
          </Tooltip>
        );
      },
    },
    {
      field: "domain_column",
      headerName: "Domain Column",
      width: 120,
      minWidth: 130,
      flex: 1,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.domain_column = value;
              }
              return item;
            })
          );
        };

        const menuData = selectedDomain?.domain_properties?.columns;
        return (
          <Tooltip title={params.value} placement="top">
            <NativeSelect
              value={params.value ?? ""}
              className="white-space-nowrap form-control capitalize"
              onChange={handleChange}
              style={{ width: "100%", height: 35 }}
            >
              <option key={"Select..."} value={""}>
                Select...
              </option>
              {menuData?.length > 0 &&
                menuData.map((domainColumnItem: any) => (
                  <option
                    key={domainColumnItem?.name}
                    value={domainColumnItem?.name}
                  >
                    {domainColumnItem?.name}
                  </option>
                ))}
            </NativeSelect>
          </Tooltip>
        );
      },
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 100,
      renderCell: (params) => {
        const dataTypeValue = resourceColumnFileData.find(
          (item: any) => item.id === params.row.id
        )?.datatype;
        const isDataTypeFilled = dataTypeValue !== "" && dataTypeValue !== null;
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.datatype = value;
                item.data_format = "";
              }
              return item;
            })
          );
        };

        return (
          <NativeSelect
            value={params?.value?.trim()}
            className={`white-space-nowrap format-arrow-pos capitalize ${
              !isDataTypeFilled ? "border-red-parent" : ""
            }`}
            onChange={handleChange}
            style={{ width: "100%", height: 35 }}
          >
            <option key={"Select..."} value={""}>
              Select...
            </option>
            {datatypes.map((typeItem) => (
              <option key={typeItem} value={typeItem}>
                {typeItem}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "is_mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      align: "center",
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.checked;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.is_mandatory = value;
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      align: "center",
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.checked;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.key = value;
                if (params.row.is_active !== true && item.key === true) {
                  item.is_active = true;
                }
              }
              return item;
            })
          );
        };
        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "is_reference",
      headerName: "Reference",
      description: "Reference Data",
      maxWidth: 90,
      align: "center",
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.checked;
          if (value) {
            setOpenReferenceDialog(value);
            setSelectedRowId(params.row.id);
            setReferenceData({
              source: "internal",
              source_type: "",
              use_translation: false,
            });
          } else {
            setResourceColumnFileData((prev: any) => {
              return prev.map((item: any) => {
                if (item.id === params.row.id) {
                  item.is_reference = false;
                  item.reference_column_definition = null;
                }
                return item;
              });
            });
          }
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "is_active",
      headerName: "Act./Inac.",
      maxWidth: 70,
      align: "center",
      renderCell: (params) => {
        const handleChange = async (event: any) => {
          const value = event.target.checked;

          const isTruthy = ["Y", "y", "Yes", "yes", true, "true"].includes(
            params.row.key
          );
          if (params.row.is_active === true && isTruthy) {
            showToast(
              `If you want to uncheck 'Active/Inactive,' please uncheck the 'Key' column first.`,
              "warning"
            );
            return;
          }
          const updatedColumns = manageAvailableColumns(
            availColumns,
            params.row.column_name,
            "toggle"
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.is_active = value;
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "column_length",
      headerName: "Length",
      width: 100,
      minWidth: 100,
      flex: 1,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                const parsedValue = parseInt(value, 10);
                item.column_length = isNaN(parsedValue) ? null : parsedValue;
              }
              return item;
            })
          );
        };

        return (
          <Input
            // placeholder="Ex: Sample Enter Length"
            value={
              resourceColumnFileData?.find(
                (item: any) => item.id === params.row.id
              )?.column_length || ""
            }
            onChange={handleChange}
            onKeyDown={(e: any) => {
              e.stopPropagation();
            }}
            className="form-control input-ellipsis"
          />
        );
      },
    },
    {
      field: "severity_level",
      headerName: "Severity",
      width: 100,
      minWidth: 100,
      flex: 1,
      renderCell: (params) => {
        const severityLevelValue = resourceColumnFileData
          .find((item: any) => item.id === params.row.id)
          ?.severity_level?.toLowerCase();
        const handleChange = (event: any) => {
          const value = event.target.value as string;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                return { ...item, severity_level: value };
              }
              return item;
            })
          );
        };

        return (
          <NativeSelect
            value={severityLevelValue}
            className="white-space-nowrap capitalize"
            onChange={handleChange}
            style={{ width: "100%", height: 35 }}
            placeholder="Select..."
          >
            {severity_level.map((severity: string) => (
              <option key={severity} value={severity}>
                {severity}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "data_format",
      headerName: "Format",
      width: 120,
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.data_format = event.target.value;
              }
              return item;
            })
          );
        };

        return (
          <>
            {!(
              resourceColumnFileData?.find(
                (item: any) => item.id === params.row.id
              )?.datatype === "datetime"
            ) ? (
              <Input
                // placeholder="Ex: Sample Enter Format"
                value={
                  resourceColumnFileData?.find(
                    (item: any) => item.id === params.row.id
                  )?.data_format || ""
                }
                onChange={handleChange}
                onKeyDown={(e: any) => {
                  e.stopPropagation();
                }}
                className="form-control input-ellipsis"
              />
            ) : (
              <Tooltip
                title={
                  dateTimeFormat.includes(params.value) ? params.value : ""
                }
                placement="top"
                arrow
              >
                <NativeSelect
                  value={
                    dateTimeFormat.includes(params.value) ? params.value : ""
                  }
                  className={`white-space-nowrap format-arrow-pos form-control ${
                    !dateTimeFormat.includes(params.value)
                      ? "border-red-parent"
                      : ""
                  }`}
                  style={{ width: "100%", height: 35 }}
                  onChange={handleChange}
                >
                  <option key={"Select..."} value={""}>
                    Select...
                  </option>
                  {dateTimeFormat.map((format: string, index: number) => (
                    <option key={format + index} value={format}>
                      {format}
                    </option>
                  ))}
                </NativeSelect>
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 165,
      renderCell: (params) => {
        const handleDelete = async () => {
          setResourceColumnFileData((prev: any) =>
            prev.filter((item: any) => item.id !== params.row.id)
          );
          const updatedColumns = manageAvailableColumns(
            availColumns,
            params.row.column_name,
            "remove"
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
        };

        const handleAddNewRow = () => {
          const newRow = { ...editResourceCDMColumns };
          const currentRowIdx = resourceColumnFileData.findIndex(
            (item: any) => item.id === params.row.id
          );
          const highestId = resourceColumnFileData.reduce(
            (maxId: any, item: any) => Math.max(maxId, item.id),
            -1
          );
          newRow.id = highestId + 1;
          const updatedData = [...resourceColumnFileData];
          updatedData.splice(currentRowIdx + 1, 0, newRow);
          setResourceColumnFileData(updatedData);
        };

        return (
          <>
            <Tooltip title="Add Validation" placement="top" arrow>
              <IconButton
                color="inherit"
                onClick={() => {
                  handleAddValidation(params.row.id);
                  // updateCDMColumns();
                }}
                className="datagrid-action-btn"
              >
                <IconPlusBase />
              </IconButton>
            </Tooltip>
            {params.row.is_derived && (
              <Tooltip title="Edit Derived Column" placement="top" arrow>
                <IconButton
                  color="inherit"
                  onClick={() => {
                    onAddDerivedColumn();
                    setDerivedModalType("edit");
                    setSelectedRowId(params.row.id);
                  }}
                  className="datagrid-action-btn"
                >
                  <IconEditBase />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Add Column" placement="top" arrow>
              <Button
                color="secondary"
                onClick={handleAddNewRow}
                className="datagrid-action-btn min-width-40"
              >
                <IconAddRowBase />
              </Button>
            </Tooltip>
            <Tooltip title="Delete Row" placement="top" arrow>
              <IconButton
                color="inherit"
                onClick={handleDelete}
                className="datagrid-action-btn"
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    if (resourceColumnData) {
      let formattedColumns: any = [];
      const cdmData: any = [];
      const isFileNameOnIdx =
        resourceColumnData?.resource_column_properties?.resource_columns.some(
          (item: any) => item.column_name.toLowerCase() === "file_name"
        );
      resourceColumnData?.resource_column_properties?.resource_columns.map(
        (RColumn: any, idx: any) => {
          if (RColumn?.is_active) {
            cdmData.push(RColumn["column_name"].trim());
          }
          let severityLevel = "";
          if (RColumn?.severity_level === 1) {
            severityLevel = "High";
          } else if (RColumn?.severity_level === 2) {
            severityLevel = "Medium";
          } else {
            severityLevel = "Low";
          }
          const dataType = (RColumn?.constraints.datatype).toLowerCase();

          formattedColumns.push({
            id: idx, // changed because we need to add a default column with fileName
            column_name: RColumn.column_name,
            domain_column: RColumn.domain_column,
            datatype: datatypes.includes(dataType)
              ? setDataType(dataType)
              : null,
            is_mandatory: RColumn?.constraints.is_mandatory,
            data_format: RColumn?.constraints.data_format,
            column_length: RColumn?.constraints.column_length,
            is_derived: RColumn?.constraints.is_derived,
            derived_column_definition:
              RColumn?.constraints.derived_column_definition,
            is_reference: RColumn?.constraints.is_reference,
            reference_column_definition:
              RColumn?.constraints.reference_column_definition,
            custom_validations: RColumn?.constraints.custom_validations,
            key: unique_columns_data?.includes(RColumn.column_name)
              ? true
              : false,
            severity_level: severityLevel,
            is_active:
              RColumn && RColumn.is_active !== undefined
                ? RColumn.is_active
                : true,
          });
        }
      );
      // if (!isFileNameOnIdx) {
      //   formattedColumns.unshift({ ...defaultEditResourceCDMColumns });
      //   cdmData.unshift("file_name");
      // }
      if (!isFileNameOnIdx) {
        // Unshift the "file_name" column with id 0
        formattedColumns = [
          {
            ...defaultEditResourceCDMColumns,
            id: 0, // Set id to 0
          },
          ...formattedColumns.map((col: any) => ({
            ...col,
            id: col.id + 1, // Shift all other column ids by 1
          })),
        ];

        cdmData.unshift("file_name");
      }
      setResourceColumnFileData(formattedColumns);
      setInitialFileData(JSON.parse(JSON.stringify(formattedColumns)));
      setAvailColumns(cdmData);
      setAvailColumnsWithResourceDetail(null);
    }
  }, [resourceColumnData]);

  useEffect(() => {
    if (domainsData) {
      const currentDomain = domainsData?.find(
        (domain: any) => domain.id === resourceColumnData?.domain_id
      );
      setSelectedDomain(currentDomain);
    }
  }, [domainsData, resourceColumnData]);

  // Handle Events
  const handleAddValidation = (id: any) => {
    setSelectedRowId(id);
    setOpenValidationDialog(true);
  };
  const handleEditValidationDialog = (id: any, index: number) => {
    setOpenValidationDialog(true);
    setIsDailogEdit(true);
    setSelectedRowId(id);
    setDailogEditIndex(index);
  };
  const handleDeleteValidation = (index: number, rowId: number) => {
    const currentIndex = resourceColumnFileData.findIndex(
      (item: any) => item.id === rowId
    );
    const updatedData = resourceColumnFileData[
      currentIndex
    ].custom_validations.filter((item: any, i: number) => i !== index);
    setResourceColumnFileData((prev: any) => {
      const newData = prev.map((item: any) => {
        if (item.id === rowId) {
          item.custom_validations = updatedData;
        }
        return item;
      });
      return newData;
    });
  };

  const validateField = async (name: any, value: any) => {
    try {
      const partialFormData = { ...formData, [name]: value };
      await editResourceColumnSchema.validateAt(name, partialFormData);
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const handleValidations = async () => {
    try {
      for (const column of resourceColumnFileData) {
        const definition = column.reference_column_definition;
        if (definition?.source === "external") {
          if (
            !(definition?.linked_service_id ?? "") ||
            !(definition?.connection_key ?? "")
          ) {
            showToast(`Please Update Reference Definition`, "error");
            return;
          }
        }
        if (column.column_name === "" || column.datatype === "") {
          showToast(
            `Please fill in all "Column Name" and "Data Type" fields`,
            "warning"
          );
          return;
        } else if (
          column.datatype === "datetime" &&
          (column.data_format === null || column.data_format === "")
        ) {
          showToast(`Please fill in all "Format" field`, "warning");
          return;
        }
      }
      const allKeysBlank = resourceColumnFileData.every(
        (obj: any) =>
          obj.key === null ||
          obj.key === undefined ||
          obj.key === "" ||
          obj.key === false ||
          obj.key === "false"
      );
      if (allKeysBlank) {
        showToast(`Please provide a key for at least one field.`, "warning");
        return;
      }

      if (resourceColumnFileData.length < 1) {
        showToast(`Please add at least one row`, "error");
        return;
      }
      if (resourceColumnName === "" || resourceColumnName === undefined) {
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          resource_column_name: "Resource column name is required",
        }));
        return;
      } else if (resourceCode === "" || resourceCode === undefined) {
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          code: "Resource code is required",
        }));
        return;
      } else {
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          resource_column_name: undefined,
          code: undefined,
        }));
      }
      if (crossFieldValidations && crossFieldValidations.length > 0) {
        const isValidFilterRules = crossFieldValidations.every(
          (adhoc: { name: undefined; sql_query: undefined }) => {
            return adhoc.name !== "" && adhoc.sql_query !== "";
          }
        );
        if (!isValidFilterRules) {
          showToast(
            "Name and SQL Query cannot be empty in Adhoc Query!",
            "warning"
          );
          return;
        }
      }

      setOpenCommentConfirmation(true);
      return true;
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  // handel event when we save a resource
  const onSaveResourceColumn = async (event: any) => {
    event.preventDefault();
    formEventRef.current = event;
    setIsSaveClicked(!isSaveClicked);
    const columns = resourceColumnFileData.map((fileItem: any) => {
      const constraints: any = {
        datatype: fileItem?.datatype,
        is_mandatory: Boolean(fileItem["is_mandatory"]),
        data_format: fileItem?.data_format,
        column_length: fileItem?.column_length ? fileItem?.column_length : null,
        is_derived: fileItem?.is_derived,
        derived_column_definition: fileItem?.derived_column_definition,
        is_reference: fileItem?.is_reference,
        reference_column_definition: fileItem?.reference_column_definition,
        custom_validations: fileItem?.custom_validations,
      };

      if (fileItem?.is_reference) {
        constraints["is_reference"] = fileItem?.is_reference;
        constraints["reference_column_definition"] =
          fileItem?.reference_column_definition;
      }

      if (fileItem?.is_derived) {
        constraints["is_derived"] = fileItem?.is_derived;
        constraints["derived_column_definition"] =
          fileItem?.derived_column_definition;
      }
      if (fileItem?.custom_validations?.length > 0) {
        constraints["custom_validations"] = fileItem?.custom_validations;
      } else {
        constraints["custom_validations"] = null;
      }
      return {
        column_name: fileItem?.column_name,
        domain_column: fileItem?.domain_column,
        constraints,
        severity_level:
          fileItem?.severity_level === "high" ||
          fileItem?.severity_level === "High"
            ? 1
            : fileItem?.severity_level === "medium" ||
              fileItem?.severity_level === "Medium"
            ? 2
            : 3,
        is_active: fileItem?.is_active,
      };
    });

    try {
      const resourceColumnId = currentResourceColumnId;

      const uniqueData: any = [];
      resourceColumnFileData.forEach((item: any) => {
        if (item?.key) {
          uniqueData.push(item?.column_name);
        }
      });
      const reqBody2 = {
        name: resourceColumnName,
        domain_id: currentDomainId,
        domain_code: resourceColumnData?.domain_code,
        is_active: true,
        code: resourceCode,
        comment: formData?.comment,
        resource_column_properties: {
          unique_columns: uniqueData,
          resource_columns: columns,
          cross_field_validations: crossFieldValidations,
          inline_variables: globalVariables,
        },
      };
      updateResourceColumn({ resourceColumnId, payload: reqBody2 })
        .then((res) => {
          if (res) {
            setResourceColumnData(res);
            showToast("Resource Columns updated successfully!", "success");
          }
        })
        .catch((err) => {
          showToast(`Cannot update Resource Columns`, "error");
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            newErrors[error.path] = error.message;
          }
        );
      }
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    } finally {
      setFormData((prev: any) => ({
        ...prev,
        comment: "",
      }));
    }
  };
  useEffect(() => {
    setTimeout(() => {
      const eventRef = formEventRef.current;
      const findParentElement = eventRef?.target;
      let dataAttr: string;
      if (findParentElement) {
        const findChildElements = findParentElement?.querySelectorAll(
          ".form-control-autocomplete"
        );
        findChildElements.forEach((childElement: any, index: any) => {
          if (!dataAttr) {
            if (childElement.classList.contains("has-error")) {
              dataAttr = childElement
                .closest(".MuiPaper-elevation1")
                ?.getAttribute("data-myattr");
              setExpandedAccordion(dataAttr);
            }
          }
        });
      }
    }, 500);
  }, [isSaveClicked]);

  const onAddDerivedColumn = () => {
    setDerivedModalType("add");
    setOpenDerivedColumnDialog(true);
    setQueryBuilderTempValue("");
    // updateCDMColumns();
  };

  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
  };

  const debouncedHandleChangeAccordion = debounce((panel: any) => {
    setExpandedAccordion(panel);
  }, 300);
  const handleChangeAccordion =
    (panel: string) => (event: any, isExpanded: any) => {
      debouncedHandleChangeAccordion(isExpanded ? panel : null);
    };

  const handleEditReferenceDialog = (
    id: any,
    connectionKeys: any,
    linkedServices: any
  ) => {
    const currentIndex = resourceColumnFileData.findIndex(
      (item: any) => item.id === id
    );
    const referenceData = {
      source:
        resourceColumnFileData[currentIndex].reference_column_definition.source,
      source_type:
        resourceColumnFileData[currentIndex].reference_column_definition
          .source_type,
      use_translation:
        resourceColumnFileData[currentIndex].reference_column_definition
          .use_translation,
      connection_key: connectionKeys.find(
        (connectionKey: any) =>
          connectionKey?.id ===
          resourceColumnFileData[currentIndex].reference_column_definition
            ?.connection_key
      ),
      linked_service: linkedServices.find(
        (linkedServices: any) =>
          linkedServices?.id ===
          resourceColumnFileData[currentIndex].reference_column_definition
            ?.linked_service_id
      ),
      inline_variables:
        resourceColumnFileData[currentIndex].reference_column_definition
          ?.inline_variables,
    };
    setOpenReferenceDialog(true);
    setSelectedRowId(id);
    setReferenceData(referenceData);
  };
  const handleDeleteReference = (rowId: number) => {
    setResourceColumnFileData((prev: any) => {
      return prev.map((item: any) => {
        if (item.id === rowId) {
          item.is_reference = false;
          item.reference_column_definition = null;
        }
        return item;
      });
    });
  };
  const tabContent: any = useMemo(() => {
    return {
      resourceColumns: (
        <Box sx={{ marginTop: "-20px" }}>
          <DataTable
            dataColumns={columns}
            dataRows={resourceColumnFileData}
            checkboxSelection={false}
            className="dataTable no-radius table-focused"
            buttonText="Derived Column"
            buttonClass="btn-orange btn-sm btn-dark"
            buttonClick={onAddDerivedColumn}
            buttonIcon={<AddCircleOutlineIcon />}
            loading={isLoading}
            handleEditValidationDialog={handleEditValidationDialog}
            handleDeleteValidation={handleDeleteValidation}
            handleAddMoreRow={handleAddMoreRow}
            handleDeleteAllRows={handleDeleteAllRows}
            handleEditReferenceDialog={handleEditReferenceDialog}
            handleDeleteReference={handleDeleteReference}
            paginationModel={{
              page: page - 1,
              pageSize: pSize,
            }}
            onPaginationModelChange={(params: any) => {
              if (params.pageSize !== pSize || params.page !== page - 1) {
                setPage(params.page + 1);
                setPSize(params.pageSize);
              }
            }}
            isPaginationChangeRequiredOnClientSide={true}
          />
        </Box>
      ),
      customValidationSummary: (
        <Box sx={{ marginTop: "-20px" }}>
          <CustomValidationSummary
            customData={resourceColumnData}
            resourceColumns={resourceColumnFileData}
          />
        </Box>
      ),
      crossFieldValidations: (
        <>
          <CrossFieldValidations
            crossFieldValidations={crossFieldValidations}
            setCrossFieldValidations={setCrossFieldValidations}
            isViewOnly={false}
            errors={errors}
            setErrors={setErrors}
          />
        </>
      ),
    };
  }, [
    columns,
    resourceColumnFileData,
    onAddDerivedColumn,
    isLoading,
    handleEditValidationDialog,
    handleDeleteValidation,
    resourceColumnData,
  ]);

  const accordionContent: any = {
    resourceData: (
      <>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <TextField
            type="text"
            fullWidth
            label={
              <span>
                Domain<span className="required-asterisk">*</span>
              </span>
            }
            variant="outlined"
            className="form-control-autocomplete"
            value={
              domainsData?.find(
                (option: { id: any }) => option.id === currentDomainId
              )?.domain_name || ""
            }
            disabled
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <TextField
            type="text"
            title="Enter Resource Column Name"
            name="resource_column_name"
            // placeholder="Ex: Sample Resource Column Name"
            onChange={handleResourceColumnName}
            //onBlur={(e) => validateField(e.target.name, e.target.value)}
            fullWidth
            label={
              <span>
                Resource Column Name{" "}
                <span className="required-asterisk">*</span>
              </span>
            }
            value={resourceColumnName || ""}
            variant="outlined"
            className={`form-control-autocomplete ${
              errors?.resource_column_name ? "has-error" : ""
            }`}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors?.resource_column_name}
            helperText={errors?.resource_column_name || ""}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
          <TextField
            type="text"
            name="code"
            // placeholder="Ex: Sample Resource Column Name"
            // onChange={handleResourceCode}
            //onBlur={(e) => validateField(e.target.name, e.target.value)}
            fullWidth
            label={
              <span>
                Code
                <span className="required-asterisk">*</span>
              </span>
            }
            value={resourceCode || ""}
            variant="outlined"
            className={`form-control-autocomplete ${
              errors?.code ? "has-error" : ""
            }`}
            InputLabelProps={{
              shrink: true,
            }}
            error={!!errors?.code}
            helperText={errors?.code || ""}
            disabled
          />
        </Grid>
      </>
    ),
  };

  useEffect(() => {
    setPageContext("resourceColumn");
  }, []);
  const handleCommentChange = (e: any) => {
    setFormData((prev: any) => ({
      ...prev,
      comment: e.target.value,
    }));
  };

  const handleSaveComment = async (e: any) => {
    setOpenCommentConfirmation(false);
    onSaveResourceColumn(e);
  };
  const handleCancelComment = () => {
    setOpenCommentConfirmation(false);
    setFormData((prev: any) => ({
      ...prev,
      comment: "",
    }));
  };
  return (
    <>
      <Box>
        <form autoComplete="off">
          <Box className="dashboard-title-group mb-6 flex-end">
            <div className="right-column">
              <Button
                variant="contained"
                color="secondary"
                fullWidth
                className="btn-orange"
                title="Save Resource"
                type="button"
                onClick={() => handleValidations()}
                disabled={!hasChanges}
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </div>
          </Box>

          <Box sx={{ marginTop: "6px" }} className="accordion-panel">
            {ACCORDION_HEADER &&
              Object.keys(ACCORDION_HEADER).map((item: any, idx: any) => {
                if (
                  item === "linkedService" &&
                  formData?.aggregation_type === "aggregated"
                ) {
                  return null;
                }
                return (
                  <Accordion
                    className="heading-bold box-shadow mb-16"
                    expanded={expandedAccordion === item}
                    onChange={handleChangeAccordion(item)}
                    data-myattr={`${item}`}
                    key={item}
                  >
                    <AccordionSummary
                      aria-controls="panel1d-content"
                      id="panel1d-header"
                      expandIcon={<ExpandMoreIcon />}
                    >
                      {ACCORDION_HEADER[item]}
                    </AccordionSummary>
                    <AccordionDetails sx={{ paddingTop: "16px" }}>
                      <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                        {accordionContent[item]}
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                );
              })}
          </Box>
          <Tabs
            sx={{
              mt: 1,
            }}
            value={activeTab}
            onChange={handleChangeTab}
            className="mui-tabs alternative-1"
            variant="scrollable"
          >
            <Tab label="Resource Columns" value="resourceColumns" />
            <Tab
              label="Custom Validation Summary"
              value="customValidationSummary"
            />
            <Tab label="Adhoc Query" value="crossFieldValidations" />
          </Tabs>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <Box>{tabContent[activeTab]}</Box>
          </Grid>

          <Grid
            mx={{
              marginTop: "6px",
              justifyContent: "flex-end",
              display: "flex",
            }}
          >
            <Button
              type="button"
              onClick={() => handleValidations()}
              variant="contained"
              color="secondary"
              className="btn-orange"
              disabled={!hasChanges}
            >
              <SaveOutlinedIcon /> &nbsp; Save
            </Button>
          </Grid>
          <ValidationDialog setAllVariablesList={setAllVariablesList} />

          <DerivedColumnDialog setAllVariablesList={setAllVariablesList} />
          <ReferenceDialog setAllVariablesList={setAllVariablesList} />
        </form>
      </Box>
      <ConfirmDailog
        isDailogOpen={isDailogOpen}
        handleCancelAction={handleCancelDeleteAction}
        handleConfirmAction={handleConfirmDeleteAction}
        dailogTitle={"Confirm Delete"}
        dailogDescription={
          "This action will delete all rows, still want to continue?"
        }
      />
      <CommentBeforeUpdateDialog
        title={"Confirm updating Resource Column Details"}
        dialogContent={
          <div>
            <p className="m-0 mb-2">
              Add a note before updating the Resource Column Details
            </p>
            <textarea
              value={formData?.comment}
              className={`form-control-1 max-60`}
              onChange={(e) => handleCommentChange(e)}
            />
          </div>
        }
        openConfirmation={openCommentConfirmation}
        handleSaveComment={(e) => handleSaveComment(e)}
        handleCancel={handleCancelComment}
      />
    </>
  );
};

export default EditResourceColumn;
