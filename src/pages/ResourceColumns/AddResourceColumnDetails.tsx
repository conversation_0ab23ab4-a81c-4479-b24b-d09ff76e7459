import React, { useState, useEffect, useRef } from "react";
import FlexBetween from "../../components/FlexBetween";
import { saveAs } from "file-saver";
import { useToast } from "../../services/utils";
import ReactDOM from "react-dom";
import {
  Box,
  Grid,
  Input,
  IconButton,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  Tooltip,
  TextField,
  Backdrop,
  CircularProgress,
  NativeSelect,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
} from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import FileUploadButton from "../../components/Uploaders/FileUpload";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { read, utils } from "xlsx";
import {
  manageAvailableColumns,
  setDataType,
  setSeverityLevel,
} from "../../services/utils";
import DataTable from "../../components/DataGrids/DataGrid";
import {
  datatypes,
  dateTimeFormat,
  severity_level,
} from "../../services/constants";
import { useNavigate } from "react-router-dom";
import { IDomainsData } from "../../types/domain";
import { addResourceColumns } from "../../services/resourcesService";
import AutoCompleteDomainList from "../../components/Molecules/Domain/AutoCompleteDomainList";
import { addDomain } from "../../services/domainsService";
import DerivedColumnDialog from "../../components/Dialogs/DerivedColumnDialog";
import ReferenceDialog from "../../components/Dialogs/ReferenceDialog";
import ValidationDialog from "../../components/Dialogs/ValidationDialog";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import {
  defaultResourceCDMColumns,
  resourceCDMColumns,
} from "../../services/constants/resource";
import {
  addResourceColumnDetailSchema,
  addResourceNewColumnSchema,
} from "../../schemas";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { useResourceContext } from "../../contexts/ResourceContext";
import ConfirmDailog from "../../components/Dialogs/ConfirmDailog";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import InfoIcon from "@mui/icons-material/Info";
import CheckboxWithKeyboardEvent from "../../components/Molecules/Resource/CheckboxWithKeyboardEvent";
import { expectedResourceColumnHeaders } from "../../services/constants/fileHeaders";
import FileHeaderToastifyMsg from "../../components/Toastify/FileHeaderToastifyMsg";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import CrossFieldValidations from "./CrossFieldValidations";
import {
  IconAddRowBase,
  IconDeleteBlueSvg,
  IconEditBase,
  IconPlusBase,
} from "../../common/utils/icons";

const AddResourceColumnDetails: React.FC = () => {
  const {
    setIsDailogEdit,
    setDailogEditIndex,
    // SetCDMColumns,
    setSelectedRowId,
    setOpenValidationDialog,
    resourceColumnFileData,
    setResourceColumnFileData,
    setPageContext,
    formData,
    setFormData,
    currentDomainId,
    setCurrentDomainId,
    setIsLoading,
    errors,
    setErrors,
    setOpenDerivedColumnDialog,
    setDerivedModalType,
    setOpenReferenceDialog,
    setReferenceData,
  } = useResourceContext();
  const {
    availColumns,
    setAvailColumns,
    setAvailColumnsWithResourceDetail,
    globalVariables,
    setQueryBuilderTempValue,
  } = useRuleResourceContext();
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(100);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const [selectedDomain, setSelectedDomain] = useState<IDomainsData>();
  const [domainsTableData, setDomainsTableData] = useState<any>([]);
  const [currentType, setCurrentType] = useState<any>("existing");
  const [backdropLoading, setBackdropLoading] = useState(true);
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [isFileUpload, setIsFileUpload] = useState<boolean>(false);
  const [isDailogOpen, setIsDailogOpen] = useState(false);
  const [setAllVariablesList] = useState({});
  const [oldColumnName, setOldColumnName] = useState("");
  const [activeTab, setActiveTab] = useState<string>("resourceColumns");
  const [crossFieldValidations, setCrossFieldValidations] = useState<any>([]);

  useEffect(() => {
    setResourceColumnFileData([defaultResourceCDMColumns]);
    setPageContext("resourceColumn");
    setBackdropLoading(false);
  }, []);
  // hooks
  const navigate = useNavigate();
  const { showToast } = useToast();

  const handleAddValidation = (id: any) => {
    setSelectedRowId(id);
    setOpenValidationDialog(true);
  };
  const handleEditValidationDialog = (id: any, index: number) => {
    setOpenValidationDialog(true);
    setIsDailogEdit(true);
    setSelectedRowId(id);
    setDailogEditIndex(index);
  };
  const handleDeleteValidation = (index: number, rowId: number) => {
    const currentIndex = resourceColumnFileData.findIndex(
      (item: any) => item.id === rowId
    );
    const updatedData = resourceColumnFileData[
      currentIndex
    ].custom_validations.filter((item: any, i: number) => i !== index);
    setResourceColumnFileData((prev: any) => {
      const newData = prev.map((item: any) => {
        if (item.id === rowId) {
          item.custom_validations = updatedData;
        }
        return item;
      });
      return newData;
    });
  };
  const handleEditReferenceDialog = (
    id: any,
    connectionKeys: any,
    linkedServices: any
  ) => {
    const currentIndex = resourceColumnFileData.findIndex(
      (item: any) => item.id === id
    );
    const referenceData = {
      source:
        resourceColumnFileData[currentIndex].reference_column_definition.source,
      source_type:
        resourceColumnFileData[currentIndex].reference_column_definition
          .source_type,
      use_translation:
        resourceColumnFileData[currentIndex].reference_column_definition
          .use_translation,
      connection_key: connectionKeys.find(
        (connectionKey: any) =>
          connectionKey?.id ===
          resourceColumnFileData[currentIndex].reference_column_definition
            ?.connection_key
      ),
      linked_service: linkedServices.find(
        (linkedServices: any) =>
          linkedServices?.id ===
          resourceColumnFileData[currentIndex].reference_column_definition
            ?.linked_service_id
      ),
      inline_variables:
        resourceColumnFileData[currentIndex].reference_column_definition
          ?.inline_variables,
    };
    setOpenReferenceDialog(true);
    setSelectedRowId(id);
    setReferenceData(referenceData);
  };
  const handleDeleteReference = (rowId: number) => {
    setResourceColumnFileData((prev: any) => {
      return prev.map((item: any) => {
        if (item.id === rowId) {
          item.is_reference = false;
          item.reference_column_definition = null;
        }
        return item;
      });
    });
  };
  const handleAddMoreRow = () => {
    let newRow = { ...resourceCDMColumns };
    const highestId = resourceColumnFileData.reduce(
      (maxId: any, item: any) => Math.max(maxId, item.id),
      -1
    );
    newRow.id = highestId + 1;
    const updatedData = [...resourceColumnFileData, newRow];
    setResourceColumnFileData(updatedData);
    const newTotalRows = updatedData.length;
    const maxRowsPerPage = pSize;
    const newPageIndex = Math.floor((newTotalRows - 1) / maxRowsPerPage);
    setTimeout(() => {
      setPage(newPageIndex + 1);
      setTimeout(() => {
        const lastInput = inputRefs.current[updatedData.length - 1];
        if (lastInput) {
          const domNode = ReactDOM.findDOMNode(lastInput);
          if (domNode instanceof HTMLElement) {
            domNode?.focus();
          }
        }
      }, 700);
    }, 300);
  };
  const handleDeleteAllRows = () => {
    if (resourceColumnFileData && resourceColumnFileData.length > 0) {
      setIsDailogOpen(true);
    }
  };
  const handleCancelDeleteAction = () => {
    setIsDailogOpen(false);
  };
  const handleConfirmDeleteAction = () => {
    setIsDailogOpen(false);
    setResourceColumnFileData([resourceColumnFileData[0]]);
    setAvailColumns([]);
    setAvailColumnsWithResourceDetail(null);
  };

  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
  };

  // Define table column names
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      renderCell: (params: any) => (
        <IconButton
          size="small"
          tabIndex={-1}
          disabled={
            params?.row?.is_derived ||
            params?.row?.custom_validations?.length > 0 ||
            params?.row?.is_reference ||
            params?.row?.reference_column_definition != null
              ? false
              : true
          }
          onClick={() => {
            // toggle in array
            if (expandedRow.includes(params.row.id)) {
              setExpandedRow((prev: any) =>
                prev.filter((item: any) => item !== params.row.id)
              );
            } else {
              setExpandedRow((prev: any) => [...prev, params.row.id]);
            }
          }}
        >
          {params?.row?.is_derived ||
          params?.row?.custom_validations?.length > 0 ||
          params?.row?.is_reference ||
          params?.row?.reference_column_definition != null ? (
            <ExpandMoreIcon
              sx={{
                transform: `rotateZ(${
                  expandedRow.includes(params.row.id) ? 180 : 0
                }deg)`,
                transition: (theme) =>
                  theme.transitions.create("transform", {
                    duration: theme.transitions.duration.shortest,
                  }),
              }}
              fontSize="inherit"
            />
          ) : null}
        </IconButton>
      ),
    },
    {
      field: "column_name",
      headerName: "Resource Column",
      width: 120,
      minWidth: 150,
      flex: 1,
      renderCell: (params: any) => {
        const handleFocusIn = (e: React.FocusEvent<HTMLInputElement>) => {
          setOldColumnName(e.target.value);
        };
        const handleOnBlur = (e: React.FocusEvent<HTMLInputElement>) => {
          const updatedColumns = manageAvailableColumns(
            availColumns,
            oldColumnName,
            oldColumnName && oldColumnName.length > 0 ? "update" : "add",
            params.row.column_name
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
        };
        const valueField = resourceColumnFileData.find(
          (item: any) => item.id === params.row.id
        )?.column_name;
        const isNameFilled = valueField !== "";
        const handleChangeName = (e: any) => {
          const value = e.target.value;
          if (value.toLowerCase() === "file_name") {
            const isFileNameExist = resourceColumnFileData.some(
              (fileItem: any) =>
                fileItem.column_name.toLowerCase() === "file_name"
            );
            if (isFileNameExist) {
              showToast(
                `"file_name" is reserved key column, and already exist, please provide different name.`,
                "warning"
              );
              return;
            }
          }
          setResourceColumnFileData((prev: any) => {
            const newData = prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.column_name = value;
              }
              return item;
            });
            return newData;
          });
        };

        return (
          <Tooltip title={valueField} placement="top">
            <input
              ref={(el) => (inputRefs.current[params?.id] = el)}
              value={
                resourceColumnFileData.find(
                  (item: any) => item.id === params.row.id
                )?.column_name
              }
              onChange={handleChangeName}
              className={`form-control-1  input-ellipsis ${
                !isNameFilled ? "border-red" : ""
              }`}
              onKeyDown={(e: any) => {
                e.stopPropagation();
              }}
              onBlur={handleOnBlur}
              onFocus={handleFocusIn}
            />
          </Tooltip>
        );
      },
    },
    {
      field: "domain_column",
      headerName: "Domain Column",
      width: 120,
      minWidth: 130,
      flex: 1,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.domain_column = value;
              }
              return item;
            })
          );
        };

        const menuData =
          currentType === "existing"
            ? selectedDomain?.domain_properties?.columns
            : domainsTableData;

        return (
          <Tooltip title={params.value} placement="top">
            <NativeSelect
              value={params.value}
              className="white-space-nowrap form-control capitalize"
              onChange={handleChange}
            >
              <option key={"Select..."} value={""}>
                Select...
              </option>
              {menuData?.length > 0 &&
                menuData.map((domainColumnItem: any) => (
                  <option
                    key={domainColumnItem?.name}
                    value={domainColumnItem?.name}
                  >
                    {domainColumnItem?.name}
                  </option>
                ))}
            </NativeSelect>
          </Tooltip>
        );
      },
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 100,
      renderCell: (params) => {
        const dataTypeValue = resourceColumnFileData.find(
          (item: any) => item.id === params.row.id
        )?.datatype;
        const isDataTypeFilled = dataTypeValue !== "" && dataTypeValue !== null;
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.datatype = value;
                item.data_format = "";
              }
              return item;
            })
          );
        };

        return (
          <NativeSelect
            value={params?.value?.trim()}
            className={`white-space-nowrap format-arrow-pos capitalize ${
              !isDataTypeFilled ? "border-red-parent" : ""
            }`}
            onChange={handleChange}
          >
            <option key={"Select..."} value={""}>
              Select...
            </option>
            {datatypes.map((typeItem) => (
              <option key={typeItem} value={typeItem}>
                {typeItem}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "is_mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      align: "center",
      renderCell: (params) => {
        const cellValue =
          params.value && typeof params.value === "string"
            ? params.value.trim()
            : params.value;
        const handleChange = (event: any) => {
          const value = event.target.checked;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.is_mandatory = value;
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      align: "center",
      renderCell: (params) => {
        const cellValue =
          params.value && typeof params.value === "string"
            ? params.value.trim()
            : params.value;
        const handleChange = (event: any) => {
          const value = event.target.checked;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.key = value;
                if (params.row.is_active !== true && item.key === true) {
                  item.is_active = true;
                }
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "is_reference",
      headerName: "Reference",
      description: "Reference Data",
      maxWidth: 90,
      align: "center",
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.checked;
          if (value) {
            setOpenReferenceDialog(value);
            setSelectedRowId(params.row.id);
            setReferenceData({
              source: "internal",
              source_type: "",
              use_translation: false,
            });
          } else {
            setResourceColumnFileData((prev: any) =>
              prev.map((item: any) => {
                if (item.id === params.row.id) {
                  item.is_reference = value;
                  item.reference_column_definition = null;
                }
                return item;
              })
            );
          }
        };
        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "is_active",
      headerName: "Act./Inac.",
      maxWidth: 70,
      align: "center",
      renderCell: (params) => {
        const cellValue =
          params.value && typeof params.value === "string"
            ? params.value.trim()
            : params.value;
        const handleChange = async (event: any) => {
          const value = event.target.checked;

          const isTruthy = ["Y", "y", "Yes", "yes", true, "true"].includes(
            params.row.key
          );
          if (params.row.is_active === true && isTruthy) {
            showToast(
              `If you want to uncheck 'Active/Inactive,' please uncheck the 'Key' column first.`,
              "warning"
            );
            return;
          }
          const updatedColumns = manageAvailableColumns(
            availColumns,
            params.row.column_name,
            "toggle"
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.is_active = value;
              }
              return item;
            })
          );
        };

        return (
          <CheckboxWithKeyboardEvent
            handleChange={handleChange}
            params={params}
          />
        );
      },
    },
    {
      field: "column_length",
      headerName: "Length",
      width: 100,
      renderCell: (params) => {
        const handleChange = (event: any) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                // item.column_length = parseInt(value, 10);
                const parsedValue = parseInt(value, 10);
                item.column_length = isNaN(parsedValue) ? null : parsedValue;
              }
              return item;
            })
          );
        };

        return (
          <Input
            // placeholder="Ex: Sample Length"
            value={(
              resourceColumnFileData
                ?.find((item: any) => item.id === params.row.id)
                ?.column_length?.toString() || ""
            ).replace(/ /g, "")}
            onChange={handleChange}
            onKeyDown={(e: any) => {
              e.stopPropagation();
            }}
            className="form-control input-ellipsis"
          />
        );
      },
    },
    {
      field: "severity_level",
      headerName: "Severity",
      width: 100,
      renderCell: (params) => {
        const severityLevelValue = resourceColumnFileData
          .find((item: any) => item.id === params.row.id)
          ?.severity_level?.toLowerCase();
        const handleChange = (event: any) => {
          const value = event.target.value as string;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                return { ...item, severity_level: value };
              }
              return item;
            })
          );
        };

        return (
          <NativeSelect
            value={severityLevelValue}
            className="white-space-nowrap capitalize"
            onChange={handleChange}
            style={{ width: "100%", height: 35 }}
            placeholder="Select..."
          >
            {severity_level.map((severity: string) => (
              <option key={severity} value={severity}>
                {severity}
              </option>
            ))}
          </NativeSelect>
        );
      },
    },
    {
      field: "data_format",
      headerName: "Format",
      width: 120,
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        const cellValue =
          params.value && typeof params.value === "string"
            ? params.value.trim()
            : params.value;
        const handleChange = (event: { target: { value: any } }) => {
          const value = event.target.value;
          setResourceColumnFileData((prev: any) =>
            prev.map((item: any) => {
              if (item.id === params.row.id) {
                item.data_format = value;
              }
              return item;
            })
          );
        };

        return (
          <>
            {!(
              resourceColumnFileData.find(
                (item: any) => item.id === params.row.id
              )?.datatype === "datetime"
            ) ? (
              <Input
                // placeholder="Ex: Sample Enter Format"
                value={
                  resourceColumnFileData?.find(
                    (item: any) => item.id === params.row.id
                  )?.data_format || ""
                }
                onChange={handleChange}
                onKeyDown={(e: any) => {
                  e.stopPropagation();
                }}
                className="form-control input-ellipsis"
              />
            ) : (
              <Tooltip
                title={dateTimeFormat.includes(cellValue) ? cellValue : ""}
                placement="top"
                arrow
              >
                <NativeSelect
                  value={dateTimeFormat.includes(cellValue) ? cellValue : ""}
                  className={`white-space-nowrap format-arrow-pos form-control ${
                    !dateTimeFormat.includes(params.value)
                      ? "border-red-parent"
                      : ""
                  }`}
                  style={{ width: "100%", height: 35 }}
                  onChange={handleChange}
                >
                  <option key={"Select..."} value={""}>
                    Select...
                  </option>
                  {dateTimeFormat.map((format: string, index: number) => (
                    <option key={format + index} value={format}>
                      {format}
                    </option>
                  ))}
                </NativeSelect>
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 165,
      renderCell: (params) => {
        const handleDelete = () => {
          setResourceColumnFileData((prev: any) =>
            prev.filter((item: any) => item.id !== params.row.id)
          );
          const updatedColumns = manageAvailableColumns(
            availColumns,
            params.row.column_name,
            "remove"
          );
          setAvailColumns(updatedColumns);
          setAvailColumnsWithResourceDetail(null);
        };

        const handleAddNewRow = () => {
          let newRow = { ...resourceCDMColumns };
          const currentRowIdx = resourceColumnFileData.findIndex(
            (item: any) => item.id === params.row.id
          );
          const highestId = resourceColumnFileData.reduce(
            (maxId: any, item: any) => Math.max(maxId, item.id),
            -1
          );
          newRow.id = highestId + 1;
          const updatedData = [...resourceColumnFileData];
          updatedData.splice(currentRowIdx + 1, 0, newRow);
          setResourceColumnFileData(updatedData);
        };

        return (
          <>
            <Tooltip title="Add Validation" placement="top" arrow>
              <IconButton
                color="inherit"
                onClick={() => {
                  handleAddValidation(params.row.id);
                  //updateCDMColumns();
                }}
                className="datagrid-action-btn"
              >
                {/* <AddOutlined /> */}
                <IconPlusBase />
              </IconButton>
            </Tooltip>
            {params.row.is_derived && (
              <Tooltip title="Edit Derived Column" placement="top" arrow>
                <IconButton
                  color="inherit"
                  onClick={() => {
                    // onAddDerivedColumn();
                    setOpenDerivedColumnDialog(true);
                    setDerivedModalType("edit");
                    setSelectedRowId(params.row.id);
                  }}
                  className="datagrid-action-btn"
                >
                  <IconEditBase />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Add Column" placement="top" arrow>
              <Button
                color="secondary"
                onClick={handleAddNewRow}
                className="datagrid-action-btn min-width-40"
              >
                <IconAddRowBase />
              </Button>
            </Tooltip>
            <Tooltip title="Delete Row" placement="top" arrow>
              <IconButton
                color="inherit"
                onClick={handleDelete}
                className="datagrid-action-btn"
              >
                <IconDeleteBlueSvg />
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  // Set forma data in state
  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (e.target.name === "resource_column_name")
      validateField("name", e.target.value);
    if (e.target.name === "domain_code")
      validateField("domain_code", e.target.value);

    const { name, value } = e.target;
    if (name === "domain_name") {
      setErrors((prevError: any) => ({
        ...prevError,
        domain_name: value ? "" : "Please enter domain name",
        domain_id: "",
        code: "",
      }));
    }
  };
  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      if (currentType === "new") {
        await addResourceNewColumnSchema.validateAt(name, partialFormData);
      } else {
        await addResourceColumnDetailSchema.validateAt(name, partialFormData);
      }
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors: any) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const handleDownloadSampleFile = () => {
    const excelFilePath = require("../../assets/sample_files/sample_file.xlsx");
    saveAs(excelFilePath, "CDM_AllData_Sample.xlsx");
  };

  // handel event when we save a resource
  const onSaveResourceColumn = async (e: any) => {
    e.preventDefault();

    for (const column of resourceColumnFileData) {
      if (column.column_name === "" || column.datatype === "") {
        showToast(
          `Please fill in all "Column Name" and "Data Type" fields`,
          "warning"
        );
        return;
      } else if (
        column.datatype === "datetime" &&
        (column.data_format === null || column.data_format === "")
      ) {
        showToast(`Please fill in all "Format" field`, "warning");
        return;
      }
    }
    const allKeysBlank = resourceColumnFileData.every(
      (obj: any) =>
        obj.key === null ||
        obj.key === undefined ||
        obj.key === "" ||
        obj.key === false ||
        obj.key === "false"
    );

    if (resourceColumnFileData.length > 0 && allKeysBlank) {
      showToast(`Please provide a key for at least one field.`, "warning");
      return;
    }
    const columns = resourceColumnFileData.map((fileItem: any) => {
      const constraints: any = {
        datatype: fileItem?.datatype,
        is_mandatory:
          fileItem?.is_mandatory == "Y" ||
          fileItem?.is_mandatory == "y" ||
          fileItem?.is_mandatory == "yes" ||
          fileItem?.is_mandatory == "Yes" ||
          fileItem?.is_mandatory == true
            ? true
            : false,
        data_format: fileItem?.data_format,
        column_length: fileItem?.column_length
          ? parseInt(
              (fileItem?.column_length?.toString() || "").replace(/ /g, ""),
              10
            )
          : null,
      };

      if (fileItem?.is_reference) {
        constraints["is_reference"] = fileItem?.is_reference;
        constraints["reference_column_definition"] =
          fileItem?.reference_column_definition;
      }

      if (fileItem?.is_derived) {
        constraints["is_derived"] = fileItem?.is_derived;
        constraints["derived_column_definition"] =
          fileItem?.derived_column_definition;
      }

      if (fileItem?.custom_validations?.length > 0) {
        constraints["custom_validations"] = fileItem?.custom_validations;
      } else {
        constraints["custom_validations"] = null;
      }
      return {
        column_name: fileItem?.column_name,
        domain_column: fileItem?.domain_column,
        is_active: fileItem?.is_active,
        severity_level:
          fileItem.severity_level === "high" ||
          fileItem.severity_level === "High"
            ? 1
            : fileItem.severity_level === "medium" ||
              fileItem.severity_level === "Medium"
            ? 2
            : 3,
        constraints,
      };
    });

    const existingColumns = resourceColumnFileData.map((fileItem: any) => {
      return {
        name: fileItem?.column_name,
        is_active: fileItem?.is_active,
        datatype: fileItem?.datatype,
        mandatory: ["Y", "y", "Yes", "yes", true].includes(
          fileItem?.is_mandatory
        )
          ? true
          : false,
        format: fileItem?.data_format,
      };
    });

    const uniqueColumns = resourceColumnFileData
      .filter(
        (fdItem: any) =>
          fdItem.key === "y" ||
          fdItem.key === "Y" ||
          fdItem.key === "yes" ||
          fdItem.key === "Yes" ||
          fdItem.key === true
      )
      .map((cItem: any) => cItem.column_name);

    const saveResource = async (domainId = null) => {
      try {
        if (crossFieldValidations && crossFieldValidations.length > 0) {
          const isValidFilterRules = crossFieldValidations.every(
            (adhoc: { name: undefined; sql_query: undefined }) => {
              return adhoc.name !== "" && adhoc.sql_query !== "";
            }
          );
          if (!isValidFilterRules) {
            showToast(
              "Name and SQL Query cannot be empty in Adhoc Query!",
              "warning"
            );
            return;
          }
        }
        const reqBody2 = {
          domain_id: currentDomainId || domainId,
          resource_type: formData.resource_column_name,
          is_active: formData.is_active,
          name: formData?.resource_column_name,
          code: formData?.code,
          domain_code: formData?.domain_code,
          resource_column_properties: {
            unique_columns:
              currentType === "new"
                ? reqBody1?.domain_properties?.unique_columns
                : uniqueColumns,
            resource_columns: columns,
            cross_field_validations: crossFieldValidations,
            inline_variables: globalVariables,
          },
        };
        if (resourceColumnFileData.length < 1) {
          showToast(`Please add at least one row`, "error");
          return;
        } else if (currentType === "new") {
          await addResourceNewColumnSchema.validate(reqBody2, {
            abortEarly: false,
          });
        } else {
          await addResourceColumnDetailSchema.validate(reqBody2, {
            abortEarly: false,
          });
        }
        addResourceColumns(reqBody2)
          .then((response) => {
            if (response) {
              showToast("Resource Columns created successfully!", "success");
              setFormData({
                id: 1,
                resource_name: "",
                resource_type: "",
                resource_prefix: "",
                domain_id: null,
                resource_location: "local",
                resource_path: "",
                aggregation_type: "flat",
                filter_rules: [],
                additional_resource_data: [],
              });
              setErrors({
                resource_name: "",
                resource_type: "",
                resource_prefix: "",
                domain_id: "",
                resource_location: "",
                resource_path: "",
                aggregation_type: "",
                filter_rules: [],
              });
              setAvailColumns([]);
              setAvailColumnsWithResourceDetail(null);
              navigate(
                `/resource/add/${Number(
                  currentDomainId || domainId
                )}?resourceColumn=${Number(response.id)}`
              );
            }
          })
          .catch((error) => {
            console.error(`Cannot create Resource Columns`);
          });
      } catch (validationErrors: any) {
        const newErrors: { [key: string]: string } = {};
        if (validationErrors.inner) {
          validationErrors.inner.forEach(
            (error: { path: string | number; message: string }) => {
              newErrors[error.path] = error.message;
            }
          );
        }
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          ...newErrors,
        }));
      }
    };
    let reqBody1: any = {};

    if (currentType === "new") {
      try {
        if (formData.domain_name === "") return;
        reqBody1 = {
          domain_name: formData?.domain_name || "",
          domain_code: formData?.domain_code,
          domain_desc: "",
          domain_properties: {
            unique_columns: uniqueColumns,
            columns: existingColumns,
          },
        };
        const reqBody2 = {
          domain_id: 0,
          domain_code: formData?.domain_code,
          resource_type: formData.resource_column_name,
          name: formData.resource_column_name,
          code: formData.code,
        };
        if (resourceColumnFileData.length < 1) {
          showToast(`Please add at least one row`, "error");
          return;
        }
        if (reqBody1?.domain_name === "") {
          setErrors((prevError: any) => ({
            ...prevError,
            domain_name: "Please enter domain name",
          }));

          await addResourceNewColumnSchema.validate(reqBody2, {
            abortEarly: false,
          });
          return;
        } else {
          await addResourceNewColumnSchema.validate(reqBody2, {
            abortEarly: false,
          });
        }
        addDomain(reqBody1)
          .then((response) => {
            setCurrentDomainId(response.id);
            saveResource(response.id);
          })
          .catch((error) => {
            console.log(error);
            // toast.error(`Cannot create domain`);
            console.error("Cannot create domain");
          });
      } catch (validationErrors: any) {
        const newErrors: { [key: string]: string } = {};
        if (validationErrors.inner) {
          validationErrors.inner.forEach(
            (error: { path: string | number; message: string }) => {
              newErrors[error.path] = error.message;
            }
          );
        }
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          ...newErrors,
        }));
      }
    } else {
      saveResource();
    }
  };

  const handleFileUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const data = e.target?.result;
      const workbook = read(data, { type: "binary" });
      const firstSheet = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheet];
      const json: any = utils.sheet_to_json(worksheet, { defval: "" });
      const headers: string[] = Object.keys(json[0]).filter(
        (header) => !/^__EMPTY/.test(header)
      );
      const unmatchedHeaders = expectedResourceColumnHeaders.filter(
        (header: any) => !headers.includes(header)
      );
      if (unmatchedHeaders.length >= 1) {
        showToast(
          <FileHeaderToastifyMsg unmatchedHeaders={unmatchedHeaders} />,
          "warning"
        );
        return;
      } else {
        const hasReservedKey = json.some((fileItem: any) => {
          return fileItem["Column Name"].trim().toLowerCase() === "file_name";
        });
        const doaminsTableData: any = [];
        const cdmData: string[] = [];
        const updatedJson = json.filter(
          (fileItem: any) => fileItem["Column Name"].trim() !== "file_name"
        );
        const initialFileData = updatedJson.map(
          (fileItem: any, index: number) => {
            cdmData.push(fileItem["Column Name"].trim());
            doaminsTableData.push({
              name: fileItem["Column Name"].trim() ?? "",
            });
            const dataType: any =
              typeof fileItem["Data Type"] === "string"
                ? fileItem["Data Type"].trim().toLowerCase()
                : "";
            const isVarChar = dataType && dataType === "varchar";
            const severity =
              typeof fileItem["Severity Level"] === "string"
                ? fileItem["Severity Level"].toLowerCase()
                : "Low";
            return {
              id: index + 1, // changed because we need to add a default column with fileName
              domain_column:
                currentType === "new" ? fileItem["Column Name"].trim() : "",
              column_name: fileItem["Column Name"].trim() ?? "",
              datatype:
                isVarChar || datatypes.includes(dataType)
                  ? setDataType(dataType)
                  : "",
              is_mandatory:
                (typeof fileItem["Mandatory"] === "string"
                  ? fileItem["Mandatory"].trim()
                  : fileItem["Mandatory"] === 1
                  ? true
                  : false) ?? "",
              data_format: fileItem["Data Format"] ?? null,
              key:
                (typeof fileItem["Key"] === "string"
                  ? fileItem["Key"].trim()
                  : fileItem["Key"] === 1
                  ? true
                  : false) ?? "",
              is_active: fileItem["is_active"] ?? true,
              action: "",
              column_length: fileItem["Column Length"] ?? "",
              severity_level: severity_level.includes(severity)
                ? setSeverityLevel(severity)
                : "Low",
              reference: fileItem["Reference"] ?? "",
            };
          }
        );
        setDomainsTableData(doaminsTableData);
        cdmData.unshift("file_name");
        setAvailColumns(cdmData);
        setAvailColumnsWithResourceDetail(null);
        selectedDomain?.domain_properties?.columns.forEach((sdcItem: any) => {
          initialFileData.forEach((ifd: any) => {
            if (ifd.column_name === sdcItem.name) {
              ifd.domain_column = sdcItem.name;
            }
          });
        });
        initialFileData.unshift({ ...defaultResourceCDMColumns });
        setResourceColumnFileData([...initialFileData]);
        showToast(`${file.name} file uploaded successfully`, "success");
      }
    };
    reader.readAsBinaryString(file as Blob);
    setErrors((prevError: any) => ({
      ...prevError,
      uploadFile: "",
    }));
    setIsFileUpload(false);
  };

  const onAddDerivedColumn = () => {
    setDerivedModalType("add");
    setOpenDerivedColumnDialog(true);
    setSelectedRowId(null);
    setQueryBuilderTempValue("");
    //updateCDMColumns();
  };

  const handelOnChangeDomain = (e: any, value: any) => {
    setFormData({
      ...formData,
      domain_id: value?.id,
      domain_code: value?.domain_code,
    });
    validateField("domain_id", value?.id);
    setSelectedDomain(value);
    setCurrentDomainId(value?.id);
  };

  if (backdropLoading)
    return (
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={true}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
    );

  const tabContent: any = {
    resourceColumns: (
      <Box>
        <FlexBetween gap="3rem" sx={{ marginTop: "-20px" }}>
          <DataTable
            dataColumns={columns}
            dataRows={resourceColumnFileData}
            checkboxSelection={false}
            dataListTitle="Resource Info"
            buttonText="Derived Column"
            buttonClass="btn-orange btn-dark btn-sm"
            buttonClick={onAddDerivedColumn}
            buttonIcon={<AddCircleOutlineIcon />}
            className="dataTable no-radius table-focused"
            handleEditValidationDialog={handleEditValidationDialog}
            handleDeleteValidation={handleDeleteValidation}
            handleEditReferenceDialog={handleEditReferenceDialog}
            handleDeleteReference={handleDeleteReference}
            handleAddMoreRow={handleAddMoreRow}
            handleDeleteAllRows={handleDeleteAllRows}
            paginationModel={{
              page: page - 1,
              pageSize: pSize,
            }}
            onPaginationModelChange={(params: any) => {
              if (params.pageSize !== pSize || params.page !== page - 1) {
                setPage(params.page + 1);
                setPSize(params.pageSize);
              }
            }}
            isPaginationChangeRequiredOnClientSide={true}
          />
        </FlexBetween>

        <FlexBetween
          gap="3rem"
          sx={{
            margin: "6px auto 0 auto",
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          <Button
            type="submit"
            color="secondary"
            variant="contained"
            className="btn-orange"
          >
            <SaveOutlinedIcon /> &nbsp; Save
          </Button>
        </FlexBetween>
      </Box>
    ),
    crossFieldValidations: (
      <CrossFieldValidations
        crossFieldValidations={crossFieldValidations}
        setCrossFieldValidations={setCrossFieldValidations}
        isViewOnly={false}
        errors={errors}
        setErrors={setErrors}
      />
    ),
  };

  return (
    <Box>
      <form onSubmit={onSaveResourceColumn} autoComplete="off">
        <Box className="accordion-panel">
          <Accordion className="heading-bold" expanded={true}>
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              <h4 className="acc-h4">Resource Column Details</h4>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <Grid container item>
                    <RadioGroup
                      sx={{
                        alignItems: "center",
                        margin: "auto 0px",
                        marginBottom: "3px",
                        display: "flex",
                      }}
                      row
                      aria-labelledby="demo-controlled-radio-buttons-group"
                      name="controlled-radio-buttons-group"
                      value={currentType}
                      onChange={(e) => {
                        setCurrentType(e.target.value);
                        setFormData({
                          id: 1,
                          name: "",
                          resource_column_name: "",
                          resource_type: "",
                          resource_prefix: "",
                          domain_id: null,
                          resource_location: "local",
                          resource_path: "",
                        });
                        setCurrentDomainId(null);
                        setDomainsTableData([]);
                        setAvailColumns([]);
                        setAvailColumnsWithResourceDetail(null);
                        setResourceColumnFileData([defaultResourceCDMColumns]);
                        setIsFileUpload(true);
                        setErrors({});
                      }}
                      className="radio-group-gap"
                    >
                      <FormControlLabel
                        value="new"
                        control={<Radio />}
                        label={
                          <span>
                            New Domain
                            <span className="required-asterisk">*</span>
                          </span>
                        }
                        sx={{
                          "& .MuiSvgIcon-root": {
                            color: "var(--orange)",
                          },
                        }}
                      />
                      <FormControlLabel
                        value="existing"
                        control={<Radio />}
                        label={
                          <span>
                            Existing Domain
                            <span className="required-asterisk">*</span>
                          </span>
                        }
                        sx={{
                          "& .MuiSvgIcon-root": {
                            color: "var(--orange)",
                          },
                        }}
                      />
                    </RadioGroup>
                  </Grid>
                  {currentType === "existing" ? (
                    <AutoCompleteDomainList
                      setIsLoading={setIsLoading}
                      handelOnChangeDomain={handelOnChangeDomain}
                      currentDomainId={currentDomainId}
                      className={`form-control-autocomplete autocomplete-no-label ${
                        errors?.domain_id ? "has-error" : ""
                      }`}
                      required={true}
                      error={errors?.domain_id}
                      helperText={errors?.domain_id}
                    />
                  ) : (
                    <>
                      <TextField
                        type="text"
                        // required
                        name="domain_name"
                        // placeholder="Ex: Sample Domain Name"
                        onChange={handleFormData}
                        className={`form-control ${
                          errors?.domain_name ? "has-error" : ""
                        }`}
                        error={!!errors?.domain_name}
                        helperText={errors?.domain_name}
                      />
                    </>
                  )}
                </Grid>
                {currentType !== "existing" && (
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                    <label className="label-text">
                      Domain Code
                      <span className="required-asterisk">*</span>
                    </label>
                    <TextField
                      type="text"
                      // required
                      name="domain_code"
                      // placeholder="Ex: Sample Resource Column Details Name"
                      onChange={handleFormData}
                      className={`form-control ${
                        errors?.name ? "has-error" : ""
                      }`}
                      value={formData.domain_code || ""}
                      error={!!errors?.domain_code}
                      helperText={errors?.domain_code}
                    />
                  </Grid>
                )}
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Resource Column Details Name
                    <span className="required-asterisk">*</span>
                  </label>
                  <TextField
                    type="text"
                    // required
                    name="resource_column_name"
                    // placeholder="Ex: Sample Resource Column Details Name"
                    onChange={handleFormData}
                    className={`form-control ${
                      errors?.name ? "has-error" : ""
                    }`}
                    value={formData.resource_column_name || ""}
                    error={!!errors?.name}
                    helperText={errors?.name}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    Code
                    <span className="required-asterisk">*</span>
                  </label>
                  <TextField
                    type="text"
                    // required
                    name="code"
                    // placeholder="Ex: Sample Resource Column Details Name"
                    onChange={(e: any) => {
                      handleFormData(e);
                      validateField(e.target.name, e.target.value);
                    }}
                    className={`form-control ${
                      errors?.code ? "has-error" : ""
                    }`}
                    value={formData.code || ""}
                    error={!!errors?.code}
                    helperText={errors?.code}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                  <label className="label-text">
                    <span className="position-relative inline-block">
                      Upload file
                      <Tooltip
                        title="Download Sample File for dummy resource columns"
                        placement="top"
                        arrow
                      >
                        <span className="upload-icon-info">
                          <InfoIcon onClick={handleDownloadSampleFile} />
                        </span>
                      </Tooltip>
                    </span>
                  </label>
                  <FileUploadButton
                    className={`fileUploadButton`}
                    onFileUpload={handleFileUpload}
                    isFileUpload={isFileUpload}
                    fileData={resourceColumnFileData}
                  />
                </Grid>
                <Grid item xs>
                  <label className="label-text line-height16">&nbsp;</label>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      columnGap: "20px",
                      justifyContent: "flex-end",
                    }}
                  >
                    <Button
                      type="submit"
                      color="secondary"
                      variant="contained"
                      className="btn-orange"
                    >
                      <SaveOutlinedIcon /> &nbsp; Save
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
          <Tabs
            sx={{
              mt: 1,
            }}
            value={activeTab}
            onChange={handleChangeTab}
            className="mui-tabs alternative-1"
            variant="scrollable"
          >
            <Tab label="Resource Columns" value="resourceColumns" />
            <Tab label="Adhoc Query" value="crossFieldValidations" />
          </Tabs>
          <Box>{tabContent[activeTab]}</Box>
        </Box>

        <ValidationDialog setAllVariablesList={setAllVariablesList} />
        <DerivedColumnDialog setAllVariablesList={setAllVariablesList} />
        <ReferenceDialog setAllVariablesList={setAllVariablesList} />
      </form>
      <ConfirmDailog
        isDailogOpen={isDailogOpen}
        handleCancelAction={handleCancelDeleteAction}
        handleConfirmAction={handleConfirmDeleteAction}
        dailogTitle={"Confirm Delete"}
        dailogDescription={
          "This action will delete all rows, still want to continue?"
        }
      />
    </Box>
  );
};

export default AddResourceColumnDetails;
