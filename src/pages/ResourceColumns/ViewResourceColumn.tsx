import React, { useEffect, useState } from "react";
import { GridColDef } from "@mui/x-data-grid";
import useFetchDomains from "../../hooks/useFetchDomains";
import DataTable from "../../components/DataGrids/DataGrid";
import { useNavigate, useParams } from "react-router-dom";
import { useToast } from "../../services/utils";
import {
  Box,
  Grid,
  TextField,
  Checkbox,
  IconButton,
  Tabs,
  Tab,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CustomValidationSummary from "../../pages/Resource/CustomValidationSummary";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import FlexBetween from "../../components/FlexBetween";
import CloneResourceColumnDialog from "../../components/Dialogs/CloneResourceColumnDialog";
import { cloneResourceColumn } from "../../services/resourcesService";
import { debounce } from "lodash";
import { useResourceContext } from "../../contexts/ResourceContext";
import CrossFieldValidations from "./CrossFieldValidations";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
import { IconBtnEditBase } from "../../common/utils/icons";

const ACCORDION_HEADER: any = {
  resourceColumnDetails: "Resource Column Details",
};

export default function ViewResourceColumn() {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { domainId } = useParams();
  const { showToast } = useToast();
  const [currentResourceColumnId, setCurrentResourceColumnId] =
    React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [resourceColumns, setResourceColumns] = React.useState<any>([]);
  const { id }: any = useParams();
  const navigate = useNavigate();
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [activeTab, setActiveTab] = useState<string>("resourceColumns");
  const [cloneResourceColumnDialog, setCloneResourceColumnDialog] =
    useState<boolean>(false);
  const [cloneResourceColumnData, setCloneResourceColumnData] = useState<any>({
    name: "",
  });
  const [expandedAccordion, setExpandedAccordion] = useState<any>(
    "resourceColumnDetails"
  );
  const [crossFieldValidations, setCrossFieldValidations] = useState<any>([]);

  const { domainsData, setDomainsData, errors, setErrors } =
    useResourceContext();
  useEffect(() => {
    setCurrentResourceColumnId(parseInt(id));
  }, [id]);

  const [resourceColumnData] = useFetchResourceColumnsById({
    resourceColumnId: currentResourceColumnId,
    setIsLoading,
  });
  const [domains] = useFetchDomains({ setIsLoading });

  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
  };

  useEffect(() => {
    if (domains) {
      setDomainsData(domains);
    }
  }, [domains]);

  // const currentDomain = domainsData?.find(
  //   (domain: any) => domain.id === resourceData?.domain_id
  // );

  // Define table column names
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      renderCell: (params: any) => (
        <IconButton
          size="small"
          tabIndex={-1}
          disabled={
            params?.row?.is_derived ||
            params?.row?.custom_validations?.length > 0 ||
            params?.row?.is_reference ||
            params?.row?.reference_column_definition != null
              ? false
              : true
          }
          // aria-label={isExpanded ? "Close" : "Open"}
          onClick={() => {
            // toggle in array
            if (expandedRow.includes(params.row.id)) {
              setExpandedRow((prev: any) =>
                prev.filter((item: any) => item !== params.row.id)
              );
            } else {
              setExpandedRow((prev: any) => [...prev, params.row.id]);
            }
          }}
        >
          {params?.row?.is_derived ||
          params?.row?.custom_validations?.length > 0 ||
          params?.row?.is_reference ||
          params?.row?.reference_column_definition != null ? (
            <ExpandMoreIcon
              sx={{
                transform: `rotateZ(${
                  expandedRow.includes(params.row.id) ? 180 : 0
                }deg)`,
                transition: (theme) =>
                  theme.transitions.create("transform", {
                    duration: theme.transitions.duration.shortest,
                  }),
              }}
              fontSize="inherit"
            />
          ) : null}
        </IconButton>
      ),
    },
    {
      field: "column_name",
      headerName: "Resource Column",
      width: 120,
      minWidth: 150,
      flex: 1,
      renderCell: (params: any) => (
        <LimitChractersWithTooltip value={params?.value} />
      ),
    },
    {
      field: "domain_column",
      headerName: "Domain Column",
      width: 120,
      minWidth: 130,
      flex: 1,
      renderCell: (params: any) => (
        <LimitChractersWithTooltip value={params?.value} />
      ),
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 100,
    },
    {
      field: "is_mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={
              params.value &&
              (params.value === "Y" ||
                params.value === "y" ||
                params.value === "Yes" ||
                params.value === "yes" ||
                params.value === true)
            }
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={
              resourceColumnData &&
              resourceColumnData?.resource_column_properties?.unique_columns.includes(
                params.row.column_name
              )
            }
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "is_reference",
      headerName: "Reference",
      description: "Reference Data",
      maxWidth: 90,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={params.value && params.value === true}
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "is_active",
      headerName: "Act./Inac.",
      maxWidth: 70,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={params && params.value !== undefined ? params.value : true}
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "column_length",
      headerName: "Length",
      width: 100,
    },
    {
      field: "severity_level",
      headerName: "Severity",
      width: 100,
    },
    {
      field: "data_format",
      headerName: "Format",
      width: 100,
      flex: 1,
    },
  ];

  const handelFormatColumns = (resourceColumnData: any) => {
    const formattedColumns = resourceColumnData?.resource_columns.map(
      (RColumn: any, idx: any) => ({
        id: idx,
        column_name: RColumn.column_name,
        domain_column: RColumn.domain_column,
        datatype: RColumn?.constraints.datatype,
        is_mandatory: RColumn?.constraints.is_mandatory,
        data_format: RColumn?.constraints.data_format,
        column_length: RColumn?.constraints.column_length,
        is_derived: RColumn?.constraints.is_derived,
        derived_column_definition:
          RColumn?.constraints.derived_column_definition,
        is_reference: RColumn?.constraints.is_reference,
        reference_column_definition:
          RColumn?.constraints.reference_column_definition,
        custom_validations: RColumn?.constraints.custom_validations,
        severity_level:
          RColumn?.severity_level === 1
            ? "High"
            : RColumn?.severity_level === 2
            ? "Medium"
            : "Low",
        is_active:
          RColumn && RColumn.is_active !== undefined ? RColumn.is_active : true,
      })
    );

    setResourceColumns(formattedColumns);
    return formattedColumns;
  };
  useEffect(() => {
    if (
      resourceColumnData?.resource_column_properties?.resource_columns?.length >
      0
    ) {
      handelFormatColumns(resourceColumnData?.resource_column_properties);
    }
    setCrossFieldValidations(
      resourceColumnData?.resource_column_properties?.cross_field_validations ||
        []
    );
    setCurrentBreadcrumbPage({
      name: resourceColumnData?.name,
      id: resourceColumnData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [resourceColumnData]);

  const tabContent: any = {
    resourceColumns: (
      <Box sx={{ marginTop: "-20px" }}>
        <DataTable
          dataColumns={columns}
          dataRows={resourceColumns}
          checkboxSelection={false}
          dataListTitle="Resource Column Info"
          className="dataTable no-radius"
          loading={isLoading}
        />
      </Box>
    ),
    customValidationSummary: (
      <Box sx={{ marginTop: "-20px" }}>
        <CustomValidationSummary
          customData={resourceColumnData}
          resourceColumns={resourceColumns}
        />
      </Box>
    ),
    crossFieldValidations: (
      <>
        <CrossFieldValidations
          crossFieldValidations={crossFieldValidations}
          setCrossFieldValidations={setCrossFieldValidations}
          isViewOnly={true}
          errors={errors}
          setErrors={setErrors}
        />
      </>
    ),
  };

  const handleCloseCloneResourceColumn = () => {
    setCloneResourceColumnDialog(false);
    setCloneResourceColumnData({});
  };
  const onSaveCloneResourceColumn = () => {
    const reqBody = {
      resource_column_details_id: id,
      name: cloneResourceColumnData.name,
      code: cloneResourceColumnData.code,
    };
    cloneResourceColumn(reqBody)
      .then((response) => {
        showToast("Resource Column clone successfully!", "success");
        navigate(`/resource-columns/all/view/${response?.id}`);
      })
      .catch((error) => {
        showToast(`Cannot create clone resource column`, "error");
      });
    handleCloseCloneResourceColumn();
  };
  const accordionContent: any = {
    resourceColumnDetails: (
      <>
        <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
          <label className="label-text text-bold">Domain Name</label>
          <div className="form-control  word-break-all">
            {domainsData?.find(
              (option: { id: any }) =>
                option.id === resourceColumnData?.domain_id
            )?.domain_name || "N/A"}
          </div>
        </Grid>
        <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
          <label className="label-text text-bold">Resource Column Name</label>
          <div className="form-control  word-break-all">
            {resourceColumnData?.name || "N/A"}
          </div>
        </Grid>
        <Grid item xs={12} sm={6} md={3} lg={3} xl={3}>
          <label className="label-text text-bold">Code</label>
          <div className="form-control  word-break-all">
            {resourceColumnData?.code || "N/A"}
          </div>
        </Grid>
      </>
    ),
  };
  const debouncedHandleChangeAccordion = debounce((panel: any) => {
    setExpandedAccordion(panel);
  }, 300);
  const handleChangeAccordion =
    (panel: string) => (event: any, isExpanded: any) => {
      debouncedHandleChangeAccordion(isExpanded ? panel : null);
    };
  return (
    <>
      <Box className="dashboard-title-group mb-6 flex-end">
        <div className="right-column">
          <Button
            variant="contained"
            color="secondary"
            onClick={() => setCloneResourceColumnDialog(true)}
            className="btn-orange btn-nostyle"
          >
            clone Resource column
          </Button>
          <button
            className="btn-nostyle icon-btn-edit"
            onClick={() =>
              navigate(
                `/resource-columns/${domainId}/edit/${currentResourceColumnId}`
              )
            }
          >
            <IconBtnEditBase />
          </button>
        </div>
      </Box>
      <Box sx={{ marginTop: 0 }} className="accordion-panel">
        {ACCORDION_HEADER &&
          Object.keys(ACCORDION_HEADER).map((item: any, idx: any) => {
            return (
              <Accordion
                className="heading-bold box-shadow mb-16"
                expanded={expandedAccordion === item}
                onChange={handleChangeAccordion(item)}
                data-myattr={`${item}`}
                key={item}
              >
                <AccordionSummary
                  aria-controls="panel1d-content"
                  id="panel1d-header"
                  expandIcon={<ExpandMoreIcon />}
                >
                  {ACCORDION_HEADER[item]}
                </AccordionSummary>
                <AccordionDetails sx={{ paddingTop: "16px" }}>
                  <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                    {accordionContent[item]}
                  </Grid>
                </AccordionDetails>
              </Accordion>
            );
          })}
      </Box>
      <Box>
        <Tabs
          sx={{
            mt: 1,
          }}
          value={activeTab}
          onChange={handleChangeTab}
          className="mui-tabs alternative-1"
          variant="scrollable"
        >
          <Tab label="Resource Columns" value="resourceColumns" />
          <Tab
            label="Custom Validaton Summary"
            value="customValidationSummary"
          />
          <Tab label="Adhoc Query" value="crossFieldValidations" />
        </Tabs>
        <Box>{tabContent[activeTab]}</Box>
      </Box>
      <CloneResourceColumnDialog
        cloneResourceColumnDialog={cloneResourceColumnDialog}
        handleCloseCloneResourceColumn={handleCloseCloneResourceColumn}
        cloneResourceColumnData={cloneResourceColumnData}
        setCloneResourceColumnData={setCloneResourceColumnData}
        onSaveCloneResourceColumn={onSaveCloneResourceColumn}
      />
    </>
  );
}
