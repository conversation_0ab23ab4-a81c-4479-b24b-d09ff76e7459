import React from "react";
import { Box } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import AddMultipleGridList from "../../components/AddMultipleGridList";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
const CrossFieldValidations = ({
  crossFieldValidations,
  setCrossFieldValidations,
  isViewOnly,
  errors,
  setErrors,
}: any) => {
  const [expanded, setExpanded] = React.useState<string | false>();
  const { availColumns } = useRuleResourceContext();

  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
      setExpanded(newExpanded ? panel : false);
    };
  return (
    <>
      <div
        className={`accordion-panel text-box-card compact-text-box-card no-radius bottom-radius bg-white ${
          isViewOnly ? "" : "table-filter-bg"
        }`}
        style={{ marginBottom: "0" }}
      >
        {isViewOnly ? (
          <Box>
            {crossFieldValidations?.length > 0 ? (
              <Box className="MuiCollapse-vertical ">
                <table className="custom-table">
                  <thead>
                    <tr>
                      <th style={{ width: "30%" }}>Name:</th>
                      <th>SQL Query(s):</th>
                    </tr>
                  </thead>
                  <tbody>
                    {crossFieldValidations?.map((item: any, index: any) => {
                      return (
                        <tr key={index}>
                          <td>{item?.name}</td>
                          <td>{item?.sql_query}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </Box>
            ) : (
              <p>No data found</p>
            )}
          </Box>
        ) : (
          <>
            <AddMultipleGridList
              gridValue={crossFieldValidations}
              setGridValue={setCrossFieldValidations}
              isViewOnly={isViewOnly}
              buttonName="Adhoc Query"
              errors={errors}
              setErrors={setErrors}
              sampleQuery={`SELECT * FROM <TABLE_NAME> WHERE [${
                availColumns && availColumns.length > 0 ? availColumns[0] : ""
              }] IS NOT NULL`}
            />
          </>
        )}
      </div>
    </>
  );
};

export default CrossFieldValidations;
