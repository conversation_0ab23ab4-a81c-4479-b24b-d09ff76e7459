import React, { useEffect, useState } from "react";
import { Box, Tooltip, Typography, Checkbox, IconButton } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import {
  formattedJson,
  getFormattedDateTime,
  removeCheckboxFromJson,
} from "../../services/utils";
import { useParams } from "react-router-dom";
import useFetchResourceColumnBackups from "../../hooks/useFetchResourceColumnBackups";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import InfoIcon from "@mui/icons-material/Info";
import AuditComponent from "../../components/AuditComponent";
import { useResourceContext } from "../../contexts/ResourceContext";
import { IconCompareReportSvg, IconReportsSvg } from "../../common/utils/icons";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import useFetchDomains from "../../hooks/useFetchDomains";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const AuditResourceColumn = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { id }: any = useParams();

  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [auditSource, setAuditSource] = useState("");

  const [resourceColumnBackupData] = useFetchResourceColumnBackups({
    resourceColumnId: id,
    setIsLoading,
    page,
    pSize,
  });
  const { domainsData, setDomainsData } = useResourceContext();

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      flex: 0,
      minWidth: 0,
      renderCell: (params: any) => null,
    },
    {
      field: "checkbox",
      headerName: "",
      width: 80,
      renderCell: (params) => (
        <Box sx={{ paddingTop: "4px" }}>
          <Checkbox
            checked={selectedRows.some((row) => row?.id === params?.row?.id)}
            onChange={() => toggleSelection(params.row)}
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
          />
        </Box>
      ),
    },
    {
      field: "domain_id",
      headerName: "Domain Name",
      flex: 1,
      renderCell: (params: any) => {
        const dataTypeValue = domainsData.find(
          (item: { id: any }) => item.id === params.row.domain_id
        )?.domain_name;
        return <LimitChractersWithTooltip value={dataTypeValue} />;
      },
    },
    {
      field: "code",
      headerName: "Code",
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "created_date",
      headerName: "Last Modified",
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        return getFormattedDateTime(params?.value);
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "info",
      headerName: "Resource Column Info",
      minWidth: 180,
      flex: 1,
      renderCell: (params) => {
        const { checkbox, ...rowWithoutCheckbox } = params.row;

        return (
          <span className="position-relative">
            <Tooltip
              componentsProps={{
                tooltip: { className: "wide-tooltip w-380" },
              }}
              title={
                <pre
                  style={{
                    whiteSpace: "pre-wrap",
                    margin: 0,
                    maxHeight: "200px",
                    overflowY: "auto",
                  }}
                >
                  <React.Fragment>
                    <Typography color="inherit">
                      Resource Column Info
                    </Typography>

                    <Typography>
                      {formattedJson(JSON.stringify(rowWithoutCheckbox))}
                    </Typography>
                  </React.Fragment>
                </pre>
              }
            >
              <InfoIcon
                sx={{
                  position: "absolute",
                  top: "50%",
                  transform: "translateY(-50%)",
                  right: "-24px",
                  width: "16px",
                  cursor: "pointer",
                }}
              />
            </Tooltip>
          </span>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 130,
      renderCell: (params: any) => {
        return (
          <>
            <Tooltip title="Compare Current Defination" placement="top">
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  compareCurrentItem(params?.row, "currentCompare")
                }
              >
                <IconCompareReportSvg />
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  const [resourceColumnData] = useFetchResourceColumnsById({
    resourceColumnId: id,
    setIsLoading,
  });
  const [domains] = useFetchDomains({ setIsLoading });

  useEffect(() => {
    if (domains) {
      setDomainsData(domains);
    }
  }, [domains]);

  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: resourceColumnData?.name,
      id: resourceColumnData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [resourceColumnData]);

  const compareCurrentItem = async (data: any, source: any) => {
    const newData = await removeCheckboxFromJson(data);
    if (selectedRows.length > 0) {
      setSelectedRows([]);
    }
    setSelectedRows((prevSelectedRows) => [
      ...prevSelectedRows,
      newData,
      resourceColumnData,
    ]);
    setAuditSource(source);
    setOpenDialog(true);
  };

  const toggleSelection = (row: { id: any }) => {
    setSelectedRows((prevSelectedRows) => {
      if (prevSelectedRows.some((selectedRow) => selectedRow.id === row.id)) {
        // Deselect row
        return prevSelectedRows.filter(
          (selectedRow) => selectedRow.id !== row.id
        );
      } else {
        // Select row
        if (prevSelectedRows.length < 2) {
          return [...prevSelectedRows, removeCheckboxFromJson(row)];
        } else {
          // Replace the second selected row with the new row
          return [prevSelectedRows[0], removeCheckboxFromJson(row)];
        }
      }
    });
  };

  return (
    <AuditComponent
      auditColumnData={columns}
      auditRowData={resourceColumnBackupData || []}
      isLoading={isLoading}
      selectedRows={selectedRows}
      setSelectedRows={setSelectedRows}
      toggleSelection={toggleSelection}
      dataListTitle="Resource Column Modification History"
      page={page}
      setPage={setPage}
      pSize={pSize}
      setPSize={setPSize}
      openDialog={openDialog}
      setOpenDialog={setOpenDialog}
      auditSource={auditSource}
      setAuditSource={setAuditSource}
      singlePageMaxHeightDiff={333}
    />
  );
};

export default AuditResourceColumn;
