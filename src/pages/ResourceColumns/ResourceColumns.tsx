import React, { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { GridColDef } from "@mui/x-data-grid";
import {
  Box,
  Button,
  Grid,
  IconButton,
  InputBase,
  Tooltip,
} from "@mui/material";
import DataTable from "../../components/DataGrids/DataGrid";
import { useToast } from "../../services/utils";
import FlexBetween from "../../components/FlexBetween";
import ButtonComponent from "../../components/Button.Component";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { Search } from "@mui/icons-material";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import { paginatedResponseFormat } from "../../services/constants";
// import useFetchDomains from "../../hooks/useFetchDomains";
import { toast } from "react-toastify";
import { deleteResourceColumn } from "../../services/resourcesService";
import AutoCompleteDomainList from "../../components/Molecules/Domain/AutoCompleteDomainList";
import useFetchPaginatedResourceColumnsByDomain from "../../hooks/useFetchPaginatedResourceColumnsByDomain";
import { useResourceContext } from "../../contexts/ResourceContext";
import {
  downloadResourceColumnExportFile,
  getFormattedDateTime,
} from "../../services/utils";
import {
  IconAudit,
  IconDeleteBlueSvg,
  IconImportFileWhite,
  IconExportIconBlue,
  IconEyeBase,
  IconEditBase,
} from "../../common/utils/icons";
import DropdownMenu from "../../components/Molecules/DropdownMenu/DropdownMenu";
import Loader from "../../components/Molecules/Loader/Loader";
import AssociateEntitiesDialog from "../../components/Dialogs/AssociateEntitiesDialog";
import useFetchDomains from "../../hooks/useFetchDomains";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
const ResourceColumns: React.FC = () => {
  const { domainsData, setDomainsData } = useResourceContext();
  const { showToast } = useToast();

  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  //for navigation & parameters
  const navigate = useNavigate();
  const { domainId, id } = useParams();

  const [fileData, setFileData] = useState<any>(paginatedResponseFormat);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isDomainsLoading, setDomainsIsLoading] = useState<boolean>(false);
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [openAssociateEntitiesDialog, setOpenAssociateEntitiesDialog] =
    useState<boolean>(false);
  const [associateEntityId, setAssociateEntityId] = useState<number | null>(
    null
  );

  const [currentDomainId, setCurrentDomainId] = useState<
    string | number | null
  >(Number(domainId) || "");
  const [resourcesColumnData, setResourcesColumnData] = useState<any>(
    paginatedResponseFormat
  );

  const [domains] = useFetchDomains({ setIsLoading: setDomainsIsLoading });
  const [fetchedResourcesColumnData] = useFetchPaginatedResourceColumnsByDomain(
    {
      currentDomainId,
      setIsLoading,
      page,
      pSize,
    }
  );
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (domains) {
      setDomainsData(domains);
    }
  }, [domains]);

  // Handel events

  const handelClickEvent = (url: any) => {
    navigate(url);
  };
  const handleAssociateEntitiesDialog = () => {
    setOpenAssociateEntitiesDialog(true);
  };

  // Table Columns
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      renderCell: (params: any) => <></>,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
    },
    {
      field: "id",
      headerName: "Id",
      minWidth: 60,
      renderCell: (params: any) => {
        return <span>{params.id}</span>;
      },
    },
    {
      field: "name",
      headerName: "Resource Column Name",
      width: 120,
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "domain_id",
      headerName: "Domain Name",
      flex: 1,
      renderCell: (params: any) => {
        const dataTypeValue = domainsData.find(
          (item: { id: any }) => item.id === params.row.domain_id
        )?.domain_name;

        return <LimitChractersWithTooltip value={dataTypeValue} />;
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "updated_on",
      headerName: "Last Modified",
      minWidth: 180,
      renderCell: (params: any) => {
        return getFormattedDateTime(params.value);
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 140,
      filterable: false,
      renderCell: (params) => {
        const handleDelete = async () => {
          setFileData((prev: any) => ({
            ...prev,
            items: prev?.items?.filter(
              (prevItem: any) => prevItem.id !== params.row.id
            ),
          }));
          await deleteResourceColumn(params.row.id);
          showToast(`${params.row.name} deleted successfully`, "success");
        };
        return (
          <>
            <Tooltip title="View Resource Column" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  navigate(
                    `/resource-columns/${domainId}/view/${params.row.id}`
                  )
                }
              >
                <IconEyeBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit Resource Column" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  navigate(
                    `/resource-columns/${domainId}/edit/${params.row.id}`
                  )
                }
              >
                <IconEditBase />
              </IconButton>
            </Tooltip>
            <DropdownMenu
              dropdownMenuItems={[
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    navigate(
                      `/resource-columns/${domainId}/auditResourceColumn/${params.row.id}`
                    )
                  }
                >
                  <IconAudit /> Audit Resource Column
                </IconButton>,
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => {
                    setAssociateEntityId(params.row.id);
                    handleAssociateEntitiesDialog();
                  }}
                >
                  <IconExportIconBlue />
                  Export
                </IconButton>,
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleDelete()}
                >
                  <IconDeleteBlueSvg />
                  Delete Resource Column
                </IconButton>,
              ]}
            />
          </>
        );
      },
    },
  ];

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    setFileData((prev: any) => ({
      ...prev,
      items: resourcesColumnData?.items?.filter((resource: any) => {
        return resource.name.toLowerCase().includes(searchQuery.toLowerCase());
      }),
    }));
  };

  const filteredData = useMemo(() => {
    return fileData?.items?.filter((report: any, index: number) => {
      // report.id = index;
      return report.name.toLowerCase().includes(searchQuery?.toLowerCase());
    });
  }, [fileData?.items, searchQuery]);
  // for update resource list data
  useEffect(() => {
    if (fetchedResourcesColumnData) {
      setFileData(fetchedResourcesColumnData);
      setResourcesColumnData(fetchedResourcesColumnData);
    }
  }, [fetchedResourcesColumnData]);

  const handelOnChangeDomain = (event: any, value: any) => {
    if (value?.id !== undefined) {
      setCurrentDomainId(value?.id);

      navigate(`/resource-columns/${value?.id}`);
      setPage(defaultPage ? parseInt(defaultPage) : 1);
      setPSize(defaultPageSize ? parseInt(defaultPageSize) : 25);
    } else {
      navigate(`/resource-columns/all`);
      setCurrentDomainId(null);
      setFileData(paginatedResponseFormat);
    }
  };

  return (
    <Box>
      {(isDownloadLoading || isDomainsLoading) && (
        <Loader isLoading={isDownloadLoading || isDomainsLoading} />
      )}
      <Box className="text-box-card compact-text-box-card list-page-card">
        <Grid>
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs={12} sm={12} md={6} lg={3} xl={3}>
              <AutoCompleteDomainList
                currentDomainId={currentDomainId}
                setIsLoading={setIsLoading}
                handelOnChangeDomain={handelOnChangeDomain}
                className="form-control-autocomplete"
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6} lg={4} xl={4}>
              <label className="label-text line-height16">
                Search Resource Column
              </label>
              <form
                className="common-search-panel"
                // onSubmit={handleSearchSubmit}
              >
                <InputBase
                  placeholder="Search..."
                  className="search-textbox"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  onKeyDown={(event) => {
                    if (event.key === "Enter") {
                      event.preventDefault();
                    }
                  }}
                />
                <IconButton className="search-icon">
                  <Search className="svg_icons" />
                </IconButton>
              </form>
            </Grid>
            <Grid item xs>
              <Box
                sx={{ display: { md: "block", sm: "none" } }}
                className="label-text"
              >
                &nbsp;
              </Box>
              <Box
                display="flex"
                justifyContent="flex-end"
                sx={{
                  columnGap: 2,
                  rowGap: {
                    xl: "0",
                    lg: "0",
                    md: "20",
                    sm: "20px",
                    xs: "20px",
                  },
                  flexDirection: {
                    xl: "row",
                    lg: "row",
                    md: "row",
                    sm: "column",
                    xs: "column",
                  },
                }}
              >
                {/* <Button
                  onClick={() =>
                    navigate(`/resource-column/import-entity?type=resource-column`, {
                      state: {
                        import_defination_name: "resource column details",
                      },
                    })
                  }
                  className="btn-orange btn-dark"
                  sx={{ columnGap: 1 }}
                >
                  <IconImportFileWhite />
                  Import
                </Button> */}
                <ButtonComponent
                  className="btn-orange"
                  handelClickEvent={() =>
                    handelClickEvent("/resource-columns/add-columns")
                  }
                >
                  <AddSharpIcon sx={{ marginRight: "4px" }} /> Resource Column
                  Details
                </ButtonComponent>
              </Box>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      {/* import data table for resource list  */}

      <FlexBetween>
        <DataTable
          // dataRows={fileData?.items || []}
          dataRows={filteredData || []}
          dataColumns={columns}
          loading={isLoading}
          dataListTitle="Resource Columns List"
          className="dataTable no-radius"
          paginationMode="server"
          rowCount={fileData?.total || 0}
          pageSizeOptions={[25]}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={370}
        />
      </FlexBetween>
      <AssociateEntitiesDialog
        openAssociateEntitiesDialog={openAssociateEntitiesDialog}
        setOpenAssociateEntitiesDialog={setOpenAssociateEntitiesDialog}
        associateEntityId={associateEntityId}
        downloadExportFile={downloadResourceColumnExportFile}
        setIsDownloadLoading={setIsDownloadLoading}
        header={"Resource Column"}
      />
    </Box>
  );
};

export default ResourceColumns;
