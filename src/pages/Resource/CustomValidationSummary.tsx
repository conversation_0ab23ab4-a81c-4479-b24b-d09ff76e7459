import React from "react";
import {
  Accordion,
  AccordionSummary,
  Typography,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

const CustomValidationSummary = ({ customData, resourceColumns }: any) => {
  // const { cross_field_validations } =
  //   customData?.resource_column_properties || {};
  const [expanded, setExpanded] = React.useState<string | false>();
  // const handleChange =
  //   (panel: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
  //     setExpanded(newExpanded ? panel : false);
  //   };
  return (
    <>
      <div
        className="accordion-panel text-box-card compact-text-box-card no-radius bottom-radius"
        style={{ marginBottom: "0", marginTop: "20px" }}
      >
        <Accordion>
          <AccordionSummary
            aria-controls="panel2d-content"
            id="panel2d-header"
            expandIcon={<ExpandMoreIcon />}
          >
            <h4>Reference Data</h4>
          </AccordionSummary>
          <AccordionDetails>
            {resourceColumns &&
            resourceColumns.length > 0 &&
            resourceColumns.some(
              (item: { reference_column_definition: any }) =>
                item.reference_column_definition
            ) ? (
              <table>
                <thead>
                  <tr>
                    <th style={{ width: "30%" }}>Column Name</th>
                    <th>Source</th>
                    <th>Type</th>
                    <th>Translation</th>
                  </tr>
                </thead>
                <tbody>
                  {resourceColumns.map((item: any) =>
                    item.reference_column_definition ? (
                      <tr className="column-validation-item" key={item.id}>
                        <td>{item.column_name}</td>
                        <td>
                          {item.reference_column_definition &&
                          item.reference_column_definition.source
                            ? item.reference_column_definition.source
                                .charAt(0)
                                .toUpperCase() +
                              item.reference_column_definition.source.slice(1)
                            : ""}
                        </td>

                        <td>{item.reference_column_definition.source_type}</td>
                        <td>
                          {item.reference_column_definition.use_translation
                            ? "True"
                            : "False"}
                        </td>
                      </tr>
                    ) : null
                  )}
                </tbody>
              </table>
            ) : (
              <p>No data found</p>
            )}
          </AccordionDetails>
        </Accordion>
        <Accordion>
          <AccordionSummary
            aria-controls="panel1d-content"
            id="panel1d-header"
            expandIcon={<ExpandMoreIcon />}
          >
            <h4>Custom Validations</h4>
          </AccordionSummary>
          <AccordionDetails>
            {resourceColumns?.some(
              (item: any) => item?.custom_validations?.length > 0
            ) ? (
              <table>
                <thead>
                  <tr>
                    <th style={{ width: "30%" }}>Column Name</th>
                    <th>Custom Validation(s)</th>
                  </tr>
                </thead>
                <tbody>
                  {resourceColumns?.map((item: any, index: number) => {
                    if (item?.custom_validations?.length > 0) {
                      return (
                        <tr key={index}>
                          <td>{item?.column_name}</td>
                          <td>
                            <ul>
                              {item?.custom_validations.map(
                                (validation: any, queryIndex: number) => (
                                  <li key={queryIndex}>
                                    <strong>Name:</strong> {validation.name}
                                    <br />
                                    <strong>Expression:</strong>{" "}
                                    {validation.expression}
                                    <br />
                                    {validation.filter_rule && (
                                      <>
                                        <strong>Filter rule:</strong>{" "}
                                        {validation.filter_rule}
                                      </>
                                    )}
                                  </li>
                                )
                              )}
                            </ul>
                          </td>
                        </tr>
                      );
                    }
                    return null;
                  })}
                </tbody>
              </table>
            ) : (
              <p>No data found</p>
            )}
          </AccordionDetails>
        </Accordion>
        {/* <Accordion>
          <AccordionSummary
            aria-controls="panel2d-content"
            id="panel2d-header"
            expandIcon={<ExpandMoreIcon />}
          >
            <h4>Cross Field Validations</h4>
          </AccordionSummary>
          <AccordionDetails>
            {cross_field_validations?.length > 0 ? (
              <table>
                <thead>
                  <tr>
                    <th style={{ width: "30%" }}>Column Name:</th>
                    <th>Custom Validation(s):</th>
                  </tr>
                </thead>

                {cross_field_validations?.map((item: any) => {
                  return (
                    <tr className="column-validation-item" key={item?.name}>
                      <td>{item?.name}</td>
                      <td>{item?.sql_query}</td>
                    </tr>
                  );
                })}
              </table>
            ) : (
              <p>No data found</p>
            )}
          </AccordionDetails>
        </Accordion> */}
      </div>
    </>
  );
};

export default CustomValidationSummary;
