import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Tooltip,
  CircularProgress,
  Backdrop,
} from "@mui/material";
import ViewLinkedServices from "../../components/Resource/ViewLinkedServices";
import useFetchConnectionKeyByLinkedServiceId from "../../hooks/useFetchConnectionKeyByLinkedServiceId";
import EditValidateResourceDataTab from "./EditValidateResourceDataTab";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { sqlDatabaseType } from "../../services/constants";
import { useNavigate, useParams } from "react-router-dom";
import { IconBtnEditBase, IconViewSvgWhite } from "../../common/utils/icons";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useToast } from "../../services/utils";
import { useResourceContext } from "../../contexts/ResourceContext";
import { IResourceDetail } from "../../types/resource";
import { getResourceDetail } from "../../services/resourcesService";
import useFetchAllResourcesWithoutDomain from "../../hooks/useFetchAllResourcesWithoutDomain";

interface ValidateResourceDataTabProps {
  resourcesData: any;
  isLoading: boolean;
  setResourcesData: Dispatch<SetStateAction<any>>;
  setOriginalResourcesData: Dispatch<SetStateAction<any>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  resource_type: string;
  resourcePath?: string;
}

const ValidateResourceDataTab = ({
  resourcesData,
  isLoading,
  setResourcesData,
  setOriginalResourcesData,
  setIsLoading,
  resource_type,
  resourcePath = "root",
}: ValidateResourceDataTabProps) => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { domainId } = useParams();
  const {
    isResourceEdit,
    setIsResourceEdit,
    fileProcessingData,
    allResourcesData,
    setAllResourcesData,
    fetchedAllConnectionKeys,
  } = useRuleResourceContext();
  const { linkedServicesData } = useResourceContext();

  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<number>(0);
  const [expandedAccordion, setExpandedAccordion] =
    useState<any>("main-resource");
  const [isResourceLoading, setIsResourceLoading] = useState<boolean>(false);

  const [connectionKeysData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId,
    setIsLoading,
  });
  const [allResources] = useFetchAllResourcesWithoutDomain({
    setIsLoading,
    aggregation_type:
      allResourcesData?.length <= 0 &&
      resourcesData?.aggregation_type === "aggregated"
        ? "aggregated"
        : "flat",
  });

  useEffect(() => {
    if (allResources?.length > 0) {
      setAllResourcesData(allResources);
    }
  }, [allResources, setAllResourcesData]);

  const updateResourceAtPath = (path: string, updatedResource: any) => {
    const update = (data: any) => {
      if (path === "root") {
        return updatedResource;
      }

      const pathParts = path.split(".");
      const newData = JSON.parse(JSON.stringify(data)); // Deep clone
      let current = newData;

      // Skip "root" keyword
      let i = pathParts[0] === "root" ? 1 : 0;

      // Traverse until the second last part
      for (; i < pathParts.length - 1; i++) {
        const part = pathParts[i];

        // If it's an array, convert to index
        if (Array.isArray(current)) {
          current = current[parseInt(part)];
        } else {
          current = current[part];
        }
      }

      const lastPart = pathParts[pathParts.length - 1];

      // Perform the merge at the final part
      if (typeof current[lastPart] === "object" && current[lastPart] !== null) {
        Object.assign(current[lastPart], updatedResource);
      } else {
        current[lastPart] = updatedResource;
      }

      return newData;
    };

    setResourcesData((prev: any) => update(prev));
    setOriginalResourcesData((prev: any) => update(prev));
  };

  useEffect(() => {
    setSelectLinkedServiceId(resourcesData?.linked_service_id);
  }, [resourcesData]);

  const getUpdatedResourceData = (resourceData: any) => {
    let updatedResourceData: any;

    if (resourceData) {
      const connectionKeyDetail = fetchedAllConnectionKeys?.find(
        (option: { id: any }) =>
          option.id ===
          resourceData?.additional_properties?.resource_definition
            ?.api_definition?.connection_key
      );
      if (
        resourceData.linked_service_id &&
        resourceData?.additional_properties?.resource_definition?.type
      ) {
        let updatedResourceDefinition =
          resourceData?.additional_properties?.resource_definition?.[
            `${resourceData?.additional_properties?.resource_definition?.type}_definition`
          ];
        if (
          sqlDatabaseType.includes(
            resourceData?.additional_properties?.resource_definition?.type
          )
        ) {
          updatedResourceDefinition =
            resourceData?.additional_properties?.resource_definition
              ?.sql_definition;
        }
        updatedResourceData = {
          ...resourceData,
          additional_properties: {
            ...resourceData.additional_properties,
            resource_definition: {
              ...resourceData.additional_properties.resource_definition,
              ...updatedResourceDefinition,
              api_definition: {
                ...resourceData?.additional_properties?.resource_definition
                  ?.api_definition,
                url: connectionKeyDetail?.api_url,
                request_timeout: 0,
              },
            },
          },
        };
      } else {
        updatedResourceData = resourceData;
      }
    }
    return updatedResourceData;
  };

  const handleChangeAccordion =
    (panel: string, resource_id?: any) =>
    async (event: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
      if (resourcesData && resourcesData?.id) {
        setExpandedAccordion(isExpanded ? panel : null);
        return;
      }

      setExpandedAccordion(isExpanded ? panel : null);
      if (resource_id && isExpanded) {
        try {
          setIsResourceLoading(true); // Set loading state
          const ResourceResult: IResourceDetail = await getResourceDetail({
            currentResourceId: resource_id,
          });

          const updatedResourceData = getUpdatedResourceData(ResourceResult);

          // Use the path-based update function
          updateResourceAtPath(resourcePath, updatedResourceData);
        } catch (error) {
          console.error("Error fetching resource details:", error);
          showToast("Failed to load resource details", "error");
        } finally {
          setIsResourceLoading(false);
        }
      }
    };

  const handleViewResource = (domainId: any, resource: any, e: any) => {
    e.preventDefault();
    if (isResourceEdit !== "") {
      return;
    }
    navigate(`/resource/${domainId}/view/${resource}`);
  };
  return (
    <Accordion
      className="heading-bold box-shadow"
      expanded={expandedAccordion === resource_type}
      onChange={
        resource_type === "aggregated"
          ? handleChangeAccordion(
              resource_type,
              resourcesData?.base_resource_id
            )
          : handleChangeAccordion(resource_type, resourcesData?.resource_id)
      }
    >
      <AccordionSummary
        aria-controls="panel1d-content"
        id="panel1d-header"
        expandIcon={<ExpandMoreIcon />}
      >
        <div className="custom-actions">
          <div className="heading">
            {resource_type === "main-resource"
              ? "Resource Data"
              : resource_type === "aggregated"
              ? `Base Resource Data-${resourcesData?.base_resource_id}`
              : `Additional Resource Data-${resourcesData?.resource_id}`}
          </div>
          <div className="action-btns">
            <Tooltip title="Navigate to view resource" placement="top">
              <button
                className="view-icon"
                onClick={(e) =>
                  handleViewResource(
                    domainId,
                    resource_type === "aggregated"
                      ? resourcesData?.base_resource_id
                      : resource_type === "main-resource"
                      ? resourcesData?.id
                      : resourcesData?.resource_id,
                    e
                  )
                }
              >
                <IconViewSvgWhite />
              </button>
            </Tooltip>
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails sx={{ paddingTop: "16px" }}>
        {isResourceEdit === resourcePath ? (
          <EditValidateResourceDataTab
            resourceData={resourcesData}
            setIsLoading={setIsLoading}
            setResourcesData={setResourcesData}
            linkedServicesData={linkedServicesData}
            connectionKeysData={connectionKeysData}
            setSelectLinkedServiceId={setSelectLinkedServiceId}
            resourcePath={resourcePath}
          />
        ) : (
          <Grid container rowSpacing={2.5} columnSpacing={4}>
            <ViewLinkedServices
              resourceData={resourcesData}
              linkedServicesData={linkedServicesData}
              connectionKeysData={connectionKeysData}
              fileProcessingData={fileProcessingData}
            />
            <Backdrop
              sx={{
                color: "#fff",
                zIndex: 9999,
                position: "absolute",
                backgroundColor: "transparent",
              }}
              open={isResourceLoading}
            >
              <CircularProgress
                sx={{ color: "#000000" }}
                size={60}
                thickness={4}
              />
            </Backdrop>
            <Grid
              item
              xs={12}
              sm={12}
              md
              sx={{
                justifyContent: "flex-end",
                display: "flex",
                padding: "20px",
              }}
            >
              <button
                className="btn-nostyle icon-btn-edit"
                onClick={() => {
                  if (isResourceEdit === "") {
                    setIsResourceEdit(resourcePath);
                  } else {
                    showToast("Please save changes first!!", "warning");
                  }
                }}
              >
                <IconBtnEditBase />
              </button>
            </Grid>
          </Grid>
        )}
        {resourcesData?.additional_properties?.additional_resource_data
          ?.length > 0 &&
          resourcesData?.additional_properties?.additional_resource_data.map(
            (additionalRes: any, index: any) => (
              <ValidateResourceDataTab
                key={`${resourcePath}-additional-${index}`}
                resourcesData={additionalRes}
                isLoading={isLoading}
                setResourcesData={setResourcesData}
                setOriginalResourcesData={setOriginalResourcesData}
                setIsLoading={setIsLoading}
                resource_type={"additional"}
                resourcePath={`${resourcePath}.additional_properties.additional_resource_data.${index}`}
              />
            )
          )}
        {resourcesData?.aggregation_type === "aggregated" && (
          <ValidateResourceDataTab
            key={`${resourcePath}-aggregated`}
            resourcesData={
              resourcesData?.additional_properties?.aggregation_properties
            }
            isLoading={isLoading}
            setResourcesData={setResourcesData}
            setOriginalResourcesData={setOriginalResourcesData}
            setIsLoading={setIsLoading}
            resource_type={"aggregated"}
            resourcePath={`${resourcePath}.additional_properties.aggregation_properties`}
          />
        )}
      </AccordionDetails>
    </Accordion>
  );
};

export default ValidateResourceDataTab;
