import React, { useEffect, useMemo, useState } from "react";
import { useToast } from "../../services/utils";
import { useNavigate, useParams } from "react-router-dom";
import { GridColDef } from "@mui/x-data-grid";
import {
  Box,
  Stack,
  Grid,
  IconButton,
  InputBase,
  Tooltip,
  Button,
  Menu,
  MenuItem,
} from "@mui/material";
import DataTable from "../../components/DataGrids/DataGrid";
import FlexBetween from "../../components/FlexBetween";
import AutoCompleteDomainList from "../../components/Molecules/Domain/AutoCompleteDomainList";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { deleteResource } from "../../services/resourcesService";
import { Search } from "@mui/icons-material";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import useFetchPaginatedResources from "../../hooks/useFetchPaginatedResources";
import { paginatedResponseFormat } from "../../services/constants";
import { enableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";
import {
  IconReportsSvg,
  IconAudit,
  IconExportIconBlue,
  IconEyeBase,
  IconEditBase,
  IconDeleteBlueSvg,
  IconValidateResourceBase,
} from "../../common/utils/icons";
import {
  downloadResourceExportFile,
  getCurrentDateAndLast7Days,
  getFormattedDateTime,
} from "../../services/utils";
import Loader from "../../components/Molecules/Loader/Loader";
import DropdownMenu from "../../components/Molecules/DropdownMenu/DropdownMenu";
import AssociateEntitiesDialog from "../../components/Dialogs/AssociateEntitiesDialog";
import CloneResourceWithResourceColumnDialog from "../../components/Dialogs/CloneResourceWithResourceColumnDialog";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
// import AuditDialog from "../../components/Dialogs/DiffAceEditorDialog";

const Resource: React.FC = () => {
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  //for navigation & parameters
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { domainId, id } = useParams();

  const [fileData, setFileData] = useState<any>(paginatedResponseFormat);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  // const [openAuditDialog, setOpenAuditDialog] = useState(false);
  const [currentDomainId, setCurrentDomainId] = useState<
    string | number | null
  >(Number(domainId) || "");
  const [resourcesData, setResourcesData] = useState<any>(
    paginatedResponseFormat
  );

  const [fetchedResourcesData] = useFetchPaginatedResources({
    currentDomainId,
    setIsLoading,
    page,
    pSize,
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [cloneResourceDialog, setCloneResourceDialog] =
    useState<boolean>(false);

  const [linkedServicesData] = useFetchLinkedServices({ setIsLoading });
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [openAssociateEntitiesDialog, setOpenAssociateEntitiesDialog] =
    useState<boolean>(false);
  const [associateEntityId, setAssociateEntityId] = useState<number | null>(
    null
  );
  const [anchorEl, setAnchorEl] = useState(null);
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  // Handel events

  const handelOnChangeDomain = (event: any, value: any) => {
    if (value?.id !== undefined) {
      setCurrentDomainId(value?.id);
      navigate(`/resource/${value?.id}`);
      setPage(defaultPage ? parseInt(defaultPage) : 1);
      setPSize(defaultPageSize ? parseInt(defaultPageSize) : 25);
    } else {
      navigate(`/resource/all`);
      setCurrentDomainId(null);
      setFileData(paginatedResponseFormat);
    }
  };
  const handelClickEvent = (url: any) => {
    navigate(url);
  };
  const getLinkedServiceCodebyId = (id: number | string | null | undefined) => {
    const linkedData = linkedServicesData?.find((item: any) => item.id === id);

    return linkedData ? linkedData.name : null;
  };

  const handleAssociateEntitiesDialog = () => {
    setOpenAssociateEntitiesDialog(true);
  };

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      renderCell: (params: any) => <></>,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
    },
    {
      field: "domain_name",
      headerName: "Domain Name",
      minWidth: 160,
      flex: 1,
      renderCell: (params: any) => {
        const handleClickDomain = (params: any) => {
          navigate(`/domain/view/${params?.row?.domain_id}`);
        };
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "resource_type",
      headerName: "System",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => (
        <LimitChractersWithTooltip value={params?.value} />
      ),
    },
    {
      field: "id",
      headerName: "Id",
      minWidth: 60,
      groupable: false,
      renderCell: (params: any) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        return <span>{params.id}</span>;
      },
    },

    {
      field: "resource_name",
      headerName: "Resource Name",
      minWidth: 160,
      flex: 1,
      groupable: false,
      renderCell: (params: any) => (
        <LimitChractersWithTooltip value={params?.value} />
      ),
    },

    {
      field: "linked_service_id",
      headerName: "Linked Service",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        const linkedServiceName = getLinkedServiceCodebyId(params.value);
        return (
          <>
            <LimitChractersWithTooltip value={linkedServiceName} />
          </>
        );
      },
    },
    {
      field: "resource_prefix",
      headerName: "Prefix",
      minWidth: 140,
      flex: 1,
      groupable: false,
      renderCell: (params: any) => (
        <LimitChractersWithTooltip value={params?.value} />
      ),
    },
    {
      field: "column_delimiter",
      headerName: "Column Delimiter",
      minWidth: 140,
      flex: 1,
      groupable: false,
      renderCell: (params: any) => {
        // console.log(params);
        return (
          <>
            <span>
              {params?.row?.additional_properties?.resource_definition?.type ===
                "local" &&
                params?.row?.additional_properties?.resource_definition
                  ?.local_definition?.column_delimiter}
            </span>
            <span>
              {params?.row?.additional_properties?.resource_definition?.type ===
                "blob" &&
                params?.row?.additional_properties?.resource_definition
                  ?.blob_definition?.column_delimiter}
            </span>
            <span>
              {params?.row?.additional_properties?.resource_definition?.type ===
                "sftp" &&
                params?.row?.additional_properties?.resource_definition
                  ?.sftp_definition?.column_delimiter}
            </span>
            <span>
              {params?.row?.additional_properties?.resource_definition?.type ===
                "sql" && "N/A"}
            </span>
          </>
        );
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "updated_on",
      headerName: "Last Modified",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return getFormattedDateTime(params.value);
      },
    },
    {
      field: "action",
      headerName: "Action",
      width: 180,
      align: "center",
      headerAlign: "center",
      groupable: false,
      renderCell: (params: any) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const handleDelete = async () => {
          setFileData((prev: any) => ({
            ...prev,
            items: prev?.items?.filter(
              (prevItem: any) => prevItem.id !== params.row.id
            ),
          }));
          await deleteResource(params.row.id);
          const updatedResourceData = fileData?.items?.filter(
            (resource: any) => resource.id !== params.row.id
          );
          // setFileData(updatedResourceData);
          setResourcesData(updatedResourceData);
          showToast(
            `${params.row.resource_name} deleted successfully`,
            "success"
          );
        };
        const { last7DaysDate } = getCurrentDateAndLast7Days();
        return (
          <>
            <Tooltip title="Validate Resource" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  navigate(
                    `/resource/${domainId}/validate-resource/${params.row.id}`
                  )
                }
              >
                <IconValidateResourceBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="View Resource" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  navigate(`/resource/${domainId}/view/${params.row.id}`)
                }
              >
                <IconEyeBase />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit Resource" placement="top" arrow>
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  navigate(`/resource/${domainId}/edit/${params.row.id}`)
                }
              >
                <IconEditBase />
              </IconButton>
            </Tooltip>
            <DropdownMenu
              dropdownMenuItems={[
                <IconButton
                  onClick={() => {
                    navigate(
                      `/validation-execution-history?resourceId=${params.row.id}&fromDate=${last7DaysDate}`
                    );
                  }}
                  size="small"
                  className="datagrid-action-btn"
                >
                  <IconReportsSvg />
                  Recent validations
                </IconButton>,

                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    navigate(
                      `/resource/${domainId}/auditResource/${params.row.id}`
                    )
                  }
                >
                  <IconAudit />
                  Audit Resource
                </IconButton>,
                // <IconButton
                //   className="datagrid-action-btn"
                //   color="inherit"
                //   onClick={() =>
                //     navigate(
                //       `/resource/${domainId}/researchQuery/${params.row.id}`,
                //       {
                //         state: {
                //           details: {
                //             name: params.row.resource_name,
                //             id: params.row.id,
                //           },
                //         },
                //       }
                //     )
                //   }
                // >
                //   <IconResearchQuerySvg />
                //   Research Query
                // </IconButton>,
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => {
                    setAssociateEntityId(params.row.id);
                    handleAssociateEntitiesDialog();
                  }}
                >
                  <IconExportIconBlue />
                  Export
                </IconButton>,
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() => handleDelete()}
                >
                  <IconDeleteBlueSvg />
                  Delete Resource
                </IconButton>,
              ]}
            />
          </>
        );
      },
    },
  ];

  // for update resource list data
  useEffect(() => {
    if (fetchedResourcesData) {
      setFileData(fetchedResourcesData);
      setResourcesData(fetchedResourcesData);
    }
  }, [fetchedResourcesData]);

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  // const handleSearchSubmit = (e: { preventDefault: () => void }) => {
  //   e.preventDefault();
  //   // Filter domains based on the searchQuery
  //   const filteredResource = resourcesData?.items?.filter((resource: any) => {
  //     return resource.resource_name
  //       .toLowerCase()
  //       .includes(searchQuery.toLowerCase());
  //   });
  //   // You can set the filtered domains as a result in your component state
  //   setFileData(filteredResource);
  // };

  const filteredData = useMemo(() => {
    return fileData?.items?.filter((report: any, index: number) => {
      // report.id = index;
      return report.resource_name
        .toLowerCase()
        .includes(searchQuery?.toLowerCase());
    });
  }, [fileData?.items, searchQuery]);

  return (
    <Box>
      {(isLoading || isDownloadLoading) && (
        <Loader isLoading={isLoading || isDownloadLoading} />
      )}
      <Box className="text-box-card compact-text-box-card">
        <Grid>
          <Grid container rowSpacing={1.5} columnSpacing={2.5}>
            <Grid item xs={12} sm={12} md={6} lg={3} xl={3}>
              <AutoCompleteDomainList
                currentDomainId={currentDomainId}
                setIsLoading={setIsLoading}
                handelOnChangeDomain={handelOnChangeDomain}
                className="form-control-autocomplete"
              />
            </Grid>
            <Grid item xs={12} sm={12} md={6} lg={3} xl={3}>
              <label className="label-text line-height16">
                Search Resource
              </label>
              <form className="common-search-panel">
                <InputBase
                  placeholder="Search..."
                  className="search-textbox"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  onKeyDown={(event) => {
                    if (event.key === "Enter") {
                      event.preventDefault();
                    }
                  }}
                />
                <IconButton className="search-icon">
                  <Search className="svg_icons" />
                </IconButton>
              </form>
            </Grid>
            <Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
              <Stack
                className="label-text"
                sx={{
                  display: { xs: "none", sm: "none", md: "none", lg: "block" },
                }}
              >
                &nbsp;
              </Stack>
              <Box
                display="flex"
                justifyContent="flex-end"
                sx={{
                  columnGap: 2,
                  rowGap: {
                    xl: "0",
                    lg: "0",
                    md: "20",
                    sm: "20px",
                    xs: "20px",
                  },
                  flexDirection: {
                    xl: "row",
                    lg: "row",
                    md: "row",
                    sm: "column",
                    xs: "column",
                  },
                }}
              >
                <Button
                  className="btn-orange"
                  onClick={handleClick}
                  variant="contained"
                  sx={{ margin: "0px" }}
                >
                  <AddSharpIcon sx={{ marginRight: "4px" }} /> Create new
                  Resource
                </Button>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleClose}
                  anchorOrigin={{
                    vertical: "top",
                    horizontal: "right",
                  }}
                  transformOrigin={{
                    vertical: "top",
                    horizontal: "right",
                  }}
                  sx={{
                    marginTop: "38px",
                  }}
                >
                  <MenuItem
                    onClick={() => {
                      navigate(`/resource/add`, {
                        state: {
                          resource_type: "new_resource",
                        },
                      });

                      //handelClickEvent("/resource/add");
                      setAnchorEl(null);
                    }}
                  >
                    New Resource
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      //handelClickEvent("/resource/add?type=sample");

                      navigate(`/resource/add`, {
                        state: {
                          resource_type: "sample",
                        },
                      });

                      setAnchorEl(null);
                    }}
                  >
                    New Resource using Sample data
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      setCloneResourceDialog(true);
                      setAnchorEl(null);
                    }}
                  >
                    Clone Resource
                  </MenuItem>
                </Menu>
                {/* <ButtonComponent
                  className="btn-orange"
                  handelClickEvent={() => handelClickEvent("/resource/add")}
                >
                  <AddSharpIcon sx={{ marginRight: "4px" }} /> Resource
                </ButtonComponent> */}
              </Box>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      {/* import data table for resource list  */}

      <FlexBetween>
        <DataTable
          // dataRows={fileData?.items || []}
          dataRows={filteredData || []}
          dataColumns={columns}
          loading={isLoading}
          dataListTitle="Resource List"
          className="dataTable no-radius hide-progress-icon"
          paginationMode="server"
          rowCount={fileData?.total || 0}
          pageSizeOptions={[25]}
          rowGroupByColumnProps={enableRowGroupByColumnProps}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={362}
        />
      </FlexBetween>

      {/* <AuditDialog
        openAuditDialog={openAuditDialog}
        setOpenAuditDialog={setOpenAuditDialog}
      /> */}
      <AssociateEntitiesDialog
        openAssociateEntitiesDialog={openAssociateEntitiesDialog}
        setOpenAssociateEntitiesDialog={setOpenAssociateEntitiesDialog}
        associateEntityId={associateEntityId}
        downloadExportFile={downloadResourceExportFile}
        setIsDownloadLoading={setIsDownloadLoading}
        header={"Resource"}
      />
      <CloneResourceWithResourceColumnDialog
        cloneResourceDialog={cloneResourceDialog}
        setCloneResourceDialog={setCloneResourceDialog}
      />
    </Box>
  );
};

export default Resource;
