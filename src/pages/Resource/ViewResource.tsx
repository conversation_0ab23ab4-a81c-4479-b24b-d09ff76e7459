import React, { useEffect, useState } from "react";
import { GridColDef } from "@mui/x-data-grid";
// import useFetchDomains from "../../hooks/useFetchDomains";
import useFetchResourceById from "../../hooks/useFetchResourceById";
import DataTable from "../../components/DataGrids/DataGrid";
import { useNavigate, useParams } from "react-router-dom";
import {
  Button,
  Box,
  Grid,
  Checkbox,
  IconButton,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
} from "@mui/material";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CustomValidationSummary from "./CustomValidationSummary";
import useFetchConnectionKeyByLinkedServiceId from "../../hooks/useFetchConnectionKeyByLinkedServiceId";
import ViewLinkedServices from "../../components/Resource/ViewLinkedServices";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import useFetchFileProcessing from "../../hooks/useFetchFileProcessing";
import FilterRules from "../../components/Resource/FilterRules";
import { dateTimeFormat, sqlDatabaseType } from "../../services/constants";
import { useResourceContext } from "../../contexts/ResourceContext";
import useFetchAllResources from "../../hooks/useFetchAllResources";
import CloneResourceDialog from "../../components/Dialogs/CloneResourceDialog";
import CrossFieldValidations from "../ResourceColumns/CrossFieldValidations";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import CustomAccordion from "../../components/Molecules/Accordian/CustomAccordion";
import RenderVariables from "../../components/Molecules/Resource/RenderVariables";
import ViewVariables from "../../components/Molecules/Resource/ViewVariables";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import useFetchDomains from "../../hooks/useFetchDomains";
import useFetchAllResourcesWithoutDomain from "../../hooks/useFetchAllResourcesWithoutDomain";
import useFetchResourcesByIdMultipleIds from "../../hooks/useFetchResourcesByMultipleIds";
import CloneResourceWithResourceColumnDialog from "../../components/Dialogs/CloneResourceWithResourceColumnDialog";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";
import { IconBtnEditBase } from "../../common/utils/icons";

export default function ViewResource() {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { domainId } = useParams();
  const [currentResourceId, setCurrentResourceId] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [resourceColumns, setResourceColumns] = React.useState<any>([]);
  const { id }: any = useParams();
  // const [domainsData] = useFetchDomains({ setIsLoading });
  const navigate = useNavigate();
  const [expandedRow, setExpandedRow] = useState<any>([]);
  const [activeTab, setActiveTab] = useState<string>("resourceColumns");
  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<number>(0);
  const [viewResourceData, setViewResourceData] = useState<any>({});
  const [crossFieldValidations, setCrossFieldValidations] = useState<any>([]);
  const [aggregationType, setAggregationType] = useState<string>("flat");
  const [additionalResourceId, setAdditionalResourceId] = useState<any>([]);
  const [connectionKeysData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId,
    setIsLoading,
  });
  const [linkedServicesData] = useFetchLinkedServices({ setIsLoading });
  const [fileProcessingData] = useFetchFileProcessing({ setIsLoading });
  const [additionalResourceData] = useFetchResourcesByIdMultipleIds({
    resourceIds: additionalResourceId,
    setIsLoading,
  });

  useEffect(() => {
    setCurrentResourceId(parseInt(id));
  }, [id]);

  const [resourceData, resourceColumnData] = useFetchResourceById({
    currentResourceId,
    setIsLoading,
  });

  const [allResources] = useFetchAllResourcesWithoutDomain({
    setIsLoading,
    aggregation_type: aggregationType,
  });
  const {
    formData,
    resourcesData,
    setResourcesData,
    domainsData,
    setDomainsData,
    errors,
    setErrors,
  } = useResourceContext();

  const {
    globalVariables,
    setGlobalVariables,
    allResourcesData,
    setAllResourcesData,
  } = useRuleResourceContext();

  const [domains] = useFetchDomains({ setIsLoading });
  const [cloneResourceDialog, setCloneResourceDialog] =
    useState<boolean>(false);

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources, setAllResourcesData]);

  useEffect(() => {
    if (domains) {
      setDomainsData(domains);
    }
  }, [domains]);

  const currentDomain = domainsData?.find(
    (domain: any) => domain.id === resourceData?.domain_id
  );
  // Define table column names
  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      renderCell: (params: any) => (
        <IconButton
          size="small"
          tabIndex={-1}
          disabled={
            params?.row?.is_derived ||
            params?.row?.custom_validations?.length > 0 ||
            params?.row?.is_reference ||
            params?.row?.reference_column_definition != null
              ? false
              : true
          }
          // aria-label={isExpanded ? "Close" : "Open"}
          onClick={() => {
            // toggle in array
            if (expandedRow.includes(params.row.id)) {
              setExpandedRow((prev: any) =>
                prev.filter((item: any) => item !== params.row.id)
              );
            } else {
              setExpandedRow((prev: any) => [...prev, params.row.id]);
            }
          }}
        >
          {params?.row?.is_derived ||
          params?.row?.custom_validations?.length > 0 ||
          params?.row?.is_reference ||
          params?.row?.reference_column_definition != null ? (
            <ExpandMoreIcon
              sx={{
                transform: `rotateZ(${
                  expandedRow.includes(params.row.id) ? 180 : 0
                }deg)`,
                transition: (theme) =>
                  theme.transitions.create("transform", {
                    duration: theme.transitions.duration.shortest,
                  }),
              }}
              fontSize="inherit"
            />
          ) : null}
        </IconButton>
      ),
    },
    {
      field: "column_name",
      headerName: "Resource Column",
      width: 120,
      minWidth: 150,
      flex: 1,
      renderCell: (params: any) => (
        <LimitChractersWithTooltip value={params?.value} />
      ),
    },
    {
      field: "domain_column",
      headerName: "Domain Column",
      width: 120,
      minWidth: 130,
      flex: 1,
      renderCell: (params: any) => (
        <LimitChractersWithTooltip value={params?.value} />
      ),
    },
    {
      field: "datatype",
      headerName: "Data Type",
      width: 100,
    },
    {
      field: "is_mandatory",
      headerName: "Mandatory",
      maxWidth: 90,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={
              params.value &&
              (params.value === "Y" ||
                params.value === "y" ||
                params.value === "Yes" ||
                params.value === "yes" ||
                params.value === true)
            }
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "key",
      headerName: "Key",
      maxWidth: 60,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={
              resourceColumnData &&
              resourceColumnData?.resource_column_properties?.unique_columns.includes(
                params.row.column_name
              )
            }
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "is_reference",
      headerName: "Reference",
      description: "Reference Data",
      maxWidth: 90,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={params.value && params.value === true}
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "is_active",
      headerName: "Act./Inac.",
      maxWidth: 70,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Checkbox
            checked={params && params.value !== undefined ? params.value : true}
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
            disabled
          />
        );
      },
    },
    {
      field: "column_length",
      headerName: "Length",
      width: 100,
    },
    {
      field: "severity_level",
      headerName: "Severity",
      width: 100,
    },
    {
      field: "data_format",
      headerName: "Format",
      width: 100,
      flex: 1,
      renderCell: (params: any) => {
        return (
          <Tooltip
            title={dateTimeFormat.includes(params.value) ? params.value : null}
            placement="top"
            arrow
          >
            <span>{params.value}</span>
          </Tooltip>
        );
      },
    },
  ];

  const handelFormatColumns = (resourceColumnData: any) => {
    const formattedColumns = resourceColumnData?.resource_columns.map(
      (RColumn: any, idx: any) => ({
        id: idx,
        column_name: RColumn.column_name,
        domain_column: RColumn.domain_column,
        datatype: RColumn?.constraints.datatype,
        is_mandatory: RColumn?.constraints.is_mandatory,
        data_format: RColumn?.constraints.data_format,
        column_length: RColumn?.constraints.column_length,
        is_derived: RColumn?.constraints.is_derived,
        derived_column_definition:
          RColumn?.constraints.derived_column_definition,
        is_reference: RColumn?.constraints.is_reference,
        reference_column_definition:
          RColumn?.constraints.reference_column_definition,
        custom_validations: RColumn?.constraints.custom_validations,
        severity_level:
          RColumn?.severity_level === 1
            ? "High"
            : RColumn?.severity_level === 2
            ? "Medium"
            : "Low",
        is_active:
          RColumn && RColumn.is_active !== undefined ? RColumn.is_active : true,
      })
    );

    setResourceColumns(formattedColumns);
    return formattedColumns;
  };
  useEffect(() => {
    if (
      resourceColumnData?.resource_column_properties?.resource_columns?.length >
      0
    ) {
      handelFormatColumns(resourceColumnData?.resource_column_properties);
    }
    setCrossFieldValidations(
      resourceColumnData?.resource_column_properties?.cross_field_validations ||
        []
    );
    setSelectLinkedServiceId((prev) => resourceData?.linked_service_id);
  }, [resourceColumnData]);

  useEffect(() => {
    if (resourceData) {
      const resourceVariables =
        resourceData?.additional_properties?.inline_variables;
      setGlobalVariables({
        ...globalVariables,
        ...resourceVariables,
      });
      setAggregationType(resourceData?.aggregation_type);
      const additionalResourceIds =
        resourceData?.additional_properties?.additional_resource_data?.map(
          (res: any) => res?.resource_id
        );
      setAdditionalResourceId(additionalResourceIds);
    }
  }, [resourceData]);

  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
  };

  const tabContent: any = {
    resourceColumns: (
      <Box sx={{ marginTop: "-20px" }}>
        <DataTable
          dataColumns={columns}
          dataRows={resourceColumns}
          checkboxSelection={false}
          dataListTitle="Resource Info"
          className="dataTable no-radius"
          loading={isLoading}
        />
      </Box>
    ),
    customValidationSummary: (
      <Box sx={{ marginTop: "-20px" }}>
        <CustomValidationSummary
          customData={resourceColumnData}
          resourceColumns={resourceColumns}
        />
      </Box>
    ),
    crossFieldValidations: (
      <>
        <CrossFieldValidations
          crossFieldValidations={crossFieldValidations}
          setCrossFieldValidations={setCrossFieldValidations}
          isViewOnly={true}
          errors={errors}
          setErrors={setErrors}
        />
      </>
    ),
  };
  const [updatedFormData, setUpdatedFormData] = useState(null);
  useEffect(() => {
    if (resourceData) {
      const newFormData = {
        ...formData,
        filter_rules: resourceData?.additional_properties?.filter_rules
          ? resourceData?.additional_properties?.filter_rules.map(
              (rule: any) => ({
                ...rule,
                id: Math.random(),
              })
            )
          : [],
      };
      setUpdatedFormData(newFormData);
      let updatedResource: any;
      if (
        resourceData.linked_service_id &&
        resourceData?.additional_properties?.resource_definition?.type
      ) {
        let updatedResourceDefinition =
          resourceData?.additional_properties?.resource_definition?.[
            `${resourceData?.additional_properties?.resource_definition?.type}_definition`
          ];
        if (
          sqlDatabaseType.includes(
            resourceData?.additional_properties?.resource_definition?.type
          )
        ) {
          updatedResourceDefinition =
            resourceData?.additional_properties?.resource_definition
              ?.sql_definition;
        }
        updatedResource = {
          ...resourceData,
          additional_properties: {
            ...resourceData.additional_properties,
            resource_definition: {
              ...resourceData.additional_properties.resource_definition,
              ...updatedResourceDefinition,
            },
          },
        };
      } else {
        updatedResource = resourceData;
      }
      setViewResourceData(updatedResource);
    }
    setCurrentBreadcrumbPage({
      name: resourceData?.resource_name,
      id: resourceData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [resourceData]);

  const handleChipClick = (domainId: number, resource_id: any) => {
    window.open(`/resource/${domainId}/view/${resource_id}`, "_blank");
  };

  return (
    <>
      <Box className="dashboard-title-group flex-end">
        <div className="right-column column-gap-12">
          <Button
            variant="contained"
            color="secondary"
            onClick={() => setCloneResourceDialog(true)}
            className="btn-orange btn-nostyle"
          >
            clone Resource
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={() =>
              navigate(
                `/resource/${domainId}/validate-resource/${currentResourceId}`
              )
            }
            className="btn-orange btn-nostyle btn-blue"
          >
            Validate
          </Button>
          <button
            className="btn-nostyle icon-btn-edit"
            onClick={() =>
              navigate(`/resource/${domainId}/edit/${currentResourceId}`)
            }
          >
            <IconBtnEditBase />
          </button>
        </div>
      </Box>
      <Box sx={{ marginTop: 2 }}>
        <div className="accordion-panel">
          <Accordion className="heading-bold">
            <AccordionSummary
              aria-controls="panel2d-content"
              id="panel2d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              <h4>Resource Details</h4>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
                  <label className="label-text text-bold">Domain</label>
                  <div className="form-control">
                    {currentDomain?.domain_name || ""}
                  </div>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
                  <label className="label-text text-bold">Resource Name</label>
                  <div className="form-control word-break-all">
                    {resourceData?.resource_name || ""}
                  </div>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
                  <label className="label-text text-bold">
                    Resource Prefix
                  </label>
                  <div className="form-control word-break-all">
                    {resourceData?.resource_prefix || ""}
                  </div>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
                  <label className="label-text text-bold">
                    Resource Column
                  </label>
                  <div className="form-control word-break-all">
                    {resourceColumnData?.name || ""}
                  </div>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
                  <label className="label-text text-bold">System</label>
                  <div className="form-control word-break-all">
                    {resourceData?.resource_type || ""}
                  </div>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
                  <label className="label-text text-bold">Code</label>
                  <div className="form-control word-break-all">
                    {resourceData?.code || "N/A"}
                  </div>
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={3} xl={3}>
                  <label className="label-text text-bold">
                    Aggregation type
                  </label>
                  <div className="form-control word-break-all">
                    {resourceData?.aggregation_type || ""}
                  </div>
                </Grid>
                {resourceData?.aggregation_type === "aggregated" && (
                  <ViewLinkedServices
                    resourceData={resourceData}
                    linkedServicesData={linkedServicesData}
                    connectionKeysData={connectionKeysData}
                    fileProcessingData={fileProcessingData}
                  />
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>
          {resourceData?.aggregation_type !== "aggregated" && (
            <Accordion className="heading-bold">
              <AccordionSummary
                aria-controls="panel2d-content"
                id="panel2d-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4>Linked Service</h4>
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                <Grid container rowSpacing={1.5} columnSpacing={2.5}>
                  <ViewLinkedServices
                    resourceData={viewResourceData}
                    linkedServicesData={linkedServicesData}
                    connectionKeysData={connectionKeysData}
                    fileProcessingData={fileProcessingData}
                  />
                </Grid>
              </AccordionDetails>
            </Accordion>
          )}
          {resourceData?.additional_properties?.additional_resource_data !==
            null &&
          resourceData?.additional_properties?.additional_resource_data
            ?.length > 0 ? (
            <Accordion className="heading-bold">
              <AccordionSummary
                aria-controls="panel2d-content"
                id="panel2d-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4>Additional Data</h4>
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                <Grid container rowSpacing={2.5} columnSpacing={4}>
                  <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                    <div className="table-responsive">
                      <table className="table-filters additonal-data-filters">
                        <tr>
                          <th>Sr. No.</th>
                          <th>Resource Name</th>
                          <th>Columns to merge (Additional Resource)</th>
                          <th>Columns to merge (Base Resource)</th>
                          <th>Merge type</th>
                        </tr>
                        <tbody>
                          {resourceData?.additional_properties?.additional_resource_data.map(
                            (additionalRes: any, idx: any) => {
                              return (
                                <tr key={additionalRes?.id}>
                                  <td width={"8%"}>
                                    <span>{idx + 1}</span>
                                  </td>
                                  <td width={"18%"}>
                                    <div
                                      onClick={() =>
                                        handleChipClick(
                                          resourceData?.domain_id,
                                          additionalRes?.resource_id
                                        )
                                      }
                                      className="form-control"
                                    >
                                      {additionalRes?.resource_id !== null &&
                                      additionalRes?.resource_id !== undefined
                                        ? additionalResourceData?.find(
                                            (res: any) =>
                                              res.id ===
                                              additionalRes?.resource_id
                                          )?.resource_name || "N/A"
                                        : "N/A"}
                                    </div>
                                  </td>
                                  <td width={"23%"}>
                                    <div className="form-control">
                                      {additionalRes?.add_on_column_names !==
                                      null
                                        ? additionalRes?.add_on_column_names
                                            ?.length > 0
                                          ? additionalRes?.add_on_column_names.join(
                                              ", "
                                            )
                                          : "N/A"
                                        : "N/A"}
                                    </div>
                                  </td>
                                  <td width={"23%"}>
                                    <div className="form-control">
                                      {additionalRes?.base_column_names !== null
                                        ? additionalRes?.base_column_names
                                            ?.length > 0
                                          ? additionalRes?.base_column_names.join(
                                              ", "
                                            )
                                          : "N/A"
                                        : "N/A"}
                                    </div>
                                  </td>
                                  <td width={"15%"}>
                                    <div className="form-control">
                                      {additionalRes?.merge_type !== null
                                        ? additionalRes?.merge_type
                                        : "N/A"}
                                    </div>
                                  </td>
                                </tr>
                              );
                            }
                          )}
                        </tbody>
                      </table>
                    </div>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          ) : null}
          {resourceData?.additional_properties?.filter_rules?.length > 0 && (
            <Accordion className="heading-bold">
              <AccordionSummary
                aria-controls="panel2d-content"
                id="panel2d-header"
                expandIcon={<ExpandMoreIcon />}
              >
                <h4>Filter Rules</h4>
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                <FilterRules formData={updatedFormData} isViewOnly={true} />
              </AccordionDetails>
            </Accordion>
          )}

          {Object.keys(globalVariables).length > 0 && (
            <CustomAccordion
              expandId="panel2d-header-support-documents"
              title="Resource Variables"
              isEnabled={true}
              topMargin={8}
            >
              <ViewVariables
                isViewOnly={true}
                error={errors}
                setErrors={setErrors}
              />
            </CustomAccordion>
          )}
        </div>
        <Tabs
          sx={{
            mt: "8px",
          }}
          value={activeTab}
          onChange={handleChangeTab}
          className="mui-tabs alternative-1"
          variant="scrollable"
        >
          <Tab label="Resource Columns" value="resourceColumns" />
          <Tab
            label="Custom Validaton Summary"
            value="customValidationSummary"
          />
          <Tab label="Adhoc Query" value="crossFieldValidations" />
        </Tabs>
        <Box>{tabContent[activeTab]}</Box>
      </Box>
      <CloneResourceWithResourceColumnDialog
        cloneResourceDialog={cloneResourceDialog}
        setCloneResourceDialog={setCloneResourceDialog}
        domainData={{
          isDomainExist: true,
          currentResourceId: currentResourceId,
        }}
      />
    </>
  );
}
