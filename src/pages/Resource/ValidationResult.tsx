import { useLocation, useParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import useFetchIncidentByExecutionId from "../../hooks/useFetchIncidentByExecutionId";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { getResourceResultByResourceId } from "../../services/resourcesService";
import DetailedValidationResult from "../../components/Resource/DetailedValidationResult";
import SummarizesValidationResults from "../../components/Resource/SummarizesValidationResults";

const ValidationResult = () => {
  const location = useLocation();

  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { resourceResultId: currentResourceResultId } = useParams();

  const [validateResult, setValidateResult] = useState<any>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [isTriggereBtnPressed, setIsTriggereBtnPressed] = useState(false);
  const [isCommentBtnPressed, setIsCommentBtnPressed] = useState(false);
  const [currentIncidentData, setCurrentIncidentData] = useState<any>([]);

  const [fetchIncidentData] = useFetchIncidentByExecutionId({
    setIsLoading,
    data: currentResourceResultId, // need to make it dynamic [currentResourceResultId],
    isTriggereBtnPressed,
    isCommentBtnPressed,
  });
  useEffect(() => {
    setCurrentIncidentData(fetchIncidentData);
  }, [fetchIncidentData]);

  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: validateResult?.resource_name,
      id: validateResult?.resource_id,
      url: `/resource/${validateResult?.domain_id}/view/${validateResult?.resource_id}`,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null, url: "" });
    };
  }, [validateResult]);

  useEffect(() => {
    const isValidateResourceResponse =
      location?.state?.isValidateResourceResponse;
    const searchParams = new URLSearchParams(location.search);
    let baseResourceId = searchParams.get("baseResource") ?? "";
    if (isValidateResourceResponse !== true) {
      getDashboardResultData(currentResourceResultId, baseResourceId);
    } else {
      setValidateResult(location.state.response);
    }
  }, [location]);

  useEffect(() => {
    const handleHashNavigation = () => {
      const hash = window.location.hash;
      if (hash) {
        const element = document.querySelector(hash);
        if (element) {
          const addBoxShadow = setTimeout(() => {
            element.classList.add("box-shadow10");
          }, 1000);
          element.scrollIntoView({ behavior: "smooth" });
          const removeBoxShadow = setTimeout(() => {
            element.classList.remove("box-shadow10");
          }, 3000);
          return () => {
            clearTimeout(addBoxShadow);
            clearTimeout(removeBoxShadow);
          };
        }
      }
    };
    handleHashNavigation();
  }, [validateResult]);

  const findBaseResourceValidationResult = (
    validationResult: any,
    baseResourceId: any
  ): any => {
    if (!validationResult) {
      return null;
    }

    // Check if the current validation result's resource_id matches the baseResourceId
    if (validationResult?.resource_id == baseResourceId) {
      return validationResult;
    }

    // Recursively search in the nested base_resource_validation_result
    return findBaseResourceValidationResult(
      validationResult?.additional_properties?.base_resource_validation_result,
      baseResourceId
    );
  };

  const getDashboardResultData = async (
    currentResourceResultId: any,
    baseResourceId: any
  ) => {
    setIsLoading(true);
    setIsBackdropLoading(true);
    try {
      const result =
        currentResourceResultId > 0 &&
        (await getResourceResultByResourceId({ currentResourceResultId }));
      if (baseResourceId !== "") {
        const baseValidationResult = findBaseResourceValidationResult(
          result?.additional_properties?.base_resource_validation_result,
          baseResourceId
        );

        setValidateResult(baseValidationResult || null);
      } else {
        setValidateResult(result);
      }
    } catch (err) {
      setValidateResult({});
    } finally {
      setIsLoading(false);
      setIsBackdropLoading(false);
    }
  };
  return (
    <>
      {validateResult?.is_detailed_execution_data_available ? (
        <DetailedValidationResult
          validateResult={validateResult}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          isBackdropLoading={isBackdropLoading}
          currentIncidentData={currentIncidentData}
          setIsTriggereBtnPressed={setIsTriggereBtnPressed}
          setIsCommentBtnPressed={setIsCommentBtnPressed}
          currentResourceResultId={currentResourceResultId}
        />
      ) : (
        <SummarizesValidationResults
          validateResult={validateResult}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          isBackdropLoading={isBackdropLoading}
          currentIncidentData={currentIncidentData}
          setIsTriggereBtnPressed={setIsTriggereBtnPressed}
          setIsCommentBtnPressed={setIsCommentBtnPressed}
          currentResourceResultId={currentResourceResultId}
        />
      )}
    </>
  );
};

export default ValidationResult;
