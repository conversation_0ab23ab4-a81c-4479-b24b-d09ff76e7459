import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useToast } from "../../services/utils";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Grid,
  Tooltip,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import FlexBetween from "../../components/FlexBetween";
import ResultStorageParameters from "../../components/Rules/ResultStorageParameters";
import RunParameters from "../../components/Rules/RunParameters";
import { useResourceContext } from "../../contexts/ResourceContext";
import useFetchFileProcessing from "../../hooks/useFetchFileProcessing";
import useFetchResourceByResourceId from "../../hooks/useFetchResourceByResourceId";
import { apiType, sqlDatabaseType } from "../../services/constants";
import {
  backgroundResourceValidation,
  ResourceValidation,
} from "../../services/resourcesService";
import ValidateResourceDataTab from "./ValidateResourceDataTab";
//add for varibles
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import Loader from "../../components/Molecules/Loader/Loader";
import useFetchConnectionKeysAll from "../../hooks/useFetchConnectionKeysAll";
import FilterRules from "../../components/Resource/FilterRules";
import InlineVariables from "./InlineVariables";
import CustomAccordian from "../../components/Molecules/Accordian/CustomAccordion";
import useFetchResourceColumnsById from "../../hooks/useFetchResourceColumnsById";
import AddMultipleGridList from "../../components/AddMultipleGridList";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { filterRulesNameSchema, filterRulesSqlSchema } from "../../schemas";
import useFetchAllResourcesWithoutDomain from "../../hooks/useFetchAllResourcesWithoutDomain";
import { executionNameSchema } from "../../schemas";
import ExecutionName from "../../components/Molecules/Rule/ExecutionName";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import { getDifferentValues } from "../../services/utils/processRuleExecutionResourcesData";
import { compareFilterRules } from "../../services/utils/compareFilterRules";
import { IconBtnEditBase } from "../../common/utils/icons";
interface FilterFormData {
  filter_rules: any[];
}

const ValidateResource = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const {
    setResourceColumnData,
    resourceColumnData,
    refetchData,
    errors,
    setErrors,
    setLinkedServicesData,
  } = useResourceContext();
  const {
    isResourceEdit,
    globalVariables,
    setGlobalVariables,
    allResourcesData,
    setAllResourcesData,
    setFileProcessingData,
    setFetchedAllConnectionKeys,
  } = useRuleResourceContext();
  const { showToast } = useToast();
  const [isEditFilters, setIsEditFilters] = useState(false);
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [filterFormData, setFilterFormData] = useState<
    FilterFormData | undefined
  >(undefined);
  const [intialFilterFormData, setIntialFilterFormData] = useState<
    FilterFormData | undefined
  >(undefined);
  const [originalFilterRulesData, setOriginalFilterRulesData] = useState<
    FilterFormData | undefined
  >(undefined);
  const [tempFilterFormData, setTempFilterFormData] = useState<any>({});
  const [hasFilterChanges, setHasFilterChanges] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [resultStorageParameters, setResultStorageParameters] = useState<any>({
    linked_service_id: process.env.REACT_APP_OUTPUT_STORAGE_LINKED_SERVICE_ID,
    linked_service_code:
      process.env.REACT_APP_OUTPUT_STORAGE_LINKED_SERVICE_CODE,
    connection_key_id: process.env.REACT_APP_OUTPUT_STORAGE_CONNECTION_KEY,
    file_path: process.env.REACT_APP_OUTPUT_FILES_BASE_PATH,
  });
  const [runParameters, setRunParameters] = useState<any>({
    no_of_errors_in_response: 0,
    no_of_errors_in_output_files: 0,
    skip_duplicate_records: true,
    summary_mode: false,
    generate_files: true,
    skip_validation: false,
    validation_severity_level: "Low",
    run_instance: {
      run_id: 1,
      run_name: "Legacy",
    },
    save_input_data_file: false,
    store_errors_snapshots_and_create_issues: true,
    keep_downloaded_files: false,
    pull_new_files_from_server: false,
    is_long_running_job: false,
    isCustomiseSeverity: false,
  });

  const [severityColumnNames, setSeverityColumnNames] = useState<any>({
    low: null,
    medium: null,
    high: null,
  });
  const [isEditResource, setIsEditResource] = useState(false);
  const [expandedAccordion, setExpandedAccordion] =
    useState<any>("resource-data");
  const [validationMessage, setValidationMessage] = useState<any[]>([]);
  const [checkValidation, setCheckValidation] = useState(false);
  const [currentResourceColumnId, setCurrentResourceColumnId] =
    useState<any>(null);

  const [validateResourcePayload, setValidateResourcePayload] =
    useState<any>(null);
  const [isEditVariables, setIsEditVariables] = useState(false);
  const [inlineVariables, setInlineVariables] = useState<any>({});
  const [intialInlineVariables, setIntialInlineVariables] = useState<any>({});
  const [resourcesData, setResourcesData] = useState<any>({});
  const [originalResourcesData, setOriginalResourcesData] = useState<any>({});
  const [isReRunData, setIsReRunData] = useState<any>({
    validationID: null,
    isReRun: false,
  });
  const [executionName, setExecutionName] = useState("");
  const [executionMode, setExecutionMode] = useState(true);

  const [fileProcessingData] = useFetchFileProcessing({ setIsLoading });
  const [linkedServices] = useFetchLinkedServices({ setIsLoading });
  const [fetchedConnectionKeys] = useFetchConnectionKeysAll({ setIsLoading });
  const [allResources] = useFetchAllResourcesWithoutDomain({
    setIsLoading,
    aggregation_type:
      allResourcesData?.length <= 0 &&
      resourcesData?.aggregation_type === "aggregated"
        ? "aggregated"
        : "flat",
  });

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const reRun = params.get("re-run");
    const validationId = params.get("validation-id");
    if (reRun && validationId) {
      setIsReRunData({
        validationID: Number(validationId),
        isReRun: true,
      });
    } else {
      setIsReRunData({
        validationID: null,
        isReRun: false,
      });
    }
  }, [location]);
  useEffect(() => {
    setFileProcessingData(fileProcessingData);
  }, [fileProcessingData]);

  useEffect(() => {
    if (linkedServices) {
      setLinkedServicesData(linkedServices);
    }
  }, [linkedServices, setLinkedServicesData]);

  useEffect(() => {
    if (fetchedConnectionKeys) {
      setFetchedAllConnectionKeys(fetchedConnectionKeys);
    }
  }, [fetchedConnectionKeys, setFetchedAllConnectionKeys]);

  const [resourceData] = useFetchResourceByResourceId({
    currentResourceId: id,
    setIsLoading,
  });
  const [resourceColumn] = useFetchResourceColumnsById({
    resourceColumnId: currentResourceColumnId,
    setIsLoading,
  });

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources, setAllResourcesData]);

  useEffect(() => {
    if (resourceData && fetchedConnectionKeys) {
      const resourceVariables =
        resourceData?.additional_properties?.inline_variables;
      setGlobalVariables({
        ...globalVariables,
        ...resourceVariables,
      });
      const connectionKeyDetail = fetchedConnectionKeys?.find(
        (option: { id: any }) =>
          option.id ===
          resourceData?.additional_properties?.resource_definition
            ?.api_definition?.connection_key
      );
      let updatedResourceData: any;
      if (
        resourceData.linked_service_id &&
        resourceData?.additional_properties?.resource_definition?.type
      ) {
        let updatedResourceDefinition =
          resourceData?.additional_properties?.resource_definition?.[
            `${resourceData?.additional_properties?.resource_definition?.type}_definition`
          ];
        if (
          sqlDatabaseType.includes(
            resourceData?.additional_properties?.resource_definition?.type
          )
        ) {
          updatedResourceDefinition =
            resourceData?.additional_properties?.resource_definition
              ?.sql_definition;
        }
        updatedResourceData = {
          ...resourceData,
          additional_properties: {
            ...resourceData.additional_properties,
            resource_definition: {
              ...resourceData.additional_properties.resource_definition,
              ...updatedResourceDefinition,
              api_definition: {
                ...resourceData?.additional_properties?.resource_definition
                  ?.api_definition,
                url: connectionKeyDetail?.api_url,
                request_timeout: 0,
              },
            },
          },
        };
      } else {
        updatedResourceData = resourceData;
      }
      setResourcesData(updatedResourceData);
      setOriginalResourcesData(JSON.parse(JSON.stringify(updatedResourceData)));
      const filter_rules = updatedResourceData?.additional_properties
        ?.filter_rules
        ? updatedResourceData?.additional_properties?.filter_rules.map(
            (rule: any, index: number) => ({
              ...rule,
              id: index,
            })
          )
        : [];
      setFilterFormData({
        ...updatedResourceData?.additional_properties,
        filter_rules: filter_rules,
      });
      setOriginalFilterRulesData({
        ...updatedResourceData?.additional_properties,
        filter_rules: filter_rules,
      });
      setTempFilterFormData({
        ...updatedResourceData?.additional_properties,
        filter_rules: filter_rules,
      });
      setIntialFilterFormData(
        JSON.parse(
          JSON.stringify({
            ...updatedResourceData?.additional_properties,
            filter_rules: filter_rules,
          })
        )
      );
      setIntialInlineVariables(
        JSON.parse(
          JSON.stringify(
            updatedResourceData?.additional_properties?.inline_variables
          )
        )
      );

      setCurrentResourceColumnId(
        updatedResourceData?.additional_properties?.resource_column_details_id
      );
    }
    setCurrentBreadcrumbPage({
      name: resourceData?.resource_name,
      id: resourceData?.id,
    });
    setExecutionName(resourceData?.resource_name);
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [resourceData]);

  useEffect(() => {
    const formChanged =
      JSON.stringify(filterFormData) !== JSON.stringify(intialFilterFormData);
    setHasChanges(formChanged);
  }, [filterFormData, intialFilterFormData]);

  useEffect(() => {
    if (resourceColumn && Object.keys(resourceColumn)?.length) {
      setResourceColumnData(resourceColumn);
    }
  }, [resourceColumn]);

  const getFilePreProcessing = (id: string | number | undefined) => {
    const filePreProcessing = fileProcessingData?.find(
      (file: { id: string | number | null | undefined }) => file.id === id
    );
    return filePreProcessing
      ? filePreProcessing.file_processing_attributes
      : null;
  };

  const getResourceReqBody = (resource: any) => {
    const aggregationType = resource?.aggregation_type;
    const type = resource?.additional_properties?.resource_definition?.type;

    if (!aggregationType || (aggregationType === "flat" && !type)) {
      return null;
    }

    const additional_base_resource_data =
      resource?.additional_properties?.additional_resource_data?.length > 0
        ? processAdditionalResourceData(
            resource?.additional_properties?.additional_resource_data
          )
        : null;

    let resourceObject: any = {
      aggregation_type: aggregationType,
      resource_column_details_id:
        resource?.additional_properties?.resource_column_details_id,
      resource_name: resource?.resource_name,
      resource_id: resource?.id || null,
      linked_service_id: resource?.linked_service_id || null,
      linked_service_code: resource?.linked_service_code || null,
      file_processing_id: resource?.file_processing_id,
      additional_base_resource_data,
    };

    // FLAT resources: add definition and API-related info
    if (aggregationType === "flat") {
      const {
        connection_key,
        container_name,
        resource_path,
        file_name,
        column_delimiter,
        sql_query,
        use_multi_thread_reader,
        column_name_to_partition_on_sql_query,
        excel_sheet_name,
      } = getResourceDefinition(resource, type);

      const {
        user_name,
        password,
        bearer_token,
        api_key,
        oauth_client_id,
        oauth_client_secret,
        oauth_url,
        method,
        content_type,
        body,
        query_params,
        url_params,
        url,
        request_timeout,
      } = getApiDefinition(resource);

      const file_pre_processing =
        sqlDatabaseType.includes(type) || apiType.includes(type)
          ? null
          : getFilePreProcessing(resource?.file_processing_id);

      const api_request = apiType.includes(type)
        ? {
            user_name,
            password,
            bearer_token,
            api_key,
            oauth_client_id,
            oauth_client_secret,
            oauth_url,
            method,
            content_type,
            body,
            query_params,
            url_params,
            url,
            request_timeout,
          }
        : null;

      resourceObject = {
        ...resourceObject,
        type,
        connection_key,
        sql_query,
        container_name,
        resource_path,
        file_name,
        column_delimiter,
        file_pre_processing,
        api_request,
        use_multi_thread_reader,
        column_name_to_partition_on_sql_query,
        excel_sheet_name,
      };
    }

    // AGGREGATED resources: include aggregation props and recurse
    if (aggregationType === "aggregated") {
      const aggregation_properties = {
        base_resource_id:
          resource?.additional_properties?.aggregation_properties
            ?.base_resource_id,
        base_resource_code:
          resource?.additional_properties?.aggregation_properties
            ?.base_resource_code,
        aggregation_query:
          resource?.additional_properties?.aggregation_properties
            ?.aggregation_query,
        base_resource_columns:
          resource?.additional_properties?.aggregation_properties
            ?.base_resource_columns,
        base_resource_validation:
          resource?.additional_properties?.aggregation_properties
            ?.base_resource_validation,
      };

      resourceObject = {
        ...resourceObject,
        aggregation_properties,
        aggregation_base_resource_data: getResourceReqBody(
          resource?.additional_properties?.aggregation_properties
        ),
      };
    }

    // Remove null/undefined values
    resourceObject = Object.fromEntries(
      Object.entries(resourceObject).filter(
        ([_, v]) => v !== null && v !== undefined
      )
    );

    return Object.keys(resourceObject).length ? resourceObject : null;
  };

  const checkOutputStorageParamsNull = (
    output_storage_params: { [s: string]: unknown } | ArrayLike<unknown>
  ) => {
    return Object.values(output_storage_params).some((value) => value === null);
  };
  const getApiDefinition = (resource: any) => {
    let user_name =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.user_name || null;
    let password =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.password || null;
    let bearer_token =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.bearer_token || null;
    let api_key =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.api_key || null;
    let oauth_client_id =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.oauth_client_id || null;
    let oauth_client_secret =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.oauth_client_secret || null;
    let oauth_url =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.oauth_url || null;
    let method =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.method || "get";
    let content_type =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.content_type || "application/json";
    let body =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.body || null;
    let query_params =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.query_params || null;
    let url_params =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.url_params || null;
    let url =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.url || null;
    let request_timeout =
      resource?.additional_properties?.resource_definition?.api_definition
        ?.request_timeout || 0;
    return {
      user_name,
      password,
      bearer_token,
      api_key,
      oauth_client_id,
      oauth_client_secret,
      oauth_url,
      method,
      content_type,
      body,
      query_params,
      url_params,
      url,
      request_timeout,
    };
  };
  const getResourceDefinition = (resource: any, type: string) => {
    let connection_key = null;
    let container_name = null;
    let resource_path = null;
    let file_name = null;
    let column_delimiter = null;
    let sql_query = null;
    let use_multi_thread_reader = false;
    let column_name_to_partition_on_sql_query = null;
    let excel_sheet_name = null;
    const resDef = resource?.additional_properties?.resource_definition;
    console.log("resDef>>>", resDef);
    if (type === "blob") {
      connection_key = resDef?.connection_key || null;
      container_name = resDef?.container_name || null;
      resource_path = resDef?.resource_path || null;
      file_name = resDef?.file_name || null;
      column_delimiter = resDef?.column_delimiter || null;
      excel_sheet_name = resDef?.excel_sheet_name || null;
    } else if (type === "sftp") {
      connection_key = resDef?.connection_key || null;
      resource_path = resDef?.remote_directory || null;
      file_name = resDef?.file_name || null;
      column_delimiter = resDef?.column_delimiter || null;
      excel_sheet_name = resDef?.excel_sheet_name || null;
    } else if (type === "local") {
      connection_key = resDef?.connection_key || null;
      resource_path = resDef?.resource_path || null;
      file_name = resDef?.file_name || null;
      column_delimiter = resDef?.column_delimiter || null;
      excel_sheet_name = resDef?.excel_sheet_name || null;
    } else if (sqlDatabaseType.includes(type)) {
      sql_query = resDef?.sql_query || null;
      connection_key = resDef?.connection_key || null;
      use_multi_thread_reader =
        resDef?.sql_definition?.use_multi_thread_reader || false;
      column_name_to_partition_on_sql_query =
        resDef?.sql_definition?.column_name_to_partition_on_sql_query || null;
    } else if (apiType.includes(type)) {
      connection_key = resDef?.api_definition?.connection_key || null;
    }

    return {
      connection_key,
      container_name,
      resource_path,
      file_name,
      column_delimiter,
      sql_query,
      use_multi_thread_reader,
      column_name_to_partition_on_sql_query,
      excel_sheet_name,
    };
  };

  const processAdditionalResourceData = (additionalResourceData: any) => {
    if (!Array.isArray(additionalResourceData)) {
      return null;
    }
    const res = additionalResourceData
      .map((resource) => getResourceReqBody(resource))
      .filter(Boolean);
    return res;
  };

  useEffect(() => {
    const validateResourceData = () => {
      setValidationMessage([]);

      let output_storage_params = {
        output_storage_linked_service_id:
          resultStorageParameters?.linked_service_id || null,
        output_storage_linked_service_code:
          resultStorageParameters?.linked_service_code || null,
        output_storage_connection_key:
          resultStorageParameters?.connection_key_id || null,
        output_storage_base_file_path:
          resultStorageParameters?.file_path || null,
      };

      const isOutputStorageParamsNull = checkOutputStorageParamsNull(
        output_storage_params
      );

      let severityColumnNamesPayload = {
        1: severityColumnNames.high,
        2: severityColumnNames.medium,
        3: severityColumnNames.low,
      };

      const severity_column_names: any = Object.fromEntries(
        Object.entries(severityColumnNamesPayload).filter(
          ([_, v]) => v !== null
        )
      );

      let resourceObject: any = getResourceReqBody(resourcesData);
      let originalResourcesObject: any = getResourceReqBody(
        originalResourcesData
      );

      // Get only the different values between resourceObject and originalResourcesObject
      resourceObject = getDifferentValues(
        resourceObject,
        originalResourcesObject
      );

      // Get only the different inline variables
      const differentInlineVariables = getDifferentValues(
        globalVariables,
        intialInlineVariables
      );

      // Remove null values
      resourceObject = Object.fromEntries(
        Object.entries(resourceObject).filter(([_, v]) => v !== null)
      );

      // Compare filter rules and only include the ones that have changed
      const changedFilterRules = compareFilterRules(
        originalFilterRulesData?.filter_rules,
        filterFormData?.filter_rules
      );

      // Only include filter_rules if there are differences
      if (changedFilterRules && changedFilterRules.length > 0) {
        resourceObject.filter_rules = changedFilterRules;
      }
      const {
        validation_severity_level,
        run_instance,
        skip_validation,
        ...filteredRunParameters
      } = runParameters;
      let reqBody: any = {
        resource_data: resourceObject,
        ...(isOutputStorageParamsNull ? {} : { output_storage_params }),
        run_instance: {
          run_id: runParameters?.run_instance.run_id || 1,
          run_name: runParameters?.run_instance.run_name || "Legacy",
        },
        severity_level:
          runParameters?.validation_severity_level === "high"
            ? 1
            : runParameters?.validation_severity_level === "medium"
            ? 2
            : 3,
        validation_execution_report_name:
          resultStorageParameters?.validation_execution_report_name || null,
        execution_name: executionName,
        query_params: {
          ...filteredRunParameters,
        },
      };

      // Only include inline_variables if there are differences
      if (differentInlineVariables !== null) {
        reqBody.inline_variables = differentInlineVariables;
      }

      if (
        reqBody.inline_variables &&
        Object.keys(reqBody.inline_variables).length > 0
      ) {
        Object.keys(reqBody.inline_variables).forEach((key) => {
          if (reqBody.inline_variables[key] === "") {
            setValidationMessage((prev) => [
              ...prev,
              {
                id: `Resource Variables`,
                message: `Please enter value for ${key}`,
              },
            ]);
          }
        });
      }
      if (Object.keys(severity_column_names).length !== 0) {
        reqBody = { ...reqBody, severity_column_names };
      }
      setValidateResourcePayload(reqBody);
    };
    if (checkValidation) validateResourceData();
  }, [checkValidation]);

  useEffect(() => {
    if (validationMessage.length === 0 && validateResourcePayload) {
      setIsBackdropLoading(true);
      if (executionMode) {
        backgroundResourceValidation(validateResourcePayload)
          .then(() => {
            showToast(
              "Your resource validation is in the queue. We will notify you once it's done!",
              "warn"
            );
            navigate(`/validation-execution-history`);
          })
          .catch((error) => {
            console.error(error?.response?.data?.message);
          })
          .finally(() => {
            setCheckValidation(false);
            setValidateResourcePayload(null);
            setIsBackdropLoading(false);
          });
      } else {
        ResourceValidation(validateResourcePayload)
          .then((response) => {
            if (response) {
              showToast("Resource is validated successfully!", "success");
              navigate(
                `/validation-execution-history/validate-result/${response?.id}`,
                {
                  state: {
                    response,
                    isValidateResourceResponse: true,
                  },
                }
              );
            }
          })
          .catch((error) => {
            console.error(error?.response?.data?.message);
          })
          .finally(() => {
            setCheckValidation(false);
            setValidateResourcePayload(null);
            setIsBackdropLoading(false);
          });
      }
    } else if (validationMessage.length > 0) {
      const temp = (
        <div>
          Not all fields are configured. Please check the following fields:
          {validationMessage.map((msg) => (
            <div>
              <span style={{ fontWeight: 900 }}>{msg.id}</span>-{msg.message}
            </div>
          ))}
        </div>
      );
      showToast(temp, "warning");
      setCheckValidation(false);
      return;
    }
  }, [validationMessage, validateResourcePayload]);

  const handleChangeAccordion =
    (panel: string) => (_: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
      setExpandedAccordion(isExpanded ? panel : null);
    };

  const handleChangeVariable = (event: any) => {
    const { name, value } = event.target;
    const newResourceData = {
      ...inlineVariables,
      additional_properties: {
        ...inlineVariables.additional_properties,
        inline_variables: {
          ...inlineVariables.additional_properties.inline_variables,
          [name]: value,
        },
      },
    };
    setInlineVariables(newResourceData);
  };

  useEffect(() => {
    const formChanged =
      JSON.stringify(
        inlineVariables?.additional_properties?.inline_variables
      ) !== JSON.stringify(intialInlineVariables);
    setHasChanges(formChanged);
  }, [inlineVariables, intialInlineVariables]);

  useEffect(() => {
    const formChanged =
      JSON.stringify(tempFilterFormData) !==
      JSON.stringify(intialFilterFormData);
    setHasFilterChanges(formChanged);
  }, [tempFilterFormData, intialFilterFormData]);

  useEffect(() => {
    refetchData();
  }, [id]);
  const checkFilterRuleValidation = async (
    updatedFormData: any,
    schemaType: any
  ) => {
    const getSchemaType =
      schemaType === "name" ? filterRulesNameSchema : filterRulesSqlSchema;
    for (let index = 0; index < updatedFormData.filter_rules.length; index++) {
      const element = updatedFormData.filter_rules[index];
      try {
        await getSchemaType.validate(element, { abortEarly: false });
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] = undefined;
        } else {
          updatedJsonData.filter_rules.push({ [updateKey]: undefined });
        }
        setErrors(updatedJsonData);
      } catch (validationErrors: any) {
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] =
            validationErrors.message;
        } else {
          updatedJsonData.filter_rules.push({
            [updateKey]: validationErrors.message,
          });
        }
        setErrors(updatedJsonData);
      }
    }
  };
  const handleValidateSubmit = async () => {
    try {
      await executionNameSchema.validate(
        { execution_name: executionName },
        {
          abortEarly: false,
        }
      );
      setCheckValidation(true);
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  return (
    <>
      <Loader isLoading={isBackdropLoading} />
      <Loader isLoading={isLoading} />

      {isReRunData?.isReRun && (
        <Box className="re-run-header">
          <h2>Re-Run</h2>
        </Box>
      )}

      <div
        className="accordion-panel"
        style={{ marginBottom: "0", marginTop: "12px" }}
      >
        <Box>
          <ExecutionName
            handleChangeAccordion={handleChangeAccordion}
            errors={errors}
            setErrors={setErrors}
            executionName={executionName}
            setExecutionName={setExecutionName}
            executionMode={executionMode}
            setExecutionMode={setExecutionMode}
          />

          <ValidateResourceDataTab
            resourcesData={resourcesData}
            setResourcesData={setResourcesData}
            setOriginalResourcesData={setOriginalResourcesData}
            isLoading={isBackdropLoading}
            setIsLoading={setIsLoading}
            resource_type={"main-resource"}
            resourcePath="root"
          />

          <Accordion
            className="mt-8 heading-bold box-shadow"
            expanded={expandedAccordion === "run-parameters"}
            onChange={handleChangeAccordion("run-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Run Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <RunParameters
                runParameters={runParameters}
                setRunParameters={setRunParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                severityColumnNames={severityColumnNames}
                setSeverityColumnNames={setSeverityColumnNames}
                isReRun={isReRunData?.isReRun}
                isShow={{
                  isShowSkipValidation: false,
                  isShowExecuteResearchQueries: false,
                  use_secondary_merge_resources: false,
                }}
              />
            </AccordionDetails>
          </Accordion>

          <Accordion
            className="mt-8 heading-bold box-shadow"
            expanded={expandedAccordion === "result-storage-parameters"}
            onChange={handleChangeAccordion("result-storage-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Result Storage Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <ResultStorageParameters
                resultStorageParameters={resultStorageParameters}
                setResultStorageParameters={setResultStorageParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                isFromRule={false}
                isReRun={isReRunData?.isReRun}
              />
            </AccordionDetails>
          </Accordion>

          {resourcesData &&
            resourcesData.additional_properties &&
            resourcesData.additional_properties.inline_variables &&
            Object.keys(resourcesData.additional_properties.inline_variables)
              .length > 0 && (
              <Accordion
                className="mt-8 heading-bold box-shadow"
                expanded={expandedAccordion === "inline-variables"}
                onChange={handleChangeAccordion("inline-variables")}
              >
                <AccordionSummary
                  aria-controls="panel1d-content"
                  id="panel1d-header"
                  expandIcon={<ExpandMoreIcon />}
                >
                  Resource Variables
                </AccordionSummary>
                <AccordionDetails sx={{ paddingTop: "16px" }}>
                  <div className="text-box-card box-card-variables full-radius">
                    {!isEditVariables ? (
                      <div className="inline-variables-parent">
                        <InlineVariables
                          inlineVariables={
                            resourcesData?.additional_properties
                              ?.inline_variables || []
                          }
                          handleChangeVariable={handleChangeVariable}
                          isReadOnly={true}
                        />
                      </div>
                    ) : (
                      <div className="inline-variables-parent">
                        <InlineVariables
                          inlineVariables={
                            inlineVariables?.additional_properties
                              ?.inline_variables || []
                          }
                          handleChangeVariable={handleChangeVariable}
                          isReadOnly={false}
                        />
                      </div>
                    )}
                    {!isEditVariables ? (
                      <Grid
                        item
                        xs={12}
                        sm={12}
                        md
                        sx={{
                          justifyContent: "flex-end",
                          display: "flex",
                          marginTop: "8px",
                        }}
                      >
                        <button
                          className="btn-nostyle icon-btn-edit"
                          onClick={() => {
                            setIsEditVariables(true);
                            setInlineVariables(resourcesData);
                          }}
                        >
                          <IconBtnEditBase />
                        </button>
                      </Grid>
                    ) : (
                      <Grid
                        item
                        xs
                        sx={{
                          display: "flex",
                          alignItems: "flex-end",
                          justifyContent: "flex-end",
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            columnGap: "8px",
                            flexWrap: "wrap",
                            marginTop: "8px",
                          }}
                        >
                          <Button
                            color="secondary"
                            variant="contained"
                            onClick={() => {
                              setIsEditVariables(false);
                              setInlineVariables(resourcesData);
                            }}
                            className="btn-orange btn-dark"
                          >
                            Cancel
                          </Button>
                          <Button
                            color="secondary"
                            variant="contained"
                            onClick={() => {
                              setResourcesData(inlineVariables);
                              setGlobalVariables((prev: any) => ({
                                ...prev,
                                ...inlineVariables?.additional_properties
                                  ?.inline_variables,
                              }));
                              setIsEditVariables(false);
                            }}
                            className="btn-orange"
                            disabled={!hasChanges}
                          >
                            <SaveOutlinedIcon /> &nbsp; Save
                          </Button>
                        </Box>
                      </Grid>
                    )}
                  </div>
                </AccordionDetails>
              </Accordion>
            )}

          {resourcesData?.additional_properties?.filter_rules &&
            resourcesData?.additional_properties?.filter_rules.length > 0 && (
              <CustomAccordian
                expandId="filter-rules"
                title="Filter Rules"
                handleChangeAccordion={handleChangeAccordion}
                isEnabled={true}
                topMargin={8}
              >
                <FilterRules
                  formData={
                    !isEditFilters ? filterFormData : tempFilterFormData
                  }
                  isAddButtonHidden={true}
                  setFormData={setTempFilterFormData}
                  isViewOnly={!isEditFilters}
                  resourceColumnData={resourceColumnData}
                  checkFilterRuleValidation={checkFilterRuleValidation}
                  setAllVariablesList={setAllVariablesList}
                />

                <Grid
                  item
                  xs={12}
                  sm={12}
                  md
                  sx={{
                    justifyContent: "flex-end",
                    display: "flex",
                    marginTop: "8px",
                  }}
                >
                  {!isEditFilters && (
                    <button
                      className="btn-nostyle icon-btn-edit"
                      onClick={() => {
                        setIsEditFilters((prev) => !prev);
                      }}
                    >
                      <IconBtnEditBase />
                    </button>
                  )}

                  {isEditFilters && (
                    <Grid
                      item
                      xs
                      sx={{
                        display: "flex",
                        alignItems: "flex-end",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          columnGap: "8px",
                          flexWrap: "wrap",
                        }}
                      >
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => {
                            setIsEditFilters((prev: any) => !prev);
                            setTempFilterFormData(filterFormData);
                          }}
                          className="btn-orange btn-dark"
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          onClick={() => {
                            setIsEditFilters((prev) => !prev);
                            setFilterFormData(tempFilterFormData);
                            setIntialFilterFormData(tempFilterFormData);
                          }}
                          variant="contained"
                          color="secondary"
                          className="btn-orange"
                          title="Save Resource"
                          disabled={!hasFilterChanges}
                        >
                          <SaveOutlinedIcon /> &nbsp; Save
                        </Button>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </CustomAccordian>
            )}
          {resourceColumnData?.resource_column_properties
            ?.cross_field_validations &&
            resourceColumnData?.resource_column_properties
              ?.cross_field_validations.length > 0 && (
              <CustomAccordian
                expandId="adhoc-query"
                title="Adhoc Query"
                handleChangeAccordion={handleChangeAccordion}
                isEnabled={true}
              >
                <AddMultipleGridList
                  gridValue={
                    resourceColumnData?.resource_column_properties
                      ?.cross_field_validations
                  }
                  setGridValue={() => {}}
                  isViewOnly={true}
                  buttonName=" "
                  errors={{}}
                  setErrors={() => {}}
                />
              </CustomAccordian>
            )}
          {!isEditResource && isResourceEdit === "" && (
            <FlexBetween
              gap="3rem"
              sx={{ marginTop: 2, display: "flex", justifyContent: "flex-end" }}
            >
              <Button
                type="submit"
                className="btn-orange"
                color="secondary"
                variant="contained"
                onClick={handleValidateSubmit}
              >
                {isReRunData?.isReRun ? "Re-Validate" : "Validate"}
              </Button>
            </FlexBetween>
          )}
        </Box>
      </div>
    </>
  );
};

export default ValidateResource;
