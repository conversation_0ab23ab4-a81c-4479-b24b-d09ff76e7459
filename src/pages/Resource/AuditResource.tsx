import React, { useEffect, useState } from "react";
import { Box, Tooltip, Typography, Checkbox, IconButton } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import {
  formattedJson,
  getFormattedDateTime,
  removeCheckboxFromJson,
} from "../../services/utils";
import { useNavigate, useParams } from "react-router-dom";
import useFetchResourceBackups from "../../hooks/useFetchResourceBackups";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import InfoIcon from "@mui/icons-material/Info";
import AuditComponent from "../../components/AuditComponent";
import { IconCompareReportSvg, IconReportsSvg } from "../../common/utils/icons";
import useFetchResourceById from "../../hooks/useFetchResourceById";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const AuditResource = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { id }: any = useParams();

  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [auditSource, setAuditSource] = useState("");

  const [resourceBackupData] = useFetchResourceBackups({
    resourceId: id,
    setIsLoading,
    page,
    pSize,
  });

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      flex: 0,
      minWidth: 0,
      renderCell: (params: any) => null,
    },
    {
      field: "checkbox",
      headerName: "",
      width: 80,
      renderCell: (params) => (
        <Box sx={{ paddingTop: "4px" }}>
          <Checkbox
            checked={selectedRows.some((row) => row?.id === params?.row?.id)}
            onChange={() => toggleSelection(params.row)}
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
          />
        </Box>
      ),
    },
    {
      field: "domain_name",
      headerName: "Domain Name",
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        const handleClickDomain = () => {
          navigate(`/domain/view/${params.row.domain_id}`);
        };
        return (
          <LimitChractersWithTooltip
            value={params?.value}
            onClick={handleClickDomain}
          />
        );
      },
    },
    {
      field: "code",
      headerName: "Code",
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "created_date",
      headerName: "Last Modified Date",
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        return getFormattedDateTime(params?.value);
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "info",
      headerName: "Resource Info",
      minWidth: 180,
      flex: 1,
      renderCell: (params) => {
        const { checkbox, ...rowWithoutCheckbox } = params.row;
        return (
          <span className="position-relative">
            <Tooltip
              componentsProps={{
                tooltip: { className: "wide-tooltip w-380" },
              }}
              title={
                <pre
                  style={{
                    whiteSpace: "pre-wrap",
                    margin: 0,
                    maxHeight: "200px",
                    overflowY: "auto",
                  }}
                >
                  <React.Fragment>
                    <Typography color="inherit">Resource Info</Typography>

                    <Typography>
                      {formattedJson(JSON.stringify(rowWithoutCheckbox))}
                    </Typography>
                  </React.Fragment>
                </pre>
              }
            >
              <InfoIcon
                sx={{
                  position: "absolute",
                  top: "50%",
                  transform: "translateY(-50%)",
                  right: "-24px",
                  width: "16px",
                  cursor: "pointer",
                }}
              />
            </Tooltip>
          </span>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 130,
      renderCell: (params: any) => {
        return (
          <>
            <Tooltip title="Compare Current Defination" placement="top">
              <IconButton
                className="datagrid-action-btn"
                color="inherit"
                onClick={() =>
                  compareCurrentItem(params?.row, "currentCompare")
                }
              >
                <IconCompareReportSvg />
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  const [resourceData] = useFetchResourceById({
    currentResourceId: id,
    setIsLoading,
  });
  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: resourceData?.resource_name,
      id: resourceData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [resourceData]);

  const compareCurrentItem = async (data: any, source: any) => {
    const newData = await removeCheckboxFromJson(data);
    if (selectedRows.length > 0) {
      setSelectedRows([]);
    }
    setSelectedRows((prevSelectedRows) => [
      ...prevSelectedRows,
      newData,
      resourceData,
    ]);
    setAuditSource(source);
    setOpenDialog(true);
  };

  const toggleSelection = async (row: { id: any }) => {
    const newRow = await removeCheckboxFromJson(row);
    setSelectedRows((prevSelectedRows) => {
      if (
        prevSelectedRows.some((selectedRow) => selectedRow.id === newRow.id)
      ) {
        // Deselect row
        return prevSelectedRows.filter(
          (selectedRow) => selectedRow.id !== newRow.id
        );
      } else {
        // Select row
        if (prevSelectedRows.length < 2) {
          return [...prevSelectedRows, removeCheckboxFromJson(row)];
        } else {
          // Replace the second selected row with the new row
          return [prevSelectedRows[0], removeCheckboxFromJson(row)];
        }
      }
    });
  };

  return (
    <AuditComponent
      auditColumnData={columns}
      auditRowData={resourceBackupData || []}
      isLoading={isLoading}
      selectedRows={selectedRows}
      setSelectedRows={setSelectedRows}
      toggleSelection={toggleSelection}
      dataListTitle="Resource Modification History"
      page={page}
      setPage={setPage}
      pSize={pSize}
      setPSize={setPSize}
      openDialog={openDialog}
      setOpenDialog={setOpenDialog}
      auditSource={auditSource}
      setAuditSource={setAuditSource}
      singlePageMaxHeightDiff={333}
    />
  );
};

export default AuditResource;
