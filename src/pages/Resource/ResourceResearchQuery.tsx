import { useEffect, useMemo, useState } from "react";
import { useToast } from "../../services/utils";
import "../../styles/research-query.scss";
import { Button, Box, Grid, Select, MenuItem, IconButton } from "@mui/material";
import ResourceQueryTab from "../../components/ResearchQuery/ResourceQueryTab";
import ExternalQueryTab from "../../components/ResearchQuery/ExternalQueryTab";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { IResearchQuery } from "../../types/researchQuery";
import { toast } from "react-toastify";
import Loader from "../../components/Molecules/Loader/Loader";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useParams } from "react-router-dom";
import { resourceQueriesType } from "../../services/constants";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { IconDeleteBlueSvg, IconEditBase } from "../../common/utils/icons";

const researchQueries: IResearchQuery[] = [
  {
    id: 1,
    name: "SELECT FACTSET DATA",
    source: {
      type: "Resource",
      resource_id: "510",
      resource_code: "RES_510",
    },
    query: "SELECT * FROM <RES_510> WHERE [Portfolio Table] IS NOT NULL",
  },
  {
    id: 2,
    name: "SELECT AUGUST CRD POSITIONS",
    source: {
      type: "External",
      linked_service_id: 1,
      linked_service_code: "CRD_TABLES",
      connection_key_id: 2,
      connection_key_code: "CRD_IHUB",
    },
    query: "SELECT * FROM crd WHERE date >= '01/08/2024'",
  },
];

export default function ResourceResearchQuery() {
  const { id } = useParams();
  const { showToast } = useToast();
  const [activeTab, setActiveTab] = useState<string>("firstTab");
  const [queriesType, setQueryType] = useState("");
  const [isEditQuery, setIsEditQuery] = useState<any>(false);
  const [checked, setChecked] = useState(false);
  const [researchQueryData, setResearchQueryData] =
    useState<IResearchQuery[]>(researchQueries);
  const [columns, setColumns] = useState<any>({
    resourceColumns: [],
    missingColumns: [],
    mismatchedColumns: [],
  });

  const {
    resourceDetailData: resourcesData,
    setCurrentResourceId,
    isLoading,
    setQueryBuilderTempValue,
    linkedServicesData,
    fetchedConnectionKeys,
  } = useRuleResourceContext();

  useEffect(() => {
    if (id) setCurrentResourceId(parseInt(id));
  }, [id]);

  const handleTransition = (isChecked: boolean) => {
    setChecked(isChecked);
  };

  const handleChangeTab = (event: any, newValue: any) => {
    setActiveTab(newValue);
    handleTransition(false);
  };

  const handleSaveResearchQuery = () => {
    const payload = {
      resource_id: resourcesData?.id,
      resource_code: resourcesData?.code,
      research_queries: researchQueryData,
    };
    showToast(`Research query updated successfully`, "success");
  };

  const getLinkedServiceCodebyId = (id: number | string | null | undefined) => {
    const linkedData = linkedServicesData?.find((item: any) => item.id === id);
    return linkedData ? linkedData.name : null;
  };
  const getConnectionKeybyId = (id: number | string | null | undefined) => {
    const connectionData = fetchedConnectionKeys?.find(
      (item: any) => item.id === id
    );
    return connectionData ? connectionData?.name : null;
  };
  const [editFormData, setEditFormData] = useState();

  const researchColumns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      renderCell: (params: any) => <></>,
    },

    {
      field: "name",
      headerName: "Query Name",
      minWidth: 200,
      flex: 1,
      renderCell: (params: any) => <span>{params.value}</span>,
    },
    {
      field: "source",
      headerName: "Query Type",
      minWidth: 140,
      renderCell: (params: any) => <span>{params.value.type}</span>,
    },
    {
      field: "linked_service_id",
      headerName: "Linked Service",
      minWidth: 140,
      renderCell: (params: any) => (
        <span>
          {getLinkedServiceCodebyId(params?.row?.source?.linked_service_id) ??
            "N/A"}
        </span>
      ),
    },
    {
      field: "connection_key_id",
      headerName: "Connection Key",
      minWidth: 140,
      renderCell: (params: any) => (
        <span>
          {getConnectionKeybyId(params?.row?.source?.connection_key_id) ??
            "N/A"}
        </span>
      ),
    },
    {
      field: "query",
      headerName: "Query",
      minWidth: 120,
      flex: 1,
      groupable: false,
      renderCell: (params: any) => <span>{params.value}</span>,
    },

    {
      field: "action",
      headerName: "Action",
      width: 180,
      align: "center",
      headerAlign: "center",
      groupable: false,
      renderCell: (params: any) => {
        if (params.rowNode.type === "group") {
          return null;
        }
        const handleEditQuery = () => {
          const formData: any = researchQueryData.find(
            (query) => query.id === params.row.id
          );
          const updatedFormData: any = {
            ...formData,
          };
          setEditFormData(updatedFormData);
          setQueryBuilderTempValue(formData?.query);
          setIsEditQuery(true);
          handleTransition(true);
          setQueryType(params?.row?.source?.type);
        };
        const handleDelete = async () => {
          setResearchQueryData((prevData) => {
            const updatedQueryData = prevData.filter(
              (query) => query.id !== params.row.id
            );
            return updatedQueryData;
          });
        };

        return (
          <>
            <IconButton onClick={handleEditQuery}>
              <IconEditBase />
            </IconButton>
            <IconButton onClick={handleDelete}>
              <IconDeleteBlueSvg />
            </IconButton>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Loader isLoading={isLoading} />

      <Box className="dashboard-title-group">
        <Grid container rowSpacing={2.5} columnSpacing={4}>
          <Grid item>
            <label className="label-text pt-11">
              Select Query Type<span className="required-asterisk">*</span>
            </label>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <Select
              MenuProps={{
                disableScrollLock: true,
              }}
              title="Aggregation type"
              value={queriesType}
              name="aggregation_type"
              style={{ width: "100%", height: 35 }}
              onChange={(e) => {
                setQueryType(e.target.value);
              }}
              className={`form-control-autocomplete form-control-autocomplete-1`}
            >
              {resourceQueriesType.map((type: string) => (
                <MenuItem key={type} value={type}>
                  <span
                    style={{ textTransform: "capitalize" }}
                  >{`${type} Queries`}</span>
                </MenuItem>
              ))}
            </Select>
          </Grid>
          <Grid item xs sx={{ justifyContent: "flex-end", display: "flex" }}>
            <Button
              variant="contained"
              color="secondary"
              className="btn-orange"
              onClick={handleSaveResearchQuery}
            >
              <SaveOutlinedIcon /> &nbsp; Save Research Query
            </Button>
          </Grid>
        </Grid>
      </Box>

      <Box>
        {queriesType === "Resource" && (
          <ResourceQueryTab
            resourcesData={resourcesData}
            researchQueryData={researchQueryData}
            setResearchQueryData={setResearchQueryData}
            handleTransition={handleTransition}
            checked={checked}
            renderFrom="resource"
            columns={columns}
            setColumns={setColumns}
            isEditQuery={isEditQuery}
            setIsEditQuery={setIsEditQuery}
            editFormData={editFormData}
            setEditFormData={setEditFormData}
            setQueryType={setQueryType}
          />
        )}
        {queriesType === "External" && (
          <ExternalQueryTab
            researchQueryData={researchQueryData}
            setResearchQueryData={setResearchQueryData}
            handleTransition={handleTransition}
            checked={checked}
            columns={columns}
            setColumns={setColumns}
            isEditQuery={isEditQuery}
            setIsEditQuery={setIsEditQuery}
            editFormData={editFormData}
            setEditFormData={setEditFormData}
            setQueryType={setQueryType}
          />
        )}
      </Box>
      <Box>
        <DataTable
          dataRows={researchQueryData}
          dataColumns={researchColumns}
          dataListTitle="Query List"
          className="dataTable hide-progress-icon no-buttons"
          paginationMode="client"
          pageSizeOptions={[25]}
          handleTransition={handleTransition}
          isExportButtonDisabled={true}
        />
      </Box>
    </>
  );
}
