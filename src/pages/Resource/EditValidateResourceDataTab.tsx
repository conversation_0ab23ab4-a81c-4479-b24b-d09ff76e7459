import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Box, Button, Checkbox, Grid, TextField } from "@mui/material";
import LinkedServices from "../../components/Resource/LinkedServices";
import { useResourceContext } from "../../contexts/ResourceContext";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import ConfirmationDialog from "../../components/Dialogs/ConfirmationDialog";
import {
  aggregatedValidateSchema,
  basicAuthApiDefinitionSchema,
  blobLinkedServiceSchema,
  keyAuthApiDefinitionSchema,
  localLinkedServiceSchema,
  oAuthApiDefinitionSchema,
  sftpLinkedServiceSchema,
  sqlLinkedServiceSchema,
  tokenAuthApiDefinitionSchema,
  noAuthApiDefinitionSchema,
} from "../../schemas";
import { updateResource } from "../../services/resourcesService";
import { apiType, sqlDatabaseType } from "../../services/constants";
import { removeUndefinedProperties } from "../../services/utils";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useToast } from "../../services/utils";

interface EditValidateResourceDataTabProps {
  resourceData: any;
  setResourcesData: Dispatch<SetStateAction<any>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  linkedServicesData: any;
  connectionKeysData: any;
  setSelectLinkedServiceId: Dispatch<SetStateAction<any>>;
  resourcePath: string;
}

const EditValidateResourceDataTab = ({
  resourceData,
  setIsLoading,
  setResourcesData,
  linkedServicesData,
  connectionKeysData,
  setSelectLinkedServiceId,
  resourcePath,
}: EditValidateResourceDataTabProps) => {
  const { setIsResourceEdit } = useRuleResourceContext();
  const { showToast } = useToast();
  const [formData, setFormData] = useState<any>({
    aggregation_type: "flat",
  });
  const [initialFormData, setInitialFormData] = useState<any>({
    aggregation_type: "flat",
  });
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [resourceId, setResourceId] = useState<any>(0);
  const [openResourceConfirmation, setOpenResourceConfirmation] =
    useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [queryParams, setQueryParams] = useState([]);
  const [urlParams, setUrlParams] = useState([]);
  const [type, setType] = useState("");
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [cancelUploadFile, setCancelUploadFile] = useState<any>(false);
  const [isLoadingFile, setIsLoadingFile] = useState<boolean>(false);
  const [comment, setComment] = useState("");

  const { baseResourceColumns, resourceColumnData } = useResourceContext();
  const { globalVariables, setGlobalVariables } = useRuleResourceContext();
  useEffect(() => {
    if (resourceData) {
      setIsLoading(true);
      setResourceId(
        resourceData?.additional_properties?.aggregation_properties
          ?.base_resource_id ?? 0
      );
      const newFormData = {
        ...resourceData,
        resource_definition:
          resourceData?.additional_properties?.resource_definition,
        base_resource_id:
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_id,
        base_resource_code:
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_code,
        resource_column_details_code:
          resourceData?.additional_properties?.resource_column_details_code,
        base_resource_validation:
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_validation,
        aggregation_query:
          resourceData?.additional_properties?.aggregation_properties
            ?.aggregation_query,
        base_resource_columns:
          resourceData?.additional_properties?.aggregation_properties
            ?.base_resource_columns,
        isUpdateResource: false,
      };
      const linkedData = linkedServicesData?.find(
        (item: any) => item.id === resourceData.linked_service_id
      );
      newFormData.linked_service = linkedData;
      newFormData.additional_resource_data =
        resourceData?.additional_properties?.additional_resource_data || [];
      setFormData(newFormData);
      setInitialFormData(JSON.parse(JSON.stringify(newFormData)));
      setQueryParams(
        resourceData?.additional_properties?.resource_definition?.api_definition
          ?.query_params || {}
      );
      setUrlParams(
        resourceData?.additional_properties?.resource_definition?.api_definition
          ?.url_params || {}
      );
      if (connectionKeysData) {
        //connectionKeyDetail for type api only
        const connectionKeyDetail = connectionKeysData?.find(
          (option: { id: any }) =>
            option.id ===
            resourceData?.additional_properties?.resource_definition
              ?.api_definition?.connection_key
        );
        setType(connectionKeyDetail?.type || " ");
      }
      setGlobalVariables(
        resourceData?.additional_properties?.inline_variables ?? {}
      );
      setAllVariablesList((prev: any) => ({
        ...prev,
        resource: {
          ...(resourceData?.additional_properties?.inline_variables ?? {}),
        },
        resourceColumn: {
          ...(resourceData?.additional_properties?.inline_variables ?? {}),
        },
      }));
      setIsLoading(false);
    }
  }, [resourceData, linkedServicesData]);

  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);
    setHasChanges(formChanged);
  }, [initialFormData, formData]);
  const handleCancelResource = () => {
    setOpenResourceConfirmation(false);
    setComment("");
  };
  const handleCommentChange = (e: any) => {
    setComment(e.target.value);
  };
  const updateResourceAtPath = (path: string, updatedResource: any) => {
    setResourcesData((prevData: any) => {
      if (path === "root") {
        return updatedResource;
      }

      const pathParts = path.split(".");
      const newData = JSON.parse(JSON.stringify(prevData)); // Deep clone
      let current = newData;

      // Skip "root" keyword
      let i = pathParts[0] === "root" ? 1 : 0;

      // Traverse to the second-last part of the path
      for (; i < pathParts.length - 1; i++) {
        const part = pathParts[i];

        // Handle array index if needed
        if (Array.isArray(current)) {
          current = current[parseInt(part)];
        } else {
          if (!(part in current)) {
            current[part] = {}; // Create missing objects on the fly
          }
          current = current[part];
        }
      }

      const lastPart = pathParts[pathParts.length - 1];

      if (typeof current[lastPart] === "object" && current[lastPart] !== null) {
        Object.assign(current[lastPart], updatedResource);
      } else {
        current[lastPart] = updatedResource;
      }

      return newData;
    });
  };

  const handleSaveResource = async () => {
    setOpenResourceConfirmation(false);
    onSaveResource();
  };
  const onSaveResource = () => {
    const newResourceData: any = {};
    let uniqueBaseResourceColumns;
    if (baseResourceColumns && baseResourceColumns.length > 0) {
      const uniqueColumns = new Set(
        baseResourceColumns.map((column: { label: any }) => column.label)
      );
      uniqueBaseResourceColumns = Array.from(uniqueColumns);
    } else {
      uniqueBaseResourceColumns = null;
    }

    // Get the linked service type
    const linkedServiceType = formData?.linked_service?.sub_type;

    Object.assign(newResourceData, {
      ...formData,
      linked_service_id: formData?.linked_service?.id,
      linked_service_code: formData.linked_service?.code,
      is_active: true,
      file_processing_id: formData?.file_processing_id,
      file_processing_code: formData?.file_processing_code,
      additional_properties: {
        ...formData.additional_properties,
        resource_definition: {
          ...formData?.resource_definition,
          // Include excel_sheet_name for file-based linked services
          ...(linkedServiceType && {
            excel_sheet_name:
              formData?.resource_definition?.excel_sheet_name || null,
          }),
          api_definition: {
            ...formData?.resource_definition?.api_definition,
            query_params: queryParams,
            url_params: urlParams,
            body:
              formData?.resource_definition?.api_definition?.method !== "get"
                ? formData?.resource_definition?.api_definition?.body
                : null,
          },
        },
        resource_column_details_id:
          parseInt(formData.additional_properties.resource_column_details_id) ||
          null,
        additional_resource_data: formData?.additional_resource_data || null,
        aggregation_properties:
          formData?.aggregation_type === "aggregated"
            ? {
                ...formData.additional_properties.aggregation_properties,
                base_resource_id: formData.base_resource_id || null,
                base_resource_code: formData.base_resource_code || null,
                aggregation_query: formData.aggregation_query || null,
                base_resource_columns: uniqueBaseResourceColumns,
                base_resource_validation:
                  formData.base_resource_validation || false,
              }
            : null,
        inline_variables: globalVariables,
        resource_column_details_code:
          formData?.resource_column_details_code || null,
      },
    });
    [
      "base_resource_id",
      "base_resource_code",
      "aggregation_query",
      "base_resource_columns",
      "base_resource_validation",
    ].forEach((e) => delete newResourceData[e]);

    updateResourceAtPath(resourcePath, newResourceData);

    setIsResourceEdit("");
    if (formData?.isUpdateResource) {
      updateResourceData(newResourceData);
    }
  };
  const saveResource = async () => {
    try {
      const type = formData?.linked_service?.sub_type;
      let uniqueBaseResourceColumns;
      if (baseResourceColumns && baseResourceColumns.length > 0) {
        const uniqueColumns = new Set(
          baseResourceColumns.map((column: { label: any }) => column.label)
        );
        uniqueBaseResourceColumns = Array.from(uniqueColumns);
      } else {
        uniqueBaseResourceColumns = null;
      }
      if (formData.aggregation_type === "flat") {
        if (type === "blob") {
          await blobLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "sftp") {
          await sftpLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "local") {
          await localLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (sqlDatabaseType.includes(type)) {
          await sqlLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "basic_auth_api") {
          await basicAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "token_auth_api") {
          await tokenAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "key_auth_api") {
          await keyAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "oauth_api") {
          await oAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "no_auth_api") {
          await noAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        }
      } else {
        const updatedFormData = {
          ...formData,
          base_resource_columns: uniqueBaseResourceColumns,
        };
        await aggregatedValidateSchema.validate(updatedFormData, {
          abortEarly: false,
        });
      }
      if (formData?.isUpdateResource) {
        setOpenResourceConfirmation(true);
      } else {
        onSaveResource();
        setIsResourceEdit("");
      }
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(
              /^resource_definition(\.api_definition)?\./,
              ""
            );
            newErrors[fieldName] = error.message;
          }
        );
      }
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const updateResourceData = (resourceData: any) => {
    const resourceId = resourceData?.id;
    let definitionType =
      resourceData?.additional_properties?.resource_definition?.type +
      "_definition";
    if (
      sqlDatabaseType.includes(
        resourceData?.additional_properties?.resource_definition?.type
      )
    ) {
      definitionType = "sql_definition";
    }

    // Get the linked service type
    const linkedServiceType = formData?.linked_service?.sub_type;

    const reqBody: any = {
      resource_name: resourceData.resource_name,
      resource_type: resourceData.resource_type,
      resource_prefix: resourceData.resource_prefix,
      code: resourceData.code,
      aggregation_type: resourceData.aggregation_type,
      domain_id: resourceData.domain_id,
      domain_name: resourceData.domain_name,
      domain_code: resourceData.domain_code,
      is_active: resourceData.is_active,
      linked_service_id: resourceData.linked_service_id,
      linked_service_code: resourceData.linked_service_code,
      current_url: resourceData.current_url,
      file_processing_id: resourceData.file_processing_id,
      file_processing_code: resourceData.file_processing_code,
      comment: comment,
      additional_properties: {
        resource_definition:
          resourceData.aggregation_type === "flat"
            ? {
                type: resourceData?.additional_properties?.resource_definition
                  ?.type,
                [definitionType]: {
                  resource_path:
                    resourceData?.additional_properties?.resource_definition
                      ?.resource_path,
                  file_name:
                    resourceData?.additional_properties?.resource_definition
                      ?.file_name,
                  column_delimiter:
                    resourceData?.additional_properties?.resource_definition
                      ?.column_delimiter,
                  excel_sheet_name:
                    resourceData?.additional_properties?.resource_definition
                      ?.excel_sheet_name,
                  connection_key:
                    resourceData?.additional_properties?.resource_definition
                      ?.connection_key,
                  connection_key_code: connectionKeysData?.find(
                    (option: { id: any }) =>
                      option.id ===
                      formData?.resource_definition?.connection_key
                  )?.code,
                  container_name:
                    resourceData?.additional_properties?.resource_definition
                      ?.container_name,
                  database_type:
                    resourceData?.additional_properties?.resource_definition
                      ?.database_type,
                  connection_string:
                    resourceData?.additional_properties?.resource_definition
                      ?.connection_string,
                  sql_query:
                    resourceData?.additional_properties?.resource_definition
                      ?.sql_query,
                  remote_directory:
                    resourceData?.additional_properties?.resource_definition
                      ?.remote_directory,
                  use_multi_thread_reader:
                    resourceData?.additional_properties?.resource_definition
                      ?.sql_definition?.use_multi_thread_reader || false,
                  column_name_to_partition_on_sql_query:
                    resourceData?.additional_properties?.resource_definition
                      ?.sql_definition?.column_name_to_partition_on_sql_query ||
                    "",
                },
                // Include excel_sheet_name for file-based linked services
                ...(linkedServiceType && {
                  excel_sheet_name:
                    resourceData?.additional_properties?.resource_definition
                      ?.excel_sheet_name || null,
                }),
                api_definition: apiType.includes(
                  resourceData?.additional_properties?.resource_definition?.type
                )
                  ? {
                      ...resourceData?.additional_properties
                        ?.resource_definition?.api_definition,
                      query_params: queryParams,
                      url_params: urlParams,
                      body:
                        resourceData?.additional_properties?.resource_definition
                          ?.api_definition?.method !== "get"
                          ? resourceData?.additional_properties
                              ?.resource_definition?.api_definition?.body
                          : null,
                    }
                  : null,
              }
            : null,
        aggregation_properties:
          resourceData.aggregation_type === "aggregated"
            ? resourceData?.additional_properties?.aggregation_properties
            : null,
        resource_column_details_id:
          resourceData?.additional_properties?.resource_column_details_id,
        additional_resource_data: resourceData?.additional_resource_data,
        filter_rules: resourceData?.additional_properties?.filter_rules,
        inline_variables: resourceData?.additional_properties?.inline_variables,
        resource_column_details_code:
          formData?.resource_column_details_code || null,
      },
    };
    if (formData?.linked_service?.type === "sql") {
      const keysToRemove = [
        "file_processing_code",
        "file_processing_id",
        "column_delimiter",
        "container_name",
        "file_name",
        "resource_path",
        "database_type",
        "connection_string",
      ];
      keysToRemove.forEach((key) => {
        if (key in reqBody) {
          delete reqBody[key];
        } else if (
          key in
          reqBody.additional_properties?.resource_definition?.sql_definition
        ) {
          delete reqBody.additional_properties.resource_definition
            .sql_definition[key];
        }
      });
    }
    const resourceObject = removeUndefinedProperties(reqBody);
    updateResource({ currentResourceId: resourceId, payload: resourceObject })
      .then((response: any) => {
        if (response) showToast("Resource updated successfully!", "success");
      })
      .catch((error: any) => {
        showToast(`Cannot update resource`, "error");
      });
    setComment("");
  };

  return (
    <Box>
      <Grid
        container
        rowSpacing={1.5}
        columnSpacing={2.5}
        sx={{ marginBottom: "16px" }}
      >
        <LinkedServices
          formData={formData}
          setFormData={setFormData}
          connectionKeysData={connectionKeysData}
          linkedServicesData={linkedServicesData}
          setResourceId={setResourceId}
          resourceId={resourceId}
          setIsLoading={setIsLoading}
          setSelectLinkedServiceId={setSelectLinkedServiceId}
          errors={errors}
          setErrors={setErrors}
          queryParams={queryParams}
          setQueryParams={setQueryParams}
          urlParams={urlParams}
          setUrlParams={setUrlParams}
          type={type}
          setType={setType}
          setAllVariablesList={setAllVariablesList}
          allVariablesList={allVariablesList}
          resourceColumnData={resourceColumnData}
          setCancelUploadFile={setCancelUploadFile}
          cancelUploadFile={cancelUploadFile}
          setIsLoadingFile={setIsLoadingFile}
          isLoadingFile={isLoadingFile}
        />
        {/* Excel Sheet Name input for file-based resources */}
        {/* {(formData?.linked_service?.sub_type === "local" ||
          formData?.linked_service?.sub_type === "blob" ||
          formData?.linked_service?.sub_type === "sftp") &&
          formData?.resource_definition?.file_name &&
          /\.(xlsx|xls)$/i.test(formData?.resource_definition?.file_name) && (
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <TextField
                type="text"
                title="excel_sheet_name"
                name="excel_sheet_name"
                fullWidth
                label={<span>Excel Sheet Name</span>}
                variant="outlined"
                className="form-control-autocomplete"
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    resource_definition: {
                      ...formData.resource_definition,
                      [e.target.name]: e.target.value,
                    },
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
                value={formData?.resource_definition?.excel_sheet_name || ""}
              />
            </Grid>
          )} */}
        {formData?.linked_service?.type === "sql" && (
          <>
            <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
              <label className="base-resource-checkbox m-0">
                <Checkbox
                  checked={
                    formData?.resource_definition?.sql_definition
                      ?.use_multi_thread_reader || false
                  }
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        sql_definition: {
                          ...formData?.resource_definition?.sql_definition,
                          use_multi_thread_reader: event.target.checked,
                          column_name_to_partition_on_sql_query: event.target
                            .checked
                            ? formData?.resource_definition?.sql_definition
                                ?.column_name_to_partition_on_sql_query || ""
                            : "",
                        },
                      },
                    });
                  }}
                  sx={{
                    "&.Mui-checked": {
                      color: "#FFA500",
                    },
                  }}
                />
                <span>Use Multi Thread Reader</span>
              </label>
            </Grid>

            {formData?.resource_definition?.sql_definition
              ?.use_multi_thread_reader && (
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <TextField
                  type="text"
                  title="Column Name to Partition On"
                  name="column_name_to_partition_on_sql_query"
                  fullWidth
                  label={
                    <span>
                      Column Name to Partition On
                      <span className="required-asterisk">*</span>
                    </span>
                  }
                  variant="outlined"
                  className={`form-control-autocomplete ${
                    errors?.column_name_to_partition_on_sql_query
                      ? "has-error"
                      : ""
                  }`}
                  value={
                    formData?.resource_definition?.sql_definition
                      ?.column_name_to_partition_on_sql_query || ""
                  }
                  onChange={(e) => {
                    setFormData({
                      ...formData,
                      resource_definition: {
                        ...formData.resource_definition,
                        sql_definition: {
                          ...formData?.resource_definition?.sql_definition,
                          column_name_to_partition_on_sql_query: e.target.value,
                        },
                      },
                    });
                  }}
                  error={!!errors?.column_name_to_partition_on_sql_query}
                  helperText={
                    errors?.column_name_to_partition_on_sql_query || ""
                  }
                />
              </Grid>
            )}
          </>
        )}
        <Grid
          item
          xs
          sx={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "flex-end",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              columnGap: "8px",
              flexWrap: "wrap",
              rowGap: "16px",
            }}
          >
            <label className="base-resource-checkbox m-0">
              <Checkbox
                checked={formData.isUpdateResource || false}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  setFormData({
                    ...formData,
                    isUpdateResource: event.target.checked,
                  });
                }}
                sx={{
                  "&.Mui-checked": {
                    color: "#FFA500",
                  },
                }}
              />
              <span>Update Resource</span>
            </label>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                columnGap: "8px",
                flexWrap: "wrap",
              }}
            >
              <Button
                color="secondary"
                variant="contained"
                onClick={() => {
                  // setIsEditResource(false);
                  setIsResourceEdit("");
                  if (cancelUploadFile) {
                    cancelUploadFile.cancel();
                  }
                }}
                className="btn-orange btn-dark"
              >
                Cancel
              </Button>
              <Button
                color="secondary"
                variant="contained"
                onClick={() => saveResource()}
                className="btn-orange"
                disabled={isLoadingFile || !hasChanges}
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </Box>
          </Box>
        </Grid>
      </Grid>
      <ConfirmationDialog
        title={"Confirm updating Resource"}
        dialogContent={
          <>
            <p className="mt-0">
              Confirm changes before saving them for the validation step.
              {formData.isUpdateResource
                ? "This action will also update the resource"
                : ""}
            </p>
            <div>
              <p className="m-0 mb-2">
                Add a note before updating the Resource
              </p>
              <textarea
                value={comment}
                className={`form-control-1 max-60`}
                onChange={(e) => handleCommentChange(e)}
              />
            </div>
          </>
        }
        handleCancel={handleCancelResource}
        openConfirmation={openResourceConfirmation}
        handleConfirm={handleSaveResource}
      />
    </Box>
  );
};

export default EditValidateResourceDataTab;
