import React, { <PERSON><PERSON>atch, SetStateAction, useEffect, useState } from "react";
import { Box, Button, Checkbox, Grid, TextField } from "@mui/material";
import LinkedServices from "../../../components/Resource/LinkedServices";
import { useResourceContext } from "../../../contexts/ResourceContext";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import ConfirmationDialog from "../../../components/Dialogs/ConfirmationDialog";
import {
  aggregatedValidateSchema,
  reRunLocalLinkedServiceSchema,
  reRunBlobLinkedServiceSchema,
  reRunSftpLinkedServiceSchema,
  reRunSqlLinkedServiceSchema,
  reRunBasicAuthApiDefinitionSchema,
  reRunKeyAuthApiDefinitionSchema,
  reRunTokenAuthApiDefinitionSchema,
  reRunOAuthApiDefinitionSchema,
  reRunNoAuthApiDefinitionSchema,
} from "../../../schemas";
import { updateResource } from "../../../services/resourcesService";
import { apiType, sqlDatabaseType } from "../../../services/constants";
import { removeUndefinedProperties } from "../../../services/utils";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import { useToast } from "../../../services/utils";
import ReRunLinkedServices from "../../../components/Resource/ReRun/ReRunLinkedServices";
import useFetchResourceByResourceId from "../../../hooks/useFetchResourceByResourceId";

interface EditValidateResourceDataTabProps {
  resourceData: any;
  setResourcesData: Dispatch<SetStateAction<any>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  linkedServicesData: any;
  connectionKeysData: any;
  setSelectLinkedServiceId: Dispatch<SetStateAction<any>>;
}

const EditReRunValidateResourceDataTab = ({
  resourceData,
  setIsLoading,
  setResourcesData,
  linkedServicesData,
  connectionKeysData,
  setSelectLinkedServiceId,
}: EditValidateResourceDataTabProps) => {
  const { setIsResourceEdit } = useRuleResourceContext();
  const { showToast } = useToast();
  const [formData, setFormData] = useState<any>({
    aggregation_type: "flat",
  });
  const [initialFormData, setInitialFormData] = useState<any>({
    aggregation_type: "flat",
  });
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [resourceId, setResourceId] = useState<any>(0);
  const [openResourceConfirmation, setOpenResourceConfirmation] =
    useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [queryParams, setQueryParams] = useState([]);
  const [urlParams, setUrlParams] = useState([]);
  const [type, setType] = useState("");
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [cancelUploadFile, setCancelUploadFile] = useState<any>(false);
  const [isLoadingFile, setIsLoadingFile] = useState<boolean>(false);
  const [comment, setComment] = useState("");
  const [currentResourceId, setCurrentResourceId] = useState(null);

  const { baseResourceColumns, resourceColumnData } = useResourceContext();
  const { globalVariables, setGlobalVariables } = useRuleResourceContext();
  const [resourceDetailData] = useFetchResourceByResourceId({
    currentResourceId,
    setIsLoading,
  });

  useEffect(() => {
    if (resourceData) {
      setIsLoading(true);
      setResourceId(
        resourceData?.aggregation_properties?.base_resource_id ?? 0
      );
      const newFormData = {
        ...resourceData,
        file_processing_id: resourceData.file_processing_id,
        file_processing_code: resourceData.file_processing_code,
        aggregation_type: resourceData?.aggregation_properties?.base_resource_id
          ? "aggregated"
          : "flat",
        base_resource_id:
          resourceData?.aggregation_properties?.base_resource_id,
        base_resource_code:
          resourceData?.aggregation_properties?.base_resource_code,
        resource_column_details_code:
          resourceData?.resource_column_details_code,
        base_resource_validation:
          resourceData?.aggregation_properties?.base_resource_validation,
        aggregation_query:
          resourceData?.aggregation_properties?.aggregation_query,
        base_resource_columns:
          resourceData?.aggregation_properties?.base_resource_columns,
        isUpdateResource: false,
      };
      const linkedData = linkedServicesData?.find(
        (item: any) => item.id === resourceData.linked_service_id
      );
      newFormData.linked_service = linkedData;
      // newFormData.additional_base_resource_data =
      //   resourceData?.additional_base_resource_data !== null
      //     ? resourceData?.additional_base_resource_data?.length > 0
      //       ? JSON.stringify(
      //           resourceData?.additional_base_resource_data
      //         )
      //       : []
      //     : null;
      // newFormData.additional_resource_data =
      //   resourceData?.additional_resource_data || [];
      setFormData(newFormData);
      setInitialFormData(JSON.parse(JSON.stringify(newFormData)));
      setQueryParams(resourceData?.api_definition?.query_params || {});
      setUrlParams(resourceData?.api_definition?.url_params || {});
      if (connectionKeysData) {
        //connectionKeyDetail for type api only
        const connectionKeyDetail = connectionKeysData?.find(
          (option: { id: any }) =>
            option.id === resourceData?.api_definition?.connection_key
        );
        setType(connectionKeyDetail?.type || " ");
      }
      setGlobalVariables(resourceData?.inline_variables ?? {});
      setAllVariablesList((prev: any) => ({
        ...prev,
        resource: {
          ...(resourceData?.inline_variables ?? {}),
        },
        resourceColumn: {
          ...(resourceData?.inline_variables ?? {}),
        },
      }));
      setIsLoading(false);
    }
  }, [resourceData, linkedServicesData]);

  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);
    setHasChanges(formChanged);
  }, [initialFormData, formData]);
  const handleCancelResource = () => {
    setOpenResourceConfirmation(false);
    setComment("");
  };
  const handleCommentChange = (e: any) => {
    setComment(e.target.value);
  };
  const handleSaveResource = async () => {
    setOpenResourceConfirmation(false);
    onSaveResource();
  };
  const onSaveResource = () => {
    const newResourceData: any = {};
    let uniqueBaseResourceColumns;
    if (baseResourceColumns && baseResourceColumns.length > 0) {
      const uniqueColumns = new Set(
        baseResourceColumns.map((column: { label: any }) => column.label)
      );
      uniqueBaseResourceColumns = Array.from(uniqueColumns);
    } else {
      uniqueBaseResourceColumns = null;
    }
    Object.assign(newResourceData, {
      ...formData,
      linked_service_id: formData?.linked_service?.id,
      linked_service_code: formData.linked_service?.code,
      is_active: true,
      file_processing_id: formData?.file_processing_id,
      file_processing_code: formData?.file_processing_code,
      api_definition: {
        ...formData?.api_definition,
        query_params: queryParams,
        url_params: urlParams,
        body:
          formData?.api_definition?.method !== "get"
            ? formData?.api_definition?.body
            : null,
      },
      resource_column_details_id:
        parseInt(formData.resource_column_details_id) || null,
      additional_base_resource_data: formData?.additional_base_resource_data,
      // additional_base_resource_data:
      //   formData?.additional_base_resource_data !== null
      //     ? formData?.additional_base_resource_data?.length > 0
      //       ? JSON.parse(formData?.additional_base_resource_data)
      //       : []
      //     : null,
      additional_resource_data: formData?.additional_resource_data || null,
      aggregation_properties: {
        base_resource_id: formData.base_resource_id || null,
        base_resource_code: formData.base_resource_code || null,
        aggregation_query: formData.aggregation_query || null,
        base_resource_columns: uniqueBaseResourceColumns,
        base_resource_validation: formData.base_resource_validation || false,
      },
      inline_variables: globalVariables,
      resource_column_details_code:
        formData?.resource_column_details_code || null,
    });
    setResourcesData(newResourceData);
    setIsResourceEdit("");
    if (formData?.isUpdateResource) {
      updateResourceData(newResourceData);
    }
  };
  const saveResource = async () => {
    try {
      const type = formData?.linked_service?.sub_type;
      let uniqueBaseResourceColumns;
      if (baseResourceColumns && baseResourceColumns.length > 0) {
        const uniqueColumns = new Set(
          baseResourceColumns.map((column: { label: any }) => column.label)
        );
        uniqueBaseResourceColumns = Array.from(uniqueColumns);
      } else {
        uniqueBaseResourceColumns = null;
      }
      if (formData.aggregation_type === "flat") {
        if (type === "blob") {
          await reRunBlobLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "sftp") {
          await reRunSftpLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "local") {
          await reRunLocalLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (sqlDatabaseType.includes(type)) {
          await reRunSqlLinkedServiceSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "basic_auth_api") {
          await reRunBasicAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "token_auth_api") {
          await reRunTokenAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "key_auth_api") {
          await reRunKeyAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "oauth_api") {
          await reRunOAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        } else if (type === "no_auth_api") {
          await reRunNoAuthApiDefinitionSchema.validate(formData, {
            abortEarly: false,
          });
        }
      } else {
        const updatedFormData = {
          ...formData,
          base_resource_columns: uniqueBaseResourceColumns,
        };
        await aggregatedValidateSchema.validate(updatedFormData, {
          abortEarly: false,
        });
      }
      if (formData?.isUpdateResource) {
        setOpenResourceConfirmation(true);
      } else {
        console.log("here");

        onSaveResource();
        setIsResourceEdit("");
      }
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(
              /^resource_definition(\.api_definition)?\./,
              ""
            );
            newErrors[fieldName] = error.message;
          }
        );
      }
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const updateResourceData = (resourceData: any) => {
    const resourceId = resourceData?.resource_id;
    let definitionType = resourceData?.type + "_definition";
    if (sqlDatabaseType.includes(resourceData?.type)) {
      definitionType = "sql_definition";
    }
    const reqBody: any = {
      resource_name: resourceData.resource_name,
      resource_type: resourceData.resource_type,
      resource_prefix: resourceData.resource_prefix,
      code: resourceDetailData?.code,
      aggregation_type: resourceData.aggregation_type,
      domain_id: resourceDetailData?.domain_id,
      domain_name: resourceDetailData?.domain_name,
      domain_code: resourceDetailData?.domain_code,
      is_active: resourceData.is_active,
      linked_service_id: resourceData.linked_service_id,
      linked_service_code: resourceData.linked_service_code,
      current_url: resourceData.current_url,
      file_processing_id: resourceData.file_processing_id,
      file_processing_code: resourceData.file_processing_code,
      comment: comment,
      additional_properties: {
        resource_definition:
          resourceData.aggregation_type === "flat"
            ? {
                type: resourceData?.type,
                [definitionType]: {
                  resource_path: resourceData?.resource_path,
                  file_name: resourceData?.file_name,
                  column_delimiter: resourceData?.column_delimiter,
                  connection_key: resourceData?.connection_key,
                  connection_key_code: connectionKeysData?.find(
                    (option: { id: any }) =>
                      option.id === formData?.connection_key
                  )?.code,
                  container_name: resourceData?.container_name,
                  database_type: resourceData?.database_type,
                  connection_string: resourceData?.connection_string,
                  sql_query: resourceData?.sql_query,
                  remote_directory: resourceData?.remote_directory,
                  use_multi_thread_reader:
                    resourceData?.additional_properties?.resource_definition
                      ?.sql_definition?.use_multi_thread_reader || false,
                  column_name_to_partition_on_sql_query:
                    resourceData?.additional_properties?.resource_definition
                      ?.sql_definition?.column_name_to_partition_on_sql_query ||
                    "",
                },
                api_definition: apiType.includes(resourceData?.type)
                  ? {
                      ...resourceData?.api_definition,
                      query_params: queryParams,
                      url_params: urlParams,
                      body:
                        resourceData?.api_definition?.method !== "get"
                          ? resourceData?.api_definition?.body
                          : null,
                    }
                  : null,
              }
            : null,
        aggregation_properties:
          resourceData.aggregation_type === "aggregated"
            ? resourceData?.aggregation_properties
            : null,
        resource_column_details_id: resourceData?.resource_column_details_id,
        additional_resource_data: resourceData?.additional_resource_data,
        filter_rules: resourceData?.filter_rules,
        inline_variables: resourceData?.inline_variables,
        resource_column_details_code:
          resourceDetailData?.additional_properties
            ?.resource_column_details_code || null,
      },
    };
    if (formData?.linked_service?.type === "sql") {
      const keysToRemove = [
        "file_processing_code",
        "file_processing_id",
        "column_delimiter",
        "container_name",
        "file_name",
        "resource_path",
        "database_type",
        "connection_string",
      ];
      keysToRemove.forEach((key) => {
        if (key in reqBody) {
          delete reqBody[key];
        } else if (key in reqBody?.sql_definition) {
          delete reqBody?.sql_definition[key];
        }
      });
    }
    const resourceObject = removeUndefinedProperties(reqBody);
    updateResource({ currentResourceId: resourceId, payload: resourceObject })
      .then((response: any) => {
        if (response) showToast("Resource updated successfully!", "success");
      })
      .catch((error: any) => {
        showToast(`Cannot update resource`, "error");
      });
    setComment("");
  };

  return (
    <Box>
      <Grid
        container
        rowSpacing={3}
        columnSpacing={3}
        sx={{ marginBottom: "16px" }}
      >
        <ReRunLinkedServices
          formData={formData}
          setFormData={setFormData}
          connectionKeysData={connectionKeysData}
          linkedServicesData={linkedServicesData}
          setResourceId={setResourceId}
          resourceId={resourceId}
          setIsLoading={setIsLoading}
          setSelectLinkedServiceId={setSelectLinkedServiceId}
          errors={errors}
          setErrors={setErrors}
          queryParams={queryParams}
          setQueryParams={setQueryParams}
          urlParams={urlParams}
          setUrlParams={setUrlParams}
          type={type}
          setType={setType}
          setAllVariablesList={setAllVariablesList}
          allVariablesList={allVariablesList}
          resourceColumnData={resourceColumnData}
          setCancelUploadFile={setCancelUploadFile}
          cancelUploadFile={cancelUploadFile}
          setIsLoadingFile={setIsLoadingFile}
          isLoadingFile={isLoadingFile}
        />
        {formData?.resource_definition?.sql_definition
          ?.use_multi_thread_reader && (
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
            <TextField
              type="text"
              title="Column Name to Partition On"
              name="column_name_to_partition_on_sql_query"
              fullWidth
              label={
                <span>
                  Column Name to Partition On
                  <span className="required-asterisk">*</span>
                </span>
              }
              variant="outlined"
              className={`form-control-autocomplete ${
                errors?.column_name_to_partition_on_sql_query ? "has-error" : ""
              }`}
              value={
                formData?.resource_definition?.sql_definition
                  ?.column_name_to_partition_on_sql_query || ""
              }
              onChange={(e) => {
                setFormData({
                  ...formData,
                  resource_definition: {
                    ...formData.resource_definition,
                    sql_definition: {
                      ...formData?.resource_definition?.sql_definition,
                      column_name_to_partition_on_sql_query: e.target.value,
                    },
                  },
                });
              }}
              error={!!errors?.column_name_to_partition_on_sql_query}
              helperText={errors?.column_name_to_partition_on_sql_query || ""}
            />
          </Grid>
        )}
        <Grid
          item
          xs
          sx={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "flex-end",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              columnGap: "8px",
              flexWrap: "wrap",
              rowGap: "16px",
            }}
          >
            <label className="base-resource-checkbox m-0">
              <Checkbox
                checked={formData.isUpdateResource || false}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  setFormData({
                    ...formData,
                    isUpdateResource: event.target.checked,
                  });
                  if (event.target.checked)
                    setCurrentResourceId(formData?.id || formData?.resource_id);
                }}
                sx={{
                  "&.Mui-checked": {
                    color: "#FFA500",
                  },
                }}
              />
              <span>Update Resource</span>
            </label>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                columnGap: "8px",
                flexWrap: "wrap",
              }}
            >
              <Button
                color="secondary"
                variant="contained"
                onClick={() => {
                  setIsResourceEdit("");
                  if (cancelUploadFile) {
                    cancelUploadFile.cancel();
                  }
                }}
                className="btn-orange btn-dark"
              >
                Cancel
              </Button>
              <Button
                color="secondary"
                variant="contained"
                onClick={() => saveResource()}
                className="btn-orange"
                disabled={isLoadingFile || !hasChanges}
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </Box>
          </Box>
        </Grid>
      </Grid>
      <ConfirmationDialog
        title={"Confirm updating Resource"}
        dialogContent={
          <>
            <p className="mt-0">
              Confirm changes before saving them for the validation step.
              {formData.isUpdateResource
                ? "This action will also update the resource"
                : ""}
            </p>
            <div>
              <p className="m-0 mb-2">
                Add a note before updating the Resource
              </p>
              <textarea
                value={comment}
                className={`form-control-1 max-60`}
                onChange={(e) => handleCommentChange(e)}
              />
            </div>
          </>
        }
        handleCancel={handleCancelResource}
        openConfirmation={openResourceConfirmation}
        handleConfirm={handleSaveResource}
      />
    </Box>
  );
};

export default EditReRunValidateResourceDataTab;
