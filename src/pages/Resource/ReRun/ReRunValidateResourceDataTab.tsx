import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Tooltip,
} from "@mui/material";
import useFetchConnectionKeyByLinkedServiceId from "../../../hooks/useFetchConnectionKeyByLinkedServiceId";
import useFetchLinkedServices from "../../../hooks/useFetchLinkedServices";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useNavigate, useParams } from "react-router-dom";
import { IconViewSvgWhite } from "../../../common/utils/icons";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import { useToast } from "../../../services/utils";
import ViewResourceData from "../../../components/Resource/ReRun/ViewResourceData";
import EditReRunValidateResourceDataTab from "./EditReRunValidateResourceDataTab";

interface ReRunValidateResourceDataTabProps {
  resourcesData: any;
  isLoading: boolean;
  setResourcesData: Dispatch<SetStateAction<any>>;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  fileProcessingData: any;
  aggregationBaseResourceData: any;
  setAggregationBaseResourceData: Dispatch<SetStateAction<any>>;
}

const ReRunValidateResourceDataTab = ({
  resourcesData,
  isLoading,
  setResourcesData,
  setIsLoading,
  fileProcessingData,
  aggregationBaseResourceData,
  setAggregationBaseResourceData,
}: ReRunValidateResourceDataTabProps) => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { domainId } = useParams();
  const { isResourceEdit } = useRuleResourceContext();
  const [resourceType, setResourceType] = useState("");

  const [selectLinkedServiceId, setSelectLinkedServiceId] = useState<number>(0);
  const [expandedAccordion, setExpandedAccordion] =
    useState<any>("resource-data");
  const [linkedServicesData] = useFetchLinkedServices({ setIsLoading });
  const [connectionKeysData] = useFetchConnectionKeyByLinkedServiceId({
    selectLinkedServiceId,
    setIsLoading,
  });

  useEffect(() => {
    if (resourcesData) {
      setResourceType(resourcesData?.aggregation_type);
      setSelectLinkedServiceId((prev) => resourcesData?.linked_service_id);
    }
  }, [resourcesData]);

  useEffect(() => {
    if (aggregationBaseResourceData) {
      setSelectLinkedServiceId(aggregationBaseResourceData?.linked_service_id);
    }
  }, [aggregationBaseResourceData]);

  const handleChangeAccordion =
    (panel: string) => (event: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
      setExpandedAccordion(isExpanded ? panel : null);
    };

  const handleViewResource = (
    domainId: any,
    resourceId: any,
    resourceName: any,
    e: any
  ) => {
    e.preventDefault();
    if (isResourceEdit !== "") {
      return;
    }
    navigate(`/resource/${domainId}/view/${resourceId}`);
  };
  return (
    <>
      {isResourceEdit === "resource" ? (
        <EditReRunValidateResourceDataTab
          resourceData={resourcesData}
          setIsLoading={setIsLoading}
          setResourcesData={setResourcesData}
          linkedServicesData={linkedServicesData}
          connectionKeysData={connectionKeysData}
          setSelectLinkedServiceId={setSelectLinkedServiceId}
        />
      ) : (
        <Grid container rowSpacing={2.5} columnSpacing={4}>
          <ViewResourceData
            resourceData={resourcesData}
            resourcesData={resourcesData}
            linkedServicesData={linkedServicesData}
            connectionKeysData={connectionKeysData}
            // fileProcessingData={fileProcessingData}
            setIsLoading={setIsLoading}
            showAdditionalResource={true}
            resource_id={resourcesData?.resource_id}
            setResourcesData={setResourcesData}
            resourceCategory={"resource"}
          />
        </Grid>
      )}
      {resourceType === "aggregated" && (
        <Accordion
          expanded={expandedAccordion === "base-resource-data"}
          onChange={handleChangeAccordion("base-resource-data")}
          className="heading-bold mt-16 no-before-shadow"
        >
          <AccordionSummary
            aria-controls="panel1d-content"
            id="panel1d-header"
            expandIcon={<ExpandMoreIcon />}
          >
            <div className="custom-actions">
              <div className="heading">
                Base Resource Data -{" "}
                <span style={{ fontWeight: "normal" }}>
                  {aggregationBaseResourceData?.resource_name}
                </span>
              </div>
              <div className="action-btns">
                <Tooltip title="Navigate to view resource" placement="top">
                  <button
                    className="view-icon"
                    onClick={(e) =>
                      handleViewResource(
                        domainId,
                        aggregationBaseResourceData?.resource_id,
                        aggregationBaseResourceData?.resource_name,
                        e
                      )
                    }
                  >
                    <IconViewSvgWhite />
                  </button>
                </Tooltip>
              </div>
            </div>
          </AccordionSummary>
          <AccordionDetails sx={{ paddingTop: "16px" }}>
            {isResourceEdit === "aggregatedResource" ? (
              <>
                <EditReRunValidateResourceDataTab
                  resourceData={aggregationBaseResourceData}
                  setIsLoading={setIsLoading}
                  setResourcesData={setAggregationBaseResourceData}
                  linkedServicesData={linkedServicesData}
                  connectionKeysData={connectionKeysData}
                  setSelectLinkedServiceId={setSelectLinkedServiceId}
                />
              </>
            ) : (
              <Grid container rowSpacing={2.5} columnSpacing={4}>
                <ViewResourceData
                  resourceData={aggregationBaseResourceData}
                  resourcesData={aggregationBaseResourceData}
                  linkedServicesData={linkedServicesData}
                  connectionKeysData={connectionKeysData}
                  // fileProcessingData={fileProcessingData}
                  setIsLoading={setIsLoading}
                  showAdditionalResource={true}
                  resource_id={resourcesData?.resource_id}
                  setResourcesData={setAggregationBaseResourceData}
                  resourceCategory={"aggregatedResource"}
                />
              </Grid>
            )}
          </AccordionDetails>
        </Accordion>
      )}
    </>
  );
};

export default ReRunValidateResourceDataTab;
