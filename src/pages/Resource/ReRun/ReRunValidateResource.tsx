import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useToast } from "../../../services/utils";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Grid,
  Tooltip,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { IconBtnEditBase, IconViewSvgWhite } from "../../../common/utils/icons";
import FlexBetween from "../../../components/FlexBetween";
import ResultStorageParameters from "../../../components/Rules/ResultStorageParameters";
import RunParameters from "../../../components/Rules/RunParameters";
import { useResourceContext } from "../../../contexts/ResourceContext";
import useFetchFileProcessing from "../../../hooks/useFetchFileProcessing";
import { apiType, sqlDatabaseType } from "../../../services/constants";
import {
  backgroundReRunResourceValidation,
  ReRunResourceValidation,
} from "../../../services/resourcesService";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import Loader from "../../../components/Molecules/Loader/Loader";
import useFetchConnectionKeysAll from "../../../hooks/useFetchConnectionKeysAll";
import FilterRules from "../../../components/Resource/FilterRules";
import InlineVariables from "../InlineVariables";
import CustomAccordian from "../../../components/Molecules/Accordian/CustomAccordion";
import useFetchResourceColumnsById from "../../../hooks/useFetchResourceColumnsById";
import AddMultipleGridList from "../../../components/AddMultipleGridList";
import { useBreadCrumbContext } from "../../../contexts/BreadCrumbContext";
import { useRuleResourceContext } from "../../../contexts/RuleResourceContext";
import { filterRulesNameSchema, filterRulesSqlSchema } from "../../../schemas";
import useFetchAllResourcesWithoutDomain from "../../../hooks/useFetchAllResourcesWithoutDomain";
import ReRunValidateResourceDataTab from "./ReRunValidateResourceDataTab";
import {
  IResultStorageParameters,
  IRunParameters,
} from "../../../types/Execution";
import {
  defaultResultStorageParameters,
  defaultRunParameters,
} from "../../../services/models/execution.model";
import useFetchValidationRerunResultByResourceId from "../../../hooks/useFetchValidationRerunResultByResourceId";
import useFetchResourcesByIdMultipleIds from "../../../hooks/useFetchResourcesByMultipleIds";
import { executionNameSchema } from "../../../schemas";
import ExecutionName from "../../../components/Molecules/Rule/ExecutionName";

interface FilterFormData {
  filter_rules: any[];
}

const ReRunValidateResource = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const { showToast } = useToast();
  const { domainId, id } = useParams();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const {
    setResourceColumnData,
    resourceColumnData,
    refetchData,
    errors,
    setErrors,
  } = useResourceContext();
  const {
    isResourceEdit,
    globalVariables,
    setGlobalVariables,
    setAllResourcesData,
    setFileProcessingData,
  } = useRuleResourceContext();

  const [resourcesData, setResourcesData] = useState<any>({});
  const [aggregationBaseResourceData, setAggregationBaseResourceData] =
    useState<any>({});

  const [isEditFilters, setIsEditFilters] = useState(false);
  const [allVariablesList, setAllVariablesList] = useState<any>({});
  const [filterFormData, setFilterFormData] = useState<
    FilterFormData | undefined
  >(undefined);
  const [tempFilterFormData, setTempFilterFormData] = useState<any>({});
  const [hasFilterChanges, setHasFilterChanges] = useState<boolean>(false);
  const [intialFilterFormData, setIntialFilterFormData] = useState<
    FilterFormData | undefined
  >(undefined);
  const [hasChanges, setHasChanges] = useState<boolean>(true);

  const [isBackdropLoading, setIsBackdropLoading] = useState<boolean>(false);
  const [resultStorageParameters, setResultStorageParameters] =
    useState<IResultStorageParameters>(defaultResultStorageParameters);
  const [runParameters, setRunParameters] = useState<IRunParameters>({
    ...defaultRunParameters,
    validation_severity_level: "Low" as const,
  });
  const [severityColumnNames, setSeverityColumnNames] = useState<any>({
    low: null,
    medium: null,
    high: null,
  });
  const [isEditResource, setIsEditResource] = useState(false);
  const [expandedAccordion, setExpandedAccordion] =
    useState<any>("resource-data");

  const [validationMessage, setValidationMessage] = useState<any[]>([]);
  const [checkValidation, setCheckValidation] = useState(false);
  const [currentResourceColumnId, setCurrentResourceColumnId] =
    useState<any>(null);

  const [validateResourcePayload, setValidateResourcePayload] =
    useState<any>(null);
  const [isEditVariables, setIsEditVariables] = useState(false);
  const [inlineVariables, setInlineVariables] = useState<any>({});
  const [tempInlineVariables, setTempInlineVariables] = useState<any>({});
  const [intialInlineVariables, setIntialInlineVariables] = useState<any>({});
  const [isReRunData, setIsReRunData] = useState<any>({
    validationID: null,
    isReRun: false,
    runId: queryParams.get("run-id"),
    runName: queryParams.get("run-name"),
  });
  const [executionName, setExecutionName] = useState("");
  const [executionMode, setExecutionMode] = useState(true);

  const [fileProcessingData] = useFetchFileProcessing({ setIsLoading });
  // Always call hook but conditionally use the result
  const [validationRerunResultData] = useFetchValidationRerunResultByResourceId(
    {
      resourceId: id ? Number(id) : null,
      setIsLoading,
      runId: isReRunData.runId ? Number(isReRunData?.runId) : null,
      runName: isReRunData.runName ? isReRunData?.runName : null,
    }
  ) || { data: null, isLoading: false };

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const reRun = params.get("re-run");
    const validationId = params.get("validation-id");
    const runId = params.get("run-id");
    const runName = params.get("run-name");
    if (reRun && validationId) {
      setIsReRunData({
        validationID: Number(validationId),
        isReRun: true,
        runId: runId !== "null" ? runId : null, // Fix: Ensure runId is not null
        runName: runName !== "null" ? runName : null, // Fix: Ensure runName is not null
      });
    } else {
      setIsReRunData({
        validationID: null,
        isReRun: false,
      });
    }
  }, [location]);

  useEffect(() => {
    setFileProcessingData(fileProcessingData);
  }, [fileProcessingData]);

  const [resourceColumn] = useFetchResourceColumnsById({
    resourceColumnId: currentResourceColumnId,
    setIsLoading,
  });
  const [fetchedConnectionKeys] = useFetchConnectionKeysAll({ setIsLoading });
  const [allResources] = useFetchAllResourcesWithoutDomain({
    setIsLoading,
    aggregation_type: "aggregated",
  });

  useEffect(() => {
    if (allResources) {
      setAllResourcesData(allResources);
    }
  }, [allResources, setAllResourcesData]);

  /**
   * Set run parametes when re run data update
   */
  useEffect(() => {
    if (validationRerunResultData && fetchedConnectionKeys) {
      const severityColumnNames = {
        high:
          validationRerunResultData?.validation_request_body
            ?.severity_column_names?.["1"] || null,
        medium:
          validationRerunResultData?.validation_request_body
            ?.severity_column_names?.["2"] || null,
        low:
          validationRerunResultData?.validation_request_body
            ?.severity_column_names?.["3"] || null,
      };

      setSeverityColumnNames(severityColumnNames);

      const queryParams =
        validationRerunResultData?.validation_request_body?.query_params || {};
      const runInstance =
        validationRerunResultData?.validation_request_body?.run_instance || {};
      const severity =
        validationRerunResultData?.validation_request_body?.severity_level;

      setRunParameters({
        ...defaultRunParameters,
        no_of_errors_in_response: queryParams.no_of_errors_in_response,
        no_of_errors_in_output_files: queryParams.no_of_errors_in_output_files,
        skip_duplicate_records: queryParams.skip_duplicate_records,
        summary_mode: queryParams.summary_mode,
        generate_files: queryParams.generate_files,
        save_input_data_file: queryParams.save_input_data_file,
        store_errors_snapshots_and_create_issues:
          queryParams.store_errors_snapshots_and_create_issues,
        keep_downloaded_files: queryParams.keep_downloaded_files,
        pull_new_files_from_server: queryParams.pull_new_files_from_server,
        is_long_running_job: queryParams.is_long_running_job,

        validation_severity_level:
          severity === 1 ? "high" : severity === 2 ? "medium" : "low",

        run_instance: {
          run_id: runInstance.run_id || 1,
          run_name: runInstance.run_name || "Legacy",
        },
        isCustomiseSeverity:
          severityColumnNames.high ||
          severityColumnNames.medium ||
          severityColumnNames.low
            ? true
            : false,
      });

      setResultStorageParameters({
        ...defaultResultStorageParameters,
        linked_service_id:
          validationRerunResultData?.validation_request_body
            ?.output_storage_params?.output_storage_linked_service_id,
        linked_service_code:
          validationRerunResultData?.validation_request_body
            ?.output_storage_params?.output_storage_linked_service_code,
        connection_key_id:
          validationRerunResultData?.validation_request_body
            ?.output_storage_params?.output_storage_connection_key,
        file_path:
          validationRerunResultData?.validation_request_body
            ?.output_storage_params?.output_storage_base_file_path,
        validation_execution_report_name:
          validationRerunResultData?.validation_request_body
            ?.validation_execution_report_name,
      });

      const connectionKeyDetail = fetchedConnectionKeys?.find(
        (option: { id: any }) =>
          option.id ===
          validationRerunResultData?.validation_request_body?.resource_data
            ?.api_definition?.connection_key
      );
      const resourceData =
        validationRerunResultData?.validation_request_body?.resource_data;
      const resourceType = resourceData?.resource_type;

      // Create a modified resource data object
      let modifiedResourceData = {
        ...resourceData,
        id: resourceData?.resource_id,
        api_definition: {
          ...resourceData?.api_definition,
          url: connectionKeyDetail?.api_url,
          request_timeout: 0,
        },
      };

      // If type is 'sftp', rename resource_path to remote_directory
      if (resourceType === "sftp" && resourceData?.resource_path) {
        modifiedResourceData = {
          ...modifiedResourceData,
          remote_directory: resourceData.resource_path,
        };
      }

      setResourcesData(modifiedResourceData);
      setAggregationBaseResourceData(
        validationRerunResultData?.validation_request_body?.resource_data
          ?.aggregation_base_resource_data
      );
      setInlineVariables(
        validationRerunResultData?.validation_request_body?.inline_variables
      );
      setTempInlineVariables(
        validationRerunResultData?.validation_request_body?.inline_variables
      );
      if (validationRerunResultData?.validation_request_body?.inline_variables)
        setIntialInlineVariables(
          validationRerunResultData?.validation_request_body?.inline_variables
        );
      const filter_rules = validationRerunResultData?.validation_request_body
        ?.resource_data?.filter_rules
        ? validationRerunResultData?.validation_request_body?.resource_data?.filter_rules.map(
            (rule: any) => ({
              ...rule,
              id: Math.random(),
            })
          )
        : [];

      setFilterFormData({
        filter_rules: filter_rules,
      });
      setTempFilterFormData({
        filter_rules: filter_rules,
      });
      setIntialFilterFormData({
        filter_rules: filter_rules,
      });
      setGlobalVariables({
        ...globalVariables,
        ...validationRerunResultData?.validation_request_body?.inline_variables,
      });

      setCurrentResourceColumnId(
        validationRerunResultData?.validation_request_body?.resource_data
          ?.resource_column_details_id
      );
      setExecutionName(
        validationRerunResultData?.validation_request_body?.execution_name || ""
      );
      setCurrentBreadcrumbPage({
        name: validationRerunResultData?.validation_request_body?.resource_data
          ?.resource_name,
        id: validationRerunResultData?.validation_request_body?.resource_data
          ?.resource_id,
      });
      return () => {
        setCurrentBreadcrumbPage({ name: "", id: null });
      };
    }
  }, [validationRerunResultData, fetchedConnectionKeys]);

  useEffect(() => {
    const formChanged =
      JSON.stringify(tempFilterFormData) !==
      JSON.stringify(intialFilterFormData);
    setHasFilterChanges(formChanged);
  }, [tempFilterFormData, intialFilterFormData]);

  useEffect(() => {
    if (resourceColumn && Object.keys(resourceColumn)?.length) {
      setResourceColumnData(resourceColumn);
    }
  }, [resourceColumn]);

  const getFilePreProcessing = (id: string | number | undefined) => {
    const filePreProcessing = fileProcessingData?.find(
      (file: { id: string | number | null | undefined }) => file.id === id
    );
    return filePreProcessing
      ? filePreProcessing.file_processing_attributes
      : null;
  };

  const aggregationBaseResource = (resourceData: any) => {
    const resource: any = resourceData;
    const type = resource?.type || resource?.resource_type;
    const {
      user_name,
      password,
      bearer_token,
      api_key,
      oauth_client_id,
      oauth_client_secret,
      oauth_url,
      method,
      content_type,
      body,
      query_params,
      url_params,
      url,
      request_timeout,
    } = getApiDefinition(resource);
    const additional_base_resource_data =
      resource?.additional_base_resource_data
        ? processAdditionalResourceData(resource?.additional_base_resource_data)
        : null;
    let resourceObject: any = {
      type: type,
      aggregation_type: resource?.aggregation_type,
      resource_name: resource?.resource_name,
      resource_column_details_id: resource?.resource_column_details_id,
      file_processing_id: resource?.file_processing_id,
      resource_id: resource?.resource_id || null,
      linked_service_id: resource?.linked_service_id || null,
      linked_service_code: resource?.linked_service_code || null,
      connection_key: resource?.connection_key,
      sql_query: resource?.sql_query,
      container_name: resource?.container_name,
      resource_path:
        resource?.resource_type === "sftp"
          ? resource?.remote_directory
          : resource?.resource_path,
      file_name: resource?.file_name,
      column_delimiter: resource?.column_delimiter,
      use_multi_thread_reader: resource?.use_multi_thread_reader,
      column_name_to_partition_on_sql_query:
        resource?.column_name_to_partition_on_sql_query,
      excel_sheet_name: resourcesData?.excel_sheet_name,
      additional_base_resource_data,
      file_pre_processing:
        sqlDatabaseType.includes(type) || apiType.includes(type)
          ? null
          : getFilePreProcessing(resource?.file_processing_id),
      aggregation_properties:
        resource?.aggregation_type === "flat"
          ? null
          : resource?.additional_properties?.aggregation_properties || null,
      aggregation_base_resource_data: null,
      api_request: {
        user_name,
        password,
        bearer_token,
        api_key,
        oauth_client_id,
        oauth_client_secret,
        oauth_url,
        method,
        content_type,
        body,
        query_params,
        url_params,
        url,
        request_timeout,
      },
    };
    checkRequiredValidation(resource, resourceObject);

    resourceObject = Object.fromEntries(
      Object.entries(resourceObject).filter(([_, v]) => v !== null)
    );
    if (
      Object.keys(resourceObject).length === 0 &&
      resourceObject.constructor === Object
    ) {
      return null;
    }
    return Object.keys(resourceObject).length !== 0 ? resourceObject : null;
  };

  const checkOutputStorageParamsNull = (
    output_storage_params: { [s: string]: unknown } | ArrayLike<unknown>
  ) => {
    return Object.values(output_storage_params).some((value) => value === null);
  };
  const getApiDefinition = (resource: any) => {
    let user_name = resource?.api_definition?.user_name || null;
    let password = resource?.api_definition?.password || null;
    let bearer_token = resource?.api_definition?.bearer_token || null;
    let api_key = resource?.api_definition?.api_key || null;
    let oauth_client_id = resource?.api_definition?.oauth_client_id || null;
    let oauth_client_secret =
      resource?.api_definition?.oauth_client_secret || null;
    let oauth_url = resource?.api_definition?.oauth_url || null;
    let method = resource?.api_definition?.method || "get";
    let content_type =
      resource?.api_definition?.content_type || "application/json";
    let body = resource?.api_definition?.body || null;
    let query_params = resource?.api_definition?.query_params || null;
    let url_params = resource?.api_definition?.url_params || null;
    let url = resource?.api_definition?.url || null;
    let request_timeout = resource?.api_definition?.request_timeout || 0;
    return {
      user_name,
      password,
      bearer_token,
      api_key,
      oauth_client_id,
      oauth_client_secret,
      oauth_url,
      method,
      content_type,
      body,
      query_params,
      url_params,
      url,
      request_timeout,
    };
  };

  const processAdditionalResourceData = (additionalResourceData: any) => {
    if (!Array.isArray(additionalResourceData)) {
      return null;
    }
    const res = additionalResourceData
      .map((resource) => aggregationBaseResource(resource))
      .filter(Boolean);
    return res;
  };
  useEffect(() => {
    const validateResourceData = () => {
      setValidationMessage([]);

      let output_storage_params = {
        output_storage_linked_service_id:
          resultStorageParameters?.linked_service_id || null,
        output_storage_linked_service_code:
          resultStorageParameters?.linked_service_code || null,
        output_storage_connection_key:
          resultStorageParameters?.connection_key_id || null,
        output_storage_base_file_path:
          resultStorageParameters?.file_path || null,
      };

      const isOutputStorageParamsNull = checkOutputStorageParamsNull(
        output_storage_params
      );

      let severityColumnNamesPayload = {
        1: severityColumnNames.high,
        2: severityColumnNames.medium,
        3: severityColumnNames.low,
      };

      const severity_column_names: any = Object.fromEntries(
        Object.entries(severityColumnNamesPayload).filter(
          ([_, v]) => v !== null
        )
      );

      const type = resourcesData?.type || resourcesData?.resource_type;

      const {
        user_name,
        password,
        bearer_token,
        api_key,
        oauth_client_id,
        oauth_client_secret,
        oauth_url,
        method,
        content_type,
        body,
        query_params,
        url_params,
        url,
        request_timeout,
      } = getApiDefinition(resourcesData);

      const additional_base_resource_data =
        resourcesData?.additional_base_resource_data
          ? processAdditionalResourceData(
              resourcesData?.additional_base_resource_data
            )
          : null;
      let resourceObject: any = {
        type: type,
        aggregation_type: resourcesData?.aggregation_type,
        resource_name: resourcesData?.resource_name,
        resource_column_details_id: resourcesData?.resource_column_details_id,
        file_processing_id: resourcesData?.file_processing_id,
        resource_id: resourcesData?.resource_id || null,
        linked_service_id: resourcesData?.linked_service_id || null,
        linked_service_code: resourcesData?.linked_service_code || null,
        connection_key: resourcesData?.connection_key,
        sql_query: resourcesData?.sql_query,
        container_name: resourcesData?.container_name,
        resource_path:
          resourcesData?.resource_type === "sftp"
            ? resourcesData?.remote_directory
            : resourcesData?.resource_path,
        file_name: resourcesData?.file_name,
        column_delimiter: resourcesData?.column_delimiter,
        use_multi_thread_reader: resourcesData?.use_multi_thread_reader,
        column_name_to_partition_on_sql_query:
          resourcesData?.column_name_to_partition_on_sql_query,
        excel_sheet_name: resourcesData?.excel_sheet_name,
        additional_base_resource_data,
        file_pre_processing:
          sqlDatabaseType.includes(type) || apiType.includes(type)
            ? null
            : getFilePreProcessing(resourcesData?.file_processing_id),
        filter_rules: filterFormData?.filter_rules ?? [],
        aggregation_properties:
          resourcesData?.aggregation_type === "flat"
            ? null
            : resourcesData?.aggregation_properties || null,
        aggregation_base_resource_data:
          resourcesData?.aggregation_type === "flat"
            ? null
            : aggregationBaseResource(aggregationBaseResourceData) || null,
        api_request: {
          user_name,
          password,
          bearer_token,
          api_key,
          oauth_client_id,
          oauth_client_secret,
          oauth_url,
          method,
          content_type,
          body,
          query_params,
          url_params,
          url,
          request_timeout,
        },
      };
      checkRequiredValidation(resourcesData, resourceObject);

      resourceObject = Object.fromEntries(
        Object.entries(resourceObject).filter(([_, v]) => v !== null)
      );

      let reqBody: any = {
        validation_request_body: {
          execution_name: executionName,
          resource_data: resourceObject,
          ...(isOutputStorageParamsNull ? {} : { output_storage_params }),
          run_instance: {
            run_id: runParameters?.run_instance.run_id || 1,
            run_name: runParameters?.run_instance.run_name || "Legacy",
          },
          severity_level:
            runParameters?.validation_severity_level === "high"
              ? 1
              : runParameters?.validation_severity_level === "medium"
              ? 2
              : 3,
          inline_variables: globalVariables || {},
          validation_execution_report_name:
            resultStorageParameters?.validation_execution_report_name || null,
          query_params: {
            ...runParameters,
          },
        },
        ...runParameters,
        no_of_sample_validation_errors:
          validationRerunResultData?.no_of_sample_validation_errors,
        no_of_errors_in_filter_record_output_files:
          validationRerunResultData?.no_of_errors_in_filter_record_output_files,
        resource_id: validationRerunResultData?.resource_id,
        resource_column_details_version:
          validationRerunResultData?.resource_column_details_version,
        resource_version: validationRerunResultData?.resource_version,
        domain_version: validationRerunResultData?.domain_version,
      };
      if (
        reqBody?.validation_request_body?.inline_variables &&
        Object.keys(reqBody?.validation_request_body?.inline_variables).length >
          0
      ) {
        Object.keys(reqBody?.validation_request_body?.inline_variables).forEach(
          (key) => {
            if (
              reqBody?.validation_request_body?.inline_variables[key] === ""
            ) {
              setValidationMessage((prev) => [
                ...prev,
                {
                  id: `Resource Variables`,
                  message: `Please enter value for ${key}`,
                },
              ]);
            }
          }
        );
      }
      if (Object.keys(severity_column_names).length !== 0) {
        reqBody = {
          ...reqBody,
          validation_request_body: {
            ...reqBody.validation_request_body,
            severity_column_names,
          },
        };
      }
      setValidateResourcePayload(reqBody);
    };
    if (checkValidation) validateResourceData();
  }, [checkValidation]);

  useEffect(() => {
    if (validationMessage.length === 0 && validateResourcePayload) {
      setIsBackdropLoading(true);
      if (executionMode) {
        backgroundReRunResourceValidation(validateResourcePayload)
          .then(() => {
            showToast(
              "Your re-run resource validation is in the queue. We will notify you once it's done!",
              "warn"
            );
            navigate(`/validation-execution-history`);
          })
          .catch((error) => {
            console.error(error?.response?.data?.message);
          })
          .finally(() => {
            setCheckValidation(false);
            setValidateResourcePayload(null);
            setIsBackdropLoading(false);
          });
      } else {
        ReRunResourceValidation(validateResourcePayload)
          .then((response) => {
            if (response) {
              showToast("Resource re-validated successfully!", "success");
              navigate(
                `/validation-execution-history/validate-result/${response?.id}`,
                {
                  state: {
                    response,
                    isValidateResourceResponse: true,
                  },
                }
              );
            }
          })
          .catch((error) => {
            console.error(error?.response?.data?.message);
          })
          .finally(() => {
            setCheckValidation(false);
            setValidateResourcePayload(null);
            setIsBackdropLoading(false);
          });
      }
    } else if (validationMessage.length > 0) {
      const temp = (
        <div>
          Not all fields are configured. Please check the following fields:
          {validationMessage.map((msg) => (
            <div>
              <span style={{ fontWeight: 900 }}>{msg.id}</span>-{msg.message}
            </div>
          ))}
        </div>
      );
      showToast(temp, "warning");
      setCheckValidation(false);
      return;
    }
  }, [validationMessage, validateResourcePayload]);
  const checkRequiredValidation = (resource: any, resourceObject: any) => {
    const type = resource?.type || resource?.resource_type;
    const fieldNamesMap: { [key: string]: string } = {
      connection_key: "Connection Key",
      container_name: "Container Name",
      resource_path: "Resource Path",
      file_name: "File Name",
      column_delimiter: "Column Delimiter",
      base_resource_id: "Base resource",
      base_resource_columns: "Base Resource Columns",
      aggregation_query: "Aggregation Query",
      sql_query: "Sql Query",
      user_name: "Username",
      password: "Password",
      url: "Url",
      request_timeout: "Request Timeout",
      bearer_token: "Bearer Token",
      api_key: "Api Key",
      oauth_client_id: "Oauth Client Id",
      oauth_client_secret: "Oauth Client Secret",
      oauth_url: "Oauth Url",
    };
    if (resource?.aggregation_type === "flat") {
      let requiredFields: any = [];
      if (sqlDatabaseType.includes(type)) {
        requiredFields = ["connection_key", "sql_query"];
      } else if (type === "sftp" || type === "local") {
        requiredFields = [
          "connection_key",
          "resource_path",
          "file_name",
          "column_delimiter",
        ];
      } else if (type === "blob") {
        requiredFields = [
          "connection_key",
          "resource_path",
          "file_name",
          "column_delimiter",
          "container_name",
        ];
      } else if (type === "basic_auth_api") {
        requiredFields = [
          "connection_key",
          "user_name",
          "password",
          "url",
          "request_timeout",
        ];
      } else if (type === "token_auth_api") {
        requiredFields = [
          "connection_key",
          "bearer_token",
          "url",
          "request_timeout",
        ];
      } else if (type === "key_auth_api") {
        requiredFields = [
          "connection_key",
          "api_key",
          "url",
          "request_timeout",
        ];
      } else if (type === "oauth_api") {
        requiredFields = [
          "connection_key",
          "oauth_client_id",
          "oauth_client_secret",
          "oauth_url",
          "url",
          "request_timeout",
        ];
      }
      let missingFields: any;
      if (
        type === "basic_auth_api" ||
        type === "token_auth_api" ||
        type === "key_auth_api" ||
        type === "oauth_api"
      ) {
        missingFields = requiredFields?.filter(
          (field: any) => resourceObject?.api_request[field] === null
        );
      } else {
        missingFields =
          requiredFields &&
          requiredFields.filter((field: any) => resourceObject[field] === null);
      }

      if (missingFields && missingFields.length > 0) {
        const missingFieldNames = missingFields.map(
          (field: any) => `${fieldNamesMap[field]}`
        );
        setValidationMessage((prev) => [
          ...prev,
          {
            id: resource?.resource_name,
            message: missingFieldNames.join(", "),
          },
        ]);
        // return false;
      }
    } else if (
      resource?.aggregation_type === "aggregated" &&
      resourceObject.aggregation_properties !== null
    ) {
      const requiredFields: string[] = [
        "base_resource_id",
        "base_resource_columns",
        "aggregation_query",
      ];
      const missingFields = requiredFields.filter(
        (field) => resourceObject.aggregation_properties[field] === null
      );

      if (missingFields.length > 0) {
        const missingFieldNames = missingFields.map(
          (field) => `${fieldNamesMap[field]}`
        );
        setValidationMessage((prev) => [
          ...prev,
          {
            id: resource?.resource_name,
            message: missingFieldNames.join(", "),
          },
        ]);
      }
    }
  };
  const handleChangeAccordion =
    (panel: string) => (_: any, isExpanded: any) => {
      if (isResourceEdit !== "") {
        showToast("Please save changes first!!", "warning");
        return;
      }
      setExpandedAccordion(isExpanded ? panel : null);
    };

  const handleViewResource = (domainId: any, resource: any, e: any) => {
    e.preventDefault();
    if (isResourceEdit !== "") {
      return;
    }
    navigate(`/resource/${domainId}/view/${resource}`);
  };
  const handleChangeVariable = (event: any) => {
    const { name, value } = event.target;
    setTempInlineVariables((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  useEffect(() => {
    const formChanged =
      JSON.stringify(tempInlineVariables) !==
      JSON.stringify(intialInlineVariables);
    setHasChanges(formChanged);
  }, [tempInlineVariables, intialInlineVariables]);

  useEffect(() => {
    refetchData();
  }, [id]);
  const checkFilterRuleValidation = async (
    updatedFormData: any,
    schemaType: any
  ) => {
    const getSchemaType =
      schemaType === "name" ? filterRulesNameSchema : filterRulesSqlSchema;
    for (let index = 0; index < updatedFormData.filter_rules.length; index++) {
      const element = updatedFormData.filter_rules[index];
      try {
        await getSchemaType.validate(element, { abortEarly: false });
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] = undefined;
        } else {
          updatedJsonData.filter_rules.push({ [updateKey]: undefined });
        }
        setErrors(updatedJsonData);
      } catch (validationErrors: any) {
        const updatedJsonData = { ...errors };
        const updateKey = schemaType === "name" ? "name" : "sql_query";
        if (updatedJsonData.filter_rules.length > index) {
          updatedJsonData.filter_rules[index][updateKey] =
            validationErrors.message;
        } else {
          updatedJsonData.filter_rules.push({
            [updateKey]: validationErrors.message,
          });
        }
        setErrors(updatedJsonData);
      }
    }
  };
  const handleExecuteSubmit = async () => {
    try {
      await executionNameSchema.validate(
        { execution_name: executionName },
        {
          abortEarly: false,
        }
      );
      setCheckValidation(true);
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  return (
    <>
      <Loader isLoading={isBackdropLoading} />
      <Loader isLoading={isLoading} />
      <div
        className="accordion-panel"
        style={{ marginBottom: "0", marginTop: "12px" }}
      >
        <Box>
          <ExecutionName
            handleChangeAccordion={handleChangeAccordion}
            errors={errors}
            setErrors={setErrors}
            executionName={executionName}
            setExecutionName={setExecutionName}
            executionMode={executionMode}
            setExecutionMode={setExecutionMode}
          />
          <Accordion
            className="heading-bold box-shadow"
            expanded={expandedAccordion === "resource-data"}
            onChange={handleChangeAccordion("resource-data")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              <div className="custom-actions">
                <div className="heading">Resource Data</div>
                <div className="action-btns">
                  <Tooltip title="Navigate to view resource" placement="top">
                    <button
                      className="view-icon"
                      onClick={(e) => handleViewResource(domainId, id, e)}
                    >
                      <IconViewSvgWhite />
                    </button>
                  </Tooltip>
                </div>
              </div>
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <ReRunValidateResourceDataTab
                resourcesData={resourcesData}
                isLoading={isBackdropLoading}
                setResourcesData={setResourcesData}
                setIsLoading={setIsLoading}
                fileProcessingData={fileProcessingData}
                aggregationBaseResourceData={aggregationBaseResourceData}
                setAggregationBaseResourceData={setAggregationBaseResourceData}
              />
            </AccordionDetails>
          </Accordion>

          <Accordion
            className="mt-8 heading-bold box-shadow"
            expanded={expandedAccordion === "run-parameters"}
            onChange={handleChangeAccordion("run-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Run Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <RunParameters
                runParameters={runParameters}
                setRunParameters={setRunParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                severityColumnNames={severityColumnNames}
                setSeverityColumnNames={setSeverityColumnNames}
                isShow={{
                  isShowSkipValidation: false,
                  isShowExecuteResearchQueries: false,
                  use_secondary_merge_resources: false,
                }}
                isReRun={isReRunData?.isReRun}
              />
            </AccordionDetails>
          </Accordion>

          <Accordion
            className="mt-8 heading-bold box-shadow"
            expanded={expandedAccordion === "result-storage-parameters"}
            onChange={handleChangeAccordion("result-storage-parameters")}
          >
            <AccordionSummary
              aria-controls="panel1d-content"
              id="panel1d-header"
              expandIcon={<ExpandMoreIcon />}
            >
              Result Storage Parameters
            </AccordionSummary>
            <AccordionDetails sx={{ paddingTop: "16px" }}>
              <ResultStorageParameters
                resultStorageParameters={resultStorageParameters}
                setResultStorageParameters={setResultStorageParameters}
                isEditResource={isEditResource}
                setIsEditResource={setIsEditResource}
                setIsLoading={setIsLoading}
                isFromRule={false}
              />
            </AccordionDetails>
          </Accordion>

          {inlineVariables && Object.keys(inlineVariables).length > 0 && (
            <Accordion
              className="mt-8 heading-bold box-shadow"
              expanded={expandedAccordion === "inline-variables"}
              onChange={handleChangeAccordion("inline-variables")}
            >
              <AccordionSummary
                aria-controls="panel1d-content"
                id="panel1d-header"
                expandIcon={<ExpandMoreIcon />}
              >
                Resource Variables
              </AccordionSummary>
              <AccordionDetails sx={{ paddingTop: "16px" }}>
                <div className="text-box-card box-card-variables full-radius">
                  {!isEditVariables ? (
                    <div className="inline-variables-parent">
                      <InlineVariables
                        inlineVariables={inlineVariables || []}
                        handleChangeVariable={handleChangeVariable}
                        isReadOnly={true}
                      />
                    </div>
                  ) : (
                    <div className="inline-variables-parent">
                      <InlineVariables
                        inlineVariables={tempInlineVariables || []}
                        handleChangeVariable={handleChangeVariable}
                        isReadOnly={false}
                      />
                    </div>
                  )}
                  {!isEditVariables ? (
                    <Grid
                      item
                      xs={12}
                      sm={12}
                      md
                      sx={{
                        justifyContent: "flex-end",
                        display: "flex",
                        marginTop: "8px",
                      }}
                    >
                      <button
                        className="btn-nostyle icon-btn-edit"
                        onClick={() => {
                          setIsEditVariables(true);
                          setTempInlineVariables(inlineVariables);
                        }}
                      >
                        <IconBtnEditBase />
                      </button>
                    </Grid>
                  ) : (
                    <Grid
                      item
                      xs
                      sx={{
                        display: "flex",
                        alignItems: "flex-end",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          columnGap: "8px",
                          flexWrap: "wrap",
                          marginTop: "8px",
                        }}
                      >
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => {
                            setIsEditVariables(false);
                          }}
                          className="btn-orange btn-dark"
                        >
                          Cancel
                        </Button>
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => {
                            setInlineVariables(tempInlineVariables);
                            setGlobalVariables((prev: any) => ({
                              ...prev,
                              ...tempInlineVariables,
                            }));
                            setIsEditVariables(false);
                          }}
                          className="btn-orange"
                          disabled={!hasChanges}
                        >
                          <SaveOutlinedIcon /> &nbsp; Save
                        </Button>
                      </Box>
                    </Grid>
                  )}
                </div>
              </AccordionDetails>
            </Accordion>
          )}

          {filterFormData?.filter_rules &&
            filterFormData?.filter_rules.length > 0 && (
              <CustomAccordian
                expandId="filter-rules"
                title="Filter Rules"
                handleChangeAccordion={handleChangeAccordion}
                isEnabled={true}
              >
                <FilterRules
                  formData={
                    !isEditFilters ? filterFormData : tempFilterFormData
                  }
                  isAddButtonHidden={true}
                  setFormData={setTempFilterFormData}
                  isViewOnly={!isEditFilters}
                  resourceColumnData={resourceColumnData}
                  checkFilterRuleValidation={checkFilterRuleValidation}
                  setAllVariablesList={setAllVariablesList}
                />

                <Grid
                  item
                  xs={12}
                  sm={12}
                  md
                  sx={{
                    justifyContent: "flex-end",
                    display: "flex",
                    marginTop: "8px",
                  }}
                >
                  {!isEditFilters && (
                    <button
                      className="btn-nostyle icon-btn-edit"
                      onClick={() => {
                        setIsEditFilters((prev) => !prev);
                      }}
                    >
                      <IconBtnEditBase />
                    </button>
                  )}

                  {isEditFilters && (
                    <Grid
                      item
                      xs
                      sx={{
                        display: "flex",
                        alignItems: "flex-end",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          columnGap: "8px",
                          flexWrap: "wrap",
                        }}
                      >
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => {
                            setIsEditFilters((prev: any) => !prev);
                            setTempFilterFormData(filterFormData);
                          }}
                          className="btn-orange btn-dark"
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          onClick={() => {
                            setIsEditFilters((prev) => !prev);
                            setFilterFormData(tempFilterFormData);
                            setIntialFilterFormData(tempFilterFormData);
                          }}
                          variant="contained"
                          color="secondary"
                          className="btn-orange"
                          title="Save Resource"
                          disabled={!hasFilterChanges}
                        >
                          <SaveOutlinedIcon /> &nbsp; Save
                        </Button>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </CustomAccordian>
            )}
          {resourceColumnData?.resource_column_properties
            ?.cross_field_validations &&
            resourceColumnData?.resource_column_properties
              ?.cross_field_validations.length > 0 && (
              <CustomAccordian
                expandId="adhoc-query"
                title="Adhoc Query"
                handleChangeAccordion={handleChangeAccordion}
                isEnabled={true}
              >
                <AddMultipleGridList
                  gridValue={
                    resourceColumnData?.resource_column_properties
                      ?.cross_field_validations
                  }
                  setGridValue={() => {}}
                  isViewOnly={true}
                  buttonName=" "
                  errors={{}}
                  setErrors={() => {}}
                />
              </CustomAccordian>
            )}
          {!isEditResource && isResourceEdit === "" && (
            <FlexBetween
              gap="3rem"
              sx={{ marginTop: 2, display: "flex", justifyContent: "flex-end" }}
            >
              <Button
                type="submit"
                className="btn-orange"
                color="secondary"
                variant="contained"
                onClick={handleExecuteSubmit}
              >
                Re-Validate
              </Button>
            </FlexBetween>
          )}
        </Box>
      </div>
    </>
  );
};

export default ReRunValidateResource;
