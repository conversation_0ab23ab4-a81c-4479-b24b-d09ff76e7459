import React, {
  Dispatch,
  SetStateAction,
  useEffect,
  useState,
  useRef,
} from "react";
import { Box, Grid } from "@mui/material";

interface InlineVariablesProps {
  handleChangeVariable: any;
  inlineVariables: any;
  isReadOnly: boolean;
}

const InlineVariables: React.FC<InlineVariablesProps> = ({
  handleChangeVariable,
  inlineVariables,
  isReadOnly,
}) => {
  return (
    <table className="inline-variables-table">
      <tbody>
        {inlineVariables &&
          Object.keys(inlineVariables).map((key: string, index: number) => {
            return (
              <tr key={index}>
                <td>
                  <div className="inner-column">
                    <div className="label">
                      <strong>{key}</strong>
                      <span className="required-asterisk">*</span>:
                    </div>
                    <input
                      className={`form-control-1 ${
                        isReadOnly ? "read-only" : ""
                      }`}
                      value={inlineVariables[key]}
                      name={key}
                      title={inlineVariables[key]}
                      onChange={(event) =>
                        handleChangeVariable(event, key + index)
                      }
                      readOnly={isReadOnly}
                    />
                  </div>
                </td>
              </tr>
            );
          })}
      </tbody>
    </table>
  );
};

export default React.memo(InlineVariables);
