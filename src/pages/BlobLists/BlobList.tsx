import {
  Box,
  Grid,
  <PERSON>Field,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Autocomplete,
  ListItemText,
  InputBase,
  IconButton,
  Stack,
} from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { getBlobListSchema } from "../../schemas";
import Loader from "../../components/Molecules/Loader/Loader";
import FlexBetween from "../../components/FlexBetween";
import ButtonComponent from "../../components/Button.Component";
import InfoIcon from "@mui/icons-material/Info";
import useFetchLinkedServices from "../../hooks/useFetchLinkedServices";
import useFetchConnectionKey from "../../hooks/useFetchConnectionKey";
import base from "../../middlewares/interceptors";
import { getBlobListsUrl } from "../../services/constants";
import * as XLSX from "xlsx";
import DataTable from "../../components/DataGrids/DataGrid";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { Search } from "@mui/icons-material";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import DirectorySelector from "../../components/Molecules/DirectorySelector/DirectorySelector";
import { toast } from "react-toastify";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const BlobList: React.FC = () => {
  const [formData, setFormData] = useState<any>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [tableData, setTableData] = useState<{ columns: any[]; rows: any[] }>({
    columns: [],
    rows: [],
  });
  const [searchBlobName, setSearchBlobName] = useState("");
  const [currentDirectory, setCurrentDirectory] = useState<any>([]);

  // hooks
  const [linkedServicesData] = useFetchLinkedServices({
    setIsLoading,
    sub_type: "blob",
  });
  const [connectionKeysData] = useFetchConnectionKey({
    filterKeys: formData?.linked_service?.connection_details?.connection_keys,
    setIsLoading,
  });

  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });

    validateField(e.target.name, e.target.value);
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await getBlobListSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  const onSaveGetBlobList = async (e: any) => {
    e.preventDefault();
    try {
      setIsLoading(true);
      // Validate the form data
      await getBlobListSchema.validate(formData, { abortEarly: false });

      // Prepare the request body
      const reqBody = {
        linked_service_code: formData?.linked_service?.code,
        connection_key: formData?.connection_key,
        container_name: formData?.container_name,
        file_name: formData?.file_name ?? "",
        file_path: formData?.file_path ?? "",
        output_location: formData?.output_location ?? "",
      };

      try {
        const response = await base.post(`${getBlobListsUrl}`, reqBody, {
          responseType: "blob",
        });

        if (response && response.status >= 200 && response.status < 300) {
          // Instead of downloading the file, parse it using XLSX
          const parsedData = await parseExcelFile(response);
          setTableData(parsedData); // Pass the parsed data to state
        }
      } catch (error) {
        console.log(error);
        return Promise.reject(error);
      }
    } catch (validationErrors: any) {
      // Handle validation errors
      const newErrors: { [key: string]: string } = {};
      if (validationErrors?.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            newErrors[error.path] = error.message;
          }
        );
      }
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // Function to parse the Excel file using XLSX
  const parseExcelFile = async (response: any) => {
    const data = await response.data.arrayBuffer();
    const workbook = XLSX.read(data, { type: "array" });

    // Get the first sheet
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    // Convert the worksheet to JSON
    const jsonData: any = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    // Identify the index of the blob_name, blob_creation_time, and blob_last_modified_time columns
    const headerRow = jsonData[0];
    const blobCreationDateIndex = headerRow.indexOf("blob_creation_time");
    const blobLastModifiedDateIndex = headerRow.indexOf(
      "blob_last_modified_time"
    );

    // Map to update specific column header names
    const customHeadersMap: { [key: string]: string } = {
      blob_name: "File Name",
      blob_creation_time: "Creation Date",
      blob_last_modified_time: "Last Modified Date",
      blob_size: "File Size (Bytes)",
    };

    // Convert the worksheet header to DataGrid columns with custom names
    const columns = headerRow.map((col: any, index: number) => ({
      field: index.toString(),
      headerName: customHeadersMap[col] || col, // Replace with custom header name if it exists
      width: 100,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    }));

    // Add the custom column definition at the start
    const modifiedColumns = [
      {
        ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
        width: 1,
        renderCell: (params: any) => <></>, // Empty render
      },
      ...columns,
    ];

    // Function to convert Excel serial date to JS Date
    const excelDateToJSDate = (serial: number) => {
      const utcDays = Math.floor(serial - 25569);
      const utcValue = utcDays * 86400; // seconds in a day
      const dateInfo = new Date(utcValue * 1000);
      const fractionalDay = serial - Math.floor(serial) + 0.0000001;
      const totalSeconds = Math.floor(86400 * fractionalDay);
      const seconds = totalSeconds % 60;
      const minutes = Math.floor(totalSeconds / 60) % 60;
      const hours = Math.floor(totalSeconds / 3600);
      dateInfo.setHours(hours, minutes, seconds);

      // Format the date as "YYYY-MM-DD HH:mm:ss"
      const year = dateInfo.getFullYear();
      const month = String(dateInfo.getMonth() + 1).padStart(2, "0");
      const day = String(dateInfo.getDate()).padStart(2, "0");
      const hour = String(dateInfo.getHours()).padStart(2, "0");
      const minute = String(dateInfo.getMinutes()).padStart(2, "0");
      const second = String(dateInfo.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    };

    // Function to check if a value is a potential Excel date
    const isExcelDate = (value: any) => {
      return typeof value === "number" && value > 25569;
    };

    // Create rows with Excel date conversion for specific columns
    const rows = jsonData.slice(1).map((row: any, rowIndex: number) => {
      const rowData: { [key: string]: any } = { id: rowIndex };
      row.forEach((cell: any, colIndex: number) => {
        // Check if the column is either blob_creation_time or blob_last_modified_time
        if (
          (colIndex === blobCreationDateIndex ||
            colIndex === blobLastModifiedDateIndex) &&
          isExcelDate(cell)
        ) {
          // Convert only for these specific columns
          rowData[colIndex.toString()] = excelDateToJSDate(cell);
        } else {
          rowData[colIndex.toString()] = cell;
        }
      });
      return rowData;
    });

    return { columns: modifiedColumns, rows };
  };

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchBlobName(e.target.value);
  };
  const filteredData = useMemo(() => {
    return currentDirectory?.filter((row: { [x: string]: string }) => {
      // Extract the filename from the string (excluding directory)
      const fileName = row["0"].split("/").pop();

      // Check if the searchBlobName matches the filename (case-insensitive)
      return fileName?.toLowerCase().includes(searchBlobName?.toLowerCase());
    });
  }, [currentDirectory, searchBlobName]);

  return (
    <>
      <Loader isLoading={isLoading} />
      <Box>
        <form onSubmit={onSaveGetBlobList} autoComplete="off">
          <Box className="text-box-card-white m-0">
            <Box className="text-box-header ">
              <h3>Get Blob List</h3>
            </Box>
            <Grid container rowSpacing={2.5} columnSpacing={4}>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  <span className="position-relative">
                    Linked Service <span className="required-asterisk">*</span>
                    {formData?.linked_service?.id && (
                      <Tooltip
                        componentsProps={{
                          tooltip: { className: "wide-tooltip w-250" },
                        }}
                        title={
                          formData?.linked_service?.id && (
                            <React.Fragment>
                              <Typography color="inherit">
                                Linked Service Code :{" "}
                                {
                                  linkedServicesData?.find(
                                    (item: any) =>
                                      item?.id === formData?.linked_service?.id
                                  )?.code
                                }
                              </Typography>
                            </React.Fragment>
                          )
                        }
                      >
                        <InfoIcon
                          sx={{
                            position: "absolute",
                            top: "50%",
                            transform: "translateY(-50%)",
                            right: "-24px",
                            width: "16px",
                          }}
                        />
                      </Tooltip>
                    )}
                  </span>
                </label>
                <Autocomplete
                  fullWidth
                  options={linkedServicesData ?? []}
                  getOptionLabel={(option) => option.name || ""}
                  value={
                    linkedServicesData?.find(
                      (option: { id: any }) =>
                        option.id === formData?.linked_service?.id
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="linked_service"
                      style={{ color: "#000000" }}
                      {...params}
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.linked_service}
                      helperText={errors?.linked_service || ""}
                    />
                  )}
                  renderOption={(params, item) => (
                    <li
                      {...params}
                      key={item.key}
                      style={{ paddingTop: "2px", paddingBottom: "2px" }}
                    >
                      <ListItemText>{item.name}</ListItemText>
                    </li>
                  )}
                  loadingText="Loading..."
                  onChange={(event, value) => {
                    setFormData((prev: any) => ({
                      ...prev,
                      linked_service: value,
                      connection_key: null,
                    }));
                    validateField("linked_service", value);
                  }}
                  className={`form-control-autocomplete ${
                    errors?.linked_service ? "has-error" : ""
                  }`}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <Autocomplete
                  fullWidth
                  options={connectionKeysData || []}
                  getOptionLabel={(connectionData) => connectionData.name}
                  onChange={(event, value) => {
                    setFormData((prev: any) => ({
                      ...prev,
                      connection_key: value?.id,
                    }));
                    validateField("connection_key", value?.id);
                  }}
                  value={
                    connectionKeysData?.find(
                      (option: { id: any }) =>
                        option.id === formData?.connection_key
                    ) || null
                  }
                  renderInput={(params) => (
                    <TextField
                      name="connection_key"
                      style={{ color: "#000000" }}
                      {...params}
                      label={
                        <span>
                          Connection Key
                          <span className="required-asterisk">*</span>
                        </span>
                      }
                      placeholder="Select..."
                      InputLabelProps={{
                        shrink: true,
                      }}
                      error={!!errors?.connection_key}
                      helperText={errors?.connection_key}
                    />
                  )}
                  loadingText="Loading..."
                  className={`form-control-autocomplete ${
                    errors?.connection_key ? "has-error" : ""
                  }`}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  Container Name<span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  name="container_name"
                  onChange={handleFormData}
                  className={`form-control ${
                    errors?.container_name ? "has-error" : ""
                  }`}
                  onBlur={(e) => validateField(e.target.name, e.target.value)}
                  error={!!errors?.container_name}
                  helperText={errors?.container_name || ""}
                  value={formData?.container_name}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">File Name</label>
                <TextField
                  type="text"
                  name="file_name"
                  onChange={handleFormData}
                  className="form-control"
                  value={formData?.file_name}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">File Path</label>
                <TextField
                  type="text"
                  name="file_path"
                  onChange={handleFormData}
                  className="form-control"
                  value={formData?.file_path}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Output Location</label>
                <TextField
                  type="text"
                  name="output_location"
                  onChange={handleFormData}
                  className={`form-control `}
                  value={formData?.output_location}
                />
              </Grid>
            </Grid>
          </Box>
          <Box>
            <FlexBetween
              gap="3rem"
              sx={{
                marginTop: "6px",
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <ButtonComponent
                className="btn-orange mb-2"
                handelClickEvent={onSaveGetBlobList}
              >
                View Files
              </ButtonComponent>
            </FlexBetween>
          </Box>
        </form>
        {tableData.rows && tableData.rows.length > 0 && (
          <>
            <FlexBetween>
              <DataTable
                dataColumns={tableData.columns}
                // dataRows={filteredData || []}
                dataRows={tableData.rows || []}
                loading={isLoading}
                dataListTitle={"Blob List"}
                className="dataTable no-radius hide-progress-icon"
              />
            </FlexBetween>
          </>
        )}
      </Box>
    </>
  );
};

export default BlobList;
