import React, { useEffect, useState } from "react";
import { Box, Grid, TextField } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import useFetchLinkedServiceById from "../../hooks/useFetchLinkedServiceById";
import Loader from "../../components/Molecules/Loader/Loader";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import { IconBtnEditBase } from "../../common/utils/icons";
const ViewLinkedServices: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { id: linkedServiceId } = useParams();
  const [formData, setFormData] = useState<any>({
    name: "",
    code: "",
    type: "",
    sub_type: "",
    connection_key: [],
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // hooks
  const [linkedServiceData] = useFetchLinkedServiceById({
    setIsLoading,
    currentLinkedServiceId: linkedServiceId,
  });

  useEffect(() => {
    console.log("connection_key", formData);
  }, [formData]);
  useEffect(() => {
    if (linkedServiceData?.name) {
      console.log(linkedServiceData);
      setFormData({
        name: linkedServiceData?.name,
        code: linkedServiceData?.code,
        type: linkedServiceData?.type,
        sub_type: linkedServiceData?.sub_type,
        connection_key: linkedServiceData?.connection_key_details,
      });
    }
    setCurrentBreadcrumbPage({
      name: linkedServiceData?.name,
      id: linkedServiceData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [linkedServiceData]);

  return (
    <>
      {isLoading ? (
        <Loader isLoading={isLoading} />
      ) : (
        <Box>
          <Box className="text-box-card-white">
            <Box className="text-box-header">
              <h3>Linked Service Details</h3>
              <button
                className="btn-nostyle icon-btn-edit"
                onClick={() =>
                  navigate(`/linked-services/edit/${linkedServiceId}`)
                }
              >
                <IconBtnEditBase width={32} height={32} />
              </button>
            </Box>

            <Grid container rowSpacing={1.5} columnSpacing={2.5}>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Name</label>
                <TextField
                  type="text"
                  // placeholder="Ex: Sample Linked Services Name"
                  name="name"
                  value={formData?.name}
                  className="form-control read-only"
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Type</label>
                <TextField
                  type="text"
                  // placeholder="Ex: Sample Linked Services Type"
                  name="type"
                  value={formData?.type}
                  className="form-control read-only"
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Code</label>
                <TextField
                  type="text"
                  // placeholder="Ex: Sample Linked Services Code"
                  name="code"
                  value={formData?.code}
                  className="form-control read-only"
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Sub Type</label>
                <TextField
                  type="text"
                  // placeholder="Ex: Sample Linked Services Sub Type"
                  name="sub_type"
                  value={formData?.sub_type}
                  className="form-control read-only"
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Connection Keys</label>
                <div className="form-control">
                  {formData?.connection_key &&
                    formData?.connection_key
                      ?.map((key: any) => key?.name)
                      .join(", ")}
                </div>
              </Grid>
            </Grid>
          </Box>
        </Box>
      )}
    </>
  );
};

export default ViewLinkedServices;
