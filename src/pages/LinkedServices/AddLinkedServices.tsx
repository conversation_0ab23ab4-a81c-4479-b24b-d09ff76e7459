import {
  Box,
  Button,
  Grid,
  TextField,
  Select,
  MenuItem,
  Autocomplete,
  Chip,
} from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import FlexBetween from "../../components/FlexBetween";
import useFetchLinkedServiceById from "../../hooks/useFetchLinkedServiceById";
import { addEditLinkedServiceSchema } from "../../schemas";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { useToast } from "../../services/utils";
import {
  addLinkedService,
  updateLinkedService,
} from "../../services/linkedService";
import {
  LINKED_SERVICES_SUBTYPES,
  LINKED_SERVICES_TYPES,
} from "../../services/constants/LinkedServices";
import Loader from "../../components/Molecules/Loader/Loader";
import { Popper } from "@mui/base";
import { CheckCircleRounded } from "@mui/icons-material";
import useFetchAllConnectionKey from "../../hooks/useFetchAllConnectionKey";
import { useBreadCrumbContext } from "../../contexts/BreadCrumbContext";
import useFetchConnectionKey from "../../hooks/useFetchConnectionKey";
import { getConnectionKeys } from "../../services/connectionKeysService";

const AddLinkedServices: React.FC = () => {
  const { setCurrentBreadcrumbPage } = useBreadCrumbContext();
  const navigate = useNavigate();
  const { showToast } = useToast();
  const { id: linkedServiceId } = useParams();
  const [formData, setFormData] = useState<any>({
    name: "",
    code: "",
    type: LINKED_SERVICES_TYPES[0] || "",
    sub_type: LINKED_SERVICES_SUBTYPES[LINKED_SERVICES_TYPES[0]][0] || "",
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [initialFormData, setInitialFormData] = useState<any>({});
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // hooks
  const [linkedServiceData] = useFetchLinkedServiceById({
    setIsLoading,
    currentLinkedServiceId: linkedServiceId,
  });
  useEffect(() => {
    setCurrentBreadcrumbPage({
      name: linkedServiceData?.name,
      id: linkedServiceData?.id,
    });
    return () => {
      setCurrentBreadcrumbPage({ name: "", id: null });
    };
  }, [linkedServiceData]);
  const [connectionKeysData] = useFetchConnectionKey({
    setIsLoading,
    type: formData?.sub_type,
  });

  const handleFormData = (e: any) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });

    validateField(e.target.name, e.target.value);
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await addEditLinkedServiceSchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setErrors((prevErrors) => ({ ...prevErrors, [name]: undefined }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  // handel event when save a linked service
  const onSaveLinkedService = async (e: any) => {
    const serviceAction = linkedServiceId
      ? updateLinkedService
      : addLinkedService;
    e.preventDefault();
    try {
      await addEditLinkedServiceSchema.validate(formData, {
        abortEarly: false,
      });
      // need to update connection key and delete this comment after updation
      const connectionKey =
        formData?.connection_keys &&
        formData?.connection_keys?.map((item: any) => item.id);
      const connectionKeyDetails = formData.connection_keys;
      const reqBody = {
        name: formData.name,
        code: formData.code,
        type: formData.type,
        sub_type: formData.sub_type,
        connection_details: {
          connection_keys: connectionKey,
          connection_keys_details: connectionKeyDetails.map(
            ({ id, code, ...rest }: any) => ({
              connection_key_id: id,
              connection_key_code: code,
            })
          ),
        },
        is_active: true,
      };
      serviceAction({
        currentLinkedServiceId: linkedServiceId,
        payload: reqBody,
      })
        .then((response) => {
          if (response) {
            showToast(
              `Linked Service ${
                linkedServiceId ? "updated" : "created"
              } successfully!`,
              "success"
            );
            navigate("/linked-services");
          }
        })
        .catch((error) => {
          console.error(
            `Cannot ${linkedServiceId ? "update" : "create"} linked service`
          );
        });
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      validationErrors.inner.forEach(
        (error: { path: string | number; message: string }) => {
          newErrors[error.path] = error.message;
        }
      );
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };
  const getConnectionKeyDataById = async (ids: string[]) => {
    try {
      setIsLoading(true);
      const result = await getConnectionKeys("", ids.join(", "));
      return result;
    } catch (err) {
      console.error("Error fetching connection key data:", err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      const isConnectionKey =
        linkedServiceData?.connection_details?.connection_keys;
      if (isConnectionKey) {
        const ids = isConnectionKey;
        const result = await getConnectionKeyDataById(ids);
        const transformResult = result?.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
        setFormData({
          name: linkedServiceData?.name,
          code: linkedServiceData?.code,
          type: linkedServiceData?.type,
          sub_type: linkedServiceData?.sub_type,
          connection_keys: isConnectionKey.length > 0 ? transformResult : [],
        });
        setInitialFormData({
          name: linkedServiceData?.name,
          code: linkedServiceData?.code,
          type: linkedServiceData?.type,
          sub_type: linkedServiceData?.sub_type,
          connection_keys: isConnectionKey.length > 0 ? transformResult : [],
        });
      }
    };
    fetchData();
  }, [linkedServiceData]);

  // Update hasChanges when formData or fileData changes
  useEffect(() => {
    const formChanged =
      JSON.stringify(formData) !== JSON.stringify(initialFormData);

    setHasChanges(formChanged);
  }, [formData, initialFormData]);

  const renderConnectionKeyPopper = useCallback((props: any) => {
    return (
      <Popper
        {...props}
        anchorEl={document.getElementById("autocomplete-selected-resources")}
      />
    );
  }, []);

  const onSelectConnectionKey = (dataObj: any) => {
    const updatedColumns = dataObj.map((item: any) => ({
      id: item.id,
      name: item.name,
      code: item.code,
    }));
    setFormData({
      ...formData,
      connection_keys: updatedColumns,
    });
    validateField("connection_keys", updatedColumns);
  };
  const handleChipClick = (option: any) => {
    const url = `/connection-keys/view/${option?.id}`;
    window.open(url, "_blank");
  };

  return (
    <>
      <Loader isLoading={isLoading} />
      <Box>
        <form onSubmit={onSaveLinkedService} autoComplete="off">
          <Box className="text-box-card-white">
            <Box className="text-box-header">
              <h3>
                {linkedServiceId ? "Edit " : "Add "}
                Linked Services
              </h3>
            </Box>
            <Grid container rowSpacing={1.5} columnSpacing={2.5}>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  Name<span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  // placeholder="Ex: Sample Linked Services Name"
                  name="name"
                  onChange={handleFormData}
                  className={`form-control ${errors?.name ? "has-error" : ""}`}
                  onBlur={(e) => validateField(e.target.name, e.target.value)}
                  error={!!errors?.name}
                  helperText={errors?.name || " "}
                  value={formData?.name}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  Type<span className="required-asterisk">*</span>
                </label>
                <Select
                  MenuProps={{
                    disableScrollLock: true,
                  }}
                  title="Type"
                  value={formData?.type}
                  name="type"
                  style={{ width: "100%", height: 35 }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      [e.target.name]: e.target.value,
                      connection_keys: [],
                      sub_type: LINKED_SERVICES_SUBTYPES[e.target.value][0],
                    })
                  }
                  className={`form-control-autocomplete form-control-autocomplete-1`}
                >
                  {LINKED_SERVICES_TYPES.map((type: string) => (
                    <MenuItem key={type} value={type}>
                      <span style={{ textTransform: "capitalize" }}>
                        {type}
                      </span>
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  Code<span className="required-asterisk">*</span>
                </label>
                <TextField
                  type="text"
                  // placeholder="Ex: Sample Linked Services Code"
                  name="code"
                  onChange={(e: any) => {
                    if (!linkedServiceData?.code) {
                      handleFormData(e);
                    }
                  }}
                  onBlur={(e) => {
                    if (!linkedServiceData?.code) {
                      validateField(e.target.name, e.target.value);
                    }
                  }}
                  className={`form-control ${errors?.code ? "has-error" : ""}`}
                  error={!!errors?.code}
                  helperText={errors?.code || " "}
                  value={formData?.code}
                  disabled={linkedServiceData?.code ? true : false}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">
                  Sub Type<span className="required-asterisk">*</span>
                </label>
                <Select
                  MenuProps={{
                    disableScrollLock: true,
                  }}
                  title="Sub Type"
                  value={formData?.sub_type}
                  name="sub_type"
                  style={{ width: "100%", height: 35 }}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      connection_keys: [],
                      [e.target.name]: e.target.value,
                    })
                  }
                  className={`form-control-autocomplete form-control-autocomplete-1`}
                >
                  {LINKED_SERVICES_SUBTYPES?.[formData?.type] &&
                    LINKED_SERVICES_SUBTYPES?.[formData?.type].map(
                      (type: string) => (
                        <MenuItem key={type} value={type}>
                          <span style={{ textTransform: "capitalize" }}>
                            {type}
                          </span>
                        </MenuItem>
                      )
                    )}
                </Select>
              </Grid>
              <Grid item xs={12} sm={6} md={4} lg={4} xl={4}>
                <label className="label-text">Connection Key</label>
                <Autocomplete
                  id={"autocomplete-selected-resources"}
                  PopperComponent={renderConnectionKeyPopper}
                  fullWidth={true}
                  className={`form-control-autocomplete ${
                    errors?.connection_keys ? "has-error" : ""
                  }`}
                  isOptionEqualToValue={(option: any, value: any) =>
                    option?.id === value?.id
                  }
                  multiple={true}
                  options={connectionKeysData || []}
                  getOptionLabel={(option) => option.name}
                  disableCloseOnSelect
                  onChange={(key: any, value: any) =>
                    onSelectConnectionKey(value)
                  }
                  value={
                    formData?.connection_keys ? formData?.connection_keys : []
                  }
                  renderInput={(params) => (
                    <div className="autocomplete-chips-direction">
                      <TextField
                        {...params}
                        variant="outlined"
                        name="connection_keys"
                        placeholder="Select..."
                        InputLabelProps={{
                          shrink: true,
                        }}
                        error={!!errors?.connection_keys}
                        helperText={errors?.connection_keys || " "}
                      />
                    </div>
                  )}
                  renderOption={(props, option, { selected }) => (
                    <MenuItem
                      {...props}
                      key={option.id}
                      value={option.name}
                      sx={{ justifyContent: "space-between" }}
                    >
                      {option.name}
                      {selected ? <CheckCircleRounded color="info" /> : null}
                    </MenuItem>
                  )}
                  renderTags={(value, getTagProps) => (
                    <div className="vertical-scroll">
                      {value.map((option, index) => (
                        <Chip
                          {...getTagProps({ index })}
                          key={index}
                          label={option.name}
                          onClick={() => handleChipClick(option)}
                        />
                      ))}
                    </div>
                  )}
                />
              </Grid>
            </Grid>
          </Box>
          <Box>
            <FlexBetween
              gap="3rem"
              sx={{
                marginTop: 4,
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <Button
                type="submit"
                className="btn-orange"
                color="secondary"
                variant="contained"
                disabled={!hasChanges}
              >
                <SaveOutlinedIcon /> &nbsp; Save
              </Button>
            </FlexBetween>
          </Box>
        </form>
      </Box>
    </>
  );
};

export default AddLinkedServices;
