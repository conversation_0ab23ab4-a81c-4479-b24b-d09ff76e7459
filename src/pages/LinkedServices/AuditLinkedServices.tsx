import React, { useState } from "react";
import { Box, Tooltip, Typography, Checkbox, IconButton } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import {
  formattedJson,
  getFormattedDateTime,
  removeCheckbox<PERSON>romJson,
} from "../../services/utils";
import { useParams } from "react-router-dom";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import InfoIcon from "@mui/icons-material/Info";
import AuditComponent from "../../components/AuditComponent";

import { IconCompareReportSvg, IconReportsSvg } from "../../common/utils/icons";
import useFetchLinkedServicesBackup from "../../hooks/useFetchLinkedServicesBackup";
import useFetchLinkedServiceById from "../../hooks/useFetchLinkedServiceById";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const AuditLinkedServices = () => {
  const { id }: any = useParams();
  //for pagination default values and states
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [auditSource, setAuditSource] = useState("");

  const [linkedServicesBackupData] = useFetchLinkedServicesBackup({
    linkedServicesId: id,
    setIsLoading,
    page,
    pSize,
  });

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      flex: 0,
      minWidth: 0,
      renderCell: (params: any) => null,
    },
    {
      field: "checkbox",
      headerName: "",
      width: 80,
      renderCell: (params) => (
        <Box sx={{ paddingTop: "4px" }}>
          <Checkbox
            checked={selectedRows.some((row) => row?.id === params?.row?.id)}
            onChange={() => toggleSelection(params.row)}
            sx={{
              "&.Mui-checked": {
                color: "#FFA500",
              },
            }}
          />
        </Box>
      ),
    },

    {
      field: "code",
      headerName: "Code",
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "created_on",
      headerName: "Last Modified",
      minWidth: 120,
      flex: 1,
      renderCell: (params) => {
        return getFormattedDateTime(params?.value);
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "info",
      headerName: "Linked Services Info",
      minWidth: 180,
      flex: 1,
      renderCell: (params) => {
        const { checkbox, ...rowWithoutCheckbox } = params.row;

        return (
          <span className="position-relative">
            <Tooltip
              componentsProps={{
                tooltip: { className: "wide-tooltip w-380" },
              }}
              title={
                <pre
                  style={{
                    whiteSpace: "pre-wrap",
                    margin: 0,
                    maxHeight: "200px",
                    overflowY: "auto",
                  }}
                >
                  <React.Fragment>
                    <Typography color="inherit">
                      Linked Services Info
                    </Typography>

                    <Typography>
                      {formattedJson(JSON.stringify(rowWithoutCheckbox))}
                    </Typography>
                  </React.Fragment>
                </pre>
              }
            >
              <InfoIcon
                sx={{
                  position: "absolute",
                  top: "50%",
                  transform: "translateY(-50%)",
                  right: "-24px",
                  width: "16px",
                  cursor: "pointer",
                }}
              />
            </Tooltip>
          </span>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "center",
      headerAlign: "center",
      width: 130,
      renderCell: (params: any) => {
        return (
          <>
            {linkedServicesData && (
              <Tooltip title="Compare Current Defination" placement="top">
                <IconButton
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    compareCurrentItem(params?.row, "currentCompare")
                  }
                >
                  <IconCompareReportSvg />
                </IconButton>
              </Tooltip>
            )}
          </>
        );
      },
    },
  ];

  const [linkedServicesData] = useFetchLinkedServiceById({
    setIsLoading,
    currentLinkedServiceId: id,
  });

  const compareCurrentItem = async (data: any, source: any) => {
    const newData = await removeCheckboxFromJson(data);
    if (selectedRows.length > 0) {
      setSelectedRows([]);
    }
    setSelectedRows((prevSelectedRows) => [
      ...prevSelectedRows,
      newData,
      linkedServicesData,
    ]);
    setAuditSource(source);
    setOpenDialog(true);
  };

  const toggleSelection = (row: { id: any }) => {
    setSelectedRows((prevSelectedRows) => {
      if (prevSelectedRows.some((selectedRow) => selectedRow.id === row.id)) {
        // Deselect row
        return prevSelectedRows.filter(
          (selectedRow) => selectedRow.id !== row.id
        );
      } else {
        // Select row
        if (prevSelectedRows.length < 2) {
          return [...prevSelectedRows, removeCheckboxFromJson(row)];
        } else {
          // Replace the second selected row with the new row
          return [prevSelectedRows[0], removeCheckboxFromJson(row)];
        }
      }
    });
  };

  return (
    <AuditComponent
      auditColumnData={columns}
      auditRowData={linkedServicesBackupData || []}
      isLoading={isLoading}
      selectedRows={selectedRows}
      setSelectedRows={setSelectedRows}
      toggleSelection={toggleSelection}
      dataListTitle="Linked Services Modification History"
      page={page}
      setPage={setPage}
      pSize={pSize}
      setPSize={setPSize}
      openDialog={openDialog}
      setOpenDialog={setOpenDialog}
      auditSource={auditSource}
      setAuditSource={setAuditSource}
    />
  );
};

export default AuditLinkedServices;
