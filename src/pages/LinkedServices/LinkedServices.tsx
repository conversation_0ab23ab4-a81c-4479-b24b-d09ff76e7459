import React, { useEffect, useMemo, useState } from "react";
import {
  Box,
  Button,
  Grid,
  IconButton,
  InputBase,
  Tooltip,
} from "@mui/material";
import FlexBetween from "../../components/FlexBetween";
import DataTable from "../../components/DataGrids/DataGrid";
import { GridColDef } from "@mui/x-data-grid";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import ButtonComponent from "../../components/Button.Component";
import { GRID_DETAIL_PANEL_TOGGLE_COL_DEF } from "@mui/x-data-grid-pro";
import { Search } from "@mui/icons-material";
import AddSharpIcon from "@mui/icons-material/AddSharp";
import { deleteLinkedService } from "../../services/linkedService";
import { useToast } from "../../services/utils";
import {
  downloadLinkedServiceExportFile,
  getFormattedDateTime,
} from "../../services/utils";
import DropdownMenu from "../../components/Molecules/DropdownMenu/DropdownMenu";
import Loader from "../../components/Molecules/Loader/Loader";
import {
  IconAudit,
  IconDeleteBlueSvg,
  IconCheckCircleIconGreenSvg,
  IconCrossCircleIconSvg,
  IconEyeBase,
  IconEditBase,
} from "../../common/utils/icons";
import { enableRowGroupByColumnProps } from "../../components/DataGridProps/DataGridProps";
import { paginatedResponseFormat } from "../../services/constants";
import useFetchPaginatedAllLinkedServices from "../../hooks/useFetchPaginatedAllLinkedServices";
import LimitChractersWithTooltip from "../../common/utils/LimitChractersWithTooltip";

const LinkedServices: React.FC = () => {
  const defaultPage = process.env.REACT_APP_DEFAULT_PAGE;
  const defaultPageSize = process.env.REACT_APP_DEFAULT_PAGE_SIZE;
  const [page, setPage] = useState<number>(
    defaultPage ? parseInt(defaultPage) : 1
  );
  const [pSize, setPSize] = useState<number>(
    defaultPageSize ? parseInt(defaultPageSize) : 25
  );
  const navigate = useNavigate();
  const { showToast } = useToast();
  // states
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isDownloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>(paginatedResponseFormat);
  const [linkedServicesData, setLinkedServicesData] = useState<any>(
    paginatedResponseFormat
  );
  // hooks
  const [fetchedLinkedServices] = useFetchPaginatedAllLinkedServices({
    setIsLoading,
    page,
    pSize,
  });
  const [searchQuery, setSearchQuery] = useState("");

  const handelClickEvent = () => {
    navigate("/linked-services/add");
  };

  const columns: GridColDef[] = [
    {
      ...GRID_DETAIL_PANEL_TOGGLE_COL_DEF,
      width: 1,
      minWidth: 20,
      flex: 0,
      renderCell: (params: any) => <></>,
      cellClassName: "no-padding-cell",
      headerClassName: "no-padding-cell",
    },
    {
      field: "id",
      headerName: "Id",
      minWidth: 80,
      flex: 1,
      renderCell: (params: any) => {
        return <span>{params.id}</span>;
      },
    },
    {
      field: "name",
      headerName: "Linked Service",
      flex: 1,
      minWidth: 140,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value} />;
      },
    },
    {
      field: "modified_by_user",
      headerName: "Modified By",
      minWidth: 140,
      flex: 1,
      renderCell: (params: any) => {
        return <LimitChractersWithTooltip value={params?.value?.username} />;
      },
    },
    {
      field: "updated_on",
      headerName: "Last Modified",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return getFormattedDateTime(params.value);
      },
    },
    {
      field: "is_active",
      headerName: "isActive",
      minWidth: 180,
      flex: 1,
      renderCell: (params: any) => {
        return params.value ? (
          <Box title="Active">
            <IconCheckCircleIconGreenSvg width={21} height={21} />
          </Box>
        ) : (
          <Box title="InActive">
            <IconCrossCircleIconSvg width={21} height={21} />
          </Box>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      align: "right",
      headerAlign: "right",
      width: 130,
      renderCell: (params: any) => {
        const handleDelete = async () => {
          setIsLoading(true);

          await deleteLinkedService(params.row.id);
          setLinkedServicesData((prev: any) => ({
            ...prev,
            items: prev?.items?.map((item: any) =>
              item.id === params.row.id ? { ...item, is_active: false } : item
            ),
          }));
          setFileData((prev: any) => ({
            ...prev,
            items: prev?.items?.map((item: any) =>
              item.id === params.row.id ? { ...item, is_active: false } : item
            ),
          }));

          setIsLoading(false);
          showToast(`${params.row.name} deleted successfully`, "success");
        };
        return (
          <>
            {params?.row?.is_active && (
              <>
                <Tooltip title="View Linked Service" placement="top" arrow>
                  <IconButton
                    className="datagrid-action-btn"
                    color="inherit"
                    onClick={() => {
                      navigate(`/linked-services/view/${params.row.id}`);
                    }}
                  >
                    <IconEyeBase />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Edit LinkedService" placement="top" arrow>
                  <IconButton
                    className="datagrid-action-btn"
                    color="inherit"
                    onClick={() => {
                      navigate(`/linked-services/edit/${params.row.id}`);
                    }}
                  >
                    <IconEditBase />
                  </IconButton>
                </Tooltip>
              </>
            )}
            <DropdownMenu
              dropdownMenuItems={[
                <IconButton
                  key="audit-linkedServices"
                  className="datagrid-action-btn"
                  color="inherit"
                  onClick={() =>
                    navigate(
                      `/linked-services/auditLinkedServices/${params.row.id}`,
                      {
                        state: {
                          details: {
                            name: params.row.name,
                            id: params.row.id,
                          },
                        },
                      }
                    )
                  }
                >
                  <IconAudit /> Audit Linked Service
                </IconButton>,
                params?.row?.is_active && (
                  <IconButton
                    className="datagrid-action-btn"
                    color="inherit"
                    onClick={() => handleDelete()}
                  >
                    <IconDeleteBlueSvg />
                    Delete Linked Service
                  </IconButton>
                ),
                // <IconButton
                //   className="datagrid-action-btn"
                //   color="inherit"
                //   onClick={() =>
                //     downloadLinkedServiceExportFile(
                //       params.row.id,
                //       setIsDownloadLoading
                //     )
                //   }
                // >
                //   <IconExportIconBlue />
                //   Export
                // </IconButton>,
              ]}
            />
          </>
        );
      },
    },
  ];

  // for update linked services list data
  useEffect(() => {
    if (fetchedLinkedServices?.items) {
      setFileData(fetchedLinkedServices);
      setLinkedServicesData(fetchedLinkedServices);
    }
  }, [fetchedLinkedServices]);

  const handleSearchInputChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    // Filter linked services based on the searchQuery
    const filteredLinkedServices = linkedServicesData?.items?.filter(
      (linkedService: any) => {
        return linkedService.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
      }
    );
    //You can set the filtered linked services as a result in your component state
    setFileData((prev: any) => {
      return {
        ...prev,
        items: filteredLinkedServices,
      };
    });
  };
  const filteredData = useMemo(() => {
    return fileData?.items?.filter((item: any, index: number) => {
      return item.name.toLowerCase().includes(searchQuery?.toLowerCase());
    });
  }, [fileData, searchQuery]);

  return (
    <Box>
      {(isLoading || isDownloadLoading) && (
        <Loader isLoading={isLoading || isDownloadLoading} />
      )}
      <Box className="text-box-card compact-text-box-card list-page-card">
        <Grid container rowSpacing={1.5} columnSpacing={2.5}>
          <Grid item xs={12} sm={12} md={6} lg={3} xl={3}>
            <label className="label-text">Search Linked Services</label>
            <form onSubmit={handleSearchSubmit} className="common-search-panel">
              <InputBase
                placeholder="Search..."
                className="search-textbox"
                value={searchQuery}
                onChange={handleSearchInputChange}
                onKeyDown={(event) => {
                  if (event.key === "Enter") {
                    event.preventDefault();
                  }
                }}
              />
              <IconButton className="search-icon" type="submit">
                <Search className="svg_icons" />
              </IconButton>
            </form>
          </Grid>
          <Grid item xs>
            <label className="label-text">&nbsp;</label>
            <Box
              display="flex"
              justifyContent="flex-end"
              sx={{
                columnGap: { md: "20px", sm: "0", xs: "0px" },
                rowGap: {
                  xl: "0",
                  lg: "0",
                  md: "20",
                  sm: "20px",
                  xs: "20px",
                },
                flexDirection: {
                  xl: "row",
                  lg: "row",
                  md: "row",
                  sm: "column",
                  xs: "column",
                },
              }}
            >
              {/* <Button
                onClick={() =>
                  navigate(`/linked-services/import-entity`, {
                    state: {
                      import_defination_name: "Linked Services",
                    },
                  })
                }
                className="btn-orange btn-dark"
                sx={{ columnGap: 1 }}
              >
                <IconImportFileWhite />
                Import
              </Button> */}
              <ButtonComponent
                handelClickEvent={handelClickEvent}
                className="btn-orange"
              >
                <AddSharpIcon sx={{ marginRight: "4px" }} /> Linked Service
              </ButtonComponent>
            </Box>
          </Grid>
        </Grid>
      </Box>

      <FlexBetween>
        <DataTable
          dataRows={filteredData}
          dataColumns={columns}
          loading={isLoading}
          dataListTitle={"Linked Services List"}
          className="dataTable no-radius hide-progress-icon"
          paginationMode="server"
          rowCount={fileData?.total || 0}
          pageSizeOptions={[25]}
          rowGroupByColumnProps={enableRowGroupByColumnProps}
          paginationModel={{
            page: page - 1,
            pageSize: pSize,
          }}
          onPaginationModelChange={(params: any) => {
            if (params.pageSize !== pSize || params.page !== page - 1) {
              setPage(params.page + 1);
              setPSize(params.pageSize);
            }
          }}
          singlePageMaxHeightDiff={360}
        />
      </FlexBetween>
    </Box>
  );
};

export default LinkedServices;
