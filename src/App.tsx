import { CssBaseline, ThemeProvider } from "@mui/material";
import { createTheme } from "@mui/material/styles";
import { useMemo } from "react";
import { BrowserRouter } from "react-router-dom";
import { themeSettings } from "./styles/Theme";
import SiteRoutes from "./routes/Index";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./styles/style.css";
import "react-querybuilder/dist/query-builder.scss";
import "./styles/styles.scss";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { AuthProvider } from "./contexts/AuthContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import WebSocketProvider from "./components/WebSocketProvider";

const App: React.FC = () => {
  const mode = "light";
  const theme = useMemo(() => createTheme(themeSettings(mode)), [mode]);
  return (
    <div className="app">
      <AuthProvider>
        <NotificationProvider>
          <WebSocketProvider />
          <BrowserRouter>
            <ThemeProvider theme={theme}>
              <CssBaseline />
              <ToastContainer autoClose={5000} />
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <SiteRoutes />
              </LocalizationProvider>
            </ThemeProvider>
          </BrowserRouter>
        </NotificationProvider>
      </AuthProvider>
    </div>
  );
};

export default App;
