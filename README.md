# React 18 VIS Sample

![](https://img.shields.io/badge/npm-success-brightgreen.svg)
![](https://img.shields.io/badge/test-success-brightgreen.svg)

# Stack

![](https://img.shields.io/badge/react_18-✓-blue.svg)
![](https://img.shields.io/badge/npm_8-✓-blue.svg)
![](https://img.shields.io/badge/ES6-✓-blue.svg)

***

<h3 align="center">Please help this repo with a :star: if you find it useful! :blush:</h3>

***

# File structure

```
VIS Sample Code
├── .env.sample                  * Sample configuration file
├── .gitignore                   * Example git ignore file
├── package.json                 * Defines our JavaScript dependencies
├── README.MD                    * This file
├── tsconfig.json                * Defines our exact TypeScript configuration
├── yarn.lock                    * Defines our exact JavaScript dependencies tree
├── public/
│   ├── index.html
│   └── loader.css
│   └── favicon.ico
│   └── robots.txt
└── src/
    ├── assets/
    │   └── images
    │       └── logo.svg
    │   └── styles
    ├── components/
    │   └── cards/
    │       ├── index.tsx
    ├── config/
    │   └── config.ts
    ├── hooks/
    │   └── useIndex.tsx
    ├── layouts/
    │   └── footer/
    │       ├── index.tsx    
    │   └── header/
    │       ├── index.tsx    
    │   └── siderbar/
    │       ├── index.tsx    
    │   └── main/
    │       ├── index.tsx 
    |   └── index.tsx
    ├── middlewares/
    │   └── index.tsx
    ├── pages/
    │   ├── home.tsx
    │   └── login.tsx
    ├── routes/
    │   └── index.tsx
    │   └── protectedRoutes.tsx
    │   └── unProtectedRoutes.tsx
    ├── services/
    │   └── actions/
    │       ├── index.tsx    
    │   └── constants/
    │       ├── index.tsx    
    │   └── utils/
    │       ├── index.tsx
    ├── styles/
    │   ├── style.css
    ├── types/
    │   └── index.ts
    ├── app.tsx
    └── index.tsx                               * Entry point of our React's app

## How to use this code?

1. Make sure you have the latest stable version of Node.js installed

```
$ sudo npm cache clean -f
$ sudo npm install -g n
$ sudo n stable
```
```
  
2. Fork this repository and clone it
  
```
$ git clone https://github.com//{YOUR_USER_NAME}/fairpoint-tech/datatool_fe
```
  
4. Navigate into the folder  

```
$ cd datatool_fe
```
  
5. Install NPM dependencies

```
$ npm install or yarn 
```


6. Run the project

```
$ npm start  or yarn run start or npm run start
```


> `npm start` will run `react-scripts start`.

9. Navigate to `http://localhost:3000` in your browser to test it! or your configurable PORT




# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
