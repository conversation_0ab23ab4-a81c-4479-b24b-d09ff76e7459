FROM node:18.17.1 AS builder

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json to the container
COPY package*.json ./

# Install Node.js dependencies
RUN npm install 

# Copy the rest of the application code to the container
COPY . .

#To create .env file

RUN echo "REACT_APP_HOST=http://localhost:3000" > .env && \
    echo  "REACT_APP_CONFIG_API_HOST=http://localhost:4000" >> .env && \
    echo "REACT_APP_ENGINE_API_HOST=http://localhost:5000" >> .env && \
    echo "REACT_APP_CONFIG_API_HOST=http://**************/poc-config" >> .env && \
    echo "REACT_APP_ENGINE_API_HOST=http://**************/poc-engine" >> .env && \
    echo "REACT_APP_HOST_BASE_PATH=http://localhost/vis/" >> .env && \
    echo "REACT_APP_DATA_GRID_PRO=47a17e277aa19e60ab837a7b569dbbe3Tz03MjE5NyxFPTE3MjI4NjAwODAwMDAsUz1wcmVtaXVtLExNPXN1YnNjcmlwdGlvbixLVj0y" >> .env

#To Create a Build
RUN npm run build


# Stage 2: Create the final image
FROM node:alpine as main

# Copy the build from the previous stage
COPY --from=builder /app/build ./build

RUN npm install -g serve

# Expose a port that your application is listening on
EXPOSE 3000

# Define the command to run your application

CMD ["serve", "-s", "build"]

