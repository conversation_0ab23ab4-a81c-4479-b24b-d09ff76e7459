{"name": "vismaterial", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.3", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.171", "@mui/material": "^5.16.0", "@mui/x-data-grid": "^6.8.0", "@mui/x-data-grid-premium": "^6.11.0", "@mui/x-data-grid-pro": "^6.10.2", "@mui/x-date-pickers": "^6.7.0", "@mui/x-license-pro": "^6.10.2", "@react-querybuilder/dnd": "^6.5.1", "@react-querybuilder/material": "^6.5.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.36", "@types/react": "^18.2.12", "@types/react-dom": "^18.2.5", "ace-builds": "^1.23.1", "axios": "^1.4.0", "chart.js": "^4.4.0", "file-saver": "^2.0.5", "formik": "^2.4.5", "i": "^0.3.7", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "moment": "^2.29.4", "node-sass": "^7.0.3", "node-sql-parser": "^4.7.0", "npm": "^9.7.2", "react": "^18.2.0", "react-ace": "^10.1.0", "react-chartjs-2": "^5.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-idle-timer": "^5.7.2", "react-querybuilder": "^6.5.1", "react-router-dom": "6", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "sass": "^1.63.4", "socket.io-client": "^4.8.1", "sql-parser": "^0.5.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202"}}